package com.game.billingsr.enums;

public enum BillingsrType {
    /**
     *
     */
    NONE(0, "无"),
    /**
     * 默认
     */
    SYSTEM(1, "默认"),
    /**
     * quicksdk正式
     */
    QUICK_SDK_FORMAL(100, "quicksdk正式"),
    /**
     * QUICKSDK提审
     */
    QUICK_SDK_ARRAIGNMENT(101, "quicksdk提审"),
    DAJI(102, "大极"),
    ;

    private int channel;
    private String name;

    public int getChannel() {
        return channel;
    }

    public void setChannel(int channel) {
        this.channel = channel;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public static BillingsrType valueOf(int channel) {
        for (BillingsrType t : BillingsrType.values()) {
            if (t.getChannel() == channel) {
                return t;
            }
        }
        return NONE;
    }

    BillingsrType(int channel, String name) {
        this.channel = channel;
        this.name = name;
    }
}
