package com.game.billingsr.manager;

import com.game.dao.order.RechargeOrderDao;
import com.game.dao.order.WithdrawOrderDao;
import com.game.dao.player.PlayerDao;
import com.game.engine.log.GameLog;
import com.game.engine.utils.GuidGeneratorUtils;
import com.game.engine.utils.TimeUtil;
import com.game.entity.order.Order;
import com.game.entity.player.Player;
import com.game.enums.OrderType;
import com.game.manager.EntityDaoMrg;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Iterator;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

public class BillingMrg {
    private static final Logger LOGGER = LoggerFactory.getLogger(BillingMrg.class);

    private static final BillingMrg instance = new BillingMrg();

    private final Map<Long, Order> orderInfoMap = new ConcurrentHashMap<>();

    public static BillingMrg getInstance() {
        return instance;
    }

    public Order getRechargeOrder(long orderId) {
        Order orderInfo = orderInfoMap.get(orderId);
        if (orderInfo == null) {
            orderInfo = findDbRechargeOrder(orderId);
        }
        return orderInfo;
    }

    public Order getWithdrawOrder(long orderId) {
        Order orderInfo = orderInfoMap.get(orderId);
        if (orderInfo == null) {
            orderInfo = findDbWithdrawOrder(orderId);
        }
        return orderInfo;
    }

    public void addOrderInfo(Order order) {
        orderInfoMap.put(order.getOrderId(), order);
    }

    public void removeOrderInfo(long orderId) {
        orderInfoMap.remove(orderId);
    }

    public void createOrderInfo(Player player, Order order, OrderType orderType, int currencyId, double amount) {
        order.setBusiness_no(player.getBusiness_no());
        order.setPlayerId(player.getPlayerId());
        order.setType(orderType.getNum());
        order.setCurrencyId(currencyId);
        order.setAmounts(amount);
        order.setStatus(2);
        order.setExpiredTime(TimeUtil.currentTimeMillis() + 30 * TimeUtil.MIN);
    }

    public Player findDbPlayer(long playerId) {
        return EntityDaoMrg.getInstance().getDao(PlayerDao.class).getById(playerId);
    }

    public Order findDbRechargeOrder(long orderId) {
        return EntityDaoMrg.getInstance().getDao(RechargeOrderDao.class).findRechargeOrder(orderId);
    }

    public Order findDbWithdrawOrder(long orderId) {
        return EntityDaoMrg.getInstance().getDao(WithdrawOrderDao.class).findWithdrawOrder(orderId);
    }

    public static GameLog newGameLog(String topic, Order orderInfo) {
        final GameLog gameLog = new GameLog(topic);
        gameLog.append("number", GuidGeneratorUtils.generateOrderId())
                .append("business_no", orderInfo.getBusiness_no())
                .append("orderId", orderInfo.getOrderId())
                .append("type", orderInfo.getType())
                .append("currencyId", orderInfo.getCurrencyId())
                .append("amounts", orderInfo.getAmounts())
                .append("playerId", orderInfo.getPlayerId())
                .append("createTime", orderInfo.getCreateTime())
                .append("status", orderInfo.getStatus())
                .append("payEndTime", orderInfo.getPayEndTime());
        return gameLog;
    }

    public void timerCheckHear() {
        try {
            if (orderInfoMap.isEmpty()) {
                return;
            }

            final long currentTime = TimeUtil.currentTimeMillis();
            final Iterator<Map.Entry<Long, Order>> iterator = orderInfoMap.entrySet().iterator();
            while (iterator.hasNext()) {
                Map.Entry<Long, Order> entry = iterator.next();
                final Order order = entry.getValue();
                if (order != null && currentTime >= order.getExpiredTime()) {
                    iterator.remove();  // Use iterator's remove method to avoid ConcurrentModificationException
                    LOGGER.warn("orderId：{}，expired remove", order.getOrderId());
                }
            }
        } catch (Exception e) {
            LOGGER.error("timerCheckHear", e);
        }
    }

}
