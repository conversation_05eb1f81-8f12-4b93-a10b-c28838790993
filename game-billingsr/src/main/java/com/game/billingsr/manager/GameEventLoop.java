package com.game.billingsr.manager;

import com.game.engine.util.concurrent.EventLoopGroup;
import com.game.engine.util.concurrent.RejectedExecutionHandler;
import com.game.engine.util.concurrent.disruptor.DisruptorEventLoop;
import com.game.engine.util.concurrent.disruptor.WaitStrategyFactory;

import javax.annotation.Nonnull;
import javax.annotation.Nullable;
import java.util.concurrent.ThreadFactory;

public class GameEventLoop extends DisruptorEventLoop {
    public GameEventLoop(@Nullable EventLoopGroup parent, @Nonnull ThreadFactory threadFactory, @Nonnull RejectedExecutionHandler rejectedExecutionHandler) {
        super(parent, threadFactory, rejectedExecutionHandler);
    }

    public GameEventLoop(@Nullable EventLoopGroup parent, @Nonnull ThreadFactory threadFactory, @Nonnull RejectedExecutionHandler rejectedExecutionHandler, @Nonnull WaitStrategyFactory waitStrategyFactory) {
        super(parent, threadFactory, rejectedExecutionHandler, waitStrategyFactory);
    }

    public GameEventLoop(@Nullable EventLoopGroup parent, @Nonnull ThreadFactory threadFactory, @Nonnull RejectedExecutionHandler rejectedExecutionHandler, int ringBufferSize, int taskBatchSize) {
        super(parent, threadFactory, rejectedExecutionHandler, ringBufferSize, taskBatchSize);
    }

    public GameEventLoop(@Nullable EventLoopGroup parent, @Nonnull ThreadFactory threadFactory, @Nonnull RejectedExecutionHandler rejectedExecutionHandler, @Nonnull WaitStrategyFactory waitStrategyFactory, int ringBufferSize, int taskBatchSize) {
        super(parent, threadFactory, rejectedExecutionHandler, waitStrategyFactory, ringBufferSize, taskBatchSize);
    }

}
