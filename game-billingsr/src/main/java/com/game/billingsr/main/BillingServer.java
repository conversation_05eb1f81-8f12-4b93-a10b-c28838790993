package com.game.billingsr.main;

import com.game.billingsr.manager.BillingMrg;
import com.game.billingsr.manager.GameEventLoop;
import com.game.billingsr.server.tcp.BillingTcpClient2Proxy;
import com.game.billingsr.server.http.BillingHttpServer;
import com.game.billingsr.server.tcp.BillingTcpServer;
import com.game.engine.HttpClientMrg;
import com.game.engine.enums.MsgType;
import com.game.engine.enums.ServerType;
import com.game.engine.enums.state.ServerState;
import com.game.engine.io.conf.*;
import com.game.engine.io.handler.IHandler;
import com.game.engine.io.handler.TcpHandler;
import com.game.engine.io.message.HttpMessageBean;
import com.game.engine.io.message.MessageBean;
import com.game.engine.io.redis.RedisPoolManager;
import com.game.engine.kafka.LogProducerMrg;
import com.game.engine.mongo.DBConnectionMrg;
import com.game.engine.net.*;
import com.game.engine.script.ScriptLoader;
import com.game.engine.thread.MyThreadFactory;
import com.game.engine.util.async.DefaultSameThreadScheduledExecutor;
import com.game.engine.util.async.SameThreadScheduledExecutor;
import com.game.engine.util.concurrent.DefaultThreadFactory;
import com.game.engine.util.concurrent.RejectedExecutionHandlers;
import com.game.engine.utils.*;
import com.game.enums.ErrorCode;
import com.game.manager.DBHandlerRegisterMrg;
import com.game.utils.VirtualThreadUtils;
import com.google.protobuf.Message;
import com.proto.InnerMessage;
import com.proto.MIDMessage;
import io.netty.channel.Channel;
import io.netty.util.internal.StringUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.*;
import java.util.function.Consumer;

public class BillingServer extends World {

    private static final Logger LOGGER = LoggerFactory.getLogger(BillingServer.class);

    private static BillingServer billingServer;

    private BillingTcpServer billingTcpServer;
    private BillingHttpServer billingHttpServer;
    private BillingTcpClient2Proxy billingTcpClient2Proxy;

    private NettyServerConfig nettyServerConfig;
    private NettyServerConfig nettyServerConfig_http;
    private NettyClientConfig nettyClientConfig_proxy;

    private HttpClientMrg httpClientMrg;
    private LogProducerMrg logProducerMrg;
    private UniqueIDGenerator uniqueIDGenerator;
    private SameThreadScheduledExecutor timerSystem;

    private List<GameEventLoop> eventLoops = new ArrayList<>();

    private BillingServer() {
        super(1000 / 30);
    }

    public BillingTcpClient2Proxy getBillingTcpClient2Proxy() {
        return billingTcpClient2Proxy;
    }

    public static BillingServer getInstance() {
        return billingServer;
    }

    public HttpClientMrg getHttpClientMrg() {
        return httpClientMrg;
    }

    public LogProducerMrg getLogProducerMrg() {
        return logProducerMrg;
    }

    public SameThreadScheduledExecutor getTimerSystem() {
        return timerSystem;
    }

    public NettyServerConfig getNettyServerConfig() {
        return nettyServerConfig;
    }

    public NettyClientConfig getNettyClientConfig_proxy() {
        return nettyClientConfig_proxy;
    }

    public UniqueIDGenerator getUniqueIDGenerator() {
        return uniqueIDGenerator;
    }

    public void setUniqueIDGenerator(UniqueIDGenerator uniqueIDGenerator) {
        this.uniqueIDGenerator = uniqueIDGenerator;
    }

    public static void main(String[] args) {
        LOGGER.info("服务器启动时间：{}", TimeUtil.getDateTimeFormat(TimeUtil.YYYYMMDDHHMMSS));

        Config.path = FileUtil.getMainPath();
        LOGGER.info("配置路径为：" + Config.path);
        ScriptLoader.getInstance().init((str) -> SysUtil.exit(BillingServer.class, null, "脚本加载错误"));

        billingServer = new BillingServer();

        final ExecutorService service = Executors.newFixedThreadPool(1, (r) -> new Thread(r, "LOGIC_THREAD"));
        final EventConsumer<LogicEvent> eventConsumer = new EventConsumer<>(billingServer);
        final GlobalQueue<LogicEvent> queue = new GlobalQueue<>(service, eventConsumer);
        GlobalQueueContainerMrg.getInstance().setGlobalQueue(queue);

        Runtime.getRuntime().addShutdownHook(new Thread(BillingServer::stops));
    }

    private void initServerConfig() {
        ConstantConfig.reloadConstantConfig(Config.path, "constantConfig.xml");
        NettyServerConfig nettyServerConfig = FileUtil.getConfigXML(Config.path, "nettyServerConfig.xml", NettyServerConfig.class);
        if (nettyServerConfig == null) {
            SysUtil.exit(BillingServer.class, null, "nettyServerConfig");
            return;
        }

        NettyServerConfig nettyServerConfig_http = FileUtil.getConfigXML(Config.path, "nettyServerConfig_http.xml", NettyServerConfig.class);
        if (nettyServerConfig_http == null) {
            SysUtil.exit(BillingServer.class, null, "nettyServerConfig_http");
            return;
        }

        NettyClientConfig nettyClientConfig_proxy = FileUtil.getConfigXML(Config.path, "nettyClientConfig_proxy.xml", NettyClientConfig.class);
        if (nettyClientConfig_proxy == null) {
            SysUtil.exit(BillingServer.class, null, "nettyClientConfig_proxy");
            return;
        }

        this.nettyServerConfig = nettyServerConfig;
        this.nettyServerConfig_http = nettyServerConfig_http;
        this.nettyClientConfig_proxy = nettyClientConfig_proxy;

        Config.SERVER_ID = nettyServerConfig.getId(); // 设置SERVERID
        Config.SERVER_NAME = nettyServerConfig.getName(); // 设置SERVERNAME
        Config.SERVER_CHANNEL = nettyServerConfig.getChannel(); // 设置SERVERWEB
        Config.serverState = ServerState.NORMAL;
    }

    private void startServer() {
        billingTcpServer = new BillingTcpServer(nettyServerConfig);
        billingTcpClient2Proxy = new BillingTcpClient2Proxy(nettyClientConfig_proxy);
        billingHttpServer = new BillingHttpServer(nettyServerConfig_http);

        {
            billingTcpServer.start();
            billingTcpClient2Proxy.start();
            billingHttpServer.start();
        }

    }

    private static void stops() {
        Config.serverState = ServerState.CLOSING;
        try {
            RedisPoolManager.getInstance().destroy();
            BillingServer.getInstance().stop();
        } catch (Exception e) {
            LOGGER.error("", e);
        }
    }

    private void stop() {
        LOGGER.info("close eventLoop");
        if (!billingServer.eventLoops.isEmpty()) {
            for (GameEventLoop gameEventLoop : billingServer.eventLoops) {
                gameEventLoop.shutdownNow();
            }
        }
        LOGGER.info("关闭充值服务器");
        try {
            if (this.billingTcpServer != null) {
                this.billingTcpServer.stop();
            }
        } catch (Exception e) {
            LOGGER.error("关闭充值服务器", e);
        }

        LOGGER.info("关闭连接http服务器");
        try {
            if (this.billingHttpServer != null) {
                this.billingHttpServer.stop();
            }
        } catch (Exception e) {
            LOGGER.error("关闭连接http服务器", e);
        }

        LOGGER.info("关闭连接代理服务器");
        try {
            if (this.billingTcpClient2Proxy != null) {
                this.billingTcpClient2Proxy.stop();
            }
        } catch (Exception e) {
            LOGGER.error("关闭连接代理服务器", e);
        }
    }

    @Override
    protected void registerSeri() {

    }

    @Override
    protected void registerProtoHandler() {

    }

    @Override
    protected void listenOrConnect() throws Exception {
        startServer();
    }

    @Override
    protected void initWhenThreadStartImpl() throws Exception {
        initServerConfig();

        for (int i = 1; i <= 5; i++) {
            final GameEventLoop eventLoop = new GameEventLoop(null, new DefaultThreadFactory("Hall_Event_Loop_" + i), RejectedExecutionHandlers.abort());
            this.eventLoops.add(eventLoop);
        }
        this.httpClientMrg = new HttpClientMrg();
        this.logProducerMrg = new LogProducerMrg();
        final String serverId = Config.SERVER_ID + "";
        this.uniqueIDGenerator = new UniqueIDGenerator(Integer.parseInt(serverId));
        this.timerSystem = new DefaultSameThreadScheduledExecutor(4);

        final JedisClusterConfig jpc = FileUtil.getConfigXML(Config.path, "jedisClusterConfig.xml", JedisClusterConfig.class);
        final RedisPoolManager redisPoolManager = new RedisPoolManager(jpc);

        final String mongoUrl = nettyServerConfig.getMongo_connection_string();
        final String mongoData = nettyServerConfig.getMongo_read_write_database();
        DBConnectionMrg.getInstance().dBConnection(mongoUrl, mongoData);
        DBHandlerRegisterMrg.getInstance().dBHandlerRegister();


        //启动kafka
        this.logProducerMrg.start(nettyServerConfig.getKafka_connection_string());
        this.timerSystem.scheduleAtFixedDelay(5 * TimeUtil.SEC, 5 * TimeUtil.SEC, this::serverHeartCheck);
        this.timerSystem.scheduleAtFixedDelay(5 * TimeUtil.SEC, TimeUtil.MIN, () -> BillingMrg.getInstance().timerCheckHear());
    }

    @Override
    protected void tickImpl(long curTime) throws Exception {
        this.httpClientMrg.tick();
        this.timerSystem.tick();
        DBHandlerRegisterMrg.getInstance().tick(curTime);
    }

    @Override
    protected void onLogicEvent(LogicEvent evt) {
        switch (evt.getLogicEventType()) {
            /**
             * 监听billing服务器
             */
            case BILLINGSERVER_ON_TCP_CONNECT:
                this.billingTcpServer.onIoSessionConnect(evt.getChannel());
                break;
            case BILLINGSERVER_ON_DISCONNECT:
                this.billingTcpServer.onIoSessionClosed(evt.getChannel());
                break;

            /**
             * 链接Proxy服务器
             */
            case B_PROXYCLIENT_ON_TCP_CONNECT:
                this.billingTcpClient2Proxy.onIoSessionConnect(evt.getChannel());
                break;
            case B_PROXYCLIENT_ON_DISCONNECT:
                this.billingTcpClient2Proxy.onIoSessionClosed(evt.getChannel());
                break;

            case BILLINGSERVER_MESSAGE_EVENT_S_RECV:
            case B_LOGCLIENT_MESSAGE_EVENT_C_RECV:
            case B_PROXYCLIENT_MESSAGE_EVENT_C_RECV:
                messageHandler(evt);
                break;

            case UDP_MESSAGE_EVENT_S_RECV:
                httpMessageHandler(evt.getParamA(), evt.getParamB(), evt.getChannel());
                break;
        }
    }

    private void messageHandler(LogicEvent event) {
        try {
            final int msgType = event.getIntParamA();
            final long id = event.getLongParamA();
            final long udpSessionId = event.getLongParamB();
            final int msgId = event.getIntParamB();
            final byte[] bytes = (byte[]) event.getParamA();
            if (msgType == MsgType.IDMESSAGE.getType()) {// 数据结构:msgId:pfbytes
                // 在本地注册，必须预处理
                final MessageBean messageBean = ScriptLoader.getInstance().getMessageBean(msgId);
                if (messageBean != null) {
                    final Message message = messageBean.buildMessage(bytes);
                    final TcpHandler handler = (TcpHandler) messageBean.newHandler();
                    if (handler != null) {
                        handler.setPid(id);
                        handler.setUdpSessionId(udpSessionId);
                        handler.setMsgBytes(bytes);
                        handler.setMessage(message);
                        handler.setSession(event.getChannel());
                        final long start = TimeUtil.currentTimeMillis();
                        asyncExecute(id, handler);
                        final long end = TimeUtil.currentTimeMillis() - start;
                        if (end > 10 && !handler.getClass().getSimpleName().equals("GameInnerHttpHandler")) {
                            LOGGER.warn("{}，执行时间过长：{}ms", handler.getClass().getSimpleName(), end);
                        }
                    }
                } else {
                    LOGGER.warn("消息[{}]代码未实现逻辑", msgId);
                }
            } else {
                LOGGER.warn("消息类型{}未实现,玩家{}消息发送失败", msgType, id);
            }
        } catch (Exception e) {
            LOGGER.error("channelRead", e);
        }
    }

    public static void httpMessageHandler(Object msg, Object uri, Channel session) {
        try {
            @SuppressWarnings("unchecked") final Map<String, Object> paramsMap = (Map<String, Object>) msg;
            final String requestPath = (String) uri;

            if (StringUtil.isNullOrEmpty(requestPath) || paramsMap.isEmpty()) {
                LOGGER.warn("ip ：{}，request error：{}，params：{}", MsgUtil.getClientIp(session), requestPath, JsonUtils.writeAsJson(paramsMap));
                MsgUtil.responseHttp(ErrorCode.Data_Error.getCode(), session);
                return;
            }

            final HttpMessageBean httpMessageBean = ScriptLoader.getInstance().getHttpMessageBean(requestPath);
            if (httpMessageBean == null) {
                LOGGER.error("HttpMessagePoll，not find，content = {} httpMessageBean", requestPath);
                MsgUtil.responseHttp(ErrorCode.Data_Error.getCode(), session);
                return;
            }

            long time = TimeUtil.currentTimeMillis();
            IHandler handler = httpMessageBean.newHandler();
            handler.setSession(session);
            handler.setCreateTime(TimeUtil.currentTimeMillis());
            handler.setParamsMap(paramsMap);
            final String simpleName = handler.getClass().getSimpleName();
            if (simpleName.contains("ReloadScripts")) {
                VirtualThreadUtils.execute(handler);
            } else {
                handler.run();
            }
            time = TimeUtil.currentTimeMillis() - time;
            if (time > 10) {
                LOGGER.warn("{}，处理时间超过，{}", handler.getClass().getSimpleName(), time);
            }
        } catch (Exception e) {
            LOGGER.error("httpMessageHandler", e);
            MsgUtil.responseHttp(ErrorCode.Data_Error.getCode(), session);
        }
    }

    private void serverHeartCheck() {
        try {
            BillingServer.getInstance().getBillingTcpClient2Proxy().checkStatus();

            BillingServer.getInstance().buildRegisterUpdateMessage(msg -> {
                BillingServer.getInstance().getBillingTcpClient2Proxy().broadcastMsgAllSessions(msg);
            });

            InnerMessage.InnerReqServerListMessage.Builder req = InnerMessage.InnerReqServerListMessage.newBuilder();
            req.setMsgID(MIDMessage.MID.InnerReqServerList_VALUE)
                    .addType(ServerType.HALL.getType());
            BillingServer.getInstance().getBillingTcpClient2Proxy().sendMsg(req.build());
        } catch (Exception e) {
            LOGGER.error("serverHeartCheck", e);
        }
    }

    /**
     * 构建登录信息
     *
     * @param action
     */
    private void buildRegisterUpdateMessage(Consumer<InnerMessage.InnerReqRegisterUpdateMessage> action) {
        final InnerMessage.InnerReqRegisterUpdateMessage.Builder req = InnerMessage.InnerReqRegisterUpdateMessage.newBuilder();
        req.setMsgID(MIDMessage.MID.InnerReqRegisterUpdate_VALUE)
                .setServerInfo(buildServerInfo(nettyServerConfig));
        if (action != null) {
            action.accept(req.build());
        }
    }

    /**
     * 构建服务器信息
     *
     * @param config
     * @return
     */
    private InnerMessage.InnerServerInfo buildServerInfo(NettyServerConfig config) {
        final InnerMessage.InnerServerInfo.Builder builder = InnerMessage.InnerServerInfo.newBuilder();
        builder.setId(config.getId())
                .setIp(config.getIp() == null ? "" : config.getIp())
                .setType(config.getType().getType())
                .setPort(config.getPort())
                .setGameState(Config.serverState.getState())
                .setPower(config.getPower())
                .setHttpPort(config.getHttpPort())
                .setName(config.getName());
        return builder.build();
    }

    public void asyncExecute(long playerId, Runnable runnable) {
        final int index = (int) (playerId % this.eventLoops.size());
        this.eventLoops.get(index).execute(runnable);
    }
}
