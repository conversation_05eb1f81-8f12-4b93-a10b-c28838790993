package com.game.billingsr.server.tcp;

import com.game.billingsr.server.handler.BillingTcpServerHandler;
import com.game.engine.enums.ServerType;
import com.game.engine.io.conf.NettyServerConfig;
import com.game.engine.io.netty.TcpServer;
import com.game.engine.io.netty.code.ProtocolCoder;
import com.game.engine.net.GlobalQueueContainerMrg;
import com.game.engine.service.TcpService;
import io.netty.channel.ChannelInitializer;
import io.netty.channel.socket.SocketChannel;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 充值tcp服务
 */
public class BillingTcpServer extends TcpService {
    private static final Logger LOGGER = LoggerFactory.getLogger(BillingTcpServer.class);

    private final TcpServer nettyServer;
    private final NettyServerConfig nettyServerConfig;

    public BillingTcpServer(NettyServerConfig nettyServerConfig) {
        super(ServerType.BILLING, GlobalQueueContainerMrg.getInstance().getGlobalQueue().getLogicQueue());
        this.nettyServerConfig = nettyServerConfig;
        nettyServer = new TcpServer(nettyServerConfig, new BillingTcpChannelInitializer(this));
    }

    @Override
    public void broadcastMsg(Object msg) {
    }

    @Override
    public void start() {
        nettyServer.start();
    }

    public void stop() {
        nettyServer.stop();
    }

    @Override
    public String toString() {
        return nettyServerConfig.getName();
    }

    public int getId() {
        return nettyServerConfig.getId();
    }

    public String getName() {
        return nettyServerConfig.getName();
    }

    static class BillingTcpChannelInitializer extends ChannelInitializer<SocketChannel> {

        private TcpService tcpService;

        public BillingTcpChannelInitializer(TcpService tcpService) {
            this.tcpService = tcpService;
        }

        @Override
        protected void initChannel(SocketChannel ch) throws Exception {
            ch.pipeline().addLast(new ProtocolCoder());
            ch.pipeline().addLast(new BillingTcpServerHandler(tcpService));
        }
    }
}
