package com.game.billingsr.server.tcp;

import com.game.billingsr.server.handler.BillingProxyClientHandler;
import com.game.engine.enums.ServerType;
import com.game.engine.io.conf.NettyClientConfig;
import com.game.engine.io.netty.TcpClient;
import com.game.engine.io.netty.code.ProtocolCoder;
import com.game.engine.net.GlobalQueueContainerMrg;
import com.game.engine.service.TcpService;
import io.netty.channel.ChannelInitializer;
import io.netty.channel.socket.SocketChannel;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 链接ProxyServer
 *
 */
public class BillingTcpClient2Proxy extends TcpService {

    private static final Logger log = LoggerFactory.getLogger(BillingTcpClient2Proxy.class);

    private final NettyClientConfig nettyClientConfig;
    private final TcpClient nettyClient;

    public BillingTcpClient2Proxy(NettyClientConfig nettyClientConfig) {
        super(ServerType.PROXY, GlobalQueueContainerMrg.getInstance().getGlobalQueue().getLogicQueue());
        this.nettyClientConfig = nettyClientConfig;
        this.nettyClient = new TcpClient(nettyClientConfig, new ProxyClientInitializer(this));
    }

    @Override
    public void broadcastMsg(Object msg) {
        this.nettyClient.broadcastMsg(msg);
    }

    @Override
    public void start() {
        nettyClient.start();
    }

    @Override
    public void stop() {
        nettyClient.stop();
    }

    @Override
    public void checkStatus() {
        nettyClient.checkStatus();
    }

    @Override
    public String toString() {
        return nettyClientConfig.getName();
    }

    public TcpClient getNettyClient() {
        return nettyClient;
    }

    static class ProxyClientInitializer extends ChannelInitializer<SocketChannel> {

        private TcpService tcpService;

        public ProxyClientInitializer(TcpService tcpService) {
            this.tcpService = tcpService;
        }

        @Override
        protected void initChannel(SocketChannel ch) throws Exception {
            ch.pipeline().addLast(new ProtocolCoder());
            ch.pipeline().addLast(new BillingProxyClientHandler(tcpService));
        }

    }
}
