package com.game.billingsr.server.http;

import com.game.billingsr.server.handler.BillingHttpServerHandler;
import com.game.engine.enums.ServerType;
import com.game.engine.io.conf.NettyServerConfig;
import com.game.engine.io.netty.HttpServer;
import com.game.engine.net.GlobalQueueContainerMrg;
import com.game.engine.service.HttpService;
import io.netty.channel.ChannelInitializer;
import io.netty.channel.socket.SocketChannel;
import io.netty.handler.codec.http.HttpObjectAggregator;
import io.netty.handler.codec.http.HttpServerCodec;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class BillingHttpServer extends HttpService {

    private static final Logger LOGGER = LoggerFactory.getLogger(BillingHttpServer.class);

    private final HttpServer nettyHttpServer;
    private final NettyServerConfig nettyServerConfig;

    public BillingHttpServer(NettyServerConfig nettyServerConfig) {
        super(ServerType.HTTP, GlobalQueueContainerMrg.getInstance().getGlobalQueue().getLogicQueue());
        this.nettyServerConfig = nettyServerConfig;
        this.nettyHttpServer = new HttpServer(nettyServerConfig, new BillingHttpServerInitializer(this));
    }

    @Override
    public void start() {
        LOGGER.info(" run ... ");
        nettyHttpServer.start();
    }

    @Override
    public void stop() {
        LOGGER.warn(" stop ... ");
        nettyHttpServer.stop();
    }

    @Override
    public String toString() {
        return nettyServerConfig.getName();
    }

    static class BillingHttpServerInitializer extends ChannelInitializer<SocketChannel> {

        private final HttpService httpService;

        public BillingHttpServerInitializer(HttpService httpService) {
            this.httpService = httpService;
        }

        @Override
        protected void initChannel(SocketChannel ch) {
            ch.pipeline().addLast(new HttpServerCodec());
            ch.pipeline().addLast(new HttpObjectAggregator(1024 * 10));
            ch.pipeline().addLast(new BillingHttpServerHandler(httpService));
        }

    }

}
