<?xml version="1.0" encoding="UTF-8"?>
<Configuration status="WARN">
    <!-- 定义日志文件的 Appender -->
    <Appenders>

        <!-- 控制台日志配置 -->
        <Console name="Console" target="SYSTEM_OUT">
            <PatternLayout>
                <Pattern>%-d{yyyy-MM-dd HH:mm:ss}[%15t] %F:%L - [ %p ] %m%n</Pattern>
            </PatternLayout>
        </Console>

        <!-- INFO 级别的日志配置 -->
        <RollingFile name="InfoFile" fileName="../logs/billing/info/info.log"
                     filePattern="../logs/billing/info/info-%d{yyyy-MM-dd}.log">
            <PatternLayout>
                <Pattern>%-d{yyyy-MM-dd HH:mm:ss}[%15t] %F:%L - [ %p ] %m%n</Pattern>
            </PatternLayout>
            <Policies>
                <TimeBasedTriggeringPolicy interval="1" modulate="true"/>
            </Policies>
            <DefaultRolloverStrategy max="5">
                <Delete basePath="../logs/billing/info" maxDepth="2">
                    <IfFileName glob="info-*.log">
                        <IfLastModified age="30d"/>
                    </IfFileName>
                </Delete>
            </DefaultRolloverStrategy>
        </RollingFile>

        <!-- WARN 级别的日志配置 -->
        <RollingFile name="WarnFile" fileName="../logs/billing/warn/warn.log"
                     filePattern="../logs/billing/warn/warn-%d{yyyy-MM-dd}.log">
            <PatternLayout>
                <Pattern>%-d{yyyy-MM-dd HH:mm:ss}[%15t] %F:%L - [ %p ] %m%n</Pattern>
            </PatternLayout>
            <Policies>
                <TimeBasedTriggeringPolicy interval="1" modulate="true"/>
            </Policies>
            <DefaultRolloverStrategy max="5">
                <Delete basePath="../logs/billing/warn" maxDepth="2">
                    <IfFileName glob="warn-*.log">
                        <IfLastModified age="30d"/>
                    </IfFileName>
                </Delete>
            </DefaultRolloverStrategy>

            <Filters>
                <!--ACCEPT warn级别的日志-->
                <ThresholdFilter level="WARN" onMatch="ACCEPT" onMismatch="DENY"/>
                <!--将INFO及其以上级别的日志给DENY掉-->
                <ThresholdFilter level="INFO" onMatch="DENY" onMismatch="NEUTRAL"/>
            </Filters>
        </RollingFile>

        <!-- ERROR 级别的日志配置 -->
        <RollingFile name="ErrorFile" fileName="../logs/billing/error/error.log"
                     filePattern="../logs/billing/error/error-%d{yyyy-MM-dd}.log">
            <PatternLayout>
                <Pattern>%-d{yyyy-MM-dd HH:mm:ss}[%15t] %F:%L - [ %p ] %m%n</Pattern>
            </PatternLayout>
            <Policies>
                <TimeBasedTriggeringPolicy interval="1" modulate="true"/>
            </Policies>
            <DefaultRolloverStrategy max="5">
                <Delete basePath="../logs/billing/error" maxDepth="2">
                    <IfFileName glob="error-*.log">
                        <IfLastModified age="30d"/>
                    </IfFileName>
                </Delete>
            </DefaultRolloverStrategy>

            <Filters>
                <!--ACCEPT error级别的日志-->
                <ThresholdFilter level="ERROR" onMatch="ACCEPT" onMismatch="DENY"/>
                <!--将INFO及其以上级别的日志给DENY掉-->
                <ThresholdFilter level="INFO" onMatch="DENY" onMismatch="NEUTRAL"/>
            </Filters>
        </RollingFile>

    </Appenders>

    <!-- 定义 Logger 配置 -->
    <Loggers>
        <!-- Root Logger 配置，INFO 级别 -->
        <Root level="info">
            <AppenderRef ref="Console"/>
            <AppenderRef ref="InfoFile"/>
            <AppenderRef ref="WarnFile"/>
            <AppenderRef ref="ErrorFile"/>
        </Root>

        <!-- 屏蔽 ReconnectionHandler 输出 -->
        <Logger name="io.lettuce.core.protocol" level="off" additivity="false">
            <AppenderRef ref="Console"/>
            <AppenderRef ref="InfoFile"/>
        </Logger>

        <Logger name="org.mongodb.driver" level="off" additivity="false">
            <AppenderRef ref="Console"/>
            <AppenderRef ref="InfoFile"/>
        </Logger>

        <AsyncLogger name="com.game" level="info" additivity="false">
            <AppenderRef ref="Console"/>
            <AppenderRef ref="InfoFile"/>
            <AppenderRef ref="WarnFile"/>
            <AppenderRef ref="ErrorFile"/>
        </AsyncLogger>
    </Loggers>
</Configuration>