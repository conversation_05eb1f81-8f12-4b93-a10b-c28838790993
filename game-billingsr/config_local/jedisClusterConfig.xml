<?xml version="1.0" encoding="UTF-8"?>
<JedisClusterConfig>
    <nodes>
        <JedisClusterNodesConfig>
            <ip>${REDIS_IP_1}</ip>
            <port>${port}</port>
        </JedisClusterNodesConfig>
        <JedisClusterNodesConfig>
            <ip>${REDIS_IP_2}</ip>
            <port>${port}</port>
        </JedisClusterNodesConfig>
        <JedisClusterNodesConfig>
            <ip>${REDIS_IP_3}</ip>
            <port>${port}</port>
        </JedisClusterNodesConfig>
    </nodes>
    <auth>${REDIS_PD}</auth>
    <mode>${mode}</mode>
    <poolMaxTotal>1000</poolMaxTotal>//最大连接数
    <poolMaxIdle>100</poolMaxIdle>//最大空闲连接数
    <poolMinIdle>50</poolMinIdle>
    <maxWaitMillis>500</maxWaitMillis>
    <testOnBorrow>true</testOnBorrow>
    <testWhileIdle>true</testWhileIdle>
    <minEvictableIdleTimeMillis>60000</minEvictableIdleTimeMillis>
    <timeBetweenEvictionRunsMillis>30000</timeBetweenEvictionRunsMillis>
    <connectionTimeout>2000</connectionTimeout>
</JedisClusterConfig>