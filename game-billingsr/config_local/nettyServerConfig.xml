<?xml version="1.0" encoding="UTF-8"?>
<nettyServerConfig>
	<id>${BILLINGID}</id>
	<name>充值服务器</name>
	<port>8600</port>
	<sendBufferSize>2048</sendBufferSize>
	<receiveBufferSize>8196</receiveBufferSize>
	<reuseAddress>true</reuseAddress>
	<tcpNoDelay>true</tcpNoDelay>
	<readerIdleTime>180</readerIdleTime>
	<writerIdleTime>180</writerIdleTime>
	<type>BILLING</type>
	<kafka_connection_string>${kafka.url}</kafka_connection_string>
	<mongo_connection_string>${mongo.url}</mongo_connection_string>
	<mongo_read_write_database>${mongo.db}</mongo_read_write_database>
</nettyServerConfig>