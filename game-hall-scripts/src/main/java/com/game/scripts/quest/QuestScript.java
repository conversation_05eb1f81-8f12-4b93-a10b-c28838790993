package com.game.scripts.quest;

import com.game.c_entity.merchant.C_Quest;
import com.game.dao.player.PlayerDao;
import com.game.dao.quest.QuestNoteDao;
import com.game.engine.log.GameLog;
import com.game.engine.math.BigDecimalUtils;
import com.game.engine.script.ScriptLoader;
import com.game.engine.utils.GuidGeneratorUtils;
import com.game.engine.utils.TimeUtil;
import com.game.engine.utils.UniqueIDGenerator;
import com.game.entity.player.FreeGameInfo;
import com.game.entity.player.GameInfo;
import com.game.entity.player.Player;
import com.game.entity.player.bonus.BonusDetailsInfo;
import com.game.entity.player.bonus.BonusInfo;
import com.game.entity.player.quest.QuestInfo;
import com.game.entity.player.quest.QuestNote;
import com.game.entity.player.quest.SingleQuestInfo;
import com.game.enums.BonusDetail;
import com.game.enums.RewardReason;
import com.game.enums.TransactionFrom;
import com.game.enums.TurnoverReason;
import com.game.hall.main.HallServer;
import com.game.hall.mrg.currency.CurrencyMrg;
import com.game.hall.mrg.currency.RewardRequest;
import com.game.hall.script.IBonusScript;
import com.game.hall.script.IPlayerScript;
import com.game.hall.script.IQuestScript;
import com.game.manager.EntityDaoMrg;
import it.unimi.dsi.fastutil.ints.IntLists;

public class QuestScript implements IQuestScript {

    @Override
    public void questReceive(Player player, C_Quest c_quest, SingleQuestInfo singleQuestInfo) {
        //任务记录
        final UniqueIDGenerator uniqueIDGenerator = HallServer.getInstance().getUniqueIDGenerator();
        final QuestNote questNote = new QuestNote(uniqueIDGenerator.nextId());
        questNote.setBusiness_no(player.getBusiness_no());
        questNote.setPlayerId(player.getPlayerId());
        questNote.setQuestId(singleQuestInfo.getQuestId());
        questNote.setState(singleQuestInfo.getState());
        questNote.setFinishedTime(singleQuestInfo.getFinishedTime());
        EntityDaoMrg.getInstance().getDao(QuestNoteDao.class).insert(questNote);

        final RewardRequest rewardRequest = new RewardRequest();
        rewardRequest.addCurrency(c_quest.getRewardCurrency().currencyId, c_quest.getRewardCurrency().amount);
        final RewardReason rewardReason = RewardReason.Quest;
        rewardReason.setSource(c_quest.getQuestId() + "");
        CurrencyMrg.getInstance().rewardNoTest(player, rewardRequest.getBonusCurrencyMap(), rewardReason);

        final QuestInfo questInfo = player.getQuestInfo();
        questInfo.incAccumulatedRewards(c_quest.getRewardCurrency().currencyId, c_quest.getRewardCurrency().amount);
        EntityDaoMrg.getInstance().getDao(PlayerDao.class).questDao
                .updateAccumulatedRewards(player.getPlayerId(), questInfo);

        //TODO 免费游戏次数
        final C_Quest.RewardFreeGame rewardFreeGame = c_quest.getRewardFreeGame();
        if (rewardFreeGame != null && rewardFreeGame.gameId > 0) {
            final FreeGameInfo freeGameInfo = player.getFreeGameInfo(rewardFreeGame.currencyId);
            final GameInfo gameInfo = freeGameInfo.getFreeGame(rewardFreeGame.gameId);
            gameInfo.incFreeTimes(rewardFreeGame.freeTimes);
            gameInfo.setBet(rewardFreeGame.bet);
            gameInfo.setMinWithdraw(rewardFreeGame.minWithdraw);
            gameInfo.setMaxWithdraw(rewardFreeGame.maxWithdraw);
            EntityDaoMrg.getInstance().getDao(PlayerDao.class)
                    .updateFreeGameInfo(player, IntLists.singleton(rewardFreeGame.currencyId));
        }

        //TODO 打码
//        final WithdrawStandard withdrawStandard = player.getWithdrawStandard(c_quest.getRewardCurrency().currencyId);
//        withdrawStandard.incDrawStandard(BigDecimalUtils.mul(c_quest.getRewardCurrency().amount, c_quest.getTurnoverMul(), 4));
        final TurnoverReason turnoverReason = TurnoverReason.Quest;
        turnoverReason.setSource(c_quest.getQuestId() + "");
        ScriptLoader.getInstance().consumerScript("PlayerWithdrawStandardScript",
                (IPlayerScript script) -> script.drawStandard(player, turnoverReason, c_quest.getRewardCurrency().currencyId, c_quest.getRewardCurrency().amount,
                        BigDecimalUtils.mul(c_quest.getRewardCurrency().amount, c_quest.getTurnoverMul(), 4)));

        final C_Quest.DescInfo descInfo = c_quest.getDescInfoMap().get(player.getLanguage());
        final GameLog playerQuestLog = new GameLog("platform_playerQuestLog");
        playerQuestLog.append("number", GuidGeneratorUtils.generateOrderId())
                .append("business_no", player.getBusiness_no())
                .append("site", player.getWebSite())
                .append("playerId", player.getPlayerId())
                .append("playerName", player.getPlayerName())
                .append("questId", singleQuestInfo.getQuestId())
                .append("questType", c_quest.getQuestType())//任务类型
                .append("goalType", c_quest.getGoalType())//任务目标
                .append("agentId", player.getAgentId())
                .append("channelId", player.getChannelId())
                .append("region", player.getRegisterRegion())
                .append("currencyId", c_quest.getRewardCurrency().currencyId)
                .append("amount", c_quest.getRewardCurrency().amount)
                .append("logTime", TimeUtil.currentTimeMillis());
        if (descInfo != null) {
            playerQuestLog.append("questName", descInfo.getQuestName());
        }
        HallServer.getInstance().getLogProducerMrg().send(playerQuestLog);

        final BonusInfo bonusInfo = player.getBonusInfo();
        final BonusDetailsInfo bonusDetailsInfo = bonusInfo.getBonusDetailsInfo(BonusDetail.Quests.getType());
        bonusDetailsInfo.incBonus(c_quest.getRewardCurrency().currencyId, c_quest.getRewardCurrency().amount);
        EntityDaoMrg.getInstance().getDao(PlayerDao.class).bonusInfoDao
                .updateBonusDetails(player, BonusDetail.Quests.getType(), bonusDetailsInfo, IntLists.singleton(c_quest.getRewardCurrency().currencyId));

        TransactionFrom transaction;
        if (c_quest.getQuestType() == 1) {//每日
            transaction = TransactionFrom.Quest_Daily;
        } else {
            transaction = TransactionFrom.Quest_Weekly;
        }
        ScriptLoader.getInstance().consumerScript("BonusScript", (IBonusScript script) ->
                script.addBonusNote(transaction, player, c_quest.getRewardCurrency().currencyId, c_quest.getRewardCurrency().amount));
    }
}
