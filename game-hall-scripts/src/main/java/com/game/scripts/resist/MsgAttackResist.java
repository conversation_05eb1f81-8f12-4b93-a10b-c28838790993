package com.game.scripts.resist;

import com.game.engine.io.conf.ConstantConfig;
import com.game.engine.net.LogicEvent;
import com.game.engine.utils.Config;
import com.game.engine.utils.FileUtil;
import com.game.engine.utils.MsgUtil;
import com.game.engine.utils.TimeUtil;
import com.game.hall.mrg.UdpMsgAttackResistMrg;
import com.game.hall.script.IMsgAttackResist;
import com.game.user.ResistUserMsg;
import com.game.utils.VirtualThreadUtils;
import io.netty.channel.Channel;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.File;
import java.io.IOException;

public class MsgAttackResist implements IMsgAttackResist {
    private static final Logger LOGGER = LoggerFactory.getLogger(MsgAttackResist.class);

    @Override
    public boolean checkMessageResist(LogicEvent evt) {
        if (ConstantConfig.getInstance().isDebug()) {
            return false;
        }

        final Channel channel = evt.getChannel();
        final String ip = MsgUtil.getClientIp(channel);

        if (UdpMsgAttackResistMrg.getInstance().getIpBlacklist().contains(ip)) {
            return true;
        }

        ResistUserMsg msg = UdpMsgAttackResistMrg.getInstance().getResistUserMsgMap().putIfAbsent(ip, new ResistUserMsg());
        if (msg == null) {
            msg = UdpMsgAttackResistMrg.getInstance().getResistUserMsgMap().get(ip);
        }

        msg.incSendTimes();
        if (TimeUtil.currentTimeMillis() < msg.getStartTime() + TimeUtil.MIN) {
            return limitAccess(channel, msg, ip);
        }

        final boolean isCheck = limitAccess(channel, msg, ip);
        msg.reset();
        return isCheck;
    }

    private boolean limitAccess(Channel channel, ResistUserMsg msg, String ip) {
        if (msg.getSendTimes() >= 150) {
            UdpMsgAttackResistMrg.getInstance().getResistUserMsgMap().remove(ip);
            UdpMsgAttackResistMrg.getInstance().getIpBlacklist().add(ip);
            LOGGER.warn("msg too many requests");
            VirtualThreadUtils.execute(() -> {
                try {
                    FileUtil.writerTxtFile(Config.path + File.separatorChar + "IP_blacklist.text", ip);
                } catch (IOException e) {
                    throw new RuntimeException(e);
                }
            });
            return true;
        }
        return false;
    }

}
