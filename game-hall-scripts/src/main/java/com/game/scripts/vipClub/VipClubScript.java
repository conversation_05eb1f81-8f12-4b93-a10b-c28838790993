package com.game.scripts.vipClub;

import com.game.c_entity.merchant.C_ReferralReward;
import com.game.c_entity.merchant.C_VipClub;
import com.game.c_entity.middleplatform.C_BaseExchangeRate;
import com.game.dao.player.PlayerDao;
import com.game.dao.player.PlayerPromoteDao;
import com.game.dao.promote.ReferralRewardsNoteDao;
import com.game.engine.log.GameLog;
import com.game.engine.math.BigDecimalUtils;
import com.game.engine.mongo.DBConnectionMrg;
import com.game.engine.utils.*;
import com.game.entity.player.Player;
import com.game.entity.player.bonus.BonusInfo;
import com.game.entity.player.bonus.BonusProcessInfo;
import com.game.entity.player.vip.VipClub;
import com.game.entity.player.promote.PlayerPromote;
import com.game.entity.player.promote.PlayerPromoteFields;
import com.game.entity.player.promote.ReferralRewards;
import com.game.entity.player.promote.ReferralRewardsNote;
import com.game.enums.*;
import com.game.hall.main.HallServer;
import com.game.hall.mrg.DataHallMrg;
import com.game.hall.mrg.MerchantData;
import com.game.hall.mrg.player.PlayerMrg;
import com.game.hall.script.IVipClubScript;
import com.game.manager.EntityDaoMrg;
import com.proto.MIDMessage;
import com.proto.TcpMessage;
import io.netty.util.internal.StringUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.mongodb.core.query.Update;

public class VipClubScript implements IVipClubScript {
    private static final Logger LOGGER = LoggerFactory.getLogger(VipClubScript.class);

    @Override
    public void addExp(Player player, int currencyId, double validBet) {
        try {
            if (validBet <= 0) {
                return;
            }

            final MerchantData merchantData = DataHallMrg.getInstance().findMerchantData(this.getClass().getSimpleName(), player.getBusiness_no());
            if (merchantData == null) {
                return;
            }

            final boolean isManyCurrency = merchantData.getC_currencyMap().size() > 1;

            final VipClub vipClub = player.getVipClub();
            double totalExp = 0;
            if (isManyCurrency) {
                final C_BaseExchangeRate c_baseExchangeRate = DataHallMrg.getInstance().findC_BaseExchangeRate(this.getClass().getSimpleName(), currencyId);
                if (c_baseExchangeRate == null) {
                    return;
                }
                totalExp = BigDecimalUtils.add(vipClub.getCurExp(), BigDecimalUtils.mul(c_baseExchangeRate.getExchangeRate(), validBet, 9), 9);
            } else {
                totalExp = BigDecimalUtils.add(vipClub.getCurExp(), validBet, 9);
            }

            if (totalExp < 0) {
                throw new IllegalArgumentException("addExp: " + validBet);
            }
            vipClub.setCurExp(totalExp);
            vipClub.incRelegationWagered(totalExp);
            syncExpInfo(player, 0, merchantData, false);

            checkLevelUp(player, merchantData);

            //TODO vip工资日志
            sendPlayerVipSalaryLog(player);

            vipClub.incDailyWagered(currencyId, validBet);
            vipClub.incWeeklyWagered(currencyId, validBet);
            vipClub.incMonthlyWagered(currencyId, validBet);
        } catch (Exception e) {
            LOGGER.error("VipClubScript，addExp", e);
        }
    }

    @Override
    public void addRecharge(Player player, int currencyId, double recharge) {
        try {
            if (recharge <= 0) {
                return;
            }

            final MerchantData merchantData = DataHallMrg.getInstance().findMerchantData(this.getClass().getSimpleName(), player.getBusiness_no());
            if (merchantData == null) {
                return;
            }

            final boolean isManyCurrency = merchantData.getC_currencyMap().size() > 1;

            final VipClub vipClub = player.getVipClub();
            double totalExp = 0;
            if (isManyCurrency) {
                final C_BaseExchangeRate c_baseExchangeRate = DataHallMrg.getInstance().findC_BaseExchangeRate(this.getClass().getSimpleName(), currencyId);
                if (c_baseExchangeRate == null) {
                    return;
                }
                totalExp = BigDecimalUtils.add(vipClub.getCurRecharge(), BigDecimalUtils.mul(c_baseExchangeRate.getExchangeRate(), recharge, 9), 9);
            } else {
                totalExp = BigDecimalUtils.add(vipClub.getCurRecharge(), recharge, 9);
            }

            if (totalExp < 0) {
                throw new IllegalArgumentException("addRecharge: " + recharge);
            }
            vipClub.setCurRecharge(totalExp);
            vipClub.incRelegationRecharge(totalExp);
            vipClub.setActivation(true);
            if (vipClub.getActivationTime() == 0) {
                vipClub.setActivationTime(TimeUtil.currentTimeMillis());
            }
            syncExpInfo(player, 0, merchantData, false);

            checkLevelUp(player, merchantData);

            //TODO vip工资日志
            sendPlayerVipSalaryLog(player);

            final GameLog playerVipSignInLog = new GameLog("platform_playerVipSignInLog");
            playerVipSignInLog.append("number", GuidGeneratorUtils.generateOrderId())
                    .append("business_no", player.getBusiness_no())
                    .append("channel", player.getChannel())
                    .append("site", player.getWebSite())
                    .append("playerId", player.getPlayerId())
                    .append("playerName", player.getPlayerName())
                    .append("agentId", player.getAgentId())
                    .append("channelId", player.getChannelId())
                    .append("region", player.getRegisterRegion())
                    .append("logTime", TimeUtil.currentTimeMillis())
                    .append("vipLevel", vipClub.getVipLevel())
                    .append("type", 2)//1.领取 2.激活
                    .append("currDay", 0)
                    .append("currencyId", 0)
                    .append("reward", 0);
            HallServer.getInstance().getLogProducerMrg().send(playerVipSignInLog);
        } catch (Exception e) {
            LOGGER.error("VipClubScript，addRecharge", e);
        }
    }

    private void checkLevelUp(Player player, MerchantData merchantData) {
        final VipClub vipClub = player.getVipClub();
        final C_VipClub c_vip = merchantData.findC_VipClub(this.getClass().getSimpleName(), vipClub.getVipLevel());
        if (c_vip == null) {
            return;
        }
        if (vipClub.getCurExp() < c_vip.getNeedExp()) {
            // 多数情况下都不会升级
            return;
        }

        if (vipClub.getCurRecharge() < c_vip.getNeedRecharge()) {
            // 多数情况下都不会升级
            return;
        }

        // 计算等级更新
        final int preLevel = vipClub.getVipLevel();
        final int maxLevel = merchantData.getC_vipClubMap().size() - 1;
        while (true) {
            final C_VipClub c_vipLevel = merchantData.findC_VipClub(this.getClass().getSimpleName(), vipClub.getVipLevel());
            if (c_vipLevel == null) {
                break;
            }

            if (vipClub.getCurExp() < c_vipLevel.getNeedExp()) {
                break;
            }

            if (vipClub.getCurRecharge() < c_vipLevel.getNeedRecharge()) {
                break;
            }

            // 满级
            if (vipClub.getVipLevel() >= maxLevel) {
                break;
            }

            if (c_vipLevel.getRelegationCycle() > 0) {
                vipClub.setRelegationTime(TimeUtil.currentTimeMillis() + c_vipLevel.getRelegationCycle() * TimeUtil.DAY);
            }

            // 改变等级和经验
            vipClub.setVipLevel(vipClub.getVipLevel() + 1);
            vipClub.setVipUpdateTime(TimeUtil.currentTimeMillis());
            vipClub.setCurExp(BigDecimalUtils.sub(vipClub.getCurExp(), c_vipLevel.getNeedExp(), 4));
            vipClub.setCurRecharge(BigDecimalUtils.sub(vipClub.getCurRecharge(), c_vipLevel.getNeedRecharge(), 4));
        }

        if (preLevel != vipClub.getVipLevel()) {
            syncExpInfo(player, preLevel, merchantData, true);
        }
    }

    private void syncExpInfo(Player player, int preLevel, MerchantData merchantData, boolean levelChanged) {
        final VipClub vipClub = player.getVipClub();
        final C_VipClub c_vipClub = merchantData.findC_VipClub(this.getClass().getSimpleName(), vipClub.getVipLevel());

        final TcpMessage.ResTcpVipClubExpChangeMessage.Builder res = TcpMessage.ResTcpVipClubExpChangeMessage.newBuilder();
        res.setMsgID(MIDMessage.MID.ResTcpVipClubExpChange_VALUE)
                .setCurExp(vipClub.getCurExp())
                .setCurRecharge(vipClub.getCurRecharge());
        if (levelChanged) {
            res.setVipLevel(vipClub.getVipLevel());
            if (c_vipClub != null) {
                res.setNeedExp(c_vipClub.getNeedExp())
                        .setNeedRecharge(c_vipClub.getNeedRecharge());
            }
        }
        player.sendMsg(res.build());

        if (levelChanged) {
            PlayerPromote playerPromote = player.getPlayerPromote();
            playerPromote.setVipLevel(vipClub.getVipLevel());
            {
                final Update update = new Update();
                update.set(PlayerPromoteFields.vipLevel, playerPromote.getVipLevel());
                EntityDaoMrg.getInstance().getDao(PlayerPromoteDao.class)
                        .updatePromotion(player.getPlayerId(), update);
            }

            //升级奖励
            if (c_vipClub != null) {
                final String upgradeRewardsOpen = merchantData.findC_GlobalValue(this.getClass().getSimpleName(), "upgradeRewardsOpen");
                if (!StringUtil.isNullOrEmpty(upgradeRewardsOpen) && Boolean.parseBoolean(upgradeRewardsOpen)) {
                    final boolean isManyCurrency = merchantData.getC_currencyMap().size() > 1;
                    preLevel += 1;
                    for (int i = preLevel; i <= vipClub.getVipLevel(); i++) {
                        final C_VipClub c_vipClubReward = merchantData.findC_VipClub(this.getClass().getSimpleName(), i);
                        if (c_vipClubReward == null) {
                            continue;
                        }
                        final String upLevelReward = c_vipClubReward.getUpLevelReward();
                        if (!StringUtil.isNullOrEmpty(upLevelReward)) {
                            final String[] mh = upLevelReward.split(Symbol.MAOHAO_REG);
                            if (mh.length > 0) {
                                final BonusInfo bonusInfo = player.getBonusInfo();
                                final BonusProcessInfo bonusProcessInfo = bonusInfo.getBonusProcessInfo(BonusDetail.LevelUpBonus.getType());
                                if (isManyCurrency) {
                                    final C_BaseExchangeRate c_baseExchangeRate = DataHallMrg.getInstance().findC_BaseExchangeRate(this.getClass().getSimpleName(), Integer.parseInt(mh[0]));
                                    bonusProcessInfo.incReward(Integer.parseInt(mh[0]), Double.parseDouble(mh[1]) * (1 / c_baseExchangeRate.getExchangeRate()));
                                } else {
                                    bonusProcessInfo.incReward(Integer.parseInt(mh[0]), Double.parseDouble(mh[1]));
                                }
                            }
                        }
                    }
                }
            }

            final C_ReferralReward c_referralReward = merchantData.findC_ReferralReward(this.getClass().getSimpleName(), vipClub.getVipLevel());
            if (c_referralReward != null) {
                {
                    final ReferralRewards referralRewards = playerPromote.getReferralRewards();
                    referralRewards.setCurrencyId(c_referralReward.getCurrencyId());
                    referralRewards.incAvailable(c_referralReward.getUnLockReward());
                    referralRewards.incTotalReceived(c_referralReward.getUnLockReward());

                    final Update update = new Update();
                    update.set(PlayerPromoteFields.referralRewards,
                            DBConnectionMrg.getInstance().getConvertMrg().writeNoTypeKey(referralRewards));
                    EntityDaoMrg.getInstance().getDao(PlayerPromoteDao.class)
                            .updatePromotion(playerPromote.getPlayerId(), update);

                    final UniqueIDGenerator uniqueIDGenerator = HallServer.getInstance().getUniqueIDGenerator();
                    final ReferralRewardsNote referralRewardsNote = new ReferralRewardsNote(uniqueIDGenerator.nextId());
                    referralRewardsNote.setSuperiorId(playerPromote.getSuperiorId());
                    referralRewardsNote.setPlayerId(player.getPlayerId());
                    referralRewardsNote.setPlayerName(player.getPlayerName());
                    referralRewardsNote.setStatus(1);
                    referralRewardsNote.setBusiness_no(player.getBusiness_no());
                    referralRewardsNote.setCurrencyId(c_referralReward.getCurrencyId());
                    referralRewardsNote.setAmount(c_referralReward.getUnLockReward());
                    EntityDaoMrg.getInstance().getDao(ReferralRewardsNoteDao.class).insert(referralRewardsNote);
                }

                {
                    //上级
                    if (playerPromote.getSuperiorId() == 0) {
                        return;
                    }
                    final PlayerPromote superior = PlayerMrg.getInstance().findDbPlayerPromote(playerPromote.getSuperiorId());
                    if (superior == null) {
                        return;
                    }
                    final ReferralRewards availableRewards = superior.getAvailableRewards();
                    availableRewards.setCurrencyId(c_referralReward.getCurrencyId());
                    availableRewards.incAvailable(c_referralReward.getUnLockReward());
                    availableRewards.incTotalReceived(c_referralReward.getUnLockReward());

                    final Update update = new Update();
                    update.set(PlayerPromoteFields.availableRewards,
                            DBConnectionMrg.getInstance().getConvertMrg().writeNoTypeKey(availableRewards));

                    EntityDaoMrg.getInstance().getDao(PlayerPromoteDao.class)
                            .updatePromotion(playerPromote.getSuperiorId(), update);
                }
            }
        }
    }

    @Override
    public void vipCheckRelegation(Player player) {
        final VipClub vipClub = player.getVipClub();
        if (vipClub.getRelegationTime() == 0 || TimeUtil.currentTimeMillis() < vipClub.getRelegationTime()) {
            return;
        }

        final MerchantData merchantData = DataHallMrg.getInstance().findMerchantData(this.getClass().getSimpleName(), player.getBusiness_no());
        if (merchantData == null) {
            return;
        }

        final C_VipClub c_vipClub = merchantData.findC_VipClub(this.getClass().getSimpleName(), vipClub.getVipLevel());
        if (c_vipClub == null) {
            return;
        }

        if (vipClub.getRelegationWagered() >= c_vipClub.getRelegationWagered() && vipClub.getRelegationRecharge() >= c_vipClub.getRelegationRecharge()) {
            vipClub.relegationReset(c_vipClub.getRelegationCycle());
            return;
        }

        vipClub.setVipLevel(vipClub.getVipLevel() - 1);
        final C_VipClub c_vipClubLow = merchantData.findC_VipClub(this.getClass().getSimpleName(), vipClub.getVipLevel());
        if (c_vipClubLow != null) {
            vipClub.relegationReset(c_vipClubLow.getRelegationCycle());
        }
    }

    @Override
    public void resetVipSignIn(Player player) {
        final VipClub vipClub = player.getVipClub();
        final int currDay = TimeUtil.getNaturalDays(TimeUtil.currentTimeMillis(), vipClub.getActivationTime(), player.getTimeZone());
        if (currDay <= 7) {
            return;
        }
        vipClub.setActivation(false);
        vipClub.setActivationTime(0);
        vipClub.getReceiveDays().clear();
        EntityDaoMrg.getInstance().getDao(PlayerDao.class)
                .vipClubDao.updateReceiveDays(player.getPlayerId(), vipClub);
    }


    @Override
    public void resetDaily(Player player) {
        final VipClub vipClub = player.getVipClub();
        vipClub.getReceiveReward().remove(1);
        vipClub.getDailyWageredMap().clear();
        EntityDaoMrg.getInstance().getDao(PlayerDao.class)
                .vipClubDao.updateDailyWagered(player.getPlayerId(), vipClub);
    }

    @Override
    public void resetWeekly(Player player) {
        final VipClub vipClub = player.getVipClub();
        vipClub.getReceiveReward().remove(2);
        vipClub.getWeeklyWageredMap().clear();
        EntityDaoMrg.getInstance().getDao(PlayerDao.class)
                .vipClubDao.updateWeeklyWagered(player.getPlayerId(), vipClub);
    }

    @Override
    public void resetMonthly(Player player) {
        final VipClub vipClub = player.getVipClub();
        vipClub.getReceiveReward().remove(3);
        vipClub.getMonthlyWageredMap().clear();
        EntityDaoMrg.getInstance().getDao(PlayerDao.class)
                .vipClubDao.updateMonthlyWagered(player.getPlayerId(), vipClub);
    }

    private void sendPlayerVipSalaryLog(Player player) {
        final VipClub vipClub = player.getVipClub();
        final MerchantData merchantData = DataHallMrg.getInstance().findMerchantData(this.getClass().getSimpleName(), player.getBusiness_no());
        if (merchantData == null) {
            return;
        }
        final C_VipClub c_vipClub = merchantData.findC_VipClub(this.getClass().getSimpleName(), vipClub.getVipLevel());
        if (c_vipClub == null) {
            return;
        }

        for (C_VipClub.ReceiveReward receiveReward : c_vipClub.getReceiveRewardMap().values()) {
            if (vipClub.getReceiveReward().contains(receiveReward.id)) {
                continue;
            }

            boolean isSend = false;
            switch (receiveReward.id) {
                case 1:
                    final double dailyWagered = vipClub.getDailyWageredMap().getOrDefault(receiveReward.getCurrencyId(), 0d);
                    if (dailyWagered < receiveReward.getWagered()) {
                        break;
                    }
                    isSend = true;
                    break;
                case 2:
                    final double weeklyWagered = vipClub.getWeeklyWageredMap().getOrDefault(receiveReward.getCurrencyId(), 0d);
                    if (weeklyWagered < receiveReward.getWagered()) {
                        break;
                    }
                    isSend = true;
                    break;
                case 3:
                    final double monthlyWagered = vipClub.getMonthlyWageredMap().getOrDefault(receiveReward.getCurrencyId(), 0d);
                    if (monthlyWagered < receiveReward.getWagered()) {
                        break;
                    }
                    isSend = true;
                    break;
            }

            if (!isSend) {
                return;
            }
            final GameLog playerVipSalaryLog = new GameLog("platform_playerVipSalaryLog");
            playerVipSalaryLog.append("number", GuidGeneratorUtils.generateOrderId())
                    .append("business_no", player.getBusiness_no())
                    .append("channel", player.getChannel())
                    .append("site", player.getWebSite())
                    .append("playerId", player.getPlayerId())
                    .append("playerName", player.getPlayerName())
                    .append("agentId", player.getAgentId())
                    .append("channelId", player.getChannelId())
                    .append("region", player.getRegisterRegion())
                    .append("logTime", TimeUtil.currentTimeMillis())
                    .append("type", 2)//1.领取 2.激活
                    .append("rewardType", receiveReward.id)//1.日 2.周 3.月
                    .append("vipLevel", vipClub.getVipLevel())
                    .append("turnover", BigDecimalUtils.mul(receiveReward.reward, receiveReward.turnoverMul, 4))
                    .append("currencyId", receiveReward.currencyId)
                    .append("reward", receiveReward.reward);
            HallServer.getInstance().getLogProducerMrg().send(playerVipSalaryLog);
        }
    }

}
