package com.game.scripts.affiliate;

import com.game.c_entity.merchant.C_CashBack;
import com.game.engine.math.BigDecimalUtils;
import com.game.engine.utils.FastCollectionsUtils;
import com.game.entity.game.GameCommissionStat;
import com.game.entity.game.GameWagerStat;
import com.game.hall.mrg.MerchantData;
import com.game.hall.script.IAffiliateScript;
import it.unimi.dsi.fastutil.ints.Int2ObjectMap;
import it.unimi.dsi.fastutil.objects.ObjectIterator;

import java.util.Iterator;

public class AffiliateScript implements IAffiliateScript {

    /**
     * Casino_Original(101, "原创"),
     * Casino_Slots(102, "电子"),
     * Casino_Live(103, "赌场视讯"),
     * Casino_Poker(104, "棋牌牌桌"),
     * Casino_Fish(105, "捕鱼"),
     * Casino_Arcade(106, "街机"),
     * Casino_Bingo(107, "宾果"),
     *
     * @param gameWagerStat
     * @param merchantData
     * @return
     */
    @Override
    public GameCommissionStat calculateSuperiorCommissionStat(GameWagerStat gameWagerStat, MerchantData merchantData) {
        double commissionOriginal = 0;
        double commission3rdParty = 0;
        double commissionSports = 0;

        final String rebateMethod = merchantData.findC_GlobalValue(this.getClass().getSimpleName(), "rebateMethod");
        if (Integer.parseInt(rebateMethod) == 4) {//佣金
            Iterator<C_CashBack> iterator = merchantData.getC_cashBackMap().values().iterator();
            while (iterator.hasNext()) {
                final C_CashBack c_cashBack = iterator.next();
                switch (c_cashBack.getGameType()) {
                    case 101:
                        commissionOriginal = BigDecimalUtils.mul(BigDecimalUtils.mul(BigDecimalUtils.mul(gameWagerStat.getTotalWagerOriginal(), c_cashBack.getEffectiveRate(), 9), 9), BigDecimalUtils.mul(c_cashBack.getSuperiorCashBackRate(), c_cashBack.getGameCashBackRate(), 9), 9);
                        break;
                    case 102:
                        final double slots = BigDecimalUtils.mul(BigDecimalUtils.mul(gameWagerStat.getTotalWagerSlots(), c_cashBack.getEffectiveRate(), 9), BigDecimalUtils.mul(c_cashBack.getSuperiorCashBackRate(), c_cashBack.getGameCashBackRate(), 9), 9);
                        commission3rdParty = BigDecimalUtils.add(commission3rdParty, slots, 9);
                        break;
                    case 103:
                        final double live = BigDecimalUtils.mul(BigDecimalUtils.mul(gameWagerStat.getTotalWagerLive(), c_cashBack.getEffectiveRate(), 9), BigDecimalUtils.mul(c_cashBack.getSuperiorCashBackRate(), c_cashBack.getGameCashBackRate(), 9), 9);
                        commission3rdParty = BigDecimalUtils.add(commission3rdParty, live, 9);
                        break;
                    case 104:
                        final double poker = BigDecimalUtils.mul(BigDecimalUtils.mul(gameWagerStat.getTotalWagerPoker(), c_cashBack.getEffectiveRate(), 9), BigDecimalUtils.mul(c_cashBack.getSuperiorCashBackRate(), c_cashBack.getGameCashBackRate(), 9), 9);
                        commission3rdParty = BigDecimalUtils.add(commission3rdParty, poker, 9);
                        break;
                    case 105:
                        final double fish = BigDecimalUtils.mul(BigDecimalUtils.mul(gameWagerStat.getTotalWagerFish(), c_cashBack.getEffectiveRate(), 9), BigDecimalUtils.mul(c_cashBack.getSuperiorCashBackRate(), c_cashBack.getGameCashBackRate(), 9), 9);
                        commission3rdParty = BigDecimalUtils.add(commission3rdParty, fish, 9);
                        break;
                    case 106:
                        final double arcade = BigDecimalUtils.mul(BigDecimalUtils.mul(gameWagerStat.getTotalWagerArcade(), c_cashBack.getEffectiveRate(), 9), BigDecimalUtils.mul(c_cashBack.getSuperiorCashBackRate(), c_cashBack.getGameCashBackRate(), 9), 9);
                        commission3rdParty = BigDecimalUtils.add(commission3rdParty, arcade, 9);
                        break;
                    case 107:
                        final double bingo = BigDecimalUtils.mul(BigDecimalUtils.mul(gameWagerStat.getTotalWagerBingo(), c_cashBack.getEffectiveRate(), 9), BigDecimalUtils.mul(c_cashBack.getSuperiorCashBackRate(), c_cashBack.getGameCashBackRate(), 9), 9);
                        commission3rdParty = BigDecimalUtils.add(commission3rdParty, bingo, 9);
                        break;
                }
            }
        } else {//有效
            Iterator<C_CashBack> iterator = merchantData.getC_cashBackMap().values().iterator();
            while (iterator.hasNext()) {
                final C_CashBack c_cashBack = iterator.next();
                switch (c_cashBack.getGameType()) {
                    case 101:
                        commissionOriginal = BigDecimalUtils.mul(BigDecimalUtils.mul(gameWagerStat.getTotalWagerOriginal(), c_cashBack.getEffectiveRate(), 9), c_cashBack.getSuperiorCashBackRate(), 9);
                        break;
                    case 102:
                        final double slots = BigDecimalUtils.mul(BigDecimalUtils.mul(gameWagerStat.getTotalWagerSlots(), c_cashBack.getEffectiveRate(), 9), c_cashBack.getSuperiorCashBackRate(), 9);
                        commission3rdParty = BigDecimalUtils.add(commission3rdParty, slots, 9);
                        break;
                    case 103:
                        final double live = BigDecimalUtils.mul(BigDecimalUtils.mul(gameWagerStat.getTotalWagerLive(), c_cashBack.getEffectiveRate(), 9), c_cashBack.getSuperiorCashBackRate(), 9);
                        commission3rdParty = BigDecimalUtils.add(commission3rdParty, live, 9);
                        break;
                    case 104:
                        final double poker = BigDecimalUtils.mul(BigDecimalUtils.mul(gameWagerStat.getTotalWagerPoker(), c_cashBack.getEffectiveRate(), 9), c_cashBack.getSuperiorCashBackRate(), 9);
                        commission3rdParty = BigDecimalUtils.add(commission3rdParty, poker, 9);
                        break;
                    case 105:
                        final double fish = BigDecimalUtils.mul(BigDecimalUtils.mul(gameWagerStat.getTotalWagerFish(), c_cashBack.getEffectiveRate(), 9), c_cashBack.getSuperiorCashBackRate(), 9);
                        commission3rdParty = BigDecimalUtils.add(commission3rdParty, fish, 9);
                        break;
                    case 106:
                        final double arcade = BigDecimalUtils.mul(BigDecimalUtils.mul(gameWagerStat.getTotalWagerArcade(), c_cashBack.getEffectiveRate(), 9), c_cashBack.getSuperiorCashBackRate(), 9);
                        commission3rdParty = BigDecimalUtils.add(commission3rdParty, arcade, 9);
                        break;
                    case 107:
                        final double bingo = BigDecimalUtils.mul(BigDecimalUtils.mul(gameWagerStat.getTotalWagerBingo(), c_cashBack.getEffectiveRate(), 9), c_cashBack.getSuperiorCashBackRate(), 9);
                        commission3rdParty = BigDecimalUtils.add(commission3rdParty, bingo, 9);
                        break;
                }
            }
        }

        final GameCommissionStat commissionStat = new GameCommissionStat();
        commissionStat.setTotalCommissionOriginal(commissionOriginal)
                .setTotalCommission3rdParty(commission3rdParty)
                .setTotalCommissionSports(commissionSports)
                .setTotalCommissionReward(BigDecimalUtils.add(BigDecimalUtils.add(commissionOriginal, commission3rdParty, 9), commissionSports, 9));
        return commissionStat;
    }


    @Override
    public GameCommissionStat calculateTeamCommissionStat(GameWagerStat gameWagerStat, MerchantData merchantData) {
        double commissionOriginal = 0;
        double commission3rdParty = 0;
        double commissionSports = 0;

        final String rebateMethod = merchantData.findC_GlobalValue(this.getClass().getSimpleName(), "rebateMethod");
        if (Integer.parseInt(rebateMethod) == 4) {//佣金

            Iterator<C_CashBack> iterator = merchantData.getC_cashBackMap().values().iterator();
            while (iterator.hasNext()) {
                final C_CashBack c_cashBack = iterator.next();
                switch (c_cashBack.getGameType()) {
                    case 101:
                        commissionOriginal = BigDecimalUtils.mul(BigDecimalUtils.mul(gameWagerStat.getTotalWagerOriginal(), c_cashBack.getEffectiveRate(), 9), BigDecimalUtils.mul(c_cashBack.getTeamCashBackRate(), c_cashBack.getGameCashBackRate(), 9), 9);
                        break;
                    case 102:
                        final double slots = BigDecimalUtils.mul(BigDecimalUtils.mul(gameWagerStat.getTotalWagerSlots(), c_cashBack.getEffectiveRate(), 9), BigDecimalUtils.mul(c_cashBack.getTeamCashBackRate(), c_cashBack.getGameCashBackRate(), 9), 9);
                        commission3rdParty = BigDecimalUtils.add(commission3rdParty, slots, 9);
                        break;
                    case 103:
                        final double live = BigDecimalUtils.mul(BigDecimalUtils.mul(gameWagerStat.getTotalWagerLive(), c_cashBack.getEffectiveRate(), 9), BigDecimalUtils.mul(c_cashBack.getTeamCashBackRate(), c_cashBack.getGameCashBackRate(), 9), 9);
                        commission3rdParty = BigDecimalUtils.add(commission3rdParty, live, 9);
                        break;
                    case 104:
                        final double poker = BigDecimalUtils.mul(BigDecimalUtils.mul(gameWagerStat.getTotalWagerPoker(), c_cashBack.getEffectiveRate(), 9), BigDecimalUtils.mul(c_cashBack.getTeamCashBackRate(), c_cashBack.getGameCashBackRate(), 9), 9);
                        commission3rdParty = BigDecimalUtils.add(commission3rdParty, poker, 9);
                        break;
                    case 105:
                        final double fish = BigDecimalUtils.mul(BigDecimalUtils.mul(gameWagerStat.getTotalWagerFish(), c_cashBack.getEffectiveRate(), 9), BigDecimalUtils.mul(c_cashBack.getTeamCashBackRate(), c_cashBack.getGameCashBackRate(), 9), 9);
                        commission3rdParty = BigDecimalUtils.add(commission3rdParty, fish, 9);
                        break;
                    case 106:
                        final double arcade = BigDecimalUtils.mul(BigDecimalUtils.mul(gameWagerStat.getTotalWagerArcade(), c_cashBack.getEffectiveRate(), 9), BigDecimalUtils.mul(c_cashBack.getTeamCashBackRate(), c_cashBack.getGameCashBackRate(), 9), 9);
                        commission3rdParty = BigDecimalUtils.add(commission3rdParty, arcade, 9);
                        break;
                    case 107:
                        final double bingo = BigDecimalUtils.mul(BigDecimalUtils.mul(gameWagerStat.getTotalWagerBingo(), c_cashBack.getEffectiveRate(), 9), BigDecimalUtils.mul(c_cashBack.getTeamCashBackRate(), c_cashBack.getGameCashBackRate(), 9), 9);
                        commission3rdParty = BigDecimalUtils.add(commission3rdParty, bingo, 9);
                        break;
                }
            }
        } else {//有效
            Iterator<C_CashBack> iterator = merchantData.getC_cashBackMap().values().iterator();
            while (iterator.hasNext()) {
                final C_CashBack c_cashBack = iterator.next();
                switch (c_cashBack.getGameType()) {
                    case 101:
                        commissionOriginal = BigDecimalUtils.mul(BigDecimalUtils.mul(gameWagerStat.getTotalWagerOriginal(), c_cashBack.getEffectiveRate(), 9), c_cashBack.getTeamCashBackRate(), 9);
                        break;
                    case 102:
                        final double slots = BigDecimalUtils.mul(BigDecimalUtils.mul(gameWagerStat.getTotalWagerSlots(), c_cashBack.getEffectiveRate(), 9), c_cashBack.getTeamCashBackRate(), 9);
                        commission3rdParty = BigDecimalUtils.add(commission3rdParty, slots, 9);
                        break;
                    case 103:
                        final double live = BigDecimalUtils.mul(BigDecimalUtils.mul(gameWagerStat.getTotalWagerLive(), c_cashBack.getEffectiveRate(), 9), c_cashBack.getTeamCashBackRate(), 9);
                        commission3rdParty = BigDecimalUtils.add(commission3rdParty, live, 9);
                        break;
                    case 104:
                        final double poker = BigDecimalUtils.mul(BigDecimalUtils.mul(gameWagerStat.getTotalWagerPoker(), c_cashBack.getEffectiveRate(), 9), c_cashBack.getTeamCashBackRate(), 9);
                        commission3rdParty = BigDecimalUtils.add(commission3rdParty, poker, 9);
                        break;
                    case 105:
                        final double fish = BigDecimalUtils.mul(BigDecimalUtils.mul(gameWagerStat.getTotalWagerFish(), c_cashBack.getEffectiveRate(), 9), c_cashBack.getTeamCashBackRate(), 9);
                        commission3rdParty = BigDecimalUtils.add(commission3rdParty, fish, 9);
                        break;
                    case 106:
                        final double arcade = BigDecimalUtils.mul(BigDecimalUtils.mul(gameWagerStat.getTotalWagerArcade(), c_cashBack.getEffectiveRate(), 9), c_cashBack.getTeamCashBackRate(), 9);
                        commission3rdParty = BigDecimalUtils.add(commission3rdParty, arcade, 9);
                        break;
                    case 107:
                        final double bingo = BigDecimalUtils.mul(BigDecimalUtils.mul(gameWagerStat.getTotalWagerBingo(), c_cashBack.getEffectiveRate(), 9), c_cashBack.getTeamCashBackRate(), 9);
                        commission3rdParty = BigDecimalUtils.add(commission3rdParty, bingo, 9);
                        break;
                }
            }
        }

        final GameCommissionStat commissionStat = new GameCommissionStat();
        commissionStat.setTotalCommissionOriginal(commissionOriginal)
                .setTotalCommission3rdParty(commission3rdParty)
                .setTotalCommissionSports(commissionSports)
                .setTotalCommissionReward(BigDecimalUtils.add(BigDecimalUtils.add(commissionOriginal, commission3rdParty, 9), commissionSports, 9));
        return commissionStat;
    }

    @Override
    public GameCommissionStat calculateThreeLevelCommissionStat(GameWagerStat gameWagerStat, MerchantData merchantData) {
        double commissionOriginal = 0;
        double commission3rdParty = 0;
        double commissionSports = 0;

        final String rebateMethod = merchantData.findC_GlobalValue(this.getClass().getSimpleName(), "rebateMethod");
        if (Integer.parseInt(rebateMethod) == 4) {//佣金
            Iterator<C_CashBack> iterator = merchantData.getC_cashBackMap().values().iterator();
            while (iterator.hasNext()) {
                final C_CashBack c_cashBack = iterator.next();
                switch (c_cashBack.getGameType()) {
                    case 101:
                        commissionOriginal = BigDecimalUtils.mul(BigDecimalUtils.mul(gameWagerStat.getTotalWagerOriginal(), c_cashBack.getEffectiveRate(), 9), BigDecimalUtils.mul(c_cashBack.getThreeLevelCashBackRate(), c_cashBack.getGameCashBackRate(), 9), 9);
                        break;
                    case 102:
                        final double slots = BigDecimalUtils.mul(BigDecimalUtils.mul(gameWagerStat.getTotalWagerSlots(), c_cashBack.getEffectiveRate(), 9), BigDecimalUtils.mul(c_cashBack.getThreeLevelCashBackRate(), c_cashBack.getGameCashBackRate(), 9), 9);
                        commission3rdParty = BigDecimalUtils.add(commission3rdParty, slots, 9);
                        break;
                    case 103:
                        final double live = BigDecimalUtils.mul(BigDecimalUtils.mul(gameWagerStat.getTotalWagerLive(), c_cashBack.getEffectiveRate(), 9), BigDecimalUtils.mul(c_cashBack.getThreeLevelCashBackRate(), c_cashBack.getGameCashBackRate(), 9), 9);
                        commission3rdParty = BigDecimalUtils.add(commission3rdParty, live, 9);
                        break;
                    case 104:
                        final double poker = BigDecimalUtils.mul(BigDecimalUtils.mul(gameWagerStat.getTotalWagerPoker(), c_cashBack.getEffectiveRate(), 9), BigDecimalUtils.mul(c_cashBack.getThreeLevelCashBackRate(), c_cashBack.getGameCashBackRate(), 9), 9);
                        commission3rdParty = BigDecimalUtils.add(commission3rdParty, poker, 9);
                        break;
                    case 105:
                        final double fish = BigDecimalUtils.mul(BigDecimalUtils.mul(gameWagerStat.getTotalWagerFish(), c_cashBack.getEffectiveRate(), 9), BigDecimalUtils.mul(c_cashBack.getThreeLevelCashBackRate(), c_cashBack.getGameCashBackRate(), 9), 9);
                        commission3rdParty = BigDecimalUtils.add(commission3rdParty, fish, 9);
                        break;
                    case 106:
                        final double arcade = BigDecimalUtils.mul(BigDecimalUtils.mul(gameWagerStat.getTotalWagerArcade(), c_cashBack.getEffectiveRate(), 9), BigDecimalUtils.mul(c_cashBack.getThreeLevelCashBackRate(), c_cashBack.getGameCashBackRate(), 9), 9);
                        commission3rdParty = BigDecimalUtils.add(commission3rdParty, arcade, 9);
                        break;
                    case 107:
                        final double bingo = BigDecimalUtils.mul(BigDecimalUtils.mul(gameWagerStat.getTotalWagerBingo(), c_cashBack.getEffectiveRate(), 9), BigDecimalUtils.mul(c_cashBack.getThreeLevelCashBackRate(), c_cashBack.getGameCashBackRate(), 9), 9);
                        commission3rdParty = BigDecimalUtils.add(commission3rdParty, bingo, 9);
                        break;
                }
            }
        } else {//有效
            Iterator<C_CashBack> iterator = merchantData.getC_cashBackMap().values().iterator();
            while (iterator.hasNext()) {
                final C_CashBack c_cashBack = iterator.next();
                switch (c_cashBack.getGameType()) {
                    case 101:
                        commissionOriginal = BigDecimalUtils.mul(BigDecimalUtils.mul(gameWagerStat.getTotalWagerOriginal(), c_cashBack.getEffectiveRate(), 9), c_cashBack.getThreeLevelCashBackRate(), 9);
                        break;
                    case 102:
                        final double slots = BigDecimalUtils.mul(BigDecimalUtils.mul(gameWagerStat.getTotalWagerSlots(), c_cashBack.getEffectiveRate(), 9), c_cashBack.getThreeLevelCashBackRate(), 9);
                        commission3rdParty = BigDecimalUtils.add(commission3rdParty, slots, 9);
                        break;
                    case 103:
                        final double live = BigDecimalUtils.mul(BigDecimalUtils.mul(gameWagerStat.getTotalWagerLive(), c_cashBack.getEffectiveRate(), 9), c_cashBack.getThreeLevelCashBackRate(), 9);
                        commission3rdParty = BigDecimalUtils.add(commission3rdParty, live, 9);
                        break;
                    case 104:
                        final double poker = BigDecimalUtils.mul(BigDecimalUtils.mul(gameWagerStat.getTotalWagerPoker(), c_cashBack.getEffectiveRate(), 9), c_cashBack.getThreeLevelCashBackRate(), 9);
                        commission3rdParty = BigDecimalUtils.add(commission3rdParty, poker, 9);
                        break;
                    case 105:
                        final double fish = BigDecimalUtils.mul(BigDecimalUtils.mul(gameWagerStat.getTotalWagerFish(), c_cashBack.getEffectiveRate(), 9), c_cashBack.getThreeLevelCashBackRate(), 9);
                        commission3rdParty = BigDecimalUtils.add(commission3rdParty, fish, 9);
                        break;
                    case 106:
                        final double arcade = BigDecimalUtils.mul(BigDecimalUtils.mul(gameWagerStat.getTotalWagerArcade(), c_cashBack.getEffectiveRate(), 9), c_cashBack.getThreeLevelCashBackRate(), 9);
                        commission3rdParty = BigDecimalUtils.add(commission3rdParty, arcade, 9);
                        break;
                    case 107:
                        final double bingo = BigDecimalUtils.mul(BigDecimalUtils.mul(gameWagerStat.getTotalWagerBingo(), c_cashBack.getEffectiveRate(), 9), c_cashBack.getThreeLevelCashBackRate(), 9);
                        commission3rdParty = BigDecimalUtils.add(commission3rdParty, bingo, 9);
                        break;
                }
            }
        }

        final GameCommissionStat commissionStat = new GameCommissionStat();
        commissionStat.setTotalCommissionOriginal(commissionOriginal)
                .setTotalCommission3rdParty(commission3rdParty)
                .setTotalCommissionSports(commissionSports)
                .setTotalCommissionReward(BigDecimalUtils.add(BigDecimalUtils.add(commissionOriginal, commission3rdParty, 9), commissionSports, 9));
        return commissionStat;
    }
}
