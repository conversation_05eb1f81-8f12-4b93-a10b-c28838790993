package com.game.scripts.billing;

import com.game.dao.player.PlayerDao;
import com.game.engine.math.BigDecimalUtils;
import com.game.engine.script.ScriptLoader;
import com.game.engine.util.misc.Tuple2;
import com.game.entity.player.Player;
import com.game.entity.player.TurnoverRecord;
import com.game.entity.player.WithdrawStandard;
import com.game.enums.TurnoverReason;
import com.game.hall.mrg.currency.CurrencyMrg;
import com.game.hall.script.IBillingScript;
import com.game.hall.script.IPlayerScript;
import com.game.manager.EntityDaoMrg;
import it.unimi.dsi.fastutil.ints.IntLists;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class BillingScript implements IBillingScript {
    private static final Logger LOGGER = LoggerFactory.getLogger(BillingScript.class);

    @Override//Available //LockedFunds
    public Tuple2<Double, Double> calculateAvailableAmount(Player player, int currencyId, int withdrawType) {
        final double balance = CurrencyMrg.getInstance().getCurrencyValue(player, currencyId);
        final WithdrawStandard withdrawStandard = player.getWithdrawStandard(player.getCurrencyId());
        switch (withdrawType) {
            case 1://已完成洗码量≥要求洗码量时
                if (withdrawStandard.getBettingVolume() >= withdrawStandard.getDrawStandard()) {
                    return new Tuple2<>(balance, 0d);
                }
                return new Tuple2<>(0d, balance);
            case 2://(已完成洗码量/要求洗码量)*总可提金额
            {
                final double bettingTurnover = withdrawStandard.getBettingVolume();
                final double drawStandard = withdrawStandard.getDrawStandard();
                double available = 0;
                if (bettingTurnover >= drawStandard) {
                    available = balance;
                } else {
                    available = BigDecimalUtils.mul(BigDecimalUtils.div(bettingTurnover, drawStandard, 9), balance, 9);
                }
                final double lockedFunds = BigDecimalUtils.sub(balance, available, 9);
                return new Tuple2<>(available, lockedFunds);
            }
            case 3://打码量按表格
            {
                double lockedFunds = 0;
                for (TurnoverRecord turnoverRecord : player.getTurnoverRecordMap().values()) {
                    if (turnoverRecord.getDrawStandard() != turnoverRecord.getBettingVolume()) {
                        lockedFunds = BigDecimalUtils.add(turnoverRecord.getAmount(), lockedFunds, 9);
                    }
                }
                //可提现金额=钱包余额-不可提现金额
                final double amount = BigDecimalUtils.sub(balance, lockedFunds, 9);
                return new Tuple2<>(Math.max(0, amount), lockedFunds);
            }
        }
        return new Tuple2<>(0d, 0d);
    }

    @Override
    public void recalculateWithdrawStandard(Player player, int currencyId, double available, double withdrawAmount, int withdrawType) {
        final double balance = CurrencyMrg.getInstance().getCurrencyValue(player, currencyId);
        final WithdrawStandard withdrawStandard = player.getWithdrawStandard(player.getCurrencyId());
        switch (withdrawType) {
            case 1://已完成洗码量≥要求洗码量时
                withdrawStandard.setLastDrawStandard(withdrawStandard.getDrawStandard());
                withdrawStandard.setLastBettingVolume(withdrawStandard.getBettingVolume());

//                withdrawStandard.reset();
                ScriptLoader.getInstance().consumerScript("PlayerWithdrawStandardScript",
                        (IPlayerScript script) -> script.drawStandard(player, TurnoverReason.Draw, currencyId, 0,
                                -withdrawStandard.getDrawStandard()));
                ScriptLoader.getInstance().consumerScript("PlayerWithdrawStandardScript",
                        (IPlayerScript script) -> script.bettingTurnover(player, TurnoverReason.Draw, currencyId,
                                -withdrawStandard.getBettingVolume()));
                EntityDaoMrg.getInstance().getDao(PlayerDao.class)
                        .updateWithdrawStandard(player, IntLists.singleton(currencyId));
                break;
            case 2:
                if (balance <= 0) {
                    withdrawStandard.setLastDrawStandard(withdrawStandard.getDrawStandard());
                    withdrawStandard.setLastBettingVolume(withdrawStandard.getBettingVolume());

//                    withdrawStandard.reset();
                    ScriptLoader.getInstance().consumerScript("PlayerWithdrawStandardScript",
                            (IPlayerScript script) -> script.drawStandard(player, TurnoverReason.Draw, currencyId, 0,
                                    -withdrawStandard.getDrawStandard()));
                    ScriptLoader.getInstance().consumerScript("PlayerWithdrawStandardScript",
                            (IPlayerScript script) -> script.bettingTurnover(player, TurnoverReason.Draw, currencyId,
                                    -withdrawStandard.getBettingVolume()));
                    EntityDaoMrg.getInstance().getDao(PlayerDao.class)
                            .updateWithdrawStandard(player, IntLists.singleton(currencyId));
                } else {
                    if (available == 0) {
                        return;
                    }

                    //提现后要求洗码量=提现前要求洗码量-提现前已完成洗码量*（提现金额/当前可提金额）
                    final double beforeDrawStandard = withdrawStandard.getDrawStandard();
                    final double beforeBettingVolume = withdrawStandard.getBettingVolume();
                    final double drawStandard = BigDecimalUtils.sub(beforeDrawStandard,
                            BigDecimalUtils.mul(beforeBettingVolume, BigDecimalUtils.div(withdrawAmount, available, 4), 4), 4);

                    //提现后已完成洗码量=提现前已完成洗码量*（1-提现金额/当前可提金额）
                    final double bettingTurnover = BigDecimalUtils.mul(beforeBettingVolume,
                            BigDecimalUtils.sub(1, BigDecimalUtils.div(withdrawAmount, available, 4), 4), 4);

                    withdrawStandard.setLastDrawStandard(BigDecimalUtils.sub(beforeDrawStandard, drawStandard > 0 ? drawStandard : 0, 4));
                    if (withdrawStandard.getLastDrawStandard() <= 0) {
                        withdrawStandard.setLastDrawStandard(0);
                    }

                    withdrawStandard.setLastBettingVolume(BigDecimalUtils.sub(beforeBettingVolume, bettingTurnover > 0 ? bettingTurnover : 0, 4));
                    if (withdrawStandard.getLastBettingVolume() <= 0) {
                        withdrawStandard.setLastBettingVolume(0);
                    }

                    ScriptLoader.getInstance().consumerScript("PlayerWithdrawStandardScript",
                            (IPlayerScript script) -> script.drawStandard(player, TurnoverReason.Draw, currencyId, 0,
                                    -withdrawStandard.getLastDrawStandard()));
//                    withdrawStandard.setDrawStandard(drawStandard);
//                    if (withdrawStandard.getDrawStandard() <= 0) {
//                        withdrawStandard.setDrawStandard(0);
//                    }

                    ScriptLoader.getInstance().consumerScript("PlayerWithdrawStandardScript",
                            (IPlayerScript script) -> script.bettingTurnover(player, TurnoverReason.Draw, currencyId,
                                    -withdrawStandard.getLastBettingVolume()));
                    EntityDaoMrg.getInstance().getDao(PlayerDao.class)
                            .updateWithdrawStandard(player, IntLists.singleton(currencyId));
//                    withdrawStandard.setBettingVolume(bettingTurnover);
//                    if (withdrawStandard.getBettingVolume() <= 0) {
//                        withdrawStandard.setBettingVolume(0);
//                    }
                }
                EntityDaoMrg.getInstance().getDao(PlayerDao.class)
                        .updateWithdrawStandard(player, IntLists.singleton(currencyId));
                break;
        }
    }

    public static void main(String[] args) {
        System.out.println(BigDecimalUtils.sub(1, -1, 2));
    }
}
