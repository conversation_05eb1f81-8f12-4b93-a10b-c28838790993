package com.game.scripts.message;

import com.game.dao.player.PlayerDao;
import com.game.dao.player.PlayerPromoteDao;
import com.game.engine.io.handler.TcpHandler;
import com.game.engine.io.message.MessageBean;
import com.game.engine.net.LogicEvent;
import com.game.engine.script.ScriptLoader;
import com.game.engine.utils.JWTUtil;
import com.game.engine.utils.MsgUtil;
import com.game.engine.utils.TimeUtil;
import com.game.entity.player.Player;
import com.game.entity.player.PlayerFields;
import com.game.entity.player.promote.PlayerPromote;
import com.game.entity.player.promote.PlayerPromoteFields;
import com.game.enums.ErrorCode;
import com.game.hall.main.HallServer;
import com.game.hall.mrg.UdpMsgAttackResistMrg;
import com.game.hall.mrg.UserTcpMrg;
import com.game.hall.mrg.UserUdpMrg;
import com.game.hall.mrg.player.PlayerMrg;
import com.game.hall.script.IJwtTokenAuthScript;
import com.game.hall.script.IMessageScript;
import com.game.manager.EntityDaoMrg;
import com.game.user.UserSession;
import com.google.protobuf.Message;
import com.proto.MIDMessage;
import com.proto.TcpMessage;
import io.netty.buffer.ByteBuf;
import io.netty.buffer.Unpooled;
import io.netty.channel.Channel;
import io.netty.util.internal.StringUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.mongodb.core.query.Update;

import java.util.Base64;
import java.util.Map;
import java.util.Objects;

public class MessageScript implements IMessageScript {
    private static Logger LOGGER = LoggerFactory.getLogger(MessageScript.class);

    @Override
    public void tcpUserMessageHandler(LogicEvent event) {
        try {
            final Channel channel = event.getChannel();
            final int messageId = event.getIntParamA();
            final byte[] bytes = (byte[]) event.getParamA();
            final long sessionID = MsgUtil.getSessionID(channel);
            final UserSession userSession = UserTcpMrg.getInstance().getUserBySessionID(sessionID);
            if (userSession == null) {
                LOGGER.warn("客户端未建立网络连接直接发消息！！！！！！");
                return;
            }

            final MessageBean messageBean = ScriptLoader.getInstance().getMessageBean(messageId);
            if (messageBean != null) {
                final Message message = messageBean.buildMessage(bytes);
                final TcpHandler handler = (TcpHandler) messageBean.newHandler();
                if (handler != null) {
                    handler.setMessage(message);
                    handler.setMsgBytes(bytes);
                    handler.setSession(channel);
                    handler.setCreateTime(TimeUtil.currentTimeMillis());
                    handler.setPid(userSession.getAccountId());
                    if (MIDMessage.MID.ReqTcpHeartBeat_VALUE == messageId) {
                        if (userSession.getAccountId() > 0) {
                            HallServer.getInstance().asyncExecute(userSession.getAccountId(), handler);
                        } else {
                            HallServer.getInstance().asyncExecute(handler);
                        }
                    } else if (MIDMessage.MID.ReqTcpQuitAgentGame_VALUE == messageId) {
                        HallServer.getInstance().asyncExecute(Math.abs(userSession.getAccountId()), handler);
                    } else if (MIDMessage.MID.ReqTcpTokenAuth_VALUE == messageId) {
                        handler.setPid(userSession.getSessionID());
                        if (tokenAuth(handler, channel, message)) {
                            final long userId = (long) handler.getParamsMap().get("userId");
                            if (userId > 0) {
                                HallServer.getInstance().asyncExecute(userId, handler);
                            } else {
                                HallServer.getInstance().asyncExecute(handler);
                            }
                        }
                    } else {
                        handler.setPid(userSession.getSessionID());
                        handler.run();
                    }
                }
            } else {
                // 直接转发
                LOGGER.warn("消息[{}]代码未实现逻辑", messageId);
            }
        } catch (Exception e) {
            LOGGER.error("", e);
        }
    }

    private boolean tokenAuth(TcpHandler handler, Channel session, Message message) {
        final TcpMessage.ResTcpTokenAuthMessage.Builder res = TcpMessage.ResTcpTokenAuthMessage.newBuilder();
        res.setMsgID(MIDMessage.MID.ResTcpTokenAuth_VALUE);
        try {
            final TcpMessage.ReqTcpTokenAuthMessage req = (TcpMessage.ReqTcpTokenAuthMessage) message;
            final String token = req.getToken();

            if (StringUtil.isNullOrEmpty(token)) {
//                LOGGER.warn("token is not exist");
                res.setMsgID(ErrorCode.Token_Invalid.getCode());
                MsgUtil.sendClientMsg(session, res.build());
                return false;
            }

            final Map<String, Object> params = JWTUtil.verify(token);
            if (params.isEmpty()) {
                res.setMsgID(ErrorCode.Token_Invalid.getCode());
                MsgUtil.sendClientMsg(session, res.build());
                return false;
            }

            if (!Objects.isNull(params.get("error"))) {
                final int error = (int) params.get("error");
                res.setMsgID(error);
                MsgUtil.sendClientMsg(session, res.build());
                return false;
            }

            handler.setParamsMap(params);
            return true;
        } catch (Exception e) {
            LOGGER.error("", e);
            res.setError(ErrorCode.Internal_Server_Error.getCode());
            MsgUtil.sendClientMsg(session, res.build());
            return false;
        }
    }

    @Override
    public void udpUserMessageHandler(LogicEvent event) {
        @SuppressWarnings("unchecked") final Map<String, Object> paramsMap = (Map<String, Object>) event.getParamA();
        @SuppressWarnings("unchecked") final Map<String, Object> headersMap = (Map<String, Object>) event.getParamC();
        final Channel session = event.getChannel();
        try {
            final String data = (String) paramsMap.get("data");
            final String token = (String) headersMap.get("authorization");
            final String host = (String) headersMap.get("origin");
            if (StringUtil.isNullOrEmpty(token) || StringUtil.isNullOrEmpty(data)) {
                LOGGER.warn("missing parameter");
                MsgUtil.responseHttp(ErrorCode.Parameter_Error.getCode(), session);
                return;
            }

            final Map<String, Object> params = JWTUtil.verify(token);
            if (params.isEmpty()) {
                MsgUtil.responseHttp(ErrorCode.Token_Invalid.getCode(), session);
                return;
            }

            final boolean auth = ScriptLoader.getInstance().functionScript("JwtTokenAuthScript",
                    (IJwtTokenAuthScript script) -> script.authToken(host, token));

            if (!auth) {
                MsgUtil.responseHttp(ErrorCode.Token_Invalid.getCode(), session);
                return;
            }

            if (!Objects.isNull(params.get("error"))) {
                final int error = (int) params.get("error");
                MsgUtil.responseHttp(error, session);
                return;
            }

            final Object country = headersMap.get("cf-ipcountry");
            if (country != null) {
                params.put("country", country);
            }

            final long udpSessionId = MsgUtil.getSessionID(session);
            final long userId = (long) params.get("userId");

//            final String ip = MsgUtil.getClientIp(session);
//            if (UdpMsgAttackResistMrg.getInstance().getIpBlacklist().contains(ip)) {
//                final Player player = PlayerMrg.getInstance().findDbPlayer(userId);
//                if (player != null && player.getState() == 1) {
//                    player.setState(2);
//                    final Update update = new Update();
//                    update.set(PlayerFields.state, player.getState());
//                    EntityDaoMrg.getInstance().getDao(PlayerDao.class).updatePlayer(userId, update);
//
//                    PlayerPromote playerPromote = player.getPlayerPromote();
//                    if (playerPromote != null) {
//                        playerPromote.setState(2);
//                        EntityDaoMrg.getInstance().getDao(PlayerPromoteDao.class)
//                                .updatePromotionField(userId, PlayerPromoteFields.state, 2);
//                    }
//                }
//            }

            final byte[] bytes = Base64.getDecoder().decode(data.replace(" ", "+"));
            final ByteBuf byteBuf = Unpooled.wrappedBuffer(bytes);
            byteBuf.readInt();
            final int msgId = byteBuf.readInt();
            final byte[] remainByte = new byte[byteBuf.readableBytes()];
            byteBuf.readBytes(remainByte);

            final MessageBean messageBean = ScriptLoader.getInstance().getMessageBean(msgId);
            if (messageBean != null) {
                if (MIDMessage.MID.ReqEntryAgentGame_VALUE == msgId || MIDMessage.MID.ReqCreateRechargeOrder_VALUE == msgId || MIDMessage.MID.ReqCreateWithdrawOrder_VALUE == msgId) {
                    UserUdpMrg.getInstance().addUdpSession(udpSessionId, session);
//                    LOGGER.info("udp add sessionId；{}", UserUdpMrg.getInstance().getUdpUserSession().size());
//                    LOGGER.info("userId：{}，receive client msg：{}", userId, msgId);
                }

                final Message message = messageBean.buildMessage(remainByte);
                final TcpHandler handler = (TcpHandler) messageBean.newHandler();
                if (handler != null) {
                    handler.setMessage(message);
                    handler.setMsgBytes(bytes);
                    handler.setSession(session);
                    handler.setCreateTime(TimeUtil.currentTimeMillis());
                    handler.setPid(userId);
                    handler.setUdpSessionId(udpSessionId);
                    handler.setParamsMap(params);
                    final String simpleName = handler.getClass().getSimpleName();
                    if (simpleName.contains("Data")) {
                        HallServer.getInstance().asyncExecute(handler);
//                        LOGGER.info("userId：{}，receive client msg：{}", userId, msgId);
                    } else {
                        HallServer.getInstance().asyncExecute(Math.abs(userId), handler);
//                        LOGGER.info("userId：{}，receive client msg：{}", userId, msgId);
                    }
                }
            } else {
                LOGGER.warn("消息[{}]代码未实现逻辑", msgId);
            }
        } catch (Exception e) {
            LOGGER.error("", e);
            MsgUtil.responseHttp(ErrorCode.Internal_Server_Error.getCode(), session);
        }
    }
}
