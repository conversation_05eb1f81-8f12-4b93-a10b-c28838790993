package com.game.scripts;

import com.game.c_entity.merchant.C_GameApi;
import com.game.engine.script.ScriptLoader;
import com.game.entity.player.Player;
import com.game.entity.player.stats.Stats;
import com.game.enums.GameType;
import com.game.enums.QuestGoalType;
import com.game.enums.TurnoverReason;
import com.game.hall.main.HallServer;
import com.game.hall.mrg.ActivityMrg;
import com.game.hall.mrg.quest.DefaultBlackboard;
import com.game.hall.mrg.quest.QuestMrg;
import com.game.hall.script.*;
import com.game.hall.script.activity.IActivityScript;
import com.game.hall.script.activity.IDailyContestScript;
import com.game.hall.script.activity.IWeeklyRaffleScript;
import io.netty.util.AttributeKey;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class BettingVolumeScript implements IBettingVolumeScript {
    private static final Logger LOGGER = LoggerFactory.getLogger(BettingVolumeScript.class);

    @Override
    public void calculateBetBettingVolume(Player player, C_GameApi c_gameApi, String noteId, int currencyId, double betAmount) {
        final int gameType = c_gameApi.getType();

        //TODO 计算有效
        final double validBet = ScriptLoader.getInstance().functionScript("GameScript",
                (IGameScript script) -> script.calculateValidBettingVolume(player, c_gameApi.getType(), currencyId, betAmount));

        //TODO 更新任务
        final DefaultBlackboard defaultBlackboard = new DefaultBlackboard();
        defaultBlackboard.set(AttributeKey.valueOf("gameType"), gameType);
        defaultBlackboard.set(AttributeKey.valueOf("currencyId"), currencyId);
        defaultBlackboard.set(AttributeKey.valueOf("wagered"), validBet);
        QuestMrg.getInstance().updateGoal(player, QuestGoalType.WageredTimes, defaultBlackboard);
        QuestMrg.getInstance().updateGoal(player, QuestGoalType.WageredAmount, defaultBlackboard);

        if (betAmount > 0) {
            //TODO 统计下注
            final Stats stats = player.getStats(currencyId);
            stats.incBetAmount(betAmount);
            stats.incValidBetAmount(validBet);
        }

        if (validBet > 0) {
//            final WithdrawStandard withdrawStandard = player.getWithdrawStandard(currencyId);
//            withdrawStandard.incBettingTurnover(validBet);
            ScriptLoader.getInstance().consumerScript("PlayerWithdrawStandardScript",
                    (IPlayerScript script) -> script.bettingTurnover(player, TurnoverReason.AgentGame, currencyId, validBet));

            //TODO vip
            ScriptLoader.getInstance().consumerScript("VipClubScript",
                    (IVipClubScript script) -> script.addExp(player, currencyId, validBet));

            //TODO 推广
//            ScriptLoader.getInstance().consumerScript("PromoteScript", (IPromoteScript script) ->
//                    script.invitedRewards(player, currencyId, validBet, 0));

            //TODO 邀请返水
            HallServer.getInstance().asyncExecute(0, () -> {
                ScriptLoader.getInstance().consumerScript("PromoteScript",
                        (IPromoteScript script) -> script.calculateSuperiorCommission(player, c_gameApi, noteId, currencyId, betAmount, validBet));
                ScriptLoader.getInstance().consumerScript("PromoteScript",
                        (IPromoteScript script) -> script.calculateTeamCommission(player, c_gameApi, noteId, currencyId, betAmount, validBet));
                ScriptLoader.getInstance().consumerScript("PromoteScript",
                        (IPromoteScript script) -> script.calculateThreeLevelCommission(player, c_gameApi, noteId, currencyId, betAmount, validBet));
            });

            //TODO 活动
            ScriptLoader.getInstance().consumerScript("WageredScript",
                    (IActivityScript script) -> script.execute(player, currencyId, validBet));
            ScriptLoader.getInstance().consumerScript("RankScript",
                    (IActivityScript script) -> script.execute(player, ActivityMrg.RANK_WAGERED, currencyId, 0, validBet));
            ScriptLoader.getInstance().consumerScript("RankScript",
                    (IActivityScript script) -> script.execute(player, currencyId, validBet, 0));

            //TODO 转盘
            ActivityMrg.luckSpinExecute(player, currencyId, validBet, 0);

            ScriptLoader.getInstance().consumerScript("DailyContestScript",
                    (IDailyContestScript script) -> script.dailyContestExecute(player, gameType, currencyId, validBet));
            ScriptLoader.getInstance().consumerScript("WeeklyRaffleScript",
                    (IWeeklyRaffleScript script) -> script.weeklyRaffleExecute(player, currencyId, validBet));

            ScriptLoader.getInstance().consumerScript("BonusScript",
                    (IBonusScript script) -> script.statsWager(player, currencyId, validBet));

            ScriptLoader.getInstance().consumerScript("RewardBoxScript",
                    (IActivityScript script) -> script.execute(player, currencyId, validBet, 0));
            ScriptLoader.getInstance().consumerScript("MysteryBonusScript",
                    (IActivityScript script) -> script.execute(player, currencyId, validBet, 0));
            ScriptLoader.getInstance().consumerScript("PiggyBankScript",
                    (IActivityScript script) -> script.execute(player, currencyId, validBet, 0));

            ScriptLoader.getInstance().consumerScript("WageredRebatesScript",
                    (IActivityScript script) -> script.execute(player, c_gameApi, currencyId, betAmount, validBet, 0));
        }
    }

}
