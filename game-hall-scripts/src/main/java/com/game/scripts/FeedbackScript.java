package com.game.scripts;

import com.facebook.ads.sdk.APIContext;
import com.facebook.ads.sdk.serverside.*;
import com.game.c_entity.middleplatform.C_BaseExchangeRate;
import com.game.engine.io.conf.ConstantConfig;
import com.game.engine.math.BigDecimalUtils;
import com.game.engine.utils.HttpUtils11;
import com.game.engine.utils.JsonUtils;
import com.game.engine.utils.SHA256Utils;
import com.game.engine.utils.TimeUtil;
import com.game.entity.player.Player;
import com.game.hall.main.HallServer;
import com.game.hall.mrg.DataHallMrg;
import com.game.hall.script.IFeedbackScript;
import com.game.utils.VirtualThreadUtils;
import com.proto.CommonMessage;
import io.netty.util.internal.StringUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.net.URI;
import java.net.http.HttpClient;
import java.net.http.HttpRequest;
import java.net.http.HttpResponse;
import java.time.Duration;
import java.util.LinkedHashMap;
import java.util.Map;

public class FeedbackScript implements IFeedbackScript {
    private static final Logger LOGGER = LoggerFactory.getLogger(FeedbackScript.class.getName());

    @Override
    public void sendFbFeedback(CommonMessage.FbInfo fbInfo, Player player, int currencyId, double totalRecharge, double recharge) {
        VirtualThreadUtils.execute(() -> {
            try {
                String eventName = "Purchase";
                if (totalRecharge == 1) {
                    eventName = "first_recharge_success";
                }
                final String fbToken = StringUtil.isNullOrEmpty(fbInfo.getFbToken()) ? player.getFbToken() : fbInfo.getFbToken();
                final String pixelIdd = StringUtil.isNullOrEmpty(fbInfo.getPixelId()) ? player.getPixelId() : fbInfo.getPixelId();

                if (StringUtil.isNullOrEmpty(pixelIdd)) {
                    return;
                }

                final C_BaseExchangeRate c_baseExchangeRate = DataHallMrg.getInstance().findC_BaseExchangeRate(this.getClass().getSimpleName(), currencyId);
                if (c_baseExchangeRate == null) {
                    return;
                }

                final APIContext context = new APIContext(fbToken).enableDebug(true);

                final UserData userData = new UserData();
                if (!StringUtil.isNullOrEmpty(player.getEmail())) {
                    userData.setEmail(SHA256Utils.SHA256(player.getEmail().toLowerCase()));
                }
                if (!StringUtil.isNullOrEmpty(player.getAreaCode())) {
                    userData.setPhone(SHA256Utils.SHA256(player.getAreaCode() + player.getPhone()));
                }
                if (!StringUtil.isNullOrEmpty(player.getRegisterRegion())) {
                    userData.countryCode(SHA256Utils.SHA256(player.getRegisterRegion().toLowerCase()));
                }
                // It is recommended to send Client IP and User Agent for Conversions API Events.
                userData.clientIpAddress(player.getIp());

                final CustomData customData = new CustomData();
                customData.currency("USD")
                        .value((float) BigDecimalUtils.mul(recharge, c_baseExchangeRate.getExchangeRate(), 2));

                final Event purchaseEvent = new Event();
                purchaseEvent.eventName(eventName)
                        .eventTime(TimeUtil.currentTimeMillis() / 1000L)
                        .customData(customData)
                        .actionSource(ActionSource.website)
                        .setUserData(userData);

                final EventRequest eventRequest = new EventRequest(pixelIdd, context);
                eventRequest.addDataItem(purchaseEvent);

                final EventResponse response = eventRequest.execute();
                LOGGER.info("eventName：{}，recharge standard API response：{}", eventName, response);
            } catch (Exception e) {
                LOGGER.error("sendFbFeedback", e);
            }
        });

        VirtualThreadUtils.execute(() -> {
            try {
                if (totalRecharge == 0) {
                    return;
                }
                String eventName = "";
                if (recharge >= 50 && recharge < 100) {
                    eventName = "purchase50brl";
                } else if (recharge >= 100 && recharge < 200) {
                    eventName = "purchase100brl";
                } else if (recharge >= 200) {
                    eventName = "purchase200brl";
                }

                if (StringUtil.isNullOrEmpty(eventName)) {
                    return;
                }

                final String fbToken = StringUtil.isNullOrEmpty(fbInfo.getFbToken()) ? player.getFbToken() : fbInfo.getFbToken();
                final String pixelIdd = StringUtil.isNullOrEmpty(fbInfo.getPixelId()) ? player.getPixelId() : fbInfo.getPixelId();

                if (StringUtil.isNullOrEmpty(pixelIdd)) {
                    return;
                }

                final C_BaseExchangeRate c_baseExchangeRate = DataHallMrg.getInstance().findC_BaseExchangeRate(this.getClass().getSimpleName(), currencyId);
                if (c_baseExchangeRate == null) {
                    return;
                }

                final APIContext context = new APIContext(fbToken).enableDebug(true);

                final UserData userData = new UserData();
                if (!StringUtil.isNullOrEmpty(player.getEmail())) {
                    userData.setEmail(SHA256Utils.SHA256(player.getEmail().toLowerCase()));
                }
                if (!StringUtil.isNullOrEmpty(player.getAreaCode())) {
                    userData.setPhone(SHA256Utils.SHA256(player.getAreaCode() + player.getPhone()));
                }
                if (!StringUtil.isNullOrEmpty(player.getRegisterRegion())) {
                    userData.countryCode(SHA256Utils.SHA256(player.getRegisterRegion().toLowerCase()));
                }
                // It is recommended to send Client IP and User Agent for Conversions API Events.
                userData.clientIpAddress(player.getIp());

                final CustomData customData = new CustomData();
                customData.currency("USD")
                        .value((float) BigDecimalUtils.mul(recharge, c_baseExchangeRate.getExchangeRate(), 2));

                final Event purchaseEvent = new Event();
                purchaseEvent.eventName(eventName)
                        .eventTime(TimeUtil.currentTimeMillis() / 1000L)
                        .customData(customData)
                        .actionSource(ActionSource.website)
                        .setUserData(userData);

                final EventRequest eventRequest = new EventRequest(pixelIdd, context);
                eventRequest.addDataItem(purchaseEvent);

                final EventResponse response = eventRequest.execute();
                LOGGER.info("eventName：{}，fb recharge standard API response：{}", eventName, response);
            } catch (Exception e) {
                LOGGER.error("sendFbFeedback", e);
            }
        });
    }

    @Override
    public void sendKWaiFeedback(Player player, int currencyId, double totalRecharge, double recharge) {
        VirtualThreadUtils.execute(() -> {
            try {
                final String pixelIdd = player.getKWaiPixelId();

                if (StringUtil.isNullOrEmpty(pixelIdd)) {
                    return;
                }

                final C_BaseExchangeRate c_baseExchangeRate = DataHallMrg.getInstance().findC_BaseExchangeRate(this.getClass().getSimpleName(), currencyId);
                if (c_baseExchangeRate == null) {
                    return;
                }

                String eventName = "EVENT_FIRST_DEPOSIT";
                if (totalRecharge == 1) {
                    eventName = "EVENT_PURCHASE";
                }

//                LOGGER.warn("iskWai：{}", ConstantConfig.getInstance().iskWai());
                final String uri = "http://www.adsnebula.com/log/common/api";
                final Map<String, Object> paramsMap = new LinkedHashMap<>();
                paramsMap.put("event_name", eventName);
                paramsMap.put("pixelId", player.getKWaiPixelId());
                paramsMap.put("access_token", player.getKWaiToken());
                paramsMap.put("testFlag", false);
                paramsMap.put("clickid", player.getClickId());//游戏代码
                paramsMap.put("trackFlag", ConstantConfig.getInstance().iskWai());
                paramsMap.put("is_attributed", 1);
                paramsMap.put("mmpcode", "PL");
                paramsMap.put("pixelSdkVersion", "9.9.9");
                paramsMap.put("currency", "USD");
                paramsMap.put("value", (float) BigDecimalUtils.mul(recharge, c_baseExchangeRate.getExchangeRate(), 2));

                final HttpRequest request = HttpRequest.newBuilder()
                        .timeout(Duration.ofSeconds(TimeUtil.MIN))
                        .uri(URI.create(uri))
                        .version(HttpClient.Version.HTTP_1_1)
                        .header("Content-Type", HttpUtils11.HTTP_CONTENT_TYPE_JSON)
                        .POST(HttpRequest.BodyPublishers.ofString(JsonUtils.writeAsJson(paramsMap)))
                        .build();
                final HttpResponse<String> httpResponse = HallServer.getInstance().getHttpClientMrg().send(request, HttpResponse.BodyHandlers.ofString());
                LOGGER.info("eventName：{}，KWai recharge standard API response：{}", eventName, httpResponse.body());
            } catch (Exception e) {
                LOGGER.error("sendFbFeedback KWai", e);
            }
        });
    }
}
