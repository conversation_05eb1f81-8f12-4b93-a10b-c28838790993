package com.game.scripts.notice;

import com.game.c_entity.merchant.C_MaintainNotice;
import com.game.c_entity.middleplatform.C_BaseMaintainNotice;
import com.game.engine.utils.MsgUtil;
import com.game.entity.player.Player;
import com.game.hall.main.HallServer;
import com.game.hall.mrg.DataHallMrg;
import com.game.hall.mrg.MerchantData;
import com.game.hall.mrg.UserTcpMrg;
import com.game.hall.mrg.player.PlayerMrg;
import com.game.hall.script.INoticeScript;
import com.game.user.UserSession;
import com.proto.BackStageMessage;
import com.proto.MIDMessage;
import com.proto.TcpMessage;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

public class NoticeScript implements INoticeScript {
    private static final Logger LOGGER = LoggerFactory.getLogger(NoticeScript.class);

    @Override
    public void notifyMiddlePlatformNotice() {
        try {
            final Map<Long, Player> playerMap = PlayerMrg.getInstance().getOnlinePlayerMap();
            if (playerMap.isEmpty()) {
                return;
            }
            final Set<String> business_nos = playerMap.values().stream().map(Player::getBusiness_no).collect(Collectors.toSet());
            final TcpMessage.ResTcpBulletinDataMessage.Builder res = TcpMessage.ResTcpBulletinDataMessage.newBuilder();
            res.setMsgID(MIDMessage.MID.ResTcpBulletinData_VALUE);
            for (String business_no : business_nos) {
                res.setBusinessNo(business_no);
                final MerchantData merchantData = DataHallMrg.getInstance().findMerchantData(this.getClass().getSimpleName(), business_no);
                if (merchantData == null) {
                    continue;
                }

                final String initLanguage = merchantData.findC_GlobalValue(this.getClass().getSimpleName(), "initLanguage");

                final Optional<C_BaseMaintainNotice> optional = DataHallMrg.getInstance().getC_maintainNoticeMap().values().stream().findFirst();
                if (optional.isEmpty()) {
                    continue;
                }
                final C_BaseMaintainNotice c_baseMaintainNotice = optional.get();
                final BackStageMessage.MaintainNoticeInfo maintainNoticeInfo = buildBaseMaintainNoticeInfo(c_baseMaintainNotice, Integer.parseInt(initLanguage));
                if (maintainNoticeInfo == null) {
                    continue;
                }

                res.setMaintainNoticeInfo(maintainNoticeInfo);
                for (Map.Entry<Long, UserSession> entry : UserTcpMrg.getInstance().getSessionMap().entrySet()) {
                    if (Objects.equals(entry.getValue().getBusiness_no(), business_no)) {
                        MsgUtil.sendClientMsg(entry.getValue().getUserSession(), res.build());
                    }
                }
            }
        } catch (Exception e) {
            LOGGER.error("notifyNotice", e);
        }
    }

    @Override
    public void notifyMerchantNotice() {
        try {
            final Map<Long, Player> playerMap = PlayerMrg.getInstance().getOnlinePlayerMap();
            if (playerMap.isEmpty()) {
                return;
            }
            final Set<String> business_nos = playerMap.values().stream().map(Player::getBusiness_no).collect(Collectors.toSet());
            final TcpMessage.ResTcpBulletinDataMessage.Builder res = TcpMessage.ResTcpBulletinDataMessage.newBuilder();
            res.setMsgID(MIDMessage.MID.ResTcpBulletinData_VALUE);
            for (String business_no : business_nos) {
                res.setBusinessNo(business_no);
                final MerchantData merchantData = DataHallMrg.getInstance().findMerchantData(this.getClass().getSimpleName(), business_no);
                if (merchantData == null) {
                    continue;
                }

                final String initLanguage = merchantData.findC_GlobalValue(this.getClass().getSimpleName(), "initLanguage");

                final Optional<C_MaintainNotice> optional = merchantData.getC_maintainNoticeMap().values().stream().findFirst();
                if (optional.isEmpty()) {
                    continue;
                }
                final C_MaintainNotice c_maintainNotice = optional.get();
                final BackStageMessage.MaintainNoticeInfo maintainNoticeInfo = buildMaintainNoticeInfo(c_maintainNotice, Integer.parseInt(initLanguage));
                if (maintainNoticeInfo == null) {
                    continue;
                }

                res.setMaintainNoticeInfo(maintainNoticeInfo);
                for (Map.Entry<Long, UserSession> entry : UserTcpMrg.getInstance().getSessionMap().entrySet()) {
                    if (Objects.equals(entry.getValue().getBusiness_no(), business_no)) {
                        MsgUtil.sendClientMsg(entry.getValue().getUserSession(), res.build());
                    }
                }
            }
        } catch (Exception e) {
            LOGGER.error("notifyNotice", e);
        }
    }

    private static BackStageMessage.MaintainNoticeInfo buildBaseMaintainNoticeInfo(C_BaseMaintainNotice c_baseMaintainNotice, int language) {
        final C_BaseMaintainNotice.MaintainInfo maintainInfo = c_baseMaintainNotice.getMaintainInfoMap().get(language);
        if (maintainInfo == null) {
            return null;
        }
        final BackStageMessage.MaintainNoticeInfo.Builder maintainNoticeInfo = BackStageMessage.MaintainNoticeInfo.newBuilder()
                .setInfo(maintainInfo.getInfo())
                .setStartTime(c_baseMaintainNotice.getStartTime())
                .setEndTime(c_baseMaintainNotice.getEndTime());
        return maintainNoticeInfo.build();
    }

    private static BackStageMessage.MaintainNoticeInfo buildMaintainNoticeInfo(C_MaintainNotice c_maintainNotice, int language) {
        final C_MaintainNotice.MaintainInfo maintainInfo = c_maintainNotice.getMaintainInfoMap().get(language);
        if (maintainInfo == null) {
            return null;
        }
        final BackStageMessage.MaintainNoticeInfo.Builder maintainNoticeInfo = BackStageMessage.MaintainNoticeInfo.newBuilder()
                .setInfo(maintainInfo.getInfo())
                .setStartTime(c_maintainNotice.getStartTime())
                .setEndTime(c_maintainNotice.getEndTime());
        return maintainNoticeInfo.build();
    }

}
