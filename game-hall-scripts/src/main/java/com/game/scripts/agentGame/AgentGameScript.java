package com.game.scripts.agentGame;

import com.game.dao.game.GameNoteDao;
import com.game.engine.math.BigDecimalUtils;
import com.game.engine.script.ScriptLoader;
import com.game.engine.utils.TimeUtil;
import com.game.entity.game.GameNote;
import com.game.entity.player.Player;
import com.game.entity.player.stats.Stats;
import com.game.hall.mrg.ActivityMrg;
import com.game.hall.script.IAgentGameScript;
import com.game.hall.script.activity.IActivityScript;
import com.game.manager.EntityDaoMrg;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

public class AgentGameScript implements IAgentGameScript {
    private static final Logger LOGGER = LoggerFactory.getLogger(AgentGameScript.class);

    @Override
    public void statisticalGameNoteData(Player player) {
        if (player.getRecodeNoteId().isEmpty()) {
            return;
        }

        final Set<Long> tempNoteIds = new HashSet<>(player.getRecodeNoteId());
        final int pageSize = 1000;
        for (int i = 0; i < tempNoteIds.size(); i += pageSize) {
            final Set<Long> pageNoteIds = tempNoteIds.stream()
                    .skip(i)
                    .limit(pageSize)
                    .collect(Collectors.toSet());
            final List<GameNote> gameNoteList = EntityDaoMrg.getInstance().getDao(GameNoteDao.class)
                    .findByNoteIdList(player.getPlayerId(), pageNoteIds);
            if (gameNoteList == null || gameNoteList.isEmpty()) {
                continue;
            }

            // 并行处理数据统计
            player.getRecodeNoteId().clear();

            final long start = TimeUtil.currentTimeMillis();
            for (GameNote gameNote : gameNoteList) {
                if (gameNote.getStatus() != 2) {
                    continue;
                }
                statisticalData(player, gameNote);
            }
            final long cost = TimeUtil.currentTimeMillis() - start;
            if (cost > 10) {
                LOGGER.warn("playerId：{}，statisticalGameData cost time：{}", player.getPlayerId(), cost);
            }
        }
    }

    private void statisticalData(Player player, GameNote gameNote) {
        final int currencyId = gameNote.getCurrencyId();
        final double betAmount = gameNote.getBetAmount();
        final double win = gameNote.getWin();

        //TODO 赢分次数
        final Stats stats = player.getStats(currencyId);
        if (BigDecimalUtils.sub(win, betAmount, 4) > 0) {
            stats.incTotalWinTimes();
        }

        final double finalValue = BigDecimalUtils.sub(win, betAmount, 4);
        ScriptLoader.getInstance().consumerScript("CompensateScript",
                (IActivityScript script) -> script.execute(player, currencyId, finalValue));

        ScriptLoader.getInstance().consumerScript("WageredRebatesScript",
                (IActivityScript script) -> script.execute(player, 0, currencyId, 0, win));

        if (betAmount > 0) {
            ScriptLoader.getInstance().consumerScript("RankScript",
                    (IActivityScript script) -> script.execute(player, ActivityMrg.RANK_MULTIPLE, currencyId, betAmount, BigDecimalUtils.div(win, betAmount, 4)));
        }
    }

}
