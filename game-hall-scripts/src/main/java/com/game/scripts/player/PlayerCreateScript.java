package com.game.scripts.player;

import com.game.dao.player.PlayerDao;
import com.game.dao.player.PlayerPromoteDao;
import com.game.engine.io.redis.RedisPoolManager;
import com.game.engine.log.GameLog;
import com.game.engine.script.ScriptLoader;
import com.game.engine.utils.GuidGeneratorUtils;
import com.game.engine.utils.TimeUtil;
import com.game.entity.account.Account;
import com.game.entity.account.ThreePartyInfo;
import com.game.entity.account.email.Email;
import com.game.entity.account.phone.Phone;
import com.game.entity.player.Player;
import com.game.entity.player.activity.rewardBox.RewardBoxInfo;
import com.game.entity.player.promote.PlayerPromote;
import com.game.entity.player.promote.ReferralCode;
import com.game.enums.redis.RedisHall;
import com.game.enums.redis.RedisLogin;
import com.game.hall.main.HallServer;
import com.game.hall.mrg.ActivityMrg;
import com.game.hall.mrg.player.PlayerMrg;
import com.game.hall.script.IPlayerScript;
import com.game.hall.script.IPromoteScript;
import com.game.hall.script.activity.IActivityScript;
import com.game.manager.EntityDaoMrg;
import com.game.utils.VirtualThreadUtils;
import com.proto.HallMessage;
import io.lettuce.core.api.async.RedisAsyncCommands;
import io.netty.util.internal.StringUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.function.Consumer;

public class PlayerCreateScript implements IPlayerScript {
    private static final Logger LOGGER = LoggerFactory.getLogger(PlayerCreateScript.class);

    @Override
    public Player createPlayer(HallMessage.ReqPlayerEntryHallMessage req, long accountId, int threeParty, Consumer<Player> callback) {
        final Account account = PlayerMrg.getInstance().findDbAccount(accountId);
        if (account == null) {
            throw new IllegalArgumentException("db error，accountId：" + accountId + "，not exist");
        }
        final Player player = new Player(accountId);
        player.setRegister(true);
        player.setBusiness_no(account.getBusiness_no());
        player.setHost(account.getHost());
        player.setAgentId(account.getAgentId());
        player.setChannelId(account.getChannelId());
        player.setMediaId(account.getMediaId());
        player.setAdId(account.getAdId());
        player.setRegisterRegion(StringUtil.isNullOrEmpty(account.getRegion()) ? "" : account.getRegion());
        player.setPixelId(account.getPixelId());
        player.setFbToken(account.getFbToken());
        player.setClickId(account.getClickId());
        player.setKWaiPixelId(account.getKWaiPixelId());
        player.setKWaiToken(account.getKWaiToken());
        player.setEmailSubscribe(account.isEmailSubscribe());
        player.setRegDevice(req.getDevice1());
        player.setRegModel(req.getModel1());
        player.setDevice(req.getDevice1());
        player.setModel(req.getModel1());
        player.setMac(req.getMac());
        player.getChannels().add(req.getChannel());

        if (callback != null) {
            callback.accept(player);
        }

        switch (threeParty) {
            case 1://邮箱
                final Email email = account.getEmailInfo();
                player.setEmail(email.getEmail());
                break;
            case 6://手机
                final Phone phone = account.getPhoneInfo();
                player.setAreaCode(phone.getAreaCode());
                player.setPhone(phone.getPhone());
                break;
            case 7://账号
                player.setAccount(account.getAccount());
                break;
            default://三方
                ThreePartyInfo threePartyInfo = account.getThreePartyInfoMap().get(threeParty);
                player.addThreePartyInfo(threeParty, threePartyInfo.getAccount(), threePartyInfo.getThreePartyId());
                break;
        }

        //TODO 创建推广数据
        final ReferralCode referralCode = PlayerMrg.getInstance().createReferralCode(player, "--");
        final PlayerPromote playerPromote = PlayerPromote.createPlayerPromote(player, referralCode);
        player.setPlayerPromote(playerPromote);

        ScriptLoader.getInstance().consumerScript("PromoteScript",
                (IPromoteScript script) -> script.bindSuperiorId(player, account.getActivity(), account.getReferralCode(), false));

        EntityDaoMrg.getInstance().getDao(PlayerPromoteDao.class)
                .insert(playerPromote);

        player.setInvitationCode(referralCode.getCode());
        EntityDaoMrg.getInstance().getDao(PlayerDao.class).insert(player);

        //绑定代理或者channelId日志
        final GameLog playerPromotionLog = new GameLog("platform_playerPromotionLog");
        playerPromotionLog.append("number", GuidGeneratorUtils.generateOrderId())
                .append("business_no", player.getBusiness_no())
                .append("playerId", player.getPlayerId())
                .append("playerName", player.getPlayerName())
                .append("registerTime", player.getCreateTime())
                .append("agentId", player.getAgentId())
                .append("channelId", player.getChannelId())
                .append("mediaId", player.getMediaId())
                .append("adId", player.getAdId())
                .append("region", player.getRegisterRegion())
                .append("registerIp", player.getRegisterIp())
                .append("logTime", TimeUtil.currentTimeMillis());
        HallServer.getInstance().getLogProducerMrg().send(playerPromotionLog);

        if (!StringUtil.isNullOrEmpty(account.getActivity())) {
            final String[] activity = account.getActivity().split("_");

            final String superiorId = RedisPoolManager.getInstance().function(jedis ->
                    jedis.sync().hget(RedisLogin.Platform_LG_Map_InvitationID.getKey(), account.getReferralCode()));

            if (activity.length > 0) {
                final GameLog playerReferralActivityLog = new GameLog("platform_playerReferralActivityLog");
                playerReferralActivityLog.append("number", GuidGeneratorUtils.generateOrderId())
                        .append("business_no", player.getBusiness_no())
                        .append("site", player.getWebSite())
                        .append("activityId", activity[0])
                        .append("activityUniqueId", activity[1])
                        .append("superiorId", StringUtil.isNullOrEmpty(superiorId) ? "" : superiorId)
                        .append("language", player.getLanguage())
                        .append("playerId", player.getPlayerId())
                        .append("playerName", player.getPlayerName())
                        .append("agentId", player.getAgentId())
                        .append("channelId", player.getChannelId())
                        .append("region", player.getRegisterRegion())
                        .append("logTime", TimeUtil.currentTimeMillis());
                HallServer.getInstance().getLogProducerMrg().send(playerReferralActivityLog);

                if (!StringUtil.isNullOrEmpty(superiorId) && Integer.parseInt(activity[0]) == ActivityMrg.REWARD_BOX) {
                    final Player superiorPlayer = PlayerMrg.getInstance().findDbPlayer(Long.parseLong(superiorId));
                    if (superiorPlayer != null) {
                        final RewardBoxInfo rewardBoxInfo = superiorPlayer.getRewardBoxInfo();

                        final boolean isSame = ScriptLoader.getInstance().functionScript("ActivityScript",
                                (IActivityScript script) -> script.isSameNetworkOrDevice(player, superiorPlayer, ActivityMrg.REWARD_BOX));
                        if (rewardBoxInfo.isStart() && !isSame) {
                            RedisPoolManager.getInstance().asyncPipeline(commands -> {
                                        final List<CompletableFuture<?>> future = new ArrayList<>();
                                        future.add(commands.sadd(RedisHall.Platform_Role_Map_RewardBoxReferral.getKey(superiorPlayer.getPlayerId()), player.getPlayerId() + "")
                                                .toCompletableFuture());
                                        future.add(commands.sadd(RedisHall.Platform_Role_Map_RewardBox_Ip.getKey(superiorPlayer.getPlayerId()), player.getIp())
                                                .toCompletableFuture());
                                        if (!StringUtil.isNullOrEmpty(req.getDevice1())) {
                                            future.add(commands.sadd(RedisHall.Platform_Role_Map_RewardBox_Device.getKey(superiorPlayer.getPlayerId()), player.getDevice() + "-" + player.getModel())
                                                    .toCompletableFuture());
                                        }
                                        return future;
                                    }
                            );
                        }
                    }
                }
            }
        }

        // TODO 记录创建日志
        final GameLog playerCreateLog = new GameLog("platform_playerCreateLog");
        playerCreateLog.append("number", GuidGeneratorUtils.generateOrderId())
                .append("business_no", player.getBusiness_no())
                .append("site", player.getWebSite())
                .append("playerId", player.getPlayerId())
                .append("playerName", player.getPlayerName())
                .append("registerTime", player.getCreateTime())
                .append("channel", req.getChannel())
                .append("agentId", player.getAgentId())
                .append("channelId", player.getChannelId())
                .append("mediaId", player.getMediaId())
                .append("adId", player.getAdId())
                .append("region", player.getRegisterRegion())
                .append("registerIp", player.getRegisterIp())
                .append("device", req.getDevice1())
                .append("model", req.getModel1())
                .append("browser", req.getBrowser())
                .append("logTime", TimeUtil.currentTimeMillis());
        HallServer.getInstance().getLogProducerMrg().send(playerCreateLog);

        return player;
    }

}
