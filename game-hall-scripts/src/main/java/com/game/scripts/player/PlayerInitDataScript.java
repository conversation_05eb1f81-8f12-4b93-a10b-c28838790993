//package com.game.scripts.player;
//
//import com.game.engine.utils.TimeUtil;
//import com.game.entity.player.Player;
//import com.game.enums.Currency;
//import com.game.hall.mrg.DataHallMrg;
//import com.game.hall.mrg.MerchantData;
//import com.game.hall.script.IPlayerScript;
//
//public class PlayerInitDataScript implements IPlayerScript {
//
//    @Override
//    public void initPlayerData(Player player) {
//        player.setState(1);
//        player.setHeadId(String.valueOf(1000));
//        player.setLoginTime(TimeUtil.currentTimeMillis());
//        player.setLogoutTime(TimeUtil.currentTimeMillis());
//        player.setLastRefreshTime(TimeUtil.currentTimeMillis());
//
//        final MerchantData merchantData = DataHallMrg.getInstance().findMerchantData(this.getClass().getSimpleName(), player.getBusiness_no());
//        if (merchantData != null) {
//
//        }
//        player.setLanguage(1);
//        player.setCurrencyId(Currency.BRL.getCurrencyId());
//        player.setViewFiat(Currency.USD.getCurrencyId());
//    }
//}
