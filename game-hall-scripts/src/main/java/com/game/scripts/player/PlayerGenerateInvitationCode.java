package com.game.scripts.player;

import com.game.engine.io.redis.RedisPoolManager;
import com.game.engine.utils.ShareCodeUtil;
import com.game.enums.redis.RedisLogin;
import com.game.hall.script.IPlayerScript;
import io.lettuce.core.ScriptOutputType;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.List;

public class PlayerGenerateInvitationCode implements IPlayerScript {
    private static final Logger LOGGER = LoggerFactory.getLogger(PlayerGenerateInvitationCode.class);

    @Override
    public String generateInvitationCode(long playerId) {
        return RedisPoolManager.getInstance().function(jedis -> {
            try {
                int batchSize = 5; // 每次尝试5个邀请码
                int maxRetries = 20;  // 最多重试20次（每次尝试5个，总尝试100个码）
                int retryCount = 0;

                final String luaScript =
                        "for i=1, #ARGV do " +
                                "    if redis.call('hsetnx', KEYS[1], ARGV[i], ARGV[#ARGV]) == 1 then " +
                                "        return ARGV[i] " +
                                "    end " +
                                "end " +
                                "return nil";

                while (retryCount < maxRetries) {
                    final List<String> codes = new ArrayList<>();
                    for (int i = 0; i < batchSize; i++) {
                        codes.add(ShareCodeUtil.generateShareCode());
                    }

                    final List<String> args = new ArrayList<>(codes);
                    args.add(String.valueOf(playerId)); // 把accountId放在最后一个ARGV

                    final String result = jedis.sync().eval(
                            luaScript,
                            ScriptOutputType.VALUE,
                            new String[]{RedisLogin.Platform_LG_Map_InvitationID.getKey()},
                            args.toArray(new String[0])
                    );

                    if (result != null) {
                        return result; // 成功返回
                    }

                    retryCount++;

                    if (retryCount % 5 == 0) {
                        try {
                            Thread.sleep(1); // 每5次冲突稍微让下CPU
                        } catch (InterruptedException e) {
                            LOGGER.error("InterruptedException", e);
                        }
                    }

                    LOGGER.warn("批量生成邀请码失败，重试 {}/{}", retryCount, maxRetries);
                }

                throw new IllegalStateException("生成邀请码失败，请稍后重试");
            } catch (Exception e) {
                LOGGER.error("generateInvitationCode", e);
                return null;
            }
        });
    }
}
