package com.game.scripts.player;

import com.game.c_entity.merchant.*;
import com.game.dao.player.PlayerDao;
import com.game.engine.BlockingTaskSchedulerMrg;
import com.game.entity.player.Player;
import com.game.entity.player.PlayerFields;
import com.game.entity.player.activity.ActivityInfo;
import com.game.entity.player.activity.continuousDeposit.ContinuousDeposit;
import com.game.entity.player.activity.firstChargeSignIn.FirstChargeSignInInfo;
import com.game.entity.player.activity.firstDepositInviteBonus.FirstDepositInviteBonusInfo;
import com.game.entity.player.activity.mysteryBonus.MysteryBonusInfo;
import com.game.entity.player.activity.piggyBank.PiggyBankInfo;
import com.game.entity.player.activity.rechargeRecover.RechargeRecoverInfo;
import com.game.entity.player.activity.redEnvelopeRain.RedEnvelopeRainInfo;
import com.game.entity.player.activity.dailyContest.DailyContestInfo;
import com.game.entity.player.activity.luckSpin.LuckSpinInfo;
import com.game.entity.player.activity.rewardBox.RewardBoxInfo;
import com.game.entity.player.activity.wageredRebates.WageredRebatesInfo;
import com.game.entity.player.activity.weeklyRaffle.WeeklyRaffleInfo;
import com.game.entity.player.bonus.BonusInfo;
import com.game.entity.player.stats.StatsInfo;
import com.game.hall.main.HallServer;
import com.game.hall.mrg.ActivityMrg;
import com.game.hall.mrg.DataHallMrg;
import com.game.hall.mrg.MerchantData;
import com.game.hall.script.IPlayerScript;
import com.game.manager.EntityDaoMrg;
import it.unimi.dsi.fastutil.ints.IntArrayList;
import it.unimi.dsi.fastutil.ints.IntList;
import it.unimi.dsi.fastutil.longs.LongArrayList;
import it.unimi.dsi.fastutil.longs.LongList;
import it.unimi.dsi.fastutil.longs.LongLists;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.mongodb.core.query.Update;

public class PlayerSaveScript implements IPlayerScript {
    private static final Logger LOGGER = LoggerFactory.getLogger(PlayerSaveScript.class);

    @Override
    public void saveRealPlayer(Player player) {
        //货币
        final IntList currencyList = new IntArrayList(player.getCurrencyMap().keySet());
        EntityDaoMrg.getInstance().getDao(PlayerDao.class)
                .updateCurrency(player, currencyList);

        //提现标准
        final IntList updateCurrencyList = new IntArrayList(player.getWithdrawStandardMap().keySet());
        EntityDaoMrg.getInstance().getDao(PlayerDao.class)
                .updateWithdrawStandard(player, updateCurrencyList);

        //打码记录
        final LongList updateOrderIdList = new LongArrayList(player.getTurnoverRecordMap().keySet());
        EntityDaoMrg.getInstance().getDao(PlayerDao.class)
                .updateTurnoverRecord(player, updateOrderIdList, LongLists.EMPTY_LIST);

        //最后一次变账
        final Update update = new Update();
        update.set(PlayerFields.lastChangeTime, player.getLastChangeTime());
        EntityDaoMrg.getInstance().getDao(PlayerDao.class)
                .updatePlayer(player.getPlayerId(), update);
    }

    @Override
    public void savePlayer(Player player) {
        final MerchantData merchantData = DataHallMrg.getInstance()
                .findMerchantData(this.getClass().getSimpleName(), player.getBusiness_no());

        //统计
        final StatsInfo statsInfo = player.getStatsInfo();
        final IntList statsIntList = new IntArrayList(statsInfo.getStatsMap().keySet());
        EntityDaoMrg.getInstance().getDao(PlayerDao.class)
                .statsDao.updateStats(player.getPlayerId(), statsInfo, statsIntList);

        //vip
        EntityDaoMrg.getInstance().getDao(PlayerDao.class)
                .updateVipClubInfo(player.getPlayerId(), player.getVipClub());

        //BonusInfo
        final BonusInfo bonusInfo = player.getBonusInfo();
        final IntList bonusTypeList = new IntArrayList(bonusInfo.getBonusProcessInfoMap().keySet());
        EntityDaoMrg.getInstance().getDao(PlayerDao.class).bonusInfoDao
                .updateBonusProcessInfo(player, bonusInfo, bonusTypeList);

        final ActivityInfo activityInfo = player.getActivityInfo();
        EntityDaoMrg.getInstance().getDao(PlayerDao.class)
                .updateActivity(player, activityInfo);

        //luckSpin
        final LuckSpinInfo luckSpinInfo = player.getLuckSpinInfo();
        EntityDaoMrg.getInstance().getDao(PlayerDao.class)
                .updateLuckSpinInfo(player.getPlayerId(), luckSpinInfo);

        //dailyContest
        final DailyContestInfo dailyContestInfo = player.getDailyContestInfo();
        EntityDaoMrg.getInstance().getDao(PlayerDao.class)
                .updateDailyContestInfo(player.getPlayerId(), dailyContestInfo);

        //weeklyRaffle
        final WeeklyRaffleInfo weeklyRaffleInfo = player.getWeeklyRaffleInfo();
        EntityDaoMrg.getInstance().getDao(PlayerDao.class)
                .updateWeeklyRaffleInfo(player.getPlayerId(), weeklyRaffleInfo);

        //redEnvelopeRain
        final RedEnvelopeRainInfo rainInfo = player.getRedEnvelopeRainInfo();
        EntityDaoMrg.getInstance().getDao(PlayerDao.class)
                .updateRedEnvelopeRainInfo(player.getPlayerId(), rainInfo);

        if (merchantData != null) {
            final C_MysteryBonus c_mysteryBonus = merchantData
                    .findC_MysteryBonus(this.getClass().getSimpleName(), ActivityMrg.MYSTERY_BONUS);
            if (c_mysteryBonus != null) {
                final MysteryBonusInfo mysteryBonusInfo = player.getMysteryBonusInfo();
                EntityDaoMrg.getInstance().getDao(PlayerDao.class)
                        .updateMysteryBonusInfo(player.getPlayerId(), mysteryBonusInfo);
            }
        }

        //rewardBoxInfo
        if (merchantData != null) {
            final C_RewardBox c_rewardBox = merchantData
                    .findC_RewardBox(this.getClass().getSimpleName(), ActivityMrg.REWARD_BOX);
            if (c_rewardBox != null) {
                final RewardBoxInfo rewardBoxInfo = player.getRewardBoxInfo();
                EntityDaoMrg.getInstance().getDao(PlayerDao.class)
                        .updateRewardBoxInfo(player.getPlayerId(), rewardBoxInfo);
            }
        }

        //piggyBankInfo
        if (merchantData != null) {
            final C_PiggyBank c_piggyBank = merchantData
                    .findC_PiggyBank(this.getClass().getSimpleName(), ActivityMrg.PIGGY_BANK);
            if (c_piggyBank != null) {
                final PiggyBankInfo piggyBankInfo = player.getPiggyBankInfo();
                EntityDaoMrg.getInstance().getDao(PlayerDao.class)
                        .updatePiggyBankInfo(player.getPlayerId(), piggyBankInfo);
            }
        }

        //continuousDeposit
        if (merchantData != null) {
            final C_ContinuousDeposit c_continuousDeposit = merchantData
                    .findC_ContinuousDeposit(this.getClass().getSimpleName(), ActivityMrg.CONTINUOUS_DEPOSIT);
            if (c_continuousDeposit != null) {
                final ContinuousDeposit continuousDepositInfo = player.getContinuousDepositInfo();
                EntityDaoMrg.getInstance().getDao(PlayerDao.class)
                        .updateContinuousDepositInfo(player.getPlayerId(), continuousDepositInfo);
            }
        }

        //firstChargeSignInInfo
        if (merchantData != null) {
            final C_FirstChargeSignIn c_firstChargeSignIn = merchantData
                    .findC_FirstChargeSignIn(this.getClass().getSimpleName(), ActivityMrg.FIRSTCHARGE_SIGNIN);
            if (c_firstChargeSignIn != null) {
                final FirstChargeSignInInfo firstChargeSignInInfo = player.getFirstChargeSignInInfo();
                EntityDaoMrg.getInstance().getDao(PlayerDao.class)
                        .updateFirstChargeSignInInfo(player.getPlayerId(), firstChargeSignInInfo);
            }
        }

        //rechargeRecoverInfo
        if (merchantData != null) {
            final C_RechargeRecover c_rechargeRecover = merchantData
                    .findC_RechargeRecover(this.getClass().getSimpleName(), ActivityMrg.RECHARGE_RECOVER);
            if (c_rechargeRecover != null) {
                final RechargeRecoverInfo rechargeRecoverInfo = player.getRechargeRecoverInfo();
                EntityDaoMrg.getInstance().getDao(PlayerDao.class)
                        .updateRechargeRecoverInfo(player.getPlayerId(), rechargeRecoverInfo);
            }
        }

        //wageredRebatesInfo
        if (merchantData != null) {
            final C_WageredRebates c_wageredRebates = merchantData
                    .findC_WageredRebates(this.getClass().getSimpleName(), ActivityMrg.WAGERED_REBATES);
            if (c_wageredRebates != null) {
                final WageredRebatesInfo wageredRebatesInfo = player.getWageredRebatesInfo();
                EntityDaoMrg.getInstance().getDao(PlayerDao.class)
                        .updateWageredRebatesInfo(player.getPlayerId(), wageredRebatesInfo);
            }
        }

        //firstDepositInviteBonusInfo
        if (merchantData != null) {
            final C_FirstDepositInviteBonus c_firstDepositInviteBonus = merchantData
                    .findC_FirstDepositInviteBonus(this.getClass().getSimpleName(), ActivityMrg.FIRSTDEPOSIT_INIVTEBONUS);
            if (c_firstDepositInviteBonus != null) {
                final FirstDepositInviteBonusInfo firstDepositInviteBonusInfo = player.getFirstDepositInviteBonusInfo();
                EntityDaoMrg.getInstance().getDao(PlayerDao.class)
                        .updateFirstDepositInviteBonusInfo(player.getPlayerId(), firstDepositInviteBonusInfo);
            }
        }
    }
}
