package com.game.scripts.player;

import com.game.c_entity.merchant.C_RechargeWithdrawLimit;
import com.game.dao.player.PlayerDao;
import com.game.engine.log.GameLog;
import com.game.engine.math.BigDecimalUtils;
import com.game.engine.utils.GuidGeneratorUtils;
import com.game.engine.utils.TimeUtil;
import com.game.engine.utils.UniqueIDGenerator;
import com.game.entity.player.Player;
import com.game.entity.player.TurnoverRecord;
import com.game.entity.player.WithdrawStandard;
import com.game.enums.TurnoverReason;
import com.game.hall.main.HallServer;
import com.game.hall.mrg.DataHallMrg;
import com.game.hall.mrg.MerchantData;
import com.game.hall.script.IPlayerScript;
import com.game.manager.EntityDaoMrg;
import it.unimi.dsi.fastutil.longs.LongArrayList;
import it.unimi.dsi.fastutil.longs.LongList;
import it.unimi.dsi.fastutil.longs.LongLists;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Iterator;
import java.util.Map;

public class PlayerWithdrawStandardScript implements IPlayerScript {
    private static final Logger LOGGER = LoggerFactory.getLogger(PlayerWithdrawStandardScript.class);

    /**
     * 要求洗码量
     *
     * @param player
     * @param turnoverReason
     * @param currencyId
     * @param amount
     * @param turnover
     */
    @Override
    public void drawStandard(Player player, TurnoverReason turnoverReason, int currencyId, double amount, double turnover) {
        final MerchantData merchantData = DataHallMrg.getInstance().findMerchantData(this.getClass().getSimpleName(), player.getBusiness_no());
        if (merchantData == null) {
            return;
        }
        final C_RechargeWithdrawLimit c_limit = merchantData.findC_RechargeWithdrawLimit(this.getClass().getSimpleName(), currencyId);
        if (c_limit == null) {
            return;
        }

        switch (c_limit.getWithdrawType()) {
            case 1:
            case 2:
                if (currencyId == 0 || turnover == 0) {
                    return;
                }
                final WithdrawStandard withdrawStandard = player.getWithdrawStandard(currencyId);

                final double beforeGold = withdrawStandard.getDrawStandard();
                withdrawStandard.incDrawStandard(turnover);
                //TODO 洗码量日志
                playerWithdrawStandardLog(player, currencyId, 1,
                        turnoverReason, beforeGold, turnover);
                return;
            case 3:
                if (amount == 0 || turnover <= 0) {
                    return;
                }
                final UniqueIDGenerator uniqueIDGenerator = HallServer.getInstance().getUniqueIDGenerator();
                final Map<Long, TurnoverRecord> turnoverRecordMap = player.getTurnoverRecordMap();
                final long uniqueId = uniqueIDGenerator.nextId();
                final TurnoverRecord turnoverRecord = new TurnoverRecord();
                turnoverRecord.setOrderId(uniqueId);
                turnoverRecord.setTurnoverType(turnoverReason.getReason());
                turnoverRecord.setCurrencyId(currencyId);
                turnoverRecord.setAmount(amount);
                turnoverRecord.setDrawStandard(turnover);
                turnoverRecordMap.put(uniqueId, turnoverRecord);
                //TODO 日志
                playerTurnoverRecordLog(player, turnoverReason, uniqueId,
                        currencyId, amount, turnover, 0);
        }
    }

    /**
     * 实际洗码量
     *
     * @param player
     * @param turnoverReason
     * @param currencyId
     * @param turnover
     */
    @Override
    public void bettingTurnover(Player player, TurnoverReason turnoverReason, int currencyId, double turnover) {
        final MerchantData merchantData = DataHallMrg.getInstance().findMerchantData(this.getClass().getSimpleName(), player.getBusiness_no());
        if (merchantData == null) {
            return;
        }
        final C_RechargeWithdrawLimit c_limit = merchantData.findC_RechargeWithdrawLimit(this.getClass().getSimpleName(), currencyId);
        if (c_limit == null) {
            return;
        }
        switch (c_limit.getWithdrawType()) {
            case 1:
            case 2:
                if (currencyId == 0 || turnover == 0) {
                    return;
                }
                final WithdrawStandard withdrawStandard = player.getWithdrawStandard(currencyId);

                final double beforeGold = withdrawStandard.getBettingVolume();
                withdrawStandard.incBettingTurnover(turnover);
                //TODO 洗码量日志
                playerWithdrawStandardLog(player, currencyId, 2,
                        turnoverReason, beforeGold, turnover);
                return;
            case 3:
                if (turnover <= 0) {
                    return;
                }

                final LongList deleteOrderId = new LongArrayList();
                final Iterator<Map.Entry<Long, TurnoverRecord>> iterator = player.getTurnoverRecordMap().entrySet().iterator();
                double remain = turnover;
                while (iterator.hasNext()) {
                    final Map.Entry<Long, TurnoverRecord> entry = iterator.next();
                    final TurnoverRecord turnoverRecord = entry.getValue();
                    if (turnoverRecord == null) {
                        continue;
                    }
                    if (turnoverRecord.getDrawStandard() == turnoverRecord.getBettingVolume()) {
                        iterator.remove();
                        deleteOrderId.add(turnoverRecord.getOrderId());
                        continue;
                    }

                    final double availableToFill = BigDecimalUtils.sub(turnoverRecord.getDrawStandard(), turnoverRecord.getBettingVolume(), 4);
                    final double usedAmount = Math.min(remain, availableToFill);
                    turnoverRecord.setBettingVolume(BigDecimalUtils.add(turnoverRecord.getBettingVolume(), usedAmount, 4));
                    //LOGGER.warn("orderId：{}，BettingVolume：{}", turnoverRecord.getOrderId(), turnoverRecord.getBettingVolume());
                    remain = BigDecimalUtils.sub(remain, usedAmount, 4);

                    //TODO 日志
                    playerTurnoverRecordLog(player, turnoverReason, turnoverRecord.getOrderId(),
                            currencyId, turnoverRecord.getAmount(), turnoverRecord.getDrawStandard(), turnoverRecord.getBettingVolume());

                    if (turnoverRecord.getDrawStandard() == turnoverRecord.getBettingVolume()) {
                        iterator.remove();
                        deleteOrderId.add(turnoverRecord.getOrderId());
                    }

                    if (remain <= 0) {
                        break;
                    }
                }
                EntityDaoMrg.getInstance().getDao(PlayerDao.class)
                        .updateTurnoverRecord(player, LongLists.EMPTY_LIST, deleteOrderId);
        }
    }

    private void playerWithdrawStandardLog(Player player, int currencyId, int turnoverType,
                                           TurnoverReason turnoverReason, double beforeGold, double changeGold) {
        final WithdrawStandard withdrawStandard = player.getWithdrawStandard(currencyId);

        final GameLog playerWithdrawStandardLog = new GameLog("platform_playerWithdrawStandardLog");
        playerWithdrawStandardLog.append("number", GuidGeneratorUtils.generateOrderId())
                .append("site", player.getWebSite())
                .append("business_no", player.getBusiness_no())
                .append("playerId", player.getPlayerId())
                .append("playerName", player.getPlayerName())
                .append("region", player.getRegisterRegion())
                .append("currencyId", currencyId)
                .append("agentId", player.getAgentId())
                .append("channelId", player.getChannelId())
                .append("mediaId", player.getMediaId())
                .append("adId", player.getAdId())
                .append("changeType", turnoverReason.getReason())//改变类型
                .append("source", turnoverReason.getSource())//来源
                .append("turnoverType", turnoverType);//打码类型 1.标准 2.实际
        if (turnoverType == 1) {//标准
            playerWithdrawStandardLog
                    .append("beforeDrawStandard", beforeGold)
                    .append("changeGold", changeGold)
                    .append("afterDrawStandard", withdrawStandard.getDrawStandard())
                    .append("bettingVolume", withdrawStandard.getBettingVolume());
        } else {//实际
            playerWithdrawStandardLog
                    .append("drawStandard", withdrawStandard.getDrawStandard())
                    .append("beforeBettingVolume", beforeGold)
                    .append("changeGold", changeGold)
                    .append("afterBettingVolume", withdrawStandard.getBettingVolume());
        }
        playerWithdrawStandardLog.append("logTime", TimeUtil.currentTimeMillis());
        HallServer.getInstance().getLogProducerMrg().send(playerWithdrawStandardLog);
    }

    private void playerTurnoverRecordLog(Player player, TurnoverReason turnoverReason, long uniqueId
            , int currencyId, double amount, double drawStandard, double bettingVolume) {
        final GameLog platform_playerTurnoverRecordLog = new GameLog("platform_playerTurnoverRecordLog");
        platform_playerTurnoverRecordLog.append("number", GuidGeneratorUtils.generateOrderId())
                .append("site", player.getWebSite())
                .append("business_no", player.getBusiness_no())
                .append("playerId", player.getPlayerId())
                .append("playerName", player.getPlayerName())
                .append("region", player.getRegisterRegion())
                .append("agentId", player.getAgentId())
                .append("channelId", player.getChannelId())
                .append("mediaId", player.getMediaId())
                .append("adId", player.getAdId())
                .append("turnoverReason", turnoverReason.getReason())//改变类型
                .append("orderId", uniqueId)
                .append("currencyId", currencyId)
                .append("amount", amount)
                .append("drawStandard", drawStandard)
                .append("bettingVolume", bettingVolume)
                .append("logTime", TimeUtil.currentTimeMillis());
        HallServer.getInstance().getLogProducerMrg().send(platform_playerTurnoverRecordLog);
    }

}
