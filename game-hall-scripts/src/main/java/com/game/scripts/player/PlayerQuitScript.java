package com.game.scripts.player;

import com.game.dao.player.PlayerDao;
import com.game.engine.log.GameLog;
import com.game.engine.script.ScriptLoader;
import com.game.engine.utils.GuidGeneratorUtils;
import com.game.engine.utils.TimeUtil;
import com.game.entity.player.Player;
import com.game.entity.player.PlayerFields;
import com.game.hall.main.HallServer;
import com.game.hall.mrg.player.PlayerMrg;
import com.game.hall.script.IAgentGameScript;
import com.game.hall.script.IPlayerScript;
import com.game.manager.EntityDaoMrg;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.mongodb.core.query.Update;

public class PlayerQuitScript implements IPlayerScript {
    private static final Logger LOGGER = LoggerFactory.getLogger(PlayerQuitScript.class);

    @Override
    public void quitHall(Player player) {
        final long time = TimeUtil.currentTimeMillis();
        player.setLogoutTime(time);
        final long onlineTime = (player.getLogoutTime() - player.getLoginTime()) / TimeUtil.SEC;
        if (onlineTime > 0) {
            player.setOnlineTime(player.getOnlineTime() + onlineTime);
        }
        player.setOnline(false);
        if (player.getFirstOnlineTime() == 0) {//首次在线时间
            player.setFirstOnlineTime((time - player.getLoginTime()) / TimeUtil.SEC);
        }
        final Update update = new Update();
        update.set(PlayerFields.logoutTime, player.getLogoutTime())
                .set(PlayerFields.onlineTime, player.getOnlineTime())
                .set(PlayerFields.online, player.isOnline())
                .set(PlayerFields.firstOnlineTime, player.getFirstOnlineTime());
        EntityDaoMrg.getInstance().getDao(PlayerDao.class)
                .updatePlayer(player.getPlayerId(), update);

        ScriptLoader.getInstance().consumerScript("AgentGameScript",
                (IAgentGameScript script) -> script.statisticalGameNoteData(player));

        //TODO 退出保存数据
        PlayerMrg.getInstance().saveDBPlayer(player);
        PlayerMrg.getInstance().saveRealDBPlayer(player);

        //日志
        final GameLog playerLoginOutLog = new GameLog("platform_playerLogoutLog");
        playerLoginOutLog.append("number", GuidGeneratorUtils.generateOrderId())
                .append("business_no", player.getBusiness_no())
                .append("playerId", player.getPlayerId())
                .append("playerName", player.getPlayerName())
                .append("firstOnlineTime", player.getFirstOnlineTime())
                .append("onlineTime", onlineTime)//秒
                .append("logoutTime", player.getLogoutTime())
                .append("ip", player.getIp())
                .append("channelId", player.getChannelId())
                .append("mediaId", player.getMediaId())
                .append("adId", player.getAdId())
                .append("region", player.getRegisterRegion())
                .append("logTime", TimeUtil.currentTimeMillis());
        HallServer.getInstance().getLogProducerMrg().send(playerLoginOutLog);

        LOGGER.info("business_no：{}，player，[{}-{}]，logout hall，onlineNum：{}", player.getBusiness_no(),
                player.getPlayerName(), player.getPlayerId(), PlayerMrg.getInstance().getOnlinePlayerMap().size());
    }
}
