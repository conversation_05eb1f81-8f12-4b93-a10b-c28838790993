package com.game.scripts.inbox;

import com.game.c_entity.merchant.C_PubMail;
import com.game.c_entity.middleplatform.C_BaseInbox;
import com.game.dao.player.PlayerDao;
import com.game.engine.utils.TimeUtil;
import com.game.engine.utils.UniqueIDGenerator;
import com.game.entity.player.Player;
import com.game.entity.player.inbox.Inbox;
import com.game.entity.player.inbox.InboxInfo;
import com.game.hall.main.HallServer;
import com.game.hall.mrg.DataHallMrg;
import com.game.hall.mrg.MerchantData;
import com.game.hall.script.IInboxScript;
import com.game.manager.EntityDaoMrg;
import com.proto.CommonMessage;
import com.proto.MIDMessage;
import com.proto.TcpMessage;
import io.netty.util.internal.StringUtil;
import it.unimi.dsi.fastutil.longs.LongArrayList;
import it.unimi.dsi.fastutil.longs.LongList;
import it.unimi.dsi.fastutil.longs.LongLists;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

public class InboxScript implements IInboxScript {
    private static final Logger LOGGER = LoggerFactory.getLogger(InboxScript.class);

    @Override
    public void sendPubMail(Player player) {
        final List<Integer> pubMailList = findPubMailList(player);
        addPlayerPubMail(player, pubMailList);
    }

    @Override
    public void deleteExpireInbox(Player player) {
        final InboxInfo inboxInfo = player.getInboxInfo();
        final List<Long> mailIds = inboxInfo.getInboxMap().values().stream()
                .filter(inbox -> TimeUtil.currentTimeMillis() >= inbox.getExpireTime())
                .map(Inbox::getInboxId).collect(Collectors.toList());
        for (long mailId : mailIds) {
            inboxInfo.getInboxMap().remove(mailId);
        }
        EntityDaoMrg.getInstance().getDao(PlayerDao.class).inboxDao
                .updateDeleteInbox(player.getPlayerId(), inboxInfo, new LongArrayList(mailIds));
    }

    @Override
    public void sendEventPubMail(Player player, int event, boolean send) {
        final MerchantData merchantData = DataHallMrg.getInstance().findMerchantData(this.getClass().getSimpleName(), player.getBusiness_no());
        if (merchantData == null) {
            return;
        }
        final List<C_PubMail> c_pubMailList = merchantData.getC_eventPubMailMap().get(event);
        if (c_pubMailList == null) {
            return;
        }

        final InboxInfo inboxInfo = player.getInboxInfo();
        final LongList longList = new LongArrayList();
        for (C_PubMail c_pubMail : c_pubMailList) {
            if (!c_pubMail.getPubMailInfoMap().containsKey(player.getLanguage())) {
                continue;
            }

            final Inbox inbox = addInbox(player, c_pubMail);
            longList.add(inbox.getInboxId());
        }
        EntityDaoMrg.getInstance().getDao(PlayerDao.class).inboxDao
                .updateInsertInbox(player.getPlayerId(), player.getInboxInfo(), longList);
        EntityDaoMrg.getInstance().getDao(PlayerDao.class).inboxDao
                .updateReceivedPublicMails(player.getPlayerId(), inboxInfo);

        if (!send || longList.isEmpty()) {
            return;
        }

        final TcpMessage.ResTcpReceiveInboxMessage.Builder res = TcpMessage.ResTcpReceiveInboxMessage.newBuilder();
        res.setMsgID(MIDMessage.MID.ResTcpReceiveInbox_VALUE);
        for (long inboxId : longList) {
            final Inbox inbox = inboxInfo.getInboxMap().get(inboxId);
            if (inbox == null) {
                continue;
            }
            res.addInboxList(builderInboxInfo(inbox));
        }
        player.sendMsg(res.build());
    }

    /**
     * int32 jumpType                = 6; //1.内链 2.外链
     * int32 popupLinks              = 7; //弹框类型 1.任务 2.转盘 3.充值 4.客服
     * string innerLinks             = 8; //内部链接
     * string externalLinks          = 9; //外链接
     *
     * @param inbox
     * @return
     */
    @Override
    public CommonMessage.InboxInfo builderInboxInfo(Inbox inbox) {
        final CommonMessage.InboxInfo.Builder inboxInfo = CommonMessage.InboxInfo.newBuilder();
        inboxInfo.setInboxId(inbox.getInboxId() + "")
                .setInboxType(inbox.getInboxType())
                .setTitle(StringUtil.isNullOrEmpty(inbox.getTitle()) ? "" : inbox.getTitle())
                .setContext(StringUtil.isNullOrEmpty(inbox.getContext()) ? "" : inbox.getContext())
                .setIsJump(inbox.getIsJump())
                .setJumpType(inbox.getJumpType())
                .setPopupLinks(inbox.getPopupLinks())
                .setInnerLinks(StringUtil.isNullOrEmpty(inbox.getInnerLinks()) ? "" : inbox.getInnerLinks())
                .setExternalLinks(StringUtil.isNullOrEmpty(inbox.getExternalLinks()) ? "" : inbox.getExternalLinks())
                .setFileUrl(StringUtil.isNullOrEmpty(inbox.getFileUrl()) ? "" : inbox.getFileUrl())
                .setRead(inbox.isRead())
                .setCreateTime(inbox.getCreateTime())
                .setImageText(StringUtil.isNullOrEmpty(inbox.getImageText()) ? "" : inbox.getImageText());
        return inboxInfo.build();
    }

    @Override
    public void sendInboxMail(Player player, int inboxId, List<String> params) {
        final C_BaseInbox c_inbox = DataHallMrg.getInstance().findC_BaseInbox(this.getClass().getSimpleName(), inboxId);
        if (c_inbox == null) {
            return;
        }
        if (!c_inbox.getInboxInfoMap().containsKey(player.getLanguage())) {
            return;
        }
        final Inbox inbox = addInbox(player, c_inbox);
        if (inbox.getContext().split("%s").length - 1 == params.size()) {
            final String formattedStr = String.format(inbox.getContext(), params.toArray());
            inbox.setContext(formattedStr);
            EntityDaoMrg.getInstance().getDao(PlayerDao.class).inboxDao
                    .updateInsertInbox(player.getPlayerId(), player.getInboxInfo(), LongLists.singleton(inbox.getInboxId()));

            final TcpMessage.ResTcpReceiveInboxMessage.Builder res = TcpMessage.ResTcpReceiveInboxMessage.newBuilder();
            res.setMsgID(MIDMessage.MID.ResTcpReceiveInbox_VALUE)
                    .addInboxList(builderInboxInfo(inbox));
            player.sendMsg(res.build());
        } else {
            LOGGER.warn("playerId：{}，inboxId；{}，Number of placeholders does not match the number of list elements.", player.getPlayerId(), inbox.getInboxId());
        }
    }

    private Inbox createInbox(int language, C_PubMail c_pubMail) {
        final UniqueIDGenerator uniqueIDGenerator = HallServer.getInstance().getUniqueIDGenerator();
        final Inbox inbox = new Inbox(uniqueIDGenerator.nextId());
        final C_PubMail.PubMailInfo pubMailInfo = c_pubMail.getPubMailInfoMap().get(language);
        inbox.setTitle(pubMailInfo.getTitle());
        inbox.setContext(pubMailInfo.getContent());
        inbox.setImageText(pubMailInfo.getImageText());
        inbox.setLanguage(pubMailInfo.getLanguage());
        inbox.setModelId(c_pubMail.getMailId());
        inbox.setInboxType(c_pubMail.getMailType());
        inbox.setIsJump(c_pubMail.getIsJump());
        inbox.setJumpType(c_pubMail.getJumpType());
        inbox.setPopupLinks(c_pubMail.getPopupLinks());
        inbox.setInnerLinks(c_pubMail.getInnerLinks());
        inbox.setExternalLinks(c_pubMail.getExternalLinks());
        inbox.setFileUrl(c_pubMail.getFileUrl());
        inbox.setExpireTime(TimeUtil.currentTimeMillis() + c_pubMail.getExpirationTime());
        return inbox;
    }

    private Inbox addInbox(Player player, C_PubMail c_pubMail) {
        final Inbox inbox = createInbox(player.getLanguage(), c_pubMail);
        final InboxInfo inboxInfo = player.getInboxInfo();
        inboxInfo.getInboxMap().put(inbox.getInboxId(), inbox);
        return inbox;
    }

    private List<Integer> findPubMailList(Player player) {
        final MerchantData merchantData = DataHallMrg.getInstance().findMerchantData(this.getClass().getSimpleName(), player.getBusiness_no());
        if (merchantData == null) {
            return new ArrayList<>();
        }
        final InboxInfo inboxInfo = player.getInboxInfo();
        final long curTime = TimeUtil.currentTimeMillis();
        final List<C_PubMail> c_pubMails = merchantData.getC_pubMailMap().values().stream()
                .filter(c_pubMail -> c_pubMail.getStartTime() <= curTime && curTime < c_pubMail.getEndTime()).toList();
        final List<Integer> list = new ArrayList<>(5);
        for (C_PubMail c_pubMail : c_pubMails) {
            if (!c_pubMail.getPubMailInfoMap().containsKey(player.getLanguage())) {
                continue;
            }
            if (c_pubMail.getSendEvent() > 0) {
                continue;
            }
            //接收过的过滤
            if (inboxInfo.getReceivedPublicMails().contains(Long.parseLong(c_pubMail.getMailId() + ""))) {
                continue;
            }
            if (c_pubMail.getSendType() == 1) {//全部
                list.add(c_pubMail.getMailId());
                continue;
            }
            if (c_pubMail.getPlayerId().contains(player.getPlayerId())) {//指定玩家id
                list.add(c_pubMail.getMailId());
            }
        }
        return list;
    }

    private void addPlayerPubMail(Player player, List<Integer> pubMailList) {
        if (pubMailList.isEmpty()) {
            return;
        }

        final MerchantData merchantData = DataHallMrg.getInstance().findMerchantData(this.getClass().getSimpleName(), player.getBusiness_no());
        if (merchantData == null) {
            return;
        }

        final InboxInfo inboxInfo = player.getInboxInfo();
        final LongList longList = new LongArrayList();
        for (int mailId : pubMailList) {
            final C_PubMail c_pubMail = merchantData.findC_PubMail(this.getClass().getSimpleName(), mailId);
            if (c_pubMail == null) {
                continue;
            }
            final Inbox inbox = addInbox(player, c_pubMail);
            longList.add(inbox.getInboxId());
            inboxInfo.getReceivedPublicMails().add(Long.parseLong(inbox.getModelId() + ""));
        }
        EntityDaoMrg.getInstance().getDao(PlayerDao.class).inboxDao
                .updateInsertInbox(player.getPlayerId(), player.getInboxInfo(), longList);
        EntityDaoMrg.getInstance().getDao(PlayerDao.class).inboxDao
                .updateReceivedPublicMails(player.getPlayerId(), inboxInfo);
    }

    private Inbox addInbox(Player player, C_BaseInbox c_inbox) {
        final Inbox inbox = createInbox(player.getLanguage(), c_inbox);
        final InboxInfo inboxInfo = player.getInboxInfo();
        inboxInfo.getInboxMap().put(inbox.getInboxId(), inbox);
        return inbox;
    }

    private Inbox createInbox(int language, C_BaseInbox c_inbox) {
        final UniqueIDGenerator uniqueIDGenerator = HallServer.getInstance().getUniqueIDGenerator();
        final Inbox inbox = new Inbox(uniqueIDGenerator.nextId());
        final C_BaseInbox.InboxInfo inboxInfo = c_inbox.getInboxInfoMap().get(language);
        inbox.setLanguage(language);
        inbox.setTitle(inboxInfo.getTitle());
        inbox.setContext(inboxInfo.getContext());
        inbox.setInboxType(c_inbox.getInboxType());
        inbox.setExpireTime(TimeUtil.currentTimeMillis() + 15 * TimeUtil.DAY);
        return inbox;
    }
}
