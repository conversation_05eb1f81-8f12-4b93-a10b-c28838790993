package com.game.scripts.rank;

import com.game.c_entity.middleplatform.C_BaseExchangeRate;
import com.game.engine.io.redis.RedisPoolManager;
import com.game.engine.math.BigDecimalUtils;
import com.game.engine.utils.JsonUtils;
import com.game.engine.utils.TimeUtil;
import com.game.entity.WinGameNote;
import com.game.entity.player.Player;
import com.game.enums.redis.RedisRanking;
import com.game.hall.mrg.DataHallMrg;
import com.game.hall.mrg.MerchantData;
import com.game.hall.script.IRankScript;
import com.game.utils.VirtualThreadUtils;
import com.proto.InnerMessage;
import io.lettuce.core.api.async.RedisAsyncCommands;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.CompletableFuture;

public class RankWinScript implements IRankScript {
    private static final Logger LOGGER = LoggerFactory.getLogger(RankWinScript.class);

    @Override
    public void addBigWinRank(Player player, InnerMessage.NotifyData notifyData) {
        final String business_no = player.getBusiness_no();
        final int gameId = notifyData.getGameId();
        final int currencyId = notifyData.getCurrencyId();
        final double betAmount = notifyData.getBetAmount();
        final double validBet = notifyData.getValidBet();
        final double totalWin = notifyData.getTotalWin();

        final MerchantData merchantData = DataHallMrg.getInstance().findMerchantData(this.getClass().getSimpleName(), player.getBusiness_no());
        if (merchantData == null) {
            return;
        }

        final boolean isManyCurrency = merchantData.getC_currencyMap().size() > 1;

        if (betAmount == 0 || validBet == 0 || totalWin == 0) {
            return;
        }

        double winAmount;
        if (isManyCurrency) {
            final C_BaseExchangeRate c_baseExchangeRate = DataHallMrg.getInstance().findC_BaseExchangeRate(this.getClass().getSimpleName(), currencyId);
            if (c_baseExchangeRate == null) {
                return;
            }
            winAmount = BigDecimalUtils.mul(totalWin, c_baseExchangeRate.getExchangeRate(), 4);
        } else {
            winAmount = totalWin;
        }

        final WinGameNote winGameNote = createGameNote(currencyId, betAmount, validBet, gameId, player.getPlayerName(), totalWin);

        Long totalRank = RedisPoolManager.getInstance().function(jedis ->
                jedis.sync().zcard(RedisRanking.RANKING_BIG_WIN.getKey(business_no, gameId)));
        if (totalRank == null) {
            totalRank = 0L;
        }
        final long finalTotalRank = totalRank;
        RedisPoolManager.getInstance().asyncPipeline(commands -> {
                    final List<CompletableFuture<?>> futures = new ArrayList<>();
                    if (finalTotalRank > 3) {
                        futures.add(commands.zremrangebyrank(RedisRanking.RANKING_BIG_WIN.getKey(business_no, gameId), 0, finalTotalRank - 4)
                                .toCompletableFuture());
                    }
                    futures.add(commands.zadd(RedisRanking.RANKING_BIG_WIN.getKey(business_no, gameId), winAmount, player.getPlayerId() + "_" + JsonUtils.writeAsJson(winGameNote))
                            .toCompletableFuture());
                    return futures;
                }
        );
    }

    @Override
    public void addLuckyWinRank(Player player, InnerMessage.NotifyData notifyData) {
        final String business_no = player.getBusiness_no();
        final int gameId = notifyData.getGameId();
        final int currencyId = notifyData.getCurrencyId();
        final double betAmount = notifyData.getBetAmount();
        final double validBets = notifyData.getValidBet();
        final double totalWin = notifyData.getTotalWin();

        if (betAmount == 0 || validBets == 0 || totalWin == 0) {
            return;
        }

        final double payout = BigDecimalUtils.div(totalWin, betAmount, 4);

        final WinGameNote winGameNote = createGameNote(currencyId, betAmount, validBets, gameId, player.getPlayerName(), totalWin);

        Long totalRank = RedisPoolManager.getInstance().function(jedis ->
                jedis.sync().zcard(RedisRanking.RANKING_LUCKY_WIN.getKey(business_no, gameId)));
        if (totalRank == null) {
            totalRank = 0L;
        }
        final long finalTotalRank = totalRank;
        RedisPoolManager.getInstance().asyncPipeline(commands -> {
                    final List<CompletableFuture<?>> futures = new ArrayList<>();
                    if (finalTotalRank > 3) {
                        futures.add(commands.zremrangebyrank(RedisRanking.RANKING_LUCKY_WIN.getKey(business_no, gameId), 0, finalTotalRank - 4)
                                .toCompletableFuture());
                    }
                    futures.add(commands.zadd(RedisRanking.RANKING_LUCKY_WIN.getKey(business_no, gameId), payout, player.getPlayerId() + "_" + JsonUtils.writeAsJson(winGameNote))
                            .toCompletableFuture());
                    return futures;
                }
        );
    }


    private WinGameNote createGameNote(int currencyId, double betAmount, double validBets,
                                       int gameId, String playerName, double win) {
        final WinGameNote winGameNote = new WinGameNote();
        winGameNote.setCurrencyId(currencyId);
        winGameNote.setBetAmount(betAmount);
        winGameNote.setValidBets(validBets);
        winGameNote.setGameId(gameId);
        winGameNote.setPlayerName(playerName);
        winGameNote.setCreateTime(TimeUtil.currentTimeMillis());
        winGameNote.setWin(win);
        return winGameNote;
    }
}
