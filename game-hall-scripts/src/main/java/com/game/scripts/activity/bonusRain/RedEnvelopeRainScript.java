package com.game.scripts.activity.bonusRain;

import com.game.c_entity.merchant.C_Activity;
import com.game.c_entity.merchant.C_RedEnvelopeRain;
import com.game.c_entity.middleplatform.C_BaseMerchant;
import com.game.engine.io.redis.RedisPoolManager;
import com.game.engine.utils.Config;
import com.game.engine.utils.TimeUtil;
import com.game.engine.utils.TimeUtils;
import com.game.entity.player.Player;
import com.game.entity.player.activity.redEnvelopeRain.RedEnvelopeRainInfo;
import com.game.enums.redis.RedisAllGame;
import com.game.hall.mrg.DataHallMrg;
import com.game.hall.mrg.MerchantData;
import com.game.hall.script.activity.IActivityScript;
import com.game.utils.VirtualThreadUtils;
import io.lettuce.core.api.async.RedisAsyncCommands;
import io.netty.util.internal.StringUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

/**
 * 每天
 */
public class RedEnvelopeRainScript implements IActivityScript {

    private static final Logger LOGGER = LoggerFactory.getLogger(RedEnvelopeRainScript.class);

    private static long time = 0;

    @Override
    public void initData(Player player, C_Activity c_activity) {
        final MerchantData merchantData = DataHallMrg.getInstance().findMerchantData(this.getClass().getSimpleName(), player.getBusiness_no());
        if (merchantData == null) {
            return;
        }

        final C_RedEnvelopeRain c_redEnvelopeRain = findOpen(player, merchantData);
        if (c_redEnvelopeRain == null) {
            return;
        }

        final RedEnvelopeRainInfo redEnvelopeRainInfo = player.getRedEnvelopeRainInfo();
        if (redEnvelopeRainInfo.isStart()) {
            return;
        }

        redEnvelopeRainInfo.setStart(true);
    }

    @Override
    public void resetData(Player player, C_Activity c_activity) {
        final RedEnvelopeRainInfo redEnvelopeRainInfo = player.getRedEnvelopeRainInfo();
        if (!redEnvelopeRainInfo.isStart()) {
            return;
        }

        redEnvelopeRainInfo.setStart(false);
        redEnvelopeRainInfo.reset();

        initData(player, c_activity);
    }

    private C_RedEnvelopeRain findOpen(Player player, MerchantData merchantData) {
        final Map<Integer, C_RedEnvelopeRain> c_redEnvelopeRainWeekMap = merchantData.getC_redEnvelopeRainWeekMap();
        for (C_RedEnvelopeRain c_redEnvelopeRain : c_redEnvelopeRainWeekMap.values()) {
            if (player != null && !c_redEnvelopeRain.getLanguages().contains(player.getLanguage())) {
                continue;
            }

            final String timeZone = c_redEnvelopeRain.getTimeZone();
            final int month = TimeUtil.getDayOfMonth(timeZone);
            if (c_redEnvelopeRain.getMonthlys().contains(month)) {
                return c_redEnvelopeRain;
            }
        }

        final Map<Integer, C_RedEnvelopeRain> c_redEnvelopeRainMap = merchantData.getC_redEnvelopeRainDayMap();
        for (C_RedEnvelopeRain c_redEnvelopeRain : c_redEnvelopeRainMap.values()) {
            if (player != null && !c_redEnvelopeRain.getLanguages().contains(player.getLanguage())) {
                continue;
            }

            final String timeZone = c_redEnvelopeRain.getTimeZone();
            final int day = TimeUtil.getDayOfWeek(timeZone);
            if (c_redEnvelopeRain.getCycles().contains(day)) {
                return c_redEnvelopeRain;
            }
        }
        return null;
    }

    @Override
    public void settlement() {
        if (Config.SERVER_ID != 4000) {
            return;
        }

        if (time == 0) {
            final String times = RedisPoolManager.getInstance().function(jedis ->
                    jedis.sync().get(RedisAllGame.Platform_All_ServerTime.getKey(Config.SERVER_ID)));
            if (StringUtil.isNullOrEmpty(times)) {
                RedisPoolManager.getInstance().executeAsync(commands ->
                        commands.set(RedisAllGame.Platform_All_ServerTime.getKey(Config.SERVER_ID), TimeUtil.currentTimeMillis() + "")
                );
                time = TimeUtil.currentTimeMillis();
            } else {
                time = Long.parseLong(times);
            }
        }

        final List<String> business_noList = DataHallMrg.getInstance().getC_baseHostMerchantMap()
                .values().stream().map(C_BaseMerchant::getBusiness_no).distinct().toList();

        int count = 0;
        for (final String business_no : business_noList) {
            final MerchantData merchantData = DataHallMrg.getInstance().findMerchantData(this.getClass().getSimpleName(), business_no);
            if (merchantData == null) {
                continue;
            }

            final C_RedEnvelopeRain c_redEnvelopeRain = findOpen(null, merchantData);
            if (c_redEnvelopeRain == null) {
                continue;
            }

            long nextZeroTime = TimeUtil.getTimeBeginOfToday(time, c_redEnvelopeRain.getTimeZone()) + TimeUtils.DAY;
            if (nextZeroTime > TimeUtil.currentTimeMillis()) {
                continue;
            }

            count++;
            final String timeZone = c_redEnvelopeRain.getTimeZone();
            int day = 0;
            if (!c_redEnvelopeRain.getMonthlys().isEmpty()) {
                day = TimeUtil.getDayOfMonth(timeZone);
            }
            if (day == 0 && !c_redEnvelopeRain.getCycles().isEmpty()) {
                day = TimeUtil.getDayOfWeek(timeZone);
            }

            for (C_RedEnvelopeRain.TimePeriod timePeriod : c_redEnvelopeRain.getTimePeriodMap().values()) {
                for (C_RedEnvelopeRain.RedEnvelope redEnvelope : c_redEnvelopeRain.getRedEnvelopeMap().values()) {
                    final int finalDay = day;

//                    LOGGER.warn("business_no：{}，Platform_All_BonusRain，reset 。。。timePeriod：{}，redId：{}", business_no, finalDay + "" + timePeriod.id, redEnvelope.id);
//                    LOGGER.warn("business_no：{}，Platform_All_BonusRainAmount，reset 。。。timePeriod：{}", business_no, finalDay + "" + timePeriod.id);
                    RedisPoolManager.getInstance().asyncPipeline(commands -> {
                        final List<CompletableFuture<?>> futures = new ArrayList<>();
                        futures.add(commands.del(RedisAllGame.Platform_All_BonusRain.getKey(business_no, Integer.parseInt(finalDay + "" + timePeriod.id), redEnvelope.id))
                                .toCompletableFuture());
                        futures.add(commands.del(RedisAllGame.Platform_All_BonusRainAmount.getKey(business_no, Integer.parseInt(finalDay + "" + timePeriod.id), 0))
                                .toCompletableFuture());
                        return futures;
                    });
                }
            }
        }

        if (count > 0) {
            time = TimeUtil.currentTimeMillis();
            RedisPoolManager.getInstance().executeAsync(commands ->
                    commands.set(RedisAllGame.Platform_All_ServerTime.getKey(Config.SERVER_ID), TimeUtil.currentTimeMillis() + ""));
        }
    }

}
