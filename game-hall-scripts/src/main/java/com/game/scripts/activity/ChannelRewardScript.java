package com.game.scripts.activity;

import com.game.c_entity.merchant.C_Pwa;
import com.game.dao.player.PlayerDao;
import com.game.engine.log.GameLog;
import com.game.engine.math.BigDecimalUtils;
import com.game.engine.script.ScriptLoader;
import com.game.engine.util.misc.Tuple2;
import com.game.engine.utils.GuidGeneratorUtils;
import com.game.engine.utils.TimeUtil;
import com.game.entity.player.Player;
import com.game.entity.player.PlayerFields;
import com.game.entity.player.stats.Stats;
import com.game.enums.RewardReason;
import com.game.enums.TransactionFrom;
import com.game.enums.TurnoverReason;
import com.game.hall.main.HallServer;
import com.game.hall.mrg.DataHallMrg;
import com.game.hall.mrg.MerchantData;
import com.game.hall.mrg.currency.CurrencyMrg;
import com.game.hall.mrg.currency.RewardRequest;
import com.game.hall.script.IBonusScript;
import com.game.hall.script.IChannelRewardScript;
import com.game.hall.script.IPlayerScript;
import com.game.manager.EntityDaoMrg;

import java.util.Optional;

public class ChannelRewardScript implements IChannelRewardScript {

    @Override
    public void receivePwaReward(Player player, int currencyId) {
        if (player.isReceivePwa()) {
            return;
        }

        if (!player.getChannels().contains(1)) {
            return;
        }

        final Stats stats = player.getStats(currencyId);
        if (stats.getTotalRechargeTimes() == 0) {
            return;
        }

        final MerchantData merchantData = DataHallMrg.getInstance().findMerchantData(this.getClass().getSimpleName(), player.getBusiness_no());
        if (merchantData == null) {
            return;
        }

        final Optional<C_Pwa> optional = merchantData.getC_pwaMap().values().stream().findFirst();
        if (optional.isEmpty()) {
            return;
        }

        final C_Pwa c_pwa = optional.get();

        final RewardRequest rewardRequest = new RewardRequest();
        rewardRequest.addCurrency(c_pwa.getCurrencyId(), c_pwa.getReward());
        final RewardReason rewardReason = RewardReason.Pwa;
        CurrencyMrg.getInstance().rewardNoTest(player, rewardRequest.getBonusCurrencyMap(), rewardReason);

        //TODO 打码
//        final WithdrawStandard withdrawStandard = player.getWithdrawStandard(c_pwa.getCurrencyId());
//        withdrawStandard.incDrawStandard(BigDecimalUtils.mul(c_pwa.getReward(), c_pwa.getTurnoverMul(), 4));
        final TurnoverReason turnoverReason = TurnoverReason.Pwa;
        ScriptLoader.getInstance().consumerScript("PlayerWithdrawStandardScript",
                (IPlayerScript script) -> script.drawStandard(player, turnoverReason, c_pwa.getCurrencyId(), c_pwa.getReward(),
                        BigDecimalUtils.mul(c_pwa.getReward(), c_pwa.getTurnoverMul(), 4)));

        if (!rewardRequest.getCurrencyMap().isEmpty()) {
            final Tuple2<Integer, Double> tuple2 = rewardRequest.getReward();
            ScriptLoader.getInstance().consumerScript("BonusScript",
                    (IBonusScript script) -> script.addBonusNote(TransactionFrom.Pwa, player, tuple2.getFirst(), tuple2.getSecond()));
        }

        player.setReceivePwa(true);
        EntityDaoMrg.getInstance().getDao(PlayerDao.class)
                .updatePlayerField(player.getPlayerId(), PlayerFields.receivePwa, player.isReceivePwa());

        //TODO 日志
        final GameLog playerPwaRewardLog = new GameLog("platform_playerPwaRewardLog");
        playerPwaRewardLog.append("number", GuidGeneratorUtils.generateOrderId())
                .append("business_no", player.getBusiness_no())
                .append("site", player.getWebSite())
                .append("channel", player.getChannel())
                .append("playerId", player.getPlayerId())
                .append("playerName", player.getPlayerName())
                .append("channelId", player.getChannelId())
                .append("agentId", player.getAgentId())
                .append("mediaId", player.getMediaId())
                .append("adId", player.getAdId())
                .append("currencyId", c_pwa.getCurrencyId())
                .append("reward", c_pwa.getReward())
                .append("logTime", TimeUtil.currentTimeMillis());
        HallServer.getInstance().getLogProducerMrg().send(playerPwaRewardLog);
    }
}
