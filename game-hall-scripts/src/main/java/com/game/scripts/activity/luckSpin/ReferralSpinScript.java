package com.game.scripts.activity.luckSpin;

import com.game.c_entity.merchant.C_LuckSpin;
import com.game.c_entity.middleplatform.C_BaseExchangeRate;
import com.game.dao.player.PlayerDao;
import com.game.engine.io.redis.RedisPoolManager;
import com.game.engine.log.GameLog;
import com.game.engine.math.BigDecimalUtils;
import com.game.engine.script.ScriptLoader;
import com.game.engine.utils.GuidGeneratorUtils;
import com.game.engine.utils.TimeUtil;
import com.game.entity.player.Player;
import com.game.entity.player.activity.luckSpin.LuckCondition;
import com.game.entity.player.activity.luckSpin.LuckSpinData;
import com.game.entity.player.activity.luckSpin.LuckSpinInfo;
import com.game.entity.player.promote.PlayerPromote;
import com.game.enums.redis.RedisHall;
import com.game.hall.main.HallServer;
import com.game.hall.mrg.ActivityMrg;
import com.game.hall.mrg.DataHallMrg;
import com.game.hall.mrg.MerchantData;
import com.game.hall.mrg.player.PlayerMrg;
import com.game.hall.script.activity.IActivityScript;
import com.game.hall.script.activity.ILuckSpinScript;
import com.game.manager.EntityDaoMrg;
import com.game.utils.VirtualThreadUtils;
import io.lettuce.core.api.async.RedisAsyncCommands;
import io.netty.util.internal.StringUtil;
import it.unimi.dsi.fastutil.ints.IntLists;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.net.InetAddress;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;

/**
 * 整个流程只有一次
 */
public class ReferralSpinScript implements ILuckSpinScript {
    private static final Logger LOGGER = LoggerFactory.getLogger(ReferralSpinScript.class);

    @Override
    public void initData(Player player, C_LuckSpin c_luckSpin) {
        if (TimeUtil.currentTimeMillis() < c_luckSpin.getStartTime() || TimeUtil.currentTimeMillis() >= c_luckSpin.getEndTime()) {
            return;
        }

        final LuckSpinInfo luckSpinInfo = player.getLuckSpinInfo();
        LuckSpinData luckSpinData = luckSpinInfo.getLuckSpinData(c_luckSpin.getActivityId());
        if (luckSpinData == null) {
            luckSpinData = new LuckSpinData();
            luckSpinInfo.getLuckSpinDataMap().put(c_luckSpin.getActivityId(), luckSpinData);
        }

        if (luckSpinData.isStart() && c_luckSpin.getC_id() == luckSpinData.getUniqueId()) {
            return;
        }

        if (luckSpinData.isFinish()) {
            return;
        }

        luckSpinData.setStart(true);
        luckSpinData.setUniqueId(c_luckSpin.getC_id());
//        luckSpinData.setRemainTimes(c_luckSpin.getInitTurntableTimes());
        RedisPoolManager.getInstance().executeAsync(commands ->
                commands.incrby(RedisHall.Platform_Role_Map_ReferralLuckSpin_RemainTimes.getKey(player.getPlayerId()), c_luckSpin.getInitTurntableTimes())
        );

        if (c_luckSpin.getExpiredTime() > 0) {
            luckSpinData.setExpiredTime(TimeUtil.currentTimeMillis() + c_luckSpin.getExpiredTime());
        } else {
            luckSpinData.setExpiredTime(-1);
        }
    }

    @Override
    public void resetData(Player player, C_LuckSpin c_luckSpin) {
        final LuckSpinInfo luckSpinInfo = player.getLuckSpinInfo();
        final LuckSpinData luckSpinData = luckSpinInfo.getLuckSpinData(c_luckSpin.getActivityId());
        if (luckSpinData == null || !luckSpinData.isStart()) {
            return;
        }

        if (c_luckSpin.getExpiredTime() == -1) {//不过期
            if (TimeUtil.currentTimeMillis() < c_luckSpin.getEndTime()) {
                return;
            }
        } else {
            if (TimeUtil.currentTimeMillis() < luckSpinData.getExpiredTime()) {
                return;
            }
        }

        if (luckSpinData.isFinish() && luckSpinData.getUniqueId() == c_luckSpin.getC_id()) {
            return;
        }

        luckSpinData.reset();

        RedisPoolManager.getInstance().asyncPipeline(commands -> {
            List<CompletableFuture<?>> futures = new ArrayList<>();
            futures.add(commands.del(RedisHall.Platform_Role_Map_ReferralLuckSpin_Invite.getKey(player.getPlayerId())).toCompletableFuture());
            futures.add(commands.del(RedisHall.Platform_Role_Map_ReferralLuckSpin_TotalTimes.getKey(player.getPlayerId())).toCompletableFuture());
            futures.add(commands.del(RedisHall.Platform_Role_Map_ReferralLuckSpin_RemainTimes.getKey(player.getPlayerId())).toCompletableFuture());
            futures.add(commands.del(RedisHall.Platform_Role_Map_ReferralLuckSpin_Ip.getKey(player.getPlayerId())).toCompletableFuture());
            futures.add(commands.del(RedisHall.Platform_Role_Map_ReferralLuckSpin_device.getKey(player.getPlayerId())).toCompletableFuture());
            return futures;
        });

        initData(player, c_luckSpin);
    }

    @Override
    public void luckSpinExecute(Player player, C_LuckSpin c_luckSpin, int currencyId, double wageredAmount, double rechargeAmount) {
        final MerchantData merchantData = DataHallMrg.getInstance().findMerchantData(this.getClass().getSimpleName(), player.getBusiness_no());
        if (merchantData == null) {
            return;
        }

        initData(player, c_luckSpin);

        final LuckSpinInfo luckSpinInfo = player.getLuckSpinInfo();
        final LuckSpinData luckSpinData = luckSpinInfo.getLuckSpinData(c_luckSpin.getActivityId());
        if (luckSpinData == null || !luckSpinData.isStart()) {
            return;
        }

        final boolean isManyCurrency = merchantData.getC_currencyMap().size() > 1;

        final PlayerPromote playerPromote = player.getPlayerPromote();
        if (playerPromote == null) {
            return;
        }

        if (playerPromote.getSuperiorId() == 0) {
            return;
        }

        final LuckCondition luckCondition = luckSpinData.getLuckCondition();
        if (wageredAmount > 0) {
            luckCondition.incBetTimes();
            luckCondition.incWagered(currencyId, wageredAmount);
        }

        if (rechargeAmount > 0) {
            luckCondition.incRecharge(currencyId, rechargeAmount);
        }

        final C_LuckSpin.ExtraCondition condition = c_luckSpin.getExtraCondition();
        if (luckCondition.getBetTimes() < condition.getBetTimes()) {
            return;
        }

        double totalRecharge = 0;
        for (Map.Entry<Integer, Double> entry : luckCondition.getRechargeAmountMap().entrySet()) {
            if (isManyCurrency) {
                final C_BaseExchangeRate c_baseExchangeRate = DataHallMrg.getInstance().findC_BaseExchangeRate(this.getClass().getSimpleName(), player.getCurrencyId());
                if (c_baseExchangeRate == null) {
                    continue;
                }
                totalRecharge = BigDecimalUtils.add(totalRecharge, BigDecimalUtils.mul(entry.getValue(), c_baseExchangeRate.getExchangeRate(), 9), 9);
            } else {
                totalRecharge = BigDecimalUtils.add(totalRecharge, entry.getValue(), 9);
            }
        }

        if (totalRecharge < condition.getRechargeUsdAmount()) {
            return;
        }

        double totalWagered = 0;
        for (Map.Entry<Integer, Double> entry : luckCondition.getWageredMap().entrySet()) {
            if (isManyCurrency) {
                final C_BaseExchangeRate c_baseExchangeRate = DataHallMrg.getInstance().findC_BaseExchangeRate(this.getClass().getSimpleName(), player.getCurrencyId());
                if (c_baseExchangeRate == null) {
                    continue;
                }
                totalWagered = BigDecimalUtils.add(totalWagered, BigDecimalUtils.mul(entry.getValue(), c_baseExchangeRate.getExchangeRate(), 9), 9);
            } else {
                totalWagered = BigDecimalUtils.add(totalWagered, entry.getValue(), 9);
            }
        }

        if (totalWagered < condition.getBetUsdAmount()) {
            return;
        }

        final Player superiorPlayer = PlayerMrg.getInstance().findDbPlayer(playerPromote.getSuperiorId());
        if (superiorPlayer == null) {
            return;
        }

        final boolean isSame = ScriptLoader.getInstance().functionScript("ActivityScript",
                (IActivityScript script) -> script.isSameNetworkOrDevice(player, superiorPlayer, ActivityMrg.LUCK_SPIN_REFERRAL));
        if (isSame) {
            return;
        }

//        final LuckSpinInfo superiorLuckSpinInfo = superiorPlayer.getLuckSpinInfo();
//        LuckSpinData superiorLuckSpinData = superiorLuckSpinInfo.getLuckSpinData(c_luckSpin.getActivityId());
//        if (superiorLuckSpinData == null) {
//            superiorLuckSpinData = new LuckSpinData();
//            superiorLuckSpinInfo.getLuckSpinDataMap().put(c_luckSpin.getActivityId(), superiorLuckSpinData);
//        }

        final boolean isExist = RedisPoolManager.getInstance().function(jedis ->
                jedis.sync().sismember(RedisHall.Platform_Role_Map_ReferralLuckSpin_Invite.getKey(superiorPlayer.getPlayerId()), player.getPlayerId() + ""));
        if (isExist) {
            return;
        }

//        final LuckCondition superiorLuckCondition = superiorLuckSpinData.getLuckCondition();
//        if (superiorLuckCondition.getInvitePlayerId().contains(player.getPlayerId())) {
//            return;
//        }

        RedisPoolManager.getInstance().asyncPipeline(commands -> {
            final List<CompletableFuture<?>> futures = new ArrayList<>();
            futures.add(commands.sadd(RedisHall.Platform_Role_Map_ReferralLuckSpin_Invite.getKey(superiorPlayer.getPlayerId()), player.getPlayerId() + "")
                    .toCompletableFuture());
            futures.add(commands.sadd(RedisHall.Platform_Role_Map_ReferralLuckSpin_Ip.getKey(superiorPlayer.getPlayerId()), player.getIp())
                    .toCompletableFuture());
            futures.add(commands.sadd(RedisHall.Platform_Role_Map_ReferralLuckSpin_device.getKey(superiorPlayer.getPlayerId()), player.getDevice() + "-" + player.getModel())
                    .toCompletableFuture());
            return futures;
        });

        //        superiorLuckCondition.getInvitePlayerId().add(player.getPlayerId());
//        EntityDaoMrg.getInstance().getDao(PlayerDao.class)
//                .luckSpinDao.updateLuckSpin(superiorPlayer.getPlayerId(), superiorLuckSpinInfo, IntLists.singleton(c_luckSpin.getActivityId()));

        final long inviteNum = RedisPoolManager.getInstance().function(jedis ->
                jedis.sync().scard(RedisHall.Platform_Role_Map_ReferralLuckSpin_Invite.getKey(superiorPlayer.getPlayerId())));
        if (inviteNum < condition.getInviteNum()) {
            return;
        }

        final String totalTimes = RedisPoolManager.getInstance().function(jedis ->
                jedis.sync().get(RedisHall.Platform_Role_Map_ReferralLuckSpin_TotalTimes.getKey(superiorPlayer.getPlayerId())));

        if (Integer.parseInt(StringUtil.isNullOrEmpty(totalTimes) ? "0" : totalTimes) >= condition.getMaxTimes()) {
            return;
        }

        RedisPoolManager.getInstance().asyncPipeline(commands -> {
            final List<CompletableFuture<?>> futures = new ArrayList<>();
            futures.add(commands.incrby(RedisHall.Platform_Role_Map_ReferralLuckSpin_TotalTimes.getKey(superiorPlayer.getPlayerId()), condition.getAddTimes())
                    .toCompletableFuture());
            futures.add(commands.incrby(RedisHall.Platform_Role_Map_ReferralLuckSpin_RemainTimes.getKey(superiorPlayer.getPlayerId()), condition.getAddTimes())
                    .toCompletableFuture());
            return futures;
        });

//        superiorLuckCondition.incTotalTimes(condition.getAddTimes());
//        superiorLuckSpinData.incRemainTimes(condition.getAddTimes());
//        EntityDaoMrg.getInstance().getDao(PlayerDao.class)
//                .luckSpinDao.updateLuckSpin(superiorPlayer.getPlayerId(), superiorLuckSpinInfo, IntLists.singleton(c_luckSpin.getActivityId()));

        //日志
        final GameLog playerActivityLog = new GameLog("platform_playerEfficientLuckSpinLog");
        playerActivityLog.append("number", GuidGeneratorUtils.generateOrderId())
                .append("business_no", player.getBusiness_no())
                .append("site", player.getWebSite())
                .append("channel", player.getChannel())
                .append("playerId", player.getPlayerId())
                .append("playerName", player.getPlayerName())
                .append("language", player.getLanguage())
                .append("agentId", player.getAgentId())
                .append("channelId", player.getChannelId())
                .append("region", player.getRegisterRegion())
                .append("activityId", c_luckSpin.getActivityId())
                .append("activityUniqueId", c_luckSpin.getC_id())
                .append("betTime", condition.getBetTimes())
                .append("registerTime", player.getCreateTime())
                .append("superiorId", superiorPlayer.getPlayerId())
                .append("currencyId", isManyCurrency ? c_luckSpin.getInviteInfo().getCurrencyId() : player.getCurrencyId())
                .append("wageredAmount", totalWagered)
                .append("rechargeAmount", totalRecharge)
                .append("logTime", TimeUtil.currentTimeMillis());
        HallServer.getInstance().getLogProducerMrg().send(playerActivityLog);
    }

    @Override
    public void referralIntervalAddTimes(Player player) {
        final MerchantData merchantData = DataHallMrg.getInstance().findMerchantData(PlayerMrg.class.getSimpleName(), player.getBusiness_no());
        if (merchantData == null) {
            return;
        }

        final List<C_LuckSpin> c_luckSpinList = merchantData.getC_luckSpinMap().get(ActivityMrg.LUCK_SPIN_REFERRAL);
        if (c_luckSpinList == null || c_luckSpinList.isEmpty()) {
            return;
        }

        final C_LuckSpin c_luckSpin = c_luckSpinList.getFirst();
        if (TimeUtil.currentTimeMillis() < c_luckSpin.getStartTime() || TimeUtil.currentTimeMillis() >= c_luckSpin.getEndTime()) {
            return;
        }

        final LuckSpinInfo luckSpinInfo = player.getLuckSpinInfo();
        final LuckSpinData luckSpinData = luckSpinInfo.getLuckSpinData(c_luckSpin.getActivityId());
        if (luckSpinData == null || !luckSpinData.isStart()) {
            return;
        }

        player.setIntervalTimes(player.getIntervalTimes() + 1);
        //间隔
        final C_LuckSpin.InviteData inviteData = c_luckSpin.getInviteInfo();
        if (inviteData.getInterval() == 0) {
            return;
        }

        if (player.getIntervalTimes() % inviteData.getInterval() != 0) {
            return;
        }

        if (luckSpinData.getTotalTimes() >= inviteData.getMaxTimes()) {
            return;
        }

        luckSpinData.incTotalTimes(inviteData.getAddTimes());
        luckSpinData.incRemainTimes(inviteData.getAddTimes());
        EntityDaoMrg.getInstance().getDao(PlayerDao.class)
                .luckSpinDao.updateLuckSpin(player.getPlayerId(), luckSpinInfo, IntLists.singleton(c_luckSpin.getActivityId()));
    }

}
