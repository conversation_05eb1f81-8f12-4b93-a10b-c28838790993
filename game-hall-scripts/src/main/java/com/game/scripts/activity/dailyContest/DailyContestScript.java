package com.game.scripts.activity.dailyContest;

import com.game.c_entity.merchant.C_DailyContest;
import com.game.c_entity.middleplatform.C_BaseExchangeRate;
import com.game.c_entity.middleplatform.C_BaseMerchant;
import com.game.dao.player.PlayerDao;
import com.game.engine.io.redis.RedisPoolManager;
import com.game.engine.log.GameLog;
import com.game.engine.math.BigDecimalUtils;
import com.game.engine.script.ScriptLoader;
import com.game.engine.util.misc.Tuple2;
import com.game.engine.utils.Config;
import com.game.engine.utils.GuidGeneratorUtils;
import com.game.engine.utils.TimeUtil;
import com.game.entity.player.Player;
import com.game.entity.player.activity.dailyContest.DailyContestInfo;
import com.game.enums.FunctionEnabled;
import com.game.enums.RewardReason;
import com.game.enums.TransactionFrom;
import com.game.enums.TurnoverReason;
import com.game.enums.redis.RedisAllGame;
import com.game.enums.redis.RedisRanking;
import com.game.hall.main.HallServer;
import com.game.hall.mrg.ActivityMrg;
import com.game.hall.mrg.DataHallMrg;
import com.game.hall.mrg.MerchantData;
import com.game.hall.mrg.currency.CurrencyMrg;
import com.game.hall.mrg.currency.RewardRequest;
import com.game.hall.script.IBonusScript;
import com.game.hall.script.IFunctionEnabledScript;
import com.game.hall.script.IPlayerScript;
import com.game.hall.script.activity.IDailyContestScript;
import com.game.manager.EntityDaoMrg;
import com.game.utils.VirtualThreadUtils;
import io.lettuce.core.ScoredValue;
import io.netty.util.internal.StringUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;

public class DailyContestScript implements IDailyContestScript {
    private static final Logger LOGGER = LoggerFactory.getLogger(DailyContestScript.class);

    @Override
    public void initData(Player player) {
        final boolean functionEnabled = ScriptLoader.getInstance().functionScript("FunctionEnabledScript", (IFunctionEnabledScript script)
                -> script.functionEnabled(player.getBusiness_no(), FunctionEnabled.DailyContest.getType()));
        if (!functionEnabled) {
            return;
        }

        final DailyContestInfo dailyContestInfo = player.getDailyContestInfo();
        final String gameId = dailyContestInfo.getGameId();
        if (!StringUtil.isNullOrEmpty(gameId)) {
            return;
        }
        final String newGameId = TimeUtil.getDateTimeFormat(TimeUtil.getTimeEndOfToday(), TimeUtil.YYYYMMDD);
        dailyContestInfo.setGameId(newGameId);
        EntityDaoMrg.getInstance().getDao(PlayerDao.class)
                .updateDailyContestInfo(player.getPlayerId(), dailyContestInfo);
    }

    @Override
    public void resetData(Player player) {
        final boolean functionEnabled = ScriptLoader.getInstance().functionScript("FunctionEnabledScript", (IFunctionEnabledScript script)
                -> script.functionEnabled(player.getBusiness_no(), FunctionEnabled.DailyContest.getType()));
        if (!functionEnabled) {
            return;
        }

        final DailyContestInfo dailyContestInfo = player.getDailyContestInfo();
        final String gameId = dailyContestInfo.getGameId();
        final String newGameId = TimeUtil.getDateTimeFormat(TimeUtil.getTimeEndOfToday(), TimeUtil.YYYYMMDD);

        if (Objects.equals(gameId, newGameId)) {
            return;
        }

        final MerchantData merchantData = DataHallMrg.getInstance().findMerchantData(this.getClass().getSimpleName(), player.getBusiness_no());
        if (merchantData == null) {
            return;
        }

        final boolean isManyCurrency = merchantData.getC_currencyMap().size() > 1;

        final C_DailyContest c_dailyContest = merchantData.findC_DailyContest(this.getClass().getSimpleName(), ActivityMrg.DAILY_CONTEST);
        if (c_dailyContest == null) {
            return;
        }

        final String currentGameId = TimeUtil.getDateTimeFormat(TimeUtil.currentTimeMillis() - TimeUtil.DAY, TimeUtil.YYYYMMDD);
        final String todayPrizePools = RedisPoolManager.getInstance().function(jedis ->
                jedis.sync().get(RedisAllGame.Platform_All_DailyContest_PrizePool.getKey(player.getBusiness_no(), currentGameId)));
        final double todayPrizePool = BigDecimalUtils.div(Double.parseDouble(StringUtil.isNullOrEmpty(todayPrizePools) ? "0" : todayPrizePools), ActivityMrg.MULTIPLE, 4);
        if (todayPrizePool == 0) {
            return;
        }

        Long ranking = RedisPoolManager.getInstance().function(jedis ->
                jedis.sync().zrevrank(RedisRanking.RANKING_DAILY_CONTEST.getKey(player.getBusiness_no(), currentGameId), player.getPlayerId() + ""));
        if (ranking == null) {
            return;
        }

        final double prizeRate = c_dailyContest.getPrizeRate(Math.toIntExact(++ranking));
        final double prize = BigDecimalUtils.mul(todayPrizePool, prizeRate, 4);

        double reward;
        if (isManyCurrency) {
            final C_BaseExchangeRate c_baseExchangeRate = DataHallMrg.getInstance().findC_BaseExchangeRate(this.getClass().getSimpleName(), c_dailyContest.getRewardCurrency());
            if (c_baseExchangeRate == null) {
                return;
            }
            double exchangeRate = BigDecimalUtils.div(1, c_baseExchangeRate.getExchangeRate(), 4);
            reward = BigDecimalUtils.mul(prize, exchangeRate, 4);
        } else {
            reward = prize;
        }

        final RewardRequest rewardRequest = new RewardRequest();
        rewardRequest.addCurrency(c_dailyContest.getRewardCurrency(), reward);
        final RewardReason rewardReason = RewardReason.DailyContest;
        rewardReason.setSource(ranking + "");
        CurrencyMrg.getInstance().rewardNoTest(player, rewardRequest.getBonusCurrencyMap(), rewardReason);

        //TODO 打码
//        final WithdrawStandard withdrawStandard = player.getWithdrawStandard(c_dailyContest.getRewardCurrency());
//        withdrawStandard.incDrawStandard(BigDecimalUtils.mul(reward, c_dailyContest.getRewardTurnoverMul(), 4));
        final TurnoverReason turnoverReason = TurnoverReason.DailyContest;
        turnoverReason.setSource(ranking + "");
        ScriptLoader.getInstance().consumerScript("PlayerWithdrawStandardScript",
                (IPlayerScript script) -> script.drawStandard(player, turnoverReason, c_dailyContest.getRewardCurrency(), reward,
                        BigDecimalUtils.mul(reward, c_dailyContest.getRewardTurnoverMul(), 4)));

        dailyContestInfo.reset();

        if (!rewardRequest.getCurrencyMap().isEmpty()) {
            final Tuple2<Integer, Double> tuple2 = rewardRequest.getReward();
            ScriptLoader.getInstance().consumerScript("BonusScript",
                    (IBonusScript script) -> script.addBonusNote(TransactionFrom.DailyContest, player, tuple2.getFirst(), tuple2.getSecond()));
        }
    }

    @Override
    public void dailyContestExecute(Player player, int gameType, int currencyId, double validBet) {
        final boolean functionEnabled = ScriptLoader.getInstance().functionScript("FunctionEnabledScript", (IFunctionEnabledScript script)
                -> script.functionEnabled(player.getBusiness_no(), FunctionEnabled.DailyContest.getType()));
        if (!functionEnabled) {
            return;
        }

        final MerchantData merchantData = DataHallMrg.getInstance().findMerchantData(this.getClass().getSimpleName(), player.getBusiness_no());
        if (merchantData == null) {
            return;
        }

        final boolean isManyCurrency = merchantData.getC_currencyMap().size() > 1;

        final C_DailyContest c_dailyContest = merchantData.findC_DailyContest(this.getClass().getSimpleName(), ActivityMrg.DAILY_CONTEST);
        if (c_dailyContest == null) {
            return;
        }

        if (c_dailyContest.getJoinCurrency() != null && !c_dailyContest.getJoinCurrency().contains(currencyId)) {
            return;
        }

        final String prizePools = RedisPoolManager.getInstance().function(jedis ->
                jedis.sync().get(RedisAllGame.Platform_All_DailyContest_PrizePool.getKey(player.getBusiness_no(), TimeUtil.getDateTimeFormat(TimeUtil.YYYYMMDD))));
        final double prizePool = BigDecimalUtils.div(Double.parseDouble(StringUtil.isNullOrEmpty(prizePools) ? "0" : prizePools), ActivityMrg.MULTIPLE, 4);
        if (prizePool >= c_dailyContest.getMaxRewardPool()) {
            return;
        }

        final C_DailyContest.GameRewardPool gameRewardPool = c_dailyContest.getGameRewardPoolMap().get(gameType);
        if (gameRewardPool == null) {
            return;
        }

        long entryAmount = 0;
        double usdWager = validBet;
        if (isManyCurrency) {
            //进入奖池金额=有效投注 X 每日竞赛投注进入奖池比例 X 游戏奖池比例
            final C_BaseExchangeRate c_baseExchangeRate = DataHallMrg.getInstance().findC_BaseExchangeRate(this.getClass().getSimpleName(), currencyId);
            if (c_baseExchangeRate == null) {
                return;
            }
            final double value = BigDecimalUtils.mul(c_dailyContest.getEntryRewardPoolRate(), gameRewardPool.getRate(), 4);
            entryAmount = (long) (BigDecimalUtils.mul(validBet, BigDecimalUtils.mul(value, c_baseExchangeRate.getExchangeRate(), 4), 4) * ActivityMrg.MULTIPLE);
            //更玩家的下注值
            usdWager = BigDecimalUtils.mul(validBet, c_baseExchangeRate.getExchangeRate(), 4);
        } else {
            entryAmount = (long) (BigDecimalUtils.mul(validBet, BigDecimalUtils.mul(c_dailyContest.getEntryRewardPoolRate(), gameRewardPool.getRate(), 4), 4) * ActivityMrg.MULTIPLE);
        }

        if (entryAmount < c_dailyContest.getMinBet()) {
            return;
        }
        if (entryAmount >= c_dailyContest.getMaxBet()) {
            entryAmount = (long) c_dailyContest.getMaxBet();
        }

        final long finalEntryAmount = entryAmount;
        final DailyContestInfo dailyContestInfo = player.getDailyContestInfo();
        dailyContestInfo.incUsdWagered(usdWager);

        RedisPoolManager.getInstance().asyncPipeline(commands -> {
            final List<CompletableFuture<?>> futures = new ArrayList<>();
            futures.add(commands.incrby(RedisAllGame.Platform_All_DailyContest_PrizePool.getKey(player.getBusiness_no(), TimeUtil.getDateTimeFormat(TimeUtil.YYYYMMDD)), finalEntryAmount)
                    .toCompletableFuture());
            futures.add(commands.zadd(RedisRanking.RANKING_DAILY_CONTEST.getKey(player.getBusiness_no(), TimeUtil.getDateTimeFormat(TimeUtil.YYYYMMDD)), dailyContestInfo.getUsdWagered(), player.getPlayerId() + "")
                    .toCompletableFuture());
            return futures;
        });
    }

    @Override
    public void settlement() {
        //指定一个服务器结算
        if (Config.SERVER_ID != 4000) {
            return;
        }

        LOGGER.warn("DailyContestScript，settlement 。。。");
        final List<String> business_noList = DataHallMrg.getInstance().getC_baseHostMerchantMap()
                .values().stream().map(C_BaseMerchant::getBusiness_no).distinct().toList();

        for (String business_no : business_noList) {
            final boolean functionEnabled = ScriptLoader.getInstance().functionScript("FunctionEnabledScript", (IFunctionEnabledScript script)
                    -> script.functionEnabled(business_no, FunctionEnabled.DailyContest.getType()));
            if (!functionEnabled) {
                continue;
            }

            final MerchantData merchantData = DataHallMrg.getInstance().findMerchantData(this.getClass().getSimpleName(), business_no);
            if (merchantData == null) {
                continue;
            }

            final boolean isManyCurrency = merchantData.getC_currencyMap().size() > 1;

            final C_DailyContest c_dailyContest = merchantData.findC_DailyContest(this.getClass().getSimpleName(), ActivityMrg.DAILY_CONTEST);
            if (c_dailyContest == null) {
                continue;
            }

            final String finalBusiness_no = business_no;
            final String key = TimeUtil.getDateTimeFormat(TimeUtil.currentTimeMillis() - TimeUtil.HOUR, TimeUtil.YYYYMMDD);
            final String todayPrizePools = RedisPoolManager.getInstance().function(jedis ->
                    jedis.sync().get(RedisAllGame.Platform_All_DailyContest_PrizePool.getKey(finalBusiness_no, key)));
            final double todayPrizePool = BigDecimalUtils.div(Double.parseDouble(StringUtil.isNullOrEmpty(todayPrizePools) ? "0" : todayPrizePools), ActivityMrg.MULTIPLE, 4);
            if (todayPrizePool == 0) {
                continue;
            }

            final int topNum = c_dailyContest.getRewardAllocationList().size();
            final List<ScoredValue<String>> players = RedisPoolManager.getInstance().function(jedis ->
                    jedis.sync().zrevrangeWithScores(RedisRanking.RANKING_DAILY_CONTEST.getKey(finalBusiness_no, key), 0, topNum - 1));
            int ranking = 0;
            if (players != null) {
                for (ScoredValue<String> tuple : players) {
                    ranking++;
                    final String element = tuple.getValue();
                    final double score = tuple.getScore();

                    final double prizeRate = c_dailyContest.getPrizeRate(ranking);
                    final double prize = BigDecimalUtils.mul(todayPrizePool, prizeRate, 4);

                    double reward = prize;
                    if (isManyCurrency) {
                        final C_BaseExchangeRate c_baseExchangeRate = DataHallMrg.getInstance().findC_BaseExchangeRate(this.getClass().getSimpleName(), c_dailyContest.getRewardCurrency());
                        if (c_baseExchangeRate == null) {
                            return;
                        }
                        final double exchangeRate = BigDecimalUtils.div(1, c_baseExchangeRate.getExchangeRate(), 4);
                        reward = BigDecimalUtils.mul(prize, exchangeRate, 4);
                    }

                    //TODO 中奖日志
                    final GameLog playerDailyContestLog = new GameLog("platform_playerWinDailyContestLog");
                    playerDailyContestLog.append("number", GuidGeneratorUtils.generateOrderId())
                            .append("business_no", business_no)
                            .append("date", key)
                            .append("playerId", Long.parseLong(element))
                            .append("ranking", ranking)
                            .append("wagered", score)
                            .append("currencyId", c_dailyContest.getRewardCurrency())
                            .append("prize", reward)
                            .append("logTime", TimeUtil.currentTimeMillis());
                    HallServer.getInstance().getLogProducerMrg().send(playerDailyContestLog);
                }
            }
            final long totalRank = RedisPoolManager.getInstance().function(jedis ->
                    jedis.sync().zcard(RedisRanking.RANKING_DAILY_CONTEST.getKey(finalBusiness_no, key)));

            if (totalRank > topNum) {
                RedisPoolManager.getInstance().executeAsync(commands ->
                        commands.zremrangebyrank(RedisRanking.RANKING_DAILY_CONTEST.getKey(finalBusiness_no, key), 0, totalRank - 1 - topNum)
                );
            }

            RedisPoolManager.getInstance().executeAsync(commands ->
                    commands.incrby(RedisAllGame.Platform_All_DailyContest_PrizePool.getKey(business_no, TimeUtil.getDateTimeFormat(TimeUtil.YYYYMMDD)), (long) c_dailyContest.getInitRewardPool() * ActivityMrg.MULTIPLE)
            );
        }
    }

}
