package com.game.scripts.activity.firstDepositInviteBonus;

import com.game.c_entity.merchant.C_Activity;
import com.game.c_entity.merchant.C_FirstDepositInviteBonus;
import com.game.engine.utils.TimeUtil;
import com.game.entity.player.Player;
import com.game.entity.player.activity.firstDepositInviteBonus.FirstDepositInviteBonusInfo;
import com.game.entity.player.promote.PlayerPromote;
import com.game.hall.mrg.ActivityMrg;
import com.game.hall.mrg.DataHallMrg;
import com.game.hall.mrg.MerchantData;
import com.game.hall.mrg.player.PlayerMrg;
import com.game.hall.script.activity.IActivityScript;

public class FirstDepositInviteBonusScript implements IActivityScript {

    @Override
    public void initData(Player player, C_Activity c_activity) {
        final MerchantData merchantData = DataHallMrg.getInstance().findMerchantData(this.getClass().getSimpleName(), player.getBusiness_no());
        if (merchantData == null) {
            return;
        }

        final C_FirstDepositInviteBonus c_firstDepositInviteBonus = merchantData.findC_FirstDepositInviteBonus(this.getClass().getSimpleName(), ActivityMrg.FIRSTDEPOSIT_INIVTEBONUS);
        if (c_firstDepositInviteBonus == null) {
            return;
        }

        final FirstDepositInviteBonusInfo firstDepositInviteBonusInfo = player.getFirstDepositInviteBonusInfo();
        if (firstDepositInviteBonusInfo.isStart() && firstDepositInviteBonusInfo.getC_id() == c_firstDepositInviteBonus.getC_id()) {
            return;
        }

        firstDepositInviteBonusInfo.reset();
        firstDepositInviteBonusInfo.setStart(true);
        firstDepositInviteBonusInfo.setC_id(c_firstDepositInviteBonus.getC_id());
    }

    @Override
    public void execute(Player player, int currencyId, double wageredAmount, double rechargeAmount) {
        initData(player, null);

        final FirstDepositInviteBonusInfo firstDepositInviteBonusInfo = player.getFirstDepositInviteBonusInfo();
        if (!firstDepositInviteBonusInfo.isStart()) {
            return;
        }

        final MerchantData merchantData = DataHallMrg.getInstance().findMerchantData(this.getClass().getSimpleName(), player.getBusiness_no());
        if (merchantData == null) {
            return;
        }

        final C_FirstDepositInviteBonus c_firstDepositInviteBonus = merchantData.findC_FirstDepositInviteBonus(this.getClass().getSimpleName(), ActivityMrg.FIRSTDEPOSIT_INIVTEBONUS);
        if (c_firstDepositInviteBonus == null) {
            return;
        }

        if (rechargeAmount > 0) {
            firstDepositInviteBonusInfo.setCurrencyId(currencyId);
            firstDepositInviteBonusInfo.setFirstDepositTime(TimeUtil.currentTimeMillis());
            firstDepositInviteBonusInfo.incCharge(currencyId, rechargeAmount);
        }

    }
}
