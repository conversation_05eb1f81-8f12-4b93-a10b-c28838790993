package com.game.scripts.activity.firstDepositInviteBonus;

import com.game.c_entity.merchant.C_Activity;
import com.game.c_entity.merchant.C_FirstDepositInviteBonus;
import com.game.dao.player.PlayerDao;
import com.game.engine.utils.TimeUtil;
import com.game.entity.player.Player;
import com.game.entity.player.activity.firstDepositInviteBonus.FirstDepositInviteBonusInfo;
import com.game.hall.mrg.ActivityMrg;
import com.game.hall.mrg.DataHallMrg;
import com.game.hall.mrg.MerchantData;
import com.game.hall.script.activity.IActivityScript;
import com.game.manager.EntityDaoMrg;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class FirstDepositInviteBonusScript implements IActivityScript {
    private static final Logger LOGGER = LoggerFactory.getLogger(FirstDepositInviteBonusScript.class);

    @Override
    public void initData(Player player, C_Activity c_activity) {
        final MerchantData merchantData = DataHallMrg.getInstance().findMerchantData(this.getClass().getSimpleName(), player.getBusiness_no());
        if (merchantData == null) {
            return;
        }

        final C_FirstDepositInviteBonus c_firstDepositInviteBonus = merchantData.findC_FirstDepositInviteBonus(this.getClass().getSimpleName(), ActivityMrg.FIRSTDEPOSIT_INIVTEBONUS);
        if (c_firstDepositInviteBonus == null) {
            return;
        }

        final FirstDepositInviteBonusInfo firstDepositInviteBonusInfo = player.getFirstDepositInviteBonusInfo();
        if (firstDepositInviteBonusInfo.isStart() && firstDepositInviteBonusInfo.getC_id() == c_firstDepositInviteBonus.getC_id()) {
            return;
        }

        firstDepositInviteBonusInfo.reset();
        firstDepositInviteBonusInfo.setStart(true);
        firstDepositInviteBonusInfo.setC_id(c_firstDepositInviteBonus.getC_id());

        EntityDaoMrg.getInstance().getDao(PlayerDao.class)
                .updateFirstDepositInviteBonusInfo(player.getPlayerId(), firstDepositInviteBonusInfo);
    }

    @Override
    public void resetData(Player player, int cycleType) {
        final MerchantData merchantData = DataHallMrg.getInstance().findMerchantData(this.getClass().getSimpleName(), player.getBusiness_no());
        if (merchantData == null) {
            return;
        }

        final C_FirstDepositInviteBonus c_firstDepositInviteBonus = merchantData.findC_FirstDepositInviteBonus(this.getClass().getSimpleName(), ActivityMrg.FIRSTDEPOSIT_INIVTEBONUS);
        if (c_firstDepositInviteBonus == null) {
            return;
        }

        if (c_firstDepositInviteBonus.getCycleType() != cycleType) {
            return;
        }

        final FirstDepositInviteBonusInfo firstDepositInviteBonusInfo = player.getFirstDepositInviteBonusInfo();

        firstDepositInviteBonusInfo.reset();
        initData(player, null);
    }

    @Override
    public void execute(Player player, int currencyId, double wageredAmount, double rechargeAmount) {
        initData(player, null);

        final FirstDepositInviteBonusInfo firstDepositInviteBonusInfo = player.getFirstDepositInviteBonusInfo();
        if (!firstDepositInviteBonusInfo.isStart()) {
            return;
        }

        final MerchantData merchantData = DataHallMrg.getInstance().findMerchantData(this.getClass().getSimpleName(), player.getBusiness_no());
        if (merchantData == null) {
            return;
        }

        final C_FirstDepositInviteBonus c_firstDepositInviteBonus = merchantData.findC_FirstDepositInviteBonus(this.getClass().getSimpleName(), ActivityMrg.FIRSTDEPOSIT_INIVTEBONUS);
        if (c_firstDepositInviteBonus == null) {
            return;
        }

        if (c_firstDepositInviteBonus.getCycleType() == 1) {
            if ((TimeUtil.currentTimeMillis() < c_firstDepositInviteBonus.getLimitTimeStart())
                    || (TimeUtil.currentTimeMillis() >= c_firstDepositInviteBonus.getLimitTimeEnd())) {
                return;
            }
        }

        if (rechargeAmount < c_firstDepositInviteBonus.getNeedChargeAmount()) {
            return;
        }

        if (rechargeAmount > 0) {
            firstDepositInviteBonusInfo.setCurrencyId(currencyId);
            firstDepositInviteBonusInfo.setVipLevel(player.getVipClub().getVipLevel());
            firstDepositInviteBonusInfo.setFirstDepositTime(TimeUtil.currentTimeMillis());
            firstDepositInviteBonusInfo.incCharge(currencyId, rechargeAmount);
        }

    }
}
