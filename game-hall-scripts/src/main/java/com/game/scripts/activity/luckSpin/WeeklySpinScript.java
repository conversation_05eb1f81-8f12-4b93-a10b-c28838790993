package com.game.scripts.activity.luckSpin;

import com.game.c_entity.middleplatform.C_BaseExchangeRate;
import com.game.c_entity.merchant.C_LuckSpin;
import com.game.dao.player.PlayerDao;
import com.game.engine.math.BigDecimalUtils;
import com.game.engine.utils.TimeUtil;
import com.game.entity.player.Player;
import com.game.entity.player.activity.luckSpin.LuckCondition;
import com.game.entity.player.activity.luckSpin.LuckSpinData;
import com.game.entity.player.activity.luckSpin.LuckSpinInfo;
import com.game.hall.mrg.ActivityMrg;
import com.game.hall.mrg.DataHallMrg;
import com.game.hall.mrg.MerchantData;
import com.game.hall.script.activity.ILuckSpinScript;
import com.game.manager.EntityDaoMrg;
import it.unimi.dsi.fastutil.ints.IntLists;

import java.util.Map;

public class WeeklySpinScript implements ILuckSpinScript {

    @Override
    public void initData(Player player, C_LuckSpin c_luckSpin) {
        if (TimeUtil.currentTimeMillis() < c_luckSpin.getStartTime() || TimeUtil.currentTimeMillis() >= c_luckSpin.getEndTime()) {
            return;
        }

        final LuckSpinInfo luckSpinInfo = player.getLuckSpinInfo();
        LuckSpinData luckSpinData = luckSpinInfo.getLuckSpinData(c_luckSpin.getActivityId());
        if (luckSpinData == null) {
            luckSpinData = new LuckSpinData();
            luckSpinInfo.getLuckSpinDataMap().put(c_luckSpin.getActivityId(), luckSpinData);
        }

        if (luckSpinData.isStart()) {
            return;
        }

        luckSpinData.setStart(true);
        luckSpinData.setUniqueId(c_luckSpin.getC_id());
        luckSpinData.setRemainTimes(c_luckSpin.getInitTurntableTimes());
    }


    @Override
    public void resetData(Player player, C_LuckSpin c_luckSpin) {
        final LuckSpinInfo luckSpinInfo = player.getLuckSpinInfo();
        final LuckSpinData luckSpinData = luckSpinInfo.getLuckSpinData(c_luckSpin.getActivityId());
        if (luckSpinData == null || !luckSpinData.isStart()) {
            return;
        }

        luckSpinData.reset();

        initData(player, c_luckSpin);
    }

    @Override
    public void luckSpinExecute(Player player, C_LuckSpin c_luckSpin, int currencyId, double wageredAmount, double rechargeAmount) {
        final MerchantData merchantData = DataHallMrg.getInstance().findMerchantData(this.getClass().getSimpleName(), player.getBusiness_no());
        if (merchantData == null) {
            return;
        }

        initData(player, c_luckSpin);

        final LuckSpinInfo luckSpinInfo = player.getLuckSpinInfo();
        final LuckSpinData luckSpinData = luckSpinInfo.getLuckSpinData(c_luckSpin.getActivityId());
        if (luckSpinData == null || !luckSpinData.isStart()) {
            return;
        }

        final boolean isManyCurrency = merchantData.getC_currencyMap().size() > 1;

        final C_LuckSpin.ExtraCondition condition = c_luckSpin.getExtraCondition();
        if (luckSpinData.getTotalTimes() >= condition.getMaxTimes()) {
            return;
        }

        final LuckCondition luckCondition = luckSpinData.getLuckCondition();
        if (wageredAmount > 0) {
            luckCondition.incBetTimes();
            luckCondition.incWagered(currencyId, wageredAmount);
        }

        if (rechargeAmount > 0) {
            luckCondition.incRecharge(currencyId, rechargeAmount);
        }

        if (luckCondition.getBetTimes() < condition.getBetTimes()) {
            return;
        }

        double totalRecharge = 0;
        for (Map.Entry<Integer, Double> entry : luckCondition.getRechargeAmountMap().entrySet()) {
            if (isManyCurrency) {
                final C_BaseExchangeRate c_baseExchangeRate = DataHallMrg.getInstance().findC_BaseExchangeRate(this.getClass().getSimpleName(), player.getCurrencyId());
                if (c_baseExchangeRate == null) {
                    continue;
                }
                totalRecharge = BigDecimalUtils.add(totalRecharge, BigDecimalUtils.mul(entry.getValue(), c_baseExchangeRate.getExchangeRate(), 9), 9);
            } else {
                totalRecharge = BigDecimalUtils.add(totalRecharge, entry.getValue(), 9);
            }
        }

        if (totalRecharge < condition.getRechargeUsdAmount()) {
            return;
        }

        double totalWagered = 0;
        for (Map.Entry<Integer, Double> entry : luckCondition.getWageredMap().entrySet()) {
            if (isManyCurrency) {
                final C_BaseExchangeRate c_baseExchangeRate = DataHallMrg.getInstance().findC_BaseExchangeRate(this.getClass().getSimpleName(), player.getCurrencyId());
                if (c_baseExchangeRate == null) {
                    continue;
                }
                totalWagered = BigDecimalUtils.add(totalWagered, BigDecimalUtils.mul(entry.getValue(), c_baseExchangeRate.getExchangeRate(), 9), 9);
            } else {
                totalWagered = BigDecimalUtils.add(totalWagered, entry.getValue(), 9);
            }
        }

        if (totalWagered < condition.getBetUsdAmount()) {
            return;
        }

        luckSpinData.incTotalTimes(condition.getAddTimes());
        luckSpinData.incRemainTimes(condition.getAddTimes());

        if (luckSpinData.getTotalTimes() >= condition.getMaxTimes()) {
            luckSpinData.setFinish(true);
        }

        luckCondition.resetCondition();
    }
}
