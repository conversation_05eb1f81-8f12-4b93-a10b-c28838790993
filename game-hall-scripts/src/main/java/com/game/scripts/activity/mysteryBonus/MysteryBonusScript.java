package com.game.scripts.activity.mysteryBonus;

import com.game.c_entity.merchant.C_Activity;
import com.game.c_entity.merchant.C_MysteryBonus;
import com.game.engine.utils.TimeUtil;
import com.game.entity.player.Player;
import com.game.entity.player.activity.mysteryBonus.MysteryBonusInfo;
import com.game.entity.player.activity.mysteryBonus.MysteryRewardInfo;
import com.game.hall.mrg.ActivityMrg;
import com.game.hall.mrg.DataHallMrg;
import com.game.hall.mrg.MerchantData;
import com.game.hall.script.activity.IActivityScript;

/**
 * 每分钟
 */
public class MysteryBonusScript implements IActivityScript {

    @Override
    public void initData(Player player, C_Activity c_activity) {
        final MerchantData merchantData = DataHallMrg.getInstance().findMerchantData(this.getClass().getSimpleName(), player.getBusiness_no());
        if (merchantData == null) {
            return;
        }

        final C_MysteryBonus c_mysteryBonus = merchantData.findC_MysteryBonus(this.getClass().getSimpleName(), ActivityMrg.MYSTERY_BONUS);
        if (c_mysteryBonus == null) {
            return;
        }

        if (TimeUtil.currentTimeMillis() < c_mysteryBonus.getStartTime() || TimeUtil.currentTimeMillis() >= c_mysteryBonus.getEndTime()) {
            return;
        }

        final MysteryBonusInfo mysteryBonusInfo = player.getMysteryBonusInfo();
        if (mysteryBonusInfo.isStart() && mysteryBonusInfo.getC_id() == c_mysteryBonus.getC_id()) {
            return;
        }

        mysteryBonusInfo.setStart(true);
        mysteryBonusInfo.setC_id(c_mysteryBonus.getC_id());

        for (C_MysteryBonus.MysteryRewardInfo rewardInfo : c_mysteryBonus.getMysteryRewardInfoMap().values()) {
            final MysteryRewardInfo mysteryRewardInfo = new MysteryRewardInfo();
            mysteryRewardInfo.setC_id(rewardInfo.getId());
            mysteryRewardInfo.setSettlementTime(player.getCreateTime() + rewardInfo.getSettlementTime());
            mysteryRewardInfo.setExpiredTime(mysteryRewardInfo.getSettlementTime() + rewardInfo.getExpiredTime());
            mysteryBonusInfo.getMysteryRewardInfoMap().put(rewardInfo.getId(), mysteryRewardInfo);
        }
    }

    @Override
    public void resetData(Player player, C_Activity c_activity) {
        final MerchantData merchantData = DataHallMrg.getInstance().findMerchantData(this.getClass().getSimpleName(), player.getBusiness_no());
        if (merchantData == null) {
            return;
        }

        final C_MysteryBonus c_mysteryBonus = merchantData.findC_MysteryBonus(this.getClass().getSimpleName(), ActivityMrg.MYSTERY_BONUS);
        if (c_mysteryBonus == null) {
            return;
        }

        if (TimeUtil.currentTimeMillis() < c_mysteryBonus.getEndTime()) {
            return;
        }

        final MysteryBonusInfo mysteryBonusInfo = player.getMysteryBonusInfo();
        mysteryBonusInfo.setStart(false);

        initData(player, c_activity);
    }

    @Override
    public void execute(Player player, int currencyId, double wageredAmount, double rechargeAmount) {
        final MysteryBonusInfo mysteryBonusInfo = player.getMysteryBonusInfo();

        initData(player, null);

        if (!mysteryBonusInfo.isStart()) {
            return;
        }

        if (rechargeAmount > 0) {
            mysteryBonusInfo.incRecharge(currencyId, rechargeAmount);
        }
    }

}
