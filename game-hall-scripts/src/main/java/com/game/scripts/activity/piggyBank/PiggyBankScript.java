package com.game.scripts.activity.piggyBank;

import com.game.c_entity.merchant.C_Activity;
import com.game.c_entity.merchant.C_PiggyBank;
import com.game.dao.player.PlayerDao;
import com.game.engine.utils.TimeUtil;
import com.game.entity.player.Player;
import com.game.entity.player.activity.piggyBank.PiggyBankInfo;
import com.game.hall.mrg.ActivityMrg;
import com.game.hall.mrg.DataHallMrg;
import com.game.hall.mrg.MerchantData;
import com.game.hall.script.activity.IActivityScript;
import com.game.manager.EntityDaoMrg;


public class PiggyBankScript implements IActivityScript {

    @Override
    public void resetData(Player player, C_Activity c_activity) {
        final MerchantData merchantData = DataHallMrg.getInstance().findMerchantData(this.getClass().getSimpleName(), player.getBusiness_no());
        if (merchantData == null) {
            return;
        }

        final C_PiggyBank c_piggyBank = merchantData.findC_PiggyBank(this.getClass().getSimpleName(), ActivityMrg.PIGGY_BANK);
        if (c_piggyBank == null) {
            return;
        }

        final PiggyBankInfo piggyBankInfo = player.getPiggyBankInfo();
        if (piggyBankInfo.getReceive().isEmpty()) {
            return;
        }

        if (piggyBankInfo.getReceive().getLast() == 7) {
            piggyBankInfo.getReceive().removeLast();
        }

        final int day = TimeUtil.getNaturalDays(TimeUtil.currentTimeMillis(), piggyBankInfo.getReceiveTime(), c_activity.getTimeZone());
        if (day >= c_piggyBank.getResetDay()) {
            piggyBankInfo.reset();
        }
    }

    @Override
    public void execute(Player player, int currencyId, double wageredAmount, double rechargeAmount) {
        final MerchantData merchantData = DataHallMrg.getInstance().findMerchantData(this.getClass().getSimpleName(), player.getBusiness_no());
        if (merchantData == null) {
            return;
        }

        final C_PiggyBank c_piggyBank = merchantData.findC_PiggyBank(this.getClass().getSimpleName(), ActivityMrg.PIGGY_BANK);
        if (c_piggyBank == null) {
            return;
        }

        final PiggyBankInfo piggyBankInfo = player.getPiggyBankInfo();
        piggyBankInfo.incRecharge(currencyId, rechargeAmount);
    }
}
