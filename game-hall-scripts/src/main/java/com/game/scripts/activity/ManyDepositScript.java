package com.game.scripts.activity;

import com.game.c_entity.merchant.C_Activity;
import com.game.dao.player.PlayerDao;
import com.game.engine.log.GameLog;
import com.game.engine.math.BigDecimalUtils;
import com.game.engine.util.misc.Tuple2;
import com.game.engine.utils.GuidGeneratorUtils;
import com.game.engine.utils.JsonUtils;
import com.game.engine.utils.TimeUtil;
import com.game.entity.player.FreeGameInfo;
import com.game.entity.player.GameInfo;
import com.game.entity.player.Player;
import com.game.entity.player.activity.ActivityData;
import com.game.entity.player.activity.ActivityInfo;
import com.game.hall.main.HallServer;
import com.game.hall.mrg.ActivityMrg;
import com.game.hall.mrg.DataHallMrg;
import com.game.hall.mrg.MerchantData;
import com.game.hall.mrg.currency.RewardRequest;
import com.game.manager.EntityDaoMrg;
import it.unimi.dsi.fastutil.ints.IntLists;

import java.util.List;
import java.util.Optional;

public class ManyDepositScript extends ActivityScript {

    @Override
    public void initData(Player player, C_Activity c_activity) {
        if (TimeUtil.currentTimeMillis() < c_activity.getStartTime() || TimeUtil.currentTimeMillis() >= c_activity.getEndTime()) {
            return;
        }

        if (c_activity.channelLimit(player.getChannelId())) {
            return;
        }

        if (c_activity.agentLimit(player.getAgentId())) {
            return;
        }

        final ActivityInfo activityInfo = player.getActivityInfo();
        final int activityId = Integer.parseInt(c_activity.getActivityId() + "" + c_activity.getActivitySubType());
        ActivityData activityData = activityInfo.getActivityData(activityId);
        if (activityData == null) {
            activityData = new ActivityData();
            activityInfo.getActivityDataMap().put(activityId, activityData);
        }

        if (activityData.isStart()) {
            return;
        }

        if (c_activity.getC_id() == activityData.getC_id()) {
            return;
        }

        activityData.reset();
        activityData.setStart(true);
        activityData.setC_id(c_activity.getC_id());
        if (c_activity.getRegisterExpired() > 0) {
            activityData.setRegisterExpired(player.getCreateTime() + c_activity.getRegisterExpired());
        }
    }

    @Override
    public void resetData(Player player, C_Activity c_activity) {
        final ActivityInfo activityInfo = player.getActivityInfo();
        final int activityId = Integer.parseInt(c_activity.getActivityId() + "" + c_activity.getActivitySubType());
        final ActivityData activityData = activityInfo.getActivityData(activityId);
        if (activityData == null) {
            return;
        }

        if (!activityData.isStart()) {
            return;
        }

        if (TimeUtil.currentTimeMillis() < activityData.getRegisterExpired()) {
            return;
        }

        activityData.resetProcess();
    }

    @Override
    public Tuple2<Double, Double> executeBack(Player player, int currencyId, double progress) {
        if (player.isRechargeBonus()) {
            return new Tuple2<>(0d, 0d);
        }

        final MerchantData merchantData = DataHallMrg.getInstance().findMerchantData(this.getClass().getSimpleName(), player.getBusiness_no());
        if (merchantData == null) {
            return new Tuple2<>(0d, 0d);
        }

        final List<C_Activity> c_activityList = merchantData.getC_activityListMap().get(ActivityMrg.MANY_DEPOSIT);
        if (c_activityList == null || c_activityList.isEmpty()) {
            return new Tuple2<>(0d, 0d);
        }

        if (player.getTotalRechargeTimes() > 2) {
            return new Tuple2<>(0d, 0d);
        }

        final Optional<C_Activity> optional = c_activityList.stream().filter(activity -> activity.getActivitySubType() == player.getTotalRechargeTimes() + 1).findFirst();
        if (optional.isEmpty()) {
            return new Tuple2<>(0d, 0d);
        }

        final C_Activity c_activity = optional.get();
        initData(player, c_activity);

        if (TimeUtil.currentTimeMillis() < c_activity.getStartTime() || TimeUtil.currentTimeMillis() >= c_activity.getEndTime()) {
            return new Tuple2<>(0d, 0d);
        }

        final ActivityInfo activityInfo = player.getActivityInfo();
        final int activityId = Integer.parseInt(c_activity.getActivityId() + "" + c_activity.getActivitySubType());
        final ActivityData activityData = activityInfo.getActivityData(activityId);
        if (activityData == null) {
            return new Tuple2<>(0d, 0d);
        }

        if (activityData.getRegisterExpired() != 0 && TimeUtil.currentTimeMillis() >= activityData.getRegisterExpired()) {
            return new Tuple2<>(0d, 0d);
        }

        if (c_activity.channelLimit(player.getChannelId())) {
            return new Tuple2<>(0d, 0d);
        }

        if (c_activity.agentLimit(player.getAgentId())) {
            return new Tuple2<>(0d, 0d);
        }

        final C_Activity.RewardInfo recharge = c_activity.findRechargeRewardInfo(currencyId, progress);
        if (recharge == null) {
            return new Tuple2<>(0d, 0d);
        }

        double extra = 0;
        if (recharge.getRewardType() == 1) {//1.固定 2.比例
            extra = recharge.getReward();
        } else {
            extra = BigDecimalUtils.mul(progress, recharge.getReward(), 4);
        }

        final RewardRequest rewardRequest = new RewardRequest();
        rewardRequest.addCurrency(currencyId, extra);
        //TODO 日志
        final GameLog playerActivityLog = new GameLog("platform_playerActivityLog");
        playerActivityLog.append("number", GuidGeneratorUtils.generateOrderId())
                .append("business_no", player.getBusiness_no())
                .append("channel", player.getChannel())
                .append("site", player.getWebSite())
                .append("id", c_activity.getC_id())
                .append("activityId", c_activity.getActivityId())
                .append("activityType", c_activity.getActivityType())
                .append("language", c_activity.getLanguage())
                .append("type", 2)//type =1，参加活动，type =2 ,领取
                .append("playerId", player.getPlayerId())
                .append("playerName", player.getPlayerName())
                .append("agentId", player.getAgentId())
                .append("channelId", player.getChannelId())
                .append("region", player.getRegisterRegion())
                .append("logTime", TimeUtil.currentTimeMillis())
                .append("currencyId", currencyId)
                .append("rechargeAmount", progress)
                .append("currencyReward", JsonUtils.writeAsJson(rewardRequest.getCurrencyMap()));
        HallServer.getInstance().getLogProducerMrg().send(playerActivityLog);

        //添加免费游戏次数
        if (recharge.rewardCurrency > 0) {
            final FreeGameInfo freeGameInfo = player.getFreeGameInfo(recharge.rewardCurrency);
            final GameInfo gameInfo = freeGameInfo.getFreeGame(recharge.gameId);
            gameInfo.incFreeTimes(recharge.freeTimes);
            gameInfo.setBet(recharge.bet);
            gameInfo.setMinWithdraw(recharge.getMinWithdraw());
            gameInfo.setMaxWithdraw(recharge.getMaxWithdraw());
            EntityDaoMrg.getInstance().getDao(PlayerDao.class)
                    .updateFreeGameInfo(player, IntLists.singleton(recharge.rewardCurrency));
        }

        return new Tuple2<>(extra, (double) recharge.turnoverMul);
    }
}
