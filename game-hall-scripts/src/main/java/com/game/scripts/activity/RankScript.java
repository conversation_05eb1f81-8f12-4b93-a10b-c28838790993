package com.game.scripts.activity;

import com.game.c_entity.merchant.C_Activity;
import com.game.c_entity.middleplatform.C_BaseExchangeRate;
import com.game.engine.io.redis.RedisPoolManager;
import com.game.engine.math.BigDecimalUtils;
import com.game.engine.script.ScriptLoader;
import com.game.engine.util.misc.Tuple2;
import com.game.engine.utils.TimeUtil;
import com.game.entity.player.Player;
import com.game.entity.player.activity.ActivityData;
import com.game.entity.player.activity.ActivityInfo;
import com.game.entity.player.promote.PlayerPromote;
import com.game.entity.player.stats.Stats;
import com.game.enums.Currency;
import com.game.enums.RewardReason;
import com.game.enums.TransactionFrom;
import com.game.enums.TurnoverReason;
import com.game.enums.redis.RedisHall;
import com.game.enums.redis.RedisRanking;
import com.game.hall.mrg.ActivityMrg;
import com.game.hall.mrg.DataHallMrg;
import com.game.hall.mrg.MerchantData;
import com.game.hall.mrg.currency.CurrencyMrg;
import com.game.hall.mrg.currency.RewardRequest;
import com.game.hall.mrg.player.PlayerMrg;
import com.game.hall.script.IBonusScript;
import com.game.hall.script.IPlayerScript;
import com.game.utils.VirtualThreadUtils;
import io.lettuce.core.api.async.RedisAsyncCommands;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.time.DayOfWeek;
import java.util.*;
import java.util.concurrent.CompletableFuture;

/**
 * 1.充值 2.下注 3.派彩 4.派彩倍数 5.邀请
 */
public class RankScript extends ActivityScript {
    private static final Logger LOGGER = LoggerFactory.getLogger(RankScript.class);

    @Override
    public void initData(Player player, C_Activity c_activity) {
        if (TimeUtil.currentTimeMillis() < c_activity.getStartTime() || TimeUtil.currentTimeMillis() >= c_activity.getEndTime()) {
            return;
        }

        if (c_activity.channelLimit(player.getChannelId())) {
            return;
        }

        if (c_activity.agentLimit(player.getAgentId())) {
            return;
        }

        final ActivityInfo activityInfo = player.getActivityInfo();
        final int activityId = Integer.parseInt(c_activity.getActivityId() + "" + c_activity.getActivitySubType());
        ActivityData activityData = activityInfo.getActivityData(activityId);
        if (activityData == null) {
            activityData = new ActivityData();
            activityInfo.getActivityDataMap().put(activityId, activityData);
        }

        if (activityData.isStart() && c_activity.getC_id() == activityData.getC_id()) {
            return;
        }

        activityData.reset();
        activityData.setStart(true);
        activityData.setC_id(c_activity.getC_id());
        activityData.setRankDate(rankDate(c_activity));
        if (c_activity.getSettlementCycle() == 1) {// 每日
            final long endTime = TimeUtil.getTimeEndOfToday(TimeUtil.currentTimeMillis(), player.getTimeZone());
            activityData.getEndTimeMap().put(c_activity.getActivitySubType(), endTime);
        } else if (c_activity.getSettlementCycle() == 2) {// 每周
            final long endTime = TimeUtil.getTimeEndOfWeek(TimeUtil.currentTimeMillis(), player.getTimeZone());
            activityData.getEndTimeMap().put(c_activity.getActivitySubType(), endTime);
        }
    }

    @Override
    public void resetData(Player player, C_Activity c_activity) {
        final ActivityInfo activityInfo = player.getActivityInfo();
        final int activityId = Integer.parseInt(c_activity.getActivityId() + "" + c_activity.getActivitySubType());
        final ActivityData activityData = activityInfo.getActivityData(activityId);
        if (activityData == null) {
            return;
        }

        if (!activityData.isStart()) {
            return;
        }

        if (c_activity.getSettlementCycle() == 0 && TimeUtil.currentTimeMillis() < c_activity.getEndTime()) {
            return;
        }

        if (c_activity.getActivitySubType() == 5) {//邀请
            final String member = player.getPlayerId() + "";
            receiveRankReward(player, activityData, c_activity, 0, member);
        } else {
            if (c_activity.isEvUsd()) {
                final int currencyId = Currency.USD.getCurrencyId();
                final String member = player.getPlayerId() + "";
                receiveRankReward(player, activityData, c_activity, currencyId, member);
            } else {
                final Map<Integer, Double> currencyMap = player.getCurrencyMap();
                for (Map.Entry<Integer, Double> entry : currencyMap.entrySet()) {
                    final int currencyId = entry.getKey();
                    final String member = currencyId + "_" + player.getPlayerId();
                    receiveRankReward(player, activityData, c_activity, currencyId, member);
                }
            }
        }
        activityData.resetProcess();

        initData(player, c_activity);
    }

    private void receiveRankReward(Player player, ActivityData activityData, C_Activity c_activity, int currencyId, String member) {
        Long ranking = RedisPoolManager.getInstance().function(jedis ->
                jedis.sync().zrevrank(RedisRanking.RANKING_ACTIVITY.getKey(player.getBusiness_no(), c_activity.getActivitySubType(), activityData.getRankDate()), member));
        if (ranking == null) {
            return;
        }
        ranking++;

        if (ranking > c_activity.getRewardNum()) {
            return;
        }

        C_Activity.RewardInfo rewardInfo = null;
        if (currencyId > 0) {
            rewardInfo = c_activity.findRankReward(currencyId, ranking);
        } else {
            rewardInfo = c_activity.findInviteRewardInfo(ranking);
        }

        if (rewardInfo == null) {
            return;
        }

        if (c_activity.getReceive() == 2) {//自动
            final int rewardCurrencyId = rewardInfo.rewardCurrency;
            final RewardRequest rewardRequest = new RewardRequest();
            rewardRequest.addCurrency(rewardCurrencyId, rewardInfo.reward);
            final RewardReason rewardReason = RewardReason.Activity_Rank;
            rewardReason.setSource(c_activity.getActivitySubType() + "");
            CurrencyMrg.getInstance().rewardNoTest(player, rewardRequest.getBonusCurrencyMap(), rewardReason);

            //TODO 打码
//            final WithdrawStandard withdrawStandard = player.getWithdrawStandard(rewardCurrencyId);
//            withdrawStandard.incDrawStandard(BigDecimalUtils.mul(rewardInfo.reward, rewardInfo.turnoverMul, 4));
            final TurnoverReason turnoverReason = TurnoverReason.Activity_Rank;
            turnoverReason.setSource(c_activity.getActivitySubType() + "");
            final C_Activity.RewardInfo finalRewardInfo = rewardInfo;
            ScriptLoader.getInstance().consumerScript("PlayerWithdrawStandardScript",
                    (IPlayerScript script) -> script.drawStandard(player, turnoverReason, rewardCurrencyId, finalRewardInfo.reward,
                            BigDecimalUtils.mul(finalRewardInfo.reward, finalRewardInfo.turnoverMul, 4)));

            //TODO 活动日志
            sendActivityLog(player, 2, c_activity, rewardRequest);

            if (!rewardRequest.getCurrencyMap().isEmpty()) {
                final Tuple2<Integer, Double> tuple2 = rewardRequest.getReward();
                ScriptLoader.getInstance().consumerScript("BonusScript", (IBonusScript script) ->
                        script.addBonusNote(switchTransaction(c_activity), player, tuple2.getFirst(), tuple2.getSecond()));
            }
        } else {//手动
            final long endTime = activityData.getEndTimeMap().getOrDefault(c_activity.getActivitySubType(), 0L);
            if (endTime > 0) {
                activityData.getLastEndTimeMap().put(c_activity.getActivitySubType(), endTime);
            }
            final double progress = activityData.getBonusMap().getOrDefault(rewardInfo.rewardCurrency * 10, 0d);
            activityData.getBonusMap().put(rewardInfo.rewardCurrency * 10, BigDecimalUtils.add(progress, rewardInfo.reward, 4));

            activityData.incTurnover(rewardInfo.rewardCurrency, BigDecimalUtils.mul(rewardInfo.reward, rewardInfo.turnoverMul, 4));
        }
    }

    private TransactionFrom switchTransaction(C_Activity c_activity) {
        return switch (c_activity.getActivitySubType()) {
            case ActivityMrg.RANK_RECHARGE -> TransactionFrom.Promotion_DepositRank;
            case ActivityMrg.RANK_WAGERED -> TransactionFrom.Promotion_WageringRank;
            case ActivityMrg.RANK_WIN -> TransactionFrom.Promotion_WinningRank;
            case ActivityMrg.RANK_MULTIPLE -> TransactionFrom.Promotion_MultiplierRank;
            case ActivityMrg.RANK_AFFILIATE -> TransactionFrom.Promotion_InvitationRank;
            default -> TransactionFrom.None;
        };
    }

    @Override
    public void clearData(Player player, C_Activity c_activity) {
        final ActivityInfo activityInfo = player.getActivityInfo();
        final int activityId = Integer.parseInt(c_activity.getActivityId() + "" + c_activity.getActivitySubType());
        final ActivityData activityData = activityInfo.getActivityData(activityId);
        if (activityData == null) {
            return;
        }

        final long endTime = activityData.getLastEndTimeMap().getOrDefault(c_activity.getActivitySubType(), 0L);
        if (c_activity.getSettlementCycle() != 0 && endTime == 0L) {
            return;
        }

        if (c_activity.getSettlementCycle() == 1) {// 每日
            if (TimeUtil.currentTimeMillis() < endTime + c_activity.getAwardClearTime()) {
                return;
            }
        } else if (c_activity.getSettlementCycle() == 2) {// 每周
            if (TimeUtil.currentTimeMillis() < endTime + c_activity.getAwardClearTime()) {
                return;
            }
        } else {
            if (TimeUtil.currentTimeMillis() < c_activity.getEndTime() + c_activity.getAwardClearTime()) {
                return;
            }
        }

        activityData.getBonusMap().clear();
        activityData.getTurnoverMap().clear();
        activityData.getLastEndTimeMap().clear();
    }

    @Override
    public RewardRequest receiveSettlementReward(Player player, C_Activity c_activity) {
        final RewardRequest rewardRequest = new RewardRequest();
        final ActivityInfo activityInfo = player.getActivityInfo();
        final int activityId = Integer.parseInt(c_activity.getActivityId() + "" + c_activity.getActivitySubType());
        final ActivityData activityData = activityInfo.getActivityData(activityId);
        if (activityData == null) {
            return rewardRequest;
        }

        if (activityData.getBonusMap().isEmpty()) {
            return new RewardRequest();
        }

        for (Map.Entry<Integer, Double> entry : activityData.getBonusMap().entrySet()) {
            rewardRequest.addCurrency(entry.getKey() / 10, entry.getValue());
        }

        final RewardReason rewardReason = RewardReason.Activity_Rank;
        rewardReason.setSource(c_activity.getActivitySubType() + "");
        CurrencyMrg.getInstance().rewardNoTest(player, rewardRequest.getBonusCurrencyMap(), rewardReason);

        for (Map.Entry<Integer, Double> entry : activityData.getTurnoverMap().entrySet()) {
            final double amount = activityData.getBonusMap().getOrDefault(entry.getKey() * 10, 0d);
            if (amount == 0) {
                continue;
            }
            final TurnoverReason turnoverReason = TurnoverReason.Activity_Rank;
            turnoverReason.setSource(c_activity.getActivitySubType() + "");
            ScriptLoader.getInstance().consumerScript("PlayerWithdrawStandardScript",
                    (IPlayerScript script) -> script.drawStandard(player, turnoverReason, entry.getKey(), amount, entry.getValue()));
        }

        activityData.getBonusMap().clear();
        activityData.getTurnoverMap().clear();
        return rewardRequest;
    }

    @Override
    public void execute(Player player, int rankType, int currencyId, double validAmount, double progress) {
        try {
            final MerchantData merchantData = DataHallMrg.getInstance().findMerchantData(this.getClass().getSimpleName(), player.getBusiness_no());
            if (merchantData == null) {
                return;
            }

            final List<C_Activity> c_activityList = merchantData.getC_activityListMap().get(ActivityMrg.RANK);
            if (c_activityList == null || c_activityList.isEmpty()) {
                return;
            }

            final Optional<C_Activity> optional = c_activityList.stream().filter(c_activity -> c_activity.getActivitySubType() == rankType).findFirst();
            if (optional.isEmpty()) {
                return;
            }

            final C_Activity c_activity = optional.get();
            final boolean isJoin = checkActivityJoin(player, currencyId, c_activity);
            if (!isJoin) {
                return;
            }

            initData(player, c_activity);

            final ActivityInfo activityInfo = player.getActivityInfo();
            final int activityId = Integer.parseInt(c_activity.getActivityId() + "" + c_activity.getActivitySubType());
            final ActivityData activityData = activityInfo.getActivityData(activityId);

            final int signUp = activityData.getSignUpMap().getOrDefault(c_activity.getActivitySubType(), 0);
            if (signUp == 0) {
                return;
            }

            if (rankType == ActivityMrg.RANK_RECHARGE) {
                activityData.incRecharge(currencyId, progress);
            }

            if (rankType == ActivityMrg.RANK_WAGERED || rankType == ActivityMrg.RANK_WIN) {
                activityData.incWagered(currencyId, progress);
            }

            if (rankType == ActivityMrg.RANK_MULTIPLE) {
                activityData.incWagered(currencyId, validAmount);
            }

            if (!checkCondition(player, activityData, c_activity, currencyId)) {
                return;
            }

            if (c_activity.isEvUsd()) {
                final C_BaseExchangeRate c_baseExchangeRate = DataHallMrg.getInstance().findC_BaseExchangeRate(this.getClass().getSimpleName(), currencyId);
                if (c_baseExchangeRate == null) {
                    return;
                }
                RedisPoolManager.getInstance().executeAsync(commands ->
                        commands.zincrby(RedisRanking.RANKING_ACTIVITY.getKey(player.getBusiness_no(), c_activity.getActivitySubType(), activityData.getRankDate()), BigDecimalUtils.mul(progress, c_baseExchangeRate.getExchangeRate(), 4), player.getPlayerId() + "")
                );
            } else {
                RedisPoolManager.getInstance().executeAsync(commands ->
                        commands.zincrby(RedisRanking.RANKING_ACTIVITY.getKey(player.getBusiness_no(), c_activity.getActivitySubType(), activityData.getRankDate()), progress, currencyId + "_" + player.getPlayerId())
                );
            }
        } catch (Exception e) {
            LOGGER.error("RankScript", e);
        }
    }

    private String rankDate(C_Activity c_activity) {
        String date = "";
        if (c_activity.getSettlementCycle() == 1) {//每日
            date = TimeUtil.getDateTimeFormat(TimeUtil.YYYYMMDD, c_activity.getTimeZone());
        } else if (c_activity.getSettlementCycle() == 2) {//每周
            final long dayOfWeekEndTimestamp = TimeUtil.getDayOfWeekEndTimestamp(TimeUtil.currentTimeMillis(), c_activity.getTimeZone(), DayOfWeek.SUNDAY);
            date = TimeUtil.getDateTimeFormat(dayOfWeekEndTimestamp, TimeUtil.YYYYMMDD, c_activity.getTimeZone());
        } else {
            date = TimeUtil.getDateTimeFormat(c_activity.getEndTime(), TimeUtil.YYYYMMDD, c_activity.getTimeZone());
        }
        return date;
    }

    private boolean checkCondition(Player player, ActivityData activityData, C_Activity c_activity, int currencyId) {
        if (c_activity.isEvUsd()) {
            currencyId = Currency.USD.getCurrencyId();
        }

        final C_Activity.ConditionInfo conditionInfo = c_activity.findCondition(currencyId);
        if (conditionInfo == null) {
            return false;
        }

        if (conditionInfo.isActivities()) {//活动期间
            double wagered = 0;
            double rechargeAmount = 0;
            if (c_activity.isEvUsd()) {
                for (Map.Entry<Integer, Double> entry : activityData.getWageredMap().entrySet()) {
                    final C_BaseExchangeRate c_baseExchangeRate = DataHallMrg.getInstance().findC_BaseExchangeRate(this.getClass().getSimpleName(), entry.getKey());
                    if (c_baseExchangeRate == null) {
                        continue;
                    }
                    wagered = BigDecimalUtils.add(wagered, BigDecimalUtils.mul(entry.getValue(), c_baseExchangeRate.getExchangeRate(), 9), 9);
                }

                for (Map.Entry<Integer, Double> entry : activityData.getRechargeAmountMap().entrySet()) {
                    final C_BaseExchangeRate c_baseExchangeRate = DataHallMrg.getInstance().findC_BaseExchangeRate(this.getClass().getSimpleName(), entry.getKey());
                    if (c_baseExchangeRate == null) {
                        continue;
                    }
                    rechargeAmount = BigDecimalUtils.add(rechargeAmount, BigDecimalUtils.mul(entry.getValue(), c_baseExchangeRate.getExchangeRate(), 9), 9);
                }
            } else {
                wagered = activityData.getWageredMap().getOrDefault(conditionInfo.currencyId, 0d);
                rechargeAmount = activityData.getRechargeAmountMap().getOrDefault(conditionInfo.currencyId, 0d);
            }

            if (c_activity.getActivitySubType() == ActivityMrg.RANK_WAGERED && wagered < conditionInfo.wagered) {
                LOGGER.warn("playerId：{}，rank is activities，wagered：{}，conditionWagered：{}", player.getPlayerId(), wagered, conditionInfo.wagered);
                return false;
            }

            if (c_activity.getActivitySubType() == ActivityMrg.RANK_RECHARGE && rechargeAmount < conditionInfo.recharge) {
                LOGGER.warn("playerId：{}，rank is activities，recharge：{}，conditionRecharge：{}", player.getPlayerId(), rechargeAmount, conditionInfo.recharge);
                return false;
            }

        } else {
            double totalBetAmount = 0;
            double totalRechargeAmount = 0;
            if (c_activity.isEvUsd()) {
                for (Map.Entry<Integer, Stats> entry : player.getStatsInfo().getStatsMap().entrySet()) {
                    final Stats stats = entry.getValue();
                    final C_BaseExchangeRate c_baseExchangeRate = DataHallMrg.getInstance().findC_BaseExchangeRate(this.getClass().getSimpleName(), entry.getKey());
                    if (c_baseExchangeRate == null) {
                        continue;
                    }
                    totalBetAmount = BigDecimalUtils.add(totalBetAmount, BigDecimalUtils.mul(stats.getTotalBetAmount(), c_baseExchangeRate.getExchangeRate(), 9), 9);
                    totalRechargeAmount = BigDecimalUtils.add(totalRechargeAmount, BigDecimalUtils.mul(stats.getTotalRechargeAmount(), c_baseExchangeRate.getExchangeRate(), 9), 9);
                }
            } else {
                final Stats stats = player.getStats(currencyId);
                totalBetAmount = stats.getTotalBetAmount();
                totalRechargeAmount = stats.getTotalRechargeAmount();
            }

            if (totalBetAmount < conditionInfo.wagered) {
                LOGGER.warn("playerId：{}，rank not activities，totalWagered：{}，conditionWagered：{}", player.getPlayerId(), totalBetAmount, conditionInfo.wagered);
                return false;
            }

            if (totalRechargeAmount < conditionInfo.recharge) {
                LOGGER.warn("playerId：{}，rank not activities，totalRecharge：{}，conditionRecharge：{}", player.getPlayerId(), totalRechargeAmount, conditionInfo.recharge);
                return false;
            }
        }
        return true;
    }

    @Override
    public void execute(Player player, int currencyId, double wageredAmount, double rechargeAmount) {
        final MerchantData merchantData = DataHallMrg.getInstance().findMerchantData(this.getClass().getSimpleName(), player.getBusiness_no());

        final List<C_Activity> c_activityList = merchantData.getC_activityListMap().get(ActivityMrg.RANK);
        if (c_activityList == null || c_activityList.isEmpty()) {
            return;
        }

        final Optional<C_Activity> optional = c_activityList.stream().filter(c_activity -> c_activity.getActivitySubType() == ActivityMrg.RANK_AFFILIATE).findFirst();
        if (optional.isEmpty()) {
            return;
        }

        final C_Activity c_activity = optional.get();

        PlayerPromote playerPromote = player.getPlayerPromote();
        if (playerPromote.getSuperiorId() == 0) {
            return;
        }

        initData(player, c_activity);

        final ActivityInfo activityInfo = player.getActivityInfo();
        final int activityId = Integer.parseInt(c_activity.getActivityId() + "" + c_activity.getActivitySubType());
        ActivityData activityData = activityInfo.getActivityData(activityId);

        if (wageredAmount > 0) {
            activityData.incBetTimes();
            activityData.incWagered(currencyId, wageredAmount);
        }

        if (rechargeAmount > 0) {
            activityData.incRecharge(currencyId, rechargeAmount);
        }

        if (!checkCondition(activityData, c_activity, currencyId)) {
            return;
        }

        final Player superiorPlayer = PlayerMrg.getInstance().findDbPlayer(playerPromote.getSuperiorId());
        if (superiorPlayer == null) {
            return;
        }

//        final ActivityInfo superiorActivityInfo = superiorPlayer.getActivityInfo();
//        ActivityData superiorActivityData = superiorActivityInfo.getActivityData(activityId);
//        if (superiorActivityData == null) {
//            superiorActivityData = new ActivityData();
//            superiorActivityInfo.getActivityDataMap().put(activityId, superiorActivityData);
//        }

        final boolean isExist = RedisPoolManager.getInstance().function(jedis ->
                jedis.sync().sismember(RedisHall.Platform_Role_Map_ReferralActivity.getKey(superiorPlayer.getPlayerId()), player.getPlayerId() + ""));

//        if (superiorActivityData.getInvitePlayerId().contains(player.getPlayerId())) {
//            return;
//        }
        if (isExist) {
            return;
        }
//        superiorActivityData.getInvitePlayerId().add(player.getPlayerId());
//        EntityDaoMrg.getInstance().getDao(PlayerDao.class).activityInfoDao
//                .updateInsertActivity(superiorPlayer.getPlayerId(), superiorActivityInfo, IntLists.singleton(activityId));

//        final int inviteNum = superiorActivityData.getInvitePlayerId().size();
        final long inviteNum = RedisPoolManager.getInstance().function(jedis ->
                jedis.sync().scard(RedisHall.Platform_Role_Map_ReferralActivity.getKey(superiorPlayer.getPlayerId())));

        final ActivityData finalActivityData = activityData;
        RedisPoolManager.getInstance().asyncPipeline(commands -> {
            final List<CompletableFuture<?>> futures = new ArrayList<>();
            futures.add(commands.sadd(RedisHall.Platform_Role_Map_ReferralActivity.getKey(superiorPlayer.getPlayerId()), player.getPlayerId() + "")
                    .toCompletableFuture());
            futures.add(commands.zadd(RedisRanking.RANKING_ACTIVITY.getKey(player.getBusiness_no(), c_activity.getActivitySubType(), finalActivityData.getRankDate()), inviteNum, superiorPlayer.getPlayerId() + "")
                    .toCompletableFuture());
            return futures;
        });
    }

    private boolean checkCondition(ActivityData activityData, C_Activity c_activity, int currencyId) {
        final C_Activity.InviteInfo condition = c_activity.getInviteInfo();
        if (condition.getCurrencyId() != Currency.USD.getCurrencyId() && condition.getCurrencyId() != currencyId) {
            return false;
        }

        if (activityData.getBetTimes() < condition.getBetTimes()) {
            return false;
        }

        double recharge = 0;
        double wagered = 0;
        if (condition.getCurrencyId() == Currency.USD.getCurrencyId()) {
            for (Map.Entry<Integer, Double> entry : activityData.getRechargeAmountMap().entrySet()) {
                final C_BaseExchangeRate c_baseExchangeRate = DataHallMrg.getInstance().findC_BaseExchangeRate(this.getClass().getSimpleName(), entry.getKey());
                if (c_baseExchangeRate == null) {
                    continue;
                }
                recharge = BigDecimalUtils.add(recharge, BigDecimalUtils.mul(entry.getValue(), c_baseExchangeRate.getExchangeRate(), 9), 9);
            }
            for (Map.Entry<Integer, Double> entry : activityData.getWageredMap().entrySet()) {
                final C_BaseExchangeRate c_baseExchangeRate = DataHallMrg.getInstance().findC_BaseExchangeRate(this.getClass().getSimpleName(), entry.getKey());
                if (c_baseExchangeRate == null) {
                    continue;
                }
                wagered = BigDecimalUtils.add(wagered, BigDecimalUtils.mul(entry.getValue(), c_baseExchangeRate.getExchangeRate(), 9), 9);
            }

        } else {
            recharge = activityData.getRechargeAmountMap().getOrDefault(currencyId, 0d);
            wagered = activityData.getWageredMap().getOrDefault(currencyId, 0d);
        }

        if (recharge < condition.getRechargeUsdAmount()) {
            return false;
        }

        if (wagered < condition.getBetUsdAmount()) {
            return false;
        }

        return true;
    }

}
