package com.game.scripts.activity;

import com.game.c_entity.merchant.C_Activity;
import com.game.c_entity.middleplatform.C_BaseExchangeRate;
import com.game.dao.player.PlayerDao;
import com.game.engine.math.BigDecimalUtils;
import com.game.engine.script.ScriptLoader;
import com.game.engine.util.misc.Tuple2;
import com.game.engine.utils.FastCollectionsUtils;
import com.game.engine.utils.TimeUtil;
import com.game.entity.player.FreeGameInfo;
import com.game.entity.player.GameInfo;
import com.game.entity.player.Player;
import com.game.entity.player.activity.ActivityData;
import com.game.entity.player.activity.ActivityInfo;
import com.game.enums.Currency;
import com.game.enums.RewardReason;
import com.game.enums.TransactionFrom;
import com.game.enums.TurnoverReason;
import com.game.hall.mrg.ActivityMrg;
import com.game.hall.mrg.DataHallMrg;
import com.game.hall.mrg.MerchantData;
import com.game.hall.mrg.currency.CurrencyMrg;
import com.game.hall.mrg.currency.RewardRequest;
import com.game.hall.script.IBonusScript;
import com.game.hall.script.IPlayerScript;
import com.game.manager.EntityDaoMrg;
import it.unimi.dsi.fastutil.ints.Int2DoubleMap;
import it.unimi.dsi.fastutil.ints.IntLists;
import it.unimi.dsi.fastutil.objects.ObjectIterator;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 1.累计（每档、最高档）
 * 2.单次（每档）
 * 3.每日、每周
 */
public class DepositScript extends ActivityScript {
    private static final Logger LOGGER = LoggerFactory.getLogger(DepositScript.class);

    @Override
    public void initData(Player player, C_Activity c_activity) {
        if (TimeUtil.currentTimeMillis() < c_activity.getStartTime() || TimeUtil.currentTimeMillis() >= c_activity.getEndTime()) {
            return;
        }

        if (c_activity.channelLimit(player.getChannelId())) {
            return;
        }

        if (c_activity.agentLimit(player.getAgentId())) {
            return;
        }

        final ActivityInfo activityInfo = player.getActivityInfo();
        final int activityId = Integer.parseInt(c_activity.getActivityId() + "" + c_activity.getActivitySubType());
        ActivityData activityData = activityInfo.getActivityData(activityId);
        if (activityData == null) {
            activityData = new ActivityData();
            activityInfo.getActivityDataMap().put(activityId, activityData);
        }

        if (activityData.isStart() && c_activity.getC_id() == activityData.getC_id()) {
            return;
        }

        activityData.reset();
        activityData.setStart(true);
        activityData.setC_id(c_activity.getC_id());

        if (c_activity.getSettlementCycle() == 1) {// 每日
            final long endTime = TimeUtil.getTimeEndOfToday(TimeUtil.currentTimeMillis(), player.getTimeZone());
            activityData.getEndTimeMap().put(c_activity.getActivitySubType(), endTime);
        } else if (c_activity.getSettlementCycle() == 2) {// 每周
            final long endTime = TimeUtil.getTimeEndOfWeek(TimeUtil.currentTimeMillis(), player.getTimeZone());
            activityData.getEndTimeMap().put(c_activity.getActivitySubType(), endTime);
        }
    }

    @Override
    public void resetData(Player player, C_Activity c_activity) {
        final ActivityInfo activityInfo = player.getActivityInfo();
        final int activityId = Integer.parseInt(c_activity.getActivityId() + "" + c_activity.getActivitySubType());
        final ActivityData activityData = activityInfo.getActivityData(activityId);
        if (activityData == null) {
            return;
        }

        if (!activityData.isStart()) {
            return;
        }

        if (c_activity.getSettlementCycle() == 0 && TimeUtil.currentTimeMillis() < c_activity.getEndTime()) {
            return;
        }

        if (c_activity.getReceive() == 2) {//自动
            final List<C_Activity.RewardInfo> rewardInfos = allReward(c_activity, activityData, activityData.getProgressMap());

            final RewardRequest rewardRequest = reward(player, activityData, activityData.getProgressMap(), c_activity, rewardInfos);

            if (!rewardRequest.getCurrencyMap().isEmpty()) {
                final RewardReason rewardReason = RewardReason.Activity_RechargeAccumulation;
                rewardReason.setSource(c_activity.getActivitySubType() + "");
                CurrencyMrg.getInstance().rewardNoTest(player, rewardRequest.getBonusCurrencyMap(), rewardReason);

                //TODO 活动日志
                sendActivityLog(player, 2, c_activity, rewardRequest);

                TransactionFrom transaction;
                if (c_activity.getActivitySubType() == 1) {//累计
                    transaction = TransactionFrom.Promotion_AccumulatedDeposit;
                } else {
                    transaction = TransactionFrom.Promotion_SingleDeposit;
                }
                final Tuple2<Integer, Double> tuple2 = rewardRequest.getReward();
                ScriptLoader.getInstance().consumerScript("BonusScript", (IBonusScript script) ->
                        script.addBonusNote(transaction, player, tuple2.getFirst(), tuple2.getSecond()));
            }
        } else {//手动
            final long endTime = activityData.getEndTimeMap().getOrDefault(c_activity.getActivitySubType(), 0L);
            if (endTime > 0) {
                activityData.getLastEndTimeMap().put(c_activity.getActivitySubType(), endTime);
            }

            int currencyId = player.getCurrencyId();
            if (c_activity.isEvUsd()) {
                currencyId = Currency.USD.getCurrencyId();
            }
            final List<C_Activity.RewardInfo> rewardInfos = reward(currencyId, c_activity, activityData, activityData.getProgressMap());
            final RewardRequest rewardRequest = manualReward(player, activityData, c_activity, rewardInfos);
            for (ObjectIterator<Int2DoubleMap.Entry> itr = FastCollectionsUtils.fastIterator(rewardRequest.getBonusCurrencyMap()); itr.hasNext(); ) {
                final Int2DoubleMap.Entry entry = itr.next();

                final double progress = activityData.getBonusMap().getOrDefault(entry.getIntKey(), 0d);
                activityData.getBonusMap().put(entry.getIntKey(), BigDecimalUtils.add(progress, entry.getDoubleValue(), 4));
            }
        }
        activityData.resetProcess();

        initData(player, c_activity);
    }

    @Override
    public void clearData(Player player, C_Activity c_activity) {
        final ActivityInfo activityInfo = player.getActivityInfo();
        final int activityId = Integer.parseInt(c_activity.getActivityId() + "" + c_activity.getActivitySubType());
        final ActivityData activityData = activityInfo.getActivityData(activityId);
        if (activityData == null) {
            return;
        }

        final long endTime = activityData.getLastEndTimeMap().getOrDefault(c_activity.getActivitySubType(), 0L);
        if (c_activity.getSettlementCycle() != 0 && endTime == 0L) {
            return;
        }

        if (c_activity.getSettlementCycle() == 1) {// 每日
            if (TimeUtil.currentTimeMillis() < endTime + c_activity.getAwardClearTime()) {
                return;
            }
        } else if (c_activity.getSettlementCycle() == 2) {// 每周
            if (TimeUtil.currentTimeMillis() < endTime + c_activity.getAwardClearTime()) {
                return;
            }
        } else {
            if (TimeUtil.currentTimeMillis() < c_activity.getEndTime() + c_activity.getAwardClearTime()) {
                return;
            }
        }

        activityData.getBonusMap().clear();
        activityData.getTurnoverMap().clear();
        activityData.getLastEndTimeMap().clear();
    }

    @Override
    public void execute(Player player, int currencyId, double progress) {
        if (progress == 0) {
            return;
        }

        final MerchantData merchantData = DataHallMrg.getInstance().findMerchantData(this.getClass().getSimpleName(), player.getBusiness_no());
        if (merchantData == null) {
            return;
        }

        final List<C_Activity> c_activityList = merchantData.getC_activityListMap().get(ActivityMrg.DEPOSIT);
        if (c_activityList == null || c_activityList.isEmpty()) {
            return;
        }


        for (C_Activity c_activity : c_activityList) {
            final boolean isJoin = checkActivityJoin(player, currencyId, c_activity);
            if (!isJoin) {
                continue;
            }

            initData(player, c_activity);

            final ActivityInfo activityInfo = player.getActivityInfo();
            final int activityId = Integer.parseInt(c_activity.getActivityId() + "" + c_activity.getActivitySubType());
            final ActivityData activityData = activityInfo.getActivityData(activityId);

            if (c_activity.getActivitySubType() == 1) {//累计
                activityData.incProgress(currencyId, progress);
            } else {
                activityData.getProgressMap().put(currencyId, progress);
            }

            //自动、完成立即
            if (c_activity.isNowReceive() && c_activity.getReceive() == 2) {

                final Set<Integer> rewards = activityData.getReceiveRewards(currencyId);
                if (c_activity.getActivitySubType() == 2 && !rewards.isEmpty()) {
                    return;
                }

                final List<C_Activity.RewardInfo> rewardInfos = reward(currencyId, c_activity, activityData, activityData.getProgressMap());

                final RewardRequest rewardRequest = reward(player, activityData, activityData.getProgressMap(), c_activity, rewardInfos);

                final RewardReason rewardReason = RewardReason.Activity_RechargeAccumulation;
                rewardReason.setSource(c_activity.getActivitySubType() + "");
                CurrencyMrg.getInstance().rewardNoTest(player, rewardRequest.getBonusCurrencyMap(), rewardReason);

                //TODO 活动日志
                sendActivityLog(player, 2, c_activity, rewardRequest);

                if (!rewardRequest.getCurrencyMap().isEmpty()) {
                    TransactionFrom transaction;
                    if (c_activity.getActivitySubType() == 1) {//累计
                        transaction = TransactionFrom.Promotion_AccumulatedDeposit;
                    } else {
                        transaction = TransactionFrom.Promotion_SingleDeposit;
                    }
                    final Tuple2<Integer, Double> tuple2 = rewardRequest.getReward();
                    ScriptLoader.getInstance().consumerScript("BonusScript", (IBonusScript script) ->
                            script.addBonusNote(transaction, player, tuple2.getFirst(), tuple2.getSecond()));
                }
            }
        }
    }

    @Override
    public RewardRequest receiveReward(Player player, C_Activity c_activity) {
        final ActivityInfo activityInfo = player.getActivityInfo();
        final int activityId = Integer.parseInt(c_activity.getActivityId() + "" + c_activity.getActivitySubType());
        final ActivityData activityData = activityInfo.getActivityData(activityId);
        if (activityData == null) {
            return new RewardRequest();
        }

        int currencyId = player.getCurrencyId();
        if (c_activity.isEvUsd()) {
            currencyId = Currency.USD.getCurrencyId();
        }
        final Set<Integer> rewards = activityData.getReceiveRewards(currencyId);
        if (c_activity.getActivitySubType() == 2 && !rewards.isEmpty()) {
            return new RewardRequest();
        }

        final List<C_Activity.RewardInfo> rewardInfos = reward(currencyId, c_activity, activityData, activityData.getProgressMap());

        final RewardRequest rewardRequest = reward(player, activityData, activityData.getProgressMap(), c_activity, rewardInfos);

        final RewardReason rewardReason = RewardReason.Activity_RechargeAccumulation;
        rewardReason.setSource(c_activity.getActivitySubType() + "");
        CurrencyMrg.getInstance().rewardNoTest(player, rewardRequest.getBonusCurrencyMap(), rewardReason);

        return rewardRequest;
    }

    @Override
    public RewardRequest receiveSettlementReward(Player player, C_Activity c_activity) {
        final ActivityInfo activityInfo = player.getActivityInfo();
        final int activityId = Integer.parseInt(c_activity.getActivityId() + "" + c_activity.getActivitySubType());
        final ActivityData activityData = activityInfo.getActivityData(activityId);

        if (activityData == null) {
            return new RewardRequest();
        }

        if (activityData.getBonusMap().isEmpty()) {
            return new RewardRequest();
        }

        final RewardRequest rewardRequest = new RewardRequest();
        for (Map.Entry<Integer, Double> entry : activityData.getBonusMap().entrySet()) {
            rewardRequest.addCurrency(entry.getKey() / 10, entry.getValue());
        }

        final RewardReason rewardReason = RewardReason.Activity_RechargeAccumulation;
        rewardReason.setSource(c_activity.getActivitySubType() + "");
        CurrencyMrg.getInstance().rewardNoTest(player, rewardRequest.getBonusCurrencyMap(), rewardReason);

        for (Map.Entry<Integer, Double> entry : activityData.getTurnoverMap().entrySet()) {
            final double amount = activityData.getBonusMap().getOrDefault(entry.getKey() * 10, 0d);
            if (amount == 0) {
                continue;
            }
            final TurnoverReason turnoverReason = TurnoverReason.Activity_RechargeAccumulation;
            turnoverReason.setSource(c_activity.getActivitySubType() + "");
            ScriptLoader.getInstance().consumerScript("PlayerWithdrawStandardScript",
                    (IPlayerScript script) -> script.drawStandard(player, turnoverReason, entry.getKey(), amount, entry.getValue()));
        }

        activityData.getBonusMap().clear();
        activityData.getTurnoverMap().clear();
        return rewardRequest;
    }


}
