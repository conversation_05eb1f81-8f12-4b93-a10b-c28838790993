package com.game.scripts.activity.firstChargeSignIn;

import com.game.c_entity.merchant.C_Activity;
import com.game.c_entity.merchant.C_FirstChargeSignIn;
import com.game.dao.player.PlayerDao;
import com.game.engine.log.GameLog;
import com.game.engine.utils.GuidGeneratorUtils;
import com.game.engine.utils.TimeUtil;
import com.game.entity.player.Player;
import com.game.entity.player.activity.firstChargeSignIn.FirstChargeSignInInfo;
import com.game.entity.player.stats.Stats;
import com.game.hall.main.HallServer;
import com.game.hall.mrg.ActivityMrg;
import com.game.hall.mrg.DataHallMrg;
import com.game.hall.mrg.MerchantData;
import com.game.hall.script.activity.IActivityScript;
import com.game.manager.EntityDaoMrg;


public class FirstChargeSignInScript implements IActivityScript {

    @Override
    public void resetData(Player player, C_Activity c_activity) {
        final MerchantData merchantData = DataHallMrg.getInstance().findMerchantData(this.getClass().getSimpleName(), player.getBusiness_no());
        if (merchantData == null) {
            return;
        }

        final C_FirstChargeSignIn c_firstChargeSignIn = merchantData.findC_FirstChargeSignIn(this.getClass().getSimpleName(), ActivityMrg.FIRSTCHARGE_SIGNIN);
        if (c_firstChargeSignIn == null) {
            return;
        }

        final FirstChargeSignInInfo firstChargeSignInInfo = player.getFirstChargeSignInInfo();
        if (firstChargeSignInInfo.getReceive().isEmpty()) {
            return;
        }

        final int day = TimeUtil.getNaturalDays(TimeUtil.currentTimeMillis(), firstChargeSignInInfo.getRechargeTime(), c_firstChargeSignIn.getTimeZone());
        if (day > c_firstChargeSignIn.getResetDay()) {
            firstChargeSignInInfo.reset();
        }
    }

    @Override
    public void execute(Player player, int currencyId, double wageredAmount, double rechargeAmount) {
        final Stats stats = player.getStats(currencyId);
        if (stats.getTotalRechargeTimes() != 1) {
            return;
        }

        final MerchantData merchantData = DataHallMrg.getInstance().findMerchantData(this.getClass().getSimpleName(), player.getBusiness_no());
        if (merchantData == null) {
            return;
        }

        final FirstChargeSignInInfo firstChargeSignInInfo = player.getFirstChargeSignInInfo();
        if (firstChargeSignInInfo.getRechargeTime() != 0) {
            return;
        }

        final C_FirstChargeSignIn c_firstChargeSignIn = merchantData.findC_FirstChargeSignIn(this.getClass().getSimpleName(), ActivityMrg.FIRSTCHARGE_SIGNIN);
        if (c_firstChargeSignIn == null) {
            return;
        }

        if (currencyId != c_firstChargeSignIn.getRechargeCurrencyId()) {
            return;
        }

        if (rechargeAmount < c_firstChargeSignIn.getRechargeAmount()) {
            return;
        }

        firstChargeSignInInfo.setRechargeTime(TimeUtil.currentTimeMillis());
        EntityDaoMrg.getInstance().getDao(PlayerDao.class)
                .updateFirstChargeSignInInfo(player.getPlayerId(), firstChargeSignInInfo);

        final GameLog playerFirstChargeSignInLog = new GameLog("platform_playerFirstChargeSignInLog");
        playerFirstChargeSignInLog.append("number", GuidGeneratorUtils.generateOrderId())
                .append("business_no", player.getBusiness_no())
                .append("channel", player.getChannel())
                .append("site", player.getWebSite())
                .append("playerId", player.getPlayerId())
                .append("playerName", player.getPlayerName())
                .append("agentId", player.getAgentId())
                .append("channelId", player.getChannelId())
                .append("region", player.getRegisterRegion())
                .append("logTime", TimeUtil.currentTimeMillis())
                .append("activityId", ActivityMrg.FIRSTCHARGE_SIGNIN)
                .append("activityUniqueId", c_firstChargeSignIn.getC_id())
                .append("type", 2)//1.领取 2.激活
                .append("currDay", 0)
                .append("currencyId", c_firstChargeSignIn.getRewardCurrencyId())
                .append("reward", 0);
        HallServer.getInstance().getLogProducerMrg().send(playerFirstChargeSignInLog);
    }
}
