package com.game.scripts.activity.continuousDeposit;

import com.game.c_entity.merchant.C_Activity;
import com.game.c_entity.merchant.C_ContinuousDeposit;
import com.game.dao.player.PlayerDao;
import com.game.engine.log.GameLog;
import com.game.engine.math.BigDecimalUtils;
import com.game.engine.script.ScriptLoader;
import com.game.engine.util.misc.Tuple2;
import com.game.engine.utils.GuidGeneratorUtils;
import com.game.engine.utils.TimeUtil;
import com.game.entity.player.Player;
import com.game.entity.player.activity.continuousDeposit.ContinuousDeposit;
import com.game.enums.RewardReason;
import com.game.enums.TransactionFrom;
import com.game.enums.TurnoverReason;
import com.game.hall.main.HallServer;
import com.game.hall.mrg.ActivityMrg;
import com.game.hall.mrg.DataHallMrg;
import com.game.hall.mrg.MerchantData;
import com.game.hall.mrg.currency.CurrencyMrg;
import com.game.hall.mrg.currency.RewardRequest;
import com.game.hall.script.IBonusScript;
import com.game.hall.script.IPlayerScript;
import com.game.hall.script.activity.IActivityScript;
import com.game.manager.EntityDaoMrg;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class ContinuousDepositScript implements IActivityScript {
    private static final Logger LOGGER = LoggerFactory.getLogger(ContinuousDepositScript.class);

    @Override
    public void initData(Player player, C_Activity c_activity) {
        final MerchantData merchantData = DataHallMrg.getInstance().findMerchantData(this.getClass().getSimpleName(), player.getBusiness_no());
        if (merchantData == null) {
            return;
        }

        final C_ContinuousDeposit c_continuousDeposit = merchantData.findC_ContinuousDeposit(this.getClass().getSimpleName(), ActivityMrg.CONTINUOUS_DEPOSIT);
        if (c_continuousDeposit == null) {
            return;
        }

        if (TimeUtil.currentTimeMillis() < c_continuousDeposit.getStartTime() || TimeUtil.currentTimeMillis() >= c_continuousDeposit.getEndTime()) {
            return;
        }

        final ContinuousDeposit continuousDepositInfo = player.getContinuousDepositInfo();
        if (continuousDepositInfo.isStart() && continuousDepositInfo.getC_id() == c_continuousDeposit.getC_id()) {
            return;
        }

        continuousDepositInfo.reset();
        continuousDepositInfo.setC_id(c_continuousDeposit.getC_id());
        continuousDepositInfo.setStart(true);
    }

    @Override
    public void resetData(Player player, C_Activity c_activity) {
        final MerchantData merchantData = DataHallMrg.getInstance().findMerchantData(this.getClass().getSimpleName(), player.getBusiness_no());
        if (merchantData == null) {
            return;
        }

        final C_ContinuousDeposit c_continuousDeposit = merchantData.findC_ContinuousDeposit(this.getClass().getSimpleName(), ActivityMrg.CONTINUOUS_DEPOSIT);
        if (c_continuousDeposit == null) {
            return;
        }

        final ContinuousDeposit continuousDepositInfo = player.getContinuousDepositInfo();
        if (!continuousDepositInfo.isStart()) {
            return;
        }

        continuousDepositInfo.setStart(false);
        continuousDepositInfo.reset();

        initData(player, c_activity);
    }

    @Override
    public void execute(Player player, int currencyId, double wageredAmount, double rechargeAmount) {
        final ContinuousDeposit continuousDepositInfo = player.getContinuousDepositInfo();

        initData(player, null);

        if (!continuousDepositInfo.isStart()) {
            return;
        }

        final MerchantData merchantData = DataHallMrg.getInstance().findMerchantData(this.getClass().getSimpleName(), player.getBusiness_no());
        if (merchantData == null) {
            return;
        }

        final C_ContinuousDeposit c_continuousDeposit = merchantData.findC_ContinuousDeposit(this.getClass().getSimpleName(), ActivityMrg.CONTINUOUS_DEPOSIT);
        if (c_continuousDeposit == null) {
            return;
        }

        final C_ContinuousDeposit.ContinuousDeposit continuousDeposit = c_continuousDeposit.getContinuousDepositMap().get(continuousDepositInfo.getRechargeTimes() + 1);
        if (continuousDeposit == null) {
            return;
        }

        continuousDepositInfo.setRechargeTimes(continuousDepositInfo.getRechargeTimes() + 1);
        EntityDaoMrg.getInstance().getDao(PlayerDao.class)
                .updateContinuousDepositInfo(player.get_id(), continuousDepositInfo);

        if (rechargeAmount < continuousDeposit.getRecharge()) {
            return;
        }

        double giveawayAmount = BigDecimalUtils.mul(rechargeAmount, continuousDeposit.getGiveawayRate(), 4);
        if (giveawayAmount > continuousDeposit.getGiveawayUpper()) {
            giveawayAmount = continuousDeposit.getGiveawayUpper();
        }

        final RewardRequest rewardRequest = new RewardRequest();
        rewardRequest.addCurrency(currencyId, giveawayAmount);
        final RewardReason rewardReason = RewardReason.ContinuousDeposit;
        rewardReason.setSource(continuousDepositInfo.getRechargeTimes() + "");
        CurrencyMrg.getInstance().rewardNoTest(player, rewardRequest.getBonusCurrencyMap(), rewardReason);

        //TODO 打码
//        final WithdrawStandard withdrawStandard = player.getWithdrawStandard(currencyId);
//        withdrawStandard.incDrawStandard(BigDecimalUtils.mul(giveawayAmount, c_continuousDeposit.getTurnoverMul(), 4));
        final TurnoverReason turnoverReason = TurnoverReason.ContinuousDeposit;
        turnoverReason.setSource(continuousDepositInfo.getRechargeTimes() + "");
        final double finalGiveawayAmount = giveawayAmount;
        ScriptLoader.getInstance().consumerScript("PlayerWithdrawStandardScript",
                (IPlayerScript script) -> script.drawStandard(player, turnoverReason, currencyId,finalGiveawayAmount ,
                        BigDecimalUtils.mul(finalGiveawayAmount, c_continuousDeposit.getTurnoverMul(), 4)));

        if (!rewardRequest.getCurrencyMap().isEmpty()) {
            final Tuple2<Integer, Double> tuple2 = rewardRequest.getReward();
            ScriptLoader.getInstance().consumerScript("BonusScript",
                    (IBonusScript script) -> script.addBonusNote(TransactionFrom.ContinuousDeposit, player, tuple2.getFirst(), tuple2.getSecond()));
        }

        //日志
        final GameLog playerActivityLog = new GameLog("platform_playerContinuousDepositLog");
        playerActivityLog.append("number", GuidGeneratorUtils.generateOrderId())
                .append("business_no", player.getBusiness_no())
                .append("site", player.getWebSite())
                .append("channel", player.getChannel())
                .append("playerId", player.getPlayerId())
                .append("playerName", player.getPlayerName())
                .append("language", player.getLanguage())
                .append("agentId", player.getAgentId())
                .append("channelId", player.getChannelId())
                .append("region", player.getRegisterRegion())
                .append("activityId", ActivityMrg.CONTINUOUS_DEPOSIT)
                .append("activityUniqueId", c_continuousDeposit.getC_id())
                .append("gear", continuousDeposit.id)// 挡位
                .append("recharge", rechargeAmount)
                .append("currencyId", currencyId)
                .append("reward", giveawayAmount)
                .append("logTime", TimeUtil.currentTimeMillis());
        HallServer.getInstance().getLogProducerMrg().send(playerActivityLog);
    }

}
