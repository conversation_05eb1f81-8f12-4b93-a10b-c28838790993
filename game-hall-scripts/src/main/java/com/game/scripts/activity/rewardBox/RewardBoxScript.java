package com.game.scripts.activity.rewardBox;

import com.game.c_entity.merchant.C_Activity;
import com.game.c_entity.merchant.C_RewardBox;
import com.game.c_entity.middleplatform.C_BaseExchangeRate;
import com.game.engine.io.redis.RedisPoolManager;
import com.game.engine.log.GameLog;
import com.game.engine.math.BigDecimalUtils;
import com.game.engine.script.ScriptLoader;
import com.game.engine.utils.GuidGeneratorUtils;
import com.game.engine.utils.TimeUtil;
import com.game.entity.player.Player;
import com.game.entity.player.activity.rewardBox.RewardBoxInfo;
import com.game.entity.player.promote.PlayerPromote;
import com.game.enums.redis.RedisHall;
import com.game.hall.main.HallServer;
import com.game.hall.mrg.ActivityMrg;
import com.game.hall.mrg.DataHallMrg;
import com.game.hall.mrg.MerchantData;
import com.game.hall.mrg.player.PlayerMrg;
import com.game.hall.script.activity.IActivityScript;
import com.game.utils.VirtualThreadUtils;
import io.lettuce.core.api.async.RedisAsyncCommands;
import io.netty.util.internal.StringUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.net.InetAddress;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;

/**
 * 每分钟
 */
public class RewardBoxScript implements IActivityScript {
    private static final Logger LOGGER = LoggerFactory.getLogger(RewardBoxScript.class);

    @Override
    public void initData(Player player, C_Activity c_activity) {
        final MerchantData merchantData = DataHallMrg.getInstance().findMerchantData(this.getClass().getSimpleName(), player.getBusiness_no());
        if (merchantData == null) {
            return;
        }

        final C_RewardBox c_rewardBox = merchantData.findC_RewardBox(this.getClass().getSimpleName(), ActivityMrg.REWARD_BOX);
        if (c_rewardBox == null) {
            return;
        }

        if (TimeUtil.currentTimeMillis() < c_rewardBox.getStartTime() || TimeUtil.currentTimeMillis() >= c_rewardBox.getEndTime()) {
            return;
        }

        final RewardBoxInfo rewardBoxInfo = player.getRewardBoxInfo();
        if (rewardBoxInfo.isStart() && rewardBoxInfo.getC_id() == c_rewardBox.getC_id()) {
            return;
        }

        rewardBoxInfo.reset();
        rewardBoxInfo.setC_id(c_rewardBox.getC_id());
        rewardBoxInfo.setStart(true);

        RedisPoolManager.getInstance().asyncPipeline(commands -> {
            final List<CompletableFuture<?>> futures = new ArrayList<>();
            futures.add(commands.del(RedisHall.Platform_Role_Map_RewardBoxInvite.getKey(player.getPlayerId())).toCompletableFuture());
            futures.add(commands.del(RedisHall.Platform_Role_Map_RewardBoxReferral.getKey(player.getPlayerId())).toCompletableFuture());
            futures.add(commands.del(RedisHall.Platform_Role_Map_RewardBox_Ip.getKey(player.getPlayerId())).toCompletableFuture());
            futures.add(commands.del(RedisHall.Platform_Role_Map_RewardBox_Device.getKey(player.getPlayerId())).toCompletableFuture());
            return futures;
        });
    }

    @Override
    public void resetData(Player player, C_Activity c_activity) {
        final MerchantData merchantData = DataHallMrg.getInstance().findMerchantData(this.getClass().getSimpleName(), player.getBusiness_no());
        if (merchantData == null) {
            return;
        }

        final C_RewardBox c_rewardBox = merchantData.findC_RewardBox(this.getClass().getSimpleName(), ActivityMrg.REWARD_BOX);
        if (c_rewardBox == null) {
            return;
        }

        if (TimeUtil.currentTimeMillis() < c_rewardBox.getEndTime()) {
            return;
        }

        final RewardBoxInfo rewardBoxInfo = player.getRewardBoxInfo();
        if (!rewardBoxInfo.isStart()) {
            return;
        }

        rewardBoxInfo.setStart(false);
    }

    @Override
    public void execute(Player player, int currencyId, double wageredAmount, double rechargeAmount) {
        final RewardBoxInfo rewardBoxInfo = player.getRewardBoxInfo();

        initData(player, null);

        if (!rewardBoxInfo.isStart()) {
            return;
        }

        final MerchantData merchantData = DataHallMrg.getInstance().findMerchantData(this.getClass().getSimpleName(), player.getBusiness_no());
        if (merchantData == null) {
            return;
        }

        final C_RewardBox c_rewardBox = merchantData.findC_RewardBox(this.getClass().getSimpleName(), ActivityMrg.REWARD_BOX);
        if (c_rewardBox == null) {
            return;
        }

        final boolean isManyCurrency = merchantData.getC_currencyMap().size() > 1;

        final PlayerPromote playerPromote = player.getPlayerPromote();
        if (playerPromote == null) {
            return;
        }

        if (playerPromote.getSuperiorId() == 0) {
            return;
        }

        if (wageredAmount > 0) {
            rewardBoxInfo.incBetTimes();
            rewardBoxInfo.incWagered(currencyId, wageredAmount);
        }

        if (rechargeAmount > 0) {
            rewardBoxInfo.incRecharge(currencyId, rechargeAmount);
        }

        final C_RewardBox.InviteInfo inviteInfo = c_rewardBox.getInviteInfo();
        if (rewardBoxInfo.getBetTimes() < inviteInfo.getBetTimes()) {
            return;
        }

        double totalRecharge = 0;
        for (Map.Entry<Integer, Double> entry : rewardBoxInfo.getRechargeAmountMap().entrySet()) {
            if (isManyCurrency) {
                final C_BaseExchangeRate c_baseExchangeRate = DataHallMrg.getInstance().findC_BaseExchangeRate(this.getClass().getSimpleName(), player.getCurrencyId());
                if (c_baseExchangeRate == null) {
                    continue;
                }
                totalRecharge = BigDecimalUtils.add(totalRecharge, BigDecimalUtils.mul(entry.getValue(), c_baseExchangeRate.getExchangeRate(), 9), 9);
            } else {
                totalRecharge = BigDecimalUtils.add(totalRecharge, entry.getValue(), 9);
            }
        }

        if (totalRecharge < inviteInfo.getRechargeAmount()) {
            return;
        }

        double totalWagered = 0;
        for (Map.Entry<Integer, Double> entry : rewardBoxInfo.getWageredMap().entrySet()) {
            if (isManyCurrency) {
                final C_BaseExchangeRate c_baseExchangeRate = DataHallMrg.getInstance().findC_BaseExchangeRate(this.getClass().getSimpleName(), player.getCurrencyId());
                if (c_baseExchangeRate == null) {
                    continue;
                }
                totalWagered = BigDecimalUtils.add(totalWagered, BigDecimalUtils.mul(entry.getValue(), c_baseExchangeRate.getExchangeRate(), 9), 9);
            } else {
                totalWagered = BigDecimalUtils.add(totalWagered, entry.getValue(), 9);
            }
        }

        if (totalWagered < inviteInfo.getWageredAmount()) {
            return;
        }

        final Player superiorPlayer = PlayerMrg.getInstance().findDbPlayer(playerPromote.getSuperiorId());
        if (superiorPlayer == null) {
            return;
        }

        final boolean isExistReferral = RedisPoolManager.getInstance().function(jedis ->
                jedis.sync().sismember(RedisHall.Platform_Role_Map_RewardBoxReferral.getKey(superiorPlayer.getPlayerId()), player.getPlayerId() + ""));
        if (!isExistReferral) {
            return;
        }

        final boolean isExist = RedisPoolManager.getInstance().function(jedis ->
                jedis.sync().sismember(RedisHall.Platform_Role_Map_RewardBoxInvite.getKey(superiorPlayer.getPlayerId()), player.getPlayerId() + ""));
        if (isExist) {
            return;
        }

        RedisPoolManager.getInstance().asyncPipeline(commands -> {
            final List<CompletableFuture<?>> futures = new ArrayList<>();
            futures.add(commands.sadd(RedisHall.Platform_Role_Map_RewardBoxInvite.getKey(superiorPlayer.getPlayerId()), player.getPlayerId() + "").toCompletableFuture());
            futures.add(commands.sadd(RedisHall.Platform_Role_Map_RewardBox_Ip.getKey(superiorPlayer.getPlayerId()), player.getIp()).toCompletableFuture());
            futures.add(commands.sadd(RedisHall.Platform_Role_Map_RewardBox_Device.getKey(superiorPlayer.getPlayerId()), player.getDevice() + "-" + player.getModel()).toCompletableFuture());
            return futures;
        });

        //日志
        final GameLog playerActivityLog = new GameLog("platform_playerEfficientRewardBoxLog");
        playerActivityLog.append("number", GuidGeneratorUtils.generateOrderId())
                .append("business_no", player.getBusiness_no())
                .append("site", player.getWebSite())
                .append("channel", player.getChannel())
                .append("playerId", player.getPlayerId())
                .append("playerName", player.getPlayerName())
                .append("language", player.getLanguage())
                .append("agentId", player.getAgentId())
                .append("channelId", player.getChannelId())
                .append("region", player.getRegisterRegion())
                .append("activityId", ActivityMrg.REWARD_BOX)
                .append("activityUniqueId", c_rewardBox.getC_id())
                .append("betTime", rewardBoxInfo.getBetTimes())
                .append("registerTime", player.getCreateTime())
                .append("superiorId", superiorPlayer.getPlayerId())
                .append("currencyId", isManyCurrency ? c_rewardBox.getRewardCurrencyId() : player.getCurrencyId())
                .append("wageredAmount", totalWagered)
                .append("rechargeAmount", totalRecharge)
                .append("logTime", TimeUtil.currentTimeMillis());
        HallServer.getInstance().getLogProducerMrg().send(playerActivityLog);
    }

}
