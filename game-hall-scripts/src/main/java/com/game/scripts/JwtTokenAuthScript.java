package com.game.scripts;

import com.game.c_entity.merchant.C_BaseServerConfig;
import com.game.c_entity.middleplatform.C_BaseMerchant;
import com.game.engine.utils.JWTUtil;
import com.game.hall.mrg.DataHallMrg;
import com.game.hall.script.IJwtTokenAuthScript;
import io.netty.util.internal.StringUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.net.URI;
import java.util.Map;
import java.util.Objects;

public class JwtTokenAuthScript implements IJwtTokenAuthScript {
    private static final Logger LOGGER = LoggerFactory.getLogger(JwtTokenAuthScript.class);

    @Override
    public boolean authToken(String host, String token) {
        Map<String, Object> params = null;
        try {
            params = JWTUtil.verify(token);

            final String version = (String) params.get("version");
            if (StringUtil.isNullOrEmpty(version)) {
                return false;
            }

//            LOGGER.warn("host：{}，params：{}", host, params);

            final URI uri = new URI(host);
            final String domain = uri.getHost();
            final C_BaseMerchant c_baseMerchant = DataHallMrg.getInstance().findC_BaseHostMerchant(this.getClass().getSimpleName(), domain);
            if (c_baseMerchant == null) {
                return false;
            }

            final C_BaseServerConfig c_baseServerConfig = DataHallMrg.getInstance().findC_BaseServerConfig(this.getClass().getSimpleName(), c_baseMerchant.getBusiness_no());
            if (c_baseServerConfig == null) {
                return false;
            }

            if (!Objects.equals(version, c_baseServerConfig.getVersion())) {
                return false;
            }

        } catch (Exception e) {
            if (params != null) {
                LOGGER.error("JwtTokenAuthScript：{}，userId：{}", e, params.get("userId"));
            } else {
                LOGGER.error("JwtTokenAuthScript", e);
            }
            return false;
        }

        return true;
    }
}
