package com.game.scripts;

import com.game.hall.mrg.DataHallMrg;
import com.game.hall.mrg.MerchantData;
import com.game.hall.script.IFunctionEnabledScript;

public class FunctionEnabledScript implements IFunctionEnabledScript {
    @Override
    public boolean functionEnabled(String business_no, int function) {
        final MerchantData merchantData = DataHallMrg.getInstance().findMerchantData(this.getClass().getSimpleName(), business_no);
        if (merchantData == null) {
            return false;
        }

        return merchantData.getC_functionEnabledMap().containsKey(function);
    }
}
