package com.game.scripts.game;

import com.game.c_entity.merchant.C_CashBack;
import com.game.c_entity.merchant.C_Currency;
import com.game.c_entity.merchant.C_DailyContest;
import com.game.c_entity.merchant.C_GameApi;
import com.game.c_entity.middleplatform.C_BaseHead;
import com.game.c_entity.middleplatform.C_BaseExchangeRate;
import com.game.c_entity.middleplatform.C_BaseMerchant;
import com.game.dao.game.GameNoteDao;
import com.game.engine.io.redis.RedisPoolManager;
import com.game.engine.math.BigDecimalUtils;
import com.game.engine.math.MathUtils;
import com.game.engine.script.ScriptLoader;
import com.game.engine.utils.MsgUtil;
import com.game.engine.utils.TimeUtil;
import com.game.engine.utils.UniqueIDGenerator;
import com.game.entity.game.GameNote;
import com.game.entity.player.Player;
import com.game.enums.Currency;
import com.game.enums.FunctionEnabled;
import com.game.enums.redis.RedisAllGame;
import com.game.hall.RandomNameUtils;
import com.game.hall.main.HallServer;
import com.game.hall.mrg.ActivityMrg;
import com.game.hall.mrg.DataHallMrg;
import com.game.hall.mrg.MerchantData;
import com.game.hall.mrg.UserTcpMrg;
import com.game.hall.script.IFunctionEnabledScript;
import com.game.hall.script.IGameScript;
import com.game.hall.script.activity.IActivityScript;
import com.game.hall.script.activity.IDailyContestScript;
import com.game.hall.script.activity.IWeeklyRaffleScript;
import com.game.manager.EntityDaoMrg;
import com.game.user.UserSession;
import com.game.utils.VirtualThreadUtils;
import com.proto.CommonMessage;
import com.proto.MIDMessage;
import com.proto.TcpMessage;
import io.netty.util.internal.StringUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.*;
import java.util.stream.Collectors;

public class GameScript implements IGameScript {
    private static final Logger LOGGER = LoggerFactory.getLogger(GameScript.class);

    @Override
    public void notifyGameNoteBet() {
        try {
            final List<String> business_noList = DataHallMrg.getInstance().getC_baseHostMerchantMap().values().stream()
                    .map(C_BaseMerchant::getBusiness_no)
                    .distinct()
                    .toList();

            if (business_noList.isEmpty()) {
                return;
            }

            final long start = TimeUtil.currentTimeMillis();

            final TcpMessage.ResTcpGameNoteDataMessage.Builder res = TcpMessage.ResTcpGameNoteDataMessage.newBuilder();
            res.setMsgID(MIDMessage.MID.ResTcpGameNoteData_VALUE);
            for (String business_no : business_noList) {
                res.clearAllBet();
                res.clearHighRollers();
                res.clearLuckyBet();

                final MerchantData merchantData = DataHallMrg.getInstance().getMerchantDataMap().get(business_no);
                if (merchantData == null) {
                    continue;
                }

                final boolean functionEnabled = ScriptLoader.getInstance().functionScript("FunctionEnabledScript", (IFunctionEnabledScript script)
                        -> script.functionEnabled(business_no, FunctionEnabled.GameNote.getType()));
                if (!functionEnabled) {
                    continue;
                }

                final String initLanguage = merchantData.findC_GlobalValue(this.getClass().getSimpleName(), "initLanguage");
                if (StringUtil.isNullOrEmpty(initLanguage)) {
                    continue;
                }

                final List<Integer> headIds = DataHallMrg.getInstance().getC_headMap().values().stream().map(C_BaseHead::getHeadId).collect(Collectors.toList());
                if (headIds.isEmpty()) {
                    continue;
                }

                final List<C_GameApi> c_gameApiList = new ArrayList<>(merchantData.getC_gameApiMap().values());
                if (c_gameApiList.isEmpty()) {
                    continue;
                }

                final List<Integer> currencys = merchantData.getC_currencyMap().values().stream()
                        .map(C_Currency::getCurrencyId)
                        .filter(currencyId -> currencyId != Currency.USD.getCurrencyId())
                        .collect(Collectors.toList());
                if (currencys.isEmpty()) {
                    continue;
                }

                final List<GameNote> allBet = generateGameNoteData(business_no, 1, 20, c_gameApiList, currencys);
                allBet.sort((o1, o2) -> Long.compare(o2.getCreateTime(), o1.getCreateTime()));
                for (GameNote gameNote : allBet) {
                    final C_GameApi c_gameApi = merchantData.findC_GameApi(this.getClass().getSimpleName(), gameNote.getGameId());
                    if (c_gameApi == null || !c_gameApi.setLanguageGameApiData(Integer.parseInt(initLanguage))) {
                        continue;
                    }
                    res.addAllBet(buildGameNote(c_gameApi, gameNote, MathUtils.random(headIds)));
                }

                final List<GameNote> highRoller = generateGameNoteData(business_no, 2, 5, c_gameApiList, currencys);
                highRoller.sort((o1, o2) -> Long.compare(o2.getCreateTime(), o1.getCreateTime()));
                for (GameNote gameNote : highRoller) {
                    final C_GameApi c_gameApi = merchantData.findC_GameApi(this.getClass().getSimpleName(), gameNote.getGameId());
                    if (c_gameApi == null || !c_gameApi.setLanguageGameApiData(Integer.parseInt(initLanguage))) {
                        continue;
                    }
                    res.addHighRollers(buildGameNote(c_gameApi, gameNote, MathUtils.random(headIds)));
                }

                final List<GameNote> luckyBet = generateGameNoteData(business_no, 3, 5, c_gameApiList, currencys);
                luckyBet.sort((o1, o2) -> Long.compare(o2.getCreateTime(), o1.getCreateTime()));
                for (GameNote gameNote : luckyBet) {
                    final C_GameApi c_gameApi = merchantData.findC_GameApi(this.getClass().getSimpleName(), gameNote.getGameId());
                    if (c_gameApi == null || !c_gameApi.setLanguageGameApiData(Integer.parseInt(initLanguage))) {
                        continue;
                    }
                    res.addLuckyBet(buildGameNote(c_gameApi, gameNote, MathUtils.random(headIds)));
                }

                res.setBusinessNo(business_no);

                for (Map.Entry<Long, UserSession> entry : UserTcpMrg.getInstance().getSessionMap().entrySet()) {
                    if (Objects.equals(entry.getValue().getBusiness_no(), business_no)) {
                        MsgUtil.sendClientMsg(entry.getValue().getUserSession(), res.build());
                    }
                }
            }
            long end = TimeUtil.currentTimeMillis() - start;
            if (end > 2) {
//                LOGGER.info("notifyGameNote：cost time：{}", end);
            }
        } catch (Exception e) {
            LOGGER.error("notifyGameNote", e);
        }
    }

    @Override
    public void notifyGameNoteHigh() {
        try {
//            final List<String> business_noList = DataHallMrg.getInstance().getC_baseHostMerchantMap().values().stream()
//                    .map(C_BaseMerchant::getBusiness_no)
//                    .distinct()
//                    .toList();
//
//            if (business_noList.isEmpty()) {
//                return;
//            }
//
//            final long start = TimeUtil.currentTimeMillis();
//
//            final TcpMessage.ResTcpGameNoteDataMessage.Builder res = TcpMessage.ResTcpGameNoteDataMessage.newBuilder();
//            res.setMsgID(MIDMessage.MID.ResTcpGameNoteData_VALUE);
//            for (String business_no : business_noList) {
//                res.clearAllBet();
//                res.clearHighRollers();
//                res.clearLuckyBet();
//
//                final MerchantData merchantData = DataHallMrg.getInstance().findMerchantData(this.getClass().getSimpleName(), business_no);
//                if (merchantData == null) {
//                    continue;
//                }
//
//                final String initLanguage = merchantData.findC_GlobalValue(this.getClass().getSimpleName(), "initLanguage");
//                if (StringUtil.isNullOrEmpty(initLanguage)) {
//                    continue;
//                }
//
//                final List<Integer> headIds = DataHallMrg.getInstance().getC_headMap().values().stream().map(C_BaseHead::getHeadId).collect(Collectors.toList());
//                if (headIds.isEmpty()) {
//                    continue;
//                }
//
//                final List<C_GameApi> c_gameApiList = new ArrayList<>(merchantData.getC_gameApiMap().values());
//                if (c_gameApiList.isEmpty()) {
//                    continue;
//                }
//
//                final List<Integer> currencys = merchantData.getC_currencyMap().values().stream()
//                        .map(C_Currency::getCurrencyId)
//                        .filter(currencyId -> currencyId != Currency.USD.getCurrencyId())
//                        .collect(Collectors.toList());
//                if (currencys.isEmpty()) {
//                    continue;
//                }
//
//                final List<GameNote> highRoller = generateGameNoteData(business_no, 2, c_gameApiList, currencys);
//                highRoller.sort((o1, o2) -> Long.compare(o2.getCreateTime(), o1.getCreateTime()));
//                for (GameNote gameNote : highRoller) {
//                    final C_GameApi c_gameApi = merchantData.findC_GameApi(this.getClass().getSimpleName(), gameNote.getGameId());
//                    if (!c_gameApi.setLanguageGameApiData(Integer.parseInt(initLanguage))) {
//                        continue;
//                    }
//                    res.addHighRollers(buildGameNote(c_gameApi, gameNote, MathUtils.random(headIds)));
//                }
//
//                final List<GameNote> luckyBet = generateGameNoteData(business_no, 3, c_gameApiList, currencys);
//                luckyBet.sort((o1, o2) -> Long.compare(o2.getCreateTime(), o1.getCreateTime()));
//                for (GameNote gameNote : luckyBet) {
//                    final C_GameApi c_gameApi = merchantData.findC_GameApi(this.getClass().getSimpleName(), gameNote.getGameId());
//                    if (!c_gameApi.setLanguageGameApiData(Integer.parseInt(initLanguage))) {
//                        continue;
//                    }
//                    res.addLuckyBet(buildGameNote(c_gameApi, gameNote, MathUtils.random(headIds)));
//                }
//
//                res.setBusinessNo(business_no);
//                HallServer.getInstance().getHallTcpClient2Gate().broadcastMsg(res.build());
//            }
//            long end = TimeUtil.currentTimeMillis() - start;
//            if (end > 2) {
////                LOGGER.info("notifyGameNote：cost time：{}", end);
//            }
        } catch (Exception e) {
            LOGGER.error("notifyGameNote", e);
        }
    }

    private static List<GameNote> generateGameNoteData(String business_no, int payoutType, int count,
                                                       List<C_GameApi> c_gameApis, List<Integer> currencys) {
        if (c_gameApis.isEmpty()) {
            return new ArrayList<>();
        }

        final List<GameNote> gameNotes = new ArrayList<>();

        long start = TimeUtil.currentTimeMillis();
        final long end = TimeUtil.currentTimeMillis();
        if (payoutType == 1) {//all bets
            final long tick = start - 10 * TimeUtil.SEC;
            final List<GameNote> gameNoteList = EntityDaoMrg.getInstance().getDao(GameNoteDao.class)
                    .loadGameNote(business_no, 0, tick, end, 5);
            gameNotes.addAll(gameNoteList);
        } else if (payoutType == 2) {//high rollers
            final long tick = start - 10 * TimeUtil.SEC;
            final List<GameNote> gameNoteList = EntityDaoMrg.getInstance().getDao(GameNoteDao.class)
                    .loadGameNote(business_no, 100, tick, end, 5);
            gameNotes.addAll(gameNoteList);
        } else if (payoutType == 3) {//luck win
            final long tick = start - 10 * TimeUtil.SEC;
            final List<GameNote> gameNoteList = EntityDaoMrg.getInstance().getDao(GameNoteDao.class)
                    .loadGameNote(business_no, 1, tick, end, 5);
            gameNotes.addAll(gameNoteList);
        }

        final UniqueIDGenerator generator = HallServer.getInstance().getUniqueIDGenerator();
        do {
            final int currencyId = MathUtils.random(currencys);
            final String playerName = RandomNameUtils.generateRandomName();
            final C_GameApi c_gameApi = MathUtils.random(c_gameApis);
            final GameNote gameNote = new GameNote(generator.nextId());
            gameNote.setPlayerName(playerName);
            gameNote.setGameId(c_gameApi.getGameId());
            gameNote.setGameType(c_gameApi.getType());
            gameNote.setPlatformId(c_gameApi.getPlatformId());
            gameNote.setCurrencyId(currencyId);

            if (payoutType == 1) {
                gameNote.setCreateTime(TimeUtil.currentTimeMillis() - MathUtils.random(1, 2) * TimeUtil.SEC);
                gameNote.setBetAmount(MathUtils.random(1, 2d));
                if (currencyId == 2018) {
                    gameNote.setBetAmount(MathUtils.random(10000, 20000d));
                }
                final int random = MathUtils.random(1, 100);
                if (random < 60) {
                    gameNote.setWin(0);
                } else if (random < 95) {
                    final double mul = MathUtils.random(1, 2d);
                    gameNote.setWin(BigDecimalUtils.mul(gameNote.getBetAmount(), mul, 2));
                } else {
                    final double mul = MathUtils.random(5, 10d);
                    gameNote.setWin(BigDecimalUtils.mul(gameNote.getBetAmount(), mul, 2));
                }
            } else if (payoutType == 2) {
                gameNote.setCreateTime(TimeUtil.currentTimeMillis() - MathUtils.random(1, 10) * TimeUtil.SEC);
                final int random = MathUtils.random(1, 100);
                double mul = 0;

                if (currencys.size() > 1) {
                    final C_BaseExchangeRate c_baseExchangeRate = DataHallMrg.getInstance().findC_BaseExchangeRate(GameScript.class.getSimpleName(), currencyId);
                    if (c_baseExchangeRate == null) {
                        continue;
                    }
                    if (c_baseExchangeRate.getExchangeRate() >= 10) {
                        continue;
                    }
                    if (random < 77) {
                        mul = MathUtils.random(1, 2d);
                        gameNote.setBetAmount(BigDecimalUtils.mul(MathUtils.random(100, 1000d), 1 / c_baseExchangeRate.getExchangeRate(), 4));
                    } else if (random < 97) {
                        mul = MathUtils.random(2, 3d);
                        gameNote.setBetAmount(BigDecimalUtils.mul(MathUtils.random(1000, 2000d), 1 / c_baseExchangeRate.getExchangeRate(), 4));
                    } else {
                        mul = MathUtils.random(3, 5d);
                        gameNote.setBetAmount(BigDecimalUtils.mul(MathUtils.random(2000, 5000d), 1 / c_baseExchangeRate.getExchangeRate(), 4));
                    }
                } else {
                    if (random < 77) {
                        mul = MathUtils.random(1, 2d);
                        gameNote.setBetAmount(BigDecimalUtils.mul(MathUtils.random(100, 1000d), 4));
                    } else if (random < 97) {
                        mul = MathUtils.random(2, 3d);
                        gameNote.setBetAmount(BigDecimalUtils.mul(MathUtils.random(1000, 2000d), 4));
                    } else {
                        mul = MathUtils.random(3, 5d);
                        gameNote.setBetAmount(BigDecimalUtils.mul(MathUtils.random(2000, 5000d), 4));
                    }
                }

                gameNote.setWin(BigDecimalUtils.mul(gameNote.getBetAmount(), mul, 4));
            } else if (payoutType == 3) {
                gameNote.setCreateTime(TimeUtil.currentTimeMillis() - MathUtils.random(1, 10) * TimeUtil.SEC);
                final int random = MathUtils.random(1, 100);
                double mul = 0;

                if (currencys.size() > 1) {
                    final C_BaseExchangeRate c_baseExchangeRate = DataHallMrg.getInstance().findC_BaseExchangeRate(GameScript.class.getSimpleName(), currencyId);
                    if (c_baseExchangeRate == null) {
                        continue;
                    }
                    if (c_baseExchangeRate.getExchangeRate() >= 10) {
                        continue;
                    }
                    if (random < 77) {
                        mul = MathUtils.random(5, 10d);
                        gameNote.setBetAmount(BigDecimalUtils.mul(MathUtils.random(100, 200d), 1 / c_baseExchangeRate.getExchangeRate(), 4));
                    } else if (random < 97) {
                        mul = MathUtils.random(10, 20d);
                        gameNote.setBetAmount(BigDecimalUtils.mul(MathUtils.random(300, 500d), 1 / c_baseExchangeRate.getExchangeRate(), 4));
                    } else {
                        mul = MathUtils.random(30, 50d);
                        gameNote.setBetAmount(BigDecimalUtils.mul(MathUtils.random(600, 1000d), 1 / c_baseExchangeRate.getExchangeRate(), 4));
                    }
                } else {
                    if (random < 77) {
                        mul = MathUtils.random(5, 10d);
                        gameNote.setBetAmount(BigDecimalUtils.mul(MathUtils.random(100, 200d), 4));
                    } else if (random < 97) {
                        mul = MathUtils.random(10, 20d);
                        gameNote.setBetAmount(BigDecimalUtils.mul(MathUtils.random(300, 500d), 4));
                    } else {
                        mul = MathUtils.random(30, 50d);
                        gameNote.setBetAmount(BigDecimalUtils.mul(MathUtils.random(600, 1000d), 4));
                    }
                }
                gameNote.setWin(BigDecimalUtils.mul(gameNote.getBetAmount(), mul, 4));
            }

            gameNotes.add(gameNote);
        } while (gameNotes.size() < count);
        return gameNotes;
    }

    private CommonMessage.BetInfo buildGameNote(C_GameApi c_gameApi, GameNote gameNote, int headId) {
        final CommonMessage.BetInfo.Builder betInfo = CommonMessage.BetInfo.newBuilder();
        try {
            betInfo.setGameName(c_gameApi.getGameName())
                    .setPlatformName(c_gameApi.getPlatformName())
                    .setGameId(gameNote.getGameId())
                    .setPlatformId(gameNote.getPlatformId())
                    .setCurrencyId(gameNote.getCurrencyId())
                    .setAmount(gameNote.getBetAmount())
                    .setTime(gameNote.getCreateTime())
                    .setMul(BigDecimalUtils.div(gameNote.getWin(), gameNote.getBetAmount(), 4))
                    .setPayout(BigDecimalUtils.sub(gameNote.getWin(), gameNote.getBetAmount(), 4))
                    .setGameType(gameNote.getGameType())
                    .setNoteId(gameNote.getNoteId() + "")
                    .setPlayerName(gameNote.getPlayerName())
                    .setHeadId(headId);
        } catch (Exception e) {
            LOGGER.error("buildGameNote：{}，betAmount：{}", e, gameNote.getBetAmount());
        }
        return betInfo.build();
    }


    @Override
    public void notifyDailyContestPrizePool() {
        try {
            final List<String> business_noList = DataHallMrg.getInstance().getC_baseHostMerchantMap().values().stream()
                    .map(C_BaseMerchant::getBusiness_no)
                    .distinct()
                    .toList();

            if (business_noList.isEmpty()) {
                return;
            }

            final TcpMessage.ResTcpDailyContestPrizePoolMessage.Builder res = TcpMessage.ResTcpDailyContestPrizePoolMessage.newBuilder();
            res.setMsgID(MIDMessage.MID.ResTcpDailyContestPrizePool_VALUE);
            for (String business_no : business_noList) {
                final boolean functionEnabled = ScriptLoader.getInstance().functionScript("FunctionEnabledScript", (IFunctionEnabledScript script)
                        -> script.functionEnabled(business_no, FunctionEnabled.DailyContest.getType()));
                if (!functionEnabled) {
                    continue;
                }

                final MerchantData merchantData = DataHallMrg.getInstance().findMerchantData(this.getClass().getSimpleName(), business_no);
                if (merchantData == null) {
                    continue;
                }

                final C_DailyContest c_dailyContest = merchantData.getC_dailyContestMap().get(ActivityMrg.DAILY_CONTEST);
                if (c_dailyContest == null) {
                    continue;
                }

                final String prizePools = RedisPoolManager.getInstance().function(jedis ->
                        jedis.sync().get(RedisAllGame.Platform_All_DailyContest_PrizePool.getKey(business_no, TimeUtil.getDateTimeFormat(TimeUtil.YYYYMMDD))));
                double prizePool = BigDecimalUtils.div(Double.parseDouble(StringUtil.isNullOrEmpty(prizePools) ? "0" : prizePools), ActivityMrg.MULTIPLE, 4);
                if (prizePool == 0) {
                    prizePool = c_dailyContest.getInitRewardPool();
                    RedisPoolManager.getInstance().executeAsync(commands ->
                            commands.incrby(RedisAllGame.Platform_All_DailyContest_PrizePool.getKey(business_no, TimeUtil.getDateTimeFormat(TimeUtil.YYYYMMDD)), (long) c_dailyContest.getInitRewardPool() * ActivityMrg.MULTIPLE)
                    );
                }
                res.setBusinessNo(business_no)
                        .setPrizePool(prizePool);

                for (Map.Entry<Long, UserSession> entry : UserTcpMrg.getInstance().getSessionMap().entrySet()) {
                    if (Objects.equals(entry.getValue().getBusiness_no(), business_no)) {
                        MsgUtil.sendClientMsg(entry.getValue().getUserSession(), res.build());
                    }
                }
            }
        } catch (Exception e) {
            LOGGER.error("notifyDailyContestPrizePool", e);
        }
    }


    @Override
    public double calculateValidBettingVolume(Player player, int gameType, int currencyId, double betAmount) {
        final MerchantData merchantData = DataHallMrg.getInstance().findMerchantData(this.getClass().getSimpleName(), player.getBusiness_no());
        if (merchantData == null) {
            return BigDecimalUtils.mul(betAmount, 0, 9);
        }

        final C_CashBack c_cashBack = merchantData.findC_CashBack(this.getClass().getSimpleName(), gameType);
        if (c_cashBack == null) {
            return BigDecimalUtils.mul(betAmount, 0, 9);
        }

        return BigDecimalUtils.mul(betAmount, c_cashBack.getEffectiveRate(), 9);
    }


    @Override
    public void gameEveryMinReset() {
        ScriptLoader.getInstance().consumerScript("RedEnvelopeRainScript",
                IActivityScript::settlement);

        VirtualThreadUtils.execute(() -> {
            ScriptLoader.getInstance().consumerScript("WageredRebatesScript",
                    IActivityScript::insertRobot);

            ScriptLoader.getInstance().consumerScript("WageredRebatesScript",
                    IActivityScript::updateBackstage);
        });
    }

    @Override
    public void gameEveryDayReset() {
        ScriptLoader.getInstance().consumerScript("DailyContestScript",
                IDailyContestScript::settlement);
    }

    @Override
    public void gameEveryWeeklyReset() {
        ScriptLoader.getInstance().consumerScript("WeeklyRaffleScript",
                IWeeklyRaffleScript::settlement);
    }
}
