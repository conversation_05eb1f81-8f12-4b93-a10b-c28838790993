package com.game.scripts.bonus;

import com.game.c_entity.middleplatform.C_BaseCurrency;
import com.game.c_entity.middleplatform.C_BaseExchangeRate;
import com.game.c_entity.merchant.C_VipClub;
import com.game.dao.activity.BonusNoteDao;
import com.game.dao.player.PlayerDao;
import com.game.engine.log.GameLog;
import com.game.engine.math.BigDecimalUtils;
import com.game.engine.script.ScriptLoader;
import com.game.engine.utils.GuidGeneratorUtils;
import com.game.engine.utils.TimeUtil;
import com.game.entity.bonus.BonusNote;
import com.game.entity.player.Player;
import com.game.entity.player.bonus.BonusInfo;
import com.game.entity.player.bonus.BonusInfoFields;
import com.game.entity.player.bonus.BonusProcessInfo;
import com.game.entity.player.stats.Stats;
import com.game.entity.player.vip.VipClub;
import com.game.enums.BonusDetail;
import com.game.enums.TransactionFrom;
import com.game.hall.main.HallServer;
import com.game.hall.mrg.DataHallMrg;
import com.game.hall.mrg.InboxMrg;
import com.game.hall.mrg.MerchantData;
import com.game.hall.script.IBonusScript;
import com.game.hall.script.IInboxScript;
import com.game.manager.EntityDaoMrg;
import io.netty.util.internal.StringUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.time.*;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

public class BonusScript implements IBonusScript {
    private static final Logger LOGGER = LoggerFactory.getLogger(BonusScript.class);

    @Override
    public void initData(Player player) {
        final BonusInfo bonusInfo = player.getBonusInfo();

        final MerchantData merchantData = DataHallMrg.getInstance().findMerchantData(this.getClass().getSimpleName(), player.getBusiness_no());
        if (merchantData == null) {
            return;
        }

        //充电
        final String chargingBenefitsOpen = merchantData.findC_GlobalValue(this.getClass().getSimpleName(), "chargingBenefitsOpen");
        if (!StringUtil.isNullOrEmpty(chargingBenefitsOpen) && Boolean.parseBoolean(chargingBenefitsOpen)
                && bonusInfo.getRechargeEndTime() == 0) {
            bonusInfo.setRechargeEndTime(TimeUtil.getDelayedEndTimestamp(7));
            EntityDaoMrg.getInstance().getDao(PlayerDao.class)
                    .bonusInfoDao.updateBonusInfo(player.getPlayerId(), BonusInfoFields.rechargeEndTime, bonusInfo.getRechargeEndTime());
        }

        //周返水
        final String weeklyCashBackOpen = merchantData.findC_GlobalValue(this.getClass().getSimpleName(), "weeklyCashBackOpen");
        if (!StringUtil.isNullOrEmpty(weeklyCashBackOpen) && Boolean.parseBoolean(weeklyCashBackOpen) &&
                bonusInfo.getWeeklyEndTime() == 0) {
            bonusInfo.setWeeklyEndTime(TimeUtil.nextDayOfWeek(DayOfWeek.FRIDAY));
            EntityDaoMrg.getInstance().getDao(PlayerDao.class)
                    .bonusInfoDao.updateBonusInfo(player.getPlayerId(), BonusInfoFields.weeklyEndTime, bonusInfo.getWeeklyEndTime());
        }

        //月返水
        final String monthlyCashBackOpen = merchantData.findC_GlobalValue(this.getClass().getSimpleName(), "monthlyCashBackOpen");
        if (!StringUtil.isNullOrEmpty(monthlyCashBackOpen) && Boolean.parseBoolean(monthlyCashBackOpen) &&
                bonusInfo.getMonthlyEndTime() == 0) {
            bonusInfo.setMonthlyEndTime(TimeUtil.getDelayedEndTimestamp(15));
            EntityDaoMrg.getInstance().getDao(PlayerDao.class)
                    .bonusInfoDao.updateBonusInfo(player.getPlayerId(), BonusInfoFields.monthlyEndTime, bonusInfo.getMonthlyEndTime());
        }
    }

    @Override
    public void statsWager(Player player, int currencyId, double validBet) {
        final MerchantData merchantData = DataHallMrg.getInstance().findMerchantData(this.getClass().getSimpleName(), player.getBusiness_no());
        if (merchantData == null) {
            return;
        }

        final VipClub vipClub = player.getVipClub();
        final BonusInfo bonusInfo = player.getBonusInfo();

        final String chargingBenefitsOpen = merchantData.findC_GlobalValue(this.getClass().getSimpleName(), "chargingBenefitsOpen");
        final String rechargeLimit = merchantData.findC_GlobalValue(this.getClass().getSimpleName(), "rechargeLimit");
        final int vipRechargeLimit = Integer.parseInt(StringUtil.isNullOrEmpty(rechargeLimit) ? "0" : rechargeLimit);
        if (!StringUtil.isNullOrEmpty(chargingBenefitsOpen) && Boolean.parseBoolean(chargingBenefitsOpen) &&
                vipClub.getVipLevel() >= vipRechargeLimit) {
            final BonusProcessInfo bonusProcessInfo = bonusInfo.getBonusProcessInfo(BonusDetail.Recharge.getType());
            bonusProcessInfo.incWager(currencyId, validBet);
        }

        final String weeklyCashBackOpen = merchantData.findC_GlobalValue(this.getClass().getSimpleName(), "weeklyCashBackOpen");
        final String weeklyCashBackLimit = merchantData.findC_GlobalValue(this.getClass().getSimpleName(), "weeklyCashbackLimit");
        final int vipWeeklyLimit = Integer.parseInt(StringUtil.isNullOrEmpty(weeklyCashBackLimit) ? "0" : weeklyCashBackLimit);
        if (!StringUtil.isNullOrEmpty(weeklyCashBackOpen) && Boolean.parseBoolean(weeklyCashBackOpen) &&
                vipClub.getVipLevel() >= vipWeeklyLimit) {
            final BonusProcessInfo bonusProcessInfo = bonusInfo.getBonusProcessInfo(BonusDetail.WeeklyCashBack.getType());
            bonusProcessInfo.incWager(currencyId, validBet);
        }

        final String monthlyCashBackOpen = merchantData.findC_GlobalValue(this.getClass().getSimpleName(), "monthlyCashBackOpen");
        final String monthlyCashBackLimit = merchantData.findC_GlobalValue(this.getClass().getSimpleName(), "monthlyCashbackLimit");
        final int vipMonthlyLimit = Integer.parseInt(StringUtil.isNullOrEmpty(monthlyCashBackLimit) ? "0" : monthlyCashBackLimit);
        if (!StringUtil.isNullOrEmpty(monthlyCashBackOpen) && Boolean.parseBoolean(monthlyCashBackOpen) &&
                vipClub.getVipLevel() >= vipMonthlyLimit) {
            final BonusProcessInfo bonusProcessInfo = bonusInfo.getBonusProcessInfo(BonusDetail.MonthlyCashBack.getType());
            bonusProcessInfo.incWager(currencyId, validBet);
        }
    }

    @Override
    public void settlementBonus(Player player) {
        final VipClub vipClub = player.getVipClub();
        if (vipClub.getVipLevel() == 0) {
            return;
        }

        final MerchantData merchantData = DataHallMrg.getInstance().findMerchantData(this.getClass().getSimpleName(), player.getBusiness_no());
        if (merchantData == null) {
            return;
        }

        final boolean isManyCurrency = merchantData.getC_currencyMap().size() > 1;

        final C_VipClub c_vipClub = merchantData.findC_VipClub(this.getClass().getSimpleName(), vipClub.getVipLevel());
        if (c_vipClub == null) {
            return;
        }

        final String chargingBenefitsOpen = merchantData.findC_GlobalValue(this.getClass().getSimpleName(), "chargingBenefitsOpen");
        final BonusInfo bonusInfo = player.getBonusInfo();
        //充电
        final long time = TimeUtil.currentTimeMillis();
        if (!StringUtil.isNullOrEmpty(chargingBenefitsOpen) && Boolean.parseBoolean(chargingBenefitsOpen)
                && bonusInfo.getRechargeEndTime() != 0 && time >= bonusInfo.getRechargeEndTime()) {
            final BonusProcessInfo bonusProcessInfo = bonusInfo.getBonusProcessInfo(BonusDetail.Recharge.getType());
            //需要清空上次奖励
            bonusInfo.setRechargeEndTime(0);
            bonusProcessInfo.getRewardMap().clear();

            final String rechargeLimit = merchantData.findC_GlobalValue(this.getClass().getSimpleName(), "rechargeLimit");
            final int vipRechargeLimit = Integer.parseInt(StringUtil.isNullOrEmpty(rechargeLimit) ? "0" : rechargeLimit);
            if (vipClub.getVipLevel() >= vipRechargeLimit) {
                double totalWager = 0;
                for (Map.Entry<Integer, Double> entry : bonusProcessInfo.getWagerMap().entrySet()) {
                    if (isManyCurrency) {
                        final C_BaseExchangeRate c_baseExchangeRate = DataHallMrg.getInstance().findC_BaseExchangeRate(this.getClass().getSimpleName(), entry.getKey());
                        if (c_baseExchangeRate == null) {
                            continue;
                        }
                        totalWager = BigDecimalUtils.add(totalWager, BigDecimalUtils.mul(entry.getValue(), c_baseExchangeRate.getExchangeRate(), 9), 9);
                    } else {
                        totalWager = BigDecimalUtils.add(totalWager, entry.getValue(), 9);
                    }
                }

                final C_VipClub.Tire tire = c_vipClub.getTire(totalWager);
                if (tire != null) {
                    for (Map.Entry<Integer, Double> entry : bonusProcessInfo.getWagerMap().entrySet()) {
                        final double value = BigDecimalUtils.mul(BigDecimalUtils.mul(entry.getValue(), 0.01, 9), tire.getRate(), 9);
                        bonusProcessInfo.incReward(entry.getKey(), value);
                    }
                } else {
                    LOGGER.warn("playerId：{}，recharge，totalWager：{}", player.getPlayerId(), totalWager);
                }
                bonusProcessInfo.getWagerMap().clear();
            }
        }

        //周返水
        final String weeklyCashBackOpen = merchantData.findC_GlobalValue(this.getClass().getSimpleName(), "weeklyCashBackOpen");
        if (!StringUtil.isNullOrEmpty(weeklyCashBackOpen) && Boolean.parseBoolean(weeklyCashBackOpen)
                && bonusInfo.getWeeklyEndTime() != 0 && time >= bonusInfo.getWeeklyEndTime()) {
            final BonusProcessInfo bonusProcessInfo = bonusInfo.getBonusProcessInfo(BonusDetail.WeeklyCashBack.getType());
            //需要清空上次奖励
            bonusInfo.setWeeklyEndTime(0);
            bonusProcessInfo.getRewardMap().clear();

            final String weeklyCashBackLimit = merchantData.findC_GlobalValue(this.getClass().getSimpleName(), "weeklyCashbackLimit");
            final int vipWeeklyLimit = Integer.parseInt(StringUtil.isNullOrEmpty(weeklyCashBackLimit) ? "0" : weeklyCashBackLimit);
            if (vipClub.getVipLevel() >= vipWeeklyLimit) {
                double totalWager = 0;
                for (Map.Entry<Integer, Double> entry : bonusProcessInfo.getWagerMap().entrySet()) {
                    if (isManyCurrency) {
                        final C_BaseExchangeRate c_baseExchangeRate = DataHallMrg.getInstance().findC_BaseExchangeRate(this.getClass().getSimpleName(), entry.getKey());
                        if (c_baseExchangeRate == null) {
                            continue;
                        }
                        totalWager = BigDecimalUtils.add(totalWager, BigDecimalUtils.mul(entry.getValue(), c_baseExchangeRate.getExchangeRate(), 9), 9);
                    } else {
                        totalWager = BigDecimalUtils.add(totalWager, entry.getValue(), 9);
                    }
                }

                final String weeklyWagerRange = c_vipClub.getWeeklyWagerRange();
                if (StringUtil.isNullOrEmpty(weeklyWagerRange)) {
                    return;
                }

                final String[] jh = weeklyWagerRange.split("-");
                if (jh.length == 2) {
                    final double min = Double.parseDouble(jh[0]);
                    final double max = Double.parseDouble(jh[1]);
                    if (totalWager >= min) {
                        if (max != 0 && totalWager >= max) {
                            totalWager = max;
                            for (Map.Entry<Integer, Double> entry : bonusProcessInfo.getWagerMap().entrySet()) {
                                double value = 0;
                                if (isManyCurrency) {
                                    final C_BaseExchangeRate c_baseExchangeRate = DataHallMrg.getInstance().findC_BaseExchangeRate(this.getClass().getSimpleName(), entry.getKey());
                                    if (c_baseExchangeRate == null) {
                                        continue;
                                    }
                                    final double a = BigDecimalUtils.mul(entry.getValue(), c_baseExchangeRate.getExchangeRate(), 9);
                                    final double wagerUsd = BigDecimalUtils.mul(BigDecimalUtils.div(a, totalWager, 9), totalWager, 9);

                                    final double exchange = BigDecimalUtils.div(1, c_baseExchangeRate.getExchangeRate(), 9);
                                    final double b = BigDecimalUtils.mul(exchange, wagerUsd, 9);
                                    value = BigDecimalUtils.mul(b, c_vipClub.getWeeklyCashBackRate(), 9);
                                } else {
                                    final double wagerUsd = BigDecimalUtils.mul(BigDecimalUtils.div(entry.getValue(), totalWager, 9), totalWager, 9);
                                    value = BigDecimalUtils.mul(wagerUsd, c_vipClub.getWeeklyCashBackRate(), 9);
                                }
                                bonusProcessInfo.incReward(entry.getKey(), value);
                            }
                        } else {
                            for (Map.Entry<Integer, Double> entry : bonusProcessInfo.getWagerMap().entrySet()) {
                                final double value = BigDecimalUtils.mul(entry.getValue(), c_vipClub.getWeeklyCashBackRate(), 9);
                                bonusProcessInfo.incReward(entry.getKey(), value);
                            }
                        }
                    }
                }
                bonusProcessInfo.getWagerMap().clear();
            }
        }

        //月返水
        final String monthlyCashBackOpen = merchantData.findC_GlobalValue(this.getClass().getSimpleName(), "monthlyCashBackOpen");
        if (!StringUtil.isNullOrEmpty(monthlyCashBackOpen) && Boolean.parseBoolean(monthlyCashBackOpen) &&
                bonusInfo.getMonthlyEndTime() != 0 && time >= bonusInfo.getMonthlyEndTime()) {
            final BonusProcessInfo bonusProcessInfo = bonusInfo.getBonusProcessInfo(BonusDetail.MonthlyCashBack.getType());
            //需要清空上次奖励
            bonusInfo.setMonthlyEndTime(0);
            bonusProcessInfo.getRewardMap().clear();

            final String monthlyCashBackLimit = merchantData.findC_GlobalValue(this.getClass().getSimpleName(), "monthlyCashbackLimit");
            final int vipMonthlyLimit = Integer.parseInt(StringUtil.isNullOrEmpty(monthlyCashBackLimit) ? "0" : monthlyCashBackLimit);
            if (vipClub.getVipLevel() >= vipMonthlyLimit) {
                double totalWager = 0;
                for (Map.Entry<Integer, Double> entry : bonusProcessInfo.getWagerMap().entrySet()) {
                    if (isManyCurrency) {
                        final C_BaseExchangeRate c_baseExchangeRate = DataHallMrg.getInstance().findC_BaseExchangeRate(this.getClass().getSimpleName(), entry.getKey());
                        if (c_baseExchangeRate == null) {
                            continue;
                        }
                        totalWager = BigDecimalUtils.add(totalWager, BigDecimalUtils.mul(entry.getValue(), c_baseExchangeRate.getExchangeRate(), 9), 9);
                    } else {
                        totalWager = BigDecimalUtils.add(totalWager, entry.getValue(), 9);
                    }
                }

                final String monthlyWagerRange = c_vipClub.getMonthlyWagerRange();
                final String[] jh = monthlyWagerRange.split("-");
                if (jh.length == 2) {
                    final double min = Double.parseDouble(jh[0]);
                    final double max = Double.parseDouble(jh[1]);
                    if (totalWager >= min) {
                        if (max != 0 && totalWager >= max) {
                            totalWager = max;
                            for (Map.Entry<Integer, Double> entry : bonusProcessInfo.getWagerMap().entrySet()) {
                                double value = 0;
                                if (isManyCurrency) {
                                    final C_BaseExchangeRate c_baseExchangeRate = DataHallMrg.getInstance().findC_BaseExchangeRate(this.getClass().getSimpleName(), entry.getKey());
                                    if (c_baseExchangeRate == null) {
                                        continue;
                                    }
                                    final double a = BigDecimalUtils.mul(entry.getValue(), c_baseExchangeRate.getExchangeRate(), 9);
                                    final double wagerUsd = BigDecimalUtils.mul(BigDecimalUtils.div(a, totalWager, 9), totalWager, 9);
                                    final double exchange = BigDecimalUtils.div(1, c_baseExchangeRate.getExchangeRate(), 9);
                                    final double b = BigDecimalUtils.mul(exchange, wagerUsd, 9);
                                    value = BigDecimalUtils.mul(b, c_vipClub.getMonthlyCashBackRate(), 9);
                                } else {
                                    final double wagerUsd = BigDecimalUtils.mul(BigDecimalUtils.div(entry.getValue(), totalWager, 9), totalWager, 9);
                                    value = BigDecimalUtils.mul(wagerUsd, c_vipClub.getMonthlyCashBackRate(), 9);
                                }
                                bonusProcessInfo.incReward(entry.getKey(), value);
                            }
                        } else {
                            for (Map.Entry<Integer, Double> entry : bonusProcessInfo.getWagerMap().entrySet()) {
                                final double value = BigDecimalUtils.mul(entry.getValue(), c_vipClub.getMonthlyCashBackRate(), 9);
                                bonusProcessInfo.incReward(entry.getKey(), value);
                            }
                        }
                    }
                }
                bonusProcessInfo.getWagerMap().clear();
            }
        }
        initData(player);
    }

    @Override
    public void addBonusNote(TransactionFrom transaction, Player player, int currencyId, double amount) {
        if (amount <= 0) {
            return;
        }
        final BonusNote bonusNote = new BonusNote();
        bonusNote.setPlayerId(player.getPlayerId());
        bonusNote.setBusiness_no(player.getBusiness_no());
        bonusNote.setBonusType(transaction.getType() / 100);
        bonusNote.setBonusSubType(transaction.getType());
        bonusNote.setCurrencyId(currencyId);
        bonusNote.setAmount(amount);
        bonusNote.setBalance(player.getCurrencyMap().getOrDefault(currencyId, 0d));

        EntityDaoMrg.getInstance().getDao(BonusNoteDao.class).insert(bonusNote);

        {
            //TODO邮件
            final C_BaseCurrency c_baseCurrency = DataHallMrg.getInstance().findC_BaseCurrency(this.getClass().getSimpleName(), currencyId);
            final List<String> params = new ArrayList<>();
            params.add(player.getPlayerName());
            params.add(TransactionFrom.valuesOf(transaction.getType()).getName());
            if (c_baseCurrency != null) {
                params.add(c_baseCurrency.getSymbol());
            }
            params.add(amount + "");
            ScriptLoader.getInstance().consumerScript("InboxScript", (IInboxScript script) ->
                    script.sendInboxMail(player, InboxMrg.ACTIVITY_REWARD, params));
        }

        //TODO 系统赠送
        final Stats stats = player.getStats(currencyId);
        stats.incSystemGift(amount);

        final GameLog playerActivityLog = new GameLog("platform_playerBonusLog");
        playerActivityLog.append("number", GuidGeneratorUtils.generateOrderId())
                .append("business_no", player.getBusiness_no())
                .append("site", player.getWebSite())
                .append("playerId", player.getPlayerId())
                .append("playerName", player.getPlayerName())
                .append("transaction", transaction.getType())
                .append("language", player.getLanguage())
                .append("agentId", player.getAgentId())
                .append("channelId", player.getChannelId())
                .append("currencyId", currencyId)
                .append("reward", amount)
                .append("region", player.getRegisterRegion())
                .append("logTime", TimeUtil.currentTimeMillis());
        HallServer.getInstance().getLogProducerMrg().send(playerActivityLog);
    }

}
