package com.game.handler.tcp.quest;

import com.game.c_entity.merchant.C_Quest;
import com.game.dao.quest.QuestNoteDao;
import com.game.engine.io.handler.TcpHandler;
import com.game.engine.script.IHandlerEntity;
import com.game.engine.util.misc.Tuple2;
import com.game.engine.utils.MsgUtil;
import com.game.engine.utils.TimeUtil;
import com.game.entity.player.Player;
import com.game.entity.player.quest.QuestNote;
import com.game.enums.ErrorCode;
import com.game.hall.mrg.CommonMrg;
import com.game.hall.mrg.player.PlayerMrg;
import com.game.hall.mrg.quest.QuestMrg;
import com.game.manager.EntityDaoMrg;
import com.proto.MIDMessage;
import com.proto.QuestMessage;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.time.DayOfWeek;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.List;

@IHandlerEntity(mid = MIDMessage.MID.ReqPreviousQuestsData_VALUE, msg = QuestMessage.ReqPreviousQuestsDataMessage.class)
public class ReqPreviousQuestsDataHandler extends TcpHandler {
    private static final Logger LOGGER = LoggerFactory.getLogger(ReqPreviousQuestsDataHandler.class);

    @Override
    public void run() {
        final QuestMessage.ResPreviousQuestsDataMessage.Builder res = QuestMessage.ResPreviousQuestsDataMessage.newBuilder();
        res.setMsgID(MIDMessage.MID.ResPreviousQuestsData_VALUE);
        try {
            final Player player = PlayerMrg.getInstance().getOnlinePlayer(pid, this.getClass().getSimpleName());
            if (player == null) {
                res.setError(ErrorCode.Player_Offline.getCode());
                MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                return;
            }
            final QuestMessage.ReqPreviousQuestsDataMessage req = (QuestMessage.ReqPreviousQuestsDataMessage) message;
            final int page = req.getPage();
            final int pageSize = req.getPageSize();

            final int skip = (page - 1) * pageSize;
            final long start = lastWeeklyStartTime();
            final long end = localWeeklyEndTime();
            final Tuple2<Integer, List<QuestNote>> tuple2 = EntityDaoMrg.getInstance().getDao(QuestNoteDao.class).loadQuestNote(pid, start, end, skip, pageSize);
            final int total = tuple2.getFirst();
            final List<QuestNote> questNotes = tuple2.getSecond();
            for (QuestNote questNote : questNotes) {
                final C_Quest c_quest = QuestMrg.getInstance().getQuest(player, questNote.getQuestId());
                if (c_quest == null) {
                    continue;
                }
                final QuestMessage.QuestInfo questInfo = buildQuestInfo(player, questNote, c_quest);
                if (questInfo != null) {
                    res.addQuestList(questInfo);
                }
            }
            res.setPage(page)
                    .setPageSize(pageSize)
                    .setTotal(total)
                    .setTotalPage(CommonMrg.totalPage(total, pageSize))
                    .setClearRewardTime(TimeUtil.getDayOfWeekEndTimestamp(DayOfWeek.SUNDAY));
            MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
        } catch (Exception e) {
            LOGGER.error("ReqPreviousQuestsDataHandler", e);
            res.setError(ErrorCode.Internal_Server_Error.getCode());
            MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
        }
    }

    private QuestMessage.QuestInfo buildQuestInfo(Player player, QuestNote questNote, C_Quest c_quest) {
        final C_Quest.DescInfo descInfo = c_quest.getDescInfoMap().get(player.getLanguage());
        if (descInfo == null) {
            return null;
        }

        final QuestMessage.QuestInfo.Builder questInfo = QuestMessage.QuestInfo.newBuilder();
        questInfo.setUniqueId(questNote.getUniqueId() + "")
                .setQuestId(questNote.getQuestId())
                .setState(questNote.getState())
                .setFinishedTime(questNote.getFinishedTime())

                .setQuestType(c_quest.getQuestType())
                .setGoalType(c_quest.getGoalType())
                .setQuestName(descInfo.getQuestName())
                .setDesc(descInfo.getDesc())
                .setRewards(QuestMrg.getInstance().buildDItemShow(c_quest.getRewardCurrency().currencyId, c_quest.getRewardCurrency().amount));
        return questInfo.build();
    }


    private static long localWeeklyEndTime() {
        LocalDate today = LocalDate.now();

        // 获取本周的开始和结束时间
        LocalDate startOfWeek = today.with(DayOfWeek.MONDAY);
        LocalDate endOfWeek = startOfWeek.plusDays(6);
        return endOfWeek.atTime(23, 59, 59, 999_999_999).atZone(ZoneId.systemDefault()).toInstant().toEpochMilli();
    }

    private static long lastWeeklyStartTime() {
        LocalDate today = LocalDate.now();

        // 获取本周的开始和结束时间
        LocalDate startOfWeek = today.with(java.time.DayOfWeek.MONDAY);

        // 获取上周的开始和结束时间
        LocalDate startOfLastWeek = startOfWeek.minusWeeks(1);
        return startOfLastWeek.atStartOfDay(ZoneId.systemDefault()).toInstant().toEpochMilli();
    }

}
