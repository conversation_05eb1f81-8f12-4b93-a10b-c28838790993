package com.game.handler.tcp.billing;

import com.game.c_entity.merchant.C_PlatformWithdraw;
import com.game.c_entity.middleplatform.C_BasePaymentMethod;
import com.game.engine.io.handler.TcpHandler;
import com.game.engine.script.IHandlerEntity;
import com.game.engine.utils.MsgUtil;
import com.game.entity.player.PayInfo;
import com.game.entity.player.Player;
import com.game.entity.player.WithdrawAccount;
import com.game.enums.ErrorCode;
import com.game.hall.mrg.DataHallMrg;
import com.game.hall.mrg.MerchantData;
import com.game.hall.mrg.player.PlayerMrg;
import com.proto.BillingMessage;
import com.proto.MIDMessage;
import io.netty.util.internal.StringUtil;
import it.unimi.dsi.fastutil.objects.Object2ObjectMap;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.Function;
import java.util.function.Predicate;

@IHandlerEntity(mid = MIDMessage.MID.ReqPaymentMethodsData_VALUE, msg = BillingMessage.ReqPaymentMethodsDataMessage.class)
public class ReqPaymentMethodsDataHandler extends TcpHandler {
    private static final Logger LOGGER = LoggerFactory.getLogger(ReqPaymentMethodsDataHandler.class);

    @Override
    public void run() {
        final BillingMessage.ResPaymentMethodsDataMessage.Builder res = BillingMessage.ResPaymentMethodsDataMessage.newBuilder();
        res.setMsgID(MIDMessage.MID.ResPaymentMethodsData_VALUE);
        try {
            final Player player = PlayerMrg.getInstance().getOnlinePlayer(pid, this.getClass().getSimpleName());
            if (player == null) {
                res.setError(ErrorCode.Player_Offline.getCode());
                MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                return;
            }

            final MerchantData merchantData = DataHallMrg.getInstance().findMerchantData(this.getClass().getSimpleName(), player.getBusiness_no());
            if (merchantData == null) {
                res.setError(ErrorCode.Data_Error.getCode());
                MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                return;
            }

            for (final Map.Entry<Integer, Map<String, C_PlatformWithdraw>> entry : merchantData.getC_platformWithdraw_fiatMap().entrySet()) {
                final Map<String, C_PlatformWithdraw> c_platformWithdrawFiatMap = entry.getValue();
                final List<C_PlatformWithdraw> c_platformWithdrawFiats = c_platformWithdrawFiatMap.values().stream()
                        .filter(distinctByKey(C_PlatformWithdraw::getPaymentMethod)).toList();
                for (C_PlatformWithdraw c_platformWithdraw : c_platformWithdrawFiats) {
                    res.addFiatPaymentList(buildFiatPayment(player, c_platformWithdraw));
                }
            }

            for (Map.Entry<Integer, List<C_PlatformWithdraw>> entry : merchantData.getC_platformWithdraw_cryptoMap().entrySet()) {
                for (C_PlatformWithdraw c_platformWithdraw : entry.getValue()) {
                    res.addCryptoPaymentList(buildCryptoPayment(player, c_platformWithdraw));
                }
            }

            MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
        } catch (Exception e) {
            LOGGER.error("ReqPaymentMethodsDataHandler", e);
            res.setError(ErrorCode.Internal_Server_Error.getCode());
            MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
        }
    }

    private BillingMessage.FiatPayment buildFiatPayment(Player player, C_PlatformWithdraw c_platformWithdraw) {
        final BillingMessage.FiatPayment.Builder fiatPayment = BillingMessage.FiatPayment.newBuilder();
        fiatPayment.setCurrencyId(c_platformWithdraw.getCurrencyId())
                .setPaymentMethod(c_platformWithdraw.getPaymentMethod());
        for (Map.Entry<String, C_PlatformWithdraw.WithdrawInfo> entry : c_platformWithdraw.getWithdrawInfoMap().entrySet()) {
            fiatPayment.addWithdrawInfos(buildWithdrawInfo(entry.getKey(), entry.getValue()));
        }
        final String currencyIdChannel = c_platformWithdraw.getCurrencyId() + "_" + c_platformWithdraw.getPaymentMethod();
        final WithdrawAccount withdrawAccount = player.getWithdrawAccountMap().get(currencyIdChannel);
        if (withdrawAccount != null) {
            for (PayInfo payInfo : withdrawAccount.getPayInfoMap().values()) {
                fiatPayment.addWithdrawAccount(buildWithdrawAccount(payInfo));
            }
        }
        final C_BasePaymentMethod c_basePaymentMethod = DataHallMrg.getInstance().findC_BasePaymentMethod(this.getClass().getSimpleName(), c_platformWithdraw.getPaymentMethod());
        if (c_basePaymentMethod != null) {
            fiatPayment.setPaymentMethodName(c_basePaymentMethod.getName());
        }
        return fiatPayment.build();
    }

    private BillingMessage.CryptoPayment buildCryptoPayment(Player player, C_PlatformWithdraw c_platformWithdraw) {
        final BillingMessage.CryptoPayment.Builder cryptoPayment = BillingMessage.CryptoPayment.newBuilder();
        cryptoPayment.setCurrencyId(c_platformWithdraw.getCurrencyId())
                .setNetwork(c_platformWithdraw.getNetwork());
        final String currencyIdChannel = c_platformWithdraw.getCurrencyId() + "_" + c_platformWithdraw.getPaymentMethod();
        final WithdrawAccount withdrawAccount = player.getWithdrawAccountMap().get(currencyIdChannel);
        if (withdrawAccount != null) {
            for (PayInfo payInfo : withdrawAccount.getPayInfoMap().values()) {
                cryptoPayment.addWithdrawAccount(buildWithdrawAccount(payInfo));
            }
        }
        return cryptoPayment.build();
    }

    private BillingMessage.WithdrawInfo buildWithdrawInfo(String filed, C_PlatformWithdraw.WithdrawInfo withdrawInfo) {
        return BillingMessage.WithdrawInfo.newBuilder()
                .setField(filed)
                .addAllParams(withdrawInfo.getParams())
                .setType(withdrawInfo.getType())
                .build();
    }

    private BillingMessage.WithdrawAccount buildWithdrawAccount(PayInfo payInfo) {
        return BillingMessage.WithdrawAccount.newBuilder()
                .setWithdrawAccountId(payInfo.getPayId() + "")
                .setExtend0(payInfo.getExtend())
                .setExtend1(payInfo.getExtend_1())
                .setExtend2(payInfo.getExtend_2())
                .setExtend3(StringUtil.isNullOrEmpty(payInfo.getExtend_3()) ? "" : payInfo.getExtend_3())
                .build();
    }

    private static <T> Predicate<T> distinctByKey(Function<? super T, ?> keyExtractor) {
        final Set<Object> seen = ConcurrentHashMap.newKeySet();
        return t -> seen.add(keyExtractor.apply(t));
    }
}
