package com.game.handler.tcp.game;

import com.game.c_entity.middleplatform.C_BaseMerchant;
import com.game.engine.io.handler.TcpHandler;
import com.game.engine.io.redis.RedisPoolManager;
import com.game.engine.math.BigDecimalUtils;
import com.game.engine.script.IHandlerEntity;
import com.game.engine.utils.JsonUtils;
import com.game.engine.utils.MsgUtil;
import com.game.engine.utils.Symbol;
import com.game.entity.WinGameNote;
import com.game.enums.ErrorCode;
import com.game.enums.redis.RedisRanking;
import com.game.hall.mrg.DataHallMrg;
import com.proto.HallMessage;
import com.proto.MIDMessage;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;

@IHandlerEntity(mid = MIDMessage.MID.ReqGetRankData_VALUE, msg = HallMessage.ReqGetRankDataMessage.class)
public class ReqGetRankDataHandler extends TcpHandler {
    private static final Logger LOGGER = LoggerFactory.getLogger(ReqGetRankDataHandler.class);

    @Override
    public void run() {
        final HallMessage.ResGetRankDataMessage.Builder res = HallMessage.ResGetRankDataMessage.newBuilder();
        res.setMsgID(MIDMessage.MID.ResGetRankData_VALUE);
        try {
            final HallMessage.ReqGetRankDataMessage req = (HallMessage.ReqGetRankDataMessage) getMessage();
            final String host = req.getHost();
            final int gameId = req.getGameId();

            final C_BaseMerchant c_baseMerchant = DataHallMrg.getInstance().findC_BaseHostMerchant(this.getClass().getSimpleName(), host);
            if (c_baseMerchant == null) {
                return;
            }

            final String business_no = c_baseMerchant.getBusiness_no();

            final List<String> binWin_players = RedisPoolManager.getInstance().function(jedis ->
                    jedis.sync().zrevrange(RedisRanking.RANKING_BIG_WIN.getKey(business_no, gameId), 0, 2));

            if (binWin_players != null) {
                for (String member : binWin_players) {
                    final String[] xh = member.split(Symbol.XIAHUAXIAN_REG, 2);
                    final WinGameNote gameNote = JsonUtils.readFromJson(xh[1], WinGameNote.class);
                    if (gameNote.getValidBets() == 0) {
                        continue;
                    }
                    res.addBigWin(buildRankInfo(gameNote));
                }
            }

            final List<String> luckyWin_players = RedisPoolManager.getInstance().function(jedis ->
                    jedis.sync().zrevrange(RedisRanking.RANKING_LUCKY_WIN.getKey(business_no, gameId), 0, 2));

            if (luckyWin_players != null) {
                for (String member : luckyWin_players) {
                    final String[] xh = member.split(Symbol.XIAHUAXIAN_REG, 2);
                    final WinGameNote gameNote = JsonUtils.readFromJson(xh[1], WinGameNote.class);
                    if (gameNote.getValidBets() == 0) {
                        continue;
                    }
                    res.addLuckyWin(buildRankInfo(gameNote));
                }
            }

            MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
        } catch (Exception e) {
            LOGGER.error("ReqGetRankDataHandler", e);
            res.setError(ErrorCode.Internal_Server_Error.getCode());
            MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
        }
    }

    private HallMessage.RankInfo buildRankInfo(WinGameNote gameNote) {
        final HallMessage.RankInfo.Builder rankInfo = HallMessage.RankInfo.newBuilder()
                .setBetAmount(gameNote.getValidBets())
                .setCurrencyId(gameNote.getCurrencyId())
                .setGameId(gameNote.getGameId())
                .setPlayerName(gameNote.getPlayerName())
                .setTime(gameNote.getCreateTime())
                .setWinAmount(gameNote.getWin())
                .setPayout(BigDecimalUtils.div(gameNote.getWin(), gameNote.getValidBets(), 4));
        return rankInfo.build();
    }

}
