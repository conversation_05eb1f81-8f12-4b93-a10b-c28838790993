package com.game.handler.tcp.account;

import com.game.dao.account.AccountDao;
import com.game.dao.player.PlayerDao;
import com.game.engine.io.handler.TcpHandler;
import com.game.engine.io.redis.RedisPoolManager;
import com.game.engine.script.IHandlerEntity;
import com.game.engine.utils.MsgUtil;
import com.game.entity.account.Account;
import com.game.entity.account.email.Email;
import com.game.entity.account.phone.Phone;
import com.game.entity.player.Player;
import com.game.entity.player.PlayerFields;
import com.game.enums.ErrorCode;
import com.game.enums.ThreeParty;
import com.game.enums.VerifyCode;
import com.game.enums.redis.RedisLogin;
import com.game.hall.mrg.player.PlayerMrg;
import com.game.manager.EntityDaoMrg;
import com.game.utils.VirtualThreadUtils;
import com.proto.HallMessage;
import com.proto.MIDMessage;
import io.netty.util.internal.StringUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.mongodb.core.query.Update;

import java.util.Objects;

@IHandlerEntity(mid = MIDMessage.MID.ReqBindAccount_VALUE, msg = HallMessage.ReqBindAccountMessage.class)
public class ReqBindAccountHandler extends TcpHandler {
    private static final Logger LOGGER = LoggerFactory.getLogger(ReqBindAccountHandler.class);

    @Override
    public void run() {
        final HallMessage.ResBindAccountMessage.Builder res = HallMessage.ResBindAccountMessage.newBuilder();
        res.setMsgID(MIDMessage.MID.ResBindAccount_VALUE);

        try {
            final Player player = PlayerMrg.getInstance().getOnlinePlayer(pid, this.getClass().getSimpleName());
            if (player == null) {
                res.setError(ErrorCode.Player_Offline.getCode());
                MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                return;
            }

            final HallMessage.ReqBindAccountMessage req = (HallMessage.ReqBindAccountMessage) getMessage();
            final int threeParty = req.getThreeParty();
            final String areaCode = req.getAreaCode().trim();
            final String reqAccount = req.getAccount().trim();
            final String verifyCode = req.getVerifyCode().trim();

            String ac;
            if (threeParty == ThreeParty.Phone.getThreeParty()) {
                ac = areaCode + "-" + reqAccount;
            } else {
                ac = reqAccount;
            }

            final String finalAc = ac;

            String oldAc;
            if (threeParty == ThreeParty.Phone.getThreeParty()) {
                oldAc = player.getAreaCode() + "-" + player.getPhone();
            } else {
                oldAc = player.getEmail();
            }

            if (!Objects.equals(finalAc, oldAc)) {
                final String accountId = RedisPoolManager.getInstance().function(jedis ->
                        jedis.sync().hget(RedisLogin.Platform_LG_Map_AccountID.getKey(player.getBusiness_no(), threeParty), finalAc));
                if (!StringUtil.isNullOrEmpty(accountId)) {
                    LOGGER.warn("account：{}，already registered", finalAc);
                    res.setError(ErrorCode.Account_AlreadyExists.getCode());
                    MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                    return;
                }
            }

            //邮箱验证码
            final String code = RedisPoolManager.getInstance().function(jedis ->
                    jedis.sync().get(RedisLogin.Platform_LG_Account_VerifyCode.getKey(player.getBusiness_no(), VerifyCode.bind.getType(), finalAc)));
            if (StringUtil.isNullOrEmpty(verifyCode) || !verifyCode.equals(code)) {
                LOGGER.warn("account：{}，verifyCode，{}-{}，", finalAc, code, verifyCode);
                res.setError(ErrorCode.VerifyCode_Error.getCode());
                MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                return;
            }

            final Account account = PlayerMrg.getInstance().findDbAccount(pid);
            if (account == null) {
                res.setError(ErrorCode.Account_NotExist.getCode());
                MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                return;
            }

            if (threeParty == ThreeParty.Email.getThreeParty()) {
                final Email emailInfo = account.getEmailInfo();
                emailInfo.setEmail(finalAc);
                EntityDaoMrg.getInstance().getDao(AccountDao.class).updateEmail(account.getAccountId(), emailInfo);

                player.setEmailBind(true);
                player.setEmail(reqAccount);
                final Update update = new Update();
                update.set(PlayerFields.emailBind, player.isEmailBind())
                        .set(PlayerFields.email, player.getEmail());
                EntityDaoMrg.getInstance().getDao(PlayerDao.class)
                        .updatePlayer(player.getPlayerId(), update);
            } else {
                final Phone phoneInfo = account.getPhoneInfo();
                phoneInfo.setAreaCode(areaCode);
                phoneInfo.setPhone(reqAccount);
                EntityDaoMrg.getInstance().getDao(AccountDao.class).updatePhone(account.getAccountId(), phoneInfo);

                player.setPhoneBind(true);
                player.setAreaCode(areaCode);
                player.setPhone(reqAccount);
                final Update update = new Update();
                update.set(PlayerFields.phoneBind, player.isPhoneBind())
                        .set(PlayerFields.areaCode, player.getAreaCode())
                        .set(PlayerFields.phone, player.getPhone());
                EntityDaoMrg.getInstance().getDao(PlayerDao.class)
                        .updatePlayer(player.getPlayerId(), update);
            }

            RedisPoolManager.getInstance().executeAsync(commands ->
                    commands.hset(RedisLogin.Platform_LG_Map_AccountID.getKey(player.getBusiness_no(), threeParty), finalAc, pid + "")
            );

            res.setAreaCode(areaCode)
                    .setAccount(reqAccount)
                    .setThreeParty(threeParty);
            MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
        } catch (Exception e) {
            LOGGER.error("ReqBindAccountHandler", e);
            res.setError(ErrorCode.Internal_Server_Error.getCode());
            MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
            ;
        }
    }
}
