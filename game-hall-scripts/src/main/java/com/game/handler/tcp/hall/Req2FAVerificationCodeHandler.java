package com.game.handler.tcp.hall;

import com.game.dao.player.PlayerDao;
import com.game.engine.io.handler.TcpHandler;
import com.game.engine.script.IHandlerEntity;
import com.game.engine.utils.MsgUtil;
import com.game.engine.utils.TimeUtil;
import com.game.entity.player.Player;
import com.game.entity.player.PlayerFields;
import com.game.enums.ErrorCode;
import com.game.hall.mrg.player.PlayerMrg;
import com.game.manager.EntityDaoMrg;
import com.proto.HallMessage;
import com.proto.MIDMessage;
import com.warrenstrange.googleauth.GoogleAuthenticator;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@IHandlerEntity(mid = MIDMessage.MID.Req2FAVerificationCode_VALUE, msg = HallMessage.Req2FAVerificationCodeMessage.class)
public class Req2FAVerificationCodeHandler extends TcpHandler {
    private static final Logger LOGGER = LoggerFactory.getLogger(Req2FAVerificationCodeHandler.class);

    @Override
    public void run() {
        final HallMessage.Res2FAVerificationCodeMessage.Builder res = HallMessage.Res2FAVerificationCodeMessage.newBuilder();
        res.setMsgID(MIDMessage.MID.Res2FAVerificationCode_VALUE);

        try {
            final Player player = PlayerMrg.getInstance().getOnlinePlayer(pid, this.getClass().getSimpleName());
            if (player == null) {
                res.setError(ErrorCode.Player_Offline.getCode());
                MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                return;
            }
            final HallMessage.Req2FAVerificationCodeMessage req = (HallMessage.Req2FAVerificationCodeMessage) getMessage();
            final String code = req.getVerificationCode();
//            final long userInputLong = StringUtil.isNullOrEmpty(code) ? 0 : Long.parseLong(code, 10);

            final GoogleAuthenticator gAuth = new GoogleAuthenticator();
            final String secretKey = player.getSecretKey();

            // 验证输入的 OTP 是否正确
            final boolean isCodeValid = gAuth.authorize(secretKey, Integer.parseInt(code), TimeUtil.currentTimeMillis() + TimeUtil.SEC * 30);
            if (!isCodeValid) {
                res.setError(ErrorCode.Two_Factor_Authentication_Fail.getCode());
                MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                return;
            }

            if (player.isVerify2FA()) {
                MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                return;
            }

            player.setVerify2FA(true);
            EntityDaoMrg.getInstance().getDao(PlayerDao.class)
                    .updatePlayerField(pid, PlayerFields.verify2FA, player.isVerify2FA());

            MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
        } catch (Exception e) {
            LOGGER.error("Req2FAVerificationCodeHandler", e);
            res.setError(ErrorCode.Internal_Server_Error.getCode());
            MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
        }
    }

}
