package com.game.handler.tcp.hall;

import com.game.dao.player.PlayerDao;
import com.game.engine.io.handler.TcpHandler;
import com.game.engine.script.IHandlerEntity;
import com.game.engine.script.ScriptLoader;
import com.game.engine.utils.MsgUtil;
import com.game.entity.player.Player;
import com.game.entity.player.PlayerFields;
import com.game.enums.ErrorCode;
import com.game.enums.GameType;
import com.game.hall.mrg.player.PlayerMrg;
import com.game.hall.script.IAgentGameScript;
import com.game.hall.script.IPlayerScript;
import com.game.manager.EntityDaoMrg;
import com.proto.HallMessage;
import com.proto.MIDMessage;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.mongodb.core.query.Update;

@IHandlerEntity(mid = MIDMessage.MID.ReqPlayerSignOut_VALUE, msg = HallMessage.ReqPlayerSignOutMessage.class)
public class ReqPlayerSignOutHandler extends TcpHandler {
    private static final Logger LOGGER = LoggerFactory.getLogger(ReqPlayerSignOutHandler.class);

    @Override
    public void run() {
        final HallMessage.ResPlayerSignOutMessage.Builder res = HallMessage.ResPlayerSignOutMessage.newBuilder();
        res.setMsgID(MIDMessage.MID.ResPlayerSignOut_VALUE);

        final Player player = PlayerMrg.getInstance().getOnlinePlayer(pid, this.getClass().getSimpleName());
        if (player == null) {
            res.setError(ErrorCode.Player_Offline.getCode());
            MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
            return;
        }

        if (player.getGameId() > 0) {
            //TODO 通知更新游戏信息
            LOGGER.warn("playerId：{}，quit gameId：{}", player.getPlayerId(), player.getGameId());

            player.resetGameInfo();
            final Update update = new Update();
            update.set(PlayerFields.gameId, player.getGameId())
                    .set(PlayerFields.bonus, player.isBonus())
                    .set(PlayerFields.platformId, player.getPlatformId())
                    .set(PlayerFields.gameType, player.getGameType())
                    .set(PlayerFields.gameCurrencyId, player.getGameCurrencyId())
                    .set(PlayerFields.playerCurrencyId, player.getPlayerCurrencyId());
            EntityDaoMrg.getInstance().getDao(PlayerDao.class)
                    .updatePlayer(player.getPlayerId(), update);

            ScriptLoader.getInstance().consumerScript("AgentGameScript",
                    (IAgentGameScript script) -> script.statisticalGameNoteData(player));
        }

        MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
        PlayerMrg.getInstance().signOut(player);
    }
}
