package com.game.handler.tcp.inner;

import com.game.engine.enums.ServerType;
import com.game.engine.io.handler.TcpHandler;
import com.game.engine.script.IHandlerEntity;
import com.game.engine.struct.ServerInfo;
import com.game.hall.main.HallServer;
import com.game.hall.mrg.ServerMrg;
import com.game.manager.ServersMrg;
import com.proto.InnerMessage;
import com.proto.MIDMessage;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.*;

@IHandlerEntity(mid = MIDMessage.MID.InnerResServerList_VALUE, msg = InnerMessage.InnerResServerListMessage.class)
public class HallInnerResServerListHandler extends TcpHandler {
    private static final Logger LOGGER = LoggerFactory.getLogger(HallInnerResServerListHandler.class);

    @Override
    public void run() {
        try {
            InnerMessage.InnerResServerListMessage resMessage = (InnerMessage.InnerResServerListMessage) getMessage();
            //更新代理返回的网关服信息
            List<InnerMessage.ServerInfoList> serverList = resMessage.getServerListList();
            if (serverList.isEmpty()) {
                return;
            }

            final Set<Integer> serverIds = new HashSet<>();
            for (InnerMessage.ServerInfoList serverInfo : serverList) {
                if (serverInfo.getType() == ServerType.BILLING.getType()) {
                    serverIds.clear();
                    final ServersMrg serversMrg = HallServer.getInstance().getHallTcpClient2Billing().getServersMrg();
                    for (InnerMessage.InnerServerInfo s : serverInfo.getServerInfosList()) {
                        serversMrg.updateServerInfo(s);
                        serverIds.add(s.getId());
                    }

                    //移除关闭的billing
                    final Map<Integer, ServerInfo> hallServers = serversMrg.getServerMap();
                    if (hallServers.size() != serverInfo.getServerInfosList().size()) {
                        final ArrayList<Integer> ids = new ArrayList<>(hallServers.keySet());
                        ids.removeAll(serverIds);
                        for (int id : ids) {
                            serversMrg.removeTcpClient(id);
                        }
                    }
                } else if (serverInfo.getType() == ServerType.AGENT_GAME.getType()) {
                    serverIds.clear();
                    for (InnerMessage.InnerServerInfo s : serverInfo.getServerInfosList()) {
                        ServerMrg.getInstance().updateServer(s);
                        serverIds.add(s.getId());
                    }
                    final Map<Integer, ServerInfo> agentGameServerMap = ServerMrg.getInstance().getServersMap(ServerType.AGENT_GAME);
                    if (agentGameServerMap != null && serverIds.size() != agentGameServerMap.size()) {
                        final ArrayList<Integer> ids = new ArrayList<>(agentGameServerMap.keySet());
                        ids.removeAll(serverIds);
                        for (int id : ids) {
                            agentGameServerMap.remove(id);
                        }
                    }
                }
            }
        } catch (Exception e) {
            LOGGER.error("InnerResServerListHandler ", e);
        }
    }

}
