package com.game.handler.tcp.activity.firstChargeSignIn;

import com.game.c_entity.merchant.C_FirstChargeSignIn;
import com.game.dao.player.PlayerDao;
import com.game.engine.io.handler.TcpHandler;
import com.game.engine.log.GameLog;
import com.game.engine.math.BigDecimalUtils;
import com.game.engine.script.IHandlerEntity;
import com.game.engine.script.ScriptLoader;
import com.game.engine.util.misc.Tuple2;
import com.game.engine.utils.GuidGeneratorUtils;
import com.game.engine.utils.MsgUtil;
import com.game.engine.utils.TimeUtil;
import com.game.entity.player.Player;
import com.game.entity.player.activity.firstChargeSignIn.FirstChargeSignInInfo;
import com.game.enums.ErrorCode;
import com.game.enums.RewardReason;
import com.game.enums.TransactionFrom;
import com.game.enums.TurnoverReason;
import com.game.hall.main.HallServer;
import com.game.hall.mrg.ActivityMrg;
import com.game.hall.mrg.CommonMrg;
import com.game.hall.mrg.DataHallMrg;
import com.game.hall.mrg.MerchantData;
import com.game.hall.mrg.currency.CurrencyMrg;
import com.game.hall.mrg.currency.RewardRequest;
import com.game.hall.mrg.player.PlayerMrg;
import com.game.hall.script.IBonusScript;
import com.game.hall.script.IPlayerScript;
import com.game.manager.EntityDaoMrg;
import com.proto.ActivityMessage;
import com.proto.MIDMessage;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Map;

@IHandlerEntity(mid = MIDMessage.MID.ReqFirstChargeSignIn_VALUE, msg = ActivityMessage.ReqFirstChargeSignInMessage.class)
public class ReqFirstChargeSignInHandler extends TcpHandler {
    private static final Logger LOGGER = LoggerFactory.getLogger(ReqFirstChargeSignInHandler.class);

    @Override
    public void run() {
        final ActivityMessage.ResFirstChargeSignInMessage.Builder res = ActivityMessage.ResFirstChargeSignInMessage.newBuilder();
        res.setMsgID(MIDMessage.MID.ResFirstChargeSignIn_VALUE);
        try {
            final ActivityMessage.ReqFirstChargeSignInMessage req = (ActivityMessage.ReqFirstChargeSignInMessage) getMessage();
            final int day = req.getDay();

            final Player player = PlayerMrg.getInstance().getOnlinePlayer(pid, this.getClass().getSimpleName());
            if (player == null) {
                res.setError(ErrorCode.Player_Offline.getCode());
                MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                return;
            }

            final MerchantData merchantData = DataHallMrg.getInstance().findMerchantData(this.getClass().getSimpleName(), player.getBusiness_no());
            if (merchantData == null) {
                res.setError(ErrorCode.Data_Error.getCode());
                MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                return;
            }

            final C_FirstChargeSignIn c_firstChargeSignIn = merchantData.findC_FirstChargeSignIn(this.getClass().getSimpleName(), ActivityMrg.FIRSTCHARGE_SIGNIN);
            if (c_firstChargeSignIn == null) {
                res.setError(ErrorCode.Data_Error.getCode());
                MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                return;
            }

            final FirstChargeSignInInfo firstChargeSignInInfo = player.getFirstChargeSignInInfo();
            if (firstChargeSignInInfo.getRechargeTime() == 0) {
                res.setError(ErrorCode.Conditions_Not_Met.getCode());
                MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                return;
            }

            if (firstChargeSignInInfo.getReceive().contains(day)) {
                res.setError(ErrorCode.Reward_Received.getCode());
                MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                return;
            }

            if (!firstChargeSignInInfo.getReceive().isEmpty()) {
                final int lastDay = firstChargeSignInInfo.getReceive().getLast();
                if (day - lastDay > c_firstChargeSignIn.getResetDay()) {
                    res.setError(ErrorCode.Conditions_Not_Met.getCode());
                    MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                    return;
                }
            }

            final int currDay = TimeUtil.getNaturalDays(TimeUtil.currentTimeMillis(), firstChargeSignInInfo.getRechargeTime(), c_firstChargeSignIn.getTimeZone());
            if (day != currDay) {
                res.setError(ErrorCode.Not_Yet_Received.getCode());
                MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                return;
            }

            final Map<Integer, C_FirstChargeSignIn.FirstChargeReward> firstChargeRewardMap = c_firstChargeSignIn.getFirstChargeSignInMap();
            final C_FirstChargeSignIn.FirstChargeReward firstChargeReward = firstChargeRewardMap.get(day);
            if (firstChargeReward == null) {
                res.setError(ErrorCode.Conditions_Not_Met.getCode());
                MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                return;
            }

            firstChargeSignInInfo.setCurrDay(currDay);
            firstChargeSignInInfo.getReceive().add(currDay);
            EntityDaoMrg.getInstance().getDao(PlayerDao.class)
                    .updateFirstChargeSignInInfo(pid, firstChargeSignInInfo);

            final RewardRequest rewardRequest = new RewardRequest();
            rewardRequest.addCurrency(firstChargeReward.currencyId, firstChargeReward.bonus);
            final RewardReason rewardReason = RewardReason.FirstChargeSignIn;
            rewardReason.setSource(currDay + "");
            CurrencyMrg.getInstance().rewardNoTest(player, rewardRequest.getBonusCurrencyMap(), rewardReason);

            //TODO 打码
            final TurnoverReason turnoverReason = TurnoverReason.FirstChargeSignIn;
            turnoverReason.setSource(currDay + "");
            ScriptLoader.getInstance().consumerScript("PlayerWithdrawStandardScript",
                    (IPlayerScript script) -> script.drawStandard(player, turnoverReason, firstChargeReward.currencyId, firstChargeReward.bonus,
                            BigDecimalUtils.mul(firstChargeReward.bonus, c_firstChargeSignIn.getTurnoverMul(), 4)));

            if (!rewardRequest.getCurrencyMap().isEmpty()) {
                final Tuple2<Integer, Double> tuple2 = rewardRequest.getReward();
                ScriptLoader.getInstance().consumerScript("BonusScript",
                        (IBonusScript script) -> script.addBonusNote(TransactionFrom.FirstChargeSignIn, player, tuple2.getFirst(), tuple2.getSecond()));
            }

            res.setRewardShow(CommonMrg.buildDItemShow(firstChargeReward.currencyId, firstChargeReward.bonus));
            MsgUtil.responseHttp(res.build(), res.getMsgID(), session);

            final GameLog playerFirstChargeSignInLog = new GameLog("platform_playerFirstChargeSignInLog");
            playerFirstChargeSignInLog.append("number", GuidGeneratorUtils.generateOrderId())
                    .append("business_no", player.getBusiness_no())
                    .append("channel", player.getChannel())
                    .append("site", player.getWebSite())
                    .append("playerId", player.getPlayerId())
                    .append("playerName", player.getPlayerName())
                    .append("agentId", player.getAgentId())
                    .append("channelId", player.getChannelId())
                    .append("region", player.getRegisterRegion())
                    .append("logTime", TimeUtil.currentTimeMillis())
                    .append("activityId", ActivityMrg.FIRSTCHARGE_SIGNIN)
                    .append("activityUniqueId", c_firstChargeSignIn.getC_id())
                    .append("type", 1)//1.领取 2.激活
                    .append("currDay", currDay)
                    .append("currencyId", firstChargeReward.currencyId)
                    .append("reward", firstChargeReward.bonus);
            HallServer.getInstance().getLogProducerMrg().send(playerFirstChargeSignInLog);
        } catch (Exception e) {
            LOGGER.error("ReqFirstChargeSignInHandler", e);
            res.setError(ErrorCode.Internal_Server_Error.getCode());
            MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
        }
    }
}
