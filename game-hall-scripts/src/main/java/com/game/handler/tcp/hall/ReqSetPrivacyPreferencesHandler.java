package com.game.handler.tcp.hall;

import com.game.dao.player.PlayerDao;
import com.game.engine.io.handler.TcpHandler;
import com.game.engine.script.IHandlerEntity;
import com.game.engine.utils.MsgUtil;
import com.game.entity.player.Player;
import com.game.entity.player.PlayerFields;
import com.game.enums.ErrorCode;
import com.game.hall.mrg.player.PlayerMrg;
import com.game.manager.EntityDaoMrg;
import com.proto.HallMessage;
import com.proto.MIDMessage;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@IHandlerEntity(mid = MIDMessage.MID.ReqSetPrivacyPreferences_VALUE, msg = HallMessage.ReqSetPrivacyPreferencesMessage.class)
public class ReqSetPrivacyPreferencesHandler extends TcpHandler {
    private static final Logger LOGGER = LoggerFactory.getLogger(ReqSetPrivacyPreferencesHandler.class);

    @Override
    public void run() {
        final HallMessage.ResSetPrivacyPreferencesMessage.Builder res = HallMessage.ResSetPrivacyPreferencesMessage.newBuilder();
        res.setMsgID(MIDMessage.MID.ResSetPrivacyPreferences_VALUE);
        try {
            final Player player = PlayerMrg.getInstance().getOnlinePlayer(pid, this.getClass().getSimpleName());
            if (player == null) {
                res.setError(ErrorCode.Player_Offline.getCode());
                MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                return;
            }

            final HallMessage.ReqSetPrivacyPreferencesMessage req = (HallMessage.ReqSetPrivacyPreferencesMessage) getMessage();
            final int privacy = req.getPrivacy();
            boolean value = false;
            switch (privacy) {
                case 1://hideMyUsername
                    if (!player.isHideName()) {
                        player.setHideName(true);
                    } else {
                        player.setHideName(false);
                    }
                    EntityDaoMrg.getInstance().getDao(PlayerDao.class)
                            .updatePlayerField(pid, PlayerFields.hideName, player.isHideName());
                    value = player.isHideName();
                    break;
            }

            res.setPrivacy(privacy)
                    .setValue(value);
            MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
        } catch (Exception e) {
            LOGGER.error("ReqSetPrivacyPreferencesHandler", e);
            res.setError(ErrorCode.Internal_Server_Error.getCode());
            MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
        }
    }
}
