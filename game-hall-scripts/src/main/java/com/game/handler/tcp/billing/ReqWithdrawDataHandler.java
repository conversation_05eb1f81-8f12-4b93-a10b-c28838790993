package com.game.handler.tcp.billing;

import com.game.c_entity.merchant.C_PlatformWithdraw;
import com.game.c_entity.merchant.C_RechargeWithdrawLimit;
import com.game.c_entity.merchant.C_VipClub;
import com.game.c_entity.middleplatform.C_BasePaymentMethod;
import com.game.dao.game.GameNoteDao;
import com.game.engine.io.handler.TcpHandler;
import com.game.engine.math.BigDecimalUtils;
import com.game.engine.script.IHandlerEntity;
import com.game.engine.script.ScriptLoader;
import com.game.engine.util.misc.Tuple2;
import com.game.engine.utils.JsonUtils;
import com.game.engine.utils.MsgUtil;
import com.game.engine.utils.TimeUtil;
import com.game.entity.game.GameNote;
import com.game.entity.game.GameNoteFields;
import com.game.entity.player.PayInfo;
import com.game.entity.player.Player;
import com.game.entity.player.WithdrawAccount;
import com.game.entity.player.WithdrawStandard;
import com.game.enums.Currency;
import com.game.enums.ErrorCode;
import com.game.hall.mrg.DataHallMrg;
import com.game.hall.mrg.MerchantData;
import com.game.hall.mrg.player.PlayerMrg;
import com.game.hall.script.IBillingScript;
import com.game.manager.EntityDaoMrg;
import com.proto.BillingMessage;
import com.proto.MIDMessage;
import io.netty.util.internal.StringUtil;
import it.unimi.dsi.fastutil.objects.Object2ObjectMap;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.*;

@IHandlerEntity(mid = MIDMessage.MID.ReqWithdrawData_VALUE, msg = BillingMessage.ReqWithdrawDataMessage.class)
public class ReqWithdrawDataHandler extends TcpHandler {

    private static final Logger LOGGER = LoggerFactory.getLogger(ReqWithdrawDataHandler.class);

    @Override
    public void run() {
        final BillingMessage.ResWithdrawDataMessage.Builder res = BillingMessage.ResWithdrawDataMessage.newBuilder();
        res.setMsgID(MIDMessage.MID.ResWithdrawData_VALUE);
        try {
            final Player player = PlayerMrg.getInstance().getOnlinePlayer(pid, this.getClass().getSimpleName());
            if (player == null) {
                res.setError(ErrorCode.Player_Offline.getCode());
                MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                return;
            }

            final BillingMessage.ReqWithdrawDataMessage req = (BillingMessage.ReqWithdrawDataMessage) getMessage();
            final int currencyId = req.getCurrencyId();
            if (currencyId == 0) {
                LOGGER.error("playerId：{}，currencyId is 0", player.getPlayerId());
                res.setError(ErrorCode.Data_Error.getCode());
                MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                return;
            }

            final MerchantData merchantData = DataHallMrg.getInstance().findMerchantData(this.getClass().getSimpleName(), player.getBusiness_no());
            if (merchantData == null) {
                res.setError(ErrorCode.Data_Error.getCode());
                MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                return;
            }

            final C_RechargeWithdrawLimit c_limit = merchantData.findC_RechargeWithdrawLimit(this.getClass().getSimpleName(), currencyId);
            if (c_limit == null) {
//                res.setError(ErrorCode.Data_Error.getCode());
                MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                return;
            }

            final Tuple2<Double, Double> tuple2 = ScriptLoader.getInstance().functionScript("BillingScript",
                    (IBillingScript script) -> script.calculateAvailableAmount(player, currencyId, c_limit.getWithdrawType()));
            final double available = tuple2.getFirst();
            final double lockedFunds = tuple2.getSecond();

            switch (currencyId / 1000) {
                case 1://法币
                    final Map<String, C_PlatformWithdraw> c_platformWithdrawFiatMap = merchantData.findC_PlatformWithdrawFiat(this.getClass().getSimpleName(), currencyId);
                    if (c_platformWithdrawFiatMap == null || c_platformWithdrawFiatMap.isEmpty()) {
//                        res.setError(ErrorCode.Data_Error.getCode());
                        MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                        return;
                    }
                    for (final Map.Entry<String, C_PlatformWithdraw> entry : c_platformWithdrawFiatMap.entrySet()) {
                        final C_PlatformWithdraw c_platformWithdraw = entry.getValue();
                        res.addWithdrawFiatData(buildWithdrawFiatData(player, c_platformWithdraw, merchantData));
                    }
                    break;
                case 2://加密
                    final List<C_PlatformWithdraw> c_platformWithdrawCryptoList = merchantData.findC_PlatformWithdrawCrypto(this.getClass().getSimpleName(), currencyId);
                    if (c_platformWithdrawCryptoList == null || c_platformWithdrawCryptoList.isEmpty()) {
//                        res.setError(ErrorCode.Data_Error.getCode());
                        MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                        return;
                    }
                    final String network = req.getNetwork();
                    final Optional<C_PlatformWithdraw> optional = c_platformWithdrawCryptoList.stream().filter(c_platform -> Objects.equals(c_platform.getNetwork(), network)).findFirst();
                    if (optional.isPresent()) {
                        C_PlatformWithdraw c_platformWithdraw = optional.get();
                        res.setWithdrawCrypto(buildWithdrawCryptoData(player, c_platformWithdraw, merchantData));
                    }
                    break;
            }

            final WithdrawStandard withdrawStandard = player.getWithdrawStandard(currencyId);
            double turnoverRate = 0;
            if (withdrawStandard.getDrawStandard() > 0) {
                turnoverRate = BigDecimalUtils.div(withdrawStandard.getBettingVolume(), withdrawStandard.getDrawStandard(), 4);
            }

            final C_VipClub c_vipClub = merchantData.findC_VipClub(this.getClass().getSimpleName(), player.getVipClub().getVipLevel());

            res.setAvailable(available)
                    .setLockedFunds(lockedFunds)
                    .setTurnoverRate(turnoverRate);
            if (c_vipClub != null) {
                res.setFreeTimes(Math.max(c_vipClub.getDailyWithdrawFreeTimes() - player.getDailyFeeTimes(), 0));
            }
            MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
        } catch (Exception e) {
            LOGGER.error("ReqWithDrawDataHandler", e);
            res.setError(ErrorCode.Internal_Server_Error.getCode());
            MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
        }
    }

    private BillingMessage.WithdrawFiatData buildWithdrawFiatData(Player player, C_PlatformWithdraw c_platformWithdraw, MerchantData merchantData) {
        final C_VipClub c_vipClub = merchantData.findC_VipClub(this.getClass().getSimpleName(), player.getVipClub().getVipLevel());

        double validBets = 0;
        if (TimeUtil.currentTimeMillis() < player.getRechargeTime() + c_platformWithdraw.getChargeTime()) {
            final GameNote gameNote = EntityDaoMrg.getInstance().getDao(GameNoteDao.class)
                    .aggregateBet(player.getPlayerId(), c_platformWithdraw.getCurrencyId(), GameNoteFields.validBets, player.getRechargeTime(), player.getRechargeTime() + c_platformWithdraw.getChargeTime());
            validBets = gameNote.getValidBets();
        } else {
            validBets = -1;
        }

        final BillingMessage.WithdrawFiatData.Builder withdrawFiatData = BillingMessage.WithdrawFiatData.newBuilder()
                .setCurrencyId(c_platformWithdraw.getCurrencyId())
                .setChannel(c_platformWithdraw.getChannel())
                .setCategory(c_platformWithdraw.getCategory())
                .setPaymentMethod(c_platformWithdraw.getPaymentMethod())
                .setMinimumWithdrawalFee(c_platformWithdraw.getMinimumWithdrawFee())
                .setFixedWithdrawalFee(c_platformWithdraw.getFixedWithdrawFee())
                .setWithdrawalFeeRate(validBets >= 0 ? c_platformWithdraw.getChargeWithdrawFeeRate() : c_platformWithdraw.getWithdrawFeeRate())
                .setLowerLimit(c_platformWithdraw.getSingleWithdrawLowerLimit())
                .setUpperLimit(c_platformWithdraw.getSingleWithdrawUpperLimit());
        if (c_vipClub != null) {
            withdrawFiatData.setLowerLimit(Math.max(c_vipClub.getDailyWithdrawLower(), c_platformWithdraw.getSingleWithdrawLowerLimit()))
                    .setUpperLimit(Math.min(c_vipClub.getDailyWithdrawUpper(), c_platformWithdraw.getSingleWithdrawUpperLimit()));
        }

        for (Map.Entry<String, C_PlatformWithdraw.WithdrawInfo> entry : c_platformWithdraw.getWithdrawInfoMap().entrySet()) {
            withdrawFiatData.addWithdrawInfos(buildWithdrawInfo(entry.getKey(), entry.getValue()));
        }
        final String currencyIdChannel = c_platformWithdraw.getCurrencyId() + "_" + c_platformWithdraw.getPaymentMethod();
        final WithdrawAccount withdrawAccount = player.getWithdrawAccountMap().get(currencyIdChannel);
        if (withdrawAccount != null) {
            if (c_platformWithdraw.getCurrencyId() == Currency.BRL.getCurrencyId()) {
                if (!withdrawAccount.getPayInfoMap().isEmpty()) {
                    final List<PayInfo> payInfos = new ArrayList<>(withdrawAccount.getPayInfoMap().values());
                    payInfos.sort(Comparator.comparingLong(PayInfo::getCreateTime));
                    final PayInfo payInfo = payInfos.getFirst();
                    withdrawFiatData.addWithdrawAccount(buildWithdrawAccount(payInfo));
                }
            } else {
                for (PayInfo payInfo : withdrawAccount.getPayInfoMap().values()) {
                    withdrawFiatData.addWithdrawAccount(buildWithdrawAccount(payInfo));
                }
            }
        }
        final C_BasePaymentMethod c_basePaymentMethod = DataHallMrg.getInstance().findC_BasePaymentMethod(this.getClass().getSimpleName(), c_platformWithdraw.getPaymentMethod());
        if (c_basePaymentMethod != null) {
            withdrawFiatData.setPaymentMethodName(c_basePaymentMethod.getName())
                    .setPaymentMethodLog(c_basePaymentMethod.getIcon());
        }
        return withdrawFiatData.build();
    }

    private BillingMessage.WithdrawInfo buildWithdrawInfo(String filed, C_PlatformWithdraw.WithdrawInfo withdrawInfo) {
        return BillingMessage.WithdrawInfo.newBuilder()
                .setField(filed)
                .addAllParams(withdrawInfo.getParams())
                .setType(withdrawInfo.getType())
                .build();
    }

    private BillingMessage.WithdrawCryptoData buildWithdrawCryptoData(Player player, C_PlatformWithdraw c_platformWithdraw, MerchantData merchantData) {
        final C_VipClub c_vipClub = merchantData.findC_VipClub(this.getClass().getSimpleName(), player.getVipClub().getVipLevel());

        double validBets = 0;
        if (TimeUtil.currentTimeMillis() < player.getRechargeTime() + c_platformWithdraw.getChargeTime()) {
            final GameNote gameNote = EntityDaoMrg.getInstance().getDao(GameNoteDao.class)
                    .aggregateBet(player.getPlayerId(), c_platformWithdraw.getCurrencyId(), GameNoteFields.validBets, player.getRechargeTime(), player.getRechargeTime() + c_platformWithdraw.getChargeTime());
            validBets = gameNote.getValidBets();
        } else {
            validBets = -1;
        }

        final BillingMessage.WithdrawCryptoData.Builder withdrawCryptoData = BillingMessage.WithdrawCryptoData.newBuilder()
                .setCurrencyId(c_platformWithdraw.getCurrencyId())
                .setNetwork(c_platformWithdraw.getNetwork())
                .setMinimumWithdrawalFee(c_platformWithdraw.getMinimumWithdrawFee())
                .setFixedWithdrawalFee(c_platformWithdraw.getFixedWithdrawFee())
                .setWithdrawalFeeRate(validBets >= 0 ? c_platformWithdraw.getChargeWithdrawFeeRate() : c_platformWithdraw.getWithdrawFeeRate())
                .setMinimum(c_platformWithdraw.getSingleWithdrawLowerLimit())
                .setMaxMum(c_platformWithdraw.getSingleWithdrawUpperLimit());
        if (c_vipClub != null) {
            withdrawCryptoData.setMinimum(Math.max(c_vipClub.getDailyWithdrawLower(), c_platformWithdraw.getSingleWithdrawLowerLimit()))
                    .setMaxMum(Math.min(c_vipClub.getDailyWithdrawUpper(), c_platformWithdraw.getSingleWithdrawUpperLimit()));
        }

        final String currencyIdChannel = c_platformWithdraw.getCurrencyId() + "_" + c_platformWithdraw.getPaymentMethod();
        final WithdrawAccount withdrawAccount = player.getWithdrawAccountMap().get(currencyIdChannel);
        if (withdrawAccount != null) {
            for (PayInfo payInfo : withdrawAccount.getPayInfoMap().values()) {
                withdrawCryptoData.addWithdrawAccount(buildWithdrawAccount(payInfo));
            }
        }
        return withdrawCryptoData.build();
    }

    private BillingMessage.WithdrawAccount buildWithdrawAccount(PayInfo payInfo) {
        return BillingMessage.WithdrawAccount.newBuilder()
                .setWithdrawAccountId(payInfo.getPayId() + "")
                .setExtend0(payInfo.getExtend())
                .setExtend1(payInfo.getExtend_1())
                .setExtend2(payInfo.getExtend_2())
                .setExtend3(StringUtil.isNullOrEmpty(payInfo.getExtend_3()) ? "" : payInfo.getExtend_3())
                .build();
    }

}
