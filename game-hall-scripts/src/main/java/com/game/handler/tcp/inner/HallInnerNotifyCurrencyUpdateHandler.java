package com.game.handler.tcp.inner;

import com.game.c_entity.merchant.C_CashBack;
import com.game.c_entity.merchant.C_FreeGameTurnover;
import com.game.c_entity.merchant.C_GameApi;
import com.game.c_entity.merchant.C_GamePlatform;
import com.game.dao.player.PlayerDao;
import com.game.engine.enums.ServerType;
import com.game.engine.io.handler.TcpHandler;
import com.game.engine.math.BigDecimalUtils;
import com.game.engine.script.IHandlerEntity;
import com.game.engine.script.ScriptLoader;
import com.game.engine.struct.ServerInfo;
import com.game.engine.utils.MsgUtil;
import com.game.engine.utils.TimeUtil;
import com.game.entity.player.GameInfo;
import com.game.entity.player.FreeGameInfo;
import com.game.entity.player.Player;
import com.game.entity.player.bonus.BonusDetailsInfo;
import com.game.entity.player.bonus.BonusInfo;
import com.game.entity.player.stats.Stats;
import com.game.enums.*;
import com.game.hall.mrg.ActivityMrg;
import com.game.hall.mrg.DataHallMrg;
import com.game.hall.mrg.MerchantData;
import com.game.hall.mrg.ServerMrg;
import com.game.hall.mrg.currency.CurrencyMrg;
import com.game.hall.mrg.currency.RewardRequest;
import com.game.hall.mrg.currency.SpendRequest;
import com.game.hall.mrg.player.PlayerMrg;
import com.game.hall.script.*;
import com.game.hall.script.activity.IActivityScript;
import com.game.manager.EntityDaoMrg;
import com.proto.InnerMessage;
import com.proto.MIDMessage;
import io.netty.util.internal.StringUtil;
import it.unimi.dsi.fastutil.ints.IntLists;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@IHandlerEntity(mid = MIDMessage.MID.InnerNotifyCurrencyUpdate_VALUE, msg = InnerMessage.InnerNotifyCurrencyUpdateMessage.class)
public class HallInnerNotifyCurrencyUpdateHandler extends TcpHandler {
    private static final Logger LOGGER = LoggerFactory.getLogger(HallInnerNotifyCurrencyUpdateHandler.class);

    @Override
    public void run() {
        final InnerMessage.InnerNotifyCurrencyUpdateMessage.Builder res = InnerMessage.InnerNotifyCurrencyUpdateMessage.newBuilder();
        res.setMsgID(MIDMessage.MID.InnerNotifyCurrencyUpdate_VALUE);
        try {
            final InnerMessage.InnerNotifyCurrencyUpdateMessage req = (InnerMessage.InnerNotifyCurrencyUpdateMessage) getMessage();
            final InnerMessage.NotifyData notifyData = req.getNotifyData();
            final int type = notifyData.getType();
            final int gameId = notifyData.getGameId();

            final Player player = PlayerMrg.getInstance().findDbPlayer(pid);
            if (player == null) {
                LOGGER.warn("HallInnerNotifyCurrencyUpdateHandler，playerId：{}，not exist", pid);
                return;
            }

            final MerchantData merchantData = DataHallMrg.getInstance().findMerchantData(this.getClass().getSimpleName(), player.getBusiness_no());
            if (merchantData == null) {
                return;
            }

            final C_GameApi c_gameApi = merchantData.findC_GameApi(this.getClass().getSimpleName(), gameId);
            if (c_gameApi == null) {
                return;
            }

            final C_GamePlatform c_gamePlatform = merchantData.findC_GamePlatform(this.getClass().getSimpleName(), c_gameApi.getPlatformId());
            if (c_gamePlatform == null) {
                return;
            }

            switch (type) {
                case 0:
                    special(notifyData, player, c_gameApi);
                    break;
                case 1://bet
                    bet(notifyData, player, c_gameApi);
                    break;
                case 2://win
                    win(notifyData, player, c_gameApi);
                    break;
                case 3://refund
                    refund(notifyData, player, c_gameApi);
                    break;
                case 4://free
                    free(notifyData, player, c_gameApi);
                    break;
                case 5://change
                    change(notifyData, player, c_gameApi);
                    break;
            }

            final ServerInfo serverInfo = ServerMrg.getInstance()
                    .getServerType(ServerType.AGENT_GAME, player.getPlayerId(), player.getAgentGameServerId());

            final double cashBalance = CurrencyMrg.getInstance().getCurrencyValue(player, notifyData.getCurrencyId());
            double bonusBalance = 0;
            if (player.isBonus()) {
                bonusBalance = CurrencyMrg.getInstance().getCurrencyValue(player, notifyData.getCurrencyId() * 10);
            }

            final InnerMessage.NotifyData resNotifyData = notifyData
                    .toBuilder()
                    .setSupplierId(c_gamePlatform.getSupplierId())
                    .setBalance(BigDecimalUtils.add(cashBalance, bonusBalance, 9))
                    .build();

            final InnerMessage.InnerNotifyCurrencyUpdateMessage.Builder msg = InnerMessage.InnerNotifyCurrencyUpdateMessage.newBuilder();
            msg.setMsgID(MIDMessage.MID.InnerNotifyCurrencyUpdate_VALUE)
                    .setNotifyData(resNotifyData);
            MsgUtil.sendInnerMsg(serverInfo.getActiveSession(), msg.build(), pid, notifyData.getUpdSessionId());

            //TODO 公共处理
            commonProcess(merchantData, notifyData, player, c_gameApi);
        } catch (Exception e) {
            LOGGER.error("HallInnerNotifyCurrencyUpdateHandler", e);
        }
    }

    /**
     * slots
     *
     * @param notifyData
     * @param player
     * @param c_gameApi
     */
    private void special(InnerMessage.NotifyData notifyData, Player player, C_GameApi c_gameApi) {
        final int currencyId = notifyData.getCurrencyId();
        final double betAmount = notifyData.getBetAmount();
        final double win = notifyData.getWin();

        if (betAmount > 0) {
            costGold(notifyData, player, c_gameApi);
        }

        if (win > 0) {
            final RewardRequest rewardRequest = new RewardRequest();
            rewardRequest.addCurrency(currencyId, win, false);
            final RewardReason rewardReason = RewardReason.AgentGame;
            rewardReason.setSource(c_gameApi.getPlatformName() + ":" + notifyData.getNoteId());
            CurrencyMrg.getInstance().rewardNoTest(player, rewardRequest.getCurrencyMap(), rewardReason);
        }
    }

    private void bet(InnerMessage.NotifyData notifyData, Player player, C_GameApi c_gameApi) {
        final double betAmount = notifyData.getBetAmount();
        if (betAmount <= 0) {
            return;
        }

        costGold(notifyData, player, c_gameApi);
    }

    private void win(InnerMessage.NotifyData notifyData, Player player, C_GameApi c_gameApi) {
        final int currencyId = notifyData.getCurrencyId();
        final double win = notifyData.getWin();

        if (win > 0) {
            final RewardRequest rewardRequest = new RewardRequest();
            rewardRequest.addCurrency(currencyId, win, false);
            final RewardReason rewardReason = RewardReason.AgentGame;
            rewardReason.setSource(c_gameApi.getPlatformName() + ":" + notifyData.getNoteId());
            CurrencyMrg.getInstance().rewardNoTest(player, rewardRequest.getCurrencyMap(), rewardReason);
        }
    }

    private void refund(InnerMessage.NotifyData notifyData, Player player, C_GameApi c_gameApi) {
        final int currencyId = notifyData.getCurrencyId();
        final double betAmount = notifyData.getBetAmount();
        final double win = notifyData.getWin();

        final double refund = BigDecimalUtils.sub(win, betAmount, 4);
        if (refund > 0) {
            final SpendRequest spend = new SpendRequest();
            spend.addCurrency(currencyId, refund);
            final SpendReason reason = SpendReason.AgentGame_Refund;
            reason.setSource(c_gameApi.getPlatformName() + ":" + notifyData.getNoteId());
            CurrencyMrg.getInstance().spend(player, spend.getCurrencyMap(), reason);
        }
        if (refund < 0) {
            final RewardRequest rewardRequest = new RewardRequest();
            rewardRequest.addCurrency(currencyId, Math.abs(refund), false);
            final RewardReason rewardReason = RewardReason.AgentGame_Refund;
            rewardReason.setSource(c_gameApi.getPlatformName() + ":" + notifyData.getNoteId());
            CurrencyMrg.getInstance().rewardNoTest(player, rewardRequest.getCurrencyMap(), rewardReason);
        }
    }

    private void free(InnerMessage.NotifyData notifyData, Player player, C_GameApi c_gameApi) {
        final int gameId = notifyData.getGameId();
        final int currencyId = notifyData.getCurrencyId();
        final double win = notifyData.getWin();
        final int freeTimes = notifyData.getFreeTimes();

        if (win > 0) {
            final RewardRequest rewardRequest = new RewardRequest();
            rewardRequest.addCurrency(currencyId, win);
            final RewardReason rewardReason = RewardReason.AgentGame_FreeGames;
            rewardReason.setSource(c_gameApi.getPlatformName() + ":" + notifyData.getNoteId());
            CurrencyMrg.getInstance().rewardNoTest(player, rewardRequest.getBonusCurrencyMap(), rewardReason);

            final BonusInfo bonusInfo = player.getBonusInfo();
            final BonusDetailsInfo bonusDetailsInfo = bonusInfo.getBonusDetailsInfo(BonusDetail.FreeSpin.getType());
            bonusDetailsInfo.incBonus(currencyId, win);

            EntityDaoMrg.getInstance().getDao(PlayerDao.class).bonusInfoDao
                    .updateBonusDetails(player, BonusDetail.FreeSpin.getType(), bonusDetailsInfo, IntLists.singleton(currencyId));

            ScriptLoader.getInstance().consumerScript("BonusScript",
                    (IBonusScript script) -> script.addBonusNote(TransactionFrom.FreeSpin, player, currencyId, win));

            final MerchantData merchantData = DataHallMrg.getInstance().findMerchantData(this.getClass().getSimpleName(), player.getBusiness_no());
            if (merchantData != null) {
                final C_FreeGameTurnover c_freeGameTurnover = merchantData.findC_FreeGameTurnover(this.getClass().getSimpleName(), c_gameApi.getPlatformId());
                if (c_freeGameTurnover != null) {
//                    final WithdrawStandard withdrawStandard = player.getWithdrawStandard(currencyId);
//                    withdrawStandard.incDrawStandard(BigDecimalUtils.mul(win, c_freeGameTurnover.getTurnoverMul(), 4));
                    final TurnoverReason turnoverReason = TurnoverReason.AgentGame_FreeGames;
                    turnoverReason.setSource(c_gameApi.getPlatformName());
                    ScriptLoader.getInstance().consumerScript("PlayerWithdrawStandardScript",
                            (IPlayerScript script) -> script.drawStandard(player, turnoverReason, currencyId, win,
                                    BigDecimalUtils.mul(win, c_freeGameTurnover.getTurnoverMul(), 4)));
                }
            }
        }

        if (currencyId > 0) {
            final FreeGameInfo freeGameInfo = player.getFreeGameInfo(currencyId);
            final GameInfo gameInfo = freeGameInfo.getFreeGame(gameId);
            if (gameInfo.getFreeTimes() > 0) {
                gameInfo.incFreeTimes(freeTimes);
                if (gameInfo.getFreeTimes() <= 0) {
                    freeGameInfo.getGameInfoMap().remove(gameId);
                }
                EntityDaoMrg.getInstance().getDao(PlayerDao.class)
                        .updateFreeGameInfo(player, IntLists.singleton(currencyId));
            }
        }
    }

    private void change(InnerMessage.NotifyData notifyData, Player player, C_GameApi c_gameApi) {
        final int currencyId = notifyData.getCurrencyId();
        final double win = notifyData.getWin();

        if (win > 0) {
            final SpendRequest spend = new SpendRequest();
            spend.addCurrency(currencyId, win);
            final SpendReason reason = SpendReason.AgentGame_Refund;
            reason.setSource(c_gameApi.getPlatformName() + ":" + notifyData.getNoteId());
            CurrencyMrg.getInstance().spend(player, spend.getCurrencyMap(), reason);
        }
    }

    private void costGold(InnerMessage.NotifyData notifyData, Player player, C_GameApi c_gameApi) {
        final int currencyId = notifyData.getCurrencyId();
        final double betAmount = notifyData.getBetAmount();

        final double cashBalance = CurrencyMrg.getInstance().getCurrencyValue(player, currencyId);
        double bonusBalance = 0;
        if (player.isBonus()) {
            bonusBalance = CurrencyMrg.getInstance().getCurrencyValue(player, currencyId * 10);
        }

        if (cashBalance == 0 && bonusBalance == 0) {
            return;
        }

        final double remain = BigDecimalUtils.sub(cashBalance, betAmount, 4);

        final SpendRequest spendRequest = new SpendRequest();
        final SpendReason spendReason = SpendReason.AgentGame;
        spendReason.setSource(c_gameApi.getPlatformName() + ":" + notifyData.getNoteId());
        if (remain >= 0) {//先扣本金
            spendRequest.addCurrency(currencyId, betAmount);
        }

        if (remain < 0) {//先扣完本金，再扣赠金
            if (cashBalance > 0) {
                spendRequest.addCurrency(currencyId, cashBalance);
            }
            if (player.isBonus()) {
                spendRequest.addCurrency(currencyId * 10, Math.abs(remain));
            }
        }
        CurrencyMrg.getInstance().spend(player, spendRequest.getCurrencyMap(), spendReason);
    }


    private void commonProcess(MerchantData merchantData, InnerMessage.NotifyData notifyData, Player player, C_GameApi c_gameApi) {
        try {
            if (notifyData.getType() == ChangeType.refund.getType()) {
                return;
            }

            if (notifyData.getType() != ChangeType.Free.getType()) {
                if (!StringUtil.isNullOrEmpty(notifyData.getNoteId())) {
                    player.getRecodeNoteId().add(Long.parseLong(notifyData.getNoteId()));
                }
            }

            final int currencyId = notifyData.getCurrencyId();
            final String noteId = notifyData.getNoteId();
            final double win = notifyData.getWin();
            final double totalWin = notifyData.getTotalWin();
            final double betAmount = notifyData.getBetAmount();
            if (betAmount > 0 && !StringUtil.isNullOrEmpty(noteId)) {
                final long start = TimeUtil.currentTimeMillis();
                if (notifyData.getType() == ChangeType.special.getType()
                        || notifyData.getType() == ChangeType.cost.getType()) {
                    ScriptLoader.getInstance().consumerScript("BettingVolumeScript",
                            (IBettingVolumeScript script) ->
                                    script.calculateBetBettingVolume(player, c_gameApi, noteId, currencyId, betAmount));
                }
                if (TimeUtil.currentTimeMillis() - start > 10) {
                    LOGGER.warn("calculateBetBettingVolume cost time：{}", TimeUtil.currentTimeMillis() - start);
                }
            }

            final C_CashBack c_cashBack = merchantData.findC_CashBack(this.getClass().getSimpleName(), c_gameApi.getType());
            if (c_cashBack == null) {
                return;
            }

            if (win > 0) {
                //TODO 统计赢分
                final Stats stats = player.getStats(currencyId);
                stats.incWin(win);
                //TODO 排行
                ScriptLoader.getInstance().consumerScript("RankScript",
                        (IActivityScript script) -> script.execute(player, ActivityMrg.RANK_WIN, currencyId, 0, win));

                final double amount = 0;
                ScriptLoader.getInstance().consumerScript("WageredRebatesScript",
                        (IActivityScript script) -> script.execute(player, c_gameApi, currencyId, amount, 0, win));
            }

            if (notifyData.getType() != ChangeType.Free.getType() && totalWin > 0) {
//                ScriptLoader.getInstance().consumerScript("RankWinScript",
//                        (IRankScript script) -> script.addBigWinRank(player, notifyData));
//                ScriptLoader.getInstance().consumerScript("RankWinScript",
//                        (IRankScript script) -> script.addLuckyWinRank(player, notifyData));
            }
        } catch (Exception e) {
            LOGGER.error("HallInnerNotifyCurrencyUpdateHandler", e);
        }
    }
}
