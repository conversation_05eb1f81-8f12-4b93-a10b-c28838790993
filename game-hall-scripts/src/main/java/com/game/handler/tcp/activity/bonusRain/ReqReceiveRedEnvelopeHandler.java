package com.game.handler.tcp.activity.bonusRain;

import com.game.c_entity.merchant.C_RedEnvelopeRain;
import com.game.c_entity.middleplatform.C_BaseExchangeRate;
import com.game.dao.player.PlayerDao;
import com.game.engine.io.handler.TcpHandler;
import com.game.engine.io.redis.RedisPoolManager;
import com.game.engine.log.GameLog;
import com.game.engine.math.BigDecimalUtils;
import com.game.engine.math.MathUtils;
import com.game.engine.script.IHandlerEntity;
import com.game.engine.script.ScriptLoader;
import com.game.engine.util.misc.Tuple2;
import com.game.engine.utils.GuidGeneratorUtils;
import com.game.engine.utils.MsgUtil;
import com.game.engine.utils.TimeUtil;
import com.game.entity.player.Player;
import com.game.entity.player.activity.redEnvelopeRain.RedEnvelopeRainInfo;
import com.game.enums.*;
import com.game.enums.redis.RedisAllGame;
import com.game.hall.main.HallServer;
import com.game.hall.mrg.ActivityMrg;
import com.game.hall.mrg.DataHallMrg;
import com.game.hall.mrg.MerchantData;
import com.game.hall.mrg.currency.CurrencyMrg;
import com.game.hall.mrg.currency.RewardRequest;
import com.game.hall.mrg.player.PlayerMrg;
import com.game.hall.script.IBonusScript;
import com.game.hall.script.IPlayerScript;
import com.game.manager.EntityDaoMrg;
import com.game.utils.VirtualThreadUtils;
import com.proto.ActivityMessage;
import com.proto.MIDMessage;
import io.lettuce.core.api.async.RedisAsyncCommands;
import io.netty.util.internal.StringUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

@IHandlerEntity(mid = MIDMessage.MID.ReqReceiveRedEnvelope_VALUE, msg = ActivityMessage.ReqReceiveRedEnvelopeMessage.class)
public class ReqReceiveRedEnvelopeHandler extends TcpHandler {
    private static final Logger LOGGER = LoggerFactory.getLogger(ReqReceiveRedEnvelopeHandler.class);

    @Override
    public void run() {
        final ActivityMessage.ResReceiveRedEnvelopeMessage.Builder res = ActivityMessage.ResReceiveRedEnvelopeMessage.newBuilder();
        res.setMsgID(MIDMessage.MID.ResReceiveRedEnvelope_VALUE);
        try {
            final ActivityMessage.ReqReceiveRedEnvelopeMessage req = (ActivityMessage.ReqReceiveRedEnvelopeMessage) getMessage();
            final int timePeriodId = req.getTimePeriodId();

            final Player player = PlayerMrg.getInstance().getOnlinePlayer(pid, this.getClass().getSimpleName());
            if (player == null) {
                res.setError(ErrorCode.Player_Offline.getCode());
                MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                return;
            }

            final MerchantData merchantData = DataHallMrg.getInstance().findMerchantData(this.getClass().getSimpleName(), player.getBusiness_no());
            if (merchantData == null) {
                res.setError(ErrorCode.Data_Error.getCode());
                MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                return;
            }

            final C_RedEnvelopeRain c_redEnvelopeRain = findOpen(player, merchantData);
            if (c_redEnvelopeRain == null) {
                res.setError(ErrorCode.Data_Error.getCode());
                MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                return;
            }

            final String timeZone = c_redEnvelopeRain.getTimeZone();

            int day = 0;
            if (!c_redEnvelopeRain.getMonthlys().isEmpty()) {
                day = TimeUtil.getDayOfMonth(timeZone);
            }
            if (day == 0 && !c_redEnvelopeRain.getCycles().isEmpty()) {
                day = TimeUtil.getDayOfWeek(timeZone);
            }

            //时间段
            final C_RedEnvelopeRain.TimePeriod timePeriod = c_redEnvelopeRain.getTimePeriodMap().get(timePeriodId);
            if (timePeriod == null) {
                res.setError(ErrorCode.Data_Error.getCode());
                MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                return;
            }

            final long time = TimeUtil.getTimeBeginOfToday(TimeUtil.currentTimeMillis(), c_redEnvelopeRain.getTimeZone());
            if (TimeUtil.currentTimeMillis() < time + timePeriod.getStartTime()) {
                res.setError(ErrorCode.Activity_NotStart.getCode());
                MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                return;
            }

            if (TimeUtil.currentTimeMillis() >= time + timePeriod.getEndTime()) {
                res.setError(ErrorCode.Activity_Ended.getCode());
                MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                return;
            }

            final RedEnvelopeRainInfo redEnvelopeRainInfo = player.getRedEnvelopeRainInfo();
            final C_RedEnvelopeRain.RedEnvelope firstRedEnvelope = c_redEnvelopeRain.getRedEnvelopeMap().values().stream().findFirst().get();
            final int w_currencyId = firstRedEnvelope.w_currencyId;
            double wageredAmount = 0;

            final int r_currencyId = firstRedEnvelope.r_currencyId;
            double rechargeAmount = 0;

            if (w_currencyId == Currency.USD.getCurrencyId()) {
                for (Map.Entry<Integer, Double> entry : redEnvelopeRainInfo.getWageredMap().entrySet()) {
                    final C_BaseExchangeRate c_baseExchangeRate = DataHallMrg.getInstance().findC_BaseExchangeRate(this.getClass().getSimpleName(), entry.getKey());
                    if (c_baseExchangeRate == null) {
                        continue;
                    }
                    wageredAmount = BigDecimalUtils.add(wageredAmount, BigDecimalUtils.mul(wageredAmount, c_baseExchangeRate.getExchangeRate(), 4), 4);
                }
            } else {
                wageredAmount = redEnvelopeRainInfo.getWageredMap().getOrDefault(player.getCurrencyId(), 0d);
            }

            if (r_currencyId == Currency.USD.getCurrencyId()) {
                for (Map.Entry<Integer, Double> entry : redEnvelopeRainInfo.getRechargeAmountMap().entrySet()) {
                    final C_BaseExchangeRate c_baseExchangeRate = DataHallMrg.getInstance().findC_BaseExchangeRate(this.getClass().getSimpleName(), entry.getKey());
                    if (c_baseExchangeRate == null) {
                        continue;
                    }
                    rechargeAmount = BigDecimalUtils.add(rechargeAmount, BigDecimalUtils.mul(rechargeAmount, c_baseExchangeRate.getExchangeRate(), 4), 4);
                }
            } else {
                rechargeAmount = redEnvelopeRainInfo.getRechargeAmountMap().getOrDefault(player.getCurrencyId(), 0d);
            }

            if (redEnvelopeRainInfo.getReceiveTimes() >= c_redEnvelopeRain.getDailyTimes()) {
                res.setError(ErrorCode.RedEnvelope_Finished.getCode());
                MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                return;
            }

            if (redEnvelopeRainInfo.getReceive().contains(timePeriod.id)) {
                res.setError(ErrorCode.Reward_Received.getCode());
                MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                return;
            }

            final C_RedEnvelopeRain.RedEnvelope redEnvelope = findRedEnvelope(player, c_redEnvelopeRain, day, timePeriodId,
                    w_currencyId, wageredAmount, r_currencyId, rechargeAmount);
            if (redEnvelope == null) {
                LOGGER.warn("playerId：{}，Conditions_Not_Met", player.get_id());
                res.setError(ErrorCode.RedEnvelope_Finished.getCode());
                MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                return;
            }

            final int finalDay = day;
            final String count = RedisPoolManager.getInstance().function(jedis ->
                    jedis.sync().get(RedisAllGame.Platform_All_BonusRain.getKey(player.getBusiness_no(), Integer.parseInt(finalDay + "" + timePeriodId), redEnvelope.id)));

            if (Integer.parseInt(StringUtil.isNullOrEmpty(count) ? "0" : count) >= redEnvelope.num) {
                res.setError(ErrorCode.RedEnvelope_Finished.getCode());
                MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                return;
            }

            final String rewardAmount = RedisPoolManager.getInstance().function(jedis ->
                    jedis.sync().get(RedisAllGame.Platform_All_BonusRainAmount.getKey(player.getBusiness_no(), Integer.parseInt(finalDay + "" + timePeriodId), 0)));

            //LOGGER.warn("timePeriodId：{}，redEnvelopeId：{}，rewardAmount：{},totalAmountLimit：{}", finalDay + "" + timePeriodId, redEnvelope.id, rewardAmount, c_redEnvelopeRain.getTotalAmountLimit());
            if (BigDecimalUtils.div(Double.parseDouble(StringUtil.isNullOrEmpty(rewardAmount) ? "0" : rewardAmount), 100, 2) >= c_redEnvelopeRain.getTotalAmountLimit()) {
                res.setError(ErrorCode.RedEnvelope_Finished.getCode());
                MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                return;
            }

            redEnvelopeRainInfo.setReceiveTimes(redEnvelopeRainInfo.getReceiveTimes() + 1);
            EntityDaoMrg.getInstance().getDao(PlayerDao.class)
                    .redEnvelopeRainDao.updateReceiveTimes(pid, redEnvelopeRainInfo);

            redEnvelopeRainInfo.getReceive().add(timePeriod.id);
            EntityDaoMrg.getInstance().getDao(PlayerDao.class)
                    .redEnvelopeRainDao.updateReceive(pid, redEnvelopeRainInfo);

            final RewardRequest rewardRequest = new RewardRequest();
            double reward = BigDecimalUtils.round(MathUtils.random(redEnvelope.getMin(), redEnvelope.getMax()), 2);
            if (reward >= c_redEnvelopeRain.getSingleAmountLimit()) {
                reward = c_redEnvelopeRain.getSingleAmountLimit();
            }

            rewardRequest.addCurrency(c_redEnvelopeRain.getCurrencyId(), reward);
            final RewardReason rewardReason = RewardReason.RedEnvelope;
            rewardReason.setSource(redEnvelope.id + "");
            CurrencyMrg.getInstance().rewardNoTest(player, rewardRequest.getBonusCurrencyMap(), rewardReason);

            final double finalReward = reward;
            RedisPoolManager.getInstance().asyncPipeline(commands -> {
                        final List<CompletableFuture<?>> futures = new ArrayList<>();
                        futures.add(commands.incrby(RedisAllGame.Platform_All_BonusRain.getKey(player.getBusiness_no(), Integer.parseInt(finalDay + "" + timePeriodId), redEnvelope.id), 1)
                                .toCompletableFuture());
                        futures.add(commands.incrby(RedisAllGame.Platform_All_BonusRainAmount.getKey(player.getBusiness_no(), Integer.parseInt(finalDay + "" + timePeriodId), 0), (long) (finalReward * 100))
                                .toCompletableFuture());
                        return futures;
                    }
            );

            //TODO 打码
//            final WithdrawStandard withdrawStandard = player.getWithdrawStandard(c_redEnvelopeRain.getCurrencyId());
//            withdrawStandard.incDrawStandard(BigDecimalUtils.mul(reward, c_redEnvelopeRain.getTurnoverMul(), 4));
            final TurnoverReason turnoverReason = TurnoverReason.RedEnvelope;
            turnoverReason.setSource(redEnvelope.id + "");
            ScriptLoader.getInstance().consumerScript("PlayerWithdrawStandardScript",
                    (IPlayerScript script) -> script.drawStandard(player, turnoverReason, c_redEnvelopeRain.getCurrencyId(), finalReward,
                            BigDecimalUtils.mul(finalReward, c_redEnvelopeRain.getTurnoverMul(), 4)));

            rewardRequest.writeToItemShowMsg(res::addRewardShow);
            MsgUtil.responseHttp(res.build(), res.getMsgID(), session);

            if (!rewardRequest.getCurrencyMap().isEmpty()) {
                final Tuple2<Integer, Double> tuple2 = rewardRequest.getReward();
                ScriptLoader.getInstance().consumerScript("BonusScript",
                        (IBonusScript script) -> script.addBonusNote(TransactionFrom.BonusRain, player, tuple2.getFirst(), tuple2.getSecond()));
            }

            //日志
            final GameLog playerActivityLog = new GameLog("platform_playerRedEnvelopeLog");
            playerActivityLog.append("number", GuidGeneratorUtils.generateOrderId())
                    .append("business_no", player.getBusiness_no())
                    .append("site", player.getWebSite())
                    .append("channel", player.getChannel())
                    .append("playerId", player.getPlayerId())
                    .append("playerName", player.getPlayerName())
                    .append("language", player.getLanguage())
                    .append("agentId", player.getAgentId())
                    .append("channelId", player.getChannelId())
                    .append("region", player.getRegisterRegion())
                    .append("activityId", ActivityMrg.RED_ENVELOPE_RAIN)
                    .append("activityUniqueId", c_redEnvelopeRain.getC_id())
                    .append("receiveTimePeriod", timePeriod.getRangeTime())
                    .append("currencyId", c_redEnvelopeRain.getCurrencyId())
                    .append("reward", reward)
                    .append("logTime", TimeUtil.currentTimeMillis());
            HallServer.getInstance().getLogProducerMrg().send(playerActivityLog);
        } catch (Exception e) {
            LOGGER.error("ReqReceiveRedEnvelopeHandler", e);
            res.setError(ErrorCode.Internal_Server_Error.getCode());
            MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
        }
    }

    private C_RedEnvelopeRain findOpen(Player player, MerchantData merchantData) {
        final Map<Integer, C_RedEnvelopeRain> c_redEnvelopeRainWeekMap = merchantData.getC_redEnvelopeRainWeekMap();
        for (C_RedEnvelopeRain c_redEnvelopeRain : c_redEnvelopeRainWeekMap.values()) {
            if (!c_redEnvelopeRain.getLanguages().contains(player.getLanguage())) {
                continue;
            }

            final String timeZone = c_redEnvelopeRain.getTimeZone();
            final int month = TimeUtil.getDayOfMonth(timeZone);
            if (c_redEnvelopeRain.getMonthlys().contains(month)) {
                return c_redEnvelopeRain;
            }
        }

        final Map<Integer, C_RedEnvelopeRain> c_redEnvelopeRainMap = merchantData.getC_redEnvelopeRainDayMap();
        for (C_RedEnvelopeRain c_redEnvelopeRain : c_redEnvelopeRainMap.values()) {
            if (!c_redEnvelopeRain.getLanguages().contains(player.getLanguage())) {
                continue;
            }

            final String timeZone = c_redEnvelopeRain.getTimeZone();
            final int day = TimeUtil.getDayOfWeek(timeZone);
            if (c_redEnvelopeRain.getCycles().contains(day)) {
                return c_redEnvelopeRain;
            }
        }
        return null;
    }

    private C_RedEnvelopeRain.RedEnvelope findRedEnvelope(Player player, C_RedEnvelopeRain c_redEnvelopeRain, int day, int timePeriodId,
                                                          int w_currencyId, double wagered, int r_currencyId, double recharge) {
        final Map<Integer, C_RedEnvelopeRain.RedEnvelope> redEnvelopeMap = c_redEnvelopeRain.getRedEnvelopeMap();
        int receiveType = c_redEnvelopeRain.getReceiveType(); // 获取红包领取类型

        for (C_RedEnvelopeRain.RedEnvelope c_redEnvelope : redEnvelopeMap.values()) {

            // 1. 获取当前红包的领取次数
            final String count = RedisPoolManager.getInstance().function(jedis ->
                    jedis.sync().get(RedisAllGame.Platform_All_BonusRain.getKey(player.getBusiness_no(), Integer.parseInt(day + "" + timePeriodId), c_redEnvelope.id)));

            // 2. 如果领取次数已达上限，则跳过
            if (Integer.parseInt(StringUtil.isNullOrEmpty(count) ? "0" : count) >= c_redEnvelope.num) {
                continue;
            }

            // 3. 货币类型检查
            if (w_currencyId != c_redEnvelope.getW_currencyId()) {
                continue;
            }
            if (r_currencyId != c_redEnvelope.getR_currencyId()) {
                continue;
            }

            // 4. 领取类型判断
            boolean wageredSatisfies = c_redEnvelope.getMinWagered() == 0 || wagered >= c_redEnvelope.getMinWagered();
            boolean rechargeSatisfies = c_redEnvelope.getMinRecharge() == 0 || recharge >= c_redEnvelope.getMinRecharge();

            if (receiveType == 1) {
                // 投注金额 OR 充值金额 满足一个即可
                if (wageredSatisfies || rechargeSatisfies) {
                    return c_redEnvelope;
                }
            } else if (receiveType == 2) {
                // 投注金额 AND 充值金额 都要满足
                if (wageredSatisfies && rechargeSatisfies) {
                    return c_redEnvelope;
                }
            } else if (receiveType == 3) {
                // 不限制，直接返回
                return c_redEnvelope;
            }
        }
        return null;
    }


}
