package com.game.handler.tcp.activity.luckSpin;

import com.game.c_entity.merchant.C_LuckSpin;
import com.game.c_entity.middleplatform.C_BaseMerchant;
import com.game.dao.activity.SpinBonusNoteDao;
import com.game.engine.io.handler.TcpHandler;
import com.game.engine.io.redis.RedisPoolManager;
import com.game.engine.math.BigDecimalUtils;
import com.game.engine.script.IHandlerEntity;
import com.game.engine.utils.MsgUtil;
import com.game.engine.utils.TimeUtil;
import com.game.entity.activity.SpinBonusNote;
import com.game.entity.player.Player;
import com.game.entity.player.activity.luckSpin.*;
import com.game.enums.ErrorCode;
import com.game.enums.redis.RedisAllGame;
import com.game.enums.redis.RedisHall;
import com.game.hall.mrg.ActivityMrg;
import com.game.hall.mrg.DataHallMrg;
import com.game.hall.mrg.CommonMrg;
import com.game.hall.mrg.MerchantData;
import com.game.hall.mrg.player.PlayerMrg;
import com.game.manager.EntityDaoMrg;
import com.proto.ActivityMessage;
import com.proto.MIDMessage;
import io.netty.util.internal.StringUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.time.DayOfWeek;
import java.util.List;
import java.util.Map;

@IHandlerEntity(mid = MIDMessage.MID.ReqLuckSpinData_VALUE, msg = ActivityMessage.ReqLuckSpinDataMessage.class)
public class ReqLuckSpinDataHandler extends TcpHandler {
    private static final Logger LOGGER = LoggerFactory.getLogger(ReqLuckSpinDataHandler.class);

    @Override
    public void run() {
        final ActivityMessage.ResLuckSpinDataMessage.Builder res = ActivityMessage.ResLuckSpinDataMessage.newBuilder();
        res.setMsgID(MIDMessage.MID.ResLuckSpinData_VALUE);
        try {
            final ActivityMessage.ReqLuckSpinDataMessage req = (ActivityMessage.ReqLuckSpinDataMessage) getMessage();
            final String host = req.getHost();
            final int language = req.getLanguage();

            final C_BaseMerchant c_baseHostMerchant = DataHallMrg.getInstance().findC_BaseHostMerchant(this.getClass().getSimpleName(), host);
            if (c_baseHostMerchant == null) {
                res.setError(ErrorCode.Data_Error.getCode());
                MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                return;
            }

            final String business_no = c_baseHostMerchant.getBusiness_no();
            final MerchantData merchantData = DataHallMrg.getInstance().findMerchantData(this.getClass().getSimpleName(), business_no);
            if (merchantData == null) {
                res.setError(ErrorCode.Data_Error.getCode());
                MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                return;
            }

            Player player = null;
            if (pid > 0) {
                player = PlayerMrg.getInstance().getOnlinePlayer(pid, this.getClass().getSimpleName());
                if (player == null) {
                    res.setError(ErrorCode.Player_Offline.getCode());
                    MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                    return;
                }
            }

            final ActivityMessage.LuckSpinList.Builder luckSpinList = ActivityMessage.LuckSpinList.newBuilder();
            for (Map.Entry<Integer, List<C_LuckSpin>> entry : merchantData.getC_luckSpinMap().entrySet()) {
                luckSpinList.clear();
                final int activityId = entry.getKey();
                final List<C_LuckSpin> c_luckSpinList = entry.getValue();
                C_LuckSpin c_luckSpin = null;
                for (C_LuckSpin luckSpin : c_luckSpinList) {
                    if (TimeUtil.currentTimeMillis() < luckSpin.getStartTime() || TimeUtil.currentTimeMillis() > luckSpin.getEndTime()) {
                        continue;
                    }
                    c_luckSpin = luckSpin;
                }
                if (c_luckSpin == null) {
                    continue;
                }

                if (!c_luckSpin.getLanguages().contains(language)) {
                    continue;
                }

                final C_LuckSpin.RuleData ruleData = c_luckSpin.getRuleDataMap().get(language);
                if (ruleData == null) {
                    continue;
                }

                luckSpinList.setActivityId(activityId)
                        .setTagSort(c_luckSpin.getTagSort())
                        .setActivityName(ruleData.getActivityName());
                for (C_LuckSpin luckSpin : c_luckSpinList) {
                    if (TimeUtil.currentTimeMillis() < luckSpin.getStartTime() || TimeUtil.currentTimeMillis() > luckSpin.getEndTime()) {
                        continue;
                    }
                    luckSpinList.addLuckSpinList(buildLuckSpinInfo(player, business_no, language, luckSpin));
                }
                res.addLuckSpinList(luckSpinList.build());
            }

            MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
        } catch (Exception e) {
            LOGGER.error("ReqLuckSpinDataHandler", e);
            res.setError(ErrorCode.Internal_Server_Error.getCode());
            MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
        }
    }


    /**
     * int32 activityId                         = 1; //活动id
     * int64 expiresTime                        = 2; //过期时间
     * DItemShow drawTotalReward                = 3; //已领取的总奖励
     * repeated SpinBonus  spinBonusList        = 4; //奖励记录
     * repeated Turntable turntables            = 5; //转盘数据
     * int64 startTime                          = 6; //活动开始时间
     * int64 endTime                            = 7; //活动结束时间
     * string desc                              = 8; //描述
     * int32 tagSort                            = 9; //标签排序
     * double needWager                         =10; //需要赌注（usd）
     * repeated DItemShow currentWager          =11; //当前赌注
     * int32 remainTimes                        =12; //剩余次数
     * int32 unLockVip                          =13; //解锁vip
     * int32 vipType                            =14; //vip类型
     * DItemShow initReward                     =15; //初始奖励
     * DItemShow availableReward                =16; //可领取奖励
     *
     * @param language
     * @param c_luckSpin
     * @return
     */
    private ActivityMessage.LuckSpinInfo buildLuckSpinInfo(Player player, String business_no, int language, C_LuckSpin c_luckSpin) {
        final ActivityMessage.LuckSpinInfo.Builder luckSpinInfo = ActivityMessage.LuckSpinInfo.newBuilder();
        {
            //邀请
            if (c_luckSpin.getActivityId() == ActivityMrg.LUCK_SPIN_REFERRAL) {
                final C_LuckSpin.InviteData inviteData = c_luckSpin.getInviteInfo();
                if (inviteData != null) {
                    double currentReward = 0;
                    if (player != null) {
                        final LuckSpinInfo referLuckSpinInfo = player.getLuckSpinInfo();
                        final LuckSpinData luckSpinData = referLuckSpinInfo.getLuckSpinData(c_luckSpin.getActivityId());
                        if (luckSpinData != null) {
                            currentReward = luckSpinData.getCurrentRewardsMap().getOrDefault(inviteData.getCurrencyId(), 0d);
                            if (luckSpinData.isFirst()) {
                                currentReward = BigDecimalUtils.add(currentReward, inviteData.getInitReward(), 4);
                            }
                            if (currentReward >= inviteData.getAvailableReward()) {
                                currentReward = inviteData.getAvailableReward();
                            }

                            final MerchantData merchantData = DataHallMrg.getInstance().findMerchantData(this.getClass().getSimpleName(), business_no);
                            final String referralLink = merchantData.findC_GlobalValue(this.getClass().getSimpleName(), "referralLink");
                            final String url = referralLink + "?" + "referralCode=" + player.getInvitationCode() + "-~." + "activity=" + c_luckSpin.getActivityId() + "_" + c_luckSpin.getC_id();

                            final String remainTimes = RedisPoolManager.getInstance().function(jedis ->
                                    jedis.sync().get(RedisHall.Platform_Role_Map_ReferralLuckSpin_RemainTimes.getKey(player.getPlayerId())));
                            luckSpinInfo.setRemainTimes(Integer.parseInt(StringUtil.isNullOrEmpty(remainTimes) ? "0" : remainTimes))
                                    .setFinish(luckSpinData.isFinish())
                                    .setExpiresTime(luckSpinData.getExpiredTime() == -1 ? 0 : luckSpinData.getExpiredTime())
                                    .setReferralLink(url);
                        }
                    }
                    luckSpinInfo.setInitReward(CommonMrg.buildDItemShow(inviteData.getCurrencyId(), currentReward))
                            .setAvailableReward(CommonMrg.buildDItemShow(inviteData.getCurrencyId(), inviteData.getAvailableReward()));
                }
            }
        }

        {
            if (c_luckSpin.getActivityId() == ActivityMrg.LUCK_SPIN_VIP) {
                //vip
                if (player != null) {
                    final LuckSpinInfo vipLuckSpinInfo = player.getLuckSpinInfo();
                    final LuckSpinData luckSpinData = vipLuckSpinInfo.getLuckSpinData(Integer.parseInt(c_luckSpin.getActivityId() + "" + c_luckSpin.getSubType()));
                    if (luckSpinData != null) {
                        final LuckCondition luckCondition = luckSpinData.getLuckCondition();
                        if (luckCondition != null) {
                            for (Map.Entry<Integer, Double> entry : luckCondition.getWageredMap().entrySet()) {
                                luckSpinInfo.addCurrentWager(CommonMrg.buildDItemShow(entry.getKey(), entry.getValue()));
                            }
                            for (Map.Entry<Integer, Double> entry : luckCondition.getRechargeAmountMap().entrySet()) {
                                luckSpinInfo.addCurrentRecharge(CommonMrg.buildDItemShow(entry.getKey(), entry.getValue()));
                            }
                            luckSpinInfo.setCurrentBetTimes(luckCondition.getBetTimes());
                        }
                        luckSpinInfo.setExpiresTime(luckSpinData.getExpiredTime() == -1 ? 0 : luckSpinData.getExpiredTime())
                                .setRemainTimes(luckSpinData.getRemainTimes())
                                .setFinish(luckSpinData.isFinish());
                    }
                }
            }
        }

        {
            //每日、每周
            if (c_luckSpin.getActivityId() == ActivityMrg.LUCK_SPIN_DAILY || c_luckSpin.getActivityId() == ActivityMrg.LUCK_SPIN_WEEKLY) {
                if (player != null) {
                    final LuckSpinInfo spinInfo = player.getLuckSpinInfo();
                    final LuckSpinData luckSpinData = spinInfo.getLuckSpinData(c_luckSpin.getActivityId());
                    if (luckSpinData != null) {
                        final LuckCondition luckCondition = luckSpinData.getLuckCondition();
                        if (luckCondition != null) {
                            for (Map.Entry<Integer, Double> entry : luckCondition.getWageredMap().entrySet()) {
                                luckSpinInfo.addCurrentWager(CommonMrg.buildDItemShow(entry.getKey(), entry.getValue()));
                            }
                            for (Map.Entry<Integer, Double> entry : luckCondition.getRechargeAmountMap().entrySet()) {
                                luckSpinInfo.addCurrentRecharge(CommonMrg.buildDItemShow(entry.getKey(), entry.getValue()));
                            }
                            luckSpinInfo.setCurrentBetTimes(luckCondition.getBetTimes());
                        }
                        luckSpinInfo.setRemainTimes(luckSpinData.getRemainTimes())
                                .setFinish(luckSpinData.isFinish());
                    }
                }
                if (c_luckSpin.getActivityId() == ActivityMrg.LUCK_SPIN_DAILY) {
                    luckSpinInfo.setExpiresTime(TimeUtil.getTimeEndOfToday());
                }
                if (c_luckSpin.getActivityId() == ActivityMrg.LUCK_SPIN_WEEKLY) {
                    luckSpinInfo.setExpiresTime(TimeUtil.getDayOfWeekEndTimestamp(DayOfWeek.SUNDAY));
                }
            }
        }

        final C_LuckSpin.ExtraCondition extraCondition = c_luckSpin.getExtraCondition();
        if (extraCondition != null) {
            luckSpinInfo.setNeedWager(extraCondition.getBetUsdAmount())
                    .setNeedRecharge(extraCondition.getRechargeUsdAmount())
                    .setNeedBetTimes(extraCondition.getBetTimes());
        }

        luckSpinInfo.setActivityId(c_luckSpin.getActivityId())
                .setCId(c_luckSpin.getC_id())
                .setTurntableType(c_luckSpin.getTurntableType())
                .setTagSort(c_luckSpin.getTagSort())
                .setStartTime(c_luckSpin.getStartTime())
                .setEndTime(c_luckSpin.getEndTime())
                .setUnLockVip(c_luckSpin.getVipLevel())
                .setSubType(c_luckSpin.getSubType());
        for (C_LuckSpin.Turntable turntable : c_luckSpin.getTurntableMap().values()) {
            luckSpinInfo.addTurntables(buildTurntable(turntable));
        }
        Map<String, String> dailyDrawTotalRewardMap = null;
        if (c_luckSpin.getActivityId() == ActivityMrg.LUCK_SPIN_VIP) {
            dailyDrawTotalRewardMap = RedisPoolManager.getInstance().function(jedis ->
                    jedis.sync().hgetall(RedisAllGame.Platform_All_LuckSpin.getKey(business_no, Integer.parseInt(c_luckSpin.getTurntableType() + "" + c_luckSpin.getSubType()))));
        } else {
            dailyDrawTotalRewardMap = RedisPoolManager.getInstance().function(jedis ->
                    jedis.sync().hgetall(RedisAllGame.Platform_All_LuckSpin.getKey(business_no, c_luckSpin.getTurntableType())));
        }

        final List<SpinBonusNote> spinBonusNotes = EntityDaoMrg.getInstance().getDao(SpinBonusNoteDao.class).loadSpinBonusNote(business_no, c_luckSpin.getTurntableType(), c_luckSpin.getSubType(), TimeUtil.currentTimeMillis() - 7 * TimeUtil.DAY, TimeUtil.currentTimeMillis(), 50);
        for (SpinBonusNote spinBonusNote : spinBonusNotes) {
            luckSpinInfo.addSpinBonusList(buildSpinBonus(spinBonusNote));
        }
        if (dailyDrawTotalRewardMap != null) {
            for (Map.Entry<String, String> entry : dailyDrawTotalRewardMap.entrySet()) {
                final int currencyId = Integer.parseInt(entry.getKey());
                final double drawTotalReward = BigDecimalUtils.div(Double.parseDouble(entry.getValue()), ActivityMrg.MULTIPLE, 4);
                luckSpinInfo.setDrawTotalReward(CommonMrg.buildDItemShow(currencyId, drawTotalReward));
            }
        }
        final C_LuckSpin.RuleData ruleData = c_luckSpin.getRuleDataMap().get(language);
        if (ruleData != null) {
            luckSpinInfo.setDesc(ruleData.desc)
                    .setSubName(ruleData.subName);
        }
        return luckSpinInfo.build();
    }

    private ActivityMessage.SpinBonus buildSpinBonus(SpinBonusNote spinBonusNote) {
        return ActivityMessage.SpinBonus.newBuilder()
                .setHeadId(spinBonusNote.getHeadId())
                .setUserName(spinBonusNote.getPlayerName())
                .setSpin(StringUtil.isNullOrEmpty(spinBonusNote.getSpin()) ? "" : spinBonusNote.getSpin())
                .setCurrencyId(spinBonusNote.getCurrencyId())
                .setPrize(spinBonusNote.getPrize())
                .build();
    }

    private ActivityMessage.Turntable buildTurntable(C_LuckSpin.Turntable cTurntable) {
        return ActivityMessage.Turntable.newBuilder()
                .setTurntableId(cTurntable.getId())
                .setItemId(cTurntable.getItemId())
                .setNum(cTurntable.getNum())
                .build();
    }
}
