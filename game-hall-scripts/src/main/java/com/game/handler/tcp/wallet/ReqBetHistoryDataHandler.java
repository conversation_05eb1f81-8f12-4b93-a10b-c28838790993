package com.game.handler.tcp.wallet;

import com.game.c_entity.merchant.C_GameApi;
import com.game.dao.game.GameNoteDao;
import com.game.engine.io.handler.TcpHandler;
import com.game.engine.math.BigDecimalUtils;
import com.game.engine.script.IHandlerEntity;
import com.game.engine.util.misc.Tuple2;
import com.game.engine.utils.MsgUtil;
import com.game.engine.utils.TimeUtil;
import com.game.entity.game.GameNote;
import com.game.entity.player.Player;
import com.game.enums.ErrorCode;
import com.game.hall.mrg.CommonMrg;
import com.game.hall.mrg.DataHallMrg;
import com.game.hall.mrg.MerchantData;
import com.game.hall.mrg.player.PlayerMrg;
import com.game.manager.EntityDaoMrg;
import com.proto.CommonMessage;
import com.proto.MIDMessage;
import com.proto.WalletMessage;
import io.netty.util.internal.StringUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;

@IHandlerEntity(mid = MIDMessage.MID.ReqBetHistoryData_VALUE, msg = WalletMessage.ReqBetHistoryDataMessage.class)
public class ReqBetHistoryDataHandler extends TcpHandler {
    private static final Logger LOGGER = LoggerFactory.getLogger(ReqBetHistoryDataHandler.class);

    @Override
    public void run() {
        final WalletMessage.ResBetHistoryDataMessage.Builder res = WalletMessage.ResBetHistoryDataMessage.newBuilder();
        res.setMsgID(MIDMessage.MID.ResBetHistoryData_VALUE);
        try {
            final Player player = PlayerMrg.getInstance().getOnlinePlayer(pid, this.getClass().getSimpleName());
            if (player == null) {
                res.setError(ErrorCode.Player_Offline.getCode());
                MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                return;
            }

            final MerchantData merchantData = DataHallMrg.getInstance().findMerchantData(this.getClass().getSimpleName(), player.getBusiness_no());
            if (merchantData == null) {
                res.setError(ErrorCode.Data_Error.getCode());
                MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                return;
            }

            final WalletMessage.ReqBetHistoryDataMessage req = (WalletMessage.ReqBetHistoryDataMessage) getMessage();
            final int language = req.getLanguage();
            final int gameId = req.getGameId();
            final int gameType = req.getGameType();
            final int platformId = req.getPlatformId();
            final int assets = req.getAssets();
            final int past = req.getPast();
            final int page = req.getPage();
            final int pageSize = req.getPageSize();

            long start = TimeUtil.currentTimeMillis();
            long end = TimeUtil.currentTimeMillis();
            switch (past) {
                case 1: //24
                    start -= TimeUtil.DAY;
                    break;
                case 2: //7
                    start -= 7 * TimeUtil.DAY;
                    break;
                case 3: //30
                    start -= 30 * TimeUtil.DAY;
                    break;
                case 4: //60
                    start -= 60 * TimeUtil.DAY;
                    break;
                case 5: //90
                    start -= 90 * TimeUtil.DAY;
                    break;
                default:
                    start = req.getStatTime();
                    end = req.getEndTime();
                    break;
            }
            res.setGameType(gameType)
                    .setAssets(assets)
                    .setPast(past)
                    .setPage(page)
                    .setPageSize(pageSize);
            final int skip = (page - 1) * pageSize;
            final Tuple2<Integer, List<GameNote>> tuple2 = EntityDaoMrg.getInstance().getDao(GameNoteDao.class).loadHistoryGameNote(player, gameId, gameType, platformId, assets, start, end, skip, pageSize);
            final int total = tuple2.getFirst();
            final List<GameNote> gameNoteList = tuple2.getSecond();
            res.setTotal(total)
                    .setTotalPage(CommonMrg.totalPage(total, pageSize));

            for (GameNote gameNote : gameNoteList) {
                final C_GameApi c_gameApi = merchantData.findC_GameApi(this.getClass().getSimpleName(), gameNote.getGameId());
                if (c_gameApi != null && c_gameApi.setLanguageGameApiData(language)) {
                    res.addBetList(buildGameNote(c_gameApi, gameNote));
                }
            }

            MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
        } catch (Exception e) {
            LOGGER.error("ReqBetHistoryDataHandler", e);
            res.setError(ErrorCode.Internal_Server_Error.getCode());
            MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
        }
    }

    /**
     * string gameName           = 1; //游戏名字
     * int32 currencyId          = 2; //货币id
     * double amount             = 3; //金额
     * int64 time                = 4; //时间
     * double payout             = 5; //赔率
     * double profit             = 6; //利润
     *
     * @param gameNote
     * @return
     */
    private CommonMessage.BetInfo buildGameNote(C_GameApi c_gameApi, GameNote gameNote) {
        final CommonMessage.BetInfo.Builder betInfo = CommonMessage.BetInfo.newBuilder()
                .setGameName(StringUtil.isNullOrEmpty(c_gameApi.getGameName()) ? "" : c_gameApi.getGameName())
                .setHeadId(Integer.parseInt(StringUtil.isNullOrEmpty(gameNote.getHeadId()) ? "0" : gameNote.getHeadId()))
                .setPlayerName(gameNote.getPlayerName())
                .setCurrencyId(gameNote.getCurrencyId())
                .setAmount(gameNote.getValidBets())
                .setTime(gameNote.getCreateTime())
                .setPayout(BigDecimalUtils.sub(gameNote.getWin(), gameNote.getValidBets(), 4))
                .setGameType(gameNote.getGameType())
                .setNoteId(gameNote.getNoteId() + "")
                .setGameId(gameNote.getGameId());
        if (gameNote.getValidBets() == 0) {
            betInfo.setMul(0);
        } else {
            betInfo.setMul(BigDecimalUtils.div(gameNote.getWin(), gameNote.getValidBets(), 4));
        }
        return betInfo.build();
    }
}
