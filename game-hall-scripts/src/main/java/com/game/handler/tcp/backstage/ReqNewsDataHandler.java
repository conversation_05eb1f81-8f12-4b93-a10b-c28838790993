package com.game.handler.tcp.backstage;

import com.game.c_entity.merchant.C_News;
import com.game.engine.io.handler.TcpHandler;
import com.game.engine.script.IHandlerEntity;
import com.game.engine.utils.MsgUtil;
import com.game.entity.player.Player;
import com.game.enums.ErrorCode;
import com.game.hall.mrg.DataHallMrg;
import com.game.hall.mrg.MerchantData;
import com.game.hall.mrg.player.PlayerMrg;
import com.proto.BackStageMessage;
import com.proto.MIDMessage;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;

@IHandlerEntity(mid = MIDMessage.MID.ReqNewsData_VALUE, msg = BackStageMessage.ReqNewsDataMessage.class)
public class ReqNewsDataHandler extends TcpHandler {
    private static final Logger LOGGER = LoggerFactory.getLogger(ReqNewsDataHandler.class);

    @Override
    public void run() {
        final BackStageMessage.ResNewsDataMessage.Builder res = BackStageMessage.ResNewsDataMessage.newBuilder();
        res.setMsgID(MIDMessage.MID.ResNewsData_VALUE);
        try {
            final Player player = PlayerMrg.getInstance().getOnlinePlayer(pid, this.getClass().getName());
            if (player == null) {
                res.setError(ErrorCode.Player_Offline.getCode());
                MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                return;
            }

            final MerchantData merchantData = DataHallMrg.getInstance().findMerchantData(this.getClass().getSimpleName(), player.getBusiness_no());
            if (merchantData == null) {
                res.setError(ErrorCode.Data_Error.getCode());
                MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                return;
            }

            final List<C_News> c_newsList = merchantData.getC_newsMap().get(player.getLanguage());
            if (c_newsList != null && !c_newsList.isEmpty()) {
                for (C_News c_news : c_newsList) {
                    res.addNewsInfo(buildNews(c_news));
                }
            }

            MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
        } catch (Exception e) {
            LOGGER.error("ReqNewsDataHandler", e);
            res.setError(ErrorCode.Internal_Server_Error.getCode());
            MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
        }
    }


    /**
     * string code                = 1; //新闻编码
     * string channel             = 2; //频道
     * string language            = 3; //语言
     * string fileUrl             = 4; //图片地址
     * string title               = 5; //标题
     * string subtitle            = 6; //副标题
     * string abstracts           = 7; //摘要
     * string content             = 8; //内容
     * repeated int32 tag         = 9; //标签
     * int32 status               =10; //状态 1.上架 0.下架
     * int32 newsId               =11; //新闻id
     *
     * @param c_news
     * @return
     */
    private BackStageMessage.NewsInfo buildNews(C_News c_news) {
        return BackStageMessage.NewsInfo.newBuilder()
                .setNewsId(c_news.getNewsId())
                .setCode(c_news.getCode())
                .setChannel(c_news.getChannel())
                .setFileUrl(c_news.getFileUrl())
                .setTitle(c_news.getTitle())
                .setSubtitle(c_news.getSubtitle())
                .setAbstracts(c_news.getAbstracts())
                .setContent(c_news.getContent())
                .addAllTag(c_news.getTag())
                .setStatus(c_news.getStatus())
                .build();
    }
}
