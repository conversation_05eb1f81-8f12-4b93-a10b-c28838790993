package com.game.handler.tcp.quest;

import com.game.engine.io.handler.TcpHandler;
import com.game.engine.script.IHandlerEntity;
import com.game.engine.utils.MsgUtil;
import com.game.engine.utils.TimeUtil;
import com.game.entity.player.Player;
import com.game.entity.player.quest.QuestInfo;
import com.game.entity.player.quest.SingleQuestInfo;
import com.game.enums.ErrorCode;
import com.game.hall.mrg.player.PlayerMrg;
import com.game.hall.mrg.quest.QuestMrg;
import com.proto.MIDMessage;
import com.proto.QuestMessage;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.time.DayOfWeek;
import java.util.Map;

@IHandlerEntity(mid = MIDMessage.MID.ReqQuestData_VALUE, msg = QuestMessage.ReqQuestDataMessage.class)
public class ReqQuestDataHandler extends TcpHandler {
    private static final Logger LOGGER = LoggerFactory.getLogger(ReqQuestDataHandler.class);

    @Override
    public void run() {
        final QuestMessage.ResQuestDataMessage.Builder res = QuestMessage.ResQuestDataMessage.newBuilder();
        res.setMsgID(MIDMessage.MID.ResQuestData_VALUE);
        try {
            final Player player = PlayerMrg.getInstance().getOnlinePlayer(pid, this.getClass().getSimpleName());
            if (player == null) {
                res.setError(ErrorCode.Player_Offline.getCode());
                MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                return;
            }
            final QuestInfo questInfo = player.getQuestInfo();
            final Map<Integer, Double> accumulatedRewards = questInfo.getAccumulatedRewards();
            for (Map.Entry<Integer, Double> entry : accumulatedRewards.entrySet()) {
                res.setAccumulatedRewards(QuestMrg.getInstance().buildDItemShow(entry.getKey(), entry.getValue()));
            }
            res.setCurrentTime(TimeUtil.currentTimeMillis())
                    .setDailyEndTime(TimeUtil.getTimeEndOfToday())
                    .setWeeklyEndTime(TimeUtil.getDayOfWeekEndTimestamp(DayOfWeek.SUNDAY));
            for (SingleQuestInfo singleQuestInfo : questInfo.getQuestInfoMap().values()) {
                final QuestMessage.QuestInfo info = QuestMrg.getInstance().buildQuestInfo(player, singleQuestInfo);
                if (info != null) {
                    res.addQuestList(info);
                }
            }
            MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
        } catch (Exception e) {
            LOGGER.error("ReqQuestDataHandler", e);
            res.setError(ErrorCode.Internal_Server_Error.getCode());
            MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
        }
    }
}
