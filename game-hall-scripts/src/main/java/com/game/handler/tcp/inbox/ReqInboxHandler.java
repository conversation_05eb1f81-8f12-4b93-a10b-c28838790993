package com.game.handler.tcp.inbox;

import com.game.engine.io.handler.TcpHandler;
import com.game.engine.script.IHandlerEntity;
import com.game.engine.script.ScriptLoader;
import com.game.engine.utils.MsgUtil;
import com.game.entity.player.Player;
import com.game.entity.player.inbox.Inbox;
import com.game.entity.player.inbox.InboxInfo;
import com.game.enums.ErrorCode;
import com.game.hall.mrg.player.PlayerMrg;
import com.game.hall.script.IInboxScript;
import com.proto.InboxMessage;
import com.proto.MIDMessage;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

@IHandlerEntity(mid = MIDMessage.MID.ReqInboxData_VALUE, msg = InboxMessage.ReqInboxDataMessage.class)
public class ReqInboxHandler extends TcpHandler {
    private static final Logger LOGGER = LoggerFactory.getLogger(ReqInboxHandler.class);

    @Override
    public void run() {
        final InboxMessage.ResInboxDataMessage.Builder res = InboxMessage.ResInboxDataMessage.newBuilder();
        res.setMsgID(MIDMessage.MID.ResInboxData_VALUE);
        try {
            final Player player = PlayerMrg.getInstance().getOnlinePlayer(pid, this.getClass().getSimpleName());
            if (player == null) {
                res.setError(ErrorCode.Player_Offline.getCode());
                MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                return;
            }

            ScriptLoader.getInstance().consumerScript("InboxScript",
                    (IInboxScript script) -> script.sendPubMail(player));

            final InboxInfo inboxInfo = player.getInboxInfo();
            final List<Inbox> inboxList = new ArrayList<>(inboxInfo.getInboxMap().values());
            Collections.reverse(inboxList);
            final List<Inbox> inboxAll = inboxList.stream()
                    .filter(inbox -> inbox.getLanguage() == player.getLanguage())
                    .limit(50)
                    .toList();
            for (Inbox inbox : inboxAll) {
                res.addInboxList(ScriptLoader.getInstance().functionScript("InboxScript",
                        (IInboxScript script) -> script.builderInboxInfo(inbox)));
            }
            MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
        } catch (Exception e) {
            LOGGER.error("ReqInboxDataHandler", e);
            res.setError(ErrorCode.Internal_Server_Error.getCode());
            MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
        }
    }

}
