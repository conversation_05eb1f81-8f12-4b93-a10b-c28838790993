package com.game.handler.tcp.affiliate;

import com.game.engine.io.handler.TcpHandler;
import com.game.engine.script.IHandlerEntity;
import com.game.engine.utils.MsgUtil;
import com.game.entity.player.Player;
import com.game.entity.player.promote.PlayerPromote;
import com.game.entity.player.promote.ThreeLevelRewards;
import com.game.enums.ErrorCode;
import com.game.hall.mrg.player.PlayerMrg;
import com.proto.AffiliateMessage;
import com.proto.MIDMessage;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Map;


@IHandlerEntity(mid = MIDMessage.MID.ReqThreeLevelRewardData_VALUE, msg = AffiliateMessage.ReqThreeLevelRewardDataMessage.class)
public class ReqThreeLevelRewardDataHandler extends TcpHandler {
    private static final Logger LOGGER = LoggerFactory.getLogger(ReqThreeLevelRewardDataHandler.class);

    @Override
    public void run() {
        final AffiliateMessage.ResThreeLevelRewardDataMessage.Builder res = AffiliateMessage.ResThreeLevelRewardDataMessage.newBuilder();
        res.setMsgID(MIDMessage.MID.ResThreeLevelRewardData_VALUE);
        try {
            final Player player = PlayerMrg.getInstance().getOnlinePlayer(pid, this.getClass().getSimpleName());
            if (player == null) {
                res.setError(ErrorCode.Player_Offline.getCode());
                MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                return;
            }

            final PlayerPromote playerPromote = player.getPlayerPromote();
            for (Map.Entry<Integer, ThreeLevelRewards> entry : playerPromote.getThreeLevelRewardsMap().entrySet()) {
                res.addReward(buildThreeLevelReward(entry.getKey(), entry.getValue()));
            }
            MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
        } catch (Exception e) {
            LOGGER.error("ReqThreeLevelRewardDataHandler", e);
            res.setError(ErrorCode.Internal_Server_Error.getCode());
            MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
        }
    }

    private AffiliateMessage.ThreeLevelReward buildThreeLevelReward(int currencyId, ThreeLevelRewards threeLevelRewards) {
        return AffiliateMessage.ThreeLevelReward.newBuilder()
                .setCurrencyId(currencyId)
                .setAvailable(threeLevelRewards.getAvailable())
                .setTotalReceived(threeLevelRewards.getTotalReceived())
                .build();
    }
}
