//package com.game.handler.tcp.inner;
//
//import com.game.c_entity.merchant.C_GameApi;
//import com.game.engine.io.handler.TcpHandler;
//import com.game.engine.script.IHandlerEntity;
//import com.game.engine.script.ScriptLoader;
//import com.game.engine.utils.TimeUtil;
//import com.game.entity.player.Player;
//import com.game.entity.player.stats.Stats;
//import com.game.enums.*;
//import com.game.hall.main.HallServer;
//import com.game.hall.mrg.ActivityMrg;
//import com.game.hall.mrg.DataHallMrg;
//import com.game.hall.mrg.MerchantData;
//import com.game.hall.mrg.player.PlayerMrg;
//import com.game.hall.script.IBettingVolumeScript;
//import com.game.hall.script.IRankScript;
//import com.game.hall.script.activity.IActivityScript;
//import com.proto.InnerMessage;
//import com.proto.MIDMessage;
//import io.netty.util.internal.StringUtil;
//import org.slf4j.Logger;
//import org.slf4j.LoggerFactory;
//
//@IHandlerEntity(mid = MIDMessage.MID.InnerNotifyGameDataUpdate_VALUE, msg = InnerMessage.InnerNotifyGameDataUpdateMessage.class)
//public class HallInnerNotifyGameUpdateHandler extends TcpHandler {
//    private static final Logger LOGGER = LoggerFactory.getLogger(HallInnerNotifyGameUpdateHandler.class);
//
//    @Override
//    public void run() {
//        final InnerMessage.InnerNotifyGameDataUpdateMessage.Builder res = InnerMessage.InnerNotifyGameDataUpdateMessage.newBuilder();
//        res.setMsgID(MIDMessage.MID.InnerNotifyGameDataUpdate_VALUE);
//        try {
//            final InnerMessage.InnerNotifyGameDataUpdateMessage req = (InnerMessage.InnerNotifyGameDataUpdateMessage) getMessage();
//            final InnerMessage.NotifyData notifyData = req.getNotifyData();
//            final int gameId = notifyData.getGameId();
//
//            final Player player = PlayerMrg.getInstance().findDbPlayer(pid);
//            if (player == null) {
//                LOGGER.warn("HallInnerNotifyGameDataUpdateHandler，playerId：{}，not exist", pid);
//                return;
//            }
//
//            final MerchantData merchantData = DataHallMrg.getInstance().findMerchantData(this.getClass().getSimpleName(), player.getBusiness_no());
//            if (merchantData == null) {
//                return;
//            }
//
//            final C_GameApi c_gameApi = merchantData.findC_GameApi(this.getClass().getSimpleName(), gameId);
//            if (c_gameApi == null) {
//                return;
//            }
//
//
//        } catch (Exception e) {
//            LOGGER.error("HallInnerNotifyGameDataUpdateHandler", e);
//        }
//    }
//
//
//
//}
