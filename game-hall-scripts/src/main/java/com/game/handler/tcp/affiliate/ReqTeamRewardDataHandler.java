package com.game.handler.tcp.affiliate;

import com.game.engine.io.handler.TcpHandler;
import com.game.engine.script.IHandlerEntity;
import com.game.engine.utils.MsgUtil;
import com.game.entity.player.Player;
import com.game.entity.player.promote.PlayerPromote;
import com.game.entity.player.promote.TeamRewards;
import com.game.enums.ErrorCode;
import com.game.hall.mrg.player.PlayerMrg;
import com.proto.AffiliateMessage;
import com.proto.MIDMessage;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Map;


@IHandlerEntity(mid = MIDMessage.MID.ReqTeamRewardData_VALUE, msg = AffiliateMessage.ReqTeamRewardDataMessage.class)
public class ReqTeamRewardDataHandler extends TcpHandler {
    private static final Logger LOGGER = LoggerFactory.getLogger(ReqTeamRewardDataHandler.class);

    @Override
    public void run() {
        final AffiliateMessage.ResTeamRewardDataMessage.Builder res = AffiliateMessage.ResTeamRewardDataMessage.newBuilder();
        res.setMsgID(MIDMessage.MID.ResTeamRewardData_VALUE);
        try {
            final Player player = PlayerMrg.getInstance().getOnlinePlayer(pid, this.getClass().getSimpleName());
            if (player == null) {
                res.setError(ErrorCode.Player_Offline.getCode());
                MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                return;
            }

            final PlayerPromote playerPromote = player.getPlayerPromote();
            for (Map.Entry<Integer, TeamRewards> entry : playerPromote.getTeamRewardsMap().entrySet()) {
                res.addReward(buildTeamReward(entry.getKey(), entry.getValue()));
            }
            MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
        } catch (Exception e) {
            LOGGER.error("ReqTeamRewardDataHandler", e);
            res.setError(ErrorCode.Internal_Server_Error.getCode());
            MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
        }
    }

    private AffiliateMessage.TeamReward buildTeamReward(int currencyId, TeamRewards teamRewards) {
        return AffiliateMessage.TeamReward.newBuilder()
                .setCurrencyId(currencyId)
                .setAvailable(teamRewards.getAvailable())
                .setTotalReceived(teamRewards.getTotalReceived())
                .build();
    }
}
