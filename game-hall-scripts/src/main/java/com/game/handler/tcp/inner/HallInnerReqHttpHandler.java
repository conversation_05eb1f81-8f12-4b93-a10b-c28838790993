package com.game.handler.tcp.inner;

import com.game.engine.io.handler.IHandler;
import com.game.engine.io.handler.TcpHandler;
import com.game.engine.io.message.HttpMessageBean;
import com.game.engine.script.IHandlerEntity;
import com.game.engine.script.ScriptLoader;
import com.game.engine.utils.JsonUtils;
import com.game.engine.utils.TimeUtil;
import com.game.hall.main.HallServer;
import com.game.utils.VirtualThreadUtils;
import com.proto.InnerMessage;
import com.proto.MIDMessage;
import io.netty.util.internal.StringUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.LinkedHashMap;


@IHandlerEntity(mid = MIDMessage.MID.InnerReqHttpHandler_VALUE, msg = InnerMessage.InnerReqHttpHandlerMessage.class)
public class HallInnerReqHttpHandler extends TcpHandler {
    private static final Logger LOGGER = LoggerFactory.getLogger(HallInnerReqHttpHandler.class);

    @Override
    public void run() {
        final InnerMessage.InnerReqHttpHandlerMessage innerHttpHandler = (InnerMessage.InnerReqHttpHandlerMessage) getMessage();
        final String params = innerHttpHandler.getParams();
        final String requestPath = innerHttpHandler.getRequestPath();

        final HttpMessageBean httpMessageBean = ScriptLoader.getInstance().getHttpMessageBean(requestPath);
        if (httpMessageBean == null) {
            LOGGER.warn("http requestPath not register：{}", requestPath);
            return;
        }

        try {
            @SuppressWarnings("unchecked") final LinkedHashMap<String, Object> paramsMap = JsonUtils.readFromJson(params, LinkedHashMap.class);
            final IHandler handler = httpMessageBean.newHandler();
            handler.setSession(session);
            handler.setCreateTime(TimeUtil.currentTimeMillis());
            handler.setParamsMap(paramsMap);
            handler.setPid(pid);
            final String simpleName = handler.getClass().getSimpleName();
            if (simpleName.contains("Data") ||
                    simpleName.contains("ReloadAllConfig") ||
                    simpleName.contains("ReloadConstantConfig") ||
                    simpleName.contains("ReloadScripts")) {
                VirtualThreadUtils.execute(handler);
            } else if (simpleName.contains("ReloadMerchantConfig")
                    || simpleName.contains("ReloadMiddlePlatformConfig")) {
                HallServer.getInstance().getConfigEventLoop().execute(handler);
            } else {
                final String playerId = (String) paramsMap.get("playerId");
                if (!StringUtil.isNullOrEmpty(playerId)) {
                    HallServer.getInstance().asyncExecute(Long.parseLong(playerId), handler);
                } else {
                    handler.run();
                }
            }
        } catch (Exception e) {
            LOGGER.error("HallInnerReqHttpHandler", e);
        }
    }

}
