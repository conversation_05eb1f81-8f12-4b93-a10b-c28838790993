package com.game.handler.tcp.vip;

import com.game.c_entity.merchant.C_VipClub;
import com.game.engine.io.handler.TcpHandler;
import com.game.engine.script.IHandlerEntity;
import com.game.engine.utils.MsgUtil;
import com.game.engine.utils.TimeUtil;
import com.game.entity.player.Player;
import com.game.entity.player.vip.VipClub;
import com.game.enums.ErrorCode;
import com.game.hall.mrg.DataHallMrg;
import com.game.hall.mrg.MerchantData;
import com.game.hall.mrg.currency.CurrencyMrg;
import com.game.hall.mrg.player.PlayerMrg;
import com.proto.ActivityMessage;
import com.proto.MIDMessage;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@IHandlerEntity(mid = MIDMessage.MID.ReqVipData_VALUE, msg = ActivityMessage.ReqVipDataMessage.class)
public class ReqVipDataHandler extends TcpHandler {
    private static final Logger LOGGER = LoggerFactory.getLogger(ReqVipDataHandler.class);

    @Override
    public void run() {
        final ActivityMessage.ResVipDataMessage.Builder res = ActivityMessage.ResVipDataMessage.newBuilder();
        res.setMsgID(MIDMessage.MID.ResVipData_VALUE);
        try {
            final Player player = PlayerMrg.getInstance().getOnlinePlayer(pid, this.getClass().getSimpleName());
            if (player == null) {
                res.setError(ErrorCode.Player_Offline.getCode());
                MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                return;
            }

            final MerchantData merchantData = DataHallMrg.getInstance().findMerchantData(this.getClass().getSimpleName(), player.getBusiness_no());
            if (merchantData == null) {
                res.setError(ErrorCode.Data_Error.getCode());
                MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                return;
            }

            final VipClub vipClub = player.getVipClub();

            int vipLevel = vipClub.getVipLevel();
            if (vipClub.getVipLevel() == 0) {
                vipLevel = 1;
            }

            final C_VipClub c_vipClub = merchantData.findC_VipClub(this.getClass().getSimpleName(), vipLevel);
            if (c_vipClub == null) {
                MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                return;
            }

            res.setVipSignInInfo(buildVipSignInInfo(vipClub, c_vipClub))
                    .setReceiveRewardInfo(buildReceiveRewardInfo(player, c_vipClub, vipClub));

            MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
        } catch (Exception e) {
            LOGGER.error("ReqVipDataHandler", e);
            res.setError(ErrorCode.Internal_Server_Error.getCode());
            MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
        }
    }

    private ActivityMessage.VipSignInInfo buildVipSignInInfo(VipClub vipClub, C_VipClub c_vipClub) {
        final ActivityMessage.VipSignInInfo.Builder vipSignInInfo = ActivityMessage.VipSignInInfo.newBuilder();

        if (c_vipClub.getSignInRewardMap().isEmpty()) {
            return vipSignInInfo.build();
        }

        final int currDay = TimeUtil.getNaturalDays(TimeUtil.currentTimeMillis(), vipClub.getActivationTime(), c_vipClub.getTimeZone());
        vipSignInInfo.setCurrDay(currDay)
                .setActivation(vipClub.isActivation())
                .addAllReceiveDays(vipClub.getReceiveDays());
        for (C_VipClub.SignInReward signInReward : c_vipClub.getSignInRewardMap().values()) {
            vipSignInInfo.addSignList(buildSignInfo(signInReward));
        }
        return vipSignInInfo.build();
    }

    private ActivityMessage.SignInfo buildSignInfo(C_VipClub.SignInReward signInReward) {
        return ActivityMessage.SignInfo.newBuilder()
                .setDay(signInReward.day)
                .setReward(CurrencyMrg.getInstance().buildCurrency(signInReward.currencyId, signInReward.reward))
                .build();
    }

    private ActivityMessage.ReceiveRewardInfo buildReceiveRewardInfo(Player player, C_VipClub c_vipClub, VipClub vipClub) {
        final C_VipClub.ReceiveReward daily = c_vipClub.getReceiveRewardMap().get(1);
        final C_VipClub.ReceiveReward week = c_vipClub.getReceiveRewardMap().get(2);
        final C_VipClub.ReceiveReward month = c_vipClub.getReceiveRewardMap().get(3);

        final ActivityMessage.ReceiveRewardInfo.Builder receiveRewardInfo = ActivityMessage.ReceiveRewardInfo.newBuilder();
        receiveRewardInfo.setCurrencyId(player.getCurrencyId())
                .setDailyWagered(vipClub.getDailyWageredMap().getOrDefault(player.getCurrencyId(), 0d))
                .setWeeklyWagered(vipClub.getWeeklyWageredMap().getOrDefault(player.getCurrencyId(), 0d))
                .setMonthlyWagered(vipClub.getMonthlyWageredMap().getOrDefault(player.getCurrencyId(), 0d))
                .addAllReceiveReward(vipClub.getReceiveReward());
        if (daily != null) {
            final long dailyTime = TimeUtil.getTimeBeginOfToday(TimeUtil.currentTimeMillis(), c_vipClub.getTimeZone());
            receiveRewardInfo.setDailyTime(dailyTime + daily.getReceiveTime());
        }
        if (week != null) {
            final long weekTime = TimeUtil.getTimeBeginOfWeek(TimeUtil.currentTimeMillis(), c_vipClub.getTimeZone());
            receiveRewardInfo.setWeeklyTime(weekTime + week.getReceiveTime());
        }
        if (month != null) {
            final long monthTime = TimeUtil.getTimeBeginOfMonth(TimeUtil.currentTimeMillis(), c_vipClub.getTimeZone());
            receiveRewardInfo.setMonthlyTime(monthTime + month.getReceiveTime());
        }
        return receiveRewardInfo.build();
    }

}
