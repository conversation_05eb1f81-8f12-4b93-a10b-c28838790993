package com.game.handler.tcp.backstage;

import com.game.c_entity.merchant.*;
import com.game.c_entity.middleplatform.*;
import com.game.engine.io.handler.TcpHandler;
import com.game.engine.script.IHandlerEntity;
import com.game.engine.utils.JsonUtils;
import com.game.engine.utils.MsgUtil;
import com.game.entity.player.Player;
import com.game.enums.Currency;
import com.game.enums.ErrorCode;
import com.game.hall.mrg.DataHallMrg;
import com.game.hall.mrg.MerchantData;
import com.game.hall.mrg.player.PlayerMrg;
import com.proto.BackStageMessage;
import com.proto.CommonMessage;
import com.proto.MIDMessage;
import io.netty.util.internal.StringUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;
import java.util.Map;
import java.util.Optional;

@IHandlerEntity(mid = MIDMessage.MID.ReqConfigData_VALUE, msg = BackStageMessage.ReqConfigDataMessage.class)
public class ReqConfigDataHandler extends TcpHandler {
    private static final Logger LOGGER = LoggerFactory.getLogger(ReqConfigDataHandler.class);

    @Override
    public void run() {
        final BackStageMessage.ResConfigDataMessage.Builder res = BackStageMessage.ResConfigDataMessage.newBuilder();
        res.setMsgID(MIDMessage.MID.ResConfigData_VALUE);
        try {
            final BackStageMessage.ReqConfigDataMessage req = (BackStageMessage.ReqConfigDataMessage) getMessage();
            final String host = req.getHost();
            final int language = req.getLanguage();

            final C_BaseMerchant c_baseHostMerchant = DataHallMrg.getInstance().findC_BaseHostMerchant(this.getClass().getSimpleName(), host);
            if (c_baseHostMerchant == null) {
                res.setError(ErrorCode.Data_Error.getCode());
                MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                return;
            }

            final String business_no = c_baseHostMerchant.getBusiness_no();
            final MerchantData merchantData = DataHallMrg.getInstance().findMerchantData(this.getClass().getSimpleName(), business_no);
            if (merchantData == null) {
                res.setError(ErrorCode.Data_Error.getCode());
                MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                return;
            }

            if (req.getConfigType() > 0) {
                sendConfigType(req, merchantData);
                return;
            }

            try {
                final Map<Integer, C_Currency> c_currencyMap = merchantData.getC_currencyMap();
                if (c_currencyMap.size() == 1) {
                    final String currencyId = merchantData.findC_GlobalValue(this.getClass().getSimpleName(), "initCurrencyId");
                    if (!StringUtil.isNullOrEmpty(currencyId)) {
                        res.setShowCurrencyId(Integer.parseInt(currencyId));
                    }
                } else {
                    res.setShowCurrencyId(Currency.USD.getCurrencyId());
                }
            } catch (Exception e) {
                LOGGER.error("c_currency", e);
            }
            {
                try {
                    //汇率
                    for (C_BaseExchangeRate c_baseExchangeRate : DataHallMrg.getInstance().getC_baseExchangeRateMap().values()) {
                        res.addExchangeRateList(buildExchangeRate(c_baseExchangeRate));
                    }
                } catch (Exception e) {
                    LOGGER.error("c_baseExchangeRate", e);
                }
            }
            {
                try {
                    //货币
                    for (C_Currency c_currency : merchantData.getC_currencyMap().values()) {
                        res.addCItemList(buildCurrency(c_currency));
                    }
                } catch (Exception e) {
                    LOGGER.error("c_currency", e);
                }
            }
            {
                try {
                    //头像
                    for (C_BaseHead c_head : DataHallMrg.getInstance().getC_headMap().values()) {
                        res.addHeadList(buildHeadInfo(c_head));
                    }
                } catch (Exception e) {
                    LOGGER.error("c_head", e);
                }
            }
            {
                //vip
                try {
                    //充电福利
                    final String chargingBenefitsOpen = merchantData.findC_GlobalValue(this.getClass().getSimpleName(), "chargingBenefitsOpen");
                    //升级奖励
                    final String upgradeRewardsOpen = merchantData.findC_GlobalValue(this.getClass().getSimpleName(), "upgradeRewardsOpen");
                    //周返水
                    final String weeklyCashBackOpen = merchantData.findC_GlobalValue(this.getClass().getSimpleName(), "weeklyCashBackOpen");
                    //月返水
                    final String monthlyCashBackOpen = merchantData.findC_GlobalValue(this.getClass().getSimpleName(), "monthlyCashBackOpen");
                    res.setChargingBenefitsOpen(Boolean.parseBoolean(StringUtil.isNullOrEmpty(chargingBenefitsOpen) ? "false" : chargingBenefitsOpen))
                            .setUpgradeRewardsOpen(Boolean.parseBoolean(StringUtil.isNullOrEmpty(upgradeRewardsOpen) ? "false" : upgradeRewardsOpen))
                            .setWeeklyCashBackOpen(Boolean.parseBoolean(StringUtil.isNullOrEmpty(weeklyCashBackOpen) ? "false" : weeklyCashBackOpen))
                            .setMonthlyCashBackOpen(Boolean.parseBoolean(StringUtil.isNullOrEmpty(monthlyCashBackOpen) ? "false" : monthlyCashBackOpen));
                } catch (Exception e) {
                    LOGGER.error("c_vipClub", e);
                }
            }
            {
                try {
                    //banner
                    final List<C_Banner> c_bannerList = merchantData.getC_bannerMap().get(host);
                    if (c_bannerList != null) {
                        for (C_Banner c_banner : c_bannerList) {
                            if (pid > 0) {
                                final Player player = PlayerMrg.getInstance().getOnlinePlayerMap().get(pid);
                                if (player != null) {
                                    if (c_banner.channelLimit(player.getChannelId())) {
                                        continue;
                                    }
                                    if (c_banner.agentLimit(player.getAgentId())) {
                                        continue;
                                    }
                                }
                            }
                            res.addBannerList(buildBanner(c_banner));
                        }
                    }
                } catch (Exception e) {
                    LOGGER.error("c_banner", e);
                }
            }
            {
                try {
                    //弹窗
                    final List<C_Popup> c_popupList = merchantData.getC_popupMap().get(language);
                    if (c_popupList != null && !c_popupList.isEmpty()) {
                        for (C_Popup c_popup : c_popupList) {
                            if (pid > 0) {
                                final Player player = PlayerMrg.getInstance().getOnlinePlayerMap().get(pid);
                                if (player != null) {
                                    if (c_popup.channelLimit(player.getChannelId())) {
                                        continue;
                                    }
                                    if (c_popup.agentLimit(player.getAgentId())) {
                                        continue;
                                    }
                                }
                            }
                            res.addPopupList(buildPopup(c_popup));
                        }
                    }
                } catch (Exception e) {
                    LOGGER.error("c_popup", e);
                }
            }
            {
                try {
                    //帮助中心
                    final List<C_HelpCenter> c_helpCenterList = merchantData.getC_helpCenterMap().get(language);
                    if (c_helpCenterList != null && !c_helpCenterList.isEmpty()) {
                        for (C_HelpCenter c_helpCenter : c_helpCenterList) {
                            res.addHelpCenterInfo(buildHelpCenter(c_helpCenter));
                        }
                    }
                } catch (Exception e) {
                    LOGGER.error("c_helpCenter", e);
                }
            }
            {
                try {
                    //推荐奖励
                    final String referralRewardOpen = merchantData.findC_GlobalValue(this.getClass().getSimpleName(), "referralRewardOpen");
                    if (!StringUtil.isNullOrEmpty(referralRewardOpen) && Boolean.parseBoolean(referralRewardOpen)) {
                        for (C_ReferralReward c_referralReward : merchantData.getC_referralRewardMap().values()) {
                            res.addReferralRewardList(buildReferralReward(c_referralReward, merchantData));
                        }
                    }
                } catch (Exception e) {
                    LOGGER.error("c_referralReward", e);
                }
            }
            {
                try {
                    //语言
                    final C_BaseMerchant c_baseMerchant = DataHallMrg.getInstance().findC_BaseHostMerchant(this.getClass().getSimpleName(), host);
                    if (c_baseMerchant != null) {
                        for (int languageId : c_baseMerchant.getOpenLanguage()) {
                            final C_BaseLanguage c_baseLanguage = DataHallMrg.getInstance().findC_Language(this.getClass().getSimpleName(), languageId);
                            if (c_baseLanguage == null) {
                                continue;
                            }
                            res.addLanguageList(buildLanguageInfo(c_baseLanguage));
                        }
                    }
                } catch (Exception e) {
                    LOGGER.error("c_baseLanguage", e);
                }
            }
            {
                try {
                    final String customerModel = merchantData.findC_GlobalValue(this.getClass().getSimpleName(), "customerModel");
                    if (!StringUtil.isNullOrEmpty(customerModel)) {
                        if (Integer.parseInt(customerModel) == 1) {//客服
                            for (C_CustomerService c_customerService : merchantData.getC_customerServiceMap().values()) {
                                res.addCustomerServiceList(buildCustomerServiceInfo(c_customerService));
                            }
                        } else {
                            final String thirdPartyCustomer = merchantData.findC_GlobalValue(this.getClass().getSimpleName(), "thirdPartyCustomer");
                            res.setThirdPartyCustomer(StringUtil.isNullOrEmpty(thirdPartyCustomer) ? "" : thirdPartyCustomer);
                        }
                    }
                } catch (Exception e) {
                    LOGGER.error("c_customerService", e);
                }
            }
            {
                try {
                    //底部菜单
                    for (C_BottomMenu c_bottomMenu : merchantData.getC_bottomMenuMap().values()) {
                        res.addBottomMenuList(buildBottomMenuInfo(c_bottomMenu, language));
                    }
                } catch (Exception e) {
                    LOGGER.error("c_bottomMenu", e);
                }
            }
            {
                try {
                    //QA
                    for (C_QualityAssurance c_qualityAssurance : merchantData.getC_qualityAssuranceMap().values()) {
                        final BackStageMessage.QualityAssuranceInfo qualityAssuranceInfo = buildQualityAssuranceInfo(language, c_qualityAssurance);
                        if (qualityAssuranceInfo != null) {
                            res.addQualityAssuranceList(qualityAssuranceInfo);
                        }
                    }
                } catch (Exception e) {
                    LOGGER.error("c_qualityAssurance", e);
                }
            }
            {
                try {
                    //游戏类型
                    for (C_BaseGameType c_baseGameType : DataHallMrg.getInstance().getC_gameTypeMap().values()) {
                        final BackStageMessage.GameTypeInfo gameTypeInfo = buildGameTypeInfo(language, c_baseGameType);
                        if (gameTypeInfo != null) {
                            res.addGameTypeList(gameTypeInfo);
                        }
                    }
                } catch (Exception e) {
                    LOGGER.error("c_gameType", e);
                }
            }
            {
                try {
                    //邀请连接
                    for (C_InvitationLinks c_invitationLinks : merchantData.getC_invitationLinksMap().values()) {
                        res.addInvitationLinksList(buildInvitationLinksInfo(c_invitationLinks, language));
                    }
                } catch (Exception e) {
                    LOGGER.error("c_invitationLinks", e);
                }
            }
            {
                try {
                    //三方配置
                    final String domainName = "https://" + host;
                    List<C_ThreePartyLogin> c_threePartyLoginList = merchantData.findC_ThreePartyLogin(this.getClass().getSimpleName(), domainName);
                    for (C_ThreePartyLogin c_threePartyLogin : c_threePartyLoginList) {
                        res.addThreePartyLoginList(buildThreePartyLoginInfo(c_threePartyLogin));
                    }
                } catch (Exception e) {
                    LOGGER.error("c_threePartyLogin", e);
                }
            }
            {
                try {
                    //功能开启
                    for (int functionId : merchantData.getC_functionEnabledMap().keySet()) {
                        res.addFunctionId(functionId);
                    }
                } catch (Exception e) {
                    LOGGER.error("c_functionEnabled", e);
                }
            }
            {
                try {
                    //站点信息
                    final C_WebSiteInfo c_webSiteInfo = merchantData.findC_WebSiteInfo(this.getClass().getSimpleName(), host);
                    if (c_webSiteInfo != null) {
                        final C_WebSiteInfo.WebSiteInfo webSiteInfo = c_webSiteInfo.getWebSiteInfoMap().get(language);
                        if (webSiteInfo != null) {
                            res.setWebSiteData(buildWebSiteData(webSiteInfo));
                        }
                    }
                } catch (Exception e) {
                    LOGGER.error("c_webSiteInfo", e);
                }
            }
            {
                try {
                    //快捷入口
                    final List<C_QuickAccess> c_quickAccessList = merchantData.findC_QuickAccess(this.getClass().getSimpleName(), host);
                    for (C_QuickAccess c_quickAccess : c_quickAccessList) {
                        final C_QuickAccess.EntranceData entranceData = c_quickAccess.getEntranceDataMap().get(language);
                        if (entranceData == null) {
                            continue;
                        }
                        res.addQuickAccessList(buildQuickAccessInfoInfo(entranceData, c_quickAccess));
                    }
                } catch (Exception e) {
                    LOGGER.error("c_quickAccess", e);
                }
            }
            {
                try {
                    //中台维护公告
                    final Optional<C_BaseMaintainNotice> baseOptional = DataHallMrg.getInstance().getC_maintainNoticeMap().values().stream().findFirst();
                    if (baseOptional.isPresent()) {
                        final C_BaseMaintainNotice c_baseMaintainNotice = baseOptional.get();
                        final BackStageMessage.MaintainNoticeInfo maintainNoticeInfo = buildBaseMaintainNoticeInfo(c_baseMaintainNotice, language);
                        if (maintainNoticeInfo != null) {
                            res.setMaintainNoticeInfo(maintainNoticeInfo);
                        }
                    }

                    //维护公告
                    final Optional<C_MaintainNotice> optional = merchantData.getC_maintainNoticeMap().values().stream().findFirst();
                    if (baseOptional.isEmpty() && optional.isPresent()) {
                        final C_MaintainNotice c_maintainNotice = optional.get();
                        final BackStageMessage.MaintainNoticeInfo maintainNoticeInfo = buildMaintainNoticeInfo(c_maintainNotice, language);
                        if (maintainNoticeInfo != null) {
                            res.setMaintainNoticeInfo(maintainNoticeInfo);
                        }
                    }
                } catch (Exception e) {
                    LOGGER.error("c_maintainNotice", e);
                }
            }
            {
                try {
                    //pwa
                    final Optional<C_Pwa> optional = merchantData.getC_pwaMap().values().stream().findFirst();
                    if (optional.isPresent()) {
                        final C_Pwa c_pwa = optional.get();
                        final C_Pwa.PwaInfo pwaInfo = c_pwa.getPwaInfoMap().get(language);
                        if (pwaInfo != null) {
                            res.setPwaInfo(buildPwaInfo(pwaInfo));
                        }
                    }
                } catch (Exception e) {
                    LOGGER.error("c_pwa", e);
                }
            }
            {
                try {
                    final String phoneRegisterVerifyCodeOpen = merchantData.findC_GlobalValue(this.getClass().getSimpleName(), "phoneRegisterVerifyCodeOpen");
                    res.setPhoneRegisterVerifyCodeOpen(Boolean.parseBoolean(StringUtil.isNullOrEmpty(phoneRegisterVerifyCodeOpen) ? "false" : phoneRegisterVerifyCodeOpen));
                } catch (Exception e) {
                    LOGGER.error("c_global", e);
                }
            }
            {
                try {
                    //注册挽回
                    final Optional<C_RegisterRetrievePop> optional = merchantData.getC_registerRetrieveMap().values().stream().findFirst();
                    if (optional.isPresent()) {
                        final C_RegisterRetrievePop c_registerRetrieve = optional.get();
                        final C_RegisterRetrievePop.RegisterInfo registerInfo = c_registerRetrieve.getRegisterInfoMap().get(language);
                        if (registerInfo != null) {
                            res.addRegisterRetrieveList(buildRegisterRetrieveInfo(c_registerRetrieve, registerInfo));
                        }
                    }
                } catch (Exception e) {
                    LOGGER.error("c_registerRetrieve", e);
                }
            }
            {
                try {
                    //每日充值弹窗
                    final Optional<C_DailyRechargePop> optional = merchantData.getC_dailyRechargePopMap().values().stream().findFirst();
                    if (optional.isPresent()) {
                        final C_DailyRechargePop c_dailyRechargePop = optional.get();
                        final BackStageMessage.DailyRechargePopInfo dailyRechargePopInfo = buildDailyRechargePopInfo(c_dailyRechargePop, language);
                        if (dailyRechargePopInfo != null) {
                            res.addDailyRechargePopList(dailyRechargePopInfo);
                        }
                    }
                } catch (Exception e) {
                    LOGGER.error("c_dailyRechargePop", e);
                }
            }
            {
                try {
                    //限时首充弹窗
                    final Optional<C_FirstChargePop> optional = merchantData.getC_firstChargePopMap().values().stream().findFirst();
                    if (optional.isPresent()) {
                        final C_FirstChargePop c_firstChargePop = optional.get();
                        final BackStageMessage.FirstChargePopInfo firstChargePopInfo = buildFirstChargePopInfo(c_firstChargePop, language);
                        if (firstChargePopInfo != null) {
                            res.addFirstChargePopList(firstChargePopInfo);
                        }
                    }
                } catch (Exception e) {
                    LOGGER.error("c_firstChargePop", e);
                }
            }
            {
                try {
                    //游戏弹窗
                    final Optional<C_GamePop> optional = merchantData.getC_gamePopMap().values().stream().findFirst();
                    if (optional.isPresent()) {
                        final C_GamePop c_gamePop = optional.get();
                        final BackStageMessage.GamePopInfo gamePopInfo = buildGamePopInfo(c_gamePop, language);
                        if (gamePopInfo != null) {
                            res.addGamePopList(gamePopInfo);
                        }
                    }
                } catch (Exception e) {
                    LOGGER.error("c_gamePop", e);
                }
            }
            {
                try {
                    //公告
                    final Map<Integer, C_PubMail> c_bulletinMap = merchantData.getC_bulletinMap();
                    for (Map.Entry<Integer, C_PubMail> entry : c_bulletinMap.entrySet()) {
                        final C_PubMail c_pubMail = entry.getValue();
                        final CommonMessage.InboxInfo inboxInfo = builderBulletinInfo(c_pubMail, language);
                        if (inboxInfo != null) {
                            res.addBulletinList(inboxInfo);
                        }
                    }
                } catch (Exception e) {
                    LOGGER.error("c_bulletin", e);
                }
            }
            MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
        } catch (Exception e) {
            LOGGER.error("ReqConfigDataHandler", e);
            res.setError(ErrorCode.Internal_Server_Error.getCode());
            MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
        }
    }

    /**
     * int32 currencyId                     = 1; //货币
     * double usdExchangeRate               = 2; //实时美元汇率
     * double exchangeRateValue             = 3; //实时汇率差值
     * bool exchangeRateValueOpen           = 4; //汇率差值开关
     * double fixedExchangeRate             = 5; //固定汇率
     * bool fixedExchangeRateOpen           = 6; //固定汇率开关
     *
     * @param c_Base_exchangeRate
     * @return
     */
    private BackStageMessage.ExchangeRate buildExchangeRate(C_BaseExchangeRate c_Base_exchangeRate) {
        return BackStageMessage.ExchangeRate.newBuilder()
                .setCurrencyId(c_Base_exchangeRate.getCurrencyId())
                .setUsdExchangeRate(c_Base_exchangeRate.getExchangeRate())
                .build();
    }

    private CommonMessage.DCurrencyItem buildCurrency(C_Currency c_currency) {
        return CommonMessage.DCurrencyItem.newBuilder()
                .setCurrencyId(c_currency.getCurrencyId())
                .setName(c_currency.getCurrencyName())
                .setIcon(c_currency.getIcon())
                .setSymbol(c_currency.getSymbol())
                .build();
    }

    private BackStageMessage.HeadInfo buildHeadInfo(C_BaseHead c_head) {
        return BackStageMessage.HeadInfo.newBuilder()
                .setHeadId(c_head.getHeadId())
                .setFileUrl(c_head.getFileUrl())
                .build();
    }

    /**
     * int32 vipClubId                    = 1;
     * string icon                        = 2; //图标
     * int32 vipLevel                     = 3; //vip等级
     * int32 needExp                      = 4; //需要经验
     * string vipName                     = 5; //vip名字
     * string upLevelReward               = 6; //升级奖励 币种:数量
     * string recharge                    = 7; //充值100-200:0.01
     * double weeklyCashBackRate          = 8; //周返现比例 有效压住X1%X周返水比例
     * double monthlyCashBackRate         = 9; //月返现比例 有效压住X1%X月返水比例
     *
     * @param c_vipClub
     * @return
     */
    private BackStageMessage.VipClubInfo buildVipClub(C_VipClub c_vipClub) {
        final BackStageMessage.VipClubInfo.Builder vipClubInfo = BackStageMessage.VipClubInfo.newBuilder()
                .setVipClubId(c_vipClub.getVipClubId())
                .setIcon(c_vipClub.getIcon())
                .setVipLevel(c_vipClub.getVipLevel())
                .setNeedExp(c_vipClub.getNeedExp())
                .setVipName(c_vipClub.getVipName())
                .setUpLevelReward(c_vipClub.getUpLevelReward())
                .setWeeklyCashBackRate(c_vipClub.getWeeklyCashBackRate())
                .setMonthlyCashBackRate(c_vipClub.getMonthlyCashBackRate())
                .setVipHost(c_vipClub.isVipHost())
                .setLuxuryGiveaway(c_vipClub.isLuxuryGiveaway())
                .setVipSpin(c_vipClub.isVipSpin())
                .setDailyFreeWithdrawTimes(c_vipClub.getDailyWithdrawFreeTimes())
                .setNeedRecharge(c_vipClub.getNeedRecharge())
                .setPlayerCashBackOpen(c_vipClub.isPlayerCashBackOpen());
        for (C_VipClub.Tire tire : c_vipClub.getTireList()) {
            final BackStageMessage.Wagered.Builder wagered = BackStageMessage.Wagered.newBuilder();
            wagered.setCurrentTier(tire.getCurrentTier())
                    .setWageredMin(tire.getWagerMin())
                    .setWageredMax(tire.getWagerMax())
                    .setRate(tire.getRate());
            vipClubInfo.addWageredList(wagered.build());
        }
        for (C_VipClub.ReceiveReward receiveReward : c_vipClub.getReceiveRewardMap().values()) {
            vipClubInfo.addReceiveRewards(buildReceiveReward(receiveReward));
        }
        return vipClubInfo.build();
    }

    private BackStageMessage.ReceiveReward buildReceiveReward(C_VipClub.ReceiveReward reward) {
        return BackStageMessage.ReceiveReward.newBuilder()
                .setType(reward.id)
                .setCurrencyId(reward.currencyId)
                .setReward(reward.reward)
                .setWagered(reward.wagered)
                .build();
    }

    private BackStageMessage.BannerInfo buildBanner(C_Banner c_banner) {
        return BackStageMessage.BannerInfo.newBuilder()
                .setBannerId(c_banner.getBannerId())
                .setBannerName(c_banner.getBannerName())
                .setIndex(c_banner.getIndex())
                .setLanguage(c_banner.getLanguage())
                .setShowSeq(c_banner.getShowSeq())
                .setIsJump(c_banner.getIsJump())
                .setJumpType(c_banner.getJumpType())
                .setPopupLinks(c_banner.getPopupLinks())
                .setInnerLinks(c_banner.getInnerLinks())
                .setExternalLinks(c_banner.getExternalLinks())
                .setTitle(c_banner.getTitle())
                .setSubtitle(c_banner.getSubtitle())
                .setFileUrl(c_banner.getFileUrl())
                .setButton(c_banner.getButton())
                .setButtonWord(c_banner.getButtonWord())
                .setStartTime(c_banner.getStartTime())
                .setEndTime(c_banner.getEndTime())
                .setStatus(c_banner.getStatus())
                .setNotLoginJump(c_banner.isNotLoginJump())
                .setFirstRechargeCurrencyId(c_banner.getFirstRechargeCurrencyId())
                .setFirstRechargeAmount(c_banner.getFirstRechargeAmount())
                .build();
    }

    /**
     * string code                = 1; //帮助编码
     * string channel             = 2; //类型
     * string language            = 3; //语言
     * string fileUrl             = 4; //图片地址
     * string title               = 5; //标题
     * string subtitle            = 6; //副标题
     * string abstracts           = 7; //摘要
     * string content             = 8; //内容
     * repeated int32 tag         = 9; //标签
     * int32 status               =10; //状态 1.上架 0.下架
     * int32 helpId               =11; //帮助id
     *
     * @return
     */
    private BackStageMessage.HelpCenterInfo buildHelpCenter(C_HelpCenter c_helpCenter) {
        return BackStageMessage.HelpCenterInfo.newBuilder()
                .setSort(c_helpCenter.getSort())
                .setChannel(c_helpCenter.getChannel())
                .setFileUrl(c_helpCenter.getFileUrl())
                .setTitle(c_helpCenter.getTitle())
                .setSubtitle(c_helpCenter.getSubtitle())
                .setAbstracts(c_helpCenter.getAbstracts())
                .setContent(c_helpCenter.getContent())
                .addAllTag(c_helpCenter.getTag())
                .setStatus(c_helpCenter.getStatus())
                .setHelpId(c_helpCenter.getHelpId())
                .build();
    }

    private BackStageMessage.ReferralRewardInfo buildReferralReward(C_ReferralReward c_referralReward, MerchantData merchantData) {
        final BackStageMessage.ReferralRewardInfo.Builder referralRewardInfo = BackStageMessage.ReferralRewardInfo.newBuilder()
                .setCurrencyId(c_referralReward.getCurrencyId())
                .setVipLevels(c_referralReward.getVipLevel())
                .setUnLockReward(c_referralReward.getUnLockReward());
        final C_VipClub c_vipClub = merchantData.findC_VipClub(this.getClass().getSimpleName(), c_referralReward.getVipLevel());
        if (c_vipClub != null) {
            referralRewardInfo.setTotalWager(c_vipClub.getNeedExp());
        }
        return referralRewardInfo.build();
    }

    /**
     * int32 customerServiceId    = 1; //
     * string name                = 2; //
     * string icon                = 3;
     * string mediaName           = 4; //媒体
     * string contactDetails      = 5; //联系方式
     * string links               = 6; //链接
     * int32 showSort             = 7; //显示顺序
     * bool open                  = 8; //开启
     * int32 language             = 9; //语言
     *
     * @param c_customerService
     * @return
     */
    private BackStageMessage.CustomerServiceInfo buildCustomerServiceInfo(C_CustomerService c_customerService) {
        return BackStageMessage.CustomerServiceInfo.newBuilder()
                .setCustomerServiceId(c_customerService.getCustomerServiceId())
                .setName(c_customerService.getName())
                .setIcon(c_customerService.getIcon())
                .setMediaName(c_customerService.getMediaName())
                .setContactDetails(c_customerService.getContactDetails())
                .setLinks(c_customerService.getLinks())
                .setShowSort(c_customerService.getShowSort())
                .setOpen(c_customerService.isOpen())
                .setLanguage(c_customerService.getLanguage())
                .build();
    }

    /**
     * int32 popupId                   = 1;
     * string popupName                = 2; //弹窗名字
     * int32 language                  = 3; //语言
     * int32 triggerType               = 4; //弹窗触发类型 1.注册成功 2.游客访问 3.每日首次登录 4.余额不足 5.提现成功 6.充值成功
     * double insufficientBalance      = 5; //余额不足
     * int32 rechargePopup             = 6; //充值弹窗 1.首充未完成 2.二充未完成 3.三充未完成 4.其它
     * bool button                     = 7; //按钮
     * string buttonWord               = 8; //按钮文字
     * int32 jumpType                  = 9; //跳转类型 1.内连 2.外链
     * int32 popupLinks                =10; //弹框链接 1.任务 2.转盘 3.充值 4.客服（内连）
     * string innerLinks               =11; //内部链接（内连）
     * string externalLinks            =12; //外链接（外连）
     * int32 appearance                =13; //出现方式 1.逐渐 2.向左滑动 3.从小变大
     * bool autoPassOff                =14; //自动消失
     * int32 showTime                  =15; //显示时间 （秒）
     * int32 popupWay                  =16; //弹窗方式 1.居中公告弹窗 2.右下角小弹窗 3.右中引流弹窗
     * int32 popupPlatform             =17; //弹窗平台 1.pc 2.app
     * repeated string imageUrls       =18; //图片地址
     * string content                  =19; //内容
     * int64 startTime                 =20; //开始时间
     * int64 endTime                   =21; //结束时间
     *
     * @param c_popup
     * @return
     */
    private BackStageMessage.PopupInfo buildPopup(C_Popup c_popup) {
        final BackStageMessage.PopupInfo.Builder popupInfo = BackStageMessage.PopupInfo.newBuilder()
                .setPopupId(c_popup.getPopupId())
                .setTitle(c_popup.getTitle())
                .setPopupName(c_popup.getPopupName())
                .setLanguage(c_popup.getLanguage())
                .setTriggerType(c_popup.getTriggerType())
                .setCurrencyId(c_popup.getCurrencyId())
                .setInsufficientBalance(c_popup.getInsufficientBalance())
                .setRechargePopup(c_popup.getRechargePopup())
                .setButton(c_popup.isButton())
                .setButtonWord(c_popup.getButtonWord())
                .setAppearance(c_popup.getAppearance())
                .setAutoPassOff(c_popup.isAutoPassOff())
                .setShowTime(c_popup.getShowTime())
                .setPopupWay(c_popup.getPopupWay())
                .addAllPopupPlatform(c_popup.getPopupPlatform())
                .setContent(c_popup.getContent())
                .setStartTime(c_popup.getStartTime())
                .setEndTime(c_popup.getEndTime())
                .setSort(c_popup.getSort())
                .setUpdateTime(c_popup.getUpdateTime())
                .setRechargeTimeType(c_popup.getRechargeTimeType())
                .setRechargeTimes(c_popup.getRechargeTimes())
                .setRechargeAmountType(c_popup.getRechargeAmountType())
                .setRechargeAmount(c_popup.getRechargeAmount())
                .setShowHint(c_popup.isShowHint())
                .setFirstRechargeCurrencyId(c_popup.getFirstRechargeCurrencyId())
                .setFirstRechargeAmount(c_popup.getFirstRechargeAmount());
        for (C_Popup.PopupData popupData : c_popup.getPopupDataMap().values()) {
            popupInfo.addPopupDataList(buildPopupData(c_popup, popupData));
        }
        return popupInfo.build();
    }

    private BackStageMessage.PopupData buildPopupData(C_Popup c_popup, C_Popup.PopupData popupData) {
        final BackStageMessage.PopupData.Builder popupDataBuilder = BackStageMessage.PopupData.newBuilder();
        popupDataBuilder.setPopupType(c_popup.getPopupType())
                .setSystemPopup(c_popup.getSystemPopup())
                .setIsJump(popupData.getIsJump())
                .setImageUrls(popupData.getImageUrl())
                .setJumpType(popupData.getJumpType())
                .setPopupLinks(popupData.getPopupLinks())
                .setInnerLinks(popupData.getInnerLinks())
                .setExternalLinks(popupData.getExternalLinks())
                .setNotLoginJump(popupData.isNotLoginJump())
                .setFirstRechargeCurrencyId(popupData.getFirstRechargeCurrencyId())
                .setFirstRechargeAmount(popupData.getFirstRechargeAmount());
        ;
        return popupDataBuilder.build();
    }

    /**
     * int32 menuId                    = 1;
     * string menuName                 = 2; //菜单名字
     * int32 menuSort                  = 3; //菜单排序
     * string subMenuName              = 4; //子菜单名字
     * int32 type                      = 5; //类型 1.菜单 2.图标
     * int32 subMenuSort               = 6; //子菜单排序
     * int32 jumpType                  = 7; //跳转类型 1.内连 2.外链
     * int32 popupLinks                = 8; //弹框类型 1.任务 2.转盘 3.充值 4.客服
     * string innerLinks               = 9; //内部链接
     * string externalLinks            =10; //外链接
     * repeated IconInfo iconList      =11; //图标列表
     *
     * @return
     */
    private BackStageMessage.BottomMenuInfo buildBottomMenuInfo(C_BottomMenu c_bottomMenu, int language) {
        final BackStageMessage.BottomMenuInfo.Builder bottomMenuInfo = BackStageMessage.BottomMenuInfo.newBuilder();
        bottomMenuInfo.setMenuId(c_bottomMenu.getMenuId())
                .setMenuSort(c_bottomMenu.getMenuSort())
                .setType(c_bottomMenu.getType())
                .setSubMenuSort(c_bottomMenu.getSubMenuSort())
                .setJumpType(c_bottomMenu.getJumpType())
                .setPopupLinks(c_bottomMenu.getPopupLinks())
                .setInnerLinks(c_bottomMenu.getInnerLinks())
                .setExternalLinks(c_bottomMenu.getExternalLinks());
        for (C_BottomMenu.IconInfo iconInfo : c_bottomMenu.getIconList()) {
            bottomMenuInfo.addIconList(buildIconInfo(iconInfo));
        }
        final C_BottomMenu.MenuNameInfo menuNameInfo = c_bottomMenu.getMenuNameInfoMap().get(language);
        if (menuNameInfo != null) {
            bottomMenuInfo.setMenuName(menuNameInfo.getMenuName())
                    .setSubMenuName(menuNameInfo.getSubMenuName());
        }
        return bottomMenuInfo.build();
    }

    /**
     * int32 id                        = 1;
     * string icon                     = 2; //图标
     * int32 jumpType                  = 3; //跳转类型 1.内连 2.外链
     * int32 popupLinks                = 4; //弹框类型 1.任务 2.转盘 3.充值 4.客服
     * string innerLinks               = 5; //内部链接
     * string externalLinks            = 6; //外链接
     *
     * @param iconInfo
     * @return
     */
    private BackStageMessage.IconInfo buildIconInfo(C_BottomMenu.IconInfo iconInfo) {
        return BackStageMessage.IconInfo.newBuilder()
                .setId(iconInfo.getId())
                .setIcon(iconInfo.getIcon())
                .setJumpType(iconInfo.getJumpType())
                .setPopupLinks(iconInfo.getPopupLinks())
                .setInnerLinks(iconInfo.getInnerLinks())
                .setExternalLinks(iconInfo.getExternalLinks())
                .build();
    }

    private BackStageMessage.QualityAssuranceInfo buildQualityAssuranceInfo(int language, C_QualityAssurance c_qualityAssurance) {
        final BackStageMessage.QualityAssuranceInfo.Builder qualityAssuranceInfo = BackStageMessage.QualityAssuranceInfo.newBuilder();
        final C_QualityAssurance.QualityAssuranceInfo assuranceInfo = c_qualityAssurance.getQualityAssuranceMap().get(language);
        if (assuranceInfo == null) {
            return null;
        }
        qualityAssuranceInfo.setQualityAssuranceType(c_qualityAssurance.getQualityAssuranceType());
        for (String data : assuranceInfo.getQuestionData()) {
            final C_QualityAssurance.QuestionInfo questionInfo = JsonUtils.readFromJson(data, C_QualityAssurance.QuestionInfo.class);
            qualityAssuranceInfo.addQuestionList(buildQuestionInfo(questionInfo));
        }
        return qualityAssuranceInfo.build();
    }

    private BackStageMessage.QuestionInfo buildQuestionInfo(C_QualityAssurance.QuestionInfo questionInfo) {
        return BackStageMessage.QuestionInfo.newBuilder()
                .setQuestion(questionInfo.getQuestion())
                .setAnswer(questionInfo.getAnswer())
                .build();
    }

    /**
     * int32 gameCategoryId            = 1; //游戏类别Id
     * int32 gameType                  = 2; //游戏类型
     * string gameCategoryName         = 3; //游戏类别名字
     * string gameTypeName             = 4; //游戏类型
     *
     * @param language
     * @param c_baseGameType
     * @return
     */
    private BackStageMessage.GameTypeInfo buildGameTypeInfo(int language, C_BaseGameType c_baseGameType) {
        final BackStageMessage.GameTypeInfo.Builder gameTypeInfo = BackStageMessage.GameTypeInfo.newBuilder();
        final C_BaseGameType.GameTypeInfo gameType = c_baseGameType.getGameTypeInfoMap().get(language);
        if (gameType == null) {
            return null;
        }
        gameTypeInfo.setGameCategoryName(gameType.getGameCategoryName())
                .setGameTypeName(gameType.getGameTypeName())
                .setGameCategoryId(c_baseGameType.getGameCategoryId())
                .setGameType(c_baseGameType.getGameType())
                .setIcon(c_baseGameType.getIcon());
        return gameTypeInfo.build();
    }

    /**
     * int32 invitationPosterId        = 1;
     * string posterName               = 2; //海报名字
     * int32 index                     = 3; //1.竖版 2.横板
     * string fileUrl                  = 4; //文件地址
     * bool generateQRCode             = 5; //是否生成二维码
     *
     * @param c_invitationPoster
     * @return
     */
    private BackStageMessage.InvitationPosterInfo buildInvitationPosterInfo(C_InvitationPoster c_invitationPoster) {
        return BackStageMessage.InvitationPosterInfo.newBuilder()
                .setInvitationPosterId(c_invitationPoster.getInvitationPosterId())
                .setPosterName(c_invitationPoster.getPosterName())
                .setIndex(c_invitationPoster.getIndex())
                .setFileUrl(c_invitationPoster.getFileUrl())
                .setFileBase64(c_invitationPoster.getFileBase64())
                .setGenerateQRCode(c_invitationPoster.isGenerateQRCode())
                .build();
    }

    private BackStageMessage.InvitationLinksInfo buildInvitationLinksInfo(C_InvitationLinks c_invitationLinks, int language) {
        final BackStageMessage.InvitationLinksInfo.Builder invitationLinksInfo = BackStageMessage.InvitationLinksInfo.newBuilder();
        invitationLinksInfo.setInvitationLinksId(c_invitationLinks.getInvitationLinksId())
                .setSocialMediaName(c_invitationLinks.getSocialMediaName())
                .setIcon(c_invitationLinks.getIcon());
        final C_InvitationLinks.DescData descData = c_invitationLinks.getDescDataMap().get(language);
        if (descData != null) {
            invitationLinksInfo.setDesc(descData.getDesc());
        }
        return invitationLinksInfo.build();
    }

    private BackStageMessage.ThreePartyLoginInfo buildThreePartyLoginInfo(C_ThreePartyLogin c_threePartyLogin) {
        return BackStageMessage.ThreePartyLoginInfo.newBuilder()
                .setDomainName(c_threePartyLogin.getDomainName())
                .setThreePartyType(c_threePartyLogin.getThreePartyType())
                .setExtend1(c_threePartyLogin.getExtend_1())
                .setExtend2(c_threePartyLogin.getExtend_2())
                .setExtend3(c_threePartyLogin.getExtend_3())
                .setExtend4(c_threePartyLogin.getExtend_4())
                .setExtend5(c_threePartyLogin.getExtend_5())
                .build();
    }

    private BackStageMessage.LanguageInfo buildLanguageInfo(C_BaseLanguage c_baseLanguage) {
        return BackStageMessage.LanguageInfo.newBuilder()
                .setLanguageId(c_baseLanguage.getLanguageId())
                .setName(c_baseLanguage.getName())
                .setIcon(c_baseLanguage.getIcon())
                .build();
    }

    private BackStageMessage.WebSiteData buildWebSiteData(C_WebSiteInfo.WebSiteInfo webSiteInfo) {
        return BackStageMessage.WebSiteData.newBuilder()
                .setInfo(webSiteInfo.getInfo())
                .setInfo1(webSiteInfo.getInfo1())
                .setInfo2(webSiteInfo.getInfo2())
                .setTitle(webSiteInfo.getTitle())
                .setKeywords(webSiteInfo.getKeywords())
                .setIntroduce(webSiteInfo.getIntroduce())
                .setOgTile(webSiteInfo.getOgTile())
                .setOgType(webSiteInfo.getOgType())
                .setOgImage(webSiteInfo.getOgImage())
                .setOgDescription(webSiteInfo.getOgDescription())
                .setPrivacyAgreement(webSiteInfo.getPrivacyAgreement())
                .setUserTerms(webSiteInfo.getUserTerms())
                .build();
    }

    private BackStageMessage.PwaInfo buildPwaInfo(C_Pwa.PwaInfo pwaInfo) {
        return BackStageMessage.PwaInfo.newBuilder()
                .setIcon(pwaInfo.getIcon())
                .setPwaInfo(pwaInfo.getInfo())
                .build();
    }

    /**
     * int32 quickAccessId             =1;
     * int32 entranceType              =2; //入口类型 1.大入口 2.小入口
     * int32 jumpType                  =3;
     * int32 popupLinks                =4; //弹框类型 1.任务 2.转盘 3.充值 4.客服 5.登录 6.注册
     * string innerLinks               =5; //内部链接
     * string externalLinks            =6; //外链接
     */
    private BackStageMessage.QuickAccessInfo buildQuickAccessInfoInfo(C_QuickAccess.EntranceData entranceData, C_QuickAccess c_quickAccess) {
        final BackStageMessage.QuickAccessInfo.Builder quickAccess = BackStageMessage.QuickAccessInfo.newBuilder();
        quickAccess.setQuickAccessId(c_quickAccess.getQuickAccessId())
                .setEntranceType(c_quickAccess.getEntranceType())
                .setIsJump(c_quickAccess.getIsJump())
                .setJumpType(c_quickAccess.getJumpType())
                .setPopupLinks(c_quickAccess.getPopupLinks())
                .setInnerLinks(c_quickAccess.getInnerLinks())
                .setImageUrl(c_quickAccess.getImageUrl())
                .setExternalLinks(c_quickAccess.getExternalLinks())
                .setEntranceName(entranceData.getEntranceName())
                .setSort(entranceData.getSort())
                .build();
        return quickAccess.build();
    }

    private BackStageMessage.MaintainNoticeInfo buildBaseMaintainNoticeInfo(C_BaseMaintainNotice c_baseMaintainNotice, int language) {
        final C_BaseMaintainNotice.MaintainInfo maintainInfo = c_baseMaintainNotice.getMaintainInfoMap().get(language);
        if (maintainInfo == null) {
            return null;
        }
        final BackStageMessage.MaintainNoticeInfo.Builder maintainNoticeInfo = BackStageMessage.MaintainNoticeInfo.newBuilder()
                .setInfo(maintainInfo.getInfo())
                .setStartTime(c_baseMaintainNotice.getStartTime())
                .setEndTime(c_baseMaintainNotice.getEndTime());
        return maintainNoticeInfo.build();
    }

    private BackStageMessage.MaintainNoticeInfo buildMaintainNoticeInfo(C_MaintainNotice c_maintainNotice, int language) {
        final C_MaintainNotice.MaintainInfo maintainInfo = c_maintainNotice.getMaintainInfoMap().get(language);
        if (maintainInfo == null) {
            return null;
        }
        final BackStageMessage.MaintainNoticeInfo.Builder maintainNoticeInfo = BackStageMessage.MaintainNoticeInfo.newBuilder()
                .setInfo(maintainInfo.getInfo())
                .setStartTime(c_maintainNotice.getStartTime())
                .setEndTime(c_maintainNotice.getEndTime());
        return maintainNoticeInfo.build();
    }

    private BackStageMessage.RegisterRetrieveInfo buildRegisterRetrieveInfo(C_RegisterRetrievePop c_registerRetrieve, C_RegisterRetrievePop.RegisterInfo registerInfo) {
        final BackStageMessage.RegisterRetrieveInfo.Builder register = BackStageMessage.RegisterRetrieveInfo.newBuilder();
        register.setCId(c_registerRetrieve.getC_id())
                .setIsJump(c_registerRetrieve.getIsJump())
                .setJumpType(c_registerRetrieve.getJumpType())
                .setPopupLinks(c_registerRetrieve.getPopupLinks())
                .setInnerLinks(c_registerRetrieve.getInnerLinks())
                .setExternalLinks(c_registerRetrieve.getExternalLinks())
                .setNotLoginJump(c_registerRetrieve.isNotLoginJump())
                .setDesc(registerInfo.getDesc())
                .setText(registerInfo.getText())
                .setIcon(registerInfo.getIcon());
        return register.build();
    }

    private BackStageMessage.DailyRechargePopInfo buildDailyRechargePopInfo(C_DailyRechargePop c_dailyRechargePop, int language) {
        final BackStageMessage.DailyRechargePopInfo.Builder dailyRechargePopInfo = BackStageMessage.DailyRechargePopInfo.newBuilder();
        final C_DailyRechargePop.DescInfo descInfo = c_dailyRechargePop.getDescInfoMap().get(language);
        if (descInfo == null) {
            return null;
        }

        dailyRechargePopInfo.setCId(c_dailyRechargePop.getC_id())
                .setCurrencyId(c_dailyRechargePop.getCurrencyId())
                .setDesc(descInfo.getDesc())
                .setTitle(descInfo.getTitle());
        for (C_DailyRechargePop.GearInfo gearInfo : c_dailyRechargePop.getGearInfoMap().values()) {
            dailyRechargePopInfo.addGearInfo(buildGearInfo(gearInfo));
        }
        return dailyRechargePopInfo.build();
    }

    private BackStageMessage.GearInfo buildGearInfo(C_DailyRechargePop.GearInfo gearInfo) {
        return BackStageMessage.GearInfo.newBuilder()
                .setGearId(gearInfo.getGearId())
                .setAmount(gearInfo.getAmount())
                .setGiftRatio(gearInfo.getGiftRatio())
                .setIcon(gearInfo.getIcon())
                .setMinRecharge(gearInfo.getMinRecharge())
                .setMaxRecharge(gearInfo.getMaxRecharge())
                .build();
    }

    private BackStageMessage.FirstChargePopInfo buildFirstChargePopInfo(C_FirstChargePop c_firstChargePop, int language) {
        final BackStageMessage.FirstChargePopInfo.Builder firstChargePopInfo = BackStageMessage.FirstChargePopInfo.newBuilder();
        final C_FirstChargePop.DescInfo descInfo = c_firstChargePop.getDescInfoMap().get(language);
        if (descInfo == null) {
            return null;
        }
        firstChargePopInfo.setCId(c_firstChargePop.getC_id())
                .setCurrencyId(c_firstChargePop.getCurrencyId())
                .setAmount(c_firstChargePop.getAmount())
                .setGiveawayAmount(c_firstChargePop.getGiveawayAmount())
                .setTitle(descInfo.getTitle())
                .setDesc(descInfo.getDesc())
                .setIcon(descInfo.getIcon())
                .setButtonText(descInfo.getButton())
                .setButtonText1(descInfo.getButton1())
                .setButton(buildFirstChargeInfo(c_firstChargePop.getFirstChargeInfo()))
                .setButton1(buildFirstChargeInfo(c_firstChargePop.getFirstChargeInfo1()));
        return firstChargePopInfo.build();
    }

    private BackStageMessage.FirstChargeInfo buildFirstChargeInfo(C_FirstChargePop.FirstChargeInfo firstChargeInfo) {
        return BackStageMessage.FirstChargeInfo.newBuilder()
                .setNotLoginJump(firstChargeInfo.isNotLoginJump())
                .setIsJump(firstChargeInfo.getIsJump())
                .setJumpType(firstChargeInfo.getJumpType())
                .setPopupLinks(firstChargeInfo.getPopupLinks())
                .setInnerLinks(firstChargeInfo.getInnerLinks())
                .setExternalLinks(firstChargeInfo.getExternalLinks())
                .build();
    }

    private BackStageMessage.GamePopInfo buildGamePopInfo(C_GamePop c_gamePop, int language) {
        final C_GamePop.DescInfo descInfo = c_gamePop.getDescInfoMap().get(language);
        final BackStageMessage.GamePopInfo.Builder gamePopInfo = BackStageMessage.GamePopInfo.newBuilder();
        if (descInfo == null) {
            return null;
        }
        gamePopInfo.setCId(c_gamePop.getC_id())
                .setTitle(descInfo.getTitle())
                .setDesc(descInfo.getDesc())
                .setIcon(descInfo.getIcon())
                .setText(descInfo.getText());
        return gamePopInfo.build();
    }

    public CommonMessage.InboxInfo builderBulletinInfo(C_PubMail c_pubMail, int language) {
        final CommonMessage.InboxInfo.Builder inboxInfo = CommonMessage.InboxInfo.newBuilder();
        final C_PubMail.PubMailInfo pubMailInfo = c_pubMail.getPubMailInfoMap().get(language);
        if (pubMailInfo == null) {
            return null;
        }
        inboxInfo.setInboxId(c_pubMail.getMailId() + "")
                .setInboxType(c_pubMail.getMailType())
                .setTitle(pubMailInfo.getTitle())
                .setContext(pubMailInfo.getContent())
                .setIsJump(c_pubMail.getIsJump())
                .setJumpType(c_pubMail.getJumpType())
                .setPopupLinks(c_pubMail.getPopupLinks())
                .setInnerLinks(c_pubMail.getInnerLinks())
                .setExternalLinks(c_pubMail.getExternalLinks())
                .setFileUrl(c_pubMail.getFileUrl())
                .setCreateTime(c_pubMail.getEndTime());
        return inboxInfo.build();
    }

    private void sendConfigType(BackStageMessage.ReqConfigDataMessage req, MerchantData merchantData) {
        final BackStageMessage.ResConfigDataMessage.Builder res = BackStageMessage.ResConfigDataMessage.newBuilder();
        res.setMsgID(MIDMessage.MID.ResConfigData_VALUE);
        final String host = req.getHost();
        final int language = req.getLanguage();
        switch (req.getConfigType()) {
            case 6: {
                try {
                    for (C_VipClub c_vipClub : merchantData.getC_vipClubMap().values()) {
                        res.addVipClubList(buildVipClub(c_vipClub));
                    }
                } catch (Exception e) {
                    LOGGER.error("c_vipClub", e);
                }
                MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                return;
            }
            case 8: {
                try {
                    //banner
                    final List<C_Banner> c_bannerList = merchantData.getC_bannerMap().get(host);
                    if (c_bannerList != null) {
                        for (C_Banner c_banner : c_bannerList) {
                            if (pid > 0) {
                                final Player player = PlayerMrg.getInstance().getOnlinePlayerMap().get(pid);
                                if (player != null) {
                                    if (c_banner.channelLimit(player.getChannelId())) {
                                        continue;
                                    }
                                    if (c_banner.agentLimit(player.getAgentId())) {
                                        continue;
                                    }
                                }
                            }
                            res.addBannerList(buildBanner(c_banner));
                        }
                    }
                } catch (Exception e) {
                    LOGGER.error("c_banner", e);
                }
                MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                return;
            }
            case 9: {
                try {
                    //弹窗
                    final List<C_Popup> c_popupList = merchantData.getC_popupMap().get(language);
                    if (c_popupList != null && !c_popupList.isEmpty()) {
                        for (C_Popup c_popup : c_popupList) {
                            if (pid > 0) {
                                final Player player = PlayerMrg.getInstance().getOnlinePlayerMap().get(pid);
                                if (player != null) {
                                    if (c_popup.channelLimit(player.getChannelId())) {
                                        continue;
                                    }
                                    if (c_popup.agentLimit(player.getAgentId())) {
                                        continue;
                                    }
                                }
                            }
                            res.addPopupList(buildPopup(c_popup));
                        }
                    }
                } catch (Exception e) {
                    LOGGER.error("c_popup", e);
                }
                MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                return;
            }
            case 18: {
                try {
                    //邀请海报
                    final List<C_InvitationPoster> c_invitationPosterList = merchantData.getC_invitationPosterMap().get(language);
                    if (c_invitationPosterList != null && !c_invitationPosterList.isEmpty()) {
                        for (C_InvitationPoster c_invitationPoster : c_invitationPosterList) {
                            res.addInvitationPosterList(buildInvitationPosterInfo(c_invitationPoster));
                        }
                    }
                } catch (Exception e) {
                    LOGGER.error("c_invitationPoster", e);
                }
                MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                return;
            }
        }
    }

}
