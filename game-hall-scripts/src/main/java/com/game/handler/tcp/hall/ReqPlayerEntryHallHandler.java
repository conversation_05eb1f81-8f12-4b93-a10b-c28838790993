package com.game.handler.tcp.hall;

import com.game.c_entity.merchant.C_MaintainNotice;
import com.game.c_entity.middleplatform.C_BaseMaintainNotice;
import com.game.c_entity.middleplatform.C_BaseMerchant;
import com.game.engine.enums.state.ServerState;
import com.game.engine.io.handler.TcpHandler;
import com.game.engine.io.redis.RedisPoolManager;
import com.game.engine.script.IHandlerEntity;
import com.game.engine.script.ScriptLoader;
import com.game.engine.utils.Config;
import com.game.engine.utils.MsgUtil;
import com.game.engine.utils.TimeUtil;
import com.game.entity.player.Player;
import com.game.enums.ErrorCode;
import com.game.enums.redis.RedisLogin;
import com.game.hall.mrg.DataHallMrg;
import com.game.hall.mrg.MerchantData;
import com.game.hall.mrg.player.PlayerMrg;
import com.game.hall.script.IPlayerScript;
import com.game.utils.VirtualThreadUtils;
import com.proto.HallMessage;
import com.proto.MIDMessage;
import io.netty.util.internal.StringUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Optional;

@IHandlerEntity(mid = MIDMessage.MID.ReqPlayerEntryHall_VALUE, msg = HallMessage.ReqPlayerEntryHallMessage.class)
public class ReqPlayerEntryHallHandler extends TcpHandler {
    private static final Logger LOGGER = LoggerFactory.getLogger(ReqPlayerEntryHallHandler.class);

    @Override
    public void run() {
        final HallMessage.ResPlayerEntryHallMessage.Builder res = HallMessage.ResPlayerEntryHallMessage.newBuilder();
        res.setMsgID(MIDMessage.MID.ResPlayerEntryHall_VALUE);
        try {
            if (Config.serverState == ServerState.MAINTAIN || Config.serverState == ServerState.CLOSING) {
                res.setError(ErrorCode.Server_Maintenance.getCode());
                MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                return;
            }

            final long start = TimeUtil.currentTimeMillis();
            final int threeParty = (int) paramsMap.get("threeParty");
            final String country = (String) paramsMap.get("country");
            final String ip = MsgUtil.getClientIp(session);

            final HallMessage.ReqPlayerEntryHallMessage req = (HallMessage.ReqPlayerEntryHallMessage) getMessage();

            final String host = req.getHost();
            final C_BaseMerchant c_baseMerchant = DataHallMrg.getInstance().findC_BaseHostMerchant(this.getClass().getSimpleName(), host);
            if (c_baseMerchant == null) {
                res.setError(ErrorCode.DomainName_Not_Exist.getCode());
                MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                return;
            }

            final MerchantData merchantData = DataHallMrg.getInstance().findMerchantData(this.getClass().getSimpleName(), c_baseMerchant.getBusiness_no());
            if (merchantData == null) {
                res.setError(ErrorCode.Data_Error.getCode());
                MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                return;
            }

            //中台维护公告
            final Optional<C_BaseMaintainNotice> baseOptional = DataHallMrg.getInstance().getC_maintainNoticeMap().values().stream().findFirst();
            if (baseOptional.isPresent()) {
                final C_BaseMaintainNotice c_baseMaintainNotice = baseOptional.get();
                if (c_baseMaintainNotice.isStatus()) {
                    res.setError(ErrorCode.Server_Maintenance.getCode());
                    MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                    LOGGER.warn("playerId；{}，c_baseMaintainNotice", pid);
                    return;
                }
            }

            final Optional<C_MaintainNotice> optional = merchantData.getC_maintainNoticeMap().values().stream().findFirst();
            if (optional.isPresent()) {
                final C_MaintainNotice c_maintainNotice = optional.get();
                if (c_maintainNotice.isStatus() && !c_maintainNotice.getWhitelistIds().contains(pid)) {
                    res.setError(ErrorCode.Server_Maintenance.getCode());
                    MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                    LOGGER.warn("playerId；{}，c_maintainNotice", pid);
                    return;
                }
            }

            final Player player = loadPlayer(req, ip, threeParty, pid);
            if (player == null) {
                LOGGER.warn("accountId：{}，find player info failed，player not exist ！！！", pid);
                res.setError(ErrorCode.Account_NotExist.getCode());
                MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                return;
            }

            //封号
            if (player.isTitle()) {
                LOGGER.warn("playerGuid：{}，playerName：{}，number has been banned", player.getPlayerId(), player.getPlayerName());
                res.setError(ErrorCode.Player_Title.getCode());
                MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                return;
            }

            ScriptLoader.getInstance().consumerScript("PlayerLoginScript", (IPlayerScript script) -> {
                script.loginHall(player, ip, country, req);
            });

            MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
            LOGGER.info("enter hall playerId：{}，consumer：{}", pid, TimeUtil.currentTimeMillis() - start);
        } catch (Exception e) {
            LOGGER.error("ReqPlayerEntryHallHandler", e);
            res.setError(ErrorCode.Internal_Server_Error.getCode());
            MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
        }
    }

    private Player loadPlayer(HallMessage.ReqPlayerEntryHallMessage req, String ip, int threeParty, long accountId) {
        Player player = PlayerMrg.getInstance().findDbPlayer(accountId);
        if (player == null) {
            player = ScriptLoader.getInstance().functionScript("PlayerCreateScript",
                    (IPlayerScript script) ->
                            script.createPlayer(req, accountId, threeParty, (p) -> {
                                p.setIp(ip);
                                initPlayerData(p, ip, req.getHost());
                            }));
        }
        return player;
    }

    private void initPlayerData(Player player, String ip, String host) {
        player.setRegisterIp(ip);

        final String playerName = "WG" + player.getPlayerId();
        player.setPlayerName(playerName);

        RedisPoolManager.getInstance().executeAsync(commands ->
                commands.sadd(RedisLogin.Platform_LG_Set_UserName.getKey(player.getBusiness_no()), playerName)
        );

        player.setState(1);
        player.setHeadId(String.valueOf(1000));
        player.setLoginTime(TimeUtil.currentTimeMillis());
        player.setLogoutTime(TimeUtil.currentTimeMillis());
        player.setLastRefreshTime(TimeUtil.currentTimeMillis());
        player.setWebSite(host);

        final MerchantData merchantData = DataHallMrg.getInstance().findMerchantData(this.getClass().getSimpleName(), player.getBusiness_no());
        if (merchantData != null) {
            final String region = merchantData.findC_GlobalValue(this.getClass().getSimpleName(), "initRegion");
            if (StringUtil.isNullOrEmpty(player.getRegisterRegion()) && !StringUtil.isNullOrEmpty(region)) {
                player.setRegisterRegion(region);
            }

            final String language = merchantData.findC_GlobalValue(this.getClass().getSimpleName(), "initLanguage");
            if (!StringUtil.isNullOrEmpty(language)) {
                player.setLanguage(Integer.parseInt(language));
            }

            final String currencyId = merchantData.findC_GlobalValue(this.getClass().getSimpleName(), "initCurrencyId");
            if (!StringUtil.isNullOrEmpty(currencyId)) {
                player.setCurrencyId(Integer.parseInt(currencyId));
                if (player.getCurrencyId() / 1000 == 1) {
                    player.setViewFiat(Integer.parseInt(currencyId));
                }
            }
        }
    }
}
