package com.game.handler.tcp.activity.rechargeRecover;

import com.game.c_entity.merchant.C_RechargeRecover;
import com.game.engine.io.handler.TcpHandler;
import com.game.engine.math.BigDecimalUtils;
import com.game.engine.script.IHandlerEntity;
import com.game.engine.script.ScriptLoader;
import com.game.engine.util.misc.Tuple2;
import com.game.engine.utils.JsonUtils;
import com.game.engine.utils.MsgUtil;
import com.game.entity.player.Player;
import com.game.entity.player.activity.rechargeRecover.RechargeRecoverInfo;
import com.game.enums.ErrorCode;
import com.game.enums.RewardReason;
import com.game.enums.TransactionFrom;
import com.game.enums.TurnoverReason;
import com.game.hall.mrg.ActivityMrg;
import com.game.hall.mrg.CommonMrg;
import com.game.hall.mrg.DataHallMrg;
import com.game.hall.mrg.MerchantData;
import com.game.hall.mrg.currency.CurrencyMrg;
import com.game.hall.mrg.currency.RewardRequest;
import com.game.hall.mrg.player.PlayerMrg;
import com.game.hall.script.IBonusScript;
import com.game.hall.script.IPlayerScript;
import com.proto.ActivityMessage;
import com.proto.MIDMessage;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.*;

@IHandlerEntity(mid = MIDMessage.MID.ReqReceiveRechargeRecover_VALUE, msg = ActivityMessage.ReqReceiveRechargeRecoverMessage.class)
public class ReqReceiveRechargeRecoverHandler extends TcpHandler {
    private static final Logger LOGGER = LoggerFactory.getLogger(ReqReceiveRechargeRecoverHandler.class);

    @Override
    public void run() {
        final ActivityMessage.ResReceiveRechargeRecoverMessage.Builder res = ActivityMessage.ResReceiveRechargeRecoverMessage.newBuilder();
        res.setMsgID(MIDMessage.MID.ResReceiveRechargeRecover_VALUE);
        try {
            final Player player = PlayerMrg.getInstance().getOnlinePlayer(pid, this.getClass().getSimpleName());
            if (player == null) {
                res.setError(ErrorCode.Player_Offline.getCode());
                MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                return;
            }

            final MerchantData merchantData = DataHallMrg.getInstance().findMerchantData(this.getClass().getSimpleName(), player.getBusiness_no());
            if (merchantData == null) {
                res.setError(ErrorCode.Data_Error.getCode());
                MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                return;
            }

            final C_RechargeRecover c_rechargeRecover = merchantData.findC_RechargeRecover(this.getClass().getSimpleName(), ActivityMrg.RECHARGE_RECOVER);
            if (c_rechargeRecover == null) {
                res.setError(ErrorCode.Data_Error.getCode());
                MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                return;
            }

            final ActivityMessage.ReqReceiveRechargeRecoverMessage req = (ActivityMessage.ReqReceiveRechargeRecoverMessage) getMessage();

            final RechargeRecoverInfo rechargeRecoverInfo = player.getRechargeRecoverInfo();

            final RewardRequest rewardRequest = new RewardRequest();
            if (rechargeRecoverInfo.isStatus()) {
                res.setError(ErrorCode.Reward_Received.getCode());
                MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                return;
            }

            final double currRechargeAmount = rechargeRecoverInfo.getCurrRechargeMap().getOrDefault(player.getCurrencyId(), 0d);
            final double rechargeAmount = rechargeRecoverInfo.getRechargeAmountMap().getOrDefault(player.getCurrencyId(), 0d);
            if (currRechargeAmount < rechargeAmount) {
                res.setError(ErrorCode.Conditions_Not_Met.getCode());
                MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                return;
            }

            rechargeRecoverInfo.setStatus(true);

            final Map<Integer, Double> receiveMap = new HashMap<>();
            for (Map.Entry<Integer, Double> entry : rechargeRecoverInfo.getWageredMap().entrySet()) {
                if (entry.getValue() == 0) {
                    continue;
                }
                rewardRequest.addCurrency(entry.getKey(), entry.getValue());
                receiveMap.put(1, entry.getValue());
            }

            for (Map.Entry<Integer, Double> entry : rechargeRecoverInfo.getRedEnvelopeRainMap().entrySet()) {
                if (entry.getValue() == 0) {
                    continue;
                }
                rewardRequest.addCurrency(entry.getKey(), entry.getValue());
                receiveMap.put(2, entry.getValue());
            }

            if (rewardRequest.getCurrencyMap().isEmpty()) {
                MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                return;
            }

            final RewardReason rewardReason = RewardReason.RechargeRecover;
            rewardReason.setSource(JsonUtils.writeAsJson(receiveMap));
            CurrencyMrg.getInstance().rewardNoTest(player, rewardRequest.getBonusCurrencyMap(), rewardReason);

            final Tuple2<Integer, Double> tuple2 = rewardRequest.getReward();
            //TODO 打码
            final TurnoverReason turnoverReason = TurnoverReason.RechargeRecover;
            turnoverReason.setSource(JsonUtils.writeAsJson(receiveMap));
            ScriptLoader.getInstance().consumerScript("PlayerWithdrawStandardScript",
                    (IPlayerScript script) -> script.drawStandard(player, turnoverReason, tuple2.getFirst(), tuple2.getSecond(),
                            BigDecimalUtils.mul(tuple2.getSecond(), c_rechargeRecover.getTurnoverMul(), 4)));

            ScriptLoader.getInstance().consumerScript("BonusScript",
                    (IBonusScript script) -> script.addBonusNote(TransactionFrom.RechargeRecover, player, tuple2.getFirst(), tuple2.getSecond()));

            res.setRewardShow(CommonMrg.buildDItemShow(tuple2.getFirst(), tuple2.getSecond()));
            MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
        } catch (Exception e) {
            LOGGER.error("ReqReceiveRechargeRecoverHandler", e);
            res.setError(ErrorCode.Internal_Server_Error.getCode());
            MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
        }
    }
}
