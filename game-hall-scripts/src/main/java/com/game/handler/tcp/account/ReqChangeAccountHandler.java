package com.game.handler.tcp.account;

import com.game.dao.account.AccountDao;
import com.game.dao.player.PlayerDao;
import com.game.engine.io.handler.TcpHandler;
import com.game.engine.io.redis.RedisPoolManager;
import com.game.engine.script.IHandlerEntity;
import com.game.engine.utils.MsgUtil;
import com.game.entity.account.Account;
import com.game.entity.account.email.Email;
import com.game.entity.account.email.EmailFields;
import com.game.entity.account.phone.Phone;
import com.game.entity.player.Player;
import com.game.entity.player.PlayerFields;
import com.game.enums.ErrorCode;
import com.game.enums.ThreeParty;
import com.game.enums.VerifyCode;
import com.game.enums.redis.RedisLogin;
import com.game.hall.mrg.player.PlayerMrg;
import com.game.manager.EntityDaoMrg;
import com.game.utils.VirtualThreadUtils;
import com.proto.HallMessage;
import com.proto.MIDMessage;
import io.lettuce.core.api.async.RedisAsyncCommands;
import io.netty.util.internal.StringUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.mongodb.core.query.Update;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.CompletableFuture;

@IHandlerEntity(mid = MIDMessage.MID.ReqChangeAccount_VALUE, msg = HallMessage.ReqChangeAccountMessage.class)
public class ReqChangeAccountHandler extends TcpHandler {
    private static final Logger LOGGER = LoggerFactory.getLogger(ReqChangeAccountHandler.class);

    @Override
    public void run() {
        final HallMessage.ResChangeAccountMessage.Builder res = HallMessage.ResChangeAccountMessage.newBuilder();
        res.setMsgID(MIDMessage.MID.ResChangeAccount_VALUE);

        try {
            final Player player = PlayerMrg.getInstance().getOnlinePlayer(pid, this.getClass().getSimpleName());
            if (player == null) {
                res.setError(ErrorCode.Player_Offline.getCode());
                MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                return;
            }

            final HallMessage.ReqChangeAccountMessage req = (HallMessage.ReqChangeAccountMessage) getMessage();
            final int threeParty = req.getThreeParty();
            final String areaCode = req.getAreaCode().trim();
            final String reqAccount = req.getAccount().trim();
            final String verifyCode = req.getVerifyCode().trim();

            String ac;
            if (threeParty == ThreeParty.Phone.getThreeParty()) {
                ac = areaCode + "-" + reqAccount;
            } else {
                ac = reqAccount;
            }

            final String finalAc = ac;
            final String accountId = RedisPoolManager.getInstance().function(jedis ->
                    jedis.sync().hget(RedisLogin.Platform_LG_Map_AccountID.getKey(player.getBusiness_no(), threeParty), finalAc));
            if (!StringUtil.isNullOrEmpty(accountId)) {
                LOGGER.warn("account：{}，already registered", finalAc);
                res.setError(ErrorCode.Account_AlreadyExists.getCode());
                MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                return;
            }

            //邮箱验证码
            final String code = RedisPoolManager.getInstance().function(jedis ->
                    jedis.sync().get(RedisLogin.Platform_LG_Account_VerifyCode.getKey(player.getBusiness_no(), VerifyCode.Change.getType(), finalAc)));
            if (StringUtil.isNullOrEmpty(verifyCode) || !verifyCode.equals(code)) {
                LOGGER.warn("account：{}，verifyCode，{}-{}，", finalAc, code, verifyCode);
                res.setError(ErrorCode.VerifyCode_Error.getCode());
                MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                return;
            }

            final Account account = PlayerMrg.getInstance().findDbAccount(pid);
            if (account == null) {
                res.setError(ErrorCode.Data_Error.getCode());
                MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                return;
            }

            String oldAccount;
            if (threeParty == ThreeParty.Email.getThreeParty()) {
                oldAccount = player.getEmail();
                player.setEmail(finalAc);
                EntityDaoMrg.getInstance().getDao(PlayerDao.class)
                        .updatePlayerField(player.getPlayerId(), PlayerFields.email, player.getEmail());

                final Email emailInfo = account.getEmailInfo();
                emailInfo.setEmail(finalAc);
                EntityDaoMrg.getInstance().getDao(AccountDao.class).updateMail2LField(account.getAccountId(), EmailFields.email, emailInfo.getEmail());
            } else {
                oldAccount = player.getAreaCode() + "-" + player.getPhone();
                player.setAreaCode(areaCode);
                player.setPhone(reqAccount);
                final Update update = new Update();
                update.set(PlayerFields.areaCode, areaCode);
                update.set(PlayerFields.phone, reqAccount);
                EntityDaoMrg.getInstance().getDao(PlayerDao.class)
                        .updatePlayer(player.getPlayerId(), update);

                final Phone phoneInfo = account.getPhoneInfo();
                phoneInfo.setAreaCode(areaCode);
                phoneInfo.setPhone(reqAccount);
                EntityDaoMrg.getInstance().getDao(AccountDao.class).updatePhone(account.getAccountId(), phoneInfo);
            }

            RedisPoolManager.getInstance().asyncPipeline(commands -> {
                        final List<CompletableFuture<?>> futures = new ArrayList<>();
                        futures.add(commands.hdel(RedisLogin.Platform_LG_Map_AccountID.getKey(player.getBusiness_no(), threeParty), oldAccount)
                                .toCompletableFuture());
                        futures.add(commands.hset(RedisLogin.Platform_LG_Map_AccountID.getKey(player.getBusiness_no(), threeParty), finalAc, pid + "")
                                .toCompletableFuture());
                        return futures;
                    }
            );
            res.setAreaCode(areaCode)
                    .setAccount(reqAccount)
                    .setThreeParty(threeParty);
            MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
        } catch (Exception e) {
            LOGGER.error("ReqChangeAccountHandler", e);
            res.setError(ErrorCode.Internal_Server_Error.getCode());
            MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
        }
    }
}
