package com.game.handler.tcp.affiliate;

import com.game.dao.player.PlayerPromoteDao;
import com.game.engine.io.handler.TcpHandler;
import com.game.engine.script.IHandlerEntity;
import com.game.engine.utils.MsgUtil;
import com.game.entity.player.Player;
import com.game.entity.player.promote.PlayerPromote;
import com.game.enums.ErrorCode;
import com.game.hall.mrg.player.PlayerMrg;
import com.game.manager.EntityDaoMrg;
import com.proto.AffiliateMessage;
import com.proto.MIDMessage;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;

@IHandlerEntity(mid = MIDMessage.MID.ReqReferralCodeAndFriendsData_VALUE, msg = AffiliateMessage.ReqReferralCodeAndFriendsDataMessage.class)
public class ReqReferralCodeAndFriendsDataHandler extends TcpHandler {
    private static final Logger LOGGER = LoggerFactory.getLogger(ReqReferralCodeAndFriendsDataHandler.class);

    @Override
    public void run() {
        final AffiliateMessage.ResReferralCodeAndFriendsDataMessage.Builder res = AffiliateMessage.ResReferralCodeAndFriendsDataMessage.newBuilder();
        res.setMsgID(MIDMessage.MID.ResReferralCodeAndFriendsData_VALUE);
        try {
            final Player player = PlayerMrg.getInstance().getOnlinePlayer(pid, this.getClass().getSimpleName());
            if (player == null) {
                res.setError(ErrorCode.Player_Offline.getCode());
                MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                return;
            }
            final PlayerPromote playerPromote = player.getPlayerPromote();
            final int referralCodeNum = playerPromote.getReferralCodeMap().size();
            final int friends = EntityDaoMrg.getInstance().getDao(PlayerPromoteDao.class).countFriends(new ArrayList<>(playerPromote.getReferralCodeMap().keySet()));
            res.setReferralCodeNum(referralCodeNum)
                    .setReferralCodeLimit(20)
                    .setFriends(friends);
            MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
        } catch (Exception e) {
            LOGGER.error("ReqReferralCodeAndFriendsDataHandler", e);
            res.setError(ErrorCode.Internal_Server_Error.getCode());
            MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
        }
    }
}
