package com.game.handler.tcp;

import com.game.engine.io.handler.TcpHandler;
import com.game.engine.script.IHandlerEntity;
import com.game.engine.utils.MsgUtil;
import com.game.entity.player.Player;
import com.game.enums.ErrorCode;
import com.game.hall.mrg.UserTcpMrg;
import com.game.hall.mrg.player.PlayerMrg;
import com.game.hall.server.handler.UserWebSocketServerHandler;
import com.game.user.UserAttributeChannelData;
import com.game.user.UserSession;
import com.proto.MIDMessage;
import com.proto.TcpMessage;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@IHandlerEntity(mid = MIDMessage.MID.ReqTcpTokenAuth_VALUE, msg = TcpMessage.ReqTcpTokenAuthMessage.class)
public class ReqTcpTokenAuthHandler extends TcpHandler {
    private static final Logger LOGGER = LoggerFactory.getLogger(ReqTcpTokenAuthHandler.class);

    @Override
    public void run() {
        final TcpMessage.ResTcpTokenAuthMessage.Builder res = TcpMessage.ResTcpTokenAuthMessage.newBuilder();
        res.setMsgID(MIDMessage.MID.ResTcpTokenAuth_VALUE);
        try {
            final UserSession userSession = UserTcpMrg.getInstance().getUserBySessionID(pid);
            if (userSession == null) {
                res.setMsgID(ErrorCode.Token_Invalid.getCode());
                MsgUtil.sendClientMsg(this.session, res.build());
                return;
            }

            final long userId = (long) paramsMap.get("userId");
            if (userId > 0) {
                final Player p = PlayerMrg.getInstance().getOnlinePlayerMap().get(userId);
                if (p == null) {
                    res.setMsgID(ErrorCode.Player_Offline.getCode());
                    MsgUtil.sendClientMsg(this.session, res.build());
                    return;
                }
            }

            final String account = (String) paramsMap.get("account");
            final String business_no = (String) paramsMap.get("business_no");
            final String browserId = (String) paramsMap.get("browserId");

            final UserAttributeChannelData userAttributeChannelData = userSession.getUserSession()
                    .attr(UserWebSocketServerHandler.CHANNEL_USER_ATTRIBUTE_KEY).get();

            userAttributeChannelData.setTokenCheckSuccess(true);

            userSession.setAccountInfo(userId, account, business_no, browserId);
            if (userId > 0) {
                LOGGER.info("userId：{}，send client msg：{}", userId, res.getMsgID());
                UserTcpMrg.getInstance().addOnlineUser(userSession);
                final Player player = PlayerMrg.getInstance().getOnlinePlayerMap().get(userId);
                if (player != null) {
                    player.setClientSession(this.session);
                }
            }

            MsgUtil.sendClientMsg(this.session, res.build());
        } catch (Exception e) {
            LOGGER.error("", e);
            res.setError(ErrorCode.Internal_Server_Error.getCode());
            MsgUtil.sendClientMsg(this.session, res.build());
        }
    }

}
