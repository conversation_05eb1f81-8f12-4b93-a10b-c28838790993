package com.game.handler.tcp.vip;

import com.game.c_entity.merchant.C_VipClub;
import com.game.dao.player.PlayerDao;
import com.game.engine.io.handler.TcpHandler;
import com.game.engine.math.BigDecimalUtils;
import com.game.engine.script.IHandlerEntity;
import com.game.engine.script.ScriptLoader;
import com.game.engine.util.misc.Tuple2;
import com.game.engine.utils.MsgUtil;
import com.game.entity.player.Player;
import com.game.entity.player.vip.VipClub;
import com.game.enums.ErrorCode;
import com.game.enums.RewardReason;
import com.game.enums.TransactionFrom;
import com.game.enums.TurnoverReason;
import com.game.hall.mrg.CommonMrg;
import com.game.hall.mrg.DataHallMrg;
import com.game.hall.mrg.MerchantData;
import com.game.hall.mrg.currency.CurrencyMrg;
import com.game.hall.mrg.currency.RewardRequest;
import com.game.hall.mrg.player.PlayerMrg;
import com.game.hall.script.IBonusScript;
import com.game.hall.script.IPlayerScript;
import com.game.manager.EntityDaoMrg;
import com.proto.ActivityMessage;
import com.proto.MIDMessage;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@IHandlerEntity(mid = MIDMessage.MID.ReqReceiveVipBackCash_VALUE, msg = ActivityMessage.ReqReceiveVipBackCashMessage.class)
public class ReqReceiveVipBackCashHandler extends TcpHandler {
    private static final Logger LOGGER = LoggerFactory.getLogger(ReqReceiveVipBackCashHandler.class);

    @Override
    public void run() {
        final ActivityMessage.ResReceiveVipBackCashMessage.Builder res = ActivityMessage.ResReceiveVipBackCashMessage.newBuilder();
        res.setMsgID(MIDMessage.MID.ResReceiveVipBackCash_VALUE);
        try {
            final Player player = PlayerMrg.getInstance().getOnlinePlayer(pid, this.getClass().getSimpleName());
            if (player == null) {
                res.setError(ErrorCode.Player_Offline.getCode());
                MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                return;
            }

            final MerchantData merchantData = DataHallMrg.getInstance().findMerchantData(this.getClass().getSimpleName(), player.getBusiness_no());
            if (merchantData == null) {
                res.setError(ErrorCode.Data_Error.getCode());
                MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                return;
            }

            final VipClub vipClub = player.getVipClub();
            final int currencyId = player.getCurrencyId();
            final double bonus = vipClub.getLastDayBackCash().getOrDefault(currencyId, 0d);
            if (bonus == 0) {
                res.setError(ErrorCode.LuckSpin_Not_Available.getCode());
                MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                return;
            }

            final C_VipClub c_vipClub = merchantData.findC_VipClub(this.getClass().getSimpleName(), player.getVipClub().getVipLevel());
            if (c_vipClub == null) {
                res.setError(ErrorCode.Data_Error.getCode());
                MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                return;
            }

            final RewardRequest rewardRequest = new RewardRequest();
            rewardRequest.addCurrency(currencyId, bonus);
            final RewardReason rewardReason = RewardReason.Vip_BachCash;
            if (c_vipClub.getPlayerCashBackType() == 1) {
                CurrencyMrg.getInstance().rewardNoTest(player, rewardRequest.getCurrencyMap(), rewardReason);
            } else {
                CurrencyMrg.getInstance().rewardNoTest(player, rewardRequest.getBonusCurrencyMap(), rewardReason);
            }
            //TODO 打码
//            final WithdrawStandard withdrawStandard = player.getWithdrawStandard(box.currencyId);
//            withdrawStandard.incDrawStandard(BigDecimalUtils.mul(box.amount, c_rewardBox.getTurnoverMul(), 4));
            final TurnoverReason turnoverReason = TurnoverReason.Vip_BachCash;
            ScriptLoader.getInstance().consumerScript("PlayerWithdrawStandardScript",
                    (IPlayerScript script) -> script.drawStandard(player, turnoverReason, currencyId, bonus,
                            BigDecimalUtils.mul(bonus, c_vipClub.getBackCashTurnoverMul(), 4)));

            res.setRewardShow(CommonMrg.buildDItemShow(currencyId, bonus));
            MsgUtil.responseHttp(res.build(), res.getMsgID(), session);

            vipClub.getLastDayBackCash().clear();
            EntityDaoMrg.getInstance().getDao(PlayerDao.class).vipClubDao.updateLastDayBackCash(pid, vipClub);

            if (!rewardRequest.getCurrencyMap().isEmpty()) {
                final Tuple2<Integer, Double> tuple2 = rewardRequest.getReward();
                ScriptLoader.getInstance().consumerScript("BonusScript",
                        (IBonusScript script) -> script.addBonusNote(TransactionFrom.Vip_DayCashBack, player, tuple2.getFirst(), tuple2.getSecond()));
            }

        } catch (Exception e) {
            LOGGER.error("ReqReceiveVipBackCashHandler", e);
            res.setError(ErrorCode.Internal_Server_Error.getCode());
            MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
        }
    }
}
