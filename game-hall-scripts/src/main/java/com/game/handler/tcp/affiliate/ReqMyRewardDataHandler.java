package com.game.handler.tcp.affiliate;

import com.game.c_entity.middleplatform.C_BaseExchangeRate;
import com.game.dao.player.PlayerPromoteDao;
import com.game.engine.io.handler.TcpHandler;
import com.game.engine.math.BigDecimalUtils;
import com.game.engine.script.IHandlerEntity;
import com.game.engine.util.misc.Tuple2;
import com.game.engine.utils.MsgUtil;
import com.game.entity.player.Player;
import com.game.entity.player.promote.*;
import com.game.enums.ErrorCode;
import com.game.hall.mrg.DataHallMrg;
import com.game.hall.mrg.MerchantData;
import com.game.hall.mrg.currency.CurrencyMrg;
import com.game.hall.mrg.player.PlayerMrg;
import com.game.manager.EntityDaoMrg;
import com.proto.AffiliateMessage;
import com.proto.MIDMessage;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@IHandlerEntity(mid = MIDMessage.MID.ReqMyRewardData_VALUE, msg = AffiliateMessage.ReqMyRewardDataMessage.class)
public class ReqMyRewardDataHandler extends TcpHandler {
    private static final Logger LOGGER = LoggerFactory.getLogger(ReqMyRewardDataHandler.class);

    @Override
    public void run() {
        final AffiliateMessage.ResMyRewardDataMessage.Builder res = AffiliateMessage.ResMyRewardDataMessage.newBuilder();
        res.setMsgID(MIDMessage.MID.ResMyRewardData_VALUE);
        try {
            final Player player = PlayerMrg.getInstance().getOnlinePlayer(pid, this.getClass().getSimpleName());
            if (player == null) {
                res.setError(ErrorCode.Player_Offline.getCode());
                MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                return;
            }
            final MerchantData merchantData = DataHallMrg.getInstance().findMerchantData(this.getClass().getSimpleName(), player.getBusiness_no());
            if (merchantData == null) {
                res.setError(ErrorCode.Data_Error.getCode());
                MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                return;
            }

            final boolean isManyCurrency = merchantData.getC_currencyMap().size() > 1;

            final PlayerPromote playerPromote = player.getPlayerPromote();
            double availableCommissionRewards = 0;
            double totalCommissionRewards = 0;
            final Map<Integer, CommissionRewards> commissionRewardsMap = playerPromote.getCommissionRewardsMap();
            for (Map.Entry<Integer, CommissionRewards> entry : commissionRewardsMap.entrySet()) {
                final CommissionRewards commissionRewards = entry.getValue();

                if (isManyCurrency) {
                    final C_BaseExchangeRate c_baseExchangeRate = DataHallMrg.getInstance().findC_BaseExchangeRate(this.getClass().getSimpleName(), entry.getKey());
                    if (c_baseExchangeRate == null) {
                        continue;
                    }
                    availableCommissionRewards = BigDecimalUtils.add(availableCommissionRewards, BigDecimalUtils.mul(commissionRewards.getAvailable(), c_baseExchangeRate.getExchangeRate(), 9), 9);
                    totalCommissionRewards = BigDecimalUtils.add(totalCommissionRewards, BigDecimalUtils.mul(commissionRewards.getTotalReceived(), c_baseExchangeRate.getExchangeRate(), 9), 9);
                } else {
                    availableCommissionRewards = BigDecimalUtils.add(availableCommissionRewards, commissionRewards.getAvailable(), 9);
                    totalCommissionRewards = BigDecimalUtils.add(totalCommissionRewards, commissionRewards.getTotalReceived(), 9);
                }
            }

            double availableTeamRewards = 0;
            double totalTeamRewards = 0;
            final Map<Integer, TeamRewards> teamRewardsMap = playerPromote.getTeamRewardsMap();
            for (Map.Entry<Integer, TeamRewards> entry : teamRewardsMap.entrySet()) {
                final TeamRewards teamRewards = entry.getValue();

                if (isManyCurrency) {
                    final C_BaseExchangeRate c_baseExchangeRate = DataHallMrg.getInstance().findC_BaseExchangeRate(this.getClass().getSimpleName(), entry.getKey());
                    if (c_baseExchangeRate == null) {
                        continue;
                    }
                    availableTeamRewards = BigDecimalUtils.add(availableTeamRewards, BigDecimalUtils.mul(teamRewards.getAvailable(), c_baseExchangeRate.getExchangeRate(), 9), 9);
                    totalTeamRewards = BigDecimalUtils.add(totalTeamRewards, BigDecimalUtils.mul(teamRewards.getTotalReceived(), c_baseExchangeRate.getExchangeRate(), 9), 9);
                } else {
                    availableTeamRewards = BigDecimalUtils.add(availableTeamRewards, teamRewards.getAvailable(), 9);
                    totalTeamRewards = BigDecimalUtils.add(totalTeamRewards, teamRewards.getTotalReceived(), 9);
                }
            }

            double availableThreeLevelRewards = 0;
            double totalThreeLevelRewards = 0;
            final Map<Integer, ThreeLevelRewards> threeLevelRewardsMap = playerPromote.getThreeLevelRewardsMap();
            for (Map.Entry<Integer, ThreeLevelRewards> entry : threeLevelRewardsMap.entrySet()) {
                final ThreeLevelRewards threeLevelRewards = entry.getValue();

                if (isManyCurrency) {
                    final C_BaseExchangeRate c_baseExchangeRate = DataHallMrg.getInstance().findC_BaseExchangeRate(this.getClass().getSimpleName(), entry.getKey());
                    if (c_baseExchangeRate == null) {
                        continue;
                    }
                    availableThreeLevelRewards = BigDecimalUtils.add(availableThreeLevelRewards, BigDecimalUtils.mul(threeLevelRewards.getAvailable(), c_baseExchangeRate.getExchangeRate(), 9), 9);
                    totalThreeLevelRewards = BigDecimalUtils.add(totalThreeLevelRewards, BigDecimalUtils.mul(threeLevelRewards.getTotalReceived(), c_baseExchangeRate.getExchangeRate(), 9), 9);
                } else {
                    availableThreeLevelRewards = BigDecimalUtils.add(availableThreeLevelRewards, threeLevelRewards.getAvailable(), 9);
                    totalThreeLevelRewards = BigDecimalUtils.add(totalThreeLevelRewards, threeLevelRewards.getTotalReceived(), 9);
                }
            }

            double availableReferralRewards = playerPromote.getAvailableRewards().getAvailable();
            double totalReferralRewards = 0;
            final Map<String, ReferralCode> referralCodeMap = playerPromote.getReferralCodeMap();
            final Tuple2<Integer, List<PlayerPromote>> playerPromotes = EntityDaoMrg.getInstance().getDao(PlayerPromoteDao.class).findByCodeFriends(new ArrayList<>(referralCodeMap.keySet()));
            for (PlayerPromote promote : playerPromotes.getSecond()) {
                final ReferralRewards referralRewards = promote.getReferralRewards();
                if (referralRewards.getCurrencyId() == 0) {
                    continue;
                }

                if (isManyCurrency) {
                    final C_BaseExchangeRate c_baseExchangeRate = DataHallMrg.getInstance().findC_BaseExchangeRate(this.getClass().getSimpleName(), referralRewards.getCurrencyId());
                    if (c_baseExchangeRate == null) {
                        continue;
                    }
//                    availableReferralRewards = BigDecimalUtils.add(availableReferralRewards, BigDecimalUtils.mul(referralRewards.getAvailable(), c_baseExchangeRate.getExchangeRate(), 9), 9);
                    totalReferralRewards = BigDecimalUtils.add(totalReferralRewards, BigDecimalUtils.mul(referralRewards.getTotalReceived(), c_baseExchangeRate.getExchangeRate(), 9), 9);
                } else {
//                    availableReferralRewards = BigDecimalUtils.add(availableReferralRewards, referralRewards.getAvailable(), 9);
                    totalReferralRewards = BigDecimalUtils.add(totalReferralRewards, referralRewards.getTotalReceived(), 9);
                }
            }

            res.setAvailableCommissionReward(availableCommissionRewards)//可用佣金
                    .setTotalCommissionReward(totalCommissionRewards)//总佣金
                    .setAvailableReferralRewards(availableReferralRewards)//可用推荐奖励
                    .setTotalReferralRewards(totalReferralRewards)//总推荐奖励
                    .setAvailableTeamReward(availableTeamRewards)
                    .setTotalTeamReward(totalTeamRewards)
                    .setAvailableThreeLevelReward(availableThreeLevelRewards)
                    .setTotalThreeLevelReward(totalThreeLevelRewards)
                    .setLockedRewards(CurrencyMrg.getInstance().buildCurrency(playerPromote.getLockedCurrencyId(), playerPromote.getLockedReward()));//锁住推荐奖励
            MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
        } catch (Exception e) {
            LOGGER.error("ReqMyRewardDataHandler", e);
            res.setError(ErrorCode.Internal_Server_Error.getCode());
            MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
        }
    }

}
