package com.game.handler.tcp.activity;

import com.game.c_entity.merchant.C_Activity;
import com.game.c_entity.merchant.C_ActivityTag;
import com.game.c_entity.merchant.C_GameApi;
import com.game.c_entity.middleplatform.C_BaseExchangeRate;
import com.game.c_entity.middleplatform.C_BaseMerchant;
import com.game.dao.ActivityLimitDao;
import com.game.engine.io.handler.TcpHandler;
import com.game.engine.math.BigDecimalUtils;
import com.game.engine.script.IHandlerEntity;
import com.game.engine.script.ScriptLoader;
import com.game.engine.utils.TimeUtil;
import com.game.entity.ActivityLimit;
import com.game.entity.player.Player;
import com.game.entity.player.activity.ActivityData;
import com.game.entity.player.activity.ActivityInfo;
import com.game.enums.ActivityType;
import com.game.enums.Currency;
import com.game.enums.ErrorCode;
import com.game.hall.mrg.ActivityMrg;
import com.game.hall.mrg.CommonMrg;
import com.game.hall.mrg.DataHallMrg;
import com.game.hall.mrg.MerchantData;
import com.game.hall.mrg.player.PlayerMrg;
import com.game.hall.script.activity.IActivityScript;
import com.game.manager.EntityDaoMrg;
import com.proto.ActivityMessage;
import com.proto.MIDMessage;
import io.netty.util.internal.StringUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.time.DayOfWeek;
import java.util.*;

@IHandlerEntity(mid = MIDMessage.MID.ReqPromotionsData_VALUE, msg = ActivityMessage.ReqPromotionsDataMessage.class)
public class ReqPromotionDataHandler extends TcpHandler {
    private static final Logger LOGGER = LoggerFactory.getLogger(ReqPromotionDataHandler.class);

    @Override
    public void run() {
        final ActivityMessage.ResPromotionsDataMessage.Builder res = ActivityMessage.ResPromotionsDataMessage.newBuilder();
        res.setMsgID(MIDMessage.MID.ResPromotionsData_VALUE);
        try {
            final ActivityMessage.ReqPromotionsDataMessage req = (ActivityMessage.ReqPromotionsDataMessage) getMessage();
            final String host = req.getHost();
            final int language = req.getLanguage();
            final C_BaseMerchant c_baseMerchant = DataHallMrg.getInstance().findC_BaseHostMerchant(this.getClass().getSimpleName(), host);
            if (c_baseMerchant == null) {
                res.setError(ErrorCode.Data_Error.getCode());
                replyWithUdpSessionId(res.build());
                return;
            }

            final String business_no = c_baseMerchant.getBusiness_no();
            final MerchantData merchantData = DataHallMrg.getInstance().findMerchantData(this.getClass().getSimpleName(), business_no);
            if (merchantData == null) {
                res.setError(ErrorCode.Data_Error.getCode());
                replyWithUdpSessionId(res.build());
                return;
            }

            final List<C_Activity> c_activities = new ArrayList<>(merchantData.getC_activityMap().values());
            c_activities.sort(Comparator.comparingInt(C_Activity::getSort));

            Player player = null;
            if (pid > 0) {
                player = PlayerMrg.getInstance().getOnlinePlayer(pid, this.getClass().getSimpleName());
                if (player == null) {
                    res.setError(ErrorCode.Player_Offline.getCode());
                    replyWithUdpSessionId(res.build());
                    return;
                }
                //检测归档数据
                for (C_Activity c_activity : c_activities) {
                    if (c_activity.getActivityType() == ActivityType.ManualAward.getType()) {
                        continue;
                    }
                    archivedClearData(player, c_activity);
                }
            }

            for (C_Activity c_activity : c_activities) {
                if (TimeUtil.currentTimeMillis() > c_activity.getEndTime() + c_activity.getAwardClearTime()) {
                    continue;
                }

                int signUpStatus = 0;
                long expirationTime = 0;
                int status = 0;
                if (player != null) {
                    if (c_activity.channelLimit(player.getChannelId())) {
                        continue;
                    }

                    expirationTime = switch (c_activity.getSettlementCycle()) {
                        case 0 -> c_activity.getEndTime();
                        case 1 -> TimeUtil.getTimeEndOfToday(TimeUtil.currentTimeMillis(), c_activity.getTimeZone());
                        case 2 -> TimeUtil.getDayOfWeekEndTimestamp(TimeUtil.currentTimeMillis(), c_activity.getTimeZone(), DayOfWeek.SUNDAY);
                        default -> expirationTime;
                    };

                    if (c_activity.getActivityId() == ActivityMrg.COMPENSATE) {
                        final ActivityInfo activityInfo = player.getActivityInfo();
                        final int activityId = Integer.parseInt(c_activity.getActivityId() + "" + c_activity.getActivitySubType());
                        final ActivityData activityData = activityInfo.getActivityData(activityId);
                        if (activityData != null) {
                            expirationTime = activityData.getEndTime(c_activity.getActivitySubType());
                        }
                    }

                    if (c_activity.getActivityType() == 7) {//排行
                        final ActivityInfo activityInfo = player.getActivityInfo();
                        final int activityId = Integer.parseInt(c_activity.getActivityId() + "" + c_activity.getActivitySubType());
                        final ActivityData activityData = activityInfo.getActivityData(activityId);
                        if (activityData != null) {
                            signUpStatus = activityData.getSignUpMap().getOrDefault(c_activity.getActivitySubType(), 0);
                        }
                    }

                    if (c_activity.getActivityType() != ActivityType.ManualAward.getType()) {
                        final String scriptName = Objects.requireNonNull(ActivityType.valueOf(c_activity.getActivityType())).getName();
                        final Player finalPlayer = player;
                        status = ScriptLoader.getInstance().functionScript(scriptName,
                                (IActivityScript script) -> script.receiveStatus(finalPlayer, c_activity));

                        if (c_activity.getActivityId() == ActivityMrg.COMPENSATE) {
                            final ActivityLimit activityLimit = EntityDaoMrg.getInstance().getDao(ActivityLimitDao.class)
                                    .findByIp(player.getBusiness_no(), player.getIp(), c_activity.getActivityId(), c_activity.getC_id());

                            final C_Activity.ConditionInfo conditionInfo = c_activity.findCondition(player.getCurrencyId());
                            if (activityLimit != null && conditionInfo != null && activityLimit.getCount() >= conditionInfo.getReceiveTimes()) {
                                status = 2;
                            }
                        }
                    }
                }

                final C_Activity.Activity activity = c_activity.getActivityMap().get(language);
                if (activity != null) {
                    res.addPromotionsList(buildPromotionsInfo(player, merchantData, c_activity, activity, expirationTime, signUpStatus, status));
                }
            }

            replyWithUdpSessionId(res.build());
        } catch (Exception e) {
            LOGGER.error("ReqPromotionDataHandler", e);
            res.setError(ErrorCode.Internal_Server_Error.getCode());
            replyWithUdpSessionId(res.build());
        }
    }

    private void archivedClearData(Player player, C_Activity c_activity) {
        if (TimeUtil.currentTimeMillis() >= c_activity.getEndTime() + c_activity.getAwardClearTime()) {
            final String scriptName = Objects.requireNonNull(ActivityType.valueOf(c_activity.getActivityType())).getName();
            ScriptLoader.getInstance().consumerScript(scriptName, (IActivityScript script) ->
                    script.clearData(player, c_activity));
        }
    }

    /**
     * int32 activityId                             = 1; //活动id
     * int32 type                                   = 2; //活动类型 1.首充 2.充值 3.洗码 4.排行
     * int32 subType                                = 3; //子类型
     * string language                              = 4; //语言
     * string name                                  = 5; //名称
     * string content                               = 6; //内容
     * string pictureUrl                            = 7; //图片地址
     * int32 status                                 = 8; //状态 1.开启 2.关闭
     * int64 startTime                              = 9; //开始时间
     * int64 endTime                                =10; //结束时间
     * DItemShow totalPrize                         =11; //总奖励
     * int32 activityTag                            =12; //活动标签
     * int32 jumpType                               =24; //跳转类型 1.内连 2.外链
     * int32 popupLinks                             =25; //弹框类型 1.任务 2.转盘 3.充值 4.客服
     * string innerLinks                            =26; //内部链接
     * string externalLinks                         =27; //外链接
     * repeated RewardPosition rewardPositionList   =28; //奖励挡位
     *
     * @param player
     * @param merchantData
     * @param c_activity
     * @param expirationTime
     * @param signUpStatus
     * @return
     */
    private ActivityMessage.PromotionsInfo buildPromotionsInfo(Player player, MerchantData merchantData, C_Activity c_activity,
                                                               C_Activity.Activity activity, long expirationTime, int signUpStatus,
                                                               int status) {
        final ActivityMessage.PromotionsInfo.Builder promotionsInfo = ActivityMessage.PromotionsInfo.newBuilder();
        if (c_activity.getWageredCondition() != null) {
            for (int gameId : c_activity.getWageredCondition().getGameId()) {
                final C_GameApi c_gameApi = merchantData.findC_GameApi(this.getClass().getSimpleName(), gameId);
                if (c_gameApi != null && c_gameApi.setLanguageGameApiData(activity.getLanguage())) {
                    promotionsInfo.addGameTarget(buildGameTarget(c_gameApi));
                }
            }
            promotionsInfo.setGameTypeTarget(c_activity.getWageredCondition().getGameType());
        }

        int currencyId = 0;
        int language = 0;
        if (player != null) {
            language = player.getLanguage();
            currencyId = player.getCurrencyId();
        } else {
            final String initLanguage = merchantData.findC_GlobalValue(this.getClass().getSimpleName(), "initLanguage");
            language = Integer.parseInt(StringUtil.isNullOrEmpty(initLanguage) ? "1" : initLanguage);
            final String initCurrencyId = merchantData.findC_GlobalValue(this.getClass().getSimpleName(), "initCurrencyId");
            currencyId = Integer.parseInt(StringUtil.isNullOrEmpty(initCurrencyId) ? "0" : initCurrencyId);
        }
        promotionsInfo.setStatus(status)
                .setSignUpStatus(signUpStatus)
                .setExpirationTime(expirationTime)
                .setLanguage(activity.getLanguage())
                .setTitle(activity.getTitle())
                .setName(activity.getActivityName())
                .setContent(activity.getContent())
                .setPictureUrl(activity.getIcon())
                .setInnerPageIcon(activity.getInnerIcon())
                .setPrizeShow(activity.getShowRewardType())
                .setButtonWord(activity.getButtonWord())
                .setTotalPrize(CommonMrg.buildDItemShow(activity.getShowRewardCurrencyId(), activity.getShowRewardAmount()))
                .setCid(c_activity.getC_id())
                .setActivityId(c_activity.getActivityId())
                .setType(c_activity.getActivityType())
                .setSubType(c_activity.getActivitySubType())
                .setStartTime(c_activity.getStartTime())
                .setEndTime(c_activity.getEndTime())
                .setStartSignUpTime(c_activity.getStartSignUpTime())
                .setEndSignUpTime(c_activity.getEndSignUpTime())
//                .setButton(c_activity.isButton())
                .setJumpType(c_activity.getJumpType())
                .setPopupLinks(c_activity.getPopupLinks())
                .setInnerLinks(c_activity.getInnerLinks())
                .setExternalLinks(c_activity.getExternalLinks())
                .setActivityTag(c_activity.getActivityTag())
                .setFunctionType(c_activity.getFunctionType())
                .setSort(c_activity.getSort());

        final C_ActivityTag c_activityTag = merchantData.findC_ActivityTag(this.getClass().getSimpleName(), c_activity.getActivityTag());
        if (c_activityTag != null) {
            promotionsInfo.setTagSort(c_activityTag.getSort());
            final C_ActivityTag.ActivityTagInfo activityTagInfo = c_activityTag.getActivityTagInfoMap().get(language);
            if (activityTagInfo != null) {
                promotionsInfo.setTagName(activityTagInfo.getTagName());
            }
        }

        if (c_activity.isEvUsd()) {
            currencyId = Currency.USD.getCurrencyId();
        }
        final List<C_Activity.RewardInfo> rewardInfos = c_activity.getRewardsMap().get(currencyId);
        if (rewardInfos != null && !rewardInfos.isEmpty()) {
            for (C_Activity.RewardInfo rewardInfo : rewardInfos) {
                String gameName = "";
                if (rewardInfo.getGameId() > 0) {
                    final C_GameApi c_gameApi = merchantData.findC_GameApi(this.getClass().getSimpleName(), rewardInfo.getGameId());
                    if (c_gameApi != null && c_gameApi.setLanguageGameApiData(language)) {
                        gameName = c_gameApi.getGameName();
                    }
                }
                promotionsInfo.addRewardPositionList(buildRewardPosition(rewardInfo, gameName));
            }
        }
        if (player != null) {
            int activityId = 0;
            if (c_activity.getActivityId() == ActivityMrg.WAGERED) {
                final C_Activity.WageredCondition condition = c_activity.getWageredCondition();
                if (condition != null) {
                    final String gameType = condition.getGameType() == 0 ? "" : condition.getGameType() + "";
//                    final String platformId = condition.getPlatformId() == 0 ? "" : condition.getPlatformId() + "";
                    activityId = Integer.parseInt(c_activity.getActivityId() + gameType);
                }
            } else {
                activityId = Integer.parseInt(c_activity.getActivityId() + "" + c_activity.getActivitySubType());
            }
            final ActivityInfo activityInfo = player.getActivityInfo();
            final ActivityData activityData = activityInfo.getActivityData(activityId);
            if (activityData != null) {
                promotionsInfo.setCurrencyId(currencyId);
                double progress = 0;
                if (c_activity.isEvUsd()) {
                    for (Map.Entry<Integer, Double> entry : activityData.getProgressMap().entrySet()) {
                        final C_BaseExchangeRate c_baseExchangeRate = DataHallMrg.getInstance().findC_BaseExchangeRate(this.getClass().getSimpleName(), entry.getKey());
                        if (c_baseExchangeRate == null) {
                            continue;
                        }
                        progress = BigDecimalUtils.add(progress, BigDecimalUtils.mul(entry.getValue(), c_baseExchangeRate.getExchangeRate(), 4), 4);
                    }
                } else {
                    progress = activityData.getProgress(currencyId);
                }
                promotionsInfo.setCurWagered(progress);

                if (c_activity.getActivityId() == ActivityMrg.MANY_DEPOSIT) {
                    promotionsInfo.setRegisterExpired(activityData.getRegisterExpired());
                }
            }
        }
        return promotionsInfo.build();
    }

    /**
     * int32 rewardCurrency                         = 1; //奖励币种
     * double min                                   = 2; //最小值
     * double max                                   = 3; //最大值
     * int32 rewardType                             = 4;//1.固定 2.比例
     * double reward                                = 5;
     * int32 gameId                                 = 6;//
     * string gameName                              = 7;//游戏名字
     * int32 freeTimes                              = 8;//免费次数
     *
     * @param rewardInfo
     * @return
     */
    private ActivityMessage.RewardPosition buildRewardPosition(C_Activity.RewardInfo rewardInfo, String gameName) {
        final ActivityMessage.RewardPosition.Builder rewardPosition = ActivityMessage.RewardPosition.newBuilder()
                .setRewardCurrency(rewardInfo.rewardCurrency)
                .setMin(rewardInfo.min)
                .setMax(rewardInfo.max)
                .setRewardType(rewardInfo.rewardType)
                .setReward(rewardInfo.reward)
                .setGameId(rewardInfo.gameId)
                .setGameName(gameName)
                .setFreeTimes(rewardInfo.freeTimes);
        return rewardPosition.build();
    }

    /**
     * int32 platformId                             = 1; //平台id
     * string platformName                          = 2; //平台名字
     * string icon                                  = 3; //图片
     * int32 gameId                                 = 4; //游戏id
     * string gameName                              = 5; //游戏名字
     * int32 gameType                               = 6; //游戏类型
     *
     * @return
     */
    private ActivityMessage.GameTarget buildGameTarget(C_GameApi c_gameApi) {
        return ActivityMessage.GameTarget.newBuilder()
                .setPlatformId(c_gameApi.getPlatformId())
                .setPlatformName(c_gameApi.getPlatformName())
                .setIcon(c_gameApi.getFileUrl())
                .setGameId(c_gameApi.getGameId())
                .setGameType(c_gameApi.getType())
                .setGameName(c_gameApi.getGameName())
                .build();
    }
}
