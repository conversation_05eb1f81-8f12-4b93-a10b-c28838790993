package com.game.handler.tcp.hall;

import com.game.dao.player.PlayerDao;
import com.game.dao.player.PlayerPromoteDao;
import com.game.engine.io.handler.TcpHandler;
import com.game.engine.io.redis.RedisPoolManager;
import com.game.engine.script.IHandlerEntity;
import com.game.engine.utils.MsgUtil;
import com.game.entity.player.Player;
import com.game.entity.player.PlayerFields;
import com.game.entity.player.promote.PlayerPromote;
import com.game.entity.player.promote.PlayerPromoteFields;
import com.game.enums.ErrorCode;
import com.game.enums.redis.RedisLogin;
import com.game.hall.mrg.player.PlayerMrg;
import com.game.manager.EntityDaoMrg;
import com.proto.HallMessage;
import com.proto.MIDMessage;
import io.lettuce.core.api.async.RedisAsyncCommands;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.CompletableFuture;


@IHandlerEntity(mid = MIDMessage.MID.ReqNameModify_VALUE, msg = HallMessage.ReqNameModifyMessage.class)
public class ReqNameModifyHandler extends TcpHandler {
    private static final Logger LOGGER = LoggerFactory.getLogger(ReqNameModifyHandler.class);

    @Override
    public void run() {
        final HallMessage.ResNameModifyMessage.Builder res = HallMessage.ResNameModifyMessage.newBuilder();
        res.setMsgID(MIDMessage.MID.ResNameModify_VALUE);
        try {
            final Player player = PlayerMrg.getInstance().getOnlinePlayer(pid, this.getClass().getSimpleName());
            if (player == null) {
                res.setError(ErrorCode.Player_Offline.getCode());
                MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                return;
            }

            final HallMessage.ReqNameModifyMessage req = (HallMessage.ReqNameModifyMessage) getMessage();
            final String userName = req.getUserName().trim();

            //字符长度
            if (userName.length() < 6 || userName.length() > 15) {
                res.setError(ErrorCode.UserName_Length.getCode());
                MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                return;
            }

            //TODO 检测敏感词汇
//            if (WordFilter.getInstance().hashBadWords(userName)) {
//                res.setError(ErrorCode.UserName_IllegalCharacter.getCode());
//                MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
//                return;
//            }

            final boolean isExist = RedisPoolManager.getInstance().function(jedis ->
                    jedis.sync().sismember(RedisLogin.Platform_LG_Set_UserName.getKey(player.getBusiness_no()), userName));
            if (isExist) {
                res.setError(ErrorCode.UserName_Repeat.getCode());
                MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                return;
            }

            final String oldName = player.getPlayerName();
            RedisPoolManager.getInstance().asyncPipeline(commands -> {
                final List<CompletableFuture<?>> futures = new ArrayList<>();
                futures.add(commands.srem(RedisLogin.Platform_LG_Set_UserName.getKey(player.getBusiness_no()), oldName)
                        .toCompletableFuture());
                futures.add(commands.sadd(RedisLogin.Platform_LG_Set_UserName.getKey(player.getBusiness_no()), userName)
                        .toCompletableFuture());
                return futures;
            });

            player.setPlayerName(userName);
            EntityDaoMrg.getInstance().getDao(PlayerDao.class)
                    .updatePlayerField(pid, PlayerFields.playerName, userName);

            final PlayerPromote playerPromote = player.getPlayerPromote();
            playerPromote.setPlayerName(userName);
            EntityDaoMrg.getInstance().getDao(PlayerPromoteDao.class)
                    .updatePromotionField(pid, PlayerPromoteFields.playerName, userName);

            res.setUserName(userName);
            MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
        } catch (Exception e) {
            LOGGER.error("ReqNameModifyHandler", e);
            res.setError(ErrorCode.Internal_Server_Error.getCode());
            MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
        }
    }
}
