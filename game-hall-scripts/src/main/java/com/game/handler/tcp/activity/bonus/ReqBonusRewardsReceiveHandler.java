package com.game.handler.tcp.activity.bonus;

import com.game.c_entity.merchant.C_VipClub;
import com.game.dao.player.PlayerDao;
import com.game.engine.io.handler.TcpHandler;
import com.game.engine.log.GameLog;
import com.game.engine.math.BigDecimalUtils;
import com.game.engine.script.IHandlerEntity;
import com.game.engine.script.ScriptLoader;
import com.game.engine.utils.FastCollectionsUtils;
import com.game.engine.utils.GuidGeneratorUtils;
import com.game.engine.utils.MsgUtil;
import com.game.engine.utils.TimeUtil;
import com.game.entity.player.Player;
import com.game.entity.player.bonus.BonusDetailsInfo;
import com.game.entity.player.bonus.BonusInfo;
import com.game.entity.player.bonus.BonusProcessInfo;
import com.game.entity.player.vip.VipClub;
import com.game.enums.*;
import com.game.hall.main.HallServer;
import com.game.hall.mrg.DataHallMrg;
import com.game.hall.mrg.MerchantData;
import com.game.hall.mrg.currency.CurrencyMrg;
import com.game.hall.mrg.currency.RewardRequest;
import com.game.hall.mrg.player.PlayerMrg;
import com.game.hall.script.IBonusScript;
import com.game.hall.script.IPlayerScript;
import com.game.manager.EntityDaoMrg;
import com.proto.ActivityMessage;
import com.proto.MIDMessage;
import it.unimi.dsi.fastutil.ints.Int2DoubleMap;
import it.unimi.dsi.fastutil.ints.IntArrayList;
import it.unimi.dsi.fastutil.ints.IntList;
import it.unimi.dsi.fastutil.objects.ObjectIterator;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Map;

@IHandlerEntity(mid = MIDMessage.MID.ReqVipBonusRewardsReceive_VALUE, msg = ActivityMessage.ReqVipBonusRewardsReceiveMessage.class)
public class ReqBonusRewardsReceiveHandler extends TcpHandler {
    private static final Logger LOGGER = LoggerFactory.getLogger(ReqBonusRewardsReceiveHandler.class);

    @Override
    public void run() {
        final ActivityMessage.ResVipBonusRewardsReceiveMessage.Builder res = ActivityMessage.ResVipBonusRewardsReceiveMessage.newBuilder();
        res.setMsgID(MIDMessage.MID.ResVipBonusRewardsReceive_VALUE);
        try {
            final Player player = PlayerMrg.getInstance().getOnlinePlayer(pid, this.getClass().getSimpleName());
            if (player == null) {
                res.setError(ErrorCode.Player_Offline.getCode());
                MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                return;
            }

            final ActivityMessage.ReqVipBonusRewardsReceiveMessage req = (ActivityMessage.ReqVipBonusRewardsReceiveMessage) getMessage();
            final int bonusType = req.getBonusType();

            final BonusInfo bonusInfo = player.getBonusInfo();
            final BonusProcessInfo bonusProcessInfo = bonusInfo.getBonusProcessInfo(bonusType);

            if (bonusProcessInfo.getRewardMap().isEmpty()) {
                MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                return;
            }

            final MerchantData merchantData = DataHallMrg.getInstance().findMerchantData(this.getClass().getSimpleName(), player.getBusiness_no());
            if (merchantData == null) {
                res.setError(ErrorCode.Data_Error.getCode());
                MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                return;
            }

            final VipClub vipClub = player.getVipClub();
            final C_VipClub c_vipClub = merchantData.findC_VipClub(this.getClass().getSimpleName(), vipClub.getVipLevel());
            if (c_vipClub == null) {
                res.setError(ErrorCode.Data_Error.getCode());
                MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                return;
            }

            double turnoverMul;
            int rewardType = 0;
            if (bonusType == BonusDetail.LevelUpBonus.getType()) {
                turnoverMul = c_vipClub.getUpLevelTurnoverMul();
                rewardType = c_vipClub.getUpLevelRewardType();
            } else if (bonusType == BonusDetail.Recharge.getType()) {
                turnoverMul = c_vipClub.getWagerTurnoverMul();
                rewardType = c_vipClub.getChargeRewardType();
            } else if (bonusType == BonusDetail.WeeklyCashBack.getType()) {
                turnoverMul = c_vipClub.getWeeklyRateTurnoverMul();
                rewardType = c_vipClub.getCashBackRewardType();
            } else if (bonusType == BonusDetail.MonthlyCashBack.getType()) {
                turnoverMul = c_vipClub.getMonthlyRateTurnoverMul();
                rewardType = c_vipClub.getCashBackRewardType();
            } else {
                turnoverMul = 0;
            }
            if (rewardType == 0) {
                rewardType = 2;
            }

            final RewardRequest rewardRequest = new RewardRequest();
            for (Map.Entry<Integer, Double> entry : bonusProcessInfo.getRewardMap().entrySet()) {
                rewardRequest.addCurrency(entry.getKey(), entry.getValue());
            }
            final RewardReason rewardReason = RewardReason.VipBonus;
            rewardReason.setSource(bonusType + "");
            if (rewardType == 1) {//1.cash 2.bonus
                CurrencyMrg.getInstance().rewardNoTest(player, rewardRequest.getCurrencyMap(), rewardReason);
            } else {
                CurrencyMrg.getInstance().rewardNoTest(player, rewardRequest.getBonusCurrencyMap(), rewardReason);
            }

            rewardRequest.writeToItemShowMsg(res::addShow);
            MsgUtil.responseHttp(res.build(), res.getMsgID(), session);

            bonusProcessInfo.getRewardMap().clear();

            //TODO 计算打码量

            final IntList intList = new IntArrayList();
            final BonusDetailsInfo bonusDetailsInfo = bonusInfo.getBonusDetailsInfo(bonusType);
            for (ObjectIterator<Int2DoubleMap.Entry> iterator = FastCollectionsUtils.fastIterator(rewardRequest.getCurrencyMap()); iterator.hasNext(); ) {
                final Int2DoubleMap.Entry entry = iterator.next();
                bonusDetailsInfo.incBonus(entry.getIntKey(), entry.getDoubleValue());
                intList.add(entry.getIntKey());

//                final WithdrawStandard withdrawStandard = player.getWithdrawStandard(entry.getIntKey());
                //withdrawStandard.incDrawStandard(BigDecimalUtils.mul(entry.getDoubleValue(), turnoverMul, 4));
                final TurnoverReason turnoverReason = TurnoverReason.VipBonus;
                turnoverReason.setSource("" + bonusType);
                ScriptLoader.getInstance().consumerScript("PlayerWithdrawStandardScript",
                        (IPlayerScript script) -> script.drawStandard(player, turnoverReason, entry.getIntKey(), entry.getDoubleValue(),
                                BigDecimalUtils.mul(entry.getDoubleValue(), turnoverMul, 4)));

                ScriptLoader.getInstance().consumerScript("BonusScript",
                        (IBonusScript script) -> script.addBonusNote(TransactionFrom.valuesOf(bonusType * 100), player, entry.getIntKey(), entry.getDoubleValue()));

                if (bonusType == BonusDetail.Recharge.getType() || bonusType == BonusDetail.WeeklyCashBack.getType()
                        || bonusType == BonusDetail.MonthlyCashBack.getType()) {
                    //TODO 玩家vip 返水
                    final GameLog playerVipCashBackLog = new GameLog("platform_playerVipCashBackLog");
                    playerVipCashBackLog.append("number", GuidGeneratorUtils.generateOrderId())
                            .append("business_no", player.getBusiness_no())
                            .append("site", player.getWebSite())
                            .append("playerId", player.getPlayerId())
                            .append("playerName", player.getPlayerName())
                            .append("bonusType", bonusType)//7.周 8.月
                            .append("currencyId", entry.getIntKey())
                            .append("amount", entry.getDoubleValue())
                            .append("channelId", player.getChannelId())
                            .append("agentId", player.getAgentId())
                            .append("mediaId", player.getMediaId())
                            .append("adId", player.getAdId())
                            .append("region", player.getRegisterRegion())
                            .append("logTime", TimeUtil.currentTimeMillis());
                    HallServer.getInstance().getLogProducerMrg().send(playerVipCashBackLog);
                }
            }
            EntityDaoMrg.getInstance().getDao(PlayerDao.class)
                    .bonusInfoDao.updateBonusDetails(player, bonusType, bonusDetailsInfo, intList);
        } catch (Exception e) {
            LOGGER.error("ReqBonusRewardsReceiveHandler", e);
            res.setError(ErrorCode.Internal_Server_Error.getCode());
            MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
        }
    }
}
