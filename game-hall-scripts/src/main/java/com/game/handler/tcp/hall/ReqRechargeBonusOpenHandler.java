package com.game.handler.tcp.hall;

import com.game.dao.player.PlayerDao;
import com.game.engine.io.handler.TcpHandler;
import com.game.engine.script.IHandlerEntity;
import com.game.engine.utils.MsgUtil;
import com.game.entity.player.Player;
import com.game.entity.player.PlayerFields;
import com.game.enums.ErrorCode;
import com.game.hall.mrg.player.PlayerMrg;
import com.game.manager.EntityDaoMrg;
import com.proto.HallMessage;
import com.proto.MIDMessage;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.mongodb.core.query.Update;

@IHandlerEntity(mid = MIDMessage.MID.ReqRechargeBonusOpen_VALUE, msg = HallMessage.ReqRechargeBonusOpenMessage.class)
public class ReqRechargeBonusOpenHandler extends TcpHandler {
    private static final Logger LOGGER = LoggerFactory.getLogger(ReqRechargeBonusOpenHandler.class);

    @Override
    public void run() {
        final HallMessage.ResRechargeBonusOpenMessage.Builder res = HallMessage.ResRechargeBonusOpenMessage.newBuilder();
        res.setMsgID(MIDMessage.MID.ResRechargeBonusOpen_VALUE);
        try {
            final Player player = PlayerMrg.getInstance().getOnlinePlayer(pid, this.getClass().getSimpleName());
            if (player == null) {
                res.setError(ErrorCode.Player_Offline.getCode());
                MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                return;
            }

            final HallMessage.ReqRechargeBonusOpenMessage req = (HallMessage.ReqRechargeBonusOpenMessage) getMessage();
            final boolean open = req.getOpen();

            player.setRechargeBonus(open);
            final Update update = new Update();
            update.set(PlayerFields.rechargeBonus, player.isRechargeBonus());
            EntityDaoMrg.getInstance().getDao(PlayerDao.class)
                    .updatePlayer(pid, update);

            res.setOpen(open);
            MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
        } catch (Exception e) {
            LOGGER.error("ReqRechargeBonusOpenHandler", e);
            res.setError(ErrorCode.Internal_Server_Error.getCode());
            MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
        }
    }
}
