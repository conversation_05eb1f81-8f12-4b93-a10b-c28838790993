package com.game.handler.tcp;

import com.game.dao.player.PlayerDao;
import com.game.engine.io.handler.TcpHandler;
import com.game.engine.script.IHandlerEntity;
import com.game.engine.script.ScriptLoader;
import com.game.engine.utils.MsgUtil;
import com.game.entity.player.Player;
import com.game.entity.player.PlayerFields;
import com.game.hall.mrg.player.PlayerMrg;
import com.game.hall.script.IAgentGameScript;
import com.game.manager.EntityDaoMrg;
import com.proto.MIDMessage;
import com.proto.TcpMessage;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.mongodb.core.query.Update;

@IHandlerEntity(mid = MIDMessage.MID.ReqTcpQuitAgentGame_VALUE, msg = TcpMessage.ReqTcpQuitAgentGameMessage.class)
public class ReqTcpQuitAgentGameHandler extends TcpHandler {
    private static final Logger LOGGER = LoggerFactory.getLogger(ReqTcpQuitAgentGameHandler.class);

    @Override
    public void run() {
        final TcpMessage.ResTcpQuitAgentGameMessage.Builder res = TcpMessage.ResTcpQuitAgentGameMessage.newBuilder();
        res.setMsgID(MIDMessage.MID.ResTcpQuitAgentGame_VALUE);

        final Player player = PlayerMrg.getInstance().findDbPlayer(pid);
        if (player == null) {
            return;
        }

        if (player.getGameId() > 0) {
            //TODO 通知更新游戏信息
            //CommonMrg.getInstance().notifyUpdateGameInfo(2, player.getGameId());
            LOGGER.warn("playerId：{}，quit gameId：{}", player.getPlayerId(), player.getGameId());

            player.resetGameInfo();
            final Update update = new Update();
            update.set(PlayerFields.gameId, player.getGameId())
                    .set(PlayerFields.bonus, player.isBonus())
                    .set(PlayerFields.platformId, player.getPlatformId())
                    .set(PlayerFields.gameType, player.getGameType())
                    .set(PlayerFields.gameCurrencyId, player.getGameCurrencyId())
                    .set(PlayerFields.playerCurrencyId, player.getPlayerCurrencyId());
            EntityDaoMrg.getInstance().getDao(PlayerDao.class)
                    .updatePlayer(player.getPlayerId(), update);

            ScriptLoader.getInstance().consumerScript("AgentGameScript",
                    (IAgentGameScript script) -> script.statisticalGameNoteData(player));
        }

        MsgUtil.sendClientMsg(session, res.build());
    }


}
