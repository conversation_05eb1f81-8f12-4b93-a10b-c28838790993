package com.game.handler.tcp.hall;

import com.game.dao.account.AccountDao;
import com.game.dao.player.PlayerDao;
import com.game.engine.io.handler.TcpHandler;
import com.game.engine.io.redis.RedisPoolManager;
import com.game.engine.script.IHandlerEntity;
import com.game.engine.utils.MsgUtil;
import com.game.entity.account.Account;
import com.game.entity.player.Player;
import com.game.enums.ErrorCode;
import com.game.enums.redis.RedisLogin;
import com.game.hall.mrg.player.PlayerMrg;
import com.game.manager.EntityDaoMrg;
import com.game.utils.VirtualThreadUtils;
import com.proto.CommonMessage;
import com.proto.HallMessage;
import com.proto.MIDMessage;
import io.netty.util.internal.StringUtil;
import it.unimi.dsi.fastutil.ints.IntLists;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@IHandlerEntity(mid = MIDMessage.MID.ReqBindThreeParty_VALUE, msg = HallMessage.ReqBindThreePartyMessage.class)
public class ReqBindThreePartyHandler extends TcpHandler {
    private static final Logger LOGGER = LoggerFactory.getLogger(ReqBindThreePartyHandler.class);

    @Override
    public void run() {
        final HallMessage.ResBindThreePartyMessage.Builder res = HallMessage.ResBindThreePartyMessage.newBuilder();
        res.setMsgID(MIDMessage.MID.ResBindThreeParty_VALUE);
        try {
            final Player player = PlayerMrg.getInstance().getOnlinePlayer(pid, this.getClass().getSimpleName());
            if (player == null) {
                res.setError(ErrorCode.Player_Offline.getCode());
                MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                return;
            }

            final HallMessage.ReqBindThreePartyMessage req = (HallMessage.ReqBindThreePartyMessage) getMessage();
            final CommonMessage.ThreePartyInfo threePartyInfo = req.getThreePartyInfo();
            final int threeParty = threePartyInfo.getThreeParty();
            final String account = threePartyInfo.getAccount();
            final String threePartyId = threePartyInfo.getThreePartyId();

            final String accountId = RedisPoolManager.getInstance().function(jedis ->
                    jedis.sync().hget(RedisLogin.Platform_LG_Map_AccountID.getKey(player.getBusiness_no(), threeParty), threePartyId));
            if (!StringUtil.isNullOrEmpty(accountId)) {
                LOGGER.warn("playerId：{}，threeParty：{}，already bind", pid, threeParty);
                res.setError(ErrorCode.ThreeParty_AlreadyBind.getCode());
                MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                return;
            }

            player.addThreePartyInfo(threeParty, account, threePartyId);
            EntityDaoMrg.getInstance().getDao(PlayerDao.class)
                    .updateInsertThreePartyInfo(player, IntLists.singleton(threeParty));

            final Account ac = PlayerMrg.getInstance().findDbAccount(pid);
            ac.addThreePartyInfo(threeParty, account, threePartyId);
            EntityDaoMrg.getInstance().getDao(AccountDao.class).updateInsertThreePartyInfo(ac, IntLists.singleton(threeParty));

            RedisPoolManager.getInstance().executeAsync(commands ->
                    commands.hset(RedisLogin.Platform_LG_Map_AccountID.getKey(player.getBusiness_no(), threeParty), threePartyId, pid + "")
            );

            res.setThreePartyInfo(threePartyInfo);
            MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
        } catch (Exception e) {
            LOGGER.error("ReqBindThreePartyHandler", e);
            res.setError(ErrorCode.Internal_Server_Error.getCode());
            MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
        }
    }

}
