package com.game.handler.tcp.affiliate;

import com.game.engine.io.handler.TcpHandler;
import com.game.engine.script.IHandlerEntity;
import com.game.engine.utils.MsgUtil;
import com.game.entity.player.Player;
import com.game.entity.player.promote.CommissionRewards;
import com.game.entity.player.promote.PlayerPromote;
import com.game.enums.ErrorCode;
import com.game.hall.mrg.player.PlayerMrg;
import com.proto.AffiliateMessage;
import com.proto.MIDMessage;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Map;


@IHandlerEntity(mid = MIDMessage.MID.ReqCommissionRewardData_VALUE, msg = AffiliateMessage.ReqCommissionRewardDataMessage.class)
public class ReqCommissionRewardDataHandler extends TcpHandler {
    private static final Logger LOGGER = LoggerFactory.getLogger(ReqCommissionRewardDataHandler.class);

    @Override
    public void run() {
        final AffiliateMessage.ResCommissionRewardDataMessage.Builder res = AffiliateMessage.ResCommissionRewardDataMessage.newBuilder();
        res.setMsgID(MIDMessage.MID.ResCommissionRewardData_VALUE);
        try {
            final Player player = PlayerMrg.getInstance().getOnlinePlayer(pid, this.getClass().getSimpleName());
            if (player == null) {
                res.setError(ErrorCode.Player_Offline.getCode());
                MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                return;
            }

            final PlayerPromote playerPromote = player.getPlayerPromote();
            for (Map.Entry<Integer, CommissionRewards> entry : playerPromote.getCommissionRewardsMap().entrySet()) {
                res.addReward(buildCommissionReward(entry.getKey(), entry.getValue()));
            }
            MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
        } catch (Exception e) {
            LOGGER.error("ReqCommissionRewardDataHandler", e);
            res.setError(ErrorCode.Internal_Server_Error.getCode());
            MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
        }
    }

    private AffiliateMessage.CommissionReward buildCommissionReward(int currencyId, CommissionRewards commissionRewards) {
        return AffiliateMessage.CommissionReward.newBuilder()
                .setCurrencyId(currencyId)
                .setAvailable(commissionRewards.getAvailable())
                .setTotalReceived(commissionRewards.getTotalReceived())
                .build();
    }
}
