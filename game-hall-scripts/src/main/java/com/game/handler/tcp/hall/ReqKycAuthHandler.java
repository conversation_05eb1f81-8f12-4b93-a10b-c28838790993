package com.game.handler.tcp.hall;

import com.game.dao.player.PlayerDao;
import com.game.engine.io.handler.TcpHandler;
import com.game.engine.log.GameLog;
import com.game.engine.script.IHandlerEntity;
import com.game.engine.utils.GuidGeneratorUtils;
import com.game.engine.utils.MsgUtil;
import com.game.engine.utils.TimeUtil;
import com.game.entity.player.Player;
import com.game.entity.player.PlayerFields;
import com.game.enums.ErrorCode;
import com.game.hall.main.HallServer;
import com.game.hall.mrg.player.PlayerMrg;
import com.game.manager.EntityDaoMrg;
import com.proto.HallMessage;
import com.proto.MIDMessage;
import io.netty.util.internal.StringUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@IHandlerEntity(mid = MIDMessage.MID.ReqKycAuth_VALUE, msg = HallMessage.ReqKycAuthMessage.class)
public class ReqKycAuthHandler extends TcpHandler {
    private static final Logger LOGGER = LoggerFactory.getLogger(ReqKycAuthHandler.class);

    @Override
    public void run() {
        final HallMessage.ResKycAuthMessage.Builder res = HallMessage.ResKycAuthMessage.newBuilder();
        res.setMsgID(MIDMessage.MID.ResKycAuth_VALUE);
        try {
            final Player player = PlayerMrg.getInstance().getOnlinePlayer(pid, this.getClass().getSimpleName());
            if (player == null) {
                res.setError(ErrorCode.Player_Offline.getCode());
                MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                return;
            }

            final HallMessage.ReqKycAuthMessage req = (HallMessage.ReqKycAuthMessage) getMessage();
            final String country = req.getCountry();
            final int documentType = req.getDocumentType();

            switch (req.getAuthType()) {
                case 1://基础
                    final String front = req.getFront();
                    final String back = req.getBack();
                    final String photo = req.getPhoto();
                    if (StringUtil.isNullOrEmpty(country) || documentType == 0 || StringUtil.isNullOrEmpty(photo)) {
                        res.setError(ErrorCode.Kyc_Params_Error.getCode());
                        MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                        return;
                    }

                    if (player.getBasicVerification() == 1) {
                        res.setError(ErrorCode.Kyc_In_Progress.getCode());
                        MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                        return;
                    }

                    player.setBasicVerification(1);
                    EntityDaoMrg.getInstance().getDao(PlayerDao.class)
                            .updatePlayerField(pid, PlayerFields.basicVerification, player.getBasicVerification());

                    MsgUtil.responseHttp(res.build(), res.getMsgID(), session);

                    final GameLog basic = new GameLog("platform_playerKycAuthLog");
                    basic.append("number", GuidGeneratorUtils.generateOrderId())
                            .append("business_no", player.getBusiness_no())
                            .append("playerId", player.getPlayerId())
                            .append("authType", req.getAuthType())
                            .append("country", country)
                            .append("documentType", documentType)
                            .append("front", front)
                            .append("back", back)
                            .append("photo", photo)
                            .append("addressDocument", "")
                            .append("video", "")
                            .append("logTime", TimeUtil.currentTimeMillis());
                    HallServer.getInstance().getLogProducerMrg().send(basic);
                    break;
                case 2://高级
                    final String addressDocument = req.getAddressDocument();
                    final String video = req.getVideo();
                    if (documentType == 0
                            || StringUtil.isNullOrEmpty(addressDocument) || StringUtil.isNullOrEmpty(video)) {
                        res.setError(ErrorCode.Kyc_Params_Error.getCode());
                        MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                        return;
                    }

                    if (player.getAdvancedVerification() == 1) {
                        res.setError(ErrorCode.Kyc_In_Progress.getCode());
                        MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                        return;
                    }

                    player.setAdvancedVerification(1);
                    EntityDaoMrg.getInstance().getDao(PlayerDao.class)
                            .updatePlayerField(pid, PlayerFields.advancedVerification, player.getAdvancedVerification());

                    MsgUtil.responseHttp(res.build(), res.getMsgID(), session);

                    final GameLog advanced = new GameLog("platform_playerKycAuthLog");
                    advanced.append("number", GuidGeneratorUtils.generateOrderId())
                            .append("business_no", player.getBusiness_no())
                            .append("playerId", player.getPlayerId())
                            .append("authType", req.getAuthType())
                            .append("country", country)
                            .append("documentType", documentType)
                            .append("front", "")
                            .append("back", "")
                            .append("photo", "")
                            .append("addressDocument", addressDocument)
                            .append("video", video)
                            .append("logTime", TimeUtil.currentTimeMillis());
                    HallServer.getInstance().getLogProducerMrg().send(advanced);
                    break;
            }
        } catch (Exception e) {
            LOGGER.error("ReqKycAuthHandler", e);
            res.setError(ErrorCode.Internal_Server_Error.getCode());
            MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
        }
    }
}
