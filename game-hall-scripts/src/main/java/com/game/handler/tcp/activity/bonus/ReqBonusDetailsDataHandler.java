package com.game.handler.tcp.activity.bonus;

import com.game.engine.io.handler.TcpHandler;
import com.game.engine.script.IHandlerEntity;
import com.game.engine.utils.MsgUtil;
import com.game.entity.player.Player;
import com.game.entity.player.bonus.BonusDetailsInfo;
import com.game.entity.player.bonus.BonusInfo;
import com.game.enums.ErrorCode;
import com.game.hall.mrg.CommonMrg;
import com.game.hall.mrg.player.PlayerMrg;
import com.proto.ActivityMessage;
import com.proto.MIDMessage;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Map;

@IHandlerEntity(mid = MIDMessage.MID.ReqBonusDetailsData_VALUE, msg = ActivityMessage.ReqBonusDetailsDataMessage.class)
public class ReqBonusDetailsDataHandler extends TcpHandler {
    private static final Logger LOGGER = LoggerFactory.getLogger(ReqBonusDetailsDataHandler.class);

    @Override
    public void run() {
        final ActivityMessage.ResBonusDetailsDataMessage.Builder res = ActivityMessage.ResBonusDetailsDataMessage.newBuilder();
        res.setMsgID(MIDMessage.MID.ResBonusDetailsData_VALUE);
        try {
            final Player player = PlayerMrg.getInstance().getOnlinePlayer(pid, this.getClass().getSimpleName());
            if (player == null) {
                res.setError(ErrorCode.Player_Offline.getCode());
                MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                return;
            }
            final BonusInfo bonusInfo = player.getBonusInfo();
            final ActivityMessage.BonusDetails.Builder bonusDetails = ActivityMessage.BonusDetails.newBuilder();
            for (Map.Entry<Integer, BonusDetailsInfo> entry : bonusInfo.getBonusDetailsMap().entrySet()) {
                bonusDetails.clear();
                bonusDetails.setBonusType(entry.getKey());

                for (Map.Entry<Integer, Double> bonus : entry.getValue().getBonusMap().entrySet()) {
                    bonusDetails.addBonusDetails(CommonMrg.buildDItemShow(bonus.getKey(), bonus.getValue()));
                }

                res.addBonusDetails(bonusDetails.build());
            }
            MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
        } catch (Exception e) {
            LOGGER.error("ReqBonusDetailsDataHandler", e);
            res.setError(ErrorCode.Internal_Server_Error.getCode());
            MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
        }
    }
}
