package com.game.handler.tcp.game;

import com.game.c_entity.middleplatform.C_BaseMerchant;
import com.game.engine.io.handler.TcpHandler;
import com.game.engine.io.redis.RedisPoolManager;
import com.game.engine.script.IHandlerEntity;
import com.game.engine.utils.MsgUtil;
import com.game.entity.player.Player;
import com.game.enums.ErrorCode;
import com.game.enums.redis.RedisAllGame;
import com.game.hall.mrg.DataHallMrg;
import com.game.hall.mrg.player.PlayerMrg;
import com.proto.HallMessage;
import com.proto.MIDMessage;
import io.netty.util.internal.StringUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@IHandlerEntity(mid = MIDMessage.MID.ReqGameOptData_VALUE, msg = HallMessage.ReqGameOptDataMessage.class)
public class ReqGameOptDataHandler extends TcpHandler {
    private static final Logger LOGGER = LoggerFactory.getLogger(ReqGameOptDataHandler.class);

    @Override
    public void run() {
        final HallMessage.ResGameOptDataMessage.Builder res = HallMessage.ResGameOptDataMessage.newBuilder();
        res.setMsgID(MIDMessage.MID.ResGameOptData_VALUE);
        try {
            final HallMessage.ReqGameOptDataMessage req = (HallMessage.ReqGameOptDataMessage) getMessage();
            final int gameId = req.getGameId();
            final String host = req.getHost();

            final C_BaseMerchant c_baseHostMerchant = DataHallMrg.getInstance().findC_BaseHostMerchant(this.getClass().getSimpleName(), host);
            if (c_baseHostMerchant == null) {
                res.setError(ErrorCode.Data_Error.getCode());
                MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                return;
            }

            if (pid > 0) {
                final Player player = PlayerMrg.getInstance().getOnlinePlayer(pid, this.getClass().getSimpleName());
                if (player == null) {
                    res.setError(ErrorCode.Player_Offline.getCode());
                    MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                    return;
                }
                res.setLike(player.getLikeGame().contains(gameId))
                        .setFavorites(player.getFavoritesGame().contains(gameId));
            }

            final String business_no = c_baseHostMerchant.getBusiness_no();
            final String gameCollect = RedisPoolManager.getInstance().function(jedis ->
                    jedis.sync().get(RedisAllGame.Platform_All_GameCollect.getKey(business_no, gameId)));
            final String gameLike = RedisPoolManager.getInstance().function(jedis ->
                    jedis.sync().get(RedisAllGame.Platform_All_GameLike.getKey(business_no, gameId)));

            res.setFavoritesNum(StringUtil.isNullOrEmpty(gameCollect) ? 0 : Integer.parseInt(gameCollect))
                    .setLikeNum(StringUtil.isNullOrEmpty(gameLike) ? 0 : Integer.parseInt(gameLike));
            MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
        } catch (Exception e) {
            LOGGER.error("ReqGameOptDataHandler", e);
            res.setError(ErrorCode.Internal_Server_Error.getCode());
            MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
        }
    }

}
