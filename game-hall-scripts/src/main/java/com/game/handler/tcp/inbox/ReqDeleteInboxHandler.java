package com.game.handler.tcp.inbox;

import com.game.dao.player.PlayerDao;
import com.game.engine.io.handler.TcpHandler;
import com.game.engine.script.IHandlerEntity;
import com.game.engine.utils.MsgUtil;
import com.game.entity.player.Player;
import com.game.enums.ErrorCode;
import com.game.hall.mrg.player.PlayerMrg;
import com.game.manager.EntityDaoMrg;
import com.proto.InboxMessage;
import com.proto.MIDMessage;
import it.unimi.dsi.fastutil.longs.LongLists;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@IHandlerEntity(mid = MIDMessage.MID.ReqDeleteInbox_VALUE, msg = InboxMessage.ReqDeleteInboxMessage.class)
public class ReqDeleteInboxHandler extends TcpHandler {
    private static final Logger LOGGER = LoggerFactory.getLogger(ReqDeleteInboxHandler.class);

    @Override
    public void run() {
        final InboxMessage.ResDeleteInboxMessage.Builder res = InboxMessage.ResDeleteInboxMessage.newBuilder();
        res.setMsgID(MIDMessage.MID.ResDeleteInbox_VALUE);
        try {
            final Player player = PlayerMrg.getInstance().getOnlinePlayer(pid, this.getClass().getName());
            if (player == null) {
                res.setError(ErrorCode.Player_Offline.getCode());
                MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                return;
            }

            final InboxMessage.ReqDeleteInboxMessage req = (InboxMessage.ReqDeleteInboxMessage) getMessage();
            final long inboxId = Long.parseLong(req.getInboxId());

            player.getInboxInfo().getInboxMap().remove(inboxId);
            EntityDaoMrg.getInstance().getDao(PlayerDao.class).inboxDao
                    .updateDeleteInbox(pid, player.getInboxInfo(), LongLists.singleton(inboxId));

            res.setInboxId(req.getInboxId());
            MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
        } catch (Exception e) {
            LOGGER.error("ReqDeleteInboxHandler", e);
            res.setError(ErrorCode.Internal_Server_Error.getCode());
            MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
        }
    }
}
