package com.game.handler.tcp.activity;

import com.game.c_entity.merchant.C_Activity;
import com.game.c_entity.middleplatform.C_BaseMerchant;
import com.game.engine.io.handler.TcpHandler;
import com.game.engine.io.redis.RedisPoolManager;
import com.game.engine.script.IHandlerEntity;
import com.game.engine.utils.MsgUtil;
import com.game.engine.utils.TimeUtil;
import com.game.entity.player.Player;
import com.game.entity.player.activity.ActivityData;
import com.game.entity.player.activity.ActivityInfo;
import com.game.enums.Currency;
import com.game.enums.ErrorCode;
import com.game.enums.redis.RedisRanking;
import com.game.hall.mrg.DataHallMrg;
import com.game.hall.mrg.MerchantData;
import com.game.hall.mrg.player.PlayerMrg;
import com.proto.ActivityMessage;
import com.proto.MIDMessage;
import io.lettuce.core.ScoredValue;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.time.DayOfWeek;
import java.util.List;
import java.util.Set;

@IHandlerEntity(mid = MIDMessage.MID.ReqActivityRankData_VALUE, msg = ActivityMessage.ReqActivityRankDataMessage.class)
public class ReqActivityRankInfoDataHandler extends TcpHandler {
    private static final Logger LOGGER = LoggerFactory.getLogger(ReqActivityRankInfoDataHandler.class);

    @Override
    public void run() {
        final ActivityMessage.ResActivityRankDataMessage.Builder res = ActivityMessage.ResActivityRankDataMessage.newBuilder();
        res.setMsgID(MIDMessage.MID.ResActivityRankData_VALUE);
        try {
            final ActivityMessage.ReqActivityRankDataMessage req = (ActivityMessage.ReqActivityRankDataMessage) getMessage();
            final int cId = req.getCId();
            final String host = req.getHost();
            final C_BaseMerchant c_baseMerchant = DataHallMrg.getInstance().findC_BaseHostMerchant(this.getClass().getSimpleName(), host);
            if (c_baseMerchant == null) {
                res.setError(ErrorCode.Data_Error.getCode());
                MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                return;
            }

            final MerchantData merchantData = DataHallMrg.getInstance().findMerchantData(this.getClass().getSimpleName(), c_baseMerchant.getBusiness_no());
            if (merchantData == null) {
                res.setError(ErrorCode.Data_Error.getCode());
                MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                return;
            }

            final C_Activity c_activity = merchantData.findByIdC_Activity(this.getClass().getSimpleName(), cId);
            if (c_activity == null) {
                res.setError(ErrorCode.Data_Error.getCode());
                MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                return;
            }

            {
                if (pid > 0) {
                    final Player player = PlayerMrg.getInstance().getOnlinePlayer(pid, this.getClass().getSimpleName());
                    if (player == null) {
                        res.setError(ErrorCode.Player_Offline.getCode());
                        MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                        return;
                    }

                    final ActivityInfo activityInfo = player.getActivityInfo();
                    final int activityId = Integer.parseInt(c_activity.getActivityId() + "" + c_activity.getActivitySubType());
                    final ActivityData activityData = activityInfo.getActivityData(activityId);
                    if (activityData != null) {
                        String member = null;
                        if (c_activity.getActivitySubType() == 5) {//邀请
                            member = player.getPlayerId() + "";
                        } else {
                            if (c_activity.isEvUsd()) {
                                member = player.getPlayerId() + "";
                            } else {
                                member = player.getCurrencyId() + "_" + player.getPlayerId();
                            }
                        }

                        String finalMember = member;
                        Long ranking = RedisPoolManager.getInstance().function(jedis ->
                                jedis.sync().zrevrank(RedisRanking.RANKING_ACTIVITY.getKey(player.getBusiness_no(), c_activity.getActivitySubType(), activityData.getRankDate()), finalMember));
                        if (ranking == null) {
                            ranking = 0L;
                        } else {
                            ranking++;
                        }

                        Double score = RedisPoolManager.getInstance().function(jedis ->
                                jedis.sync().zscore(RedisRanking.RANKING_ACTIVITY.getKey(player.getBusiness_no(), c_activity.getActivitySubType(), activityData.getRankDate()), finalMember));
                        if (score == null) {
                            score = 0d;
                        }
                        res.setMyRank(buildRankInfo(player, Math.toIntExact(ranking), score, player.getCurrencyId(), 0));
                    }
                }
            }

            final List<ScoredValue<String>> players = RedisPoolManager.getInstance().function(jedis ->
                    jedis.sync().zrevrangeWithScores(RedisRanking.RANKING_ACTIVITY.getKey(c_baseMerchant.getBusiness_no(), c_activity.getActivitySubType(), rankDate(c_activity)), 0, 9));
            int ranking = 0;
            if (players != null) {
                for (ScoredValue<String> tuple : players) {
                    ++ranking;
                    final String element = tuple.getValue();
                    final double score = tuple.getScore();
                    int currencyId = 0;
                    long playerId = 0;
                    if (c_activity.getActivitySubType() == 5) {
                        playerId = Long.parseLong(element);
                    } else {
                        if (c_activity.isEvUsd()) {
                            currencyId = Currency.USD.getCurrencyId();
                            playerId = Long.parseLong(element);
                        } else {
                            currencyId = Integer.parseInt(element.split("_")[0]);
                            playerId = Long.parseLong(element.split("_")[1]);
                        }
                    }
                    final Player rankPlayer = PlayerMrg.getInstance().findDbPlayer(playerId);
                    if (rankPlayer == null) {
                        continue;
                    }

                    C_Activity.RewardInfo rank = null;
                    if (currencyId > 0) {
                        rank = c_activity.findRankReward(currencyId, ranking);
                    } else {
                        rank = c_activity.findInviteRewardInfo(ranking);
                    }

                    if (rank == null) {
                        continue;
                    }
                    res.addRankList(buildRankInfo(rankPlayer, ranking, score, rank.getCurrencyId(), rank.getReward()));
                }
            }
            MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
        } catch (Exception e) {
            LOGGER.error("ReqActivityRankInfoDataHandler", e);
            res.setError(ErrorCode.Internal_Server_Error.getCode());
            MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
        }
    }

    private ActivityMessage.ActivityRankInfo buildRankInfo(Player player, int ranking, double score, int currencyId, double prize) {
        return ActivityMessage.ActivityRankInfo.newBuilder()
                .setRanking(ranking)
                .setHeadId(player.getHeadId())
                .setName(player.getPlayerName())
                .setWagered(score)
                .setCurrencyId(currencyId)
                .setPrize(prize)
                .build();
    }

    private String rankDate(C_Activity c_activity) {
        String date = "";
        if (c_activity.getSettlementCycle() == 1) {//每日
            date = TimeUtil.getDateTimeFormat(TimeUtil.YYYYMMDD, c_activity.getTimeZone());
        } else if (c_activity.getSettlementCycle() == 2) {//每周
            final long dayOfWeekEndTimestamp = TimeUtil.getDayOfWeekEndTimestamp(TimeUtil.currentTimeMillis(), c_activity.getTimeZone(), DayOfWeek.SUNDAY);
            date = TimeUtil.getDateTimeFormat(dayOfWeekEndTimestamp, TimeUtil.YYYYMMDD, c_activity.getTimeZone());
        } else {
            date = TimeUtil.getDateTimeFormat(c_activity.getEndTime(), TimeUtil.YYYYMMDD, c_activity.getTimeZone());
        }
        return date;
    }
}
