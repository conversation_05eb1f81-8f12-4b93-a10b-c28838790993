package com.game.handler.tcp.affiliate;

import com.game.c_entity.middleplatform.C_BaseExchangeRate;
import com.game.dao.player.PlayerPromoteDao;
import com.game.engine.io.handler.TcpHandler;
import com.game.engine.math.BigDecimalUtils;
import com.game.engine.script.IHandlerEntity;
import com.game.engine.util.misc.Tuple2;
import com.game.engine.utils.MsgUtil;
import com.game.entity.player.Player;
import com.game.entity.player.promote.PlayerPromote;
import com.game.entity.player.promote.ReferralRewards;
import com.game.enums.ErrorCode;
import com.game.hall.mrg.DataHallMrg;
import com.game.hall.mrg.CommonMrg;
import com.game.hall.mrg.MerchantData;
import com.game.hall.mrg.player.PlayerMrg;
import com.game.manager.EntityDaoMrg;
import com.proto.AffiliateMessage;
import com.proto.MIDMessage;
import io.netty.util.internal.StringUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.List;

@IHandlerEntity(mid = MIDMessage.MID.ReqReferralRewardData_VALUE, msg = AffiliateMessage.ReqReferralRewardDataMessage.class)
public class ReqReferralRewardDataHandler extends TcpHandler {
    private static final Logger LOGGER = LoggerFactory.getLogger(ReqReferralRewardDataHandler.class);

    @Override
    public void run() {
        final AffiliateMessage.ResReferralRewardDataMessage.Builder res = AffiliateMessage.ResReferralRewardDataMessage.newBuilder();
        res.setMsgID(MIDMessage.MID.ResReferralRewardData_VALUE);
        try {
            final Player player = PlayerMrg.getInstance().getOnlinePlayer(pid, this.getClass().getSimpleName());
            if (player == null) {
                res.setError(ErrorCode.Player_Offline.getCode());
                MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                return;
            }
            final MerchantData merchantData = DataHallMrg.getInstance().findMerchantData(this.getClass().getSimpleName(), player.getBusiness_no());
            if (merchantData == null) {
                res.setError(ErrorCode.Data_Error.getCode());
                MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                return;
            }

            final boolean isManyCurrency = merchantData.getC_currencyMap().size() > 1;

            final AffiliateMessage.ReqReferralRewardDataMessage req = (AffiliateMessage.ReqReferralRewardDataMessage) getMessage();
            final long registerStart = req.getRegisterStart();
            final long reqRegisterEnd = req.getRegisterEnd();
            final int page = req.getPage();
            final int pageSize = req.getPageSize();
            final String userName = req.getUsername();

            final List<PlayerPromote> playerPromoteList = new ArrayList<>();
            if (!StringUtil.isNullOrEmpty(userName)) {
                final PlayerPromote playerPromote = EntityDaoMrg.getInstance().getDao(PlayerPromoteDao.class).findByUserName(userName);
                if (playerPromote != null) {
                    playerPromoteList.add(playerPromote);
                }
            } else {
                final PlayerPromote playerPromote = player.getPlayerPromote();
                final int skip = (page - 1) * pageSize;
                final Tuple2<Integer, List<PlayerPromote>> tuple2 = EntityDaoMrg.getInstance().getDao(PlayerPromoteDao.class)
                        .findByCodeFriends(new ArrayList<>(playerPromote.getReferralCodeMap().keySet()), registerStart, reqRegisterEnd, skip, pageSize);
                final int count = tuple2.getFirst();
                playerPromoteList.addAll(tuple2.getSecond());
                res.setPage(page)
                        .setPageSize(pageSize)
                        .setTotal(count)
                        .setTotalPage(CommonMrg.totalPage(count, pageSize));
            }

            for (PlayerPromote playerPromote : playerPromoteList) {
                final ReferralRewards referralRewards = playerPromote.getReferralRewards();
                if (referralRewards.getCurrencyId() == 0) {
                    continue;
                }
                double earned = 0;
                if (isManyCurrency) {
                    final C_BaseExchangeRate c_baseExchangeRate = DataHallMrg.getInstance().findC_BaseExchangeRate(this.getClass().getSimpleName(), referralRewards.getCurrencyId());
                    if (c_baseExchangeRate == null) {
                        continue;
                    }
                    earned = BigDecimalUtils.mul(referralRewards.getTotalReceived(), c_baseExchangeRate.getExchangeRate(), 9);
                } else {
                    earned = referralRewards.getTotalReceived();
                }
                res.addReferralReward(buildReferralReward(playerPromote, earned));
            }

            MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
        } catch (Exception e) {
            LOGGER.error("ReqReferralRewardDataHandler", e);
            res.setError(ErrorCode.Internal_Server_Error.getCode());
            MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
        }
    }

    private AffiliateMessage.ReferralReward buildReferralReward(PlayerPromote playerPromote, double earned) {
        return AffiliateMessage.ReferralReward.newBuilder()
                .setUsername(playerPromote.getPlayerName())
                .setRegistrationDate(playerPromote.getCreateTime())
                .setVipLevel(playerPromote.getVipLevel())
                .setCode(playerPromote.getSuperiorCode())
                .setEarned(earned)
                .build();
    }
}
