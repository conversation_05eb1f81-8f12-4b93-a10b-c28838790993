package com.game.handler.tcp.agentGame;

import com.game.c_entity.merchant.C_Currency;
import com.game.c_entity.merchant.C_GameApi;
import com.game.c_entity.merchant.C_GameKillRate;
import com.game.dao.player.PlayerDao;
import com.game.engine.enums.ServerType;
import com.game.engine.io.handler.TcpHandler;
import com.game.engine.math.BigDecimalUtils;
import com.game.engine.script.IHandlerEntity;
import com.game.engine.struct.ServerInfo;
import com.game.engine.utils.MsgUtil;
import com.game.entity.player.Player;
import com.game.entity.player.PlayerFields;
import com.game.entity.player.stats.Stats;
import com.game.enums.Currency;
import com.game.enums.ErrorCode;
import com.game.enums.GameType;
import com.game.hall.mrg.DataHallMrg;
import com.game.hall.mrg.MerchantData;
import com.game.hall.mrg.ServerMrg;
import com.game.hall.mrg.currency.CurrencyMrg;
import com.game.hall.mrg.player.PlayerMrg;
import com.game.manager.EntityDaoMrg;
import com.proto.HallMessage;
import com.proto.MIDMessage;
import io.netty.util.internal.StringUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;
import java.util.Map;
import java.util.Optional;

@IHandlerEntity(mid = MIDMessage.MID.ReqEntryAgentGame_VALUE, msg = HallMessage.ReqEntryAgentGameMessage.class)
public class ReqEntryAgentGameHandler extends TcpHandler {
    private static final Logger LOGGER = LoggerFactory.getLogger(ReqEntryAgentGameHandler.class);

    @Override
    public void run() {
        final HallMessage.ResEntryAgentGameMessage.Builder res = HallMessage.ResEntryAgentGameMessage.newBuilder();
        res.setMsgID(MIDMessage.MID.ResEntryAgentGame_VALUE);
        try {
            final Player player = PlayerMrg.getInstance().getOnlinePlayer(pid, this.getClass().getSimpleName());
            if (player == null) {
                res.setError(ErrorCode.Player_Offline.getCode());
                MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                return;
            }

            if (player.isFreeze()) {
                res.setError(ErrorCode.Player_Freeze.getCode());
                MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                return;
            }

            final MerchantData merchantData = DataHallMrg.getInstance().findMerchantData(this.getClass().getSimpleName(), player.getBusiness_no());
            if (merchantData == null) {
                res.setError(ErrorCode.Data_Error.getCode());
                MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                return;
            }

            final HallMessage.ReqEntryAgentGameMessage req = (HallMessage.ReqEntryAgentGameMessage) getMessage();
            final int gameId = req.getGameId();
            final int currencyId = req.getCurrencyId();
            final boolean freePlay = req.getFreePlay();

            if (player.getGameId() > 0) {
                res.setError(ErrorCode.AgentGame_InGame.getCode());
                MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                return;
            }

            final C_GameApi c_gameApi = merchantData.findC_GameApi(this.getClass().getSimpleName(), gameId);
            if (c_gameApi == null) {
                res.setError(ErrorCode.Data_Error.getCode());
                MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                return;
            }

            if (freePlay && c_gameApi.getType() != GameType.Casino_Slots.getType()) {
                LOGGER.warn("business_no：{}，gameId：{}，is not freePlay", player.getBusiness_no(), gameId);
                res.setError(ErrorCode.Data_Error.getCode());
                MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                return;
            }

            if (freePlay && !c_gameApi.isFreePlay()) {
                LOGGER.warn("business_no：{}，gameId：{}，config is not freePlay", player.getBusiness_no(), gameId);
                res.setError(ErrorCode.Data_Error.getCode());
                MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                return;
            }

            int gameCurrencyId = currencyId;
            if (c_gameApi.getSupportCurrency().contains(Currency.USD.getCurrencyId())) {
                gameCurrencyId = Currency.USD.getCurrencyId();
            }

            final String entryGameLimit = merchantData.findC_GlobalValue(this.getClass().getSimpleName(), "entryGameLimit");
            //false 限制
            if (StringUtil.isNullOrEmpty(entryGameLimit) || !Boolean.parseBoolean(entryGameLimit)) {
                final Stats statsInfo = player.getStats(currencyId);
                final double balance = CurrencyMrg.getInstance().getCurrencyValue(player, currencyId);
                final double bonusBalance = CurrencyMrg.getInstance().getCurrencyValue(player, currencyId * 10);
                if (balance == 0 && statsInfo.getTotalRechargeTimes() == 0) {
                    res.setError(ErrorCode.Currency_Not_Enough_Error.getCode());
                    MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                    return;
                }

                if (!freePlay && BigDecimalUtils.add(balance, bonusBalance, 2) <= 0) {
                    res.setError(ErrorCode.Currency_Not_Enough_Error.getCode());
                    MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                    return;
                }
            }

            final C_Currency c_currency = merchantData.findC_Currency(this.getClass().getSimpleName(), gameCurrencyId);
            if (c_currency == null) {
                res.setError(ErrorCode.Data_Error.getCode());
                MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                return;
            }

            final HallMessage.ReqEntryAgentGameMessage.Builder reqEntryAgentGame = req.toBuilder()
                    .setCurrencyId(currencyId)//游戏货币
                    .setGameCurrencyId(gameCurrencyId)
                    .setCurrencyName(c_currency.getCurrencyName())
                    .setGameId(c_gameApi.getGameId())
                    .setPlatformId(c_gameApi.getPlatformId())
                    .setGameType(c_gameApi.getType())
                    .setPlatformGameId(c_gameApi.getPlatformGameId())
                    .setBonus(c_gameApi.isBonus())
                    .setRtpPool(c_gameApi.getRtpPool());

            //指定杀率
            double rtpPool = 0;
            final List<C_GameKillRate> c_playerGameKillRates = merchantData.getC_gameKillRateMap().get(1);
            if (c_playerGameKillRates != null) {
                for (final C_GameKillRate c_gameKillRate : c_playerGameKillRates) {
                    if (c_gameKillRate.getKillId().contains(player.getPlayerId())) {
                        rtpPool = c_gameKillRate.getRtp();
//                        LOGGER.warn("killRate：{}，playerId：{}，rtpPool：{}", 1, player.getPlayerId(), rtpPool);
                    }
                }
            }

            if (player.getAgentId() > 0) {
                final List<C_GameKillRate> c_proxyGameKillRates = merchantData.getC_gameKillRateMap().get(2);
                if (c_proxyGameKillRates != null) {
                    for (final C_GameKillRate c_gameKillRate : c_proxyGameKillRates) {
                        if (c_gameKillRate.getKillId().contains(Long.parseLong(player.getAgentId() + ""))) {
                            rtpPool = c_gameKillRate.getRtp();
//                            LOGGER.warn("killRate：{}，playerId：{}，rtpPool：{}", 2, player.getPlayerId(), rtpPool);
                        }
                    }
                }
            }

            if (rtpPool > 0) {
                reqEntryAgentGame.setRtpPool(rtpPool);
            }

            final ServerInfo serverInfo = ServerMrg.getInstance()
                    .getServerType(ServerType.AGENT_GAME, pid, 0);

            MsgUtil.sendInnerMsg(serverInfo.getActiveSession(), reqEntryAgentGame.build(), pid, udpSessionId);

            player.setAgentGameServerId(serverInfo.getId());
            EntityDaoMrg.getInstance().getDao(PlayerDao.class)
                    .updatePlayerField(player.getPlayerId(), PlayerFields.agentGameServerId, player.getAgentGameServerId());
        } catch (Exception e) {
            LOGGER.error("ReqEntryAgentGameHandler", e);
            res.setError(ErrorCode.Internal_Server_Error.getCode());
            MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
        }
    }

}
