package com.game.handler.tcp.activity.rewardBox;

import com.game.c_entity.merchant.C_RewardBox;
import com.game.dao.player.PlayerDao;
import com.game.engine.io.handler.TcpHandler;
import com.game.engine.io.redis.RedisPoolManager;
import com.game.engine.log.GameLog;
import com.game.engine.math.BigDecimalUtils;
import com.game.engine.script.IHandlerEntity;
import com.game.engine.script.ScriptLoader;
import com.game.engine.util.misc.Tuple2;
import com.game.engine.utils.GuidGeneratorUtils;
import com.game.engine.utils.MsgUtil;
import com.game.engine.utils.TimeUtil;
import com.game.entity.player.Player;
import com.game.entity.player.activity.rewardBox.RewardBoxInfo;
import com.game.enums.ErrorCode;
import com.game.enums.RewardReason;
import com.game.enums.TransactionFrom;
import com.game.enums.TurnoverReason;
import com.game.enums.redis.RedisHall;
import com.game.hall.main.HallServer;
import com.game.hall.mrg.ActivityMrg;
import com.game.hall.mrg.CommonMrg;
import com.game.hall.mrg.DataHallMrg;
import com.game.hall.mrg.MerchantData;
import com.game.hall.mrg.currency.CurrencyMrg;
import com.game.hall.mrg.currency.RewardRequest;
import com.game.hall.mrg.player.PlayerMrg;
import com.game.hall.script.IBonusScript;
import com.game.hall.script.IPlayerScript;
import com.game.manager.EntityDaoMrg;
import com.proto.ActivityMessage;
import com.proto.MIDMessage;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.HashSet;
import java.util.Set;

@IHandlerEntity(mid = MIDMessage.MID.ReqReceiveRewardBox_VALUE, msg = ActivityMessage.ReqReceiveRewardBoxMessage.class)
public class ReqReceiveRewardBoxHandler extends TcpHandler {
    private static final Logger LOGGER = LoggerFactory.getLogger(ReqReceiveRewardBoxHandler.class);

    @Override
    public void run() {
        final ActivityMessage.ResReceiveRewardBoxMessage.Builder res = ActivityMessage.ResReceiveRewardBoxMessage.newBuilder();
        res.setMsgID(MIDMessage.MID.ResReceiveRewardBox_VALUE);
        try {
            final ActivityMessage.ReqReceiveRewardBoxMessage req = (ActivityMessage.ReqReceiveRewardBoxMessage) getMessage();
            final int inviteNum = req.getInviteNum();

            final Player player = PlayerMrg.getInstance().getOnlinePlayer(pid, this.getClass().getSimpleName());
            if (player == null) {
                res.setError(ErrorCode.Player_Offline.getCode());
                MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                return;
            }

            final MerchantData merchantData = DataHallMrg.getInstance().findMerchantData(this.getClass().getSimpleName(), player.getBusiness_no());
            if (merchantData == null) {
                res.setError(ErrorCode.Data_Error.getCode());
                MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                return;
            }

            final C_RewardBox c_rewardBox = merchantData.findC_RewardBox(this.getClass().getSimpleName(), ActivityMrg.REWARD_BOX);
            if (c_rewardBox == null) {
                res.setError(ErrorCode.Data_Error.getCode());
                MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                return;
            }

            final RewardBoxInfo rewardBoxInfo = player.getRewardBoxInfo();

            Set<String> invitePlayerIds = RedisPoolManager.getInstance().function(jedis ->
                    jedis.sync().smembers(RedisHall.Platform_Role_Map_RewardBoxInvite.getKey(pid)));
            if (invitePlayerIds == null) {
                invitePlayerIds = new HashSet<>();
            }

            final int num = invitePlayerIds.size();
            if (inviteNum > num) {
                res.setError(ErrorCode.Conditions_Not_Met.getCode());
                MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                return;
            }

            final C_RewardBox.Box box = c_rewardBox.getBoxMap().get(inviteNum);
            if (box == null) {
                res.setError(ErrorCode.Data_Error.getCode());
                MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                return;
            }

            if (rewardBoxInfo.getReceive().contains(inviteNum)) {
                res.setError(ErrorCode.Reward_Received.getCode());
                MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                return;
            }

            rewardBoxInfo.getReceive().add(inviteNum);
            EntityDaoMrg.getInstance().getDao(PlayerDao.class)
                    .rewardBoxDao.updateReceive(player.getPlayerId(), rewardBoxInfo);

            final RewardRequest rewardRequest = new RewardRequest();
            rewardRequest.addCurrency(box.currencyId, box.amount);
            final RewardReason rewardReason = RewardReason.RewardBox;
            rewardReason.setSource(box.id + "");
            CurrencyMrg.getInstance().rewardNoTest(player, rewardRequest.getBonusCurrencyMap(), rewardReason);

            //TODO 打码
//            final WithdrawStandard withdrawStandard = player.getWithdrawStandard(box.currencyId);
//            withdrawStandard.incDrawStandard(BigDecimalUtils.mul(box.amount, c_rewardBox.getTurnoverMul(), 4));
            final TurnoverReason turnoverReason = TurnoverReason.RewardBox;
            turnoverReason.setSource(box.id + "");
            ScriptLoader.getInstance().consumerScript("PlayerWithdrawStandardScript",
                    (IPlayerScript script) -> script.drawStandard(player, turnoverReason, box.currencyId, box.amount,
                            BigDecimalUtils.mul(box.amount, c_rewardBox.getTurnoverMul(), 4)));

            res.setRewardShow(CommonMrg.buildDItemShow(box.currencyId, box.amount));
            MsgUtil.responseHttp(res.build(), res.getMsgID(), session);

            if (!rewardRequest.getCurrencyMap().isEmpty()) {
                final Tuple2<Integer, Double> tuple2 = rewardRequest.getReward();
                ScriptLoader.getInstance().consumerScript("BonusScript",
                        (IBonusScript script) -> script.addBonusNote(TransactionFrom.RewardBox, player, tuple2.getFirst(), tuple2.getSecond()));
            }

            //日志
            final GameLog playerActivityLog = new GameLog("platform_playerRewardBoxLog");
            playerActivityLog.append("number", GuidGeneratorUtils.generateOrderId())
                    .append("business_no", player.getBusiness_no())
                    .append("site", player.getWebSite())
                    .append("channel", player.getChannel())
                    .append("playerId", player.getPlayerId())
                    .append("playerName", player.getPlayerName())
                    .append("language", player.getLanguage())
                    .append("agentId", player.getAgentId())
                    .append("channelId", player.getChannelId())
                    .append("region", player.getRegisterRegion())
                    .append("activityId", ActivityMrg.REWARD_BOX)
                    .append("activityUniqueId", c_rewardBox.getC_id())
                    .append("currencyId", box.currencyId)
                    .append("reward", box.amount)
                    .append("logTime", TimeUtil.currentTimeMillis());
            HallServer.getInstance().getLogProducerMrg().send(playerActivityLog);
        } catch (Exception e) {
            LOGGER.error("ReqReceiveRewardBoxHandler", e);
            res.setError(ErrorCode.Internal_Server_Error.getCode());
            MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
        }
    }
}
