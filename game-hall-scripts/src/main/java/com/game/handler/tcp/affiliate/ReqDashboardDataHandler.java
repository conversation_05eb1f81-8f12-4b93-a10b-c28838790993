package com.game.handler.tcp.affiliate;

import com.alibaba.fastjson.JSONObject;
import com.game.c_entity.middleplatform.C_BaseGameType;
import com.game.c_entity.merchant.C_CashBack;
import com.game.c_entity.middleplatform.C_BaseExchangeRate;
import com.game.dao.player.PlayerPromoteDao;
import com.game.engine.io.handler.TcpHandler;
import com.game.engine.math.BigDecimalUtils;
import com.game.engine.script.IHandlerEntity;
import com.game.engine.util.misc.Tuple2;
import com.game.engine.utils.MsgUtil;
import com.game.engine.utils.Symbol;
import com.game.entity.player.Player;
import com.game.entity.player.promote.*;
import com.game.enums.ErrorCode;
import com.game.hall.mrg.DataHallMrg;
import com.game.hall.mrg.MerchantData;
import com.game.hall.mrg.currency.CurrencyMrg;
import com.game.hall.mrg.player.PlayerMrg;
import com.game.manager.EntityDaoMrg;
import com.proto.AffiliateMessage;
import com.proto.MIDMessage;
import io.netty.util.internal.StringUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.*;
import java.util.stream.Collectors;

@IHandlerEntity(mid = MIDMessage.MID.ReqDashboardData_VALUE, msg = AffiliateMessage.ReqDashboardDataMessage.class)
public class ReqDashboardDataHandler extends TcpHandler {
    private static final Logger LOGGER = LoggerFactory.getLogger(ReqDashboardDataHandler.class);

    @Override
    public void run() {
        final AffiliateMessage.ResDashboardDataMessage.Builder res = AffiliateMessage.ResDashboardDataMessage.newBuilder();
        res.setMsgID(MIDMessage.MID.ResDashboardData_VALUE);
        try {
            final Player player = PlayerMrg.getInstance().getOnlinePlayer(pid, this.getClass().getSimpleName());
            if (player == null) {
                res.setError(ErrorCode.Player_Offline.getCode());
                MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                return;
            }
            final MerchantData merchantData = DataHallMrg.getInstance().findMerchantData(this.getClass().getSimpleName(), player.getBusiness_no());
            if (merchantData == null) {
                res.setError(ErrorCode.Data_Error.getCode());
                MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                return;
            }

            final boolean isManyCurrency = merchantData.getC_currencyMap().size() > 1;

            final PlayerPromote playerPromote = player.getPlayerPromote();
            double totalCommissionRewards = 0;
            final Map<Integer, CommissionRewards> commissionRewardsMap = playerPromote.getCommissionRewardsMap();
            for (Map.Entry<Integer, CommissionRewards> entry : commissionRewardsMap.entrySet()) {
                if (isManyCurrency) {
                    final C_BaseExchangeRate c_baseExchangeRate = DataHallMrg.getInstance().findC_BaseExchangeRate(this.getClass().getSimpleName(), entry.getKey());
                    if (c_baseExchangeRate == null) {
                        continue;
                    }
                    totalCommissionRewards = BigDecimalUtils.add(totalCommissionRewards, BigDecimalUtils.mul(entry.getValue().getTotalReceived(), c_baseExchangeRate.getExchangeRate(), 9), 9);
                } else {
                    totalCommissionRewards = BigDecimalUtils.add(totalCommissionRewards, entry.getValue().getTotalReceived(), 9);
                }
            }

            double totalTeamRewards = 0;
            final Map<Integer, TeamRewards> teamRewardsMap = playerPromote.getTeamRewardsMap();
            for (Map.Entry<Integer, TeamRewards> entry : teamRewardsMap.entrySet()) {
                if (isManyCurrency) {
                    final C_BaseExchangeRate c_baseExchangeRate = DataHallMrg.getInstance().findC_BaseExchangeRate(this.getClass().getSimpleName(), entry.getKey());
                    if (c_baseExchangeRate == null) {
                        continue;
                    }
                    totalTeamRewards = BigDecimalUtils.add(totalTeamRewards, BigDecimalUtils.mul(entry.getValue().getTotalReceived(), c_baseExchangeRate.getExchangeRate(), 9), 9);
                } else {
                    totalTeamRewards = BigDecimalUtils.add(totalTeamRewards, entry.getValue().getTotalReceived(), 9);
                }
            }

            double totalThreeLevelRewards = 0;
            final Map<Integer, ThreeLevelRewards> threeLevelRewardsMap = playerPromote.getThreeLevelRewardsMap();
            for (Map.Entry<Integer, ThreeLevelRewards> entry : threeLevelRewardsMap.entrySet()) {
                if (isManyCurrency) {
                    final C_BaseExchangeRate c_baseExchangeRate = DataHallMrg.getInstance().findC_BaseExchangeRate(this.getClass().getSimpleName(), entry.getKey());
                    if (c_baseExchangeRate == null) {
                        continue;
                    }
                    totalThreeLevelRewards = BigDecimalUtils.add(totalThreeLevelRewards, BigDecimalUtils.mul(entry.getValue().getTotalReceived(), c_baseExchangeRate.getExchangeRate(), 9), 9);
                } else {
                    totalThreeLevelRewards = BigDecimalUtils.add(totalThreeLevelRewards, entry.getValue().getTotalReceived(), 9);
                }
            }

            double totalReferralRewards = 0;
            final Map<String, ReferralCode> referralCodeMap = playerPromote.getReferralCodeMap();
            final Tuple2<Integer, List<PlayerPromote>> tuple2 = EntityDaoMrg.getInstance().getDao(PlayerPromoteDao.class).findByCodeFriends(new ArrayList<>(referralCodeMap.keySet()));
            final List<PlayerPromote> playerPromotes = tuple2.getSecond();
            for (PlayerPromote promote : playerPromotes) {
                final ReferralRewards referralRewards = promote.getReferralRewards();
                if (referralRewards.getCurrencyId() == 0) {
                    continue;
                }
                if (isManyCurrency) {
                    final C_BaseExchangeRate c_baseExchangeRate = DataHallMrg.getInstance().findC_BaseExchangeRate(this.getClass().getSimpleName(), referralRewards.getCurrencyId());
                    if (c_baseExchangeRate == null) {
                        continue;
                    }
                    totalReferralRewards = BigDecimalUtils.add(totalReferralRewards, BigDecimalUtils.mul(referralRewards.getTotalReceived(), c_baseExchangeRate.getExchangeRate(), 9), 9);
                } else {
                    totalReferralRewards = BigDecimalUtils.add(totalReferralRewards, referralRewards.getTotalReceived(), 9);
                }
            }

            final double totalRewards = BigDecimalUtils.add(totalCommissionRewards + totalReferralRewards, totalTeamRewards, 9);

            final int totalFriends = tuple2.getFirst();
            final int totalTeams = EntityDaoMrg.getInstance().getDao(PlayerPromoteDao.class).findByCodeAllTeamFriends(new ArrayList<>(referralCodeMap.keySet()));
            final int totalThreeLevels = EntityDaoMrg.getInstance().getDao(PlayerPromoteDao.class).findByCodeAllThreeLevelFriends(new ArrayList<>(referralCodeMap.keySet()));

            final String referralLink = merchantData.findC_GlobalValue(this.getClass().getSimpleName(), "referralLink");
            if (!StringUtil.isNullOrEmpty(referralLink)) {
                final String url = referralLink + "?" + "referralCode=" + player.getInvitationCode();
                res.setReferralLink(url);
            }
            final String lockedRewards = merchantData.findC_GlobalValue(this.getClass().getSimpleName(), "lockedRewards");
            if (!StringUtil.isNullOrEmpty(lockedRewards)) {
                final String[] mh = lockedRewards.split(Symbol.MAOHAO_REG);
                if (mh.length > 0) {
                    res.setReferralBonus(CurrencyMrg.getInstance().buildCurrency(Integer.parseInt(mh[0]), Integer.parseInt(mh[1])));
                }
            }

            final String referralRewardOpen = merchantData.findC_GlobalValue(this.getClass().getSimpleName(), "referralRewardOpen");

            res.setReferralCode(player.getInvitationCode())
                    .setTotalRewards(totalRewards)
                    .setTotalFriends(totalFriends)
                    .setTotalTeammates(totalTeams)
                    .setTotalThreeLevels(totalThreeLevels)

                    .setTeamRewards(totalTeamRewards)
                    .setReferralRewards(totalReferralRewards)
                    .setCommissionRewards(totalCommissionRewards)
                    .setThreeLevelRewards(totalThreeLevelRewards)

                    .setReferralRewardOpen(!StringUtil.isNullOrEmpty(referralRewardOpen) && Boolean.parseBoolean(referralRewardOpen))
                    .setRewardsActivities(buildRewardsActivities(playerPromote, totalReferralRewards));

            final OptionalDouble commissionRate = merchantData.getC_cashBackMap().values().stream()
                    .mapToDouble(C_CashBack::getSuperiorCashBackRate).max();

            final OptionalDouble teamRate = merchantData.getC_cashBackMap().values().stream()
                    .mapToDouble(C_CashBack::getTeamCashBackRate).max();

            final OptionalDouble threeLevelRate = merchantData.getC_cashBackMap().values().stream()
                    .mapToDouble(C_CashBack::getThreeLevelCashBackRate).max();

            final String rebateMethod = merchantData.findC_GlobalValue(this.getClass().getSimpleName(), "rebateMethod");
            res.setCommissionRate(commissionRate.isPresent() ? commissionRate.getAsDouble() : 0)
                    .setTeamRate(teamRate.isPresent() ? teamRate.getAsDouble() : 0)
                    .setThreeLevelRate(threeLevelRate.isPresent() ? threeLevelRate.getAsDouble() : 0)
                    .setRebateMethod(Integer.parseInt(StringUtil.isNullOrEmpty(rebateMethod) ? "0" : rebateMethod));

            if (res.getRebateMethod() == 4) {//佣金
                for (C_CashBack c_cashBack : merchantData.getC_cashBackMap().values()) {
                    final C_BaseGameType c_baseGameType = DataHallMrg.getInstance().findC_GameType(this.getClass().getSimpleName(), c_cashBack.getGameType());
                    if (c_baseGameType == null) {
                        continue;
                    }
                    res.addRebateRateList(buildRebateRate(player.getLanguage(), c_cashBack, c_baseGameType));
                }
            } else {
                final List<C_CashBack> c_cashBackList = merchantData.getC_cashBackMap().values().stream()
                        .collect(Collectors.groupingBy(c_invitationCashBack -> c_invitationCashBack.getGameType() / 100))
                        .values()
                        .stream()
                        .flatMap(Collection::stream)
                        .toList();
                for (C_CashBack c_cashBack : c_cashBackList) {
                    final C_BaseGameType c_baseGameType = DataHallMrg.getInstance().findC_GameType(this.getClass().getSimpleName(), c_cashBack.getGameType());
                    if (c_baseGameType == null) {
                        continue;
                    }
                    res.addRebateRateList(buildRebateRate(player.getLanguage(), c_cashBack, c_baseGameType));
                }
            }

            final String affilateText = merchantData.findC_GlobalValue(this.getClass().getSimpleName(), "affilateText");
            if (!StringUtil.isNullOrEmpty(affilateText)) {
                final List<Rule> rules = JSONObject.parseArray(affilateText, Rule.class);
                for (Rule rule : rules) {
                    if (rule.language == player.getLanguage()) {
                        res.setText(rule.getRule());
                    }
                }
            }

            MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
        } catch (Exception e) {
            LOGGER.error("ReqDashboardDataHandler", e);
            res.setError(ErrorCode.Internal_Server_Error.getCode());
            MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
        }
    }

    public static class Rule {
        public int language;
        public String rule;

        public int getLanguage() {
            return language;
        }

        public void setLanguage(int language) {
            this.language = language;
        }

        public String getRule() {
            return StringUtil.isNullOrEmpty(rule) ? "" : rule;
        }

        public void setRule(String rule) {
            this.rule = rule;
        }
    }

    private AffiliateMessage.RewardsActivities buildRewardsActivities(PlayerPromote playerPromote, double referralRewards) {
        final AffiliateMessage.RewardsActivities.Builder rewardsActivities = AffiliateMessage.RewardsActivities.newBuilder();
        for (Map.Entry<Integer, CommissionRewards> entry : playerPromote.getCommissionRewardsMap().entrySet()) {
            rewardsActivities.addCommissionReward(buildCommissionReward(entry.getKey(), entry.getValue()));
        }

        for (Map.Entry<Integer, TeamRewards> entry : playerPromote.getTeamRewardsMap().entrySet()) {
            rewardsActivities.addTeamReward(buildTeamReward(entry.getKey(), entry.getValue()));
        }

        for (Map.Entry<Integer, ThreeLevelRewards> entry : playerPromote.getThreeLevelRewardsMap().entrySet()) {
            rewardsActivities.addThreeLevelReward(buildThreeLevelReward(entry.getKey(), entry.getValue()));
        }
        rewardsActivities.setReferralReward(referralRewards);
        return rewardsActivities.build();
    }

    private AffiliateMessage.CommissionReward buildCommissionReward(int currencyId, CommissionRewards commissionRewards) {
        return AffiliateMessage.CommissionReward.newBuilder()
                .setCurrencyId(currencyId)
                .setAvailable(commissionRewards.getAvailable())
                .setTotalReceived(commissionRewards.getTotalReceived())
                .build();
    }

    private AffiliateMessage.TeamReward buildTeamReward(int currencyId, TeamRewards teamRewards) {
        return AffiliateMessage.TeamReward.newBuilder()
                .setCurrencyId(currencyId)
                .setAvailable(teamRewards.getAvailable())
                .setTotalReceived(teamRewards.getTotalReceived())
                .build();
    }

    private AffiliateMessage.ThreeLevelReward buildThreeLevelReward(int currencyId, ThreeLevelRewards threeLevelRewards) {
        return AffiliateMessage.ThreeLevelReward.newBuilder()
                .setCurrencyId(currencyId)
                .setAvailable(threeLevelRewards.getAvailable())
                .setTotalReceived(threeLevelRewards.getTotalReceived())
                .build();
    }

    private AffiliateMessage.RebateRate buildRebateRate(int language, C_CashBack c_cashBack, C_BaseGameType c_Base_gameType) {
        final AffiliateMessage.RebateRate.Builder rebateRate = AffiliateMessage.RebateRate.newBuilder()
                .setGameType(c_cashBack.getGameType())
                .setRate(c_cashBack.getGameCashBackRate())
                .setEffectiveRate(c_cashBack.getEffectiveRate());
        final C_BaseGameType.GameTypeInfo gameTypeInfo = c_Base_gameType.getGameTypeInfoMap().get(language);
        if (gameTypeInfo != null) {
            rebateRate.setGameCategory(gameTypeInfo.getGameCategoryName())
                    .setTypeName(gameTypeInfo.getGameTypeName());
        }
        return rebateRate.build();
    }
}
