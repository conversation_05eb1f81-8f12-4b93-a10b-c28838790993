package com.game.handler.tcp.agentGame;

import com.game.dao.player.PlayerDao;
import com.game.engine.io.handler.TcpHandler;
import com.game.engine.script.IHandlerEntity;
import com.game.engine.utils.MsgUtil;
import com.game.entity.player.FreeGameInfo;
import com.game.entity.player.GameInfo;
import com.game.entity.player.Player;
import com.game.entity.player.PlayerFields;
import com.game.enums.ErrorCode;
import com.game.hall.mrg.UserUdpMrg;
import com.game.hall.mrg.player.PlayerMrg;
import com.game.manager.EntityDaoMrg;
import com.proto.HallMessage;
import com.proto.MIDMessage;
import io.netty.util.internal.StringUtil;
import it.unimi.dsi.fastutil.ints.IntLists;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.mongodb.core.query.Update;

@IHandlerEntity(mid = MIDMessage.MID.ResEntryAgentGame_VALUE, msg = HallMessage.ResEntryAgentGameMessage.class)
public class ResEntryAgentGameHandler extends TcpHandler {
    private static final Logger LOGGER = LoggerFactory.getLogger(ResEntryAgentGameHandler.class);

    @Override
    public void run() {
        final HallMessage.ResEntryAgentGameMessage req = (HallMessage.ResEntryAgentGameMessage) getMessage();

        final HallMessage.ResEntryAgentGameMessage.Builder res = req.toBuilder();
        try {
            final Player player = PlayerMrg.getInstance().getOnlinePlayer(pid, this.getClass().getName());
            if (player == null) {
                LOGGER.warn("playerId：{}，not exist，forward gate msgId：{} ，fail", pid, req.getMsgID());
                res.setError(ErrorCode.Player_Offline.getCode());
                MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                return;
            }

            if (res.getError() == ErrorCode.Success.getCode()) {
                player.setGameId(req.getGameId());
                player.setBonus(req.getBonus());
                player.setPlatformId(req.getPlatformId());
                player.setGameType(req.getGameType());
                player.setGameCurrencyId(req.getGameCurrencyId());
                player.setPlayerCurrencyId(req.getCurrencyId());

                final Update update = new Update();
                update.set(PlayerFields.gameId, player.getGameId())
                        .set(PlayerFields.bonus, player.isBonus())
                        .set(PlayerFields.platformId, player.getPlatformId())
                        .set(PlayerFields.gameType, player.getGameType())
                        .set(PlayerFields.gameCurrencyId, player.getGameCurrencyId())
                        .set(PlayerFields.playerCurrencyId, player.getPlayerCurrencyId());

                if (!StringUtil.isNullOrEmpty(res.getReferenceId())) {
                    final FreeGameInfo freeGameInfo = player.getFreeGameInfo(req.getCurrencyId());
                    final GameInfo gameInfo = freeGameInfo.getFreeGame(req.getGameId());
                    gameInfo.setReferenceId(res.getReferenceId());
                    EntityDaoMrg.getInstance().getDao(PlayerDao.class)
                            .updateFreeGameInfo(player, IntLists.singleton(req.getCurrencyId()));
                }
                if (req.getGameId() != 0 && (player.getRecentGame().isEmpty() || player.getRecentGame().getLast() != req.getGameId())) {
                    player.getRecentGame().removeIf((gameId -> req.getGameId() == gameId));
                    player.getRecentGame().add(req.getGameId());
                    if (player.getRecentGame().size() > 50) {
                        player.getRecentGame().removeFirst();
                    }
                    update.set(PlayerFields.recentGame, player.getRecentGame());
                }
                EntityDaoMrg.getInstance().getDao(PlayerDao.class)
                        .updatePlayer(player.getPlayerId(), update);
            }
            MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
        } catch (Exception e) {
            LOGGER.error("ResEntryAgentGameHandler", e);
            res.setError(ErrorCode.Internal_Server_Error.getCode());
            MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
        }
    }

}
