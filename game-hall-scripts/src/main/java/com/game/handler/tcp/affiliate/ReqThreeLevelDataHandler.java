package com.game.handler.tcp.affiliate;

import com.game.c_entity.merchant.C_CashBack;
import com.game.dao.game.GameNoteDao;
import com.game.dao.player.PlayerPromoteDao;
import com.game.engine.io.handler.TcpHandler;
import com.game.engine.script.IHandlerEntity;
import com.game.engine.script.ScriptLoader;
import com.game.engine.util.misc.Tuple2;
import com.game.engine.utils.MsgUtil;
import com.game.entity.game.GameCommissionStat;
import com.game.entity.game.GameNoteFields;
import com.game.entity.game.GameWagerStat;
import com.game.entity.player.Player;
import com.game.entity.player.promote.PlayerPromote;
import com.game.entity.player.promote.ReferralCode;
import com.game.enums.ErrorCode;
import com.game.hall.mrg.DataHallMrg;
import com.game.hall.mrg.MerchantData;
import com.game.hall.mrg.player.PlayerMrg;
import com.game.hall.script.IAffiliateScript;
import com.game.manager.EntityDaoMrg;
import com.proto.AffiliateMessage;
import com.proto.MIDMessage;
import io.netty.util.internal.StringUtil;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.OptionalDouble;

@IHandlerEntity(mid = MIDMessage.MID.ReqThreeLevelData_VALUE, msg = AffiliateMessage.ReqThreeLevelDataMessage.class)
public class ReqThreeLevelDataHandler extends TcpHandler {
    private static final Logger LOGGER = LoggerFactory.getLogger(ReqThreeLevelDataHandler.class);

    @Override
    public void run() {
        final AffiliateMessage.ResThreeLevelDataMessage.Builder res = AffiliateMessage.ResThreeLevelDataMessage.newBuilder();
        res.setMsgID(MIDMessage.MID.ResThreeLevelData_VALUE);
        try {
            final Player player = PlayerMrg.getInstance().getOnlinePlayer(pid, this.getClass().getSimpleName());
            if (player == null) {
                res.setError(ErrorCode.Player_Offline.getCode());
                MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                return;
            }

            final MerchantData merchantData = DataHallMrg.getInstance().findMerchantData(this.getClass().getSimpleName(), player.getBusiness_no());
            if (merchantData == null) {
                res.setError(ErrorCode.Data_Error.getCode());
                MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                return;
            }

            final boolean isManyCurrency = merchantData.getC_currencyMap().size() > 1;

            final AffiliateMessage.ReqThreeLevelDataMessage req = (AffiliateMessage.ReqThreeLevelDataMessage) getMessage();
            final String referralCode = req.getReferralCode();
            final long registerStart = req.getRegisterStart();
            final long registerEnd = req.getRegisterEnd();
            final long wagerStart = req.getWagerStart();
            final long wagerEnd = req.getWagerEnd();
            final int page = req.getPage();
            final int pageSize = req.getPageSize();
            final String search = req.getUsername();

            final Map<String, ReferralCode> referralCodeMap = player.getPlayerPromote().getReferralCodeMap();
            final List<PlayerPromote> playerPromotes = new ArrayList<>();
            if (!StringUtil.isNullOrEmpty(search)) {
                long userId = 0;
                String userName = "";
                if (StringUtils.isNumeric(search)) {
                    userId = Long.parseLong(search);
                } else {
                    userName = search;
                }
                final PlayerPromote playerPromote = EntityDaoMrg.getInstance().getDao(PlayerPromoteDao.class)
                        .findUserNameOrUserIdThreeLevelFriend(new ArrayList<>(referralCodeMap.keySet()), userName, userId);
                if (playerPromote != null) {
                    playerPromotes.add(playerPromote);
                }
            } else {
                final List<String> referralCodes = new ArrayList<>();
                if (StringUtil.isNullOrEmpty(referralCode)) {
                    referralCodes.addAll(referralCodeMap.keySet());
                } else {
                    referralCodes.add(referralCode);
                }
                final int skip = (page - 1) * pageSize;
                final Tuple2<Integer, List<PlayerPromote>> tuple2 = EntityDaoMrg.getInstance().getDao(PlayerPromoteDao.class)
                        .findByCodeThreeLevelFriends(referralCodes, registerStart, registerEnd, skip, pageSize);
                final List<PlayerPromote> playerPromoteList = tuple2.getSecond();
                if (!playerPromoteList.isEmpty()) {
                    playerPromotes.addAll(playerPromoteList);
                }
            }

            final OptionalDouble optionalDouble = merchantData.getC_cashBackMap().values().stream()
                    .mapToDouble(C_CashBack::getThreeLevelCashBackRate).max();
            final double threeLevelRate = optionalDouble.isPresent() ? optionalDouble.getAsDouble() : 0;

            final List<Long> referrals = new ArrayList<>();
            for (PlayerPromote playerPromote : playerPromotes) {
                referrals.add(playerPromote.getPlayerId());

                GameWagerStat gameWagerStat = null;
                if (isManyCurrency) {
                    gameWagerStat = EntityDaoMrg.getInstance().getDao(GameNoteDao.class).aggregateGameWagerStat(referrals, 0, GameNoteFields.usdValidBets, wagerStart, wagerEnd);
                } else {
                    gameWagerStat = EntityDaoMrg.getInstance().getDao(GameNoteDao.class).aggregateGameWagerStat(referrals, player.getCurrencyId(), GameNoteFields.validBets, wagerStart, wagerEnd);
                }

                final GameWagerStat finalGameWagerStat = gameWagerStat;
                final GameCommissionStat commissionStat = ScriptLoader.getInstance().functionScript("AffiliateScript",
                        (IAffiliateScript script) -> script.calculateThreeLevelCommissionStat(finalGameWagerStat, merchantData));
                res.addFriends(buildFriends(playerPromote, gameWagerStat, commissionStat, threeLevelRate));
                referrals.clear();
            }
            MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
        } catch (Exception e) {
            LOGGER.error("ReqThreeLevelDataHandler", e);
            res.setError(ErrorCode.Internal_Server_Error.getCode());
            MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
        }
    }

    private AffiliateMessage.Friends buildFriends(PlayerPromote playerPromote, GameWagerStat gameWagerStat, GameCommissionStat commissionStat, double commissionRate) {
        return AffiliateMessage.Friends.newBuilder()
                .setUserName(playerPromote.getPlayerName())
                .setUserId(playerPromote.getPlayerId())
                .setCode(playerPromote.getSuperiorCode())
                .setRegistrationDate(playerPromote.getCreateTime())
                .setStatus(playerPromote.getState())
                .setCommissionRate(commissionRate)
                .setWagerUsdForBCOriginal(gameWagerStat.getTotalWagerOriginal())
                .setWagerUsdFor3RdParty(gameWagerStat.getTotalWager3rdParty())
                .setWagerUsdForSports(gameWagerStat.getTotalWagerSports())
                .setWagerUsd(gameWagerStat.getTotalWager())
                .setCommissionUsdForBCOriginal(commissionStat.getTotalCommissionOriginal())
                .setCommissionUsdFor3RdParty(commissionStat.getTotalCommission3rdParty())
                .setCommissionUsdForSports(commissionStat.getTotalCommissionSports())
                .setCommissionUsd(commissionStat.getTotalCommissionReward())
                .build();
    }

}
