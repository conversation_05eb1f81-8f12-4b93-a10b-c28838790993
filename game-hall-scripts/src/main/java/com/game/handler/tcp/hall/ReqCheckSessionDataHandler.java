package com.game.handler.tcp.hall;

import com.game.dao.session.SessionDao;
import com.game.engine.io.handler.TcpHandler;
import com.game.engine.script.IHandlerEntity;
import com.game.engine.util.misc.Tuple2;
import com.game.engine.utils.MsgUtil;
import com.game.engine.utils.TimeUtil;
import com.game.entity.session.SessionsNote;
import com.game.entity.player.Player;
import com.game.enums.ErrorCode;
import com.game.hall.mrg.CommonMrg;
import com.game.hall.mrg.player.PlayerMrg;
import com.game.manager.EntityDaoMrg;
import com.proto.HallMessage;
import com.proto.MIDMessage;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;

@IHandlerEntity(mid = MIDMessage.MID.ReqCheckSessionData_VALUE, msg = HallMessage.ReqCheckSessionDataMessage.class)
public class ReqCheckSessionDataHandler extends TcpHandler {
    private static final Logger LOGGER = LoggerFactory.getLogger(ReqCheckSessionDataHandler.class);

    @Override
    public void run() {
        final HallMessage.ResCheckSessionDataMessage.Builder res = HallMessage.ResCheckSessionDataMessage.newBuilder();
        res.setMsgID(MIDMessage.MID.ResCheckSessionData_VALUE);
        try {
            final Player player = PlayerMrg.getInstance().getOnlinePlayer(pid, this.getClass().getSimpleName());
            if (player == null) {
                res.setError(ErrorCode.Player_Offline.getCode());
                MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                return;
            }

            final HallMessage.ReqCheckSessionDataMessage req = (HallMessage.ReqCheckSessionDataMessage) getMessage();
            final int page = req.getPage();
            final int pageSize = req.getPageSize();

            final int skip = (page - 1) * pageSize;
            final long startTime = TimeUtil.currentTimeMillis() - 30 * TimeUtil.DAY;
            final long endTime = TimeUtil.currentTimeMillis();
            final Tuple2<Integer, List<SessionsNote>> tuple2 = EntityDaoMrg.getInstance().getDao(SessionDao.class).loadSessions(player, startTime, endTime, skip, pageSize);
            final int total = tuple2.getFirst();
            final List<SessionsNote> sessionsNoteList = tuple2.getSecond();

            res.setPage(page)
                    .setPageSize(pageSize)
                    .setTotal(total)
                    .setTotalPage(CommonMrg.totalPage(total, pageSize));
            for (SessionsNote sessionsNote : sessionsNoteList) {
                res.addSessionList(buildSessions(sessionsNote));
            }
            MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
        } catch (Exception e) {
            LOGGER.error("ReqCheckSessionDataHandler", e);
            res.setError(ErrorCode.Internal_Server_Error.getCode());
            MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
        }
    }

    private HallMessage.Sessions buildSessions(SessionsNote sessionsNote) {
        return HallMessage.Sessions.newBuilder()
                .setDevice(sessionsNote.getDevice())
                .setLocation(sessionsNote.getLocation())
                .setIpAddress(sessionsNote.getIpAddress())
                .setLastTime(sessionsNote.getLastTime())
                .build();
    }
}
