package com.game.handler.tcp.affiliate;

import com.game.c_entity.merchant.C_CashBack;
import com.game.dao.player.PlayerDao;
import com.game.dao.player.PlayerPromoteDao;
import com.game.engine.io.handler.TcpHandler;
import com.game.engine.script.IHandlerEntity;
import com.game.engine.utils.MsgUtil;
import com.game.entity.player.Player;
import com.game.entity.player.PlayerFields;
import com.game.entity.player.promote.PlayerPromote;
import com.game.entity.player.promote.ReferralCode;
import com.game.enums.ErrorCode;
import com.game.hall.mrg.DataHallMrg;
import com.game.hall.mrg.MerchantData;
import com.game.hall.mrg.player.PlayerMrg;
import com.game.manager.EntityDaoMrg;
import com.proto.AffiliateMessage;
import com.proto.MIDMessage;
import io.netty.util.internal.StringUtil;
import it.unimi.dsi.fastutil.objects.ObjectLists;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.mongodb.core.query.Update;

import java.util.Map;
import java.util.OptionalDouble;

@IHandlerEntity(mid = MIDMessage.MID.ReqCreateReferralCode_VALUE, msg = AffiliateMessage.ReqCreateReferralCodeMessage.class)
public class ReqCreateReferralCodeHandler extends TcpHandler {
    private static final Logger LOGGER = LoggerFactory.getLogger(ReqCreateReferralCodeHandler.class);

    @Override
    public void run() {
        final AffiliateMessage.ResCreateReferralCodeMessage.Builder res = AffiliateMessage.ResCreateReferralCodeMessage.newBuilder();
        res.setMsgID(MIDMessage.MID.ResCreateReferralCode_VALUE);
        try {
            final Player player = PlayerMrg.getInstance().getOnlinePlayer(pid, this.getClass().getSimpleName());
            if (player == null) {
                res.setError(ErrorCode.Player_Offline.getCode());
                MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                return;
            }

            final MerchantData merchantData = DataHallMrg.getInstance().findMerchantData(this.getClass().getSimpleName(), player.getBusiness_no());
            if (merchantData == null) {
                res.setError(ErrorCode.Data_Error.getCode());
                MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                return;
            }

            final AffiliateMessage.ReqCreateReferralCodeMessage req = (AffiliateMessage.ReqCreateReferralCodeMessage) getMessage();
            final String campaignName = req.getCampaignName();

            final PlayerPromote playerPromote = player.getPlayerPromote();
            final Map<String, ReferralCode> referralCodeMap = playerPromote.getReferralCodeMap();
            final ReferralCode referralCode = PlayerMrg.getInstance().createReferralCode(player, StringUtil.isNullOrEmpty(campaignName) ? "--" : campaignName);
            referralCodeMap.put(referralCode.getCode(), referralCode);
            EntityDaoMrg.getInstance().getDao(PlayerPromoteDao.class)
                    .updateInsertReferralCode(playerPromote, ObjectLists.singleton(referralCode.getCode()));

            player.setInvitationCode(referralCode.getCode());
            final Update update = new Update();
            update.set(PlayerFields.invitationCode, player.getInvitationCode());
            EntityDaoMrg.getInstance().getDao(PlayerDao.class)
                    .updatePlayer(player.getPlayerId(), update);

            final OptionalDouble commissionRate = merchantData.getC_cashBackMap().values().stream()
                    .mapToDouble(C_CashBack::getSuperiorCashBackRate).max();

            res.setName(campaignName)
                    .setReferralCode(referralCode.getCode())
                    .setLink(referralCode.getLink())
                    .setCreateTime(referralCode.getCreatedDate())
                    .setCommissionRate(commissionRate.isPresent() ? commissionRate.getAsDouble() : 0);
            MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
        } catch (Exception e) {
            LOGGER.error("ReqCreateReferralCodeHandler", e);
            res.setError(ErrorCode.Internal_Server_Error.getCode());
            MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
        }
    }

}
