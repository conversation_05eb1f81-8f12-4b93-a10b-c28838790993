package com.game.handler.tcp.activity.firstDepositInviteBonus;

import com.game.c_entity.merchant.C_FirstDepositInviteBonus;
import com.game.dao.game.GameNoteDao;
import com.game.dao.player.PlayerDao;
import com.game.dao.player.PlayerPromoteDao;
import com.game.engine.io.handler.TcpHandler;
import com.game.engine.math.BigDecimalUtils;
import com.game.engine.script.IHandlerEntity;
import com.game.engine.script.ScriptLoader;
import com.game.engine.utils.MsgUtil;
import com.game.engine.utils.TimeUtil;
import com.game.entity.game.GameNoteFields;
import com.game.entity.game.GameWagerStat;
import com.game.entity.player.Player;
import com.game.entity.player.activity.firstDepositInviteBonus.FirstDepositInviteBonusInfo;
import com.game.entity.player.promote.PlayerPromote;
import com.game.enums.ErrorCode;
import com.game.enums.GameType;
import com.game.hall.mrg.ActivityMrg;
import com.game.hall.mrg.DataHallMrg;
import com.game.hall.mrg.MerchantData;
import com.game.hall.mrg.player.PlayerMrg;
import com.game.hall.script.IGameScript;
import com.game.manager.EntityDaoMrg;
import com.proto.ActivityMessage;
import com.proto.MIDMessage;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

@IHandlerEntity(mid = MIDMessage.MID.ReqCheckDetailsData_VALUE, msg = ActivityMessage.ReqCheckDetailsDataMessage.class)
public class ReqCheckDetailsDataHandler extends TcpHandler {
    private static final Logger LOGGER = LoggerFactory.getLogger(ReqCheckDetailsDataHandler.class);

    @Override
    public void run() {
        final ActivityMessage.ResCheckDetailsDataMessage.Builder res = ActivityMessage.ResCheckDetailsDataMessage.newBuilder();
        res.setMsgID(MIDMessage.MID.ResCheckDetailsData_VALUE);
        try {
            final Player player = PlayerMrg.getInstance().getOnlinePlayer(pid, this.getClass().getSimpleName());
            if (player == null) {
                res.setError(ErrorCode.Player_Offline.getCode());
                MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                return;
            }

            final MerchantData merchantData = DataHallMrg.getInstance().findMerchantData(this.getClass().getSimpleName(), player.getBusiness_no());
            if (merchantData == null) {
                res.setError(ErrorCode.Data_Error.getCode());
                MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                return;
            }

            final C_FirstDepositInviteBonus c_firstDepositInviteBonus = merchantData.findC_FirstDepositInviteBonus(this.getClass().getSimpleName(), ActivityMrg.FIRSTDEPOSIT_INIVTEBONUS);
            if (c_firstDepositInviteBonus == null) {
                res.setError(ErrorCode.Data_Error.getCode());
                MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                return;
            }

            final ActivityMessage.ReqCheckDetailsDataMessage req = (ActivityMessage.ReqCheckDetailsDataMessage) getMessage();
            final int page = req.getPage();
            final int pageSize = req.getPageSize();

            long startTime = TimeUtil.currentTimeMillis();
            final List<Long> playerId = findSubordinates(player);
            final List<Long> allPlayerId = paginate(playerId, page, pageSize);

            if (allPlayerId.isEmpty()) {
                MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                return;
            }

            final FirstDepositInviteBonusInfo firstDepositInviteBonusInfo = player.getFirstDepositInviteBonusInfo();

            final List<Player> playerList = EntityDaoMrg.getInstance().getDao(PlayerDao.class).loadPlayersByIds(allPlayerId);
            for (Player p : playerList) {
                final double totalValidBet = totalValidBet(player, p.getPlayerId(), firstDepositInviteBonusInfo.getCurrencyId(), firstDepositInviteBonusInfo.getFirstDepositTime());
                res.addFriendsValidWageredList(buildFriendsValidWageredData(p, totalValidBet));
            }

            LOGGER.warn("ReqCheckDetailsDataHandler，playerId：{}，cost time：{}", pid, TimeUtil.currentTimeMillis() - startTime);
            MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
        } catch (Exception e) {
            LOGGER.error("ReqCheckDetailsDataHandler", e);
            res.setError(ErrorCode.Internal_Server_Error.getCode());
            MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
        }
    }

    private <T> List<T> paginate(List<T> list, int page, int pageSize) {
        if (list == null || list.isEmpty()) return Collections.emptyList();

        int totalItems = list.size();
        int fromIndex = (page - 1) * pageSize;
        int toIndex = Math.min(fromIndex + pageSize, totalItems);

        if (fromIndex >= totalItems) {
            return Collections.emptyList(); // 页码过大返回空列表
        }

        return list.subList(fromIndex, toIndex);
    }

    private double totalValidBet(Player player, long playerId, int currencyId, long firstDepositTime) {
        if (firstDepositTime == 0) {
            return 0.0;
        }

        /**
         * Casino_Original(101, "原创"),
         *     Casino_Slots(102, "电子"),
         *     Casino_Live(103, "赌场视讯"),
         *     Casino_Poker(104, "棋牌牌桌"),
         *     Casino_Fish(105, "捕鱼"),
         *     Casino_Arcade(106, "街机"),
         *     Casino_Bingo(107, "宾果"),
         */
        final GameWagerStat gameWagerStat = EntityDaoMrg.getInstance().getDao(GameNoteDao.class)
                .aggregateGameWagerStat(List.of(playerId), currencyId, GameNoteFields.validBets, firstDepositTime, TimeUtil.currentTimeMillis());

        //TODO 计算有效
        final double originalValidBet = ScriptLoader.getInstance().functionScript("GameScript",
                (IGameScript script) -> script.calculateValidBettingVolume(player, GameType.Casino_Original.getType(), currencyId, gameWagerStat.getTotalWagerOriginal()));

        final double slotsValidBet = ScriptLoader.getInstance().functionScript("GameScript",
                (IGameScript script) -> script.calculateValidBettingVolume(player, GameType.Casino_Slots.getType(), currencyId, gameWagerStat.getTotalWagerSlots()));

        final double liveValidBet = ScriptLoader.getInstance().functionScript("GameScript",
                (IGameScript script) -> script.calculateValidBettingVolume(player, GameType.Casino_Live.getType(), currencyId, gameWagerStat.getTotalWagerLive()));

        final double pokerValidBet = ScriptLoader.getInstance().functionScript("GameScript",
                (IGameScript script) -> script.calculateValidBettingVolume(player, GameType.Casino_Poker.getType(), currencyId, gameWagerStat.getTotalWagerPoker()));

        final double fishValidBet = ScriptLoader.getInstance().functionScript("GameScript",
                (IGameScript script) -> script.calculateValidBettingVolume(player, GameType.Casino_Fish.getType(), currencyId, gameWagerStat.getTotalWagerFish()));

        final double arcadeValidBet = ScriptLoader.getInstance().functionScript("GameScript",
                (IGameScript script) -> script.calculateValidBettingVolume(player, GameType.Casino_Arcade.getType(), currencyId, gameWagerStat.getTotalWagerArcade()));

        final double bingoValidBet = ScriptLoader.getInstance().functionScript("GameScript",
                (IGameScript script) -> script.calculateValidBettingVolume(player, GameType.Casino_Bingo.getType(), currencyId, gameWagerStat.getTotalWagerBingo()));

        return BigDecimalUtils.add(originalValidBet + slotsValidBet + liveValidBet, pokerValidBet + fishValidBet + arcadeValidBet + bingoValidBet, 9);
    }

    private ActivityMessage.FriendsValidWageredData buildFriendsValidWageredData(Player player, double totalValidBet) {
        final ActivityMessage.FriendsValidWageredData.Builder res = ActivityMessage.FriendsValidWageredData.newBuilder();
        res.setHeadId(player.getHeadId())
                .setPlayerName(player.getPlayerName())
                .setValidWagered(totalValidBet);
        return res.build();
    }

    private List<Long> findSubordinates(Player player) {
        final int limit = 1000;
        int totalCount = 0;
        final List<Long> subordinates = new ArrayList<>();
        while (true) {
            final List<PlayerPromote> friendsList = EntityDaoMrg.getInstance().getDao(PlayerPromoteDao.class).loadAllBySuperiorId(player.getPlayerId(), totalCount, limit);
            if (friendsList == null || friendsList.isEmpty()) break;

            for (var friend : friendsList) {
                if (friend == null) continue;
                subordinates.add(friend.getPlayerId());
            }

            totalCount += friendsList.size();
            if (friendsList.size() < limit) break;
        }
        return subordinates;
    }
}
