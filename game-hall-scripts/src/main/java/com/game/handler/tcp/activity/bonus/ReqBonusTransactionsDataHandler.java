package com.game.handler.tcp.activity.bonus;

import com.game.dao.activity.BonusNoteDao;
import com.game.engine.io.handler.TcpHandler;
import com.game.engine.script.IHandlerEntity;
import com.game.engine.util.misc.Tuple2;
import com.game.engine.utils.MsgUtil;
import com.game.engine.utils.TimeUtil;
import com.game.entity.bonus.BonusNote;
import com.game.entity.player.Player;
import com.game.enums.BonusDetail;
import com.game.enums.ErrorCode;
import com.game.hall.mrg.CommonMrg;
import com.game.hall.mrg.player.PlayerMrg;
import com.game.manager.EntityDaoMrg;
import com.proto.ActivityMessage;
import com.proto.MIDMessage;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Arrays;
import java.util.LinkedList;
import java.util.List;
import java.util.stream.Collectors;

@IHandlerEntity(mid = MIDMessage.MID.ReqBonusTransactionsData_VALUE, msg = ActivityMessage.ReqBonusTransactionsDataMessage.class)
public class ReqBonusTransactionsDataHandler extends TcpHandler {
    private static final Logger LOGGER = LoggerFactory.getLogger(ReqBonusTransactionsDataHandler.class);

    @Override
    public void run() {
        final ActivityMessage.ResBonusTransactionsDataMessage.Builder res = ActivityMessage.ResBonusTransactionsDataMessage.newBuilder();
        res.setMsgID(MIDMessage.MID.ResBonusTransactionsData_VALUE);
        try {
            final Player player = PlayerMrg.getInstance().getOnlinePlayer(pid, this.getClass().getSimpleName());
            if (player == null) {
                res.setError(ErrorCode.Player_Offline.getCode());
                MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                return;
            }
            final ActivityMessage.ReqBonusTransactionsDataMessage req = (ActivityMessage.ReqBonusTransactionsDataMessage) getMessage();
            final int bonusType = req.getBonusType();
            final int page = req.getPage();
            final int pageSize = req.getPageSize();

            final List<Integer> bonusTypes = new LinkedList<>();
            if (bonusType == 0) {
                List<Integer> values = Arrays.stream(BonusDetail.values())
                        .map(BonusDetail::getType)
                        .toList();
                bonusTypes.addAll(values);
            } else {
                bonusTypes.add(bonusType);
            }

            final int skip = (page - 1) * pageSize;
            final long start = TimeUtil.currentTimeMillis() - 60 * TimeUtil.DAY;
            final long end = TimeUtil.currentTimeMillis();
            final Tuple2<Integer, List<BonusNote>> tuple2 = EntityDaoMrg.getInstance().getDao(BonusNoteDao.class).loadBonusNote(pid, bonusTypes, start, end, skip, pageSize);
            final int total = tuple2.getFirst();
            final List<BonusNote> bonusNotes = tuple2.getSecond();

            res.setPage(page)
                    .setPageSize(pageSize)
                    .setTotal(total)
                    .setTotalPage(CommonMrg.totalPage(total, pageSize));
            for (BonusNote bonusNote : bonusNotes) {
                res.addBonusNoteList(buildBonusNote(bonusNote));
            }
            MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
        } catch (Exception e) {
            LOGGER.error("ReqBonusTransactionsDataHandler", e);
            res.setError(ErrorCode.Internal_Server_Error.getCode());
            MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
        }
    }

    private ActivityMessage.BonusNote buildBonusNote(BonusNote bonusNote) {
        return ActivityMessage.BonusNote.newBuilder()
                .setBonusType(bonusNote.getBonusType())
                .setCurrencyId(bonusNote.getCurrencyId())
                .setAmount(bonusNote.getAmount())
                .setTime(bonusNote.getCreateTime())
                .build();
    }
}
