package com.game.handler.tcp.affiliate;

import com.game.dao.promote.CommissionRewardsNoteDao;
import com.game.dao.promote.ReferralRewardsNoteDao;
import com.game.dao.promote.TeamRewardsNoteDao;
import com.game.dao.promote.ThreeLevelRewardsNoteDao;
import com.game.engine.io.handler.TcpHandler;
import com.game.engine.script.IHandlerEntity;
import com.game.engine.util.misc.Tuple2;
import com.game.engine.utils.MsgUtil;
import com.game.engine.utils.TimeUtil;
import com.game.entity.player.Player;
import com.game.entity.player.promote.CommissionRewardsNote;
import com.game.entity.player.promote.ReferralRewardsNote;
import com.game.entity.player.promote.TeamRewardsNote;
import com.game.entity.player.promote.ThreeLevelRewardsNote;
import com.game.enums.ErrorCode;
import com.game.hall.mrg.CommonMrg;
import com.game.hall.mrg.player.PlayerMrg;
import com.game.manager.EntityDaoMrg;
import com.proto.AffiliateMessage;
import com.proto.MIDMessage;
import io.netty.util.internal.StringUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;

@IHandlerEntity(mid = MIDMessage.MID.ReqHistoryData_VALUE, msg = AffiliateMessage.ReqHistoryDataMessage.class)
public class ReqHistoryDataHandler extends TcpHandler {
    private static final Logger LOGGER = LoggerFactory.getLogger(ReqHistoryDataHandler.class);

    @Override
    public void run() {
        final AffiliateMessage.ResHistoryDataMessage.Builder res = AffiliateMessage.ResHistoryDataMessage.newBuilder();
        res.setMsgID(MIDMessage.MID.ResHistoryData_VALUE);
        try {
            final Player player = PlayerMrg.getInstance().getOnlinePlayer(pid, this.getClass().getSimpleName());
            if (player == null) {
                res.setError(ErrorCode.Player_Offline.getCode());
                MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                return;
            }

            final AffiliateMessage.ReqHistoryDataMessage req = (AffiliateMessage.ReqHistoryDataMessage) getMessage();
            final int type = req.getType();//1.referralRewards 2.commissionRewards 3.teamRewards
            final int page = req.getPage();
            final int pageSize = req.getPageSize();

            res.setPage(page)
                    .setPageSize(pageSize);
            final long startTime = TimeUtil.currentTimeMillis() - 60 * TimeUtil.DAY;
            final long endTime = TimeUtil.currentTimeMillis();
            final int skip = (page - 1) * pageSize;
            switch (type) {
                case 1:
                    final Tuple2<Integer, List<ReferralRewardsNote>> tuple1 = EntityDaoMrg.getInstance().getDao(ReferralRewardsNoteDao.class)
                            .findByTime(pid, startTime, endTime, skip, pageSize);

                    final int referralCount = tuple1.getFirst();
                    final List<ReferralRewardsNote> referralRewardsNotes = tuple1.getSecond();
                    res.setTotal(referralCount)
                            .setTotalPage(CommonMrg.totalPage(referralCount, pageSize));
                    for (ReferralRewardsNote referralRewardsNote : referralRewardsNotes) {
                        res.addReferralHistory(buildReferralRewardHistory(referralRewardsNote));
                    }
                    break;
                case 2:
                    final Tuple2<Integer, List<CommissionRewardsNote>> tuple2 = EntityDaoMrg.getInstance().getDao(CommissionRewardsNoteDao.class)
                            .findByTime(pid, startTime, endTime, skip, pageSize);

                    final int commissionCount = tuple2.getFirst();
                    final List<CommissionRewardsNote> commissionRewardsNoteList = tuple2.getSecond();
                    res.setTotal(commissionCount)
                            .setTotalPage(CommonMrg.totalPage(commissionCount, pageSize));
                    for (CommissionRewardsNote commissionRewardsNote : commissionRewardsNoteList) {
                        res.addCommissionHistory(buildCommissionRewardHistory(commissionRewardsNote));
                    }
                    break;
                case 3:
                    final Tuple2<Integer, List<TeamRewardsNote>> tuple3 = EntityDaoMrg.getInstance().getDao(TeamRewardsNoteDao.class)
                            .findByTime(pid, startTime, endTime, skip, pageSize);

                    final int teamCount = tuple3.getFirst();
                    final List<TeamRewardsNote> teamRewardsNoteList = tuple3.getSecond();
                    res.setTotal(teamCount)
                            .setTotalPage(CommonMrg.totalPage(teamCount, pageSize));
                    for (TeamRewardsNote teamRewardsNote : teamRewardsNoteList) {
                        res.addTeamRewardHistory(buildTeamRewardHistory(teamRewardsNote));
                    }
                case 4:
                    final Tuple2<Integer, List<ThreeLevelRewardsNote>> tuple4 = EntityDaoMrg.getInstance().getDao(ThreeLevelRewardsNoteDao.class)
                            .findByTime(pid, startTime, endTime, skip, pageSize);

                    final int threeLevelCount = tuple4.getFirst();
                    final List<ThreeLevelRewardsNote> threeLevelRewardsNoteList = tuple4.getSecond();
                    res.setTotal(threeLevelCount)
                            .setTotalPage(CommonMrg.totalPage(threeLevelCount, pageSize));
                    for (ThreeLevelRewardsNote threeLevelRewardsNote : threeLevelRewardsNoteList) {
                        res.addThreeLevelRewardHistory(buildThreeLevelRewardHistory(threeLevelRewardsNote));
                    }
                    break;
            }

            MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
        } catch (Exception e) {
            LOGGER.error("ReqHistoryHandler", e);
            res.setError(ErrorCode.Internal_Server_Error.getCode());
            MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
        }
    }

    private AffiliateMessage.CommissionRewardHistory buildCommissionRewardHistory(CommissionRewardsNote commissionRewardsNote) {
        return AffiliateMessage.CommissionRewardHistory.newBuilder()
                .setCurrencyId(commissionRewardsNote.getCurrencyId())
                .setAmount(commissionRewardsNote.getAmount())
                .setTime(commissionRewardsNote.getTime())
                .setStatus(commissionRewardsNote.getStatus())
                .build();
    }

    private AffiliateMessage.ReferralRewardHistory buildReferralRewardHistory(ReferralRewardsNote referralRewardsNote) {
        return AffiliateMessage.ReferralRewardHistory.newBuilder()
                .setPlayerName(StringUtil.isNullOrEmpty(referralRewardsNote.getPlayerName()) ? "" : referralRewardsNote.getPlayerName())
                .setCurrencyId(referralRewardsNote.getCurrencyId())
                .setAmount(referralRewardsNote.getAmount())
                .setTime(referralRewardsNote.getTime())
                .setStatus(referralRewardsNote.getStatus())
                .build();
    }

    private AffiliateMessage.TeamRewardHistory buildTeamRewardHistory(TeamRewardsNote teamRewardsNote) {
        return AffiliateMessage.TeamRewardHistory.newBuilder()
                .setCurrencyId(teamRewardsNote.getCurrencyId())
                .setAmount(teamRewardsNote.getAmount())
                .setTime(teamRewardsNote.getTime())
                .setStatus(teamRewardsNote.getStatus())
                .build();
    }

    private AffiliateMessage.ThreeLevelRewardHistory buildThreeLevelRewardHistory(ThreeLevelRewardsNote threeLevelRewardsNote) {
        return AffiliateMessage.ThreeLevelRewardHistory.newBuilder()
                .setCurrencyId(threeLevelRewardsNote.getCurrencyId())
                .setAmount(threeLevelRewardsNote.getAmount())
                .setTime(threeLevelRewardsNote.getTime())
                .setStatus(threeLevelRewardsNote.getStatus())
                .build();
    }
}
