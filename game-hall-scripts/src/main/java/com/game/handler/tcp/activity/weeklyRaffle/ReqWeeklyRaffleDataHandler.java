package com.game.handler.tcp.activity.weeklyRaffle;

import com.game.c_entity.merchant.C_WeeklyRaffle;
import com.game.c_entity.middleplatform.C_BaseMerchant;
import com.game.dao.activity.TicketsNoteDao;
import com.game.engine.io.handler.TcpHandler;
import com.game.engine.io.redis.RedisPoolManager;
import com.game.engine.script.IHandlerEntity;
import com.game.engine.utils.MsgUtil;
import com.game.engine.utils.TimeUtil;
import com.game.entity.activity.TicketsNote;
import com.game.enums.ErrorCode;
import com.game.enums.redis.RedisAllGame;
import com.game.hall.mrg.ActivityMrg;
import com.game.hall.mrg.DataHallMrg;
import com.game.hall.mrg.CommonMrg;
import com.game.hall.mrg.MerchantData;
import com.game.manager.EntityDaoMrg;
import com.proto.ActivityMessage;
import com.proto.MIDMessage;
import io.netty.util.internal.StringUtil;
import org.checkerframework.checker.units.qual.C;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.time.DayOfWeek;
import java.util.List;

@IHandlerEntity(mid = MIDMessage.MID.ReqWeeklyRaffleData_VALUE, msg = ActivityMessage.ReqWeeklyRaffleDataMessage.class)
public class ReqWeeklyRaffleDataHandler extends TcpHandler {
    private static final Logger LOGGER = LoggerFactory.getLogger(ReqWeeklyRaffleDataHandler.class);

    @Override
    public void run() {
        final ActivityMessage.ResWeeklyRaffleDataMessage.Builder res = ActivityMessage.ResWeeklyRaffleDataMessage.newBuilder();
        res.setMsgID(MIDMessage.MID.ResWeeklyRaffleData_VALUE);
        try {
            final ActivityMessage.ReqWeeklyRaffleDataMessage req = (ActivityMessage.ReqWeeklyRaffleDataMessage) getMessage();
            final String host = req.getHost();

            final C_BaseMerchant c_baseHostMerchant = DataHallMrg.getInstance().findC_BaseHostMerchant(this.getClass().getSimpleName(), host);
            if (c_baseHostMerchant == null) {
                res.setError(ErrorCode.Data_Error.getCode());
                MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                return;
            }

            final String business_no = c_baseHostMerchant.getBusiness_no();
            final MerchantData merchantData = DataHallMrg.getInstance().findMerchantData(this.getClass().getSimpleName(), c_baseHostMerchant.getBusiness_no());
            if (merchantData == null) {
                res.setError(ErrorCode.Data_Error.getCode());
                MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                return;
            }

            final C_WeeklyRaffle c_weeklyRaffle = merchantData.findC_WeeklyRaffle(this.getClass().getSimpleName(), ActivityMrg.WEEKLY_RAFFLE);
            if (c_weeklyRaffle == null) {
                res.setError(ErrorCode.Data_Error.getCode());
                MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                return;
            }

            final String thisRoundTickets = RedisPoolManager.getInstance().function(jedis ->
                    jedis.sync().get(RedisAllGame.Platform_All_WeeklyRaffle_Tickets.getKey(business_no, ActivityMrg.getGameId())));
            res.setGameId(ActivityMrg.getGameId())
                    .setThisRoundTickets(Integer.parseInt(StringUtil.isNullOrEmpty(thisRoundTickets) ? "0" : thisRoundTickets))
                    .setSuperLuckDraw(CommonMrg.buildDItemShow(c_weeklyRaffle.getRewardCurrency(), c_weeklyRaffle.getReward()))
                    .setEndTime(TimeUtil.getDayOfWeekEndTimestamp(DayOfWeek.SUNDAY))
                    .setDailyWagered(c_weeklyRaffle.getDailyUsdWagered())
                    .setEveryWagered(c_weeklyRaffle.getEveryUsdWagered());

            final List<TicketsNote> ticketsNotes = EntityDaoMrg.getInstance().getDao(TicketsNoteDao.class).loadTicketsNote(c_baseHostMerchant.getBusiness_no(), TimeUtil.currentTimeMillis() - 7 * TimeUtil.DAY, TimeUtil.currentTimeMillis(), 50);
            for (TicketsNote ticketsNote : ticketsNotes) {
                res.addRecentGetList(buildRecentGetData(ticketsNote));
            }

            //1，2-3，4-6
            int max = 0;
            for (int i = 0; i < c_weeklyRaffle.getRewardAllocationList().size(); i++) {
                final C_WeeklyRaffle.RewardAllocation rewardAllocation = c_weeklyRaffle.getRewardAllocationList().get(i);
                int min = rewardAllocation.getNum();
                max += rewardAllocation.getNum();
                if (i > 0) {
                    min = max - rewardAllocation.getNum() + 1;
                }
                res.addRewardList(buildRewardAllocation(rewardAllocation, min, max));

            }
            MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
        } catch (Exception e) {
            LOGGER.error("ReqWeeklyRaffleDataHandler", e);
            res.setError(ErrorCode.Internal_Server_Error.getCode());
            MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
        }
    }

    private ActivityMessage.RecentGetData buildRecentGetData(TicketsNote ticketsNote) {
        return ActivityMessage.RecentGetData.newBuilder()
                .setHeadId(ticketsNote.getHeadId())
                .setPlayerName(ticketsNote.getPlayerName())
                .setNum(1)
                .build();
    }

    private ActivityMessage.RewardAllocation buildRewardAllocation(C_WeeklyRaffle.RewardAllocation rewardAllocation, int min, int max) {
        return ActivityMessage.RewardAllocation.newBuilder()
                .setRewardLevels(rewardAllocation.getRewardLevels())
                .setMin(min)
                .setMax(max)
                .setCurrencyId(rewardAllocation.getCurrencyId())
                .setReward(rewardAllocation.getReward())
                .build();
    }
}
