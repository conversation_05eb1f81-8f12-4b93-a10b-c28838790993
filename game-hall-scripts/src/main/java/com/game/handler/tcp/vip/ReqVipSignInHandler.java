package com.game.handler.tcp.vip;

import com.game.c_entity.merchant.C_VipClub;
import com.game.dao.player.PlayerDao;
import com.game.engine.io.handler.TcpHandler;
import com.game.engine.log.GameLog;
import com.game.engine.math.BigDecimalUtils;
import com.game.engine.script.IHandlerEntity;
import com.game.engine.script.ScriptLoader;
import com.game.engine.util.misc.Tuple2;
import com.game.engine.utils.GuidGeneratorUtils;
import com.game.engine.utils.MsgUtil;
import com.game.engine.utils.TimeUtil;
import com.game.entity.player.Player;
import com.game.entity.player.vip.VipClub;
import com.game.enums.ErrorCode;
import com.game.enums.RewardReason;
import com.game.enums.TransactionFrom;
import com.game.enums.TurnoverReason;
import com.game.hall.main.HallServer;
import com.game.hall.mrg.CommonMrg;
import com.game.hall.mrg.DataHallMrg;
import com.game.hall.mrg.MerchantData;
import com.game.hall.mrg.currency.CurrencyMrg;
import com.game.hall.mrg.currency.RewardRequest;
import com.game.hall.mrg.player.PlayerMrg;
import com.game.hall.script.IBonusScript;
import com.game.hall.script.IPlayerScript;
import com.game.manager.EntityDaoMrg;
import com.proto.ActivityMessage;
import com.proto.MIDMessage;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Map;

@IHandlerEntity(mid = MIDMessage.MID.ReqVipSignIn_VALUE, msg = ActivityMessage.ReqVipSignInMessage.class)
public class ReqVipSignInHandler extends TcpHandler {
    private static final Logger LOGGER = LoggerFactory.getLogger(ReqVipSignInHandler.class);

    @Override
    public void run() {
        final ActivityMessage.ResVipSignInMessage.Builder res = ActivityMessage.ResVipSignInMessage.newBuilder();
        res.setMsgID(MIDMessage.MID.ResVipSignIn_VALUE);
        try {
            final ActivityMessage.ReqVipSignInMessage req = (ActivityMessage.ReqVipSignInMessage) getMessage();
            final int day = req.getDay();

            final Player player = PlayerMrg.getInstance().getOnlinePlayer(pid, this.getClass().getSimpleName());
            if (player == null) {
                res.setError(ErrorCode.Player_Offline.getCode());
                MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                return;
            }

            final MerchantData merchantData = DataHallMrg.getInstance().findMerchantData(this.getClass().getSimpleName(), player.getBusiness_no());
            if (merchantData == null) {
                res.setError(ErrorCode.Data_Error.getCode());
                MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                return;
            }

            final VipClub vipClub = player.getVipClub();

            final C_VipClub c_vipClub = merchantData.findC_VipClub(this.getClass().getSimpleName(), vipClub.getVipLevel());
            if (c_vipClub == null) {
                res.setError(ErrorCode.Data_Error.getCode());
                MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                return;
            }

            if (!vipClub.isActivation()) {
                res.setError(ErrorCode.Conditions_Not_Met.getCode());
                MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                return;
            }

            if (vipClub.getReceiveDays().contains(day)) {
                res.setError(ErrorCode.Reward_Received.getCode());
                MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                return;
            }

            final int currDay = TimeUtil.getNaturalDays(TimeUtil.currentTimeMillis(), vipClub.getActivationTime(), c_vipClub.getTimeZone());
            if (day != currDay) {
                res.setError(ErrorCode.Not_Yet_Received.getCode());
                MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                return;
            }

            final Map<Integer, C_VipClub.SignInReward> signInRewardMap = c_vipClub.getSignInRewardMap();
            final C_VipClub.SignInReward signInReward = signInRewardMap.get(day);
            if (signInReward == null) {
                res.setError(ErrorCode.Conditions_Not_Met.getCode());
                MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                return;
            }

            vipClub.setCurrDay(currDay);
            vipClub.getReceiveDays().add(currDay);
            EntityDaoMrg.getInstance().getDao(PlayerDao.class)
                    .vipClubDao.updateReceiveDays(pid, vipClub);

            final RewardRequest rewardRequest = new RewardRequest();
            rewardRequest.addCurrency(signInReward.currencyId, signInReward.reward);
            final RewardReason rewardReason = RewardReason.Vip_SignIn;
            rewardReason.setSource(currDay + "");
            if (c_vipClub.getSignInRewardType() == 1) {
                CurrencyMrg.getInstance().rewardNoTest(player, rewardRequest.getCurrencyMap(), rewardReason);
            } else {
                CurrencyMrg.getInstance().rewardNoTest(player, rewardRequest.getBonusCurrencyMap(), rewardReason);
            }

            //TODO 打码
//            final WithdrawStandard withdrawStandard = player.getWithdrawStandard(signInReward.currencyId);
//            withdrawStandard.incDrawStandard(BigDecimalUtils.mul(signInReward.reward, signInReward.turnoverMul, 4));
            final TurnoverReason turnoverReason = TurnoverReason.Vip_SignIn;
            turnoverReason.setSource(currDay + "");
            ScriptLoader.getInstance().consumerScript("PlayerWithdrawStandardScript",
                    (IPlayerScript script) -> script.drawStandard(player, turnoverReason, signInReward.currencyId, signInReward.reward,
                            BigDecimalUtils.mul(signInReward.reward, signInReward.turnoverMul, 4)));

            if (!rewardRequest.getCurrencyMap().isEmpty()) {
                final Tuple2<Integer, Double> tuple2 = rewardRequest.getReward();
                ScriptLoader.getInstance().consumerScript("BonusScript",
                        (IBonusScript script) -> script.addBonusNote(TransactionFrom.VipSignIn, player, tuple2.getFirst(), tuple2.getSecond()));
            }

            res.setRewardShow(CommonMrg.buildDItemShow(signInReward.currencyId, signInReward.reward));
            MsgUtil.responseHttp(res.build(), res.getMsgID(), session);

            final GameLog playerVipSignInLog = new GameLog("platform_playerVipSignInLog");
            playerVipSignInLog.append("number", GuidGeneratorUtils.generateOrderId())
                    .append("business_no", player.getBusiness_no())
                    .append("channel", player.getChannel())
                    .append("site", player.getWebSite())
                    .append("playerId", player.getPlayerId())
                    .append("playerName", player.getPlayerName())
                    .append("agentId", player.getAgentId())
                    .append("channelId", player.getChannelId())
                    .append("region", player.getRegisterRegion())
                    .append("logTime", TimeUtil.currentTimeMillis())
                    .append("type", 1)//1.领取 2.激活
                    .append("vipLevel", vipClub.getVipLevel())
                    .append("currDay", currDay)
                    .append("currencyId", signInReward.currencyId)
                    .append("reward", signInReward.reward);
            HallServer.getInstance().getLogProducerMrg().send(playerVipSignInLog);
        } catch (Exception e) {
            LOGGER.error("ReqVipSignInHandler", e);
            res.setError(ErrorCode.Internal_Server_Error.getCode());
            MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
        }
    }
}
