package com.game.handler.tcp.account;

import com.game.c_entity.middleplatform.C_BaseMerchant;
import com.game.dao.account.AccountDao;
import com.game.engine.io.handler.TcpHandler;
import com.game.engine.io.redis.RedisPoolManager;
import com.game.engine.script.IHandlerEntity;
import com.game.engine.utils.MD5;
import com.game.engine.utils.MsgUtil;
import com.game.engine.utils.PatternUtil;
import com.game.entity.account.Account;
import com.game.entity.account.AccountFields;
import com.game.enums.ErrorCode;
import com.game.enums.ThreeParty;
import com.game.enums.VerifyCode;
import com.game.enums.redis.RedisLogin;
import com.game.hall.mrg.DataHallMrg;
import com.game.hall.mrg.player.PlayerMrg;
import com.game.manager.EntityDaoMrg;
import com.proto.HallMessage;
import com.proto.MIDMessage;
import io.netty.util.internal.StringUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Objects;

@IHandlerEntity(mid = MIDMessage.MID.ReqResetPassword_VALUE, msg = HallMessage.ReqResetPasswordMessage.class)
public class ReqResetPasswordHandler extends TcpHandler {
    private static final Logger LOGGER = LoggerFactory.getLogger(ReqResetPasswordHandler.class);

    @Override
    public void run() {
        final HallMessage.ResResetPasswordMessage.Builder res = HallMessage.ResResetPasswordMessage.newBuilder();
        res.setMsgID(MIDMessage.MID.ResResetPassword_VALUE);
        try {
            final HallMessage.ReqResetPasswordMessage req = (HallMessage.ReqResetPasswordMessage) getMessage();
            final String host = req.getHost();
            final int threeParty = req.getThreeParty();
            final String areaCode = req.getAreaCode();
            final String reqAccount = req.getAccount();
            final String password = req.getPassword();
            final String confirmPassWord = req.getConfirmPassWord();
            final String verification = req.getVerification();

            final C_BaseMerchant c_baseHostMerchant = DataHallMrg.getInstance().findC_BaseHostMerchant(this.getClass().getSimpleName(), host);
            if (c_baseHostMerchant == null) {
                res.setError(ErrorCode.Data_Error.getCode());
                MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                return;
            }

            final String business_no = c_baseHostMerchant.getBusiness_no();

            if (confirmPassWord.length() < 6 || confirmPassWord.length() > 16) {
                res.setError(ErrorCode.PassWard_Length.getCode());
                MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                return;
            }

//            if (!password.matches("^[a-zA-Z0-9]+$")) {
//                res.setError(ErrorCode.PassWord_Format.getCode());
//                MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
//                return;
//            }

            if (!Objects.equals(password, confirmPassWord)) {
                res.setError(ErrorCode.PassWord_Error.getCode());
                MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                return;
            }

            String ac;
            if (threeParty == ThreeParty.Phone.getThreeParty()) {
                ac = areaCode + "-" + reqAccount;
            } else {
                ac = reqAccount;
            }

            //邮箱验证码
            final String finalAc = ac;
            final String verifyCode = RedisPoolManager.getInstance().function(jedis ->
                    jedis.sync().get(RedisLogin.Platform_LG_Account_VerifyCode.getKey(business_no, VerifyCode.reset.getType(), finalAc)));
            if (StringUtil.isNullOrEmpty(verification) || !verification.equals(verifyCode)) {
                LOGGER.warn("account：{}，verifyCode，{}-{}，", finalAc, verification, verifyCode);
                res.setError(ErrorCode.VerifyCode_Error.getCode());
                MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                return;
            }

            final String accountIds = RedisPoolManager.getInstance().function(jedis ->
                    jedis.sync().hget(RedisLogin.Platform_LG_Map_AccountID.getKey(business_no, threeParty), finalAc));
            final long accountId = StringUtil.isNullOrEmpty(accountIds) ? 0 : Long.parseLong(accountIds);
            if (accountId == 0) {
                res.setError(ErrorCode.Account_NotExist.getCode());
                MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                return;
            }

            final Account account = PlayerMrg.getInstance().findDbAccount(accountId);
            if (account == null) {
                res.setError(ErrorCode.Account_NotExist.getCode());
                MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                return;
            }

            account.setPassword(MD5.MD5Encode(password));
            EntityDaoMrg.getInstance().getDao(AccountDao.class)
                    .updateAccountField(account.getAccountId(), AccountFields.password, account.getPassword());

            res.setAreaCode(areaCode)
                    .setAccount(reqAccount)
                    .setThreeParty(threeParty);
            MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
        } catch (Exception e) {
            LOGGER.error("ReqResetPasswordHandler", e);
            res.setError(ErrorCode.Internal_Server_Error.getCode());
            MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
        }
    }
}
