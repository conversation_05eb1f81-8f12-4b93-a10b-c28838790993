//package com.game.handler.tcp.inner;
//
//import com.game.engine.io.handler.TcpHandler;
//import com.game.engine.script.IHandlerEntity;
//import com.game.entity.AgentGameInfo;
//import com.game.hall.mrg.ServerMrg;
//import com.proto.InnerMessage;
//import com.proto.MIDMessage;
//
//@IHandlerEntity(mid = MIDMessage.MID.InnerGetGameList_VALUE, msg = InnerMessage.InnerGetGameListMessage.class)
//public class HallInnerGetGameListHandler extends TcpHandler {
//    @Override
//    public void run() {
//        final InnerMessage.InnerGetGameListMessage req = (InnerMessage.InnerGetGameListMessage) getMessage();
//
//        for (InnerMessage.AgentGameInfo gameInfo : req.getAgentGameListList()) {
//            final AgentGameInfo agentGameInfo = ServerMrg.getInstance().addAgentGameInfo(gameInfo.getGameId());
//            agentGameInfo.setOnline(gameInfo.getOnline());
//        }
//    }
//
//}
