package com.game.handler.tcp.activity.firstDepositInviteBonus;

import com.game.c_entity.merchant.C_FirstDepositInviteBonus;
import com.game.dao.player.PlayerDao;
import com.game.engine.io.handler.TcpHandler;
import com.game.engine.log.GameLog;
import com.game.engine.math.BigDecimalUtils;
import com.game.engine.script.IHandlerEntity;
import com.game.engine.script.ScriptLoader;
import com.game.engine.util.misc.Tuple2;
import com.game.engine.utils.GuidGeneratorUtils;
import com.game.engine.utils.MsgUtil;
import com.game.engine.utils.TimeUtil;
import com.game.entity.player.Player;
import com.game.entity.player.activity.firstDepositInviteBonus.FirstDepositInviteBonusInfo;
import com.game.enums.ErrorCode;
import com.game.enums.RewardReason;
import com.game.enums.TransactionFrom;
import com.game.enums.TurnoverReason;
import com.game.hall.main.HallServer;
import com.game.hall.mrg.ActivityMrg;
import com.game.hall.mrg.CommonMrg;
import com.game.hall.mrg.DataHallMrg;
import com.game.hall.mrg.MerchantData;
import com.game.hall.mrg.currency.CurrencyMrg;
import com.game.hall.mrg.currency.RewardRequest;
import com.game.hall.mrg.player.PlayerMrg;
import com.game.hall.script.IBonusScript;
import com.game.hall.script.IPlayerScript;
import com.game.manager.EntityDaoMrg;
import com.proto.ActivityMessage;
import com.proto.MIDMessage;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;

@IHandlerEntity(mid = MIDMessage.MID.ReqReceiveDepositInviteBonus_VALUE, msg = ActivityMessage.ReqReceiveDepositInviteBonusMessage.class)
public class ReqReceiveDepositInviteBonusHandler extends TcpHandler {
    private static final Logger LOGGER = LoggerFactory.getLogger(ReqReceiveDepositInviteBonusHandler.class);

    @Override
    public void run() {
        final ActivityMessage.ResReceiveDepositInviteBonusMessage.Builder res = ActivityMessage.ResReceiveDepositInviteBonusMessage.newBuilder();
        res.setMsgID(MIDMessage.MID.ResReceiveDepositInviteBonus_VALUE);
        try {
            final Player player = PlayerMrg.getInstance().getOnlinePlayer(pid, this.getClass().getSimpleName());
            if (player == null) {
                res.setError(ErrorCode.Player_Offline.getCode());
                MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                return;
            }

            final MerchantData merchantData = DataHallMrg.getInstance().findMerchantData(this.getClass().getSimpleName(), player.getBusiness_no());
            if (merchantData == null) {
                res.setError(ErrorCode.Data_Error.getCode());
                MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                return;
            }

            final C_FirstDepositInviteBonus c_firstDepositInviteBonus = merchantData.findC_FirstDepositInviteBonus(this.getClass().getSimpleName(), ActivityMrg.FIRSTDEPOSIT_INIVTEBONUS);
            if (c_firstDepositInviteBonus == null) {
                res.setError(ErrorCode.Data_Error.getCode());
                MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                return;
            }

            final FirstDepositInviteBonusInfo firstDepositInviteBonusInfo = player.getFirstDepositInviteBonusInfo();
            final double totalValidBet = firstDepositInviteBonusInfo.getTotalValidWagered();

            final double charge = firstDepositInviteBonusInfo.getChargeMap()
                    .getOrDefault(firstDepositInviteBonusInfo.getCurrencyId(), 0d);
            final double process = BigDecimalUtils.div(totalValidBet, charge * c_firstDepositInviteBonus.getTurnoverMul(), 4);
            final List<C_FirstDepositInviteBonus.RewardInfo> rewardInfos = c_firstDepositInviteBonus.findRewardsList(firstDepositInviteBonusInfo.getReceiveBonus(), process);

            if (rewardInfos.isEmpty()) {
                res.setError(ErrorCode.LuckSpin_Not_Available.getCode());
                MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                return;
            }

            final RewardRequest rewardRequest = new RewardRequest();
            double bonus = 0;
            for (final C_FirstDepositInviteBonus.RewardInfo rewardInfo : rewardInfos) {
                firstDepositInviteBonusInfo.getReceiveBonus().add(rewardInfo.id);
                bonus = BigDecimalUtils.add(bonus, BigDecimalUtils.mul(charge, rewardInfo.getUnlockRate(), 4), 4);
            }

            rewardRequest.addCurrency(firstDepositInviteBonusInfo.getCurrencyId(), bonus);
            if (c_firstDepositInviteBonus.getRewardType() == 1) {//cash
                final RewardReason rewardReason = RewardReason.FirstDepositInviteBonus;
                CurrencyMrg.getInstance().rewardNoTest(player, rewardRequest.getCurrencyMap(), rewardReason);
            } else {//bonus
                final RewardReason rewardReason = RewardReason.FirstDepositInviteBonus;
                CurrencyMrg.getInstance().rewardNoTest(player, rewardRequest.getBonusCurrencyMap(), rewardReason);
            }

            //TODO 打码
//            final WithdrawStandard withdrawStandard = player.getWithdrawStandard(box.currencyId);
//            withdrawStandard.incDrawStandard(BigDecimalUtils.mul(box.amount, c_rewardBox.getTurnoverMul(), 4));
            final TurnoverReason turnoverReason = TurnoverReason.FirstDepositInviteBonus;
            final double finalBonus = bonus;
            ScriptLoader.getInstance().consumerScript("PlayerWithdrawStandardScript",
                    (IPlayerScript script) -> script.drawStandard(player, turnoverReason, firstDepositInviteBonusInfo.getCurrencyId(), finalBonus,
                            BigDecimalUtils.mul(finalBonus, c_firstDepositInviteBonus.getTurnoverMul(), 4)));

            res.setRewardShow(CommonMrg.buildDItemShow(firstDepositInviteBonusInfo.getCurrencyId(), finalBonus));

            if (!rewardRequest.getCurrencyMap().isEmpty()) {
                final Tuple2<Integer, Double> tuple2 = rewardRequest.getReward();
                ScriptLoader.getInstance().consumerScript("BonusScript",
                        (IBonusScript script) -> script.addBonusNote(TransactionFrom.FirstDepositInviteBonus, player, tuple2.getFirst(), tuple2.getSecond()));
            }

            EntityDaoMrg.getInstance().getDao(PlayerDao.class).firstDepositInviteBonusDao
                    .updateReceive(pid, firstDepositInviteBonusInfo);

            MsgUtil.responseHttp(res.build(), res.getMsgID(), session);

            //日志
            final GameLog playerActivityLog = new GameLog("platform_playerFirstDepositInviteBonusLog");
            playerActivityLog.append("number", GuidGeneratorUtils.generateOrderId())
                    .append("business_no", player.getBusiness_no())
                    .append("site", player.getWebSite())
                    .append("channel", player.getChannel())
                    .append("playerId", player.getPlayerId())
                    .append("playerName", player.getPlayerName())
                    .append("language", player.getLanguage())
                    .append("agentId", player.getAgentId())
                    .append("channelId", player.getChannelId())
                    .append("region", player.getRegisterRegion())
                    .append("activityId", ActivityMrg.FIRSTDEPOSIT_INIVTEBONUS)
                    .append("activityUniqueId", c_firstDepositInviteBonus.getC_id())
                    .append("currencyId", firstDepositInviteBonusInfo.getCurrencyId())
                    .append("reward", bonus)
                    .append("logTime", TimeUtil.currentTimeMillis());
            HallServer.getInstance().getLogProducerMrg().send(playerActivityLog);
        } catch (Exception e) {
            LOGGER.error("ReqReceiveDepositInviteBonusHandler", e);
            res.setError(ErrorCode.Internal_Server_Error.getCode());
            MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
        }
    }
}
