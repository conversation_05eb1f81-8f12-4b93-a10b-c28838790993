package com.game.handler.tcp.inner;

import com.game.dao.player.PlayerDao;
import com.game.engine.io.handler.TcpHandler;
import com.game.engine.script.IHandlerEntity;
import com.game.engine.script.ScriptLoader;
import com.game.entity.player.Player;
import com.game.entity.player.PlayerFields;
import com.game.entity.player.WithdrawStandard;
import com.game.entity.player.stats.Stats;
import com.game.enums.RewardReason;
import com.game.hall.mrg.InboxMrg;
import com.game.hall.mrg.currency.CurrencyMrg;
import com.game.hall.mrg.currency.RewardRequest;
import com.game.hall.mrg.player.PlayerMrg;
import com.game.hall.script.IInboxScript;
import com.game.manager.EntityDaoMrg;
import com.proto.InnerMessage;
import com.proto.MIDMessage;
import it.unimi.dsi.fastutil.ints.IntLists;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.mongodb.core.query.Update;

import java.util.Collections;

@IHandlerEntity(mid = MIDMessage.MID.InnerNotifyWithdraw_VALUE, msg = InnerMessage.InnerNotifyWithdrawMessage.class)
public class HallInnerNotifyWithdrawHandler extends TcpHandler {
    private static final Logger LOGGER = LoggerFactory.getLogger(HallInnerNotifyWithdrawHandler.class);

    @Override
    public void run() {
        final InnerMessage.InnerNotifyWithdrawMessage withDraw = (InnerMessage.InnerNotifyWithdrawMessage) getMessage();
        final int currencyId = withDraw.getCurrencyId();
        final double amount = withDraw.getAmount();
        final long orderId = withDraw.getOrderId();
        final int status = withDraw.getStatus();

        final Player player = PlayerMrg.getInstance().findDbPlayer(pid);
        if (player == null) {
            LOGGER.warn("playerId：{}，not exist", pid);
            return;
        }

        player.setWithdrawInProgress(0);
        final Update update = new Update();
        update.set(PlayerFields.withdrawInProgress, player.getWithdrawInProgress());
        EntityDaoMrg.getInstance().getDao(PlayerDao.class).updatePlayer(player.getPlayerId(), update);

        if (status == 3 || status == 4) {//失败
            final RewardRequest rewardRequest = new RewardRequest();
            final RewardReason rewardReason = RewardReason.Draw_fail;
            rewardReason.setSource(orderId + "");
            rewardRequest.addCurrency(currencyId, amount, false);
            CurrencyMrg.getInstance().rewardNoTest(player, rewardRequest.getCurrencyMap(), rewardReason);
            EntityDaoMrg.getInstance().getDao(PlayerDao.class)
                    .updateCurrency(player, IntLists.singleton(currencyId));

            //TODO 邮件通知
            ScriptLoader.getInstance().consumerScript("InboxScript",
                    (IInboxScript script) -> script.sendInboxMail(player, InboxMrg.WITHDRAW_REJECT, Collections.singletonList(player.getPlayerName())));
            return;
        }

        player.setDailyFeeTimes(player.getDailyFeeTimes() + 1);
        EntityDaoMrg.getInstance().getDao(PlayerDao.class)
                .updatePlayerField(pid, PlayerFields.dailyFeeTimes, player.getDailyFeeTimes());

        //TODO 统计提现
        final Stats stats = player.getStats(currencyId);
        stats.incWithdrawAmount(amount);
        EntityDaoMrg.getInstance().getDao(PlayerDao.class).statsDao
                .updateStats(player.getPlayerId(), player.getStatsInfo(), IntLists.singleton(currencyId));

        final WithdrawStandard withdrawStandard = player.getWithdrawStandard(currencyId);
        withdrawStandard.setTotalRecharge(0);
        withdrawStandard.resetTemp();
        EntityDaoMrg.getInstance().getDao(PlayerDao.class)
                .updateWithdrawStandard(player, IntLists.singleton(currencyId));

        //TODO 邮件通知
        ScriptLoader.getInstance().consumerScript("InboxScript",
                (IInboxScript script) -> script.sendInboxMail(player, InboxMrg.WITHDRAW_PASS, Collections.singletonList(player.getPlayerName())));

        ScriptLoader.getInstance().consumerScript("InboxScript",
                (IInboxScript script) -> script.sendEventPubMail(player, InboxMrg.WITHDRAW, true));
    }

}
