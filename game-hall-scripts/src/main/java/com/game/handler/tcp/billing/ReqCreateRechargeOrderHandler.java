package com.game.handler.tcp.billing;

import com.game.c_entity.merchant.C_PlatformRecharge;
import com.game.c_entity.merchant.C_RechargeWithdrawLimit;
import com.game.engine.io.handler.TcpHandler;
import com.game.engine.math.BigDecimalUtils;
import com.game.engine.script.IHandlerEntity;
import com.game.engine.utils.MsgUtil;
import com.game.entity.player.Player;
import com.game.entity.player.stats.Stats;
import com.game.enums.ErrorCode;
import com.game.hall.main.HallServer;
import com.game.hall.mrg.DataHallMrg;
import com.game.hall.mrg.MerchantData;
import com.game.hall.mrg.currency.CurrencyMrg;
import com.game.hall.mrg.player.PlayerMrg;
import com.game.manager.ServersMrg;
import com.proto.BillingMessage;
import com.proto.CommonMessage;
import com.proto.MIDMessage;
import io.netty.channel.Channel;
import io.netty.util.internal.StringUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Map;

/**
 * 目前创建订单只有法币
 */
@IHandlerEntity(mid = MIDMessage.MID.ReqCreateRechargeOrder_VALUE, msg = BillingMessage.ReqCreateRechargeOrderMessage.class)
public class ReqCreateRechargeOrderHandler extends TcpHandler {

    private static final Logger LOGGER = LoggerFactory.getLogger(ReqCreateRechargeOrderHandler.class);

    @Override
    public void run() {
        final BillingMessage.ResCreateRechargeOrderMessage.Builder res = BillingMessage.ResCreateRechargeOrderMessage.newBuilder();
        res.setMsgID(MIDMessage.MID.ResCreateRechargeOrder_VALUE);
        try {
            final Player player = PlayerMrg.getInstance().getOnlinePlayer(this.pid, this.getClass().getSimpleName());
            if (player == null) {
                res.setError(ErrorCode.Player_Offline.getCode());
                MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                return;
            }

            final BillingMessage.ReqCreateRechargeOrderMessage req = (BillingMessage.ReqCreateRechargeOrderMessage) getMessage();
            final int currencyId = req.getCurrencyId();
            final int channel = req.getChannel();
            final int paymentMethod = req.getPaymentMethod();
            final double amounts = req.getAmounts();

            if (player.isFreeze()) {
                res.setError(ErrorCode.Player_Freeze.getCode());
                MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                return;
            }

            if (player.isDepositLimit()) {
                res.setError(ErrorCode.DepositLimit.getCode());
                MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                return;
            }

            final MerchantData merchantData = DataHallMrg.getInstance().findMerchantData(this.getClass().getSimpleName(), player.getBusiness_no());
            if (merchantData == null) {
                res.setError(ErrorCode.Data_Error.getCode());
                MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                return;
            }

            final Map<String, C_PlatformRecharge> c_platformRechargeFiatMap = merchantData.findC_PlatformRechargeFiat(this.getClass().getSimpleName(), currencyId);
            if (c_platformRechargeFiatMap == null || c_platformRechargeFiatMap.isEmpty()) {
                LOGGER.warn("C_PlatformRecharge，currencyId：{}，not exits", currencyId);
                res.setError(ErrorCode.Data_Error.getCode());
                MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                return;
            }

            final C_PlatformRecharge c_platformRecharge = c_platformRechargeFiatMap.get(channel + "_" + paymentMethod);
            if (c_platformRecharge == null) {
                LOGGER.warn("C_PlatformRecharge，channel：{}，paymentMethod：{}，not exits", channel, paymentMethod);
                res.setError(ErrorCode.Data_Error.getCode());
                MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                return;
            }

            if (!c_platformRecharge.isOpen()) {
                res.setError(ErrorCode.Channel_Maintenance.getCode());
                MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                return;
            }

            if (amounts < c_platformRecharge.getSingleRechargeLowerLimit() || amounts > c_platformRecharge.getSingleRechargeUpperLimit()) {
                res.setError(ErrorCode.Recharge_Not_Within_The_Range.getCode());
                MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                return;
            }

            final C_RechargeWithdrawLimit c_limit = merchantData.findC_RechargeWithdrawLimit(this.getClass().getSimpleName(), currencyId);
            if (c_limit == null) {
                res.setError(ErrorCode.Data_Error.getCode());
                MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                return;
            }

            final Stats stats = player.getStats(currencyId);
            if (c_limit.getDailyRechargeTimes() > 0 && stats.getDailyRechargeTimes() >= c_limit.getDailyRechargeTimes()) {
                res.setError(ErrorCode.No_Recharge_Times.getCode());
                MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                return;
            }

            if (stats.getDailyRechargeAmount() + amounts > c_limit.getDailyRechargeUpperLimit()) {
                res.setError(ErrorCode.Recharge_UpperLimit.getCode());
                MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                return;
            }

            final double cash = CurrencyMrg.getInstance().getCurrencyValue(player, currencyId);
            final double bonus = CurrencyMrg.getInstance().getCurrencyValue(player, currencyId * 10);
            final double beforeBalance = BigDecimalUtils.add(cash, bonus, 9);

            final BillingMessage.ReqCreateRechargeOrderMessage.Builder reqBuilder = req.toBuilder();
            final BillingMessage.RechargeAccount rechargeAccount = req.getRechargeAccount();
            final BillingMessage.RechargeAccount.Builder rechargeAccountBuild = BillingMessage.RechargeAccount.newBuilder()
                    .setExtend0(rechargeAccount.getExtend0())
                    .setExtend1(rechargeAccount.getExtend1())
                    .setExtend2(rechargeAccount.getExtend2())
                    .setExtend3(rechargeAccount.getExtend3());
            reqBuilder.setRechargeAccount(rechargeAccountBuild.build())
                    .setPlayerInfo(buildPlayerInfo(player, stats))
                    .setFbInfo(buildFbInfo(player))
                    .setBeforeBalance(beforeBalance);

            final ServersMrg serversMrg = HallServer.getInstance().getHallTcpClient2Billing().getServersMrg();
            final Channel billingSession = serversMrg.getSessionByAccountId(pid);
            MsgUtil.sendInnerMsg(billingSession, reqBuilder.build(), pid, udpSessionId);
        } catch (Exception e) {
            LOGGER.error("ReqCreateRechargeOrderHandler", e);
            res.setError(ErrorCode.Internal_Server_Error.getCode());
            MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
        }
    }

    private CommonMessage.FbInfo buildFbInfo(Player player) {
        return CommonMessage.FbInfo.newBuilder()
                .setPixelId(StringUtil.isNullOrEmpty(player.getPixelId()) ? "" : player.getPixelId())
                .setFbToken(StringUtil.isNullOrEmpty(player.getFbToken()) ? "" : player.getFbToken())
                .build();
    }

    private CommonMessage.PlayerInfo buildPlayerInfo(Player player, Stats stats) {
        final CommonMessage.PlayerInfo.Builder playerInfo = CommonMessage.PlayerInfo.newBuilder();
        playerInfo.setBusinessNo(player.getBusiness_no())
                .setPlayerName(player.getPlayerName())
                .setSite(player.getWebSite())
                .setRegisterRegion(player.getRegisterRegion())
                .setAgentId(player.getAgentId())
                .setChannelId(player.getChannelId())
                .setMediaId(player.getMediaId())
                .setAdId(player.getAdId())
                .setChannel(player.getChannel())
                .addAllChannels(player.getChannels())
                .setHallId(player.getHallId())
                .setTotalBetTimes(stats.getTotalBetTimes())
                .setDevice(player.getDevice())
                .setModel(player.getModel());
        return playerInfo.build();
    }
}
