package com.game.handler.tcp.activity.luckSpin;

import com.game.c_entity.merchant.C_LuckSpin;
import com.game.dao.activity.SpinBonusNoteDao;
import com.game.dao.player.PlayerDao;
import com.game.engine.io.handler.TcpHandler;
import com.game.engine.io.redis.RedisPoolManager;
import com.game.engine.log.GameLog;
import com.game.engine.math.BigDecimalUtils;
import com.game.engine.script.IHandlerEntity;
import com.game.engine.script.ScriptLoader;
import com.game.engine.util.misc.Tuple2;
import com.game.engine.utils.GuidGeneratorUtils;
import com.game.engine.utils.MsgUtil;
import com.game.engine.utils.TimeUtil;
import com.game.engine.utils.WeightUtils;
import com.game.entity.activity.SpinBonusNote;
import com.game.entity.player.Player;
import com.game.entity.player.activity.luckSpin.*;
import com.game.entity.player.bonus.BonusDetailsInfo;
import com.game.entity.player.bonus.BonusInfo;
import com.game.entity.player.vip.VipClub;
import com.game.enums.*;
import com.game.enums.redis.RedisAllGame;
import com.game.enums.redis.RedisHall;
import com.game.hall.main.HallServer;
import com.game.hall.mrg.ActivityMrg;
import com.game.hall.mrg.DataHallMrg;
import com.game.hall.mrg.CommonMrg;
import com.game.hall.mrg.MerchantData;
import com.game.hall.mrg.currency.CurrencyMrg;
import com.game.hall.mrg.currency.RewardRequest;
import com.game.hall.mrg.player.PlayerMrg;
import com.game.hall.script.IBonusScript;
import com.game.hall.script.IPlayerScript;
import com.game.manager.EntityDaoMrg;
import com.game.utils.VirtualThreadUtils;
import com.proto.ActivityMessage;
import com.proto.MIDMessage;
import io.netty.util.internal.StringUtil;
import it.unimi.dsi.fastutil.ints.IntLists;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;
import java.util.Optional;

@IHandlerEntity(mid = MIDMessage.MID.ReqClickLuckSpin_VALUE, msg = ActivityMessage.ReqClickLuckSpinMessage.class)
public class ReqClickLuckSpinHandler extends TcpHandler {
    private static final Logger LOGGER = LoggerFactory.getLogger(ReqClickLuckSpinHandler.class);

    @Override
    public void run() {
        final ActivityMessage.ResClickLuckSpinMessage.Builder res = ActivityMessage.ResClickLuckSpinMessage.newBuilder();
        res.setMsgID(MIDMessage.MID.ResClickLuckSpin_VALUE);
        try {
            final Player player = PlayerMrg.getInstance().getOnlinePlayer(pid, this.getClass().getSimpleName());
            if (player == null) {
                res.setError(ErrorCode.Player_Offline.getCode());
                MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                return;
            }

            final ActivityMessage.ReqClickLuckSpinMessage req = (ActivityMessage.ReqClickLuckSpinMessage) getMessage();
            final int activityId = req.getActivityId();
            final int subType = req.getSubType();
            final MerchantData merchantData = DataHallMrg.getInstance().findMerchantData(this.getClass().getSimpleName(), player.getBusiness_no());
            if (merchantData == null) {
                res.setError(ErrorCode.Data_Error.getCode());
                MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                return;
            }
            final LuckSpinInfo luckSpinInfo = player.getLuckSpinInfo();
            final RewardRequest rewardRequest = new RewardRequest();
            switch (activityId) {
                case ActivityMrg.LUCK_SPIN_REFERRAL://1.邀请
                    final C_LuckSpin c_referralLuckSpin = merchantData.findC_LuckSpin(this.getClass().getSimpleName(), activityId);
                    if (c_referralLuckSpin == null) {
                        res.setError(ErrorCode.Data_Error.getCode());
                        MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                        return;
                    }

                    final LuckSpinData referralSpinData = luckSpinInfo.getLuckSpinData(c_referralLuckSpin.getActivityId());
                    if (referralSpinData.getExpiredTime() != -1 && TimeUtil.currentTimeMillis() > referralSpinData.getExpiredTime()) {
                        LOGGER.warn("playerId：{}，activityId：{}，activity end", pid, req.getActivityId());
                        res.setError(ErrorCode.Activity_Ended.getCode());
                        MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                        return;
                    }

                    final C_LuckSpin.InviteData inviteData = c_referralLuckSpin.getInviteInfo();
                    if (inviteData == null) {
                        res.setError(ErrorCode.Data_Error.getCode());
                        MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                        return;
                    }

                    final String remainTimes = RedisPoolManager.getInstance().function(jedis ->
                            jedis.sync().get(RedisHall.Platform_Role_Map_ReferralLuckSpin_RemainTimes.getKey(player.getPlayerId())));

                    if (Integer.parseInt(StringUtil.isNullOrEmpty(remainTimes) ? "0" : remainTimes) <= 0) {
                        LOGGER.warn("playerId：{}，activityId：{}，not remainFree", pid, req.getActivityId());
                        res.setError(ErrorCode.LuckSpin_Insufficient_Times.getCode());
                        MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                        return;
                    }

                    if (referralSpinData.getCurrentRewardsMap().getOrDefault(inviteData.getCurrencyId(), 0d) + inviteData.getInitReward() >= inviteData.getAvailableReward()) {
                        LOGGER.warn("playerId：{}，activityId：{}，conditions met", pid, req.getActivityId());
                        res.setError(ErrorCode.LuckSpin_ConditionsMet.getCode());
                        MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                        return;
                    }

//                    referralSpinData.incRemainTimes(-1);
                    int remain = Integer.parseInt(remainTimes);
                    remain -= 1;
                    if (remain <= 0) {
                        remain = 0;
                    }

                    RedisPoolManager.getInstance().executeAsync(commands ->
                            commands.incrby(RedisHall.Platform_Role_Map_ReferralLuckSpin_RemainTimes.getKey(player.getPlayerId()), -1)
                    );

                    double currentRewards = 0;
                    int turntableId = -1;
                    if (!referralSpinData.isFirst()) {
                        referralSpinData.setFirst(true);
                        currentRewards = inviteData.getInitReward();
                    } else {
                        final int inviteKey = WeightUtils.getWeightValue(c_referralLuckSpin.getWeightMap());
                        final C_LuckSpin.Turntable inviteTurntable = c_referralLuckSpin.getTurntableMap().get(inviteKey);
                        turntableId = inviteTurntable.getId();
                        referralSpinData.incCurrentRewards(inviteTurntable.getItemId(), inviteTurntable.getNum());

                        rewardRequest.addCurrency(inviteTurntable.getItemId(), inviteTurntable.getNum());
                        rewardRequest.writeToItemShowMsg(res::addShow);

                        currentRewards = referralSpinData.getCurrentRewardsMap().getOrDefault(inviteTurntable.getItemId(), 0d);
                        currentRewards += inviteData.getInitReward();
                        if (currentRewards >= inviteData.getAvailableReward()) {
                            currentRewards = inviteData.getAvailableReward();
                        }
                    }

                    res.setCurReward(CommonMrg.buildDItemShow(inviteData.getCurrencyId(), currentRewards))
                            .setRemainFreeTimes(remain)
                            .setTurntableId(turntableId);

                    final GameLog playerActivityLog = new GameLog("platform_playerLuckSpinRewardLog");
                    playerActivityLog.append("number", GuidGeneratorUtils.generateOrderId())
                            .append("business_no", player.getBusiness_no())
                            .append("site", player.getWebSite())
                            .append("channel", player.getChannel())
                            .append("playerId", player.getPlayerId())
                            .append("playerName", player.getPlayerName())
                            .append("language", player.getLanguage())
                            .append("agentId", player.getAgentId())
                            .append("channelId", player.getChannelId())
                            .append("region", player.getRegisterRegion())
                            .append("type", 2)//1.领取 2.参与
                            .append("activityId", c_referralLuckSpin.getActivityId())
                            .append("activityUniqueId", c_referralLuckSpin.getC_id())
                            .append("turntableType", c_referralLuckSpin.getTurntableType())//转盘类型 1.邀请转盘 2.vip转盘 3.每日 4.每周
                            .append("currencyId", 0)
                            .append("reward", 0)
                            .append("logTime", TimeUtil.currentTimeMillis());
                    HallServer.getInstance().getLogProducerMrg().send(playerActivityLog);
                    break;
                case ActivityMrg.LUCK_SPIN_VIP://vip
                    final List<C_LuckSpin> c_luckSpinList = merchantData.findC_VipSpinList(this.getClass().getSimpleName(), activityId);
                    if (c_luckSpinList.isEmpty()) {
                        res.setError(ErrorCode.Data_Error.getCode());
                        MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                        return;
                    }

                    final Optional<C_LuckSpin> optional = c_luckSpinList.stream().filter(luckSpin -> luckSpin.getSubType() == subType).findFirst();
                    if (optional.isEmpty()) {
                        res.setError(ErrorCode.Data_Error.getCode());
                        MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                        return;
                    }

                    final C_LuckSpin c_vipLuckSpin = optional.get();
                    final LuckSpinData luckSpinData = luckSpinInfo.getLuckSpinData(Integer.parseInt(c_vipLuckSpin.getActivityId() + "" + subType));
                    if (luckSpinData == null) {
                        res.setError(ErrorCode.Data_Error.getCode());
                        MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                        return;
                    }

                    if (luckSpinData.getExpiredTime() != -1 && TimeUtil.currentTimeMillis() > luckSpinData.getExpiredTime()) {
                        LOGGER.warn("playerId：{}，activityId：{}，activity end", pid, req.getActivityId());
                        res.setError(ErrorCode.Activity_Ended.getCode());
                        MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                        return;
                    }

                    final VipClub vipClub = player.getVipClub();
                    if (vipClub.getVipLevel() < c_vipLuckSpin.getVipLevel()) {
                        LOGGER.warn("playerId：{}，activityId：{}，vipLevel Insufficient", pid, req.getActivityId());
                        res.setError(ErrorCode.LuckSpin_VipLevel_Insufficient.getCode());
                        MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                        return;
                    }

                    if (luckSpinData.getRemainTimes() <= 0) {
                        LOGGER.warn("playerId：{}，activityId：{}，not remainFree", pid, req.getActivityId());
                        res.setError(ErrorCode.LuckSpin_Insufficient_Times.getCode());
                        MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                        return;
                    }

                    luckSpinData.incRemainTimes(-1);

                    final int vipKey = WeightUtils.getWeightValue(c_vipLuckSpin.getWeightMap());
                    final C_LuckSpin.Turntable vipTurntable = c_vipLuckSpin.getTurntableMap().get(vipKey);
                    rewardRequest.addCurrency(vipTurntable.getItemId(), vipTurntable.getNum());
                    rewardRequest.writeToItemShowMsg(res::addShow);

                    final RewardReason vipReason = RewardReason.LuckSpin;
                    vipReason.setSource(req.getActivityId() + "");
                    CurrencyMrg.getInstance().rewardNoTest(player, rewardRequest.getBonusCurrencyMap(), vipReason);

                    //TODO 计算打码量
//                    final WithdrawStandard vipWithdrawStandard = player.getWithdrawStandard(vipTurntable.getItemId());
//                    vipWithdrawStandard.incDrawStandard(BigDecimalUtils.mul(vipTurntable.getNum(), c_vipLuckSpin.getRewardTurnoverMul(), 4));
                {
                    final TurnoverReason turnoverReason = TurnoverReason.LuckSpin;
                    turnoverReason.setSource(req.getActivityId() + "");
                    ScriptLoader.getInstance().consumerScript("PlayerWithdrawStandardScript",
                            (IPlayerScript script) -> script.drawStandard(player, turnoverReason, vipTurntable.getItemId(), vipTurntable.getNum(),
                                    BigDecimalUtils.mul(vipTurntable.getNum(), c_vipLuckSpin.getRewardTurnoverMul(), 4)));
                }

                //TODO 统计
                final int turnTable = Integer.parseInt(c_vipLuckSpin.getTurntableType() + "" + c_vipLuckSpin.getSubType());
                RedisPoolManager.getInstance().executeAsync(commands ->
                        commands.hincrby(RedisAllGame.Platform_All_LuckSpin.getKey(player.getBusiness_no(), turnTable),
                                vipTurntable.getItemId() + "",
                                (long) BigDecimalUtils.mul(vipTurntable.getNum(), ActivityMrg.MULTIPLE))
                );

                res.setTurntableId(vipTurntable.getId())
                        .setRemainFreeTimes(luckSpinData.getRemainTimes());

                addSpinBonusNote(player, c_vipLuckSpin, vipTurntable, rewardRequest);
                break;
                case ActivityMrg.LUCK_SPIN_DAILY://每日
                case ActivityMrg.LUCK_SPIN_WEEKLY://每周
                    final C_LuckSpin c_luckSpin = merchantData.findC_LuckSpin(this.getClass().getSimpleName(), activityId);
                    if (c_luckSpin == null) {
                        res.setError(ErrorCode.Data_Error.getCode());
                        MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                        return;
                    }

                    if (TimeUtil.currentTimeMillis() > c_luckSpin.getEndTime()) {
                        LOGGER.warn("playerId：{}，activityId：{}，activity end", pid, req.getActivityId());
                        res.setError(ErrorCode.Activity_Ended.getCode());
                        MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                        return;
                    }

                    final LuckSpinData spinData = luckSpinInfo.getLuckSpinData(c_luckSpin.getActivityId());
                    if (spinData.getRemainTimes() <= 0) {
                        LOGGER.warn("playerId：{}，activityId：{}，not remainFree", pid, req.getActivityId());
                        res.setError(ErrorCode.LuckSpin_Insufficient_Times.getCode());
                        MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                        return;
                    }

                    spinData.incRemainTimes(-1);

                    final int key = WeightUtils.getWeightValue(c_luckSpin.getWeightMap());
                    final C_LuckSpin.Turntable dailyTurntable = c_luckSpin.getTurntableMap().get(key);
                    rewardRequest.addCurrency(dailyTurntable.getItemId(), dailyTurntable.getNum());
                    rewardRequest.writeToItemShowMsg(res::addShow);

                    final RewardReason rewardReason = RewardReason.LuckSpin;
                    rewardReason.setSource(req.getActivityId() + "");
                    CurrencyMrg.getInstance().rewardNoTest(player, rewardRequest.getBonusCurrencyMap(), rewardReason);

                    //TODO 计算打码量
//                    final WithdrawStandard dailyWithdrawStandard = player.getWithdrawStandard(dailyTurntable.getItemId());
//                    dailyWithdrawStandard.incDrawStandard(BigDecimalUtils.mul(dailyTurntable.getNum(), c_luckSpin.getRewardTurnoverMul(), 4));
                {
                    final TurnoverReason turnoverReason = TurnoverReason.LuckSpin;
                    turnoverReason.setSource(req.getActivityId() + "");
                    ScriptLoader.getInstance().consumerScript("PlayerWithdrawStandardScript",
                            (IPlayerScript script) -> script.drawStandard(player, turnoverReason, dailyTurntable.getItemId(), dailyTurntable.getNum(),
                                    BigDecimalUtils.mul(dailyTurntable.getNum(), c_luckSpin.getRewardTurnoverMul(), 4)));
                }

                //TODO 统计
                RedisPoolManager.getInstance().executeAsync(commands ->
                        commands.hincrby(RedisAllGame.Platform_All_LuckSpin.getKey(player.getBusiness_no(), c_luckSpin.getTurntableType()),
                                dailyTurntable.getItemId() + "",
                                (long) BigDecimalUtils.mul(dailyTurntable.getNum(), ActivityMrg.MULTIPLE))
                );

                res.setRemainFreeTimes(spinData.getRemainTimes())
                        .setTurntableId(dailyTurntable.getId());

                addSpinBonusNote(player, c_luckSpin, dailyTurntable, rewardRequest);
                break;
                default:
                    res.setError(ErrorCode.Data_Error.getCode());
                    MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                    break;
            }

            MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
        } catch (Exception e) {
            LOGGER.error("ReqClickLuckSpinHandler", e);
            res.setError(ErrorCode.Internal_Server_Error.getCode());
            MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
        }
    }

    private void addSpinBonusNote(Player player, C_LuckSpin c_luckSpin, C_LuckSpin.Turntable turntable, RewardRequest rewardRequest) {
        final SpinBonusNote spinBonusNote = new SpinBonusNote();
        spinBonusNote.setPlayerId(player.getPlayerId());
        spinBonusNote.setHeadId(player.getHeadId());
        spinBonusNote.setBusiness_no(player.getBusiness_no());
        spinBonusNote.setPlayerName(player.getPlayerName());
        spinBonusNote.setTurntable(c_luckSpin.getTurntableType());
        spinBonusNote.setSubType(c_luckSpin.getSubType());
        final C_LuckSpin.RuleData ruleData = c_luckSpin.getRuleDataMap().get(player.getLanguage());
        if (ruleData != null) {
            spinBonusNote.setSpin(ruleData.activityName);
        }
        spinBonusNote.setCurrencyId(turntable.getItemId());
        spinBonusNote.setPrize(turntable.getNum());
        EntityDaoMrg.getInstance().getDao(SpinBonusNoteDao.class).insert(spinBonusNote);

        if (!rewardRequest.getCurrencyMap().isEmpty()) {
            final Tuple2<Integer, Double> tuple2 = rewardRequest.getReward();
            final BonusInfo bonusInfo = player.getBonusInfo();
            final BonusDetailsInfo bonusDetailsInfo = bonusInfo.getBonusDetailsInfo(BonusDetail.LuckySpin.getType());
            bonusDetailsInfo.incBonus(tuple2.getFirst(), tuple2.getSecond());
            EntityDaoMrg.getInstance().getDao(PlayerDao.class).bonusInfoDao
                    .updateBonusDetails(player, BonusDetail.LuckySpin.getType(), bonusDetailsInfo, IntLists.singleton(tuple2.getFirst()));

            ScriptLoader.getInstance().consumerScript("BonusScript", (IBonusScript script) ->
                    script.addBonusNote(switchTransaction(c_luckSpin), player, tuple2.getFirst(), tuple2.getSecond()));

            final GameLog playerActivityLog = new GameLog("platform_playerLuckSpinRewardLog");
            playerActivityLog.append("number", GuidGeneratorUtils.generateOrderId())
                    .append("business_no", player.getBusiness_no())
                    .append("site", player.getWebSite())
                    .append("channel", player.getChannel())
                    .append("playerId", player.getPlayerId())
                    .append("playerName", player.getPlayerName())
                    .append("language", player.getLanguage())
                    .append("agentId", player.getAgentId())
                    .append("channelId", player.getChannelId())
                    .append("region", player.getRegisterRegion())
                    .append("type", 3)//1.领取 2.参与 3.参与并中奖
                    .append("activityId", c_luckSpin.getActivityId())
                    .append("activityUniqueId", c_luckSpin.getC_id())
                    .append("turntableType", c_luckSpin.getTurntableType())//转盘类型 1.邀请转盘 2.vip转盘 3.每日 4.每周
                    .append("currencyId", tuple2.getFirst())
                    .append("reward", tuple2.getSecond())
                    .append("logTime", TimeUtil.currentTimeMillis());
            HallServer.getInstance().getLogProducerMrg().send(playerActivityLog);
        }
    }

    private TransactionFrom switchTransaction(C_LuckSpin c_luckSpin) {
        TransactionFrom transaction;
        switch (c_luckSpin.getActivityId()) {
            case ActivityMrg.LUCK_SPIN_VIP:
                transaction = TransactionFrom.LuckSpin_Vip;
                break;
            case ActivityMrg.LUCK_SPIN_DAILY:
                transaction = TransactionFrom.LuckSpin_Daily;
                break;
            case ActivityMrg.LUCK_SPIN_WEEKLY:
                transaction = TransactionFrom.LuckSpin_Weekly;
                break;
            default:
                transaction = TransactionFrom.None;
                break;
        }
        return transaction;
    }

}
