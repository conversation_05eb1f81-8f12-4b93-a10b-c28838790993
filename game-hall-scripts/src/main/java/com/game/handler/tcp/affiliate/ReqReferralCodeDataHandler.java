package com.game.handler.tcp.affiliate;

import com.game.c_entity.merchant.C_CashBack;
import com.game.dao.game.GameNoteDao;
import com.game.dao.player.PlayerPromoteDao;
import com.game.engine.io.handler.TcpHandler;
import com.game.engine.script.IHandlerEntity;
import com.game.engine.script.ScriptLoader;
import com.game.engine.utils.MsgUtil;
import com.game.entity.game.GameCommissionStat;
import com.game.entity.game.GameNoteFields;
import com.game.entity.game.GameWagerStat;
import com.game.entity.player.Player;
import com.game.entity.player.promote.PlayerPromote;
import com.game.entity.player.promote.ReferralCode;
import com.game.enums.ErrorCode;
import com.game.hall.mrg.CommonMrg;
import com.game.hall.mrg.DataHallMrg;
import com.game.hall.mrg.MerchantData;
import com.game.hall.mrg.player.PlayerMrg;
import com.game.hall.script.IAffiliateScript;
import com.game.manager.EntityDaoMrg;
import com.proto.AffiliateMessage;
import com.proto.MIDMessage;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;
import java.util.Map;
import java.util.OptionalDouble;

@IHandlerEntity(mid = MIDMessage.MID.ReqReferralCodeData_VALUE, msg = AffiliateMessage.ReqReferralCodeDataMessage.class)
public class ReqReferralCodeDataHandler extends TcpHandler {
    private static final Logger LOGGER = LoggerFactory.getLogger(ReqReferralCodeDataHandler.class);

    @Override
    public void run() {
        final AffiliateMessage.ResReferralCodeDataMessage.Builder res = AffiliateMessage.ResReferralCodeDataMessage.newBuilder();
        res.setMsgID(MIDMessage.MID.ResReferralCodeData_VALUE);
        try {
            final Player player = PlayerMrg.getInstance().getOnlinePlayer(pid, this.getClass().getSimpleName());
            if (player == null) {
                res.setError(ErrorCode.Player_Offline.getCode());
                MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                return;
            }

            final MerchantData merchantData = DataHallMrg.getInstance().findMerchantData(this.getClass().getSimpleName(), player.getBusiness_no());
            if (merchantData == null) {
                res.setError(ErrorCode.Data_Error.getCode());
                MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                return;
            }

            final boolean isManyCurrency = merchantData.getC_currencyMap().size() > 1;

            final AffiliateMessage.ReqReferralCodeDataMessage req = (AffiliateMessage.ReqReferralCodeDataMessage) getMessage();
            final long wagerStart = req.getWagerStart();
            final long wagerEnd = req.getWagerEnd();
            final int page = req.getPage();
            final int pageSize = req.getPageSize();

            final OptionalDouble commissionRate = merchantData.getC_cashBackMap().values().stream()
                    .mapToDouble(C_CashBack::getSuperiorCashBackRate).max();

            final PlayerPromote playerPromote = player.getPlayerPromote();
            final Map<String, ReferralCode> referralCodeMap = playerPromote.getReferralCodeMap();
            final int count = referralCodeMap.size();
            final int skip = (page - 1) * pageSize;
            final List<ReferralCode> referralCodeList = referralCodeMap.values().stream().skip(skip).limit(pageSize).toList();
            for (ReferralCode referralCode : referralCodeList) {
                final List<Long> referrals = EntityDaoMrg.getInstance().getDao(PlayerPromoteDao.class)
                        .findByCodeFriends(referralCode.getCode());

                GameWagerStat gameWagerStat = null;
                if (isManyCurrency) {
                    gameWagerStat = EntityDaoMrg.getInstance().getDao(GameNoteDao.class).aggregateGameWagerStat(referrals, 0, GameNoteFields.usdValidBets, wagerStart, wagerEnd);
                } else {
                    gameWagerStat = EntityDaoMrg.getInstance().getDao(GameNoteDao.class).aggregateGameWagerStat(referrals, player.getCurrencyId(), GameNoteFields.validBets, wagerStart, wagerEnd);
                }

                final GameWagerStat finalGameWagerStat = gameWagerStat;
                final GameCommissionStat commissionStat = ScriptLoader.getInstance().functionScript("AffiliateScript",
                        (IAffiliateScript script) -> script.calculateSuperiorCommissionStat(finalGameWagerStat, merchantData));
                res.addReferralCode(buildReferralCode(referralCode, referrals.size(), commissionRate.isPresent() ? commissionRate.getAsDouble() : 0, gameWagerStat, commissionStat));
            }
            res.setPage(page)
                    .setPageSize(pageSize)
                    .setTotal(count)
                    .setTotalPage(CommonMrg.totalPage(count, pageSize));
            MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
        } catch (Exception e) {
            LOGGER.error("ReqReferralCodeDataHandler", e);
            res.setError(ErrorCode.Internal_Server_Error.getCode());
            MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
        }
    }

    /**
     * double totalWagerOriginal         = 7; //自研投注
     * double totalWager3rdParty         = 8; //三方投注
     * double totalWagerSports           = 9; //运动投注
     * double totalWager                 =10; //总投注金额
     * double totalCommissionOriginal    =11; //自研佣金
     * double totalCommission3rdParty    =12; //三方佣金
     * double totalCommissionSports      =13; //运动佣金
     * double totalCommissionReward      =14; //总佣金
     *
     * @param referralCode
     * @param referrals
     * @param gameWagerStat
     * @return
     */
    private AffiliateMessage.ReferralCode buildReferralCode(ReferralCode referralCode, int referrals, double commissionRate,
                                                            GameWagerStat gameWagerStat, GameCommissionStat commissionStat) {
        return AffiliateMessage.ReferralCode.newBuilder()
                .setName(referralCode.getName())
                .setCode(referralCode.getCode())
                .setLink(referralCode.getLink())
                .setCreateTime(referralCode.getCreatedDate())
                .setReferrals(referrals)
                .setCommissionRate(commissionRate)
                .setTotalWagerOriginal(gameWagerStat.getTotalWagerOriginal())
                .setTotalWager3RdParty(gameWagerStat.getTotalWager3rdParty())
                .setTotalWagerSports(gameWagerStat.getTotalWagerSports())
                .setTotalWager(gameWagerStat.getTotalWager())
                .setTotalCommissionOriginal(commissionStat.getTotalCommissionOriginal())
                .setTotalCommission3RdParty(commissionStat.getTotalCommission3rdParty())
                .setTotalCommissionSports(commissionStat.getTotalCommissionSports())
                .setTotalCommissionReward(commissionStat.getTotalCommissionReward())
                .build();
    }

}
