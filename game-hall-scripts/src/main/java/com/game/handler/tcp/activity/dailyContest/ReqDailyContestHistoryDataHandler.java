package com.game.handler.tcp.activity.dailyContest;

import com.game.c_entity.merchant.C_DailyContest;
import com.game.c_entity.middleplatform.C_BaseMerchant;
import com.game.engine.io.handler.TcpHandler;
import com.game.engine.io.redis.RedisPoolManager;
import com.game.engine.math.BigDecimalUtils;
import com.game.engine.script.IHandlerEntity;
import com.game.engine.utils.MsgUtil;
import com.game.engine.utils.TimeUtil;
import com.game.entity.player.Player;
import com.game.enums.ErrorCode;
import com.game.enums.redis.RedisAllGame;
import com.game.enums.redis.RedisRanking;
import com.game.hall.mrg.ActivityMrg;
import com.game.hall.mrg.DataHallMrg;
import com.game.hall.mrg.MerchantData;
import com.game.hall.mrg.player.PlayerMrg;
import com.proto.ActivityMessage;
import com.proto.MIDMessage;
import io.lettuce.core.ScoredValue;
import io.netty.util.internal.StringUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;

@IHandlerEntity(mid = MIDMessage.MID.ReqDailyContestHistoryData_VALUE, msg = ActivityMessage.ReqDailyContestHistoryDataMessage.class)
public class ReqDailyContestHistoryDataHandler extends TcpHandler {
    private static final Logger LOGGER = LoggerFactory.getLogger(ReqDailyContestHistoryDataHandler.class);

    @Override
    public void run() {
        final ActivityMessage.ResDailyContestHistoryDataMessage.Builder res = ActivityMessage.ResDailyContestHistoryDataMessage.newBuilder();
        res.setMsgID(MIDMessage.MID.ResDailyContestHistoryData_VALUE);
        try {
            final ActivityMessage.ReqDailyContestHistoryDataMessage req = (ActivityMessage.ReqDailyContestHistoryDataMessage) getMessage();
            final String host = req.getHost();

            final C_BaseMerchant c_baseHostMerchant = DataHallMrg.getInstance().findC_BaseHostMerchant(this.getClass().getSimpleName(), host);
            if (c_baseHostMerchant == null) {
                res.setError(ErrorCode.Data_Error.getCode());
                MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                return;
            }

            final String business_no = c_baseHostMerchant.getBusiness_no();
            final MerchantData merchantData = DataHallMrg.getInstance().findMerchantData(this.getClass().getSimpleName(), business_no);
            if (merchantData == null) {
                res.setError(ErrorCode.Data_Error.getCode());
                MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                return;
            }


            final C_DailyContest c_dailyContest = merchantData.findC_DailyContest(this.getClass().getSimpleName(), ActivityMrg.DAILY_CONTEST);
            if (c_dailyContest == null) {
                res.setError(ErrorCode.Data_Error.getCode());
                MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                return;
            }

            final String yesterday = TimeUtil.getDateTimeFormat(TimeUtil.currentTimeMillis() - TimeUtil.DAY, TimeUtil.YYYYMMDD);
            final String yesterdayPrizePools = RedisPoolManager.getInstance().function(jedis ->
                    jedis.sync().get(RedisAllGame.Platform_All_DailyContest_PrizePool.getKey(business_no, yesterday)));

            final double yesterdayPrizePool = BigDecimalUtils.div(Double.parseDouble(StringUtil.isNullOrEmpty(yesterdayPrizePools) ? "0" : yesterdayPrizePools), ActivityMrg.MULTIPLE, 4);
            final List<ScoredValue<String>> players = RedisPoolManager.getInstance().function(jedis ->
                    jedis.sync().zrevrangeWithScores(RedisRanking.RANKING_DAILY_CONTEST.getKey(business_no, yesterday), 0, 9));

            int ranking = 0;
            if (players != null) {
                for (ScoredValue<String> tuple : players) {
                    ++ranking;
                    final String element = tuple.getValue();
                    final double score = tuple.getScore();
                    final Player player = PlayerMrg.getInstance().findDbPlayer(Long.parseLong(element));
                    if (player == null) {
                        LOGGER.warn("playerId：{}，not exist", element);
                        continue;
                    }
                    final double prizeRate = c_dailyContest.getPrizeRate(ranking);
                    final double prize = BigDecimalUtils.mul(yesterdayPrizePool, prizeRate, 4);
                    res.addRankInfo(buildDailyRankInfo(player, ranking, score, prize, prizeRate));
                }
            }

            res.setStartDate(TimeUtil.currentTimeMillis() - 2 * TimeUtil.DAY)
                    .setEndDate(TimeUtil.currentTimeMillis() - TimeUtil.DAY);
            MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
        } catch (Exception e) {
            LOGGER.error("ReqDailyContestHistoryDataHandler", e);
            res.setError(ErrorCode.Internal_Server_Error.getCode());
            MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
        }
    }

    private ActivityMessage.DailyRankInfo buildDailyRankInfo(Player player, int ranking, double wagered, double prize, double prizeRate) {
        final ActivityMessage.DailyRankInfo.Builder dailyRankInfo = ActivityMessage.DailyRankInfo.newBuilder()
                .setRanking(ranking)
                .setHeadId(player.getHeadId())
                .setName(player.getPlayerName())
                .setWagered(wagered)
                .setPrize(prize)
                .setPrizeRate(prizeRate);
        return dailyRankInfo.build();
    }
}
