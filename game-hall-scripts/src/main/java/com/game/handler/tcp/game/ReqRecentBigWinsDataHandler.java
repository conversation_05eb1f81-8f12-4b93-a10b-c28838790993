package com.game.handler.tcp.game;

import com.game.c_entity.merchant.C_BigWin;
import com.game.c_entity.merchant.C_BigWinRobot;
import com.game.c_entity.merchant.C_GameApi;
import com.game.c_entity.middleplatform.C_BaseExchangeRate;
import com.game.c_entity.middleplatform.C_BaseMerchant;
import com.game.dao.game.GameNoteDao;
import com.game.engine.io.handler.TcpHandler;
import com.game.engine.math.BigDecimalUtils;
import com.game.engine.math.MathUtils;
import com.game.engine.script.IHandlerEntity;
import com.game.engine.script.ScriptLoader;
import com.game.engine.utils.MsgUtil;
import com.game.engine.utils.TimeUtil;
import com.game.entity.game.GameNote;
import com.game.enums.ErrorCode;
import com.game.enums.FunctionEnabled;
import com.game.hall.mrg.DataHallMrg;
import com.game.hall.mrg.MerchantData;
import com.game.hall.script.IFunctionEnabledScript;
import com.game.manager.EntityDaoMrg;
import com.proto.HallMessage;
import com.proto.MIDMessage;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.Iterator;
import java.util.List;

@IHandlerEntity(mid = MIDMessage.MID.ReqRecentBigWinsData_VALUE, msg = HallMessage.ReqRecentBigWinsDataMessage.class)
public class ReqRecentBigWinsDataHandler extends TcpHandler {
    private static final Logger LOGGER = LoggerFactory.getLogger(ReqRecentBigWinsDataHandler.class);

    @Override
    public void run() {
        final HallMessage.ResRecentBigWinsDataMessage.Builder res = HallMessage.ResRecentBigWinsDataMessage.newBuilder();
        res.setMsgID(MIDMessage.MID.ResRecentBigWinsData_VALUE);
        try {
            final HallMessage.ReqRecentBigWinsDataMessage req = (HallMessage.ReqRecentBigWinsDataMessage) getMessage();
            final int language = req.getLanguage();
            final String host = req.getHost();
            final C_BaseMerchant c_baseHostMerchant = DataHallMrg.getInstance().findC_BaseHostMerchant(this.getClass().getSimpleName(), host);
            if (c_baseHostMerchant == null) {
                res.setError(ErrorCode.Data_Error.getCode());
                MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                return;
            }

            final String business_no = c_baseHostMerchant.getBusiness_no();
            final boolean functionEnabled = ScriptLoader.getInstance().functionScript("FunctionEnabledScript", (IFunctionEnabledScript script)
                    -> script.functionEnabled(business_no, FunctionEnabled.BigWinner.getType()));
            if (!functionEnabled) {
                return;
            }

            final MerchantData merchantData = DataHallMrg.getInstance().findMerchantData(this.getClass().getSimpleName(), business_no);
            if (merchantData == null) {
                res.setError(ErrorCode.Data_Error.getCode());
                MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                return;
            }

            final int date = req.getDate();
            final int recentType = req.getRecentType();
            final List<GameNote> gameNoteList = new ArrayList<>();
            switch (date) {
                case 1://1d
                    gameNoteList.addAll(EntityDaoMrg.getInstance().getDao(GameNoteDao.class)
                            .loadBigWinGameNote(business_no, recentType, TimeUtil.currentTimeMillis() - TimeUtil.DAY, TimeUtil.currentTimeMillis(), 20));
                    break;
                case 2://3d
                    gameNoteList.addAll(EntityDaoMrg.getInstance().getDao(GameNoteDao.class)
                            .loadBigWinGameNote(business_no, recentType, TimeUtil.currentTimeMillis() - 3 * TimeUtil.DAY, TimeUtil.currentTimeMillis(), 20));
                    break;
                case 3://7d
                    gameNoteList.addAll(EntityDaoMrg.getInstance().getDao(GameNoteDao.class)
                            .loadBigWinGameNote(business_no, recentType, TimeUtil.currentTimeMillis() - 7 * TimeUtil.DAY, TimeUtil.currentTimeMillis(), 20));
                    break;
            }

            Iterator<GameNote> iterator = gameNoteList.iterator();
            while (iterator.hasNext()) {
                final GameNote gameNote = iterator.next();
                final C_BigWin c_bigWin = merchantData.getC_bigWinMap().get(gameNote.getGameId());
                if (c_bigWin == null) {
                    iterator.remove();
                    continue;
                }
                if (!c_bigWin.getCurrencys().contains(gameNote.getCurrencyId())) {
                    iterator.remove();
                    continue;
                }
                final boolean isMany = c_bigWin.getCurrencys().size() != 1;
                if (isMany) {
                    final C_BaseExchangeRate c_baseExchangeRate = DataHallMrg.getInstance().findC_BaseExchangeRate(this.getClass().getSimpleName(), gameNote.getCurrencyId());
                    if (c_baseExchangeRate != null) {
                        if (BigDecimalUtils.mul(gameNote.getWin(), c_baseExchangeRate.getExchangeRate(), 4) < c_bigWin.getLowAmount()) {
                            iterator.remove();
                        }
                    }
                } else {
                    if (gameNote.getWin() < c_bigWin.getLowAmount()) {
                        iterator.remove();
                    }
                }
            }

            final List<C_BigWinRobot> c_bigWinRobots = merchantData.loadBigWinRobot(business_no, recentType, 20 - gameNoteList.size());
            for (C_BigWinRobot c_bigWinRobot : c_bigWinRobots) {
                final GameNote gameNote = getGameNote(c_bigWinRobot);
                gameNoteList.add(gameNote);
            }

            final List<GameNote> updataGameList = gameNoteList.stream().sorted(
                    Comparator.comparingLong(GameNote::getCreateTime).reversed()//降序
            ).toList();

            for (GameNote gameNote : updataGameList) {
                final C_GameApi c_gameApi = merchantData.findC_GameApi(this.getClass().getSimpleName(), gameNote.getGameId());
                if (c_gameApi != null && c_gameApi.setLanguageGameApiData(language)) {
                    res.addBigWinsList(buildBigWinsInfo(gameNote, c_gameApi));
                }
            }

            MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
        } catch (Exception e) {
            LOGGER.error("ReqRecentBigWinsDataHandler", e);
            res.setError(ErrorCode.Internal_Server_Error.getCode());
            MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
        }
    }

    private static GameNote getGameNote(C_BigWinRobot c_bigWinRobot) {
        final GameNote gameNote = new GameNote();
        gameNote.setGameId(c_bigWinRobot.getGameId());
        gameNote.setHeadId(MathUtils.random(1000, 1015) + "");
        gameNote.setCurrencyId(c_bigWinRobot.getCurrencyId());
        gameNote.setWin(c_bigWinRobot.getAmount());
        gameNote.setPlayerName(c_bigWinRobot.getPlayerName());
        gameNote.setPlatformId(c_bigWinRobot.getPlatformId());
        gameNote.setGameType(c_bigWinRobot.getGameType());
        gameNote.setCreateTime(c_bigWinRobot.getCreateTime());
        return gameNote;
    }

    /**
     * int32 gameId                       = 1; //游戏id
     * string gameIcon                    = 2; //游戏icon
     * int32 currencyId                   = 3; //货币id
     * double amount                      = 4; //金额
     * string playerName                  = 5; //玩家名字
     * int32 platformId                   = 6; //平台id
     * string platformName                = 7; //平台名字
     * int32 gameType                     = 8; //游戏类型
     * string gameName                    = 9; //游戏名字
     *
     * @param gameNote
     * @return
     */
    private HallMessage.BigWinsInfo buildBigWinsInfo(GameNote gameNote, C_GameApi c_gameApi) {
        return HallMessage.BigWinsInfo.newBuilder()
                .setGameIcon(c_gameApi.getFileUrl())
                .setGameName(c_gameApi.getGameName())
                .setPlatformName(c_gameApi.getPlatformName())
                .setGameId(gameNote.getGameId())
                .setCurrencyId(gameNote.getCurrencyId())
                .setAmount(gameNote.getWin())
                .setPlayerName(gameNote.getPlayerName())
                .setPlatformId(gameNote.getPlatformId())
                .setGameType(gameNote.getGameType())
                .setHeadId(gameNote.getHeadId())
                .build();
    }
}
