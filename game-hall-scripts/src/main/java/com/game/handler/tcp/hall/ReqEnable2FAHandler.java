package com.game.handler.tcp.hall;

import com.game.dao.player.PlayerDao;
import com.game.engine.io.handler.TcpHandler;
import com.game.engine.script.IHandlerEntity;
import com.game.engine.utils.MsgUtil;
import com.game.entity.player.Player;
import com.game.entity.player.PlayerFields;
import com.game.enums.ErrorCode;
import com.game.hall.mrg.player.PlayerMrg;
import com.game.manager.EntityDaoMrg;
import com.proto.HallMessage;
import com.proto.MIDMessage;
import com.warrenstrange.googleauth.*;
import io.netty.util.internal.StringUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.mongodb.core.query.Update;

@IHandlerEntity(mid = MIDMessage.MID.ReqEnable2FAData_VALUE, msg = HallMessage.ReqEnable2FADataMessage.class)
public class ReqEnable2FAHandler extends TcpHandler {
    private static final Logger LOGGER = LoggerFactory.getLogger(ReqEnable2FAHandler.class);

    @Override
    public void run() {
        final HallMessage.ResEnable2FADataMessage.Builder res = HallMessage.ResEnable2FADataMessage.newBuilder();
        res.setMsgID(MIDMessage.MID.ResEnable2FAData_VALUE);

        try {
            final Player player = PlayerMrg.getInstance().getOnlinePlayer(pid, this.getClass().getSimpleName());
            if (player == null) {
                res.setError(ErrorCode.Player_Offline.getCode());
                MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                return;
            }

            if (player.isVerify2FA()) {
                res.setError(ErrorCode.GoogleCertified_Error.getCode());
                MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                return;
            }


            if (StringUtil.isNullOrEmpty(player.getSecretKey()) || StringUtil.isNullOrEmpty(player.getQrCode())) {
                final GoogleAuthenticatorConfig config = new GoogleAuthenticatorConfig
                        .GoogleAuthenticatorConfigBuilder()
                        .setSecretBits(160)
                        .setKeyRepresentation(KeyRepresentation.BASE32)
                        .build();
                final GoogleAuthenticator gAuth = new GoogleAuthenticator(config);
                final GoogleAuthenticatorKey key = gAuth.createCredentials();

                final String secretKey = key.getKey().toLowerCase();
                final String qrCode = GoogleAuthenticatorQRGenerator
                        .getOtpAuthURL("WGS", player.getPlayerName(), key);

                player.setSecretKey(secretKey);
                player.setQrCode(qrCode);
                final Update update = new Update();
                update.set(PlayerFields.secretKey, secretKey)
                        .set(PlayerFields.qrCode, qrCode);
                EntityDaoMrg.getInstance().getDao(PlayerDao.class)
                        .updatePlayer(pid, update);
            }

            res.setSecretKey(player.getSecretKey())
                    .setQrCode(player.getQrCode());
            MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
        } catch (Exception e) {
            LOGGER.error("ReqEnable2FADataHandler", e);
            res.setError(ErrorCode.Internal_Server_Error.getCode());
            MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
        }

    }

}
