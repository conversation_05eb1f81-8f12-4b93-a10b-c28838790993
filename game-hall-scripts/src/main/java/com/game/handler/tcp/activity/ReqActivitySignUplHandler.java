package com.game.handler.tcp.activity;

import com.game.c_entity.merchant.C_Activity;
import com.game.dao.player.PlayerDao;
import com.game.engine.io.handler.TcpHandler;
import com.game.engine.log.GameLog;
import com.game.engine.script.IHandlerEntity;
import com.game.engine.script.ScriptLoader;
import com.game.engine.utils.GuidGeneratorUtils;
import com.game.engine.utils.MsgUtil;
import com.game.engine.utils.TimeUtil;
import com.game.entity.player.Player;
import com.game.entity.player.activity.ActivityData;
import com.game.entity.player.activity.ActivityInfo;
import com.game.enums.ErrorCode;
import com.game.hall.main.HallServer;
import com.game.hall.mrg.DataHallMrg;
import com.game.hall.mrg.MerchantData;
import com.game.hall.mrg.player.PlayerMrg;
import com.game.hall.script.activity.IActivityScript;
import com.game.manager.EntityDaoMrg;
import com.proto.ActivityMessage;
import com.proto.MIDMessage;
import io.netty.util.internal.StringUtil;
import it.unimi.dsi.fastutil.ints.IntLists;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;


@IHandlerEntity(mid = MIDMessage.MID.ReqActivitySignUp_VALUE, msg = ActivityMessage.ReqActivitySignUpMessage.class)
public class ReqActivitySignUplHandler extends TcpHandler {
    private static final Logger LOGGER = LoggerFactory.getLogger(ReqActivitySignUplHandler.class);

    @Override
    public void run() {
        final ActivityMessage.ResActivitySignUpMessage.Builder res = ActivityMessage.ResActivitySignUpMessage.newBuilder();
        res.setMsgID(MIDMessage.MID.ResActivitySignUp_VALUE);
        try {
            final Player player = PlayerMrg.getInstance().getOnlinePlayer(pid, this.getClass().getSimpleName());
            if (player == null) {
                res.setError(ErrorCode.Player_Offline.getCode());
                MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                return;
            }

            final ActivityMessage.ReqActivitySignUpMessage req = (ActivityMessage.ReqActivitySignUpMessage) getMessage();
            final int cId = req.getCId();

            final MerchantData merchantData = DataHallMrg.getInstance().findMerchantData(this.getClass().getSimpleName(), player.getBusiness_no());
            if (merchantData == null) {
                res.setError(ErrorCode.Data_Error.getCode());
                MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                return;
            }

            final C_Activity c_activity = merchantData.findByIdC_Activity(this.getClass().getSimpleName(), cId);
            if (c_activity == null) {
                res.setError(ErrorCode.Data_Error.getCode());
                MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                return;
            }

            if (TimeUtil.currentTimeMillis() < c_activity.getStartSignUpTime() || TimeUtil.currentTimeMillis() > c_activity.getEndSignUpTime()) {
                res.setError(ErrorCode.Activity_SignUpNotStart.getCode());
                MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                return;
            }

            final ActivityInfo activityInfo = player.getActivityInfo();
            final int activityId = Integer.parseInt(c_activity.getActivityId() + "" + c_activity.getActivitySubType());
            ActivityData activityData = activityInfo.getActivityData(activityId);
            if (activityData == null) {
                activityData = new ActivityData();
                activityInfo.getActivityDataMap().put(activityId, activityData);
            }
            activityData.getSignUpMap().put(c_activity.getActivitySubType(), 1);

            MsgUtil.responseHttp(res.build(), res.getMsgID(), session);

            //TODO 活动日志
            ScriptLoader.getInstance().consumerScript("ActivityScript",
                    (IActivityScript script) -> script.sendActivityLog(player, 1, c_activity, null));
        } catch (Exception e) {
            LOGGER.error("ReqActivitySignUplHandler", e);
            res.setError(ErrorCode.Internal_Server_Error.getCode());
            MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
        }
    }
}
