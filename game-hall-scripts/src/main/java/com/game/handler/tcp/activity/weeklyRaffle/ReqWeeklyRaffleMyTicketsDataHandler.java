package com.game.handler.tcp.activity.weeklyRaffle;

import com.game.c_entity.middleplatform.C_BaseExchangeRate;
import com.game.dao.activity.TicketsNoteDao;
import com.game.dao.activity.WinTicketsNoteDao;
import com.game.engine.io.handler.TcpHandler;
import com.game.engine.math.BigDecimalUtils;
import com.game.engine.script.IHandlerEntity;
import com.game.engine.util.misc.Tuple2;
import com.game.engine.utils.MsgUtil;
import com.game.entity.activity.TicketsNote;
import com.game.entity.activity.WinTicketsNote;
import com.game.entity.player.Player;
import com.game.entity.player.activity.weeklyRaffle.WeeklyRaffleInfo;
import com.game.enums.Currency;
import com.game.enums.ErrorCode;
import com.game.hall.mrg.ActivityMrg;
import com.game.hall.mrg.CommonMrg;
import com.game.hall.mrg.DataHallMrg;
import com.game.hall.mrg.MerchantData;
import com.game.hall.mrg.player.PlayerMrg;
import com.game.manager.EntityDaoMrg;
import com.proto.ActivityMessage;
import com.proto.MIDMessage;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;
import java.util.Map;

@IHandlerEntity(mid = MIDMessage.MID.ReqWeeklyRaffleMyTicketsData_VALUE, msg = ActivityMessage.ReqWeeklyRaffleMyTicketsDataMessage.class)
public class ReqWeeklyRaffleMyTicketsDataHandler extends TcpHandler {
    private static final Logger LOGGER = LoggerFactory.getLogger(ReqWeeklyRaffleMyTicketsDataHandler.class);

    @Override
    public void run() {
        final ActivityMessage.ResWeeklyRaffleMyTicketsDataMessage.Builder res = ActivityMessage.ResWeeklyRaffleMyTicketsDataMessage.newBuilder();
        res.setMsgID(MIDMessage.MID.ResWeeklyRaffleMyTicketsData_VALUE);
        try {
            final Player player = PlayerMrg.getInstance().getOnlinePlayer(pid, this.getClass().getSimpleName());
            if (player == null) {
                res.setError(ErrorCode.Player_Offline.getCode());
                MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                return;
            }

            final MerchantData merchantData = DataHallMrg.getInstance().findMerchantData(this.getClass().getSimpleName(), player.getBusiness_no());
            if (merchantData == null) {
                res.setError(ErrorCode.Data_Error.getCode());
                MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                return;
            }

            final WeeklyRaffleInfo weeklyRaffleInfo = player.getWeeklyRaffleInfo();
            final ActivityMessage.ReqWeeklyRaffleMyTicketsDataMessage req = (ActivityMessage.ReqWeeklyRaffleMyTicketsDataMessage) getMessage();
            final int type = req.getType();//
            final String gameId = req.getGameId();
            switch (type) {
                case 1://1.active
                    for (TicketsNote ticketsNote : weeklyRaffleInfo.getActiveTicketsMap().values()) {
                        res.addActiveList(buildTicketsInfo(ticketsNote));
                    }
                    break;
                case 2://2.past
                    final Tuple2<Integer, List<TicketsNote>> tuple2 = EntityDaoMrg.getInstance().getDao(TicketsNoteDao.class).loadByPlayerIdTicketsNote(player.getPlayerId(), gameId, 0, 100);
                    for (TicketsNote ticketsNote : tuple2.getSecond()) {
                        res.addPastLists(buildTicketsInfo(ticketsNote));
                    }
                    break;
                case 3://3.myWinnings
                    final List<WinTicketsNote> winTicketsNotes = EntityDaoMrg.getInstance().getDao(WinTicketsNoteDao.class).loadByPlayerIdWinTicketsNote(player.getBusiness_no(), pid);
                    for (WinTicketsNote winTicketsNote : winTicketsNotes) {
                        res.addMyWinningList(ActivityMrg.buildTicketsInfo(winTicketsNote));
                    }
                    break;
            }

            res.setTotalTickets(weeklyRaffleInfo.getTotalTickets())
                    .setTotalWinTickets(weeklyRaffleInfo.getTotalWinTickets());

            final boolean isManyCurrency = merchantData.getC_currencyMap().size() > 1;

            double totalPrize = 0;
            for (Map.Entry<Integer, Double> entry : weeklyRaffleInfo.getTotalPrizeMap().entrySet()) {
                final int currencyId = entry.getKey();
                final double prize = entry.getValue();
                if (isManyCurrency) {
                    final C_BaseExchangeRate c_baseExchangeRate = DataHallMrg.getInstance().findC_BaseExchangeRate(this.getClass().getSimpleName(), currencyId);
                    if (c_baseExchangeRate == null) {
                        continue;
                    }
                    totalPrize = BigDecimalUtils.add(totalPrize, BigDecimalUtils.mul(prize, c_baseExchangeRate.getExchangeRate(), 9), 9);
                } else {
                    totalPrize = BigDecimalUtils.add(totalPrize, prize, 9);
                }
            }
            res.setTotalPrize(CommonMrg.buildDItemShow(isManyCurrency ? Currency.USD.getCurrencyId() : player.getCurrencyId(), totalPrize));
            MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
        } catch (Exception e) {
            LOGGER.error("ReqWeeklyRaffleMyTicketsDataHandler", e);
            res.setError(ErrorCode.Internal_Server_Error.getCode());
            MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
        }
    }

    private ActivityMessage.TicketsInfo buildTicketsInfo(TicketsNote winTicketsNote) {
        return ActivityMessage.TicketsInfo.newBuilder()
                .setGameId(winTicketsNote.getGameId())
                .setTicketNumbers(winTicketsNote.getTicketNumbers())
                .setCurrencyId(winTicketsNote.getCurrencyId())
                .setPlayerName(winTicketsNote.getPlayerName())
                .setHeadId(winTicketsNote.getHeadId())
                .build();
    }

}
