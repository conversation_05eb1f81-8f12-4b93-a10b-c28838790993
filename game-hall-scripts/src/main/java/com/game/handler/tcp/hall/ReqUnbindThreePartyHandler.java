package com.game.handler.tcp.hall;

import com.game.dao.account.AccountDao;
import com.game.dao.player.PlayerDao;
import com.game.engine.io.handler.TcpHandler;
import com.game.engine.io.redis.RedisPoolManager;
import com.game.engine.script.IHandlerEntity;
import com.game.engine.utils.MsgUtil;
import com.game.entity.account.Account;
import com.game.entity.account.ThreePartyInfo;
import com.game.entity.player.Player;
import com.game.enums.ErrorCode;
import com.game.enums.ThreeParty;
import com.game.enums.VerifyCode;
import com.game.enums.redis.RedisLogin;
import com.game.hall.mrg.player.PlayerMrg;
import com.game.manager.EntityDaoMrg;
import com.game.utils.VirtualThreadUtils;
import com.proto.HallMessage;
import com.proto.MIDMessage;
import io.netty.util.internal.StringUtil;
import it.unimi.dsi.fastutil.ints.IntLists;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@IHandlerEntity(mid = MIDMessage.MID.ReqUnbindThreeParty_VALUE, msg = HallMessage.ReqUnbindThreePartyMessage.class)
public class ReqUnbindThreePartyHandler extends TcpHandler {
    private static final Logger LOGGER = LoggerFactory.getLogger(ReqUnbindThreePartyHandler.class);

    @Override
    public void run() {
        final HallMessage.ResUnbindThreePartyMessage.Builder res = HallMessage.ResUnbindThreePartyMessage.newBuilder();
        res.setMsgID(MIDMessage.MID.ResUnbindThreeParty_VALUE);
        try {
            final Player player = PlayerMrg.getInstance().getOnlinePlayer(pid, this.getClass().getSimpleName());
            if (player == null) {
                res.setError(ErrorCode.Player_Offline.getCode());
                MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                return;
            }

            final HallMessage.ReqUnbindThreePartyMessage req = (HallMessage.ReqUnbindThreePartyMessage) getMessage();
            final int threeParty = req.getThreeParty();
            final String account = req.getAccount();
            final String verifyCode = req.getVerificationCode();
            switch (req.getChoose()) {
                case 1://邮件
                    if (!player.isEmailBind()) {
                        LOGGER.warn("email：{}，not bind", player.getEmail());
                        res.setError(ErrorCode.Mail_NotBind.getCode());
                        MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                        return;
                    }

                    final String code = RedisPoolManager.getInstance().function(jedis ->
                            jedis.sync().get(RedisLogin.Platform_LG_Account_VerifyCode.getKey(player.getBusiness_no(), VerifyCode.unbind.getType(), account)));
                    if (StringUtil.isNullOrEmpty(verifyCode) || !verifyCode.equals(code)) {
                        LOGGER.warn("email：{}，verifyCode，{}-{}，", account, code, verifyCode);
                        res.setError(ErrorCode.VerifyCode_Error.getCode());
                        MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                        return;
                    }
                    break;
            }

            final ThreePartyInfo threePartyInfo = player.removeThreePartyInfo(threeParty);
            EntityDaoMrg.getInstance().getDao(PlayerDao.class)
                    .updateDeleteThreePartyInfo(player, IntLists.singleton(threeParty));

            final Account ac = PlayerMrg.getInstance().findDbAccount(pid);
            ac.removeThreePartyInfo(threeParty);

            String threePartyId;
            if (threeParty == ThreeParty.Email.getThreeParty()) {
                threePartyId = threePartyInfo.getAccount();
            } else {
                threePartyId = threePartyInfo.getThreePartyId();
            }
            EntityDaoMrg.getInstance().getDao(AccountDao.class).updateDeleteThreePartyInfo(ac, IntLists.singleton(threeParty));

            RedisPoolManager.getInstance().executeAsync(commands ->
                    commands.hdel(RedisLogin.Platform_LG_Map_AccountID.getKey(player.getBusiness_no(), threeParty), threePartyId)
            );

            res.setThreeParty(threeParty);
            MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
        } catch (Exception e) {
            LOGGER.error("ReqUnbindThreePartyHandler", e);
            res.setError(ErrorCode.Internal_Server_Error.getCode());
            MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
        }
    }
}
