package com.game.handler.tcp.activity.mysteryBonus;

import com.game.c_entity.merchant.C_MysteryBonus;
import com.game.dao.player.PlayerDao;
import com.game.engine.io.handler.TcpHandler;
import com.game.engine.log.GameLog;
import com.game.engine.math.BigDecimalUtils;
import com.game.engine.script.IHandlerEntity;
import com.game.engine.script.ScriptLoader;
import com.game.engine.util.misc.Tuple2;
import com.game.engine.utils.GuidGeneratorUtils;
import com.game.engine.utils.MsgUtil;
import com.game.engine.utils.TimeUtil;
import com.game.entity.player.Player;
import com.game.entity.player.activity.mysteryBonus.MysteryBonusInfo;
import com.game.entity.player.activity.mysteryBonus.MysteryRewardInfo;
import com.game.enums.ErrorCode;
import com.game.enums.RewardReason;
import com.game.enums.TransactionFrom;
import com.game.enums.TurnoverReason;
import com.game.hall.main.HallServer;
import com.game.hall.mrg.ActivityMrg;
import com.game.hall.mrg.CommonMrg;
import com.game.hall.mrg.DataHallMrg;
import com.game.hall.mrg.MerchantData;
import com.game.hall.mrg.currency.CurrencyMrg;
import com.game.hall.mrg.currency.RewardRequest;
import com.game.hall.mrg.player.PlayerMrg;
import com.game.hall.script.IBonusScript;
import com.game.hall.script.IPlayerScript;
import com.game.manager.EntityDaoMrg;
import com.proto.ActivityMessage;
import com.proto.MIDMessage;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@IHandlerEntity(mid = MIDMessage.MID.ReqReceiveMysteryBonus_VALUE, msg = ActivityMessage.ReqReceiveMysteryBonusMessage.class)
public class ReqReceiveMysteryBonusHandler extends TcpHandler {
    private static final Logger LOGGER = LoggerFactory.getLogger(ReqReceiveMysteryBonusHandler.class);

    @Override
    public void run() {
        final ActivityMessage.ResReceiveMysteryBonusMessage.Builder res = ActivityMessage.ResReceiveMysteryBonusMessage.newBuilder();
        res.setMsgID(MIDMessage.MID.ResReceiveMysteryBonus_VALUE);
        try {
            final ActivityMessage.ReqReceiveMysteryBonusMessage req = (ActivityMessage.ReqReceiveMysteryBonusMessage) getMessage();
            final int day = req.getDay();

            final Player player = PlayerMrg.getInstance().getOnlinePlayer(pid, this.getClass().getSimpleName());
            if (player == null) {
                res.setError(ErrorCode.Player_Offline.getCode());
                MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                return;
            }

            final MerchantData merchantData = DataHallMrg.getInstance().findMerchantData(this.getClass().getSimpleName(), player.getBusiness_no());
            if (merchantData == null) {
                res.setError(ErrorCode.Data_Error.getCode());
                MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                return;
            }

            final C_MysteryBonus c_mysteryBonus = merchantData.findC_MysteryBonus(this.getClass().getSimpleName(), ActivityMrg.MYSTERY_BONUS);
            if (c_mysteryBonus == null) {
                res.setError(ErrorCode.Data_Error.getCode());
                MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                return;
            }

            final MysteryBonusInfo mysteryBonusInfo = player.getMysteryBonusInfo();
            final MysteryRewardInfo mysteryRewardInfo = mysteryBonusInfo.getMysteryRewardInfoMap().get(day);
            if (mysteryRewardInfo == null) {
                res.setError(ErrorCode.Data_Error.getCode());
                MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                return;
            }

            if (TimeUtil.currentTimeMillis() < mysteryRewardInfo.getSettlementTime()) {
                res.setError(ErrorCode.Not_Yet_Received.getCode());
                MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                return;
            }

            if (TimeUtil.currentTimeMillis() >= mysteryRewardInfo.getExpiredTime()) {
                res.setError(ErrorCode.Reward_Has_Expired.getCode());
                MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                return;
            }

            if (mysteryBonusInfo.getReceive().contains(day)) {
                res.setError(ErrorCode.Reward_Received.getCode());
                MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                return;
            }

            mysteryBonusInfo.getReceive().add(day);
            EntityDaoMrg.getInstance().getDao(PlayerDao.class)
                    .mysteryBonusDao.updateReceive(player.getPlayerId(), mysteryBonusInfo);

            final RewardRequest rewardRequest = new RewardRequest();
            rewardRequest.addCurrency(c_mysteryBonus.getCurrencyId(), mysteryRewardInfo.getBonus());
            final RewardReason rewardReason = RewardReason.MysteryBonus;
            rewardReason.setSource(day + "");
            CurrencyMrg.getInstance().rewardNoTest(player, rewardRequest.getBonusCurrencyMap(), rewardReason);

            //TODO 打码
//            final WithdrawStandard withdrawStandard = player.getWithdrawStandard(c_mysteryBonus.getCurrencyId());
//            withdrawStandard.incDrawStandard(BigDecimalUtils.mul(mysteryRewardInfo.getBonus(), c_mysteryBonus.getTurnoverMul(), 4));
            final TurnoverReason turnoverReason = TurnoverReason.MysteryBonus;
            turnoverReason.setSource(day + "");
            ScriptLoader.getInstance().consumerScript("PlayerWithdrawStandardScript",
                    (IPlayerScript script) -> script.drawStandard(player, turnoverReason, c_mysteryBonus.getCurrencyId(), mysteryRewardInfo.getBonus(),
                            BigDecimalUtils.mul(mysteryRewardInfo.getBonus(), c_mysteryBonus.getTurnoverMul(), 4)));

            if (!rewardRequest.getCurrencyMap().isEmpty()) {
                final Tuple2<Integer, Double> tuple2 = rewardRequest.getReward();
                ScriptLoader.getInstance().consumerScript("BonusScript",
                        (IBonusScript script) -> script.addBonusNote(TransactionFrom.MysteryBonus, player, tuple2.getFirst(), tuple2.getSecond()));
            }

            res.setRewardShow(CommonMrg.buildDItemShow(c_mysteryBonus.getCurrencyId(), mysteryRewardInfo.getBonus()));
            MsgUtil.responseHttp(res.build(), res.getMsgID(), session);

            //日志
            final GameLog playerActivityLog = new GameLog("platform_playerMysteryBonusLog");
            playerActivityLog.append("number", GuidGeneratorUtils.generateOrderId())
                    .append("business_no", player.getBusiness_no())
                    .append("site", player.getWebSite())
                    .append("channel", player.getChannel())
                    .append("playerId", player.getPlayerId())
                    .append("playerName", player.getPlayerName())
                    .append("language", player.getLanguage())
                    .append("agentId", player.getAgentId())
                    .append("channelId", player.getChannelId())
                    .append("region", player.getRegisterRegion())
                    .append("day", day)
                    .append("activityId", ActivityMrg.MYSTERY_BONUS)
                    .append("activityUniqueId", c_mysteryBonus.getC_id())
                    .append("currencyId", c_mysteryBonus.getCurrencyId())
                    .append("reward", mysteryRewardInfo.getBonus())
                    .append("logTime", TimeUtil.currentTimeMillis());
            HallServer.getInstance().getLogProducerMrg().send(playerActivityLog);
        } catch (Exception e) {
            LOGGER.error("ReqReceiveMysteryBonusHandler", e);
            res.setError(ErrorCode.Internal_Server_Error.getCode());
            MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
        }
    }

}
