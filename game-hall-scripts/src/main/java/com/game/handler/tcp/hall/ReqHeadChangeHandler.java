package com.game.handler.tcp.hall;

import com.game.dao.player.PlayerDao;
import com.game.engine.io.handler.TcpHandler;
import com.game.engine.script.IHandlerEntity;
import com.game.engine.utils.MsgUtil;
import com.game.entity.player.Player;
import com.game.entity.player.PlayerFields;
import com.game.enums.ErrorCode;
import com.game.hall.mrg.player.PlayerMrg;
import com.game.manager.EntityDaoMrg;
import com.proto.HallMessage;
import com.proto.MIDMessage;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@IHandlerEntity(mid = MIDMessage.MID.ReqHeadChange_VALUE, msg = HallMessage.ReqHeadChangeMessage.class)
public class ReqHeadChangeHandler extends TcpHandler {
    private static final Logger LOGGER = LoggerFactory.getLogger(ReqHeadChangeHandler.class);

    @Override
    public void run() {
        final HallMessage.ResHeadChangeMessage.Builder res = HallMessage.ResHeadChangeMessage.newBuilder();
        res.setMsgID(MIDMessage.MID.ResHeadChange_VALUE);
        try {
            final Player player = PlayerMrg.getInstance().getOnlinePlayer(pid, this.getClass().getSimpleName());
            if (player == null) {
                res.setError(ErrorCode.Player_Offline.getCode());
                MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                return;
            }

            final HallMessage.ReqHeadChangeMessage req = (HallMessage.ReqHeadChangeMessage) getMessage();
            final int headId = req.getHeadId();

            if (headId == 0) {
                LOGGER.warn("玩家id：{}，头像id：{}", pid, headId);
                MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                return;
            }

            player.setHeadId(String.valueOf(headId));
            EntityDaoMrg.getInstance().getDao(PlayerDao.class)
                    .updatePlayerField(pid, PlayerFields.headId, headId);

            res.setHeadId(headId);
            MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
        } catch (Exception e) {
            LOGGER.error("", e);
            res.setError(ErrorCode.Internal_Server_Error.getCode());
            MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
        }
    }
}
