package com.game.handler.tcp.affiliate;

import com.game.engine.io.handler.TcpHandler;
import com.game.engine.script.IHandlerEntity;
import com.game.engine.utils.FastCollectionsUtils;
import com.game.engine.utils.MsgUtil;
import com.game.entity.player.Player;
import com.game.entity.player.promote.*;
import com.game.enums.ErrorCode;
import com.game.hall.mrg.currency.CurrencyMrg;
import com.game.hall.mrg.currency.RewardRequest;
import com.game.hall.mrg.player.PlayerMrg;
import com.proto.AffiliateMessage;
import com.proto.MIDMessage;
import it.unimi.dsi.fastutil.ints.Int2DoubleMap;
import it.unimi.dsi.fastutil.objects.ObjectIterator;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Map;

@IHandlerEntity(mid = MIDMessage.MID.ReqAffiliateWithdrawData_VALUE, msg = AffiliateMessage.ReqAffiliateWithdrawDataMessage.class)
public class ReqAffiliateWithdrawDataHandler extends TcpHandler {
    private static final Logger LOGGER = LoggerFactory.getLogger(ReqAffiliateWithdrawDataHandler.class);

    @Override
    public void run() {
        final AffiliateMessage.ResAffiliateWithdrawDataMessage.Builder res = AffiliateMessage.ResAffiliateWithdrawDataMessage.newBuilder();
        res.setMsgID(MIDMessage.MID.ResAffiliateWithdrawData_VALUE);
        try {
            final Player player = PlayerMrg.getInstance().getOnlinePlayer(pid, this.getClass().getSimpleName());
            if (player == null) {
                res.setError(ErrorCode.Player_Offline.getCode());
                MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                return;
            }

            final PlayerPromote playerPromote = player.getPlayerPromote();
            final RewardRequest rewardRequest = new RewardRequest();
            final Map<Integer, CommissionRewards> commissionRewardsMap = playerPromote.getCommissionRewardsMap();
            for (Map.Entry<Integer, CommissionRewards> entry : commissionRewardsMap.entrySet()) {
                rewardRequest.addCurrency(entry.getKey(), entry.getValue().getAvailable());
            }

            final ReferralRewards availableRewards = playerPromote.getAvailableRewards();
            if (availableRewards.getCurrencyId() != 0) {
                rewardRequest.addCurrency(availableRewards.getCurrencyId(), availableRewards.getAvailable());
            }

            final Map<Integer, TeamRewards> teamRewardsMap = playerPromote.getTeamRewardsMap();
            for (Map.Entry<Integer, TeamRewards> entry : teamRewardsMap.entrySet()) {
                rewardRequest.addCurrency(entry.getKey(), entry.getValue().getAvailable());
            }

            final Map<Integer, ThreeLevelRewards> threeLevelRewardsMap = playerPromote.getThreeLevelRewardsMap();
            for (Map.Entry<Integer, ThreeLevelRewards> entry : threeLevelRewardsMap.entrySet()) {
                rewardRequest.addCurrency(entry.getKey(), entry.getValue().getAvailable());
            }

            for (ObjectIterator<Int2DoubleMap.Entry> iterator = FastCollectionsUtils.fastIterator(rewardRequest.getCurrencyMap()); iterator.hasNext(); ) {
                final Int2DoubleMap.Entry entry = iterator.next();
                res.addCItem(CurrencyMrg.getInstance().buildCurrency(entry.getIntKey(), entry.getDoubleValue()));
            }

            MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
        } catch (Exception e) {
            LOGGER.error("ReqAffiliateWithdrawDataHandler", e);
            res.setError(ErrorCode.Internal_Server_Error.getCode());
            MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
        }
    }
}
