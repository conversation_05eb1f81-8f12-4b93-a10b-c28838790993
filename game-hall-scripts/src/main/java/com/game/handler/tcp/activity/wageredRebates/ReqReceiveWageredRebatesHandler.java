package com.game.handler.tcp.activity.wageredRebates;

import com.game.c_entity.merchant.C_WageredRebates;
import com.game.dao.player.PlayerDao;
import com.game.engine.io.handler.TcpHandler;
import com.game.engine.io.redis.RedisPoolManager;
import com.game.engine.log.GameLog;
import com.game.engine.math.BigDecimalUtils;
import com.game.engine.script.IHandlerEntity;
import com.game.engine.script.ScriptLoader;
import com.game.engine.util.misc.Tuple2;
import com.game.engine.utils.GuidGeneratorUtils;
import com.game.engine.utils.MsgUtil;
import com.game.engine.utils.TimeUtil;
import com.game.entity.player.Player;
import com.game.entity.player.activity.wageredRebates.WageredRebatesInfo;
import com.game.enums.ErrorCode;
import com.game.enums.RewardReason;
import com.game.enums.TransactionFrom;
import com.game.enums.TurnoverReason;
import com.game.enums.redis.RedisRanking;
import com.game.hall.main.HallServer;
import com.game.hall.mrg.ActivityMrg;
import com.game.hall.mrg.CommonMrg;
import com.game.hall.mrg.DataHallMrg;
import com.game.hall.mrg.MerchantData;
import com.game.hall.mrg.currency.CurrencyMrg;
import com.game.hall.mrg.currency.RewardRequest;
import com.game.hall.mrg.player.PlayerMrg;
import com.game.hall.script.IBonusScript;
import com.game.hall.script.IPlayerScript;
import com.game.manager.EntityDaoMrg;
import com.proto.ActivityMessage;
import com.proto.MIDMessage;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@IHandlerEntity(mid = MIDMessage.MID.ReqReceiveWageredRebates_VALUE, msg = ActivityMessage.ReqReceiveWageredRebatesMessage.class)
public class ReqReceiveWageredRebatesHandler extends TcpHandler {
    private static final Logger LOGGER = LoggerFactory.getLogger(ReqReceiveWageredRebatesHandler.class);

    @Override
    public void run() {
        final ActivityMessage.ResReceiveWageredRebatesMessage.Builder res = ActivityMessage.ResReceiveWageredRebatesMessage.newBuilder();
        res.setMsgID(MIDMessage.MID.ResReceiveWageredRebates_VALUE);
        try {
            final Player player = PlayerMrg.getInstance().getOnlinePlayer(pid, this.getClass().getSimpleName());
            if (player == null) {
                res.setError(ErrorCode.Player_Offline.getCode());
                MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                return;
            }

            final MerchantData merchantData = DataHallMrg.getInstance().findMerchantData(this.getClass().getSimpleName(), player.getBusiness_no());
            if (merchantData == null) {
                res.setError(ErrorCode.Data_Error.getCode());
                MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                return;
            }

            final C_WageredRebates c_wageredRebates = merchantData.findC_WageredRebates(this.getClass().getSimpleName(), ActivityMrg.WAGERED_REBATES);
            if (c_wageredRebates == null) {
                MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                return;
            }

            final WageredRebatesInfo wageredRebates = player.getWageredRebatesInfo();

            int topNum = 0;
            if (c_wageredRebates.getRankType() == 1) {// 比例
                final long totalNum = RedisPoolManager.getInstance().function(jedis ->
                        jedis.sync().zcard(RedisRanking.RANKING_WAGEREDREBATES.getKey(player.getBusiness_no(), TimeUtil.getDateTimeFormat(TimeUtil.YYYYMMDD, c_wageredRebates.getTimeZone()))));
                topNum = (int) BigDecimalUtils.mul((double) totalNum, c_wageredRebates.getRankNum(), 9);
                topNum = Math.max(10, topNum);
            } else {//
                topNum = (int) c_wageredRebates.getRankNum();
            }

            Long ranking = RedisPoolManager.getInstance().function(jedis ->
                    jedis.sync().zrevrank(RedisRanking.RANKING_WAGEREDREBATES.getKey(player.getBusiness_no(), TimeUtil.getDateTimeFormat(TimeUtil.YYYYMMDD, player.getTimeZone())), player.getCurrencyId() + "_" + player.getPlayerId()));
            if (ranking == null) {
                ranking = 0L;
            } else {
                ranking++;
            }

            if (wageredRebates.isStatus()) {
                res.setError(ErrorCode.Reward_Received.getCode());
                MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                return;
            }

            if (ranking > topNum) {
                res.setError(ErrorCode.Conditions_Not_Met.getCode());
                MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                return;
            }

            final double bonus = wageredRebates.getBonusMap()
                    .getOrDefault(player.getCurrencyId(), 0d);

            wageredRebates.setStatus(true);
            wageredRebates.getBonusMap().clear();
            EntityDaoMrg.getInstance().getDao(PlayerDao.class)
                    .updateWageredRebatesInfo(player.getPlayerId(), wageredRebates);

            final RewardRequest rewardRequest = new RewardRequest();
            rewardRequest.addCurrency(player.getCurrencyId(), bonus);
            final RewardReason rewardReason = RewardReason.WageredRebates;
            if (c_wageredRebates.getRewardType() == 1) {//cash
                CurrencyMrg.getInstance().rewardNoTest(player, rewardRequest.getCurrencyMap(), rewardReason);
            } else {//bonus
                CurrencyMrg.getInstance().rewardNoTest(player, rewardRequest.getBonusCurrencyMap(), rewardReason);
            }

            //TODO 打码
//            final WithdrawStandard withdrawStandard = player.getWithdrawStandard(box.currencyId);
//            withdrawStandard.incDrawStandard(BigDecimalUtils.mul(box.amount, c_rewardBox.getTurnoverMul(), 4));
            final TurnoverReason turnoverReason = TurnoverReason.WageredRebates;
            final double finalBonus = bonus;
            ScriptLoader.getInstance().consumerScript("PlayerWithdrawStandardScript",
                    (IPlayerScript script) -> script.drawStandard(player, turnoverReason, player.getCurrencyId(), finalBonus,
                            BigDecimalUtils.mul(finalBonus, c_wageredRebates.getTurnoverMul(), 4)));

            res.setRewardShow(CommonMrg.buildDItemShow(player.getCurrencyId(), finalBonus));
            MsgUtil.responseHttp(res.build(), res.getMsgID(), session);

            if (!rewardRequest.getCurrencyMap().isEmpty()) {
                final Tuple2<Integer, Double> tuple2 = rewardRequest.getReward();
                ScriptLoader.getInstance().consumerScript("BonusScript",
                        (IBonusScript script) -> script.addBonusNote(TransactionFrom.WageredRebates, player, tuple2.getFirst(), tuple2.getSecond()));

                final GameLog playerActivityLog = new GameLog("platform_playerWageredRebatesRewardLog");
                playerActivityLog.append("number", GuidGeneratorUtils.generateOrderId())
                        .append("business_no", player.getBusiness_no())
                        .append("site", player.getWebSite())
                        .append("channel", player.getChannel())
                        .append("playerId", player.getPlayerId())
                        .append("playerName", player.getPlayerName())
                        .append("language", player.getLanguage())
                        .append("agentId", player.getAgentId())
                        .append("channelId", player.getChannelId())
                        .append("region", player.getRegisterRegion())
                        .append("activityId", ActivityMrg.WAGERED_REBATES)
                        .append("date", TimeUtil.getDateTimeFormat(TimeUtil.currentTimeMillis() - TimeUtil.DAY, TimeUtil.YYYYMMDD, c_wageredRebates.getTimeZone()))
                        .append("activityUniqueId", c_wageredRebates.getC_id())
                        .append("currencyId", tuple2.getFirst())
                        .append("reward", tuple2.getSecond())
                        .append("logTime", TimeUtil.currentTimeMillis());
                HallServer.getInstance().getLogProducerMrg().send(playerActivityLog);
            }
        } catch (Exception e) {
            LOGGER.error("ReqReceiveWageredRebatesHandler", e);
            res.setError(ErrorCode.Internal_Server_Error.getCode());
            MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
        }
    }
}
