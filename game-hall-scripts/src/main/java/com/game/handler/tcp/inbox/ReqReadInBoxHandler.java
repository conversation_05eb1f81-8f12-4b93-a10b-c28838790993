package com.game.handler.tcp.inbox;

import com.game.dao.player.PlayerDao;
import com.game.engine.io.handler.TcpHandler;
import com.game.engine.script.IHandlerEntity;
import com.game.engine.utils.MsgUtil;
import com.game.entity.player.Player;
import com.game.entity.player.inbox.Inbox;
import com.game.entity.player.inbox.InboxInfo;
import com.game.enums.ErrorCode;
import com.game.hall.mrg.player.PlayerMrg;
import com.game.manager.EntityDaoMrg;
import com.proto.InboxMessage;
import com.proto.MIDMessage;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@IHandlerEntity(mid = MIDMessage.MID.ReqReadInBox_VALUE, msg = InboxMessage.ReqReadInBoxMessage.class)
public class ReqReadInBoxHandler extends TcpHandler {
    private static final Logger LOGGER = LoggerFactory.getLogger(ReqReadInBoxHandler.class);

    @Override
    public void run() {
        final InboxMessage.ResReadInBoxMessage.Builder res = InboxMessage.ResReadInBoxMessage.newBuilder();
        res.setMsgID(MIDMessage.MID.ResReadInBox_VALUE);
        try {
            final Player player = PlayerMrg.getInstance().getOnlinePlayer(pid, this.getClass().getName());
            if (player == null) {
                res.setError(ErrorCode.Player_Offline.getCode());
                MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                return;
            }

            final InboxMessage.ReqReadInBoxMessage req = (InboxMessage.ReqReadInBoxMessage) getMessage();
            final long inboxId = Long.parseLong(req.getInboxId());

            final InboxInfo inboxInfo = player.getInboxInfo();
            final Inbox inbox = inboxInfo.getInboxMap().get(inboxId);
            if (inbox == null) {
                LOGGER.warn("playerId：{}，inboxId：{}，not exist", pid, inboxId);
                res.setError(ErrorCode.Data_Error.getCode());
                MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                return;
            }

            inbox.setRead(true);
            EntityDaoMrg.getInstance().getDao(PlayerDao.class).inboxDao
                    .updateInboxRead(player.getPlayerId(), inbox);

            res.setInboxId(req.getInboxId());
            MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
        } catch (Exception e) {
            LOGGER.error("ReqReadInBoxHandler", e);
            res.setError(ErrorCode.Internal_Server_Error.getCode());
            MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
        }
    }
}
