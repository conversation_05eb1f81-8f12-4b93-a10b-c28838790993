package com.game.handler.tcp.game;

import com.game.dao.player.PlayerDao;
import com.game.engine.io.handler.TcpHandler;
import com.game.engine.io.redis.RedisPoolManager;
import com.game.engine.script.IHandlerEntity;
import com.game.engine.utils.MsgUtil;
import com.game.entity.player.Player;
import com.game.entity.player.PlayerFields;
import com.game.enums.ErrorCode;
import com.game.enums.redis.RedisAllGame;
import com.game.hall.mrg.player.PlayerMrg;
import com.game.manager.EntityDaoMrg;
import com.game.utils.VirtualThreadUtils;
import com.proto.HallMessage;
import com.proto.MIDMessage;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.mongodb.core.query.Update;

@IHandlerEntity(mid = MIDMessage.MID.ReqUserInteraction_VALUE, msg = HallMessage.ReqUserInteractionMessage.class)
public class ReqUserInteractionHandler extends TcpHandler {
    private static final Logger LOGGER = LoggerFactory.getLogger(ReqUserInteractionHandler.class);

    @Override
    public void run() {
        final HallMessage.ResUserInteractionMessage.Builder res = HallMessage.ResUserInteractionMessage.newBuilder();
        res.setMsgID(MIDMessage.MID.ResUserInteraction_VALUE);
        try {
            final Player player = PlayerMrg.getInstance().getOnlinePlayer(pid, this.getClass().getSimpleName());
            if (player == null) {
                res.setError(ErrorCode.Player_Offline.getCode());
                MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                return;
            }

            final HallMessage.ReqUserInteractionMessage req = (HallMessage.ReqUserInteractionMessage) getMessage();
            final int type = req.getType();
            final int gameId = req.getGameId();

            long collect = 0;
            long like = 0;
            switch (type) {
                case 1://收藏
                    if (gameId == 0) {
                        break;
                    }
                    int a = 0;
                    if (player.getFavoritesGame().contains(gameId)) {
                        player.getFavoritesGame().removeIf(id -> id == gameId);
                        a = -1;
                    } else {
                        player.getFavoritesGame().add(gameId);
                        a = 1;
                    }

                    final int finalA = a;
                    collect = RedisPoolManager.getInstance().function(jedis ->
                            jedis.sync().incrby(RedisAllGame.Platform_All_GameCollect.getKey(player.getBusiness_no(), gameId), finalA));
                    if (collect <= 0) {
                        collect = 0;
                        RedisPoolManager.getInstance().executeAsync(commands ->
                                commands.del(RedisAllGame.Platform_All_GameCollect.getKey(player.getBusiness_no(), gameId))
                        );
                    }

                    final Update update = new Update();
                    update.set(PlayerFields.favoritesGame, player.getFavoritesGame());
                    EntityDaoMrg.getInstance().getDao(PlayerDao.class)
                            .updatePlayer(pid, update);
                    break;
                case 2://点赞
                    if (gameId == 0) {
                        break;
                    }
                    int b = 0;
                    if (player.getLikeGame().contains(gameId)) {
                        player.getLikeGame().removeIf(id -> id == gameId);
                        b = -1;
                    } else {
                        player.getLikeGame().add(gameId);
                        b = 1;
                    }

                    final int finalB = b;
                    like = RedisPoolManager.getInstance().function(jedis ->
                            jedis.sync().incrby(RedisAllGame.Platform_All_GameLike.getKey(player.getBusiness_no(), gameId), finalB));
                    if (like <= 0) {
                        like = 0;
                        RedisPoolManager.getInstance().executeAsync(commands ->
                                commands.del(RedisAllGame.Platform_All_GameLike.getKey(player.getBusiness_no(), gameId))
                        );
                    }
                    final Update update1 = new Update();
                    update1.set(PlayerFields.likeGame, player.getLikeGame());
                    EntityDaoMrg.getInstance().getDao(PlayerDao.class)
                            .updatePlayer(pid, update1);
                    break;
            }

            res.setType(type)
                    .setGameId(gameId)
                    .setNum((int) (type == 1 ? collect : like));
            MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
        } catch (Exception e) {
            LOGGER.error("ReqUserInteractionHandler", e);
            res.setError(ErrorCode.Internal_Server_Error.getCode());
            MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
        }
    }
}
