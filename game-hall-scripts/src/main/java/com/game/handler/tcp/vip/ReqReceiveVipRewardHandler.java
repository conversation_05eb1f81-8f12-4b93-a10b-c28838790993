package com.game.handler.tcp.vip;

import com.game.c_entity.merchant.C_VipClub;
import com.game.dao.player.PlayerDao;
import com.game.engine.io.handler.TcpHandler;
import com.game.engine.log.GameLog;
import com.game.engine.math.BigDecimalUtils;
import com.game.engine.script.IHandlerEntity;
import com.game.engine.script.ScriptLoader;
import com.game.engine.util.misc.Tuple2;
import com.game.engine.utils.GuidGeneratorUtils;
import com.game.engine.utils.MsgUtil;
import com.game.engine.utils.TimeUtil;
import com.game.entity.player.Player;
import com.game.entity.player.vip.VipClub;
import com.game.enums.ErrorCode;
import com.game.enums.RewardReason;
import com.game.enums.TransactionFrom;
import com.game.enums.TurnoverReason;
import com.game.hall.main.HallServer;
import com.game.hall.mrg.CommonMrg;
import com.game.hall.mrg.DataHallMrg;
import com.game.hall.mrg.MerchantData;
import com.game.hall.mrg.currency.CurrencyMrg;
import com.game.hall.mrg.currency.RewardRequest;
import com.game.hall.mrg.player.PlayerMrg;
import com.game.hall.script.IBonusScript;
import com.game.hall.script.IPlayerScript;
import com.game.manager.EntityDaoMrg;
import com.proto.ActivityMessage;
import com.proto.MIDMessage;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@IHandlerEntity(mid = MIDMessage.MID.ReqReceiveVipReward_VALUE, msg = ActivityMessage.ReqReceiveVipRewardMessage.class)
public class ReqReceiveVipRewardHandler extends TcpHandler {
    private static final Logger LOGGER = LoggerFactory.getLogger(ReqReceiveVipRewardHandler.class);

    @Override
    public void run() {
        final ActivityMessage.ResReceiveVipRewardMessage.Builder res = ActivityMessage.ResReceiveVipRewardMessage.newBuilder();
        res.setMsgID(MIDMessage.MID.ResReceiveVipReward_VALUE);
        try {
            final Player player = PlayerMrg.getInstance().getOnlinePlayer(pid, this.getClass().getSimpleName());
            if (player == null) {
                res.setError(ErrorCode.Player_Offline.getCode());
                MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                return;
            }

            final ActivityMessage.ReqReceiveVipRewardMessage req = (ActivityMessage.ReqReceiveVipRewardMessage) getMessage();
            final int type = req.getType();

            final MerchantData merchantData = DataHallMrg.getInstance().findMerchantData(this.getClass().getSimpleName(), player.getBusiness_no());
            if (merchantData == null) {
                res.setError(ErrorCode.Data_Error.getCode());
                MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                return;
            }

            final VipClub vipClub = player.getVipClub();

            final C_VipClub c_vipClub = merchantData.findC_VipClub(this.getClass().getSimpleName(), vipClub.getVipLevel());
            if (c_vipClub == null) {
                res.setError(ErrorCode.Data_Error.getCode());
                MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                return;
            }

            final C_VipClub.ReceiveReward receiveReward = c_vipClub.getReceiveRewardMap().get(type);
            if (receiveReward == null) {
                res.setError(ErrorCode.Data_Error.getCode());
                MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                return;
            }

            if (!isReceiveTime(c_vipClub, receiveReward)) {
                res.setError(ErrorCode.Not_Yet_Received.getCode());
                MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                return;
            }

            if (vipClub.getReceiveReward().contains(type)) {
                res.setError(ErrorCode.Reward_Received.getCode());
                MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                return;
            }

            RewardReason rewardReason = RewardReason.Default;
            TransactionFrom transactionFrom = TransactionFrom.None;
            TurnoverReason turnoverReason = TurnoverReason.Default;
            switch (type) {
                case 1:
                    rewardReason = RewardReason.Vip_Daily;
                    transactionFrom = TransactionFrom.Vip_Daily;
                    turnoverReason = TurnoverReason.Vip_Daily;
                    final double dailyWagered = vipClub.getDailyWageredMap().getOrDefault(receiveReward.getCurrencyId(), 0d);
                    if (dailyWagered < receiveReward.getWagered()) {
                        res.setError(ErrorCode.Conditions_Not_Met.getCode());
                        MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                        return;
                    }
                    break;
                case 2:
                    rewardReason = RewardReason.Vip_Weekly;
                    transactionFrom = TransactionFrom.Vip_Weekly;
                    turnoverReason = TurnoverReason.Vip_Weekly;
                    final double weeklyWagered = vipClub.getWeeklyWageredMap().getOrDefault(receiveReward.getCurrencyId(), 0d);
                    if (weeklyWagered < receiveReward.getWagered()) {
                        res.setError(ErrorCode.Conditions_Not_Met.getCode());
                        MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                        return;
                    }
                    break;
                case 3:
                    rewardReason = RewardReason.Vip_Monthly;
                    transactionFrom = TransactionFrom.Vip_Monthly;
                    turnoverReason = TurnoverReason.Vip_Monthly;
                    final double monthlyWagered = vipClub.getMonthlyWageredMap().getOrDefault(receiveReward.getCurrencyId(), 0d);
                    if (monthlyWagered < receiveReward.getWagered()) {
                        res.setError(ErrorCode.Conditions_Not_Met.getCode());
                        MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                        return;
                    }
                    break;
            }

            vipClub.getReceiveReward().add(type);
            EntityDaoMrg.getInstance().getDao(PlayerDao.class).vipClubDao
                    .updateReceiveReward(pid, vipClub);

            final RewardRequest rewardRequest = new RewardRequest();
            rewardRequest.addCurrency(receiveReward.currencyId, receiveReward.reward);
            rewardReason.setSource(receiveReward.getId() + "");
            if (c_vipClub.getCycleRewardType() == 1) {
                CurrencyMrg.getInstance().rewardNoTest(player, rewardRequest.getCurrencyMap(), rewardReason);
            } else {
                CurrencyMrg.getInstance().rewardNoTest(player, rewardRequest.getBonusCurrencyMap(), rewardReason);
            }

            //TODO 打码
//            final WithdrawStandard withdrawStandard = player.getWithdrawStandard(receiveReward.currencyId);
//            withdrawStandard.incDrawStandard(BigDecimalUtils.mul(receiveReward.reward, receiveReward.turnoverMul, 4));
            turnoverReason.setSource(receiveReward.getId() + "");
            final TurnoverReason finalTurnoverReason = turnoverReason;
            ScriptLoader.getInstance().consumerScript("PlayerWithdrawStandardScript",
                    (IPlayerScript script) -> script.drawStandard(player, finalTurnoverReason, receiveReward.currencyId, receiveReward.reward,
                            BigDecimalUtils.mul(receiveReward.reward, receiveReward.turnoverMul, 4)));

            if (!rewardRequest.getCurrencyMap().isEmpty()) {
                final Tuple2<Integer, Double> tuple2 = rewardRequest.getReward();
                final TransactionFrom finalTransactionFrom = transactionFrom;
                ScriptLoader.getInstance().consumerScript("BonusScript",
                        (IBonusScript script) -> script.addBonusNote(finalTransactionFrom, player, tuple2.getFirst(), tuple2.getSecond()));
            }

            res.setRewardShow(CommonMrg.buildDItemShow(receiveReward.currencyId, receiveReward.reward));
            MsgUtil.responseHttp(res.build(), res.getMsgID(), session);

            final GameLog playerVipSalaryLog = new GameLog("platform_playerVipSalaryLog");
            playerVipSalaryLog.append("number", GuidGeneratorUtils.generateOrderId())
                    .append("business_no", player.getBusiness_no())
                    .append("channel", player.getChannel())
                    .append("site", player.getWebSite())
                    .append("playerId", player.getPlayerId())
                    .append("playerName", player.getPlayerName())
                    .append("agentId", player.getAgentId())
                    .append("channelId", player.getChannelId())
                    .append("region", player.getRegisterRegion())
                    .append("logTime", TimeUtil.currentTimeMillis())
                    .append("type", 1)//1.领取 2.激活
                    .append("rewardType", type)//1.日 2.周 3.月
                    .append("vipLevel", vipClub.getVipLevel())
                    .append("turnover", BigDecimalUtils.mul(receiveReward.reward, receiveReward.turnoverMul, 4))
                    .append("currencyId", receiveReward.currencyId)
                    .append("reward", receiveReward.reward);
            HallServer.getInstance().getLogProducerMrg().send(playerVipSalaryLog);
        } catch (Exception e) {
            LOGGER.error("ReqReceiveVipRewardHandler", e);
            res.setError(ErrorCode.Internal_Server_Error.getCode());
            MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
        }
    }

    private boolean isReceiveTime(C_VipClub c_vipClub, C_VipClub.ReceiveReward receiveReward) {
        if (receiveReward.getReceiveTime() == 0) {
            return true;
        }
        if (receiveReward.getId() == 1) {
            final long dailyTime = TimeUtil.getTimeBeginOfToday(TimeUtil.currentTimeMillis(), c_vipClub.getTimeZone());
            return TimeUtil.currentTimeMillis() >= dailyTime + receiveReward.getReceiveTime();
        } else if (receiveReward.getId() == 2) {
            final long weekTime = TimeUtil.getTimeBeginOfWeek(TimeUtil.currentTimeMillis(), c_vipClub.getTimeZone());
            return TimeUtil.currentTimeMillis() >= weekTime + receiveReward.getReceiveTime();
        } else if (receiveReward.getId() == 3) {
            final long monthTime = TimeUtil.getTimeBeginOfMonth(TimeUtil.currentTimeMillis(), c_vipClub.getTimeZone());
            return TimeUtil.currentTimeMillis() >= monthTime + receiveReward.getReceiveTime();
        }
        return false;
    }

}
