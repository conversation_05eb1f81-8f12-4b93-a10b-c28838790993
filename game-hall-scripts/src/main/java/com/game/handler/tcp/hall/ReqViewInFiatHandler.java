package com.game.handler.tcp.hall;

import com.game.dao.player.PlayerDao;
import com.game.engine.io.handler.TcpHandler;
import com.game.engine.script.IHandlerEntity;
import com.game.engine.utils.MsgUtil;
import com.game.entity.player.Player;
import com.game.entity.player.PlayerFields;
import com.game.enums.ErrorCode;
import com.game.hall.mrg.player.PlayerMrg;
import com.game.manager.EntityDaoMrg;
import com.proto.HallMessage;
import com.proto.MIDMessage;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.mongodb.core.query.Update;


@IHandlerEntity(mid = MIDMessage.MID.ReqViewInFiat_VALUE, msg = HallMessage.ReqViewInFiatMessage.class)
public class ReqViewInFiatHandler extends TcpHandler {
    private static final Logger LOGGER = LoggerFactory.getLogger(ReqViewInFiatHandler.class);

    @Override
    public void run() {
        final HallMessage.ResViewInFiatMessage.Builder res = HallMessage.ResViewInFiatMessage.newBuilder();
        res.setMsgID(MIDMessage.MID.ResViewInFiat_VALUE);

        try {
            final Player player = PlayerMrg.getInstance().getOnlinePlayer(pid, this.getClass().getName());
            if (player == null) {
                res.setError(ErrorCode.Player_Offline.getCode());
                MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                return;
            }

            final HallMessage.ReqViewInFiatMessage req = (HallMessage.ReqViewInFiatMessage) getMessage();
            final int currencyId = req.getCurrency();

            if (currencyId / 1000 != 1) {
                LOGGER.warn("playerId：{}，currencyId：{}，viewInFiat not fiat", pid, currencyId);
                res.setError(ErrorCode.Data_Error.getCode());
                MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                return;
            }

            player.setViewFiat(currencyId);
            final Update update = new Update();
            update.set(PlayerFields.viewFiat, currencyId);
            EntityDaoMrg.getInstance().getDao(PlayerDao.class)
                    .updatePlayer(pid, update);

            res.setFiatCurrency(currencyId);
            MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
        } catch (Exception e) {
            LOGGER.error("", e);
            res.setError(ErrorCode.Internal_Server_Error.getCode());
            MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
        }

    }
}
