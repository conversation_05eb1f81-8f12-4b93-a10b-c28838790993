package com.game.handler.tcp.quest;

import com.game.c_entity.merchant.C_Quest;
import com.game.dao.player.PlayerDao;
import com.game.dao.quest.QuestNoteDao;
import com.game.engine.io.handler.TcpHandler;
import com.game.engine.log.GameLog;
import com.game.engine.math.BigDecimalUtils;
import com.game.engine.script.IHandlerEntity;
import com.game.engine.script.ScriptLoader;
import com.game.engine.utils.GuidGeneratorUtils;
import com.game.engine.utils.MsgUtil;
import com.game.engine.utils.TimeUtil;
import com.game.engine.utils.UniqueIDGenerator;
import com.game.entity.player.FreeGameInfo;
import com.game.entity.player.GameInfo;
import com.game.entity.player.Player;
import com.game.entity.player.bonus.BonusDetailsInfo;
import com.game.entity.player.bonus.BonusInfo;
import com.game.entity.player.quest.QuestInfo;
import com.game.entity.player.quest.QuestNote;
import com.game.entity.player.quest.SingleQuestInfo;
import com.game.enums.*;
import com.game.hall.main.HallServer;
import com.game.hall.mrg.currency.CurrencyMrg;
import com.game.hall.mrg.currency.RewardRequest;
import com.game.hall.mrg.player.PlayerMrg;
import com.game.hall.mrg.quest.QuestMrg;
import com.game.hall.mrg.quest.QuestState;
import com.game.hall.script.IBonusScript;
import com.game.hall.script.IPlayerScript;
import com.game.manager.EntityDaoMrg;
import com.proto.MIDMessage;
import com.proto.QuestMessage;
import io.netty.util.internal.StringUtil;
import it.unimi.dsi.fastutil.ints.IntLists;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@IHandlerEntity(mid = MIDMessage.MID.ReqReceiveQuestReward_VALUE, msg = QuestMessage.ReqReceiveQuestRewardMessage.class)
public class ReqReceiveQuestRewardHandler extends TcpHandler {
    private static final Logger LOGGER = LoggerFactory.getLogger(ReqReceiveQuestRewardHandler.class);

    @Override
    public void run() {
        final QuestMessage.ResReceiveQuestRewardMessage.Builder res = QuestMessage.ResReceiveQuestRewardMessage.newBuilder();
        res.setMsgID(MIDMessage.MID.ResReceiveQuestReward_VALUE);
        try {
            final Player player = PlayerMrg.getInstance().getOnlinePlayer(pid, this.getClass().getSimpleName());
            if (player == null) {
                res.setError(ErrorCode.Player_Offline.getCode());
                MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                return;
            }

            final QuestMessage.ReqReceiveQuestRewardMessage req = (QuestMessage.ReqReceiveQuestRewardMessage) getMessage();
            if (req.getQuestId() > 0) {
                final int questId = req.getQuestId();
                final QuestInfo questInfo = player.getQuestInfo();
                final SingleQuestInfo singleQuestInfo = questInfo.getQuestInfoMap().get(questId);
                if (singleQuestInfo == null) {
                    LOGGER.warn("playerId：{}，questId：{}，not exist", pid, questId);
                    res.setError(ErrorCode.Data_Error.getCode());
                    MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                    return;
                }
                if (singleQuestInfo.getState() == QuestState.ACCEPTED) {
                    LOGGER.warn("playerId：{}，questId：{}，not completed", pid, questId);
                    res.setError(ErrorCode.Task_Not_Completed.getCode());
                    MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                    return;
                }

                if (singleQuestInfo.getState() == QuestState.RECEIVED) {
                    LOGGER.warn("playerId：{}，questId：{}，received", pid, questId);
                    res.setError(ErrorCode.Task_Already_Received.getCode());
                    MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                    return;
                }

                final C_Quest c_quest = QuestMrg.getInstance().getQuest(player, questId);
                if (c_quest == null) {
                    res.setError(ErrorCode.Data_Error.getCode());
                    MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                    return;
                }

                singleQuestInfo.setState(QuestState.RECEIVED);
                EntityDaoMrg.getInstance().getDao(PlayerDao.class).questDao
                        .updateQuestState(pid, questInfo, questId);

                //TODO 任务记录
                final UniqueIDGenerator uniqueIDGenerator = HallServer.getInstance().getUniqueIDGenerator();
                final QuestNote questNote = new QuestNote(uniqueIDGenerator.nextId());
                questNote.setBusiness_no(player.getBusiness_no());
                questNote.setPlayerId(player.getPlayerId());
                questNote.setQuestId(c_quest.getQuestId());
                questNote.setState(singleQuestInfo.getState());
                questNote.setFinishedTime(singleQuestInfo.getFinishedTime());
                EntityDaoMrg.getInstance().getDao(QuestNoteDao.class).insert(questNote);

                res.setRewards(QuestMrg.getInstance().buildDItemShow(c_quest.getRewardCurrency().currencyId, c_quest.getRewardCurrency().amount))
                        .setState(singleQuestInfo.getState());
                MsgUtil.responseHttp(res.build(), res.getMsgID(), session);

                receiveQuest(player, c_quest);
            }

            if (!StringUtil.isNullOrEmpty(req.getUniqueId())) {
                final long uniqueId = Long.parseLong(req.getUniqueId());
                final QuestNote questNote = EntityDaoMrg.getInstance().getDao(QuestNoteDao.class).loadQuestNote(pid, uniqueId);

                if (questNote.getState() == QuestState.ACCEPTED) {
                    LOGGER.warn("playerId：{}，questId：{}，not completed", pid, questNote.getQuestId());
                    res.setError(ErrorCode.Task_Not_Completed.getCode());
                    MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                    return;
                }

                if (questNote.getState() == QuestState.RECEIVED) {
                    LOGGER.warn("playerId：{}，questId：{}，received", pid, questNote.getQuestId());
                    res.setError(ErrorCode.Task_Already_Received.getCode());
                    MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                    return;
                }

                final C_Quest c_quest = QuestMrg.getInstance().getQuest(player, questNote.getQuestId());
                if (c_quest == null) {
                    res.setError(ErrorCode.Data_Error.getCode());
                    MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                    return;
                }

                questNote.setState(QuestState.RECEIVED);
                EntityDaoMrg.getInstance().getDao(QuestNoteDao.class).updateQuestState(pid, uniqueId, questNote.getState());

                res.setRewards(QuestMrg.getInstance().buildDItemShow(c_quest.getRewardCurrency().currencyId, c_quest.getRewardCurrency().amount))
                        .setState(questNote.getState());
                MsgUtil.responseHttp(res.build(), res.getMsgID(), session);

                receiveQuest(player, c_quest);
            }
        } catch (Exception e) {
            LOGGER.error("ReqReceiveQuestRewardHandler", e);
            res.setError(ErrorCode.Internal_Server_Error.getCode());
            MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
        }
    }

    private void receiveQuest(Player player, C_Quest c_quest) {
        final C_Quest.RewardCurrency rewardCurrency = c_quest.getRewardCurrency();
        final RewardRequest rewardRequest = new RewardRequest();
        rewardRequest.addCurrency(rewardCurrency.currencyId, rewardCurrency.amount);
        final RewardReason rewardReason = RewardReason.Quest;
        rewardReason.setSource(c_quest.getQuestId() + "");
        CurrencyMrg.getInstance().rewardNoTest(player, rewardRequest.getBonusCurrencyMap(), rewardReason);

        final QuestInfo questInfo = player.getQuestInfo();
        questInfo.incAccumulatedRewards(rewardCurrency.currencyId, rewardCurrency.amount);
        EntityDaoMrg.getInstance().getDao(PlayerDao.class).questDao
                .updateAccumulatedRewards(pid, questInfo);

        //TODO 免费游戏次数
        final C_Quest.RewardFreeGame rewardFreeGame = c_quest.getRewardFreeGame();
        if (rewardFreeGame != null && rewardFreeGame.currencyId > 0) {
            final FreeGameInfo freeGameInfo = player.getFreeGameInfo(rewardFreeGame.currencyId);
            final GameInfo gameInfo = freeGameInfo.getFreeGame(rewardFreeGame.gameId);
            gameInfo.incFreeTimes(rewardFreeGame.freeTimes);
            gameInfo.setBet(rewardFreeGame.bet);
            gameInfo.setMinWithdraw(rewardFreeGame.getMinWithdraw());
            gameInfo.setMaxWithdraw(rewardFreeGame.getMaxWithdraw());
            EntityDaoMrg.getInstance().getDao(PlayerDao.class)
                    .updateFreeGameInfo(player, IntLists.singleton(rewardFreeGame.currencyId));
        }

        //TODO 打码
//        final WithdrawStandard withdrawStandard = player.getWithdrawStandard(rewardCurrency.currencyId);
//        withdrawStandard.incDrawStandard(BigDecimalUtils.mul(rewardCurrency.amount, c_quest.getTurnoverMul(), 4));
        final TurnoverReason turnoverReason = TurnoverReason.Quest;
        turnoverReason.setSource(c_quest.getQuestId() + "");
        ScriptLoader.getInstance().consumerScript("PlayerWithdrawStandardScript",
                (IPlayerScript script) -> script.drawStandard(player, turnoverReason, rewardCurrency.currencyId, rewardCurrency.amount,
                        BigDecimalUtils.mul(rewardCurrency.amount, c_quest.getTurnoverMul(), 4)));

        final C_Quest.DescInfo descInfo = c_quest.getDescInfoMap().get(player.getLanguage());
        //TODO 日志
        final GameLog playerQuestLog = new GameLog("platform_playerQuestLog");
        playerQuestLog.append("number", GuidGeneratorUtils.generateOrderId())
                .append("business_no", player.getBusiness_no())
                .append("site", player.getWebSite())
                .append("playerId", player.getPlayerId())
                .append("playerName", player.getPlayerName())
                .append("questId", c_quest.getQuestId())

                .append("questType", c_quest.getQuestType())//任务类型
                .append("goalType", c_quest.getGoalType())//任务目标
                .append("agentId", player.getAgentId())
                .append("channelId", player.getChannelId())
                .append("region", player.getRegisterRegion())
                .append("currencyId", c_quest.getRewardCurrency().currencyId)
                .append("amount", c_quest.getRewardCurrency().amount)
                .append("logTime", TimeUtil.currentTimeMillis());
        if (descInfo != null) {
            playerQuestLog.append("questName", descInfo.getQuestName());
        }
        HallServer.getInstance().getLogProducerMrg().send(playerQuestLog);

        final BonusInfo bonusInfo = player.getBonusInfo();
        final BonusDetailsInfo bonusDetailsInfo = bonusInfo.getBonusDetailsInfo(BonusDetail.Quests.getType());
        bonusDetailsInfo.incBonus(rewardCurrency.currencyId, rewardCurrency.amount);
        EntityDaoMrg.getInstance().getDao(PlayerDao.class).bonusInfoDao
                .updateBonusDetails(player, BonusDetail.Quests.getType(), bonusDetailsInfo, IntLists.singleton(rewardCurrency.currencyId));

        TransactionFrom transaction;
        if (c_quest.getQuestType() == 1) {//每日
            transaction = TransactionFrom.Quest_Daily;
        } else {
            transaction = TransactionFrom.Quest_Weekly;
        }
        ScriptLoader.getInstance().consumerScript("BonusScript", (IBonusScript script) ->
                script.addBonusNote(transaction, player, rewardCurrency.currencyId, rewardCurrency.amount));
    }

}
