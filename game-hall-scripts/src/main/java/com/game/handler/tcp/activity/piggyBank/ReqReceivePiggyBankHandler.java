package com.game.handler.tcp.activity.piggyBank;

import com.game.c_entity.merchant.C_PiggyBank;
import com.game.dao.player.PlayerDao;
import com.game.engine.io.handler.TcpHandler;
import com.game.engine.log.GameLog;
import com.game.engine.math.BigDecimalUtils;
import com.game.engine.script.IHandlerEntity;
import com.game.engine.script.ScriptLoader;
import com.game.engine.util.misc.Tuple2;
import com.game.engine.utils.GuidGeneratorUtils;
import com.game.engine.utils.MsgUtil;
import com.game.engine.utils.TimeUtil;
import com.game.entity.player.Player;
import com.game.entity.player.activity.piggyBank.PiggyBankInfo;
import com.game.enums.ErrorCode;
import com.game.enums.RewardReason;
import com.game.enums.TransactionFrom;
import com.game.enums.TurnoverReason;
import com.game.hall.main.HallServer;
import com.game.hall.mrg.ActivityMrg;
import com.game.hall.mrg.CommonMrg;
import com.game.hall.mrg.DataHallMrg;
import com.game.hall.mrg.MerchantData;
import com.game.hall.mrg.currency.CurrencyMrg;
import com.game.hall.mrg.currency.RewardRequest;
import com.game.hall.mrg.player.PlayerMrg;
import com.game.hall.script.IBonusScript;
import com.game.hall.script.IPlayerScript;
import com.game.manager.EntityDaoMrg;
import com.proto.ActivityMessage;
import com.proto.MIDMessage;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

@IHandlerEntity(mid = MIDMessage.MID.ReqReceivePiggyBank_VALUE, msg = ActivityMessage.ReqReceivePiggyBankMessage.class)
public class ReqReceivePiggyBankHandler extends TcpHandler {
    private static final Logger LOGGER = LoggerFactory.getLogger(ReqReceivePiggyBankHandler.class);

    @Override
    public void run() {
        final ActivityMessage.ResReceivePiggyBankMessage.Builder res = ActivityMessage.ResReceivePiggyBankMessage.newBuilder();
        res.setMsgID(MIDMessage.MID.ResReceivePiggyBank_VALUE);
        try {
            final ActivityMessage.ReqReceivePiggyBankMessage req = (ActivityMessage.ReqReceivePiggyBankMessage) getMessage();
            final int day = req.getDay();

            final Player player = PlayerMrg.getInstance().getOnlinePlayer(pid, this.getClass().getSimpleName());
            if (player == null) {
                res.setError(ErrorCode.Player_Offline.getCode());
                MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                return;
            }

            final MerchantData merchantData = DataHallMrg.getInstance().findMerchantData(this.getClass().getSimpleName(), player.getBusiness_no());
            if (merchantData == null) {
                res.setError(ErrorCode.Data_Error.getCode());
                MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                return;
            }

            final C_PiggyBank c_piggyBank = merchantData.findC_PiggyBank(this.getClass().getSimpleName(), ActivityMrg.PIGGY_BANK);
            if (c_piggyBank == null) {
                res.setError(ErrorCode.Data_Error.getCode());
                MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                return;
            }

            final PiggyBankInfo piggyBankInfo = player.getPiggyBankInfo();
            final double recharge = piggyBankInfo.getRechargeAmountMap().getOrDefault(player.getCurrencyId(), 0d);
            final C_PiggyBank.PiggyInfo c_piggyInfo = findPiggyBankInfo(recharge, c_piggyBank);
            if (c_piggyInfo == null) {
                res.setError(ErrorCode.Data_Error.getCode());
                MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                return;
            }

            //升档重置
            double totalBonus = 0;
            if (c_piggyInfo.getId() > piggyBankInfo.getGearId()) {
                totalBonus = skipReward(piggyBankInfo.getGearId(), recharge, c_piggyBank);
                piggyBankInfo.getReceive().clear();
                piggyBankInfo.setReceiveTime(0);
            }

            final C_PiggyBank.PiggyReward piggyReward = findPiggyReward(day, c_piggyInfo.getPiggyRewards());
            if (piggyReward == null) {
                res.setError(ErrorCode.Data_Error.getCode());
                MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                return;
            }

            if (piggyBankInfo.getReceive().contains(day)) {
                res.setError(ErrorCode.Reward_Received.getCode());
                MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                return;
            }

            piggyBankInfo.setGearId(c_piggyInfo.getId());
            piggyBankInfo.getReceive().add(day);
            piggyBankInfo.setCurrDay(day);
            if (day > 0) {
                piggyBankInfo.setReceiveTime(TimeUtil.currentTimeMillis());
            }
            EntityDaoMrg.getInstance().getDao(PlayerDao.class)
                    .updatePiggyBankInfo(player.getPlayerId(), piggyBankInfo);

            final RewardRequest rewardRequest = new RewardRequest();
            if (totalBonus > 0) {
                rewardRequest.addCurrency(piggyReward.currencyId, totalBonus);
            }
            rewardRequest.addCurrency(piggyReward.currencyId, piggyReward.bonus);
            final RewardReason rewardReason = RewardReason.PiggyBank;
            rewardReason.setSource(day + "");
            CurrencyMrg.getInstance().rewardNoTest(player, rewardRequest.getBonusCurrencyMap(), rewardReason);

            //TODO 打码
//            final WithdrawStandard withdrawStandard = player.getWithdrawStandard(piggyReward.currencyId);
//            withdrawStandard.incDrawStandard(BigDecimalUtils.mul(piggyReward.bonus, c_piggyBank.getTurnoverMul(), 4));
            final TurnoverReason turnoverReason = TurnoverReason.PiggyBank;
            turnoverReason.setSource(day + "");
            ScriptLoader.getInstance().consumerScript("PlayerWithdrawStandardScript",
                    (IPlayerScript script) -> script.drawStandard(player, turnoverReason, piggyReward.currencyId, piggyReward.bonus,
                            BigDecimalUtils.mul(piggyReward.bonus, c_piggyBank.getTurnoverMul(), 4)));

            if (!rewardRequest.getCurrencyMap().isEmpty()) {
                final Tuple2<Integer, Double> tuple2 = rewardRequest.getReward();
                ScriptLoader.getInstance().consumerScript("BonusScript",
                        (IBonusScript script) -> script.addBonusNote(TransactionFrom.PiggyBank, player, tuple2.getFirst(), tuple2.getSecond()));
            }

            res.setRewardShow(CommonMrg.buildDItemShow(piggyReward.currencyId, piggyReward.bonus));
            MsgUtil.responseHttp(res.build(), res.getMsgID(), session);

            //日志
            final GameLog playerActivityLog = new GameLog("platform_playerPiggyBankLog");
            playerActivityLog.append("number", GuidGeneratorUtils.generateOrderId())
                    .append("business_no", player.getBusiness_no())
                    .append("site", player.getWebSite())
                    .append("channel", player.getChannel())
                    .append("playerId", player.getPlayerId())
                    .append("playerName", player.getPlayerName())
                    .append("language", player.getLanguage())
                    .append("agentId", player.getAgentId())
                    .append("channelId", player.getChannelId())
                    .append("region", player.getRegisterRegion())
                    .append("activityId", ActivityMrg.PIGGY_BANK)
                    .append("activityUniqueId", c_piggyBank.getC_id())
                    .append("day", day)// 0开始
                    .append("level", piggyReward.getId())
                    .append("currencyId", piggyReward.getCurrencyId())
                    .append("reward", piggyReward.bonus)
                    .append("logTime", TimeUtil.currentTimeMillis());
            HallServer.getInstance().getLogProducerMrg().send(playerActivityLog);
        } catch (Exception e) {
            LOGGER.error("ReqReceivePiggyBankHandler", e);
            res.setError(ErrorCode.Internal_Server_Error.getCode());
            MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
        }
    }

    private double skipReward(int gearId, double recharge, C_PiggyBank c_piggyBank) {
        double totalBonus = 0;
        double lastBonus = 0;
        final List<C_PiggyBank.PiggyInfo> piggyInfoList = new ArrayList<>(c_piggyBank.getPiggyInfoMap().values());
        for (C_PiggyBank.PiggyInfo piggyInfo : piggyInfoList) {
            if (piggyInfo.getId() <= gearId) {
                continue;
            }
            if (recharge >= piggyInfo.getRecharge()) {
                totalBonus += piggyInfo.getPiggyRewards().getFirst().bonus;
                lastBonus = piggyInfo.getPiggyRewards().getFirst().bonus;
            }
        }
        return BigDecimalUtils.sub(totalBonus, lastBonus, 2);
    }

    private C_PiggyBank.PiggyInfo findPiggyBankInfo(double recharge, C_PiggyBank c_piggyBank) {
        final List<C_PiggyBank.PiggyInfo> piggyInfoList = new ArrayList<>(c_piggyBank.getPiggyInfoMap().values());
        Collections.reverse(piggyInfoList);
        for (C_PiggyBank.PiggyInfo piggyInfo : piggyInfoList) {
            if (recharge >= piggyInfo.getRecharge()) {
                return piggyInfo;
            }
        }
        return null;
    }

    private C_PiggyBank.PiggyReward findPiggyReward(int day, List<C_PiggyBank.PiggyReward> piggyRewards) {
        for (C_PiggyBank.PiggyReward reward : piggyRewards) {
            if (reward.id == day) {
                return reward;
            }
        }
        return null;
    }

}
