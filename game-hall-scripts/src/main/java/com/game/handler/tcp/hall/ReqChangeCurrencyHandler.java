package com.game.handler.tcp.hall;

import com.game.dao.player.PlayerDao;
import com.game.engine.io.handler.TcpHandler;
import com.game.engine.script.IHandlerEntity;
import com.game.engine.utils.MsgUtil;
import com.game.entity.player.Player;
import com.game.entity.player.PlayerFields;
import com.game.enums.ErrorCode;
import com.game.hall.mrg.player.PlayerMrg;
import com.game.manager.EntityDaoMrg;
import com.proto.HallMessage;
import com.proto.MIDMessage;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;


@IHandlerEntity(mid = MIDMessage.MID.ReqChangeCurrency_VALUE, msg = HallMessage.ReqChangeCurrencyMessage.class)
public class ReqChangeCurrencyHandler extends TcpHandler {
    private static final Logger LOGGER = LoggerFactory.getLogger(ReqChangeCurrencyHandler.class);

    @Override
    public void run() {
        final HallMessage.ResChangeCurrencyMessage.Builder res = HallMessage.ResChangeCurrencyMessage.newBuilder();
        res.setMsgID(MIDMessage.MID.ResChangeCurrency_VALUE);

        final Player player = PlayerMrg.getInstance().getOnlinePlayer(pid, this.getClass().getSimpleName());
        if (player == null) {
            res.setError(ErrorCode.Player_Offline.getCode());
            MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
            return;
        }

        final HallMessage.ReqChangeCurrencyMessage req = (HallMessage.ReqChangeCurrencyMessage) getMessage();
        final int currencyId = req.getCurrency();
        player.setCurrencyId(currencyId);
        EntityDaoMrg.getInstance().getDao(PlayerDao.class)
                .updatePlayerField(pid, PlayerFields.currencyId, currencyId);

        res.setCurrency(currencyId);
        MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
    }

}
