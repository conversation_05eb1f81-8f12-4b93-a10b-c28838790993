package com.game.handler.tcp.account;

import com.game.dao.account.AccountDao;
import com.game.dao.player.PlayerDao;
import com.game.engine.io.handler.TcpHandler;
import com.game.engine.io.redis.RedisPoolManager;
import com.game.engine.script.IHandlerEntity;
import com.game.engine.utils.MD5;
import com.game.engine.utils.MsgUtil;
import com.game.entity.account.Account;
import com.game.entity.account.AccountFields;
import com.game.entity.account.email.Email;
import com.game.entity.account.phone.Phone;
import com.game.entity.player.Player;
import com.game.entity.player.PlayerFields;
import com.game.enums.ErrorCode;
import com.game.enums.ThreeParty;
import com.game.enums.VerifyCode;
import com.game.enums.redis.RedisLogin;
import com.game.hall.mrg.player.PlayerMrg;
import com.game.manager.EntityDaoMrg;
import com.game.utils.VirtualThreadUtils;
import com.proto.HallMessage;
import com.proto.MIDMessage;
import io.netty.util.internal.StringUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.mongodb.core.query.Update;

import java.util.Objects;

@IHandlerEntity(mid = MIDMessage.MID.ReqAddAccount_VALUE, msg = HallMessage.ReqAddAccountMessage.class)
public class ReqAddAccountHandler extends TcpHandler {
    private static final Logger LOGGER = LoggerFactory.getLogger(ReqAddAccountHandler.class);

    @Override
    public void run() {
        final HallMessage.ResAddAccountMessage.Builder res = HallMessage.ResAddAccountMessage.newBuilder();
        res.setMsgID(MIDMessage.MID.ResAddAccount_VALUE);
        try {
            final Player player = PlayerMrg.getInstance().getOnlinePlayer(pid, this.getClass().getSimpleName());
            if (player == null) {
                res.setError(ErrorCode.Player_Offline.getCode());
                MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                return;
            }

            final HallMessage.ReqAddAccountMessage req = (HallMessage.ReqAddAccountMessage) getMessage();
            final int threeParty = req.getThreeParty();
            final String areaCode = req.getAreaCode();
            final String reqAccount = req.getAccount();
            final String password = req.getPassword();
            final String confirmPassWord = req.getConfirmPassWord();
            final String verification = req.getVerification();

            if (confirmPassWord.length() < 6 || confirmPassWord.length() > 16) {
                res.setError(ErrorCode.PassWard_Length.getCode());
                MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                return;
            }

//            if (!password.matches("^[a-zA-Z0-9]+$")) {
//                res.setError(ErrorCode.PassWord_Format.getCode());
//                MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
//                return;
//            }

            if (!Objects.equals(password, confirmPassWord)) {
                res.setError(ErrorCode.PassWord_Error.getCode());
                MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                return;
            }

            String ac = "";
            if (threeParty == ThreeParty.Phone.getThreeParty()) {
                ac = areaCode + "-" + reqAccount;
            } else {
                ac = reqAccount;
            }

            final String finalAc = ac;
            final String accountId = RedisPoolManager.getInstance().function(jedis ->
                    jedis.sync().hget(RedisLogin.Platform_LG_Map_AccountID.getKey(player.getBusiness_no(), threeParty), finalAc));
            if (!StringUtil.isNullOrEmpty(accountId)) {
                LOGGER.warn("account：{}，already registered", finalAc);
                res.setError(ErrorCode.Account_AlreadyExists.getCode());
                MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                return;
            }

            //邮箱验证码
            final String verifyCode = RedisPoolManager.getInstance().function(jedis ->
                    jedis.sync().get(RedisLogin.Platform_LG_Account_VerifyCode.getKey(player.getBusiness_no(), VerifyCode.Add.getType(), finalAc)));
            if (StringUtil.isNullOrEmpty(verification) || !verification.equals(verifyCode)) {
                LOGGER.warn("account：{}，verifyCode，{}-{}，", finalAc, verification, verifyCode);
                res.setError(ErrorCode.VerifyCode_Error.getCode());
                MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                return;
            }

            final Account account = PlayerMrg.getInstance().findDbAccount(pid);
            if (account == null) {
                res.setError(ErrorCode.Account_NotExist.getCode());
                MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                return;
            }

            account.setPassword(MD5.MD5Encode(password));
            EntityDaoMrg.getInstance().getDao(AccountDao.class).updateAccountField(account.getAccountId(), AccountFields.password, account.getPassword());

            if (threeParty == ThreeParty.Email.getThreeParty()) {
                final Email emailInfo = account.getEmailInfo();
                emailInfo.setEmail(finalAc);
                EntityDaoMrg.getInstance().getDao(AccountDao.class).updateEmail(account.getAccountId(), emailInfo);

                player.setEmailBind(true);
                player.setEmail(finalAc);
                final Update update = new Update();
                update.set(PlayerFields.emailBind, player.isEmailBind())
                        .set(PlayerFields.email, player.getEmail());
                EntityDaoMrg.getInstance().getDao(PlayerDao.class)
                        .updatePlayer(player.getPlayerId(), update);
            } else {
                final Phone phoneInfo = account.getPhoneInfo();
                phoneInfo.setAreaCode(areaCode);
                phoneInfo.setPhone(reqAccount);
                EntityDaoMrg.getInstance().getDao(AccountDao.class).updatePhone(account.getAccountId(), phoneInfo);

                player.setPhoneBind(true);
                player.setAreaCode(areaCode);
                player.setPhone(reqAccount);
                final Update update = new Update();
                update.set(PlayerFields.phoneBind, player.isPhoneBind())
                        .set(PlayerFields.areaCode, player.getAreaCode())
                        .set(PlayerFields.phone, player.getPhone());
                EntityDaoMrg.getInstance().getDao(PlayerDao.class)
                        .updatePlayer(player.getPlayerId(), update);
            }

            RedisPoolManager.getInstance().executeAsync(commands ->
                    commands.hset(RedisLogin.Platform_LG_Map_AccountID.getKey(player.getBusiness_no(), threeParty), finalAc, pid + "")
            );

            res.setAccount(reqAccount)
                    .setAreaCode(areaCode)
                    .setThreeParty(threeParty);
            MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
        } catch (Exception e) {
            LOGGER.error("ReqAddAccountHandler", e);
            res.setError(ErrorCode.Internal_Server_Error.getCode());
            MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
        }
    }
}
