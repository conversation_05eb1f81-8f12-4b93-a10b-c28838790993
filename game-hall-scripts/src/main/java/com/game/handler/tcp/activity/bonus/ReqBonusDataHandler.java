package com.game.handler.tcp.activity.bonus;

import com.game.c_entity.merchant.C_Activity;
import com.game.c_entity.merchant.C_GameApi;
import com.game.c_entity.middleplatform.C_BaseExchangeRate;
import com.game.c_entity.merchant.C_LuckSpin;
import com.game.c_entity.merchant.C_VipClub;
import com.game.engine.io.handler.TcpHandler;
import com.game.engine.math.BigDecimalUtils;
import com.game.engine.script.IHandlerEntity;
import com.game.engine.utils.MsgUtil;
import com.game.entity.player.FreeGameInfo;
import com.game.entity.player.GameInfo;
import com.game.entity.player.Player;
import com.game.entity.player.activity.luckSpin.LuckCondition;
import com.game.entity.player.activity.luckSpin.LuckSpinData;
import com.game.entity.player.bonus.BonusInfo;
import com.game.entity.player.bonus.BonusProcessInfo;
import com.game.entity.player.quest.QuestInfo;
import com.game.entity.player.vip.VipClub;
import com.game.entity.player.activity.luckSpin.LuckSpinInfo;
import com.game.enums.BonusDetail;
import com.game.enums.ErrorCode;
import com.game.hall.mrg.ActivityMrg;
import com.game.hall.mrg.DataHallMrg;
import com.game.hall.mrg.CommonMrg;
import com.game.hall.mrg.MerchantData;
import com.game.hall.mrg.player.PlayerMrg;
import com.proto.ActivityMessage;
import com.proto.MIDMessage;
import io.netty.util.internal.StringUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.*;

@IHandlerEntity(mid = MIDMessage.MID.ReqBonusData_VALUE, msg = ActivityMessage.ReqBonusDataMessage.class)
public class ReqBonusDataHandler extends TcpHandler {
    private static final Logger LOGGER = LoggerFactory.getLogger(ReqBonusDataHandler.class);

    @Override
    public void run() {
        final ActivityMessage.ResBonusDataMessage.Builder res = ActivityMessage.ResBonusDataMessage.newBuilder();
        res.setMsgID(MIDMessage.MID.ResBonusData_VALUE);
        try {
            final Player player = PlayerMrg.getInstance().getOnlinePlayer(pid, this.getClass().getSimpleName());
            if (player == null) {
                res.setError(ErrorCode.Player_Offline.getCode());
                MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                return;
            }

            final MerchantData merchantData = DataHallMrg.getInstance().findMerchantData(this.getClass().getSimpleName(), player.getBusiness_no());
            if (merchantData == null) {
                res.setError(ErrorCode.Data_Error.getCode());
                MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                return;
            }

            final VipClub vipClub = player.getVipClub();
            final BonusInfo bonusInfo = player.getBonusInfo();

            final List<C_Activity> c_activityList = merchantData.findC_ActivityList(this.getClass().getSimpleName(), ActivityMrg.MANY_DEPOSIT);
            if (!c_activityList.isEmpty()) {
                final List<C_Activity> activityList = new ArrayList<>(c_activityList);
                activityList.sort(Comparator.comparingInt(C_Activity::getActivitySubType));
                for (C_Activity c_activity : activityList) {
                    final List<C_Activity.RewardInfo> recharges = c_activity.getRewardsMap().get(player.getCurrencyId());
                    if (recharges == null || recharges.isEmpty()) {
                        continue;
                    }
                    res.addDepositBonus(buildDepositBonus(c_activity, recharges.getLast()));
                }
            }
            res.setCurDeposit(player.getTotalRechargeTimes())
                    .setQuests(buildQuests(player))
                    .setLuckySpin(buildLuckySpin(player, merchantData))
                    .setLevelUpRewards(buildLevelUpRewards(bonusInfo))
                    .setRecharge(buildRecharge(bonusInfo, vipClub, merchantData))
                    .setDayCashBack(buildDayCashBack(vipClub))
                    .setWeeklyCashBack(buildWeeklyCashBack(bonusInfo, vipClub, merchantData))
                    .setMonthlyCashBack(buildMonthlyCashBack(bonusInfo, vipClub, merchantData));

            final ActivityMessage.FreeGameInfo.Builder freeGameInfo = ActivityMessage.FreeGameInfo.newBuilder();
            for (Map.Entry<Integer, FreeGameInfo> entry : player.getFreeGameInfoMap().entrySet()) {
                final int currencyId = entry.getKey();
                final Map<Integer, GameInfo> gameInfoMap = entry.getValue().getGameInfoMap();

                for (Map.Entry<Integer, GameInfo> gameInfoEntry : gameInfoMap.entrySet()) {
                    if (gameInfoEntry.getKey() == 0) {
                        continue;
                    }
                    final C_GameApi c_gameApi = merchantData.findC_GameApi(this.getClass().getSimpleName(), gameInfoEntry.getKey());
                    if (c_gameApi == null) {
                        continue;
                    }
                    if (!c_gameApi.setLanguageGameApiData(player.getLanguage())) {
                        continue;
                    }
                    freeGameInfo.setCurrencyId(currencyId)
                            .setGameId(c_gameApi.getGameId())
                            .setGameName(c_gameApi.getGameName())
                            .setFreeTimes(gameInfoEntry.getValue().getFreeTimes());
                    res.addFreeGameList(freeGameInfo.build());
                }
            }
            MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
        } catch (Exception e) {
            LOGGER.error("ReqBonusDataHandler", e);
            res.setError(ErrorCode.Internal_Server_Error.getCode());
            MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
        }
    }

    private ActivityMessage.Quests buildQuests(Player player) {
        final QuestInfo questInfo = player.getQuestInfo();
        final int dailyNum = (int) questInfo.getQuestInfoMap().values().stream().filter(singleQuestInfo -> singleQuestInfo.getQuestType() == 1).count();
        final int weeklyNum = questInfo.getQuestInfoMap().size() - dailyNum;

        final int dailyProgress = (int) questInfo.getQuestInfoMap().values().stream().filter(singleQuestInfo -> singleQuestInfo.getQuestType() == 1 && singleQuestInfo.getState() >= 2).count();
        final int weeklyProgress = (int) questInfo.getQuestInfoMap().values().stream().filter(singleQuestInfo -> singleQuestInfo.getQuestType() == 2 && singleQuestInfo.getState() >= 2).count();
        final ActivityMessage.Quests.Builder quests = ActivityMessage.Quests.newBuilder();
        quests.setDailyProgress(dailyProgress)
                .setDailyNum(dailyNum)
                .setWeeklyProgress(weeklyProgress)
                .setWeeklyNum(weeklyNum);
        return quests.build();
    }

    private ActivityMessage.DepositBonus buildDepositBonus(C_Activity c_activity, C_Activity.RewardInfo recharge) {
        final ActivityMessage.DepositBonus.Builder depositBonus = ActivityMessage.DepositBonus.newBuilder();
        depositBonus.setActivityId(c_activity.getActivityId())
                .setCId(c_activity.getC_id())
                .setDepositType(c_activity.getActivitySubType())
                .setCurrencyId(recharge.getCurrencyId())
                .setBonus(recharge.getRewardType())
                .setExtra(recharge.getReward())
                .setMinimum(recharge.getMin())
                .setMaximum(recharge.getMax());
        return depositBonus.build();
    }

    private ActivityMessage.LuckySpin buildLuckySpin(Player player, MerchantData merchantData) {
        final LuckSpinInfo luckSpinInfo = player.getLuckSpinInfo();
        final List<C_LuckSpin> c_vipLuckSpinList = merchantData.findC_VipSpinList(this.getClass().getSimpleName(), ActivityMrg.LUCK_SPIN_VIP);

        final List<C_LuckSpin> c_luckSpins = new ArrayList<>(c_vipLuckSpinList);
        final ActivityMessage.LuckySpin.Builder luckySpin = ActivityMessage.LuckySpin.newBuilder();
        if (!c_luckSpins.isEmpty()) {
            c_luckSpins.sort(Comparator.comparingInt(C_LuckSpin::getSubType));
            final C_LuckSpin c_vipLuckSpin = c_luckSpins.getFirst();
            luckySpin.setVipSpinLimit(c_vipLuckSpin.getVipLevel());
        }
        final C_LuckSpin c_dailyLuckSpin = merchantData.findC_LuckSpin(this.getClass().getSimpleName(), ActivityMrg.LUCK_SPIN_DAILY);
        if (c_dailyLuckSpin != null) {
            final C_LuckSpin.ExtraCondition extraCondition = c_dailyLuckSpin.getExtraCondition();
            luckySpin.setDailyWager(extraCondition.getBetUsdAmount());
        }
        final LuckSpinData luckSpinData = luckSpinInfo.getLuckSpinData(ActivityMrg.LUCK_SPIN_DAILY);
        if (luckSpinData != null) {
            final LuckCondition luckCondition = luckSpinData.getLuckCondition();
            if (luckCondition != null) {
                for (Map.Entry<Integer, Double> entry : luckCondition.getWageredMap().entrySet()) {
                    luckySpin.addCurrentWager(CommonMrg.buildDItemShow(entry.getKey(), entry.getValue()));
                }
            }
        }
        return luckySpin.build();
    }

    private ActivityMessage.LevelUpRewards buildLevelUpRewards(BonusInfo bonusInfo) {
        final BonusProcessInfo bonusProcessInfo = bonusInfo.getBonusProcessInfo(BonusDetail.LevelUpBonus.getType());
        final ActivityMessage.LevelUpRewards.Builder levelUpRewards = ActivityMessage.LevelUpRewards.newBuilder();
        for (Map.Entry<Integer, Double> entry : bonusProcessInfo.getRewardMap().entrySet()) {
            levelUpRewards.addRewards(CommonMrg.buildDItemShow(entry.getKey(), entry.getValue()));
        }
        return levelUpRewards.build();
    }

    private ActivityMessage.Recharge buildRecharge(BonusInfo bonusInfo, VipClub vipClub, MerchantData merchantData) {
        final ActivityMessage.Recharge.Builder recharge = ActivityMessage.Recharge.newBuilder();

        final boolean isManyCurrency = merchantData.getC_currencyMap().size() > 1;

        final String rechargeLimit = merchantData.findC_GlobalValue(this.getClass().getSimpleName(), "rechargeLimit");
        final int vipRechargeLimit = Integer.parseInt(StringUtil.isNullOrEmpty(rechargeLimit) ? "0" : rechargeLimit);
        recharge.setVipLevelLimit(vipRechargeLimit);

        final BonusProcessInfo bonusProcessInfo = bonusInfo.getBonusProcessInfo(BonusDetail.Recharge.getType());
        double totalRechargeWager = 0;
        for (Map.Entry<Integer, Double> entry : bonusProcessInfo.getWagerMap().entrySet()) {
            if (isManyCurrency) {
                final C_BaseExchangeRate c_baseExchangeRate = DataHallMrg.getInstance().findC_BaseExchangeRate(this.getClass().getSimpleName(), entry.getKey());
                if (c_baseExchangeRate == null) {
                    continue;
                }
                totalRechargeWager = BigDecimalUtils.add(totalRechargeWager, BigDecimalUtils.mul(entry.getValue(), c_baseExchangeRate.getExchangeRate(), 9), 9);
            } else {
                totalRechargeWager = BigDecimalUtils.add(totalRechargeWager, entry.getValue(), 9);
            }
        }
        for (Map.Entry<Integer, Double> entry : bonusProcessInfo.getRewardMap().entrySet()) {
            recharge.addReward(CommonMrg.buildDItemShow(entry.getKey(), entry.getValue()));
        }
        final int vipLevel = Math.max(vipClub.getVipLevel(), vipRechargeLimit);
        final C_VipClub c_vipClub = merchantData.findC_VipClub(this.getClass().getSimpleName(), vipLevel);
        if (c_vipClub != null) {
            C_VipClub.Tire tire = c_vipClub.getTire(totalRechargeWager);
            if (tire != null) {
                recharge.setCurrentTire(tire.getCurrentTier());
                for (C_VipClub.Tire t : c_vipClub.getTireList()) {
                    recharge.addTireList(buildTire(t));
                }
            }
        }
        return recharge.build();
    }

    private ActivityMessage.DayCashBack buildDayCashBack(VipClub vipClub) {
        final ActivityMessage.DayCashBack.Builder dayCashBack = ActivityMessage.DayCashBack.newBuilder();
        for (Map.Entry<Integer, Double> entry : vipClub.getPlayerWageredMap().entrySet()) {
            dayCashBack.addCurrentWager(CommonMrg.buildDItemShow(entry.getKey(), entry.getValue()));
        }
        for (Map.Entry<Integer, Double> entry : vipClub.getLastDayBackCash().entrySet()) {
            dayCashBack.addReward(CommonMrg.buildDItemShow(entry.getKey(), entry.getValue()));
        }
        return dayCashBack.build();
    }

    private ActivityMessage.WeeklyCashBack buildWeeklyCashBack(BonusInfo bonusInfo, VipClub vipClub, MerchantData merchantData) {
        final String weeklyCashBackLimit = merchantData.findC_GlobalValue(this.getClass().getSimpleName(), "weeklyCashbackLimit");
        final int weeklyLimit = Integer.parseInt(StringUtil.isNullOrEmpty(weeklyCashBackLimit) ? "0" : weeklyCashBackLimit);

        final ActivityMessage.WeeklyCashBack.Builder weeklyCashBack = ActivityMessage.WeeklyCashBack.newBuilder();
        final int vipLevel = Math.max(vipClub.getVipLevel(), weeklyLimit);
        final C_VipClub c_vipClub = merchantData.findC_VipClub(this.getClass().getSimpleName(), vipLevel);
        if (c_vipClub != null) {
            final String weeklyWagerRange = c_vipClub.getWeeklyWagerRange();
            if (!StringUtil.isNullOrEmpty(weeklyWagerRange)) {
                weeklyCashBack.setMinWager(Double.parseDouble(weeklyWagerRange.split("-")[0]))
                        .setMaxWager(Double.parseDouble(weeklyWagerRange.split("-")[1]));
            }
        }
        weeklyCashBack.setEndTime(bonusInfo.getWeeklyEndTime())
                .setVipLevelLimit(weeklyLimit);
        final BonusProcessInfo bonusProcessInfo = bonusInfo.getBonusProcessInfo(BonusDetail.WeeklyCashBack.getType());
        for (Map.Entry<Integer, Double> entry : bonusProcessInfo.getWagerMap().entrySet()) {
            weeklyCashBack.addCurrentWager(CommonMrg.buildDItemShow(entry.getKey(), entry.getValue()));
        }
        for (Map.Entry<Integer, Double> entry : bonusProcessInfo.getRewardMap().entrySet()) {
            weeklyCashBack.addReward(CommonMrg.buildDItemShow(entry.getKey(), entry.getValue()));
        }
        return weeklyCashBack.build();
    }

    private ActivityMessage.MonthlyCashBack buildMonthlyCashBack(BonusInfo bonusInfo, VipClub vipClub, MerchantData merchantData) {
        final String monthlyCashBackLimit = merchantData.findC_GlobalValue(this.getClass().getSimpleName(), "monthlyCashbackLimit");
        final int monthlyLimit = Integer.parseInt(StringUtil.isNullOrEmpty(monthlyCashBackLimit) ? "0" : monthlyCashBackLimit);

        final ActivityMessage.MonthlyCashBack.Builder monthlyCashBack = ActivityMessage.MonthlyCashBack.newBuilder();
        final int vipLevel = Math.max(vipClub.getVipLevel(), monthlyLimit);
        final C_VipClub c_vipClub = merchantData.findC_VipClub(this.getClass().getSimpleName(), vipLevel);
        if (c_vipClub != null) {
            final String monthlyWagerRange = c_vipClub.getMonthlyWagerRange();
            if (!StringUtil.isNullOrEmpty(monthlyWagerRange)) {
                monthlyCashBack.setMinWager(Double.parseDouble(monthlyWagerRange.split("-")[0]))
                        .setMaxWager(Double.parseDouble(monthlyWagerRange.split("-")[1]));
            }
        }

        monthlyCashBack.setEndTime(bonusInfo.getMonthlyEndTime())
                .setVipLevelLimit(monthlyLimit);
        final BonusProcessInfo bonusProcessInfo = bonusInfo.getBonusProcessInfo(BonusDetail.MonthlyCashBack.getType());
        for (Map.Entry<Integer, Double> entry : bonusProcessInfo.getWagerMap().entrySet()) {
            monthlyCashBack.addCurrentWager(CommonMrg.buildDItemShow(entry.getKey(), entry.getValue()));
        }
        for (Map.Entry<Integer, Double> entry : bonusProcessInfo.getRewardMap().entrySet()) {
            monthlyCashBack.addReward(CommonMrg.buildDItemShow(entry.getKey(), entry.getValue()));
        }
        return monthlyCashBack.build();
    }

    private ActivityMessage.Tire buildTire(C_VipClub.Tire tire) {
        return ActivityMessage.Tire.newBuilder()
                .setStartWager(tire.getWagerMin())
                .setEndWager(tire.getWagerMax())
                .setRechargeRate(tire.getRate())
                .setCurrencyTire(tire.getCurrentTier())
                .build();
    }

}
