package com.game.handler.tcp.activity.bonus;

import com.game.c_entity.merchant.C_RedemptionCode;
import com.game.dao.player.PlayerDao;
import com.game.engine.io.handler.TcpHandler;
import com.game.engine.io.redis.RedisPoolManager;
import com.game.engine.log.GameLog;
import com.game.engine.math.BigDecimalUtils;
import com.game.engine.script.IHandlerEntity;
import com.game.engine.script.ScriptLoader;
import com.game.engine.utils.GuidGeneratorUtils;
import com.game.engine.utils.MsgUtil;
import com.game.engine.utils.TimeUtil;
import com.game.entity.player.Player;
import com.game.entity.player.PlayerFields;
import com.game.enums.ErrorCode;
import com.game.enums.RewardReason;
import com.game.enums.TransactionFrom;
import com.game.enums.TurnoverReason;
import com.game.enums.redis.RedisAllGame;
import com.game.hall.main.HallServer;
import com.game.hall.mrg.DataHallMrg;
import com.game.hall.mrg.MerchantData;
import com.game.hall.mrg.currency.CurrencyMrg;
import com.game.hall.mrg.currency.RewardRequest;
import com.game.hall.mrg.player.PlayerMrg;
import com.game.hall.script.IBonusScript;
import com.game.hall.script.IPlayerScript;
import com.game.manager.EntityDaoMrg;
import com.game.utils.VirtualThreadUtils;
import com.proto.ActivityMessage;
import com.proto.MIDMessage;
import io.netty.util.internal.StringUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@IHandlerEntity(mid = MIDMessage.MID.ReqRedemptionCodeReward_VALUE, msg = ActivityMessage.ReqRedemptionCodeRewardMessage.class)
public class ReqRedemptionCodeRewardHandler extends TcpHandler {
    private static final Logger LOGGER = LoggerFactory.getLogger(ReqRedemptionCodeRewardHandler.class);

    @Override
    public void run() {
        final ActivityMessage.ResRedemptionCodeRewardMessage.Builder res = ActivityMessage.ResRedemptionCodeRewardMessage.newBuilder();
        res.setMsgID(MIDMessage.MID.ResRedemptionCodeReward_VALUE);
        try {
            final Player player = PlayerMrg.getInstance().getOnlinePlayer(pid, this.getClass().getSimpleName());
            if (player == null) {
                res.setError(ErrorCode.Player_Offline.getCode());
                MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                return;
            }

            final ActivityMessage.ReqRedemptionCodeRewardMessage req = (ActivityMessage.ReqRedemptionCodeRewardMessage) getMessage();
            final String redemptionCode = req.getRedemptionCode();

            final MerchantData merchantData = DataHallMrg.getInstance().findMerchantData(this.getClass().getSimpleName(), player.getBusiness_no());
            if (merchantData == null) {
                res.setError(ErrorCode.Data_Error.getCode());
                MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                return;
            }

            final C_RedemptionCode c_redemptionCode = merchantData.findC_RedemptionCode(this.getClass().getSimpleName(), redemptionCode);
            if (c_redemptionCode == null) {
                res.setError(ErrorCode.Invalid_Redemption_Code.getCode());
                MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                return;
            }

            if (TimeUtil.currentTimeMillis() >= c_redemptionCode.getExpiredTime()) {
                res.setError(ErrorCode.Invalid_Redemption_Code.getCode());
                MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                return;
            }

            //指定玩家
            if (c_redemptionCode.getRedeemType() == 2 && !c_redemptionCode.getPlayerIds().contains(pid)) {
                res.setError(ErrorCode.Invalid_Redemption_Code.getCode());
                MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                return;
            }

            //指定渠道
            if (c_redemptionCode.getRedeemType() == 3 && !c_redemptionCode.getChannelIds().contains(player.getChannelId())) {
                res.setError(ErrorCode.Invalid_Redemption_Code.getCode());
                MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                return;
            }

            if (player.getRedemptionCodes().contains(redemptionCode)) {
                res.setError(ErrorCode.Invalid_Redemption_Code.getCode());
                MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                return;
            }

            final String redemptionCodeTimes = RedisPoolManager.getInstance().function(jedis ->
                    jedis.sync().get(RedisAllGame.Platform_All_RedemptionCode.getKey(player.getBusiness_no(), redemptionCode)));

            final long times = StringUtil.isNullOrEmpty(redemptionCodeTimes) ? 0 : Long.parseLong(redemptionCodeTimes);

            //复用
            if (c_redemptionCode.getType() == 1 && times >= c_redemptionCode.getAvailableTimes()) {
                res.setError(ErrorCode.Invalid_Redemption_Code.getCode());
                MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                return;
            }

            //单次
            if (c_redemptionCode.getType() == 2 && times > 0) {
                res.setError(ErrorCode.Invalid_Redemption_Code.getCode());
                MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                return;
            }

            player.getRedemptionCodes().add(redemptionCode);
            EntityDaoMrg.getInstance().getDao(PlayerDao.class)
                    .updatePlayerField(player.getPlayerId(), PlayerFields.redemptionCodes, player.getRedemptionCodes());

            RedisPoolManager.getInstance().executeAsync(commands ->
                    commands.incrby(RedisAllGame.Platform_All_RedemptionCode.getKey(player.getBusiness_no(), redemptionCode), 1)
            );

            final RewardRequest rewardRequest = new RewardRequest();
            rewardRequest.addCurrency(c_redemptionCode.getRewardCurrencyId(), c_redemptionCode.getRewardAmount());
            final RewardReason rewardReason = RewardReason.Redemption_Code;
            rewardReason.setSource(redemptionCode);
            CurrencyMrg.getInstance().rewardNoTest(player, rewardRequest.getBonusCurrencyMap(), rewardReason);

//            final WithdrawStandard withdrawStandard = player.getWithdrawStandard(c_redemptionCode.getRewardCurrencyId());
//            withdrawStandard.incDrawStandard(BigDecimalUtils.mul(c_redemptionCode.getRewardAmount(), c_redemptionCode.getTurnoverMul(), 4));
            final TurnoverReason turnoverReason = TurnoverReason.Redemption_Code;
            turnoverReason.setSource(redemptionCode);
            ScriptLoader.getInstance().consumerScript("PlayerWithdrawStandardScript",
                    (IPlayerScript script) -> script.drawStandard(player, turnoverReason, c_redemptionCode.getRewardCurrencyId(), c_redemptionCode.getRewardAmount(),
                            BigDecimalUtils.mul(c_redemptionCode.getRewardAmount(), c_redemptionCode.getTurnoverMul(), 4)));

            res.setError(ErrorCode.Success.getCode());
            rewardRequest.writeToItemShowMsg(res::addRewardShow);
            MsgUtil.responseHttp(res.build(), res.getMsgID(), session);

            //TODO 日志
            final GameLog gameLog = new GameLog("platform_redemptionCodeLog");
            gameLog.append("number", GuidGeneratorUtils.generateOrderId())
                    .append("business_no", player.getBusiness_no())
                    .append("site", player.getWebSite())
                    .append("channel", player.getChannel())
                    .append("playerId", player.getPlayerId())
                    .append("playerName", player.getPlayerName())
                    .append("redemptionId", c_redemptionCode.getRedemptionId())
                    .append("redemptionCode", redemptionCode)
                    .append("logTime", TimeUtil.currentTimeMillis());
            HallServer.getInstance().getLogProducerMrg().send(gameLog);

            ScriptLoader.getInstance().consumerScript("BonusScript", (IBonusScript script) ->
                    script.addBonusNote(TransactionFrom.Affiliate_RedemptionCodeRewards, player, c_redemptionCode.getRewardCurrencyId(), c_redemptionCode.getRewardAmount()));
        } catch (Exception e) {
            LOGGER.error("ReqRedemptionCodeRewardHandler", e);
            res.setError(ErrorCode.Internal_Server_Error.getCode());
            MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
        }
    }
}
