package com.game.handler.tcp.wallet;

import com.game.c_entity.merchant.C_GameApi;
import com.game.c_entity.middleplatform.C_BasePaymentMethod;
import com.game.dao.activity.BonusNoteDao;
import com.game.dao.game.GameNoteDao;
import com.game.dao.order.RechargeOrderDao;
import com.game.dao.order.WithdrawOrderDao;
import com.game.engine.io.handler.TcpHandler;
import com.game.engine.script.IHandlerEntity;
import com.game.engine.util.misc.Tuple2;
import com.game.engine.utils.JsonUtils;
import com.game.engine.utils.MsgUtil;
import com.game.engine.utils.TimeUtil;
import com.game.entity.bonus.BonusNote;
import com.game.entity.game.GameNote;
import com.game.entity.order.Order;
import com.game.entity.order.RechargeOrder;
import com.game.entity.order.WithdrawInfo;
import com.game.entity.order.WithdrawOrder;
import com.game.entity.player.Player;
import com.game.enums.ErrorCode;
import com.game.enums.TransactionFrom;
import com.game.hall.mrg.CommonMrg;
import com.game.hall.mrg.DataHallMrg;
import com.game.hall.mrg.MerchantData;
import com.game.hall.mrg.player.PlayerMrg;
import com.game.manager.EntityDaoMrg;
import com.proto.MIDMessage;
import com.proto.WalletMessage;
import io.netty.util.internal.StringUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Arrays;
import java.util.LinkedList;
import java.util.List;
import java.util.stream.Collectors;

@IHandlerEntity(mid = MIDMessage.MID.ReqTransactionData_VALUE, msg = WalletMessage.ReqTransactionDataMessage.class)
public class ReqTransactionDataHandler extends TcpHandler {
    private static final Logger LOGGER = LoggerFactory.getLogger(ReqTransactionDataHandler.class);

    @Override
    public void run() {
        final WalletMessage.ResTransactionDataMessage.Builder res = WalletMessage.ResTransactionDataMessage.newBuilder();
        res.setMsgID(MIDMessage.MID.ResTransactionData_VALUE);
        try {
            final Player player = PlayerMrg.getInstance().getOnlinePlayer(pid, this.getClass().getSimpleName());
            if (player == null) {
                res.setError(ErrorCode.Player_Offline.getCode());
                MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                return;
            }

            final MerchantData merchantData = DataHallMrg.getInstance().findMerchantData(this.getClass().getSimpleName(), player.getBusiness_no());
            if (merchantData == null) {
                res.setError(ErrorCode.Data_Error.getCode());
                MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                return;
            }

            final WalletMessage.ReqTransactionDataMessage req = (WalletMessage.ReqTransactionDataMessage) getMessage();
            final int transactionType = req.getTransactionType();
            final int assets = req.getAssets();
            final int past = req.getPast();
            final int status = req.getStatus();
            final int page = req.getPage();
            final int pageSize = req.getPageSize();
            final int language = req.getLanguage();

            long start = TimeUtil.currentTimeMillis();
            long end = TimeUtil.currentTimeMillis();
            switch (past) {
                case 1: //24
                    start -= TimeUtil.DAY;
                    break;
                case 2: //7
                    start -= 7 * TimeUtil.DAY;
                    break;
                case 3: //30
                    start -= 30 * TimeUtil.DAY;
                    break;
                case 4: //60
                    start -= 60 * TimeUtil.DAY;
                    break;
                case 5: //90
                    start -= 90 * TimeUtil.DAY;
                    break;
                default:
                    start = req.getStatTime();
                    end = req.getEndTime();
                    break;
            }

            res.setTransactionType(transactionType)
                    .setAssets(assets)
                    .setPast(past)
                    .setStatus(status)
                    .setPage(page)
                    .setPageSize(pageSize);
            final int skip = (page - 1) * pageSize;
            switch (transactionType) {
                case 1://deposit
                    final Tuple2<Integer, List<RechargeOrder>> deposit = EntityDaoMrg.getInstance().getDao(RechargeOrderDao.class).loadRechargeOrder(player, transactionType, assets, start, end, status, skip, pageSize);
                    final int totalDeposit = deposit.getFirst();
                    final List<RechargeOrder> rechargeOrderList = deposit.getSecond();
                    res.setTotal(totalDeposit)
                            .setTotalPage(CommonMrg.totalPage(totalDeposit, pageSize));
                    for (RechargeOrder rechargeOrder : rechargeOrderList) {
                        res.addTransactionList(buildDeposit(transactionType, rechargeOrder));
                    }
                    break;
                case 2://withdraw
                    final Tuple2<Integer, List<WithdrawOrder>> withdraw = EntityDaoMrg.getInstance().getDao(WithdrawOrderDao.class).loadWithdrawOrder(player, transactionType, assets, start, end, status, skip, pageSize);
                    final int totalWithdraw = withdraw.getFirst();
                    final List<WithdrawOrder> withdrawOrderList = withdraw.getSecond();
                    res.setTotal(totalWithdraw)
                            .setTotalPage(CommonMrg.totalPage(totalWithdraw, pageSize));
                    for (WithdrawOrder withdrawOrder : withdrawOrderList) {
                        res.addTransactionList(buildWithdraw(transactionType, withdrawOrder));
                    }
                    break;
                case 3://bill
                    final Tuple2<Integer, List<GameNote>> bill = EntityDaoMrg.getInstance().getDao(GameNoteDao.class).loadTransactionGameNote(player, gameStatus(status), assets, start, end, skip, pageSize);
                    final int totalBill = bill.getFirst();
                    final List<GameNote> gameNoteList = bill.getSecond();
                    res.setTotal(totalBill)
                            .setTotalPage(CommonMrg.totalPage(totalBill, pageSize));
                    for (GameNote gameNote : gameNoteList) {
                        final C_GameApi c_gameApi = merchantData.findC_GameApi(this.getClass().getSimpleName(), gameNote.getGameId());
                        if (c_gameApi != null && c_gameApi.setLanguageGameApiData(language)) {
                            res.addTransactionList(buildBill(transactionType, c_gameApi, gameNote));
                        }
                    }
                    break;
                case 4://bonus
                    final List<Integer> transactionFrom = Arrays.stream(TransactionFrom.values())
                            .map(TransactionFrom::getType).collect(Collectors.toCollection(LinkedList::new));

                    final Tuple2<Integer, List<BonusNote>> bonus = EntityDaoMrg.getInstance().getDao(BonusNoteDao.class).loadTransactionsNote(player.getPlayerId(), assets, transactionFrom, start, end, skip, pageSize);
                    final int totalBonus = bonus.getFirst();
                    final List<BonusNote> bonusNoteList = bonus.getSecond();
                    res.setTotal(totalBonus)
                            .setTotalPage(CommonMrg.totalPage(totalBonus, pageSize));
                    for (BonusNote bonusNote : bonusNoteList) {
                        res.addTransactionList(buildBonus(transactionType, bonusNote));
                    }
                    break;
            }

            MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
        } catch (Exception e) {
            LOGGER.error("", e);
            res.setError(ErrorCode.Internal_Server_Error.getCode());
            MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
        }
    }

    private WalletMessage.TransactionInfo buildDeposit(int type, Order order) {
        final C_BasePaymentMethod c_basePaymentMethod = DataHallMrg.getInstance().findC_BasePaymentMethod(this.getClass().getSimpleName(), order.getPaymentMethod());
        final WalletMessage.TransactionInfo.Builder transactionInfo = WalletMessage.TransactionInfo.newBuilder();
        transactionInfo.setType(type)
                .setOrderId(order.getOrderId() + "")
                .setCurrencyId(order.getCurrencyId())
                .setAmount(order.getAmounts())
                .setTime(order.getCreateTime())
                .setStatus(order.getStatus());
        if (c_basePaymentMethod != null) {
            transactionInfo.setChannel(c_basePaymentMethod.getName());
        }
        return transactionInfo.build();
    }

    private WalletMessage.TransactionInfo buildWithdraw(int type, WithdrawOrder withdrawOrder) {
        final C_BasePaymentMethod c_basePaymentMethod = DataHallMrg.getInstance().findC_BasePaymentMethod(this.getClass().getSimpleName(), withdrawOrder.getPaymentMethod());

        final WithdrawInfo withdrawInfo = JsonUtils.readFromJson(withdrawOrder.getWithdrawInfo(), WithdrawInfo.class);
        final WalletMessage.TransactionInfo.Builder transactionInfo = WalletMessage.TransactionInfo.newBuilder();
        transactionInfo.setType(type)
                .setOrderId(withdrawOrder.getOrderId() + "")
                .setCurrencyId(withdrawOrder.getCurrencyId())
                .setAmount(withdrawOrder.getAmounts())
                .setTime(withdrawOrder.getCreateTime())
                .setStatus(withdrawOrder.getStatus())
                .setFee(withdrawInfo.getFee())
                .setTips(StringUtil.isNullOrEmpty(withdrawOrder.getTips()) ? "" : withdrawOrder.getTips());
        if (c_basePaymentMethod != null) {
            transactionInfo.setChannel(c_basePaymentMethod.getName());
        }
        return transactionInfo.build();
    }

    private WalletMessage.TransactionInfo buildBill(int type, C_GameApi c_gameApi, GameNote gameNote) {
        final WalletMessage.TransactionInfo.Builder transactionInfo = WalletMessage.TransactionInfo.newBuilder();
        transactionInfo.setType(type)
                .setGameName(c_gameApi.getGameName())
                .setBetId(gameNote.getNoteId() + "")
                .setCurrencyId(gameNote.getCurrencyId())
                .setAmount(gameNote.getBetAmount())
                .setBalance(gameNote.getBalance())
                .setTime(gameNote.getCreateTime())
                .setStatus(gameStatus(gameNote.getStatus()));//1.未结算 2.结算 3.退款
        return transactionInfo.build();
    }

    private WalletMessage.TransactionInfo buildBonus(int type, BonusNote bonusNote) {
        final WalletMessage.TransactionInfo.Builder transactionInfo = WalletMessage.TransactionInfo.newBuilder();
        transactionInfo.setType(type)
                .setChannel(TransactionFrom.valuesOf(bonusNote.getBonusSubType()).getName())
                .setCurrencyId(bonusNote.getCurrencyId())
                .setAmount(bonusNote.getAmount())
                .setBalance(bonusNote.getBalance())
                .setTime(bonusNote.getCreateTime());
        return transactionInfo.build();
    }

    private int gameStatus(int status) {
        //1.complete 2.processing 3.failed 4.canceled
        switch (status) {
            case 1://未结算
                return 2;
            case 2://结算
                return 1;
            case 3://取消
                return 4;
        }
        return 0;
    }
}
