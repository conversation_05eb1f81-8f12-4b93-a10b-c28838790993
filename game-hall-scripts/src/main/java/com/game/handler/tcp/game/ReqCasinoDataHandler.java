package com.game.handler.tcp.game;

import com.game.c_entity.merchant.C_GameApi;
import com.game.c_entity.merchant.C_GameChannel;
import com.game.c_entity.merchant.C_GamePlatform;
import com.game.c_entity.merchant.C_SubChannelGameApi;
import com.game.c_entity.middleplatform.C_BaseMerchant;
import com.game.engine.io.handler.TcpHandler;
import com.game.engine.script.IHandlerEntity;
import com.game.engine.utils.MsgUtil;
import com.game.entity.player.Player;
import com.game.enums.ErrorCode;
import com.game.hall.mrg.CommonMrg;
import com.game.hall.mrg.DataHallMrg;
import com.game.hall.mrg.MerchantData;
import com.game.hall.mrg.player.PlayerMrg;
import com.proto.HallMessage;
import com.proto.MIDMessage;
import io.netty.util.internal.StringUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.*;
import java.util.stream.Collectors;

@IHandlerEntity(mid = MIDMessage.MID.ReqCasinoData_VALUE, msg = HallMessage.ReqCasinoDataMessage.class)
public class ReqCasinoDataHandler extends TcpHandler {
    private static final Logger LOGGER = LoggerFactory.getLogger(ReqCasinoDataHandler.class);

    @Override
    public void run() {
        final HallMessage.ResCasinoDataMessage.Builder res = HallMessage.ResCasinoDataMessage.newBuilder();
        res.setMsgID(MIDMessage.MID.ResCasinoData_VALUE);

        try {
            final HallMessage.ReqCasinoDataMessage req = (HallMessage.ReqCasinoDataMessage) getMessage();
            final List<HallMessage.PagerList> pagerLists = req.getPagerListList();
            final int language = req.getLanguage();

            final String host = req.getHost();

            final C_BaseMerchant c_baseHostMerchant = DataHallMrg.getInstance().findC_BaseHostMerchant(this.getClass().getSimpleName(), host);
            if (c_baseHostMerchant == null) {
                res.setError(ErrorCode.Data_Error.getCode());
                MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                return;
            }

            final String business_no = c_baseHostMerchant.getBusiness_no();
            final MerchantData merchantData = DataHallMrg.getInstance().findMerchantData(this.getClass().getSimpleName(), business_no);
            if (merchantData == null) {
                res.setError(ErrorCode.Data_Error.getCode());
                MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                return;
            }

            Player player = null;
            if (pid > 0) {
                player = PlayerMrg.getInstance().getOnlinePlayerMap().get(pid);
            }

            final List<C_GameApi> gameAllApi = new ArrayList<>();
            for (HallMessage.PagerList pagerList : pagerLists) {
                gameAllApi.clear();

                final int page = pagerList.getPage();
                final int pageSize = pagerList.getPageSize();
                final String sectionId = pagerList.getSectionId();
                final List<Integer> platformIdList = pagerList.getPlatformIdList();
                final String gameName = pagerList.getGameName();

                if (!platformIdList.isEmpty()) {
                    //平台游戏
                    gameAllApi.addAll(findPlatformIdGameApiList(player, language, merchantData, platformIdList));
                } else {
                    //频道游戏
                    final List<C_GameApi> c_gameApis = findSectionIdGameApiList(player, language, sectionId, merchantData);
                    if (!StringUtil.isNullOrEmpty(gameName)) {
                        gameAllApi.addAll(c_gameApis.stream().filter(c_gameApi ->
                                c_gameApi.getGameName().toLowerCase().contains(gameName.toLowerCase())).toList());
                    } else {
                        gameAllApi.addAll(c_gameApis);
                    }
                }

                final List<C_GameApi> updateGameApi = gameSort(gameAllApi);

                final HallMessage.PageList.Builder pageList = HallMessage.PageList.newBuilder();
                pageList.setPage(page)
                        .setPageSize(pageSize)
                        .setTotal(updateGameApi.size())
                        .setTotalPage(CommonMrg.totalPage(updateGameApi.size(), pageSize))
                        .setSectionId(sectionId);
                final int skip = (page - 1) * pageSize;
                final List<C_GameApi> c_gameApiList = updateGameApi.stream().skip(skip).limit(pageSize).toList();

                for (C_GameApi c_gameApi : c_gameApiList) {
                    pageList.addGameApiInfo(CommonMrg.buildGameApiInfo(player, c_gameApi));
                }

                findGameProvider(pageList, sectionId, merchantData);

                res.addPageList(pageList.build());
            }

            MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
        } catch (Exception e) {
            LOGGER.error("ReqCasinoDataHandler", e);
            res.setError(ErrorCode.Internal_Server_Error.getCode());
            MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
        }
    }

    private List<C_GameApi> findSectionIdGameApiList(Player player, int language, String sectionId, MerchantData merchantData) {
        final List<C_GameApi> c_gameApis = new ArrayList<>();

        if (StringUtil.isNullOrEmpty(sectionId)) {
            return c_gameApis;
        }

        final C_GameChannel c_gameChannel = merchantData.findC_SubGameChannel(this.getClass().getSimpleName(), Integer.parseInt(sectionId));
        if (c_gameChannel == null) {
            return c_gameApis;
        }

        switch (c_gameChannel.getSubChannelType()) {
            case 2://游戏
            case 5://推荐
                final List<C_SubChannelGameApi> c_subChannelGameApiList = merchantData.findC_SubChannelGameApi(this.getClass().getSimpleName(), Integer.parseInt(c_gameChannel.getChannelType() + "" + c_gameChannel.getSubChannel()));
                for (final C_SubChannelGameApi c_subChannelGameApi : c_subChannelGameApiList) {
                    if (!c_subChannelGameApi.isStatus()) {
                        continue;
                    }
                    final C_GameApi c_gameApi = merchantData.findC_GameApi(this.getClass().getSimpleName(), c_subChannelGameApi.getGameId());
                    if (c_gameApi == null) {
                        continue;
                    }
                    if (c_gameApi.setLanguageGameApiData(language)) {
                        c_gameApi.setSeq(c_subChannelGameApi.getSeq());
                        c_gameApis.add(c_gameApi);
                    }
                }
                break;
            case 3://最近
                if (player == null) {
                    break;
                }
                final List<Integer> recentGameList = player.getRecentGame();
                final List<Integer> tempRecentGameList = new ArrayList<>(recentGameList);
                Collections.reverse(tempRecentGameList);
                for (int gameId : tempRecentGameList) {
                    final C_GameApi c_gameApi = merchantData.findC_GameApi(this.getClass().getSimpleName(), gameId);
                    if (c_gameApi != null && c_gameApi.setLanguageGameApiData(language)) {
                        c_gameApis.add(c_gameApi);
                    }
                }
                break;
            case 4://收藏
                if (player == null) {
                    break;
                }
                final List<Integer> favoritesGameList = player.getFavoritesGame();
                final List<Integer> tempFavoritesGameList = new ArrayList<>(favoritesGameList);
                Collections.reverse(tempFavoritesGameList);
                for (int gameId : tempFavoritesGameList) {
                    final C_GameApi c_gameApi = merchantData.findC_GameApi(this.getClass().getSimpleName(), gameId);
                    if (c_gameApi != null && c_gameApi.setLanguageGameApiData(language)) {
                        c_gameApis.add(c_gameApi);
                    }
                }
                break;
        }
        return c_gameApis.stream().distinct().collect(Collectors.toList());
    }

    private List<C_GameApi> findPlatformIdGameApiList(Player player, int language, MerchantData merchantData, List<Integer> platformIdList) {
        final List<C_GameApi> c_gameApis = new ArrayList<>();
        for (int platformId : platformIdList) {
            final List<C_GameApi> c_gameApiList = merchantData.findC_platformId_gameApi(this.getClass().getSimpleName(), platformId);
            if (c_gameApiList == null || c_gameApiList.isEmpty()) {
                continue;
            }

            for (C_GameApi c_gameApi : c_gameApiList) {
                if (player != null) {
                    final C_GamePlatform c_gamePlatform = merchantData.findC_GamePlatformIdGameType(this.getClass().getSimpleName(), Integer.parseInt(c_gameApi.getPlatformId() + "" + c_gameApi.getType()));
                    if (c_gamePlatform == null) {
                        continue;
                    }

                    if (c_gamePlatform.regionLimit(player.getRegion())) {
                        continue;
                    }
                }

                if (c_gameApi.setLanguageGameApiData(language)) {
                    c_gameApis.add(c_gameApi);
                }
            }
        }
        return c_gameApis;
    }

    private void findGameProvider(HallMessage.PageList.Builder pageList, String sectionId, MerchantData merchantData) {
        if (StringUtil.isNullOrEmpty(sectionId)) {
            return;
        }

        final C_GameChannel c_gameChannel = merchantData.findC_SubGameChannel(this.getClass().getSimpleName(), Integer.parseInt(sectionId));
        if (c_gameChannel == null) {
            return;
        }

        final List<Integer> platformIdGameTypes = c_gameChannel.getPlatformId();
        final Set<Integer> platformIds = new HashSet<>();
        for (int platformIdGameType : platformIdGameTypes) {
            final int platformId = platformIdGameType / 1000;
            if (platformIds.contains(platformId)) {
                continue;
            }

            final C_GamePlatform c_gamePlatform = merchantData.findC_GamePlatform(this.getClass().getSimpleName(), platformId);
            if (c_gamePlatform == null) {
                continue;
            }

            final List<C_GameApi> gameApiList = merchantData.findC_platformIdGameType_gameApi(this.getClass().getSimpleName(), platformIdGameType);
            if (gameApiList == null || gameApiList.isEmpty()) {
                continue;
            }

            platformIds.add(platformId);
            final int gameNum = gameApiList.size();
            pageList.addGameProviders(buildProvider(c_gamePlatform, gameNum));
        }
    }

    private HallMessage.GameProvider buildProvider(C_GamePlatform c_gamePlatform, int gameNum) {
        final HallMessage.GameProvider.Builder gameProvider = HallMessage.GameProvider.newBuilder()
                .setPlatformId(c_gamePlatform.getPlatformId())
                .setPlatformName(c_gamePlatform.getPlatformName())
                .setFileUrl(c_gamePlatform.getFileUrl())
                .setGameNum(gameNum);
        return gameProvider.build();
    }


//    private List<C_GameApi> gameSort(List<C_GameApi> gameAllApi) {
//        if (gameAllApi.isEmpty()) {
//            return new ArrayList<>();
//        }
//        /**
//         * 排序规则：标签>序号>更新时间倒序
//         *
//         * 标签排序：推荐>热门>最新
//         *
//         * 优先按标签排序，同标签按排序，同标签同排序按更新时间倒序
//         */
//        return gameAllApi.stream().sorted(
//                Comparator.comparingInt(C_GameApi::getTag)//升序
//                        .thenComparing(Comparator.comparingInt(C_GameApi::getSeq).reversed())//降序
//                        .thenComparing(Comparator.comparingLong(C_GameApi::getUpdateTime).reversed())//降序
//        ).collect(Collectors.toList());
//    }

    private List<C_GameApi> gameSort(List<C_GameApi> gameAllApi) {
        if (gameAllApi.isEmpty()) {
            return new ArrayList<>();
        }

        return gameAllApi.stream().sorted(
                Comparator.comparing(
                                C_GameApi::getTag,
                                Comparator.nullsFirst(Comparator.naturalOrder())
                        )
                        .thenComparing(
                                Comparator.comparing(
                                        C_GameApi::getSeq,
                                        Comparator.nullsFirst(Comparator.naturalOrder())
                                ).reversed()
                        )
                        .thenComparing(
                                Comparator.comparing(
                                        C_GameApi::getUpdateTime,
                                        Comparator.nullsFirst(Comparator.naturalOrder())
                                ).reversed()
                        )
        ).collect(Collectors.toList());
    }

}
