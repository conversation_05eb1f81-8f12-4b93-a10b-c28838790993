package com.game.handler.tcp.affiliate;

import com.game.c_entity.merchant.C_CashBack;
import com.game.dao.player.PlayerPromoteDao;
import com.game.engine.io.handler.TcpHandler;
import com.game.engine.math.BigDecimalUtils;
import com.game.engine.mongo.DBConnectionMrg;
import com.game.engine.mongo.MongoConverterMrg;
import com.game.engine.script.IHandlerEntity;
import com.game.engine.script.ScriptLoader;
import com.game.engine.utils.MsgUtil;
import com.game.entity.player.Player;
import com.game.entity.player.promote.*;
import com.game.enums.ErrorCode;
import com.game.enums.RewardReason;
import com.game.enums.TransactionFrom;
import com.game.enums.TurnoverReason;
import com.game.hall.mrg.DataHallMrg;
import com.game.hall.mrg.MerchantData;
import com.game.hall.mrg.currency.CurrencyMrg;
import com.game.hall.mrg.currency.RewardRequest;
import com.game.hall.mrg.player.PlayerMrg;
import com.game.hall.script.IBonusScript;
import com.game.hall.script.IPlayerScript;
import com.game.manager.EntityDaoMrg;
import com.proto.AffiliateMessage;
import com.proto.MIDMessage;
import io.netty.util.internal.StringUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.mongodb.core.query.Update;

import java.util.Map;
import java.util.Optional;

@IHandlerEntity(mid = MIDMessage.MID.ReqWithdrawToWallet_VALUE, msg = AffiliateMessage.ReqWithdrawToWalletMessage.class)
public class ReqWithdrawToWalletHandler extends TcpHandler {
    private static final Logger LOGGER = LoggerFactory.getLogger(ReqWithdrawToWalletHandler.class);

    @Override
    public void run() {
        final AffiliateMessage.ResWithdrawToWalletMessage.Builder res = AffiliateMessage.ResWithdrawToWalletMessage.newBuilder();
        res.setMsgID(MIDMessage.MID.ResWithdrawToWallet_VALUE);
        try {
            final Player player = PlayerMrg.getInstance().getOnlinePlayer(pid, this.getClass().getSimpleName());
            if (player == null) {
                res.setError(ErrorCode.Player_Offline.getCode());
                MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                return;
            }

            final MerchantData merchantData = DataHallMrg.getInstance().findMerchantData(this.getClass().getSimpleName(), player.getBusiness_no());
            if (merchantData == null) {
                res.setError(ErrorCode.Data_Error.getCode());
                MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                return;
            }

            final PlayerPromote playerPromote = player.getPlayerPromote();
            final RewardRequest rewardRequest = new RewardRequest();
//            final RewardRequest withdrawStandard = new RewardRequest();

            //推广奖励
            final ReferralRewards availableRewards = playerPromote.getAvailableRewards();
            if (availableRewards.getCurrencyId() > 0) {
                rewardRequest.addCurrency(availableRewards.getCurrencyId(), availableRewards.getAvailable());
                ScriptLoader.getInstance().consumerScript("BonusScript",
                        (IBonusScript script) -> script.addBonusNote(TransactionFrom.Affiliate_ReferralRewards, player, availableRewards.getCurrencyId(), availableRewards.getAvailable()));

                final String referralTurnoverMul = merchantData.findC_GlobalValue(this.getClass().getSimpleName(), "referralTurnoverMul");
                final int turnoverMul = StringUtil.isNullOrEmpty(referralTurnoverMul) ? 5 : Integer.parseInt(referralTurnoverMul);
//                withdrawStandard.addCurrency(referralRewards.getCurrencyId(), BigDecimalUtils.mul(referralRewards.getAvailable(), turnoverMul, 9));

                final TurnoverReason turnoverReason = TurnoverReason.Withdraw_ToWallet;
                turnoverReason.setSource("referralRewards");
                ScriptLoader.getInstance().consumerScript("PlayerWithdrawStandardScript",
                        (IPlayerScript script) -> script.drawStandard(player, turnoverReason, availableRewards.getCurrencyId(), availableRewards.getAvailable(),
                                BigDecimalUtils.mul(availableRewards.getAvailable(), turnoverMul, 9)));
            }


            final Optional<C_CashBack> optional = merchantData.getC_cashBackMap().values().stream().findFirst();

            //上级返水
            final Map<Integer, CommissionRewards> commissionRewardsMap = playerPromote.getCommissionRewardsMap();
            for (Map.Entry<Integer, CommissionRewards> entry : commissionRewardsMap.entrySet()) {
                rewardRequest.addCurrency(entry.getKey(), entry.getValue().getAvailable());
                ScriptLoader.getInstance().consumerScript("BonusScript",
                        (IBonusScript script) -> script.addBonusNote(TransactionFrom.Affiliate_CommissionRewards, player, entry.getKey(), entry.getValue().getAvailable()));

                if (optional.isPresent()) {
                    final C_CashBack c_cashBack = optional.get();
                    final TurnoverReason turnoverReason = TurnoverReason.Withdraw_ToWallet;
                    turnoverReason.setSource("1st-Lvl Rewards");
                    ScriptLoader.getInstance().consumerScript("PlayerWithdrawStandardScript",
                            (IPlayerScript script) -> script.drawStandard(player, turnoverReason, entry.getKey(), entry.getValue().getAvailable(),
                                    BigDecimalUtils.mul(entry.getValue().getAvailable(), c_cashBack.getTurnoverMul(), 9)));
                }
            }

            //团长返水
            final Map<Integer, TeamRewards> teamRewardsMap = playerPromote.getTeamRewardsMap();
            for (Map.Entry<Integer, TeamRewards> entry : teamRewardsMap.entrySet()) {
                rewardRequest.addCurrency(entry.getKey(), entry.getValue().getAvailable());
                ScriptLoader.getInstance().consumerScript("BonusScript",
                        (IBonusScript script) -> script.addBonusNote(TransactionFrom.Affiliate_TeamRewards, player, entry.getKey(), entry.getValue().getAvailable()));

                if (optional.isPresent()) {
                    final C_CashBack c_cashBack = optional.get();
                    final TurnoverReason turnoverReason = TurnoverReason.Withdraw_ToWallet;
                    turnoverReason.setSource("2nd-Lvl Rewards");
                    ScriptLoader.getInstance().consumerScript("PlayerWithdrawStandardScript",
                            (IPlayerScript script) -> script.drawStandard(player, turnoverReason, entry.getKey(), entry.getValue().getAvailable(),
                                    BigDecimalUtils.mul(entry.getValue().getAvailable(), c_cashBack.getTurnoverMul(), 9)));
                }
            }

            //三级返水
            final Map<Integer, ThreeLevelRewards> threeLevelRewardsMap = playerPromote.getThreeLevelRewardsMap();
            for (Map.Entry<Integer, ThreeLevelRewards> entry : threeLevelRewardsMap.entrySet()) {
                rewardRequest.addCurrency(entry.getKey(), entry.getValue().getAvailable());
                ScriptLoader.getInstance().consumerScript("BonusScript",
                        (IBonusScript script) -> script.addBonusNote(TransactionFrom.Affiliate_ThreeLevelRewards, player,
                                entry.getKey(), entry.getValue().getAvailable()));

                if (optional.isPresent()) {
                    final C_CashBack c_cashBack = optional.get();
                    final TurnoverReason turnoverReason = TurnoverReason.Withdraw_ToWallet;
                    turnoverReason.setSource("3rd-Lvl Rewards");
                    ScriptLoader.getInstance().consumerScript("PlayerWithdrawStandardScript",
                            (IPlayerScript script) -> script.drawStandard(player, turnoverReason, entry.getKey(), entry.getValue().getAvailable(),
                                    BigDecimalUtils.mul(entry.getValue().getAvailable(), c_cashBack.getTurnoverMul(), 9)));
                }
            }

            MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
            CurrencyMrg.getInstance().rewardNoTest(player, rewardRequest.getCurrencyMap(), RewardReason.Withdraw_ToWallet);

            availableRewards.reset();

            for (CommissionRewards commissionRewards : commissionRewardsMap.values()) {
                commissionRewards.reset();
            }

            for (TeamRewards teamRewards : teamRewardsMap.values()) {
                teamRewards.reset();
            }

            for (ThreeLevelRewards threeLevelRewards : threeLevelRewardsMap.values()) {
                threeLevelRewards.reset();
            }

            final MongoConverterMrg converterMrg = DBConnectionMrg.getInstance().getConvertMrg();
            final Update update = new Update();
            update.set(PlayerPromoteFields.commissionRewardsMap, converterMrg.writeNoTypeKey(commissionRewardsMap))
                    .set(PlayerPromoteFields.availableRewards, converterMrg.writeNoTypeKey(availableRewards))
                    .set(PlayerPromoteFields.teamRewardsMap, converterMrg.writeNoTypeKey(teamRewardsMap))
                    .set(PlayerPromoteFields.threeLevelRewardsMap, converterMrg.writeNoTypeKey(threeLevelRewardsMap));
            EntityDaoMrg.getInstance().getDao(PlayerPromoteDao.class)
                    .updatePromotion(player.getPlayerId(), update);
        } catch (Exception e) {
            LOGGER.error("ReqWithdrawToWalletHandler", e);
            res.setError(ErrorCode.Internal_Server_Error.getCode());
            MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
        }
    }
}
