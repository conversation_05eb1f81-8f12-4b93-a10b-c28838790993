//package com.game.handler.tcp.inner;
//
//import com.game.dao.player.PlayerDao;
//import com.game.engine.io.handler.TcpHandler;
//import com.game.engine.script.IHandlerEntity;
//import com.game.engine.script.ScriptLoader;
//import com.game.entity.player.Player;
//import com.game.entity.player.PlayerFields;
//import com.game.hall.mrg.player.PlayerMrg;
//import com.game.hall.script.IAgentGameScript;
//import com.game.hall.script.IPlayerScript;
//import com.game.manager.EntityDaoMrg;
//import com.proto.InnerMessage;
//import com.proto.MIDMessage;
//import org.slf4j.Logger;
//import org.slf4j.LoggerFactory;
//import org.springframework.data.mongodb.core.query.Update;
//
//@IHandlerEntity(mid = MIDMessage.MID.InnerReqLoginOutHall_VALUE, msg = InnerMessage.InnerReqLoginOutHallMessage.class)
//public class HallInnerReqLoginOutHallHandler extends TcpHandler {
//    private static final Logger LOGGER = LoggerFactory.getLogger(HallInnerReqLoginOutHallHandler.class);
//
//    @Override
//    public void run() {
//        final InnerMessage.InnerReqLoginOutHallMessage req = (InnerMessage.InnerReqLoginOutHallMessage) getMessage();
//        final long playerId = req.getPlayerID();
//
//        final Player player = PlayerMrg.getInstance().getOnlinePlayerMap().get(playerId);
//        if (player == null) {
//            return;
//        }
//
//        if (player.getGameId() > 0) {
//            //TODO 通知更新游戏信息
////            CommonMrg.getInstance().notifyUpdateGameInfo(2, player.getGameId());
//
//            LOGGER.warn("playerId：{}，quit gameId：{}", player.getPlayerId(), player.getGameId());
//
//            player.resetGameInfo();
//            final Update update = new Update();
//            update.set(PlayerFields.gameId, player.getGameId())
//                    .set(PlayerFields.bonus, player.isBonus())
//                    .set(PlayerFields.platformId, player.getPlatformId())
//                    .set(PlayerFields.gameType, player.getGameType())
//                    .set(PlayerFields.gameCurrencyId, player.getGameCurrencyId())
//                    .set(PlayerFields.playerCurrencyId, player.getPlayerCurrencyId());
//            EntityDaoMrg.getInstance().getDao(PlayerDao.class)
//                    .updatePlayer(player.getPlayerId(), update);
//
//            ScriptLoader.getInstance().consumerScript("AgentGameScript",
//                    (IAgentGameScript script) -> script.statisticalGameNoteData(player));
//        }
//    }
//}
