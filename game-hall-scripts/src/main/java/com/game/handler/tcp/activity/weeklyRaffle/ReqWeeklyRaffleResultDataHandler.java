package com.game.handler.tcp.activity.weeklyRaffle;

import com.game.c_entity.middleplatform.C_BaseMerchant;
import com.game.dao.activity.WinTicketsNoteDao;
import com.game.engine.io.handler.TcpHandler;
import com.game.engine.io.redis.RedisPoolManager;
import com.game.engine.script.IHandlerEntity;
import com.game.engine.util.misc.Tuple2;
import com.game.engine.utils.MsgUtil;
import com.game.entity.activity.WinTicketsNote;
import com.game.enums.ErrorCode;
import com.game.enums.redis.RedisAllGame;
import com.game.hall.mrg.ActivityMrg;
import com.game.hall.mrg.DataHallMrg;
import com.game.hall.mrg.CommonMrg;
import com.game.manager.EntityDaoMrg;
import com.proto.ActivityMessage;
import com.proto.MIDMessage;
import io.netty.util.internal.StringUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;

@IHandlerEntity(mid = MIDMessage.MID.ReqWeeklyRaffleResultData_VALUE, msg = ActivityMessage.ReqWeeklyRaffleResultDataMessage.class)
public class ReqWeeklyRaffleResultDataHandler extends TcpHandler {
    private static final Logger LOGGER = LoggerFactory.getLogger(ReqWeeklyRaffleResultDataHandler.class);

    @Override
    public void run() {
        final ActivityMessage.ResWeeklyRaffleResultDataMessage.Builder res = ActivityMessage.ResWeeklyRaffleResultDataMessage.newBuilder();
        res.setMsgID(MIDMessage.MID.ResWeeklyRaffleResultData_VALUE);
        try {
            final ActivityMessage.ReqWeeklyRaffleResultDataMessage req = (ActivityMessage.ReqWeeklyRaffleResultDataMessage) message;
            final String host = req.getHost();

            final C_BaseMerchant c_baseHostMerchant = DataHallMrg.getInstance().findC_BaseHostMerchant(this.getClass().getSimpleName(), host);
            if (c_baseHostMerchant == null) {
                res.setError(ErrorCode.Data_Error.getCode());
                MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                return;
            }

            final String business_no = c_baseHostMerchant.getBusiness_no();
            final String gameId = req.getGameId();
            final int page = req.getPage();
            final int pageSize = req.getPageSize();

            final int skip = (page - 1) * pageSize;
            final Tuple2<Integer, List<WinTicketsNote>> tuple2 = EntityDaoMrg.getInstance().getDao(WinTicketsNoteDao.class).loadWinTicketsNote(business_no, gameId, skip, pageSize);
            final int total = tuple2.getFirst();
            final List<WinTicketsNote> winTicketsNotes = tuple2.getSecond();
            for (WinTicketsNote winTicketsNote : winTicketsNotes) {
                res.addWinnerList(ActivityMrg.buildTicketsInfo(winTicketsNote));
            }

            final String thisRoundTickets = RedisPoolManager.getInstance().function(jedis ->
                    jedis.sync().get(RedisAllGame.Platform_All_WeeklyRaffle_Tickets.getKey(business_no, gameId)));
            res.setTotalTickets(Integer.parseInt(StringUtil.isNullOrEmpty(thisRoundTickets) ? "0" : thisRoundTickets))
                    .setPage(page)
                    .setPageSize(pageSize)
                    .setTotal(total)
                    .setTotalPage(CommonMrg.totalPage(total, pageSize));
            MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
        } catch (Exception e) {
            LOGGER.error("ReqWeeklyRaffleResultDataHandler", e);
            res.setError(ErrorCode.Internal_Server_Error.getCode());
            MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
        }
    }

}
