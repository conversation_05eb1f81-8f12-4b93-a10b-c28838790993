package com.game.handler.tcp;

import com.game.engine.io.handler.TcpHandler;
import com.game.engine.script.IHandlerEntity;
import com.game.engine.utils.MsgUtil;
import com.game.engine.utils.TimeUtil;
import com.game.entity.player.Player;
import com.game.hall.mrg.player.PlayerMrg;
import com.proto.MIDMessage;
import com.proto.TcpMessage;

@IHandlerEntity(mid = MIDMessage.MID.ReqTcpHeartBeat_VALUE, msg = TcpMessage.ReqTcpHeartBeatMessage.class)
public class ReqTcpHeartBeatHandler extends TcpHandler {

    @Override
    public void run() {
        final TcpMessage.ResTcpHeartBeatMessage.Builder res = TcpMessage.ResTcpHeartBeatMessage.newBuilder();
        res.setMsgID(MIDMessage.MID.ResTcpHeartBeat_VALUE)
                .setSystemTime(TimeUtil.currentTimeMillis());
        if (pid < 0) {
            MsgUtil.sendClientMsg(session, res.build());
            return;
        }

        final Player player = PlayerMrg.getInstance().getOnlinePlayer(pid, this.getClass().getSimpleName());
        if (player == null) {
            session.close();
            return;
        }

        player.setHeartbeat(TimeUtil.currentTimeMillis() + 15 * TimeUtil.MIN);
        player.setClientSession(session);
        player.sendMsg(res.build());
    }

}
