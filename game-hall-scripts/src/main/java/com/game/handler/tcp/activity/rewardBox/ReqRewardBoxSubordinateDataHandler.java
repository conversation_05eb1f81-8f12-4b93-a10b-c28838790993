package com.game.handler.tcp.activity.rewardBox;

import com.game.c_entity.merchant.C_RewardBox;
import com.game.engine.io.handler.TcpHandler;
import com.game.engine.io.redis.RedisPoolManager;
import com.game.engine.script.IHandlerEntity;
import com.game.engine.utils.MsgUtil;
import com.game.entity.player.Player;
import com.game.entity.player.activity.rewardBox.RewardBoxInfo;
import com.game.enums.ErrorCode;
import com.game.enums.redis.RedisHall;
import com.game.hall.mrg.ActivityMrg;
import com.game.hall.mrg.CommonMrg;
import com.game.hall.mrg.DataHallMrg;
import com.game.hall.mrg.MerchantData;
import com.game.hall.mrg.player.PlayerMrg;
import com.proto.ActivityMessage;
import com.proto.MIDMessage;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.HashSet;
import java.util.List;
import java.util.Set;

@IHandlerEntity(mid = MIDMessage.MID.ReqRewardBoxSubordinateData_VALUE, msg = ActivityMessage.ReqRewardBoxSubordinateDataMessage.class)
public class ReqRewardBoxSubordinateDataHandler extends TcpHandler {
    private static final Logger LOGGER = LoggerFactory.getLogger(ReqRewardBoxSubordinateDataHandler.class);

    @Override
    public void run() {
        final ActivityMessage.ResRewardBoxSubordinateDataMessage.Builder res = ActivityMessage.ResRewardBoxSubordinateDataMessage.newBuilder();
        res.setMsgID(MIDMessage.MID.ResRewardBoxSubordinateData_VALUE);
        try {
            final Player player = PlayerMrg.getInstance().getOnlinePlayer(pid, this.getClass().getSimpleName());
            if (player == null) {
                res.setError(ErrorCode.Player_Offline.getCode());
                MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                return;
            }

            final MerchantData merchantData = DataHallMrg.getInstance().findMerchantData(this.getClass().getSimpleName(), player.getBusiness_no());
            if (merchantData == null) {
                res.setError(ErrorCode.Data_Error.getCode());
                MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                return;
            }

            final C_RewardBox c_rewardBox = merchantData.findC_RewardBox(this.getClass().getSimpleName(), ActivityMrg.REWARD_BOX);
            if (c_rewardBox == null) {
                res.setError(ErrorCode.Data_Error.getCode());
                MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                return;
            }

            final ActivityMessage.ReqRewardBoxSubordinateDataMessage req = (ActivityMessage.ReqRewardBoxSubordinateDataMessage) getMessage();
            int page = req.getPage();
            final int pageSize = req.getPageSize();
            final boolean efficient = req.getEfficient();

            if (page <= 0) {
                page = 1;
            }

            final int skip = (page - 1) * pageSize;

            if (efficient) {
                Set<String> invite = RedisPoolManager.getInstance().function(jedis ->
                        jedis.sync().smembers(RedisHall.Platform_Role_Map_RewardBoxInvite.getKey(pid)));
                if (invite == null) {
                    invite = new HashSet<>();
                }

                final int total = invite.size();
                if (total == 0) {
                    MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                    return;
                }

                final List<Long> invitePlayerIds = invite.stream().map(Long::parseLong)
                        .skip(skip).limit(pageSize).toList();

                for (long playerId : invitePlayerIds) {
                    final Player superiorPlayer = PlayerMrg.getInstance().findDbPlayer(playerId);
                    if (superiorPlayer == null) {
                        continue;
                    }
                    final RewardBoxInfo superiorRewardBoxInfo = superiorPlayer.getRewardBoxInfo();
                    double recharge = 0;
                    if (superiorRewardBoxInfo.getRechargeAmountMap() != null) {
                        recharge = superiorRewardBoxInfo.getRechargeAmountMap().getOrDefault(c_rewardBox.getRewardCurrencyId(), 0d);

                    }
                    double wagered = 0;
                    if (superiorRewardBoxInfo.getWageredMap() != null) {
                        wagered = superiorRewardBoxInfo.getWageredMap().getOrDefault(c_rewardBox.getRewardCurrencyId(), 0d);
                    }
                    res.addSubordinateList(buildSubordinateInfo(superiorPlayer.getPlayerName(), c_rewardBox.getRewardCurrencyId(), recharge, wagered));
                }
                res.setTotal(total)
                        .setTotalPage(CommonMrg.totalPage(total, pageSize));
            } else {
                Set<String> referral = RedisPoolManager.getInstance().function(jedis ->
                        jedis.sync().smembers(RedisHall.Platform_Role_Map_RewardBoxReferral.getKey(pid)));
                if (referral == null) {
                    referral = new HashSet<>();
                }

                final int total = referral.size();
                if (total == 0) {
                    MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                    return;
                }

                final List<Long> referralPlayerIds = referral.stream().map(Long::parseLong)
                        .skip(skip).limit(pageSize).toList();

                for (long playerId : referralPlayerIds) {
                    final Player superiorPlayer = PlayerMrg.getInstance().findDbPlayer(playerId);
                    if (superiorPlayer == null) {
                        continue;
                    }
                    final RewardBoxInfo superiorRewardBoxInfo = superiorPlayer.getRewardBoxInfo();
                    double recharge = 0;
                    if (superiorRewardBoxInfo.getRechargeAmountMap() != null) {
                        recharge = superiorRewardBoxInfo.getRechargeAmountMap().getOrDefault(c_rewardBox.getRewardCurrencyId(), 0d);

                    }
                    double wagered = 0;
                    if (superiorRewardBoxInfo.getWageredMap() != null) {
                        wagered = superiorRewardBoxInfo.getWageredMap().getOrDefault(c_rewardBox.getRewardCurrencyId(), 0d);
                    }
                    res.addSubordinateList(buildSubordinateInfo(superiorPlayer.getPlayerName(), c_rewardBox.getRewardCurrencyId(), recharge, wagered));
                }
                res.setTotal(total)
                        .setTotalPage(CommonMrg.totalPage(total, pageSize));
            }
            MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
        } catch (Exception e) {
            LOGGER.error("ReqRewardBoxSubordinateDataHandler", e);
            res.setError(ErrorCode.Internal_Server_Error.getCode());
            MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
        }
    }

    private ActivityMessage.SubordinateInfo buildSubordinateInfo(String name, int currencyId, double recharge, double wagered) {
        return ActivityMessage.SubordinateInfo.newBuilder()
                .setName(name)
                .setCurrencyId(currencyId)
                .setRecharge(recharge)
                .setWagered(wagered)
                .build();
    }

}
