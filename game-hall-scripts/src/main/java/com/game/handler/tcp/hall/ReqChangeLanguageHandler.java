package com.game.handler.tcp.hall;

import com.game.dao.player.PlayerDao;
import com.game.engine.io.handler.TcpHandler;
import com.game.engine.script.IHandlerEntity;
import com.game.engine.utils.MsgUtil;
import com.game.entity.player.Player;
import com.game.entity.player.PlayerFields;
import com.game.enums.ErrorCode;
import com.game.hall.mrg.player.PlayerMrg;
import com.game.manager.EntityDaoMrg;
import com.proto.HallMessage;
import com.proto.MIDMessage;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@IHandlerEntity(mid = MIDMessage.MID.ReqChangeLanguage_VALUE, msg = HallMessage.ReqChangeLanguageMessage.class)
public class ReqChangeLanguageHandler extends TcpHandler {
    private static final Logger LOGGER = LoggerFactory.getLogger(ReqChangeLanguageHandler.class);

    @Override
    public void run() {
        final HallMessage.ResChangeLanguageMessage.Builder res = HallMessage.ResChangeLanguageMessage.newBuilder();
        res.setMsgID(MIDMessage.MID.ResChangeLanguage_VALUE);
        try {
            final Player player = PlayerMrg.getInstance().getOnlinePlayer(pid, this.getClass().getSimpleName());
            if (player == null) {
                res.setError(ErrorCode.Player_Offline.getCode());
                MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                return;
            }

            final HallMessage.ReqChangeLanguageMessage req = (HallMessage.ReqChangeLanguageMessage) getMessage();
            final int language = req.getLanguage();
            player.setLanguage(language);
            EntityDaoMrg.getInstance().getDao(PlayerDao.class)
                    .updatePlayerField(pid, PlayerFields.language, language);

            res.setLanguage(language);
            MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
        } catch (Exception e) {
            LOGGER.error("ReqChangeLanguageHandler", e);
            res.setError(ErrorCode.Internal_Server_Error.getCode());
            MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
        }
    }
}
