package com.game.handler.tcp.activity.luckSpin;

import com.game.c_entity.merchant.C_LuckSpin;
import com.game.dao.activity.SpinBonusNoteDao;
import com.game.dao.player.PlayerDao;
import com.game.engine.io.handler.TcpHandler;
import com.game.engine.io.redis.RedisPoolManager;
import com.game.engine.log.GameLog;
import com.game.engine.math.BigDecimalUtils;
import com.game.engine.script.IHandlerEntity;
import com.game.engine.script.ScriptLoader;
import com.game.engine.util.misc.Tuple2;
import com.game.engine.utils.GuidGeneratorUtils;
import com.game.engine.utils.MsgUtil;
import com.game.engine.utils.TimeUtil;
import com.game.entity.activity.SpinBonusNote;
import com.game.entity.player.Player;
import com.game.entity.player.activity.luckSpin.LuckSpinData;
import com.game.entity.player.activity.luckSpin.LuckSpinInfo;
import com.game.entity.player.bonus.BonusDetailsInfo;
import com.game.entity.player.bonus.BonusInfo;
import com.game.enums.*;
import com.game.enums.redis.RedisAllGame;
import com.game.hall.main.HallServer;
import com.game.hall.mrg.ActivityMrg;
import com.game.hall.mrg.DataHallMrg;
import com.game.hall.mrg.CommonMrg;
import com.game.hall.mrg.MerchantData;
import com.game.hall.mrg.currency.CurrencyMrg;
import com.game.hall.mrg.currency.RewardRequest;
import com.game.hall.mrg.player.PlayerMrg;
import com.game.hall.script.IBonusScript;
import com.game.hall.script.IPlayerScript;
import com.game.manager.EntityDaoMrg;
import com.game.utils.VirtualThreadUtils;
import com.proto.ActivityMessage;
import com.proto.MIDMessage;
import it.unimi.dsi.fastutil.ints.IntLists;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@IHandlerEntity(mid = MIDMessage.MID.ReqLuckSpinReferralWithdraw_VALUE, msg = ActivityMessage.ReqLuckSpinReferralWithdrawMessage.class)
public class ReqLuckSpinReferralWithdrawHandler extends TcpHandler {
    private static final Logger LOGGER = LoggerFactory.getLogger(ReqLuckSpinReferralWithdrawHandler.class);

    @Override
    public void run() {
        final ActivityMessage.ResLuckSpinReferralWithdrawMessage.Builder res = ActivityMessage.ResLuckSpinReferralWithdrawMessage.newBuilder();
        res.setMsgID(MIDMessage.MID.ResLuckSpinReferralWithdraw_VALUE);
        try {
            final Player player = PlayerMrg.getInstance().getOnlinePlayer(pid, this.getClass().getSimpleName());
            if (player == null) {
                res.setError(ErrorCode.Player_Offline.getCode());
                MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                return;
            }

            final ActivityMessage.ReqLuckSpinReferralWithdrawMessage req = (ActivityMessage.ReqLuckSpinReferralWithdrawMessage) getMessage();

            final MerchantData merchantData = DataHallMrg.getInstance().findMerchantData(this.getClass().getSimpleName(), player.getBusiness_no());
            if (merchantData == null) {
                res.setError(ErrorCode.Data_Error.getCode());
                MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                return;
            }

            final C_LuckSpin c_luckSpin = merchantData.findC_LuckSpin(this.getClass().getSimpleName(), ActivityMrg.LUCK_SPIN_REFERRAL);
            if (c_luckSpin == null) {
                res.setError(ErrorCode.Data_Error.getCode());
                MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                return;
            }

            final C_LuckSpin.InviteData inviteData = c_luckSpin.getInviteInfo();
            if (inviteData == null) {
                res.setError(ErrorCode.Data_Error.getCode());
                MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                return;
            }

            final LuckSpinInfo luckSpinInfo = player.getLuckSpinInfo();
            final LuckSpinData luckSpinData = luckSpinInfo.getLuckSpinData(c_luckSpin.getActivityId());

            if (luckSpinData.isFinish()) {
                res.setError(ErrorCode.LuckSpin_Not_Available.getCode());
                MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                return;
            }

            final double currentRewards = luckSpinData.getCurrentRewardsMap().getOrDefault(inviteData.getCurrencyId(), 0d);
            if (currentRewards + inviteData.getInitReward() < inviteData.getAvailableReward()) {
                LOGGER.warn("playerId：{}，Conditions not met", player.getPlayerId());
                res.setError(ErrorCode.LuckSpin_Conditions_Not_Met.getCode());
                MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                return;
            }

            luckSpinData.setFinish(true);

            final RewardRequest rewardRequest = new RewardRequest();
            rewardRequest.addCurrency(inviteData.getCurrencyId(), inviteData.getAvailableReward());
            final RewardReason rewardReason = RewardReason.LuckSpin;
            rewardReason.setSource(ActivityMrg.LUCK_SPIN_REFERRAL + "");
            CurrencyMrg.getInstance().rewardNoTest(player, rewardRequest.getBonusCurrencyMap(), rewardReason);
            res.setCurrentProcess(CommonMrg.buildDItemShow(inviteData.getCurrencyId(), inviteData.getAvailableReward()));
            MsgUtil.responseHttp(res.build(), res.getMsgID(), session);

            final BonusInfo bonusInfo = player.getBonusInfo();
            final BonusDetailsInfo bonusDetailsInfo = bonusInfo.getBonusDetailsInfo(BonusDetail.LuckySpin.getType());
            bonusDetailsInfo.incBonus(inviteData.getCurrencyId(), inviteData.getAvailableReward());
            EntityDaoMrg.getInstance().getDao(PlayerDao.class).bonusInfoDao
                    .updateBonusDetails(player, BonusDetail.LuckySpin.getType(), bonusDetailsInfo, IntLists.singleton(inviteData.getCurrencyId()));

            final SpinBonusNote spinBonusNote = new SpinBonusNote();
            spinBonusNote.setPlayerId(pid);
            spinBonusNote.setHeadId(player.getHeadId());
            spinBonusNote.setBusiness_no(player.getBusiness_no());
            spinBonusNote.setPlayerName(player.getPlayerName());
            spinBonusNote.setTurntable(c_luckSpin.getTurntableType());
            spinBonusNote.setSubType(c_luckSpin.getSubType());
            final C_LuckSpin.RuleData ruleData = c_luckSpin.getRuleDataMap().get(player.getLanguage());
            if (ruleData != null) {
                spinBonusNote.setSpin(ruleData.activityName);
            }
            spinBonusNote.setCurrencyId(inviteData.getCurrencyId());
            spinBonusNote.setPrize(inviteData.getAvailableReward());
            EntityDaoMrg.getInstance().getDao(SpinBonusNoteDao.class).insert(spinBonusNote);

            //TODO 计算打码量
//            final WithdrawStandard withdrawStandard = player.getWithdrawStandard(inviteData.getCurrencyId());
//            withdrawStandard.incDrawStandard(BigDecimalUtils.mul(inviteData.getAvailableReward(), c_luckSpin.getRewardTurnoverMul(), 4));
            final TurnoverReason turnoverReason = TurnoverReason.LuckSpin;
            turnoverReason.setSource(ActivityMrg.LUCK_SPIN_REFERRAL + "");
            ScriptLoader.getInstance().consumerScript("PlayerWithdrawStandardScript",
                    (IPlayerScript script) -> script.drawStandard(player, turnoverReason, inviteData.getCurrencyId(), inviteData.getAvailableReward(),
                            BigDecimalUtils.mul(inviteData.getAvailableReward(), c_luckSpin.getRewardTurnoverMul(), 4)));

            //TODO 统计领取
            RedisPoolManager.getInstance().executeAsync(commands ->
                    commands.hincrby(RedisAllGame.Platform_All_LuckSpin.getKey(player.getBusiness_no(), c_luckSpin.getTurntableType()), inviteData.getCurrencyId() + "", (long) inviteData.getAvailableReward() * ActivityMrg.MULTIPLE)
            );

            if (!rewardRequest.getCurrencyMap().isEmpty()) {
                final Tuple2<Integer, Double> tuple2 = rewardRequest.getReward();
                ScriptLoader.getInstance().consumerScript("BonusScript", (IBonusScript script) ->
                        script.addBonusNote(TransactionFrom.LuckSpin_Referral, player, tuple2.getFirst(), tuple2.getSecond()));

                final GameLog playerActivityLog = new GameLog("platform_playerLuckSpinRewardLog");
                playerActivityLog.append("number", GuidGeneratorUtils.generateOrderId())
                        .append("business_no", player.getBusiness_no())
                        .append("site", player.getWebSite())
                        .append("channel", player.getChannel())
                        .append("playerId", player.getPlayerId())
                        .append("playerName", player.getPlayerName())
                        .append("language", player.getLanguage())
                        .append("agentId", player.getAgentId())
                        .append("channelId", player.getChannelId())
                        .append("region", player.getRegisterRegion())
                        .append("type", 1)//1.领取 2.参与
                        .append("activityId", c_luckSpin.getActivityId())
                        .append("activityUniqueId", c_luckSpin.getC_id())
                        .append("turntableType", c_luckSpin.getTurntableType())//转盘类型 1.邀请转盘 2.vip转盘 3.每日 4.每周
                        .append("currencyId", tuple2.getFirst())
                        .append("reward", tuple2.getSecond())
                        .append("logTime", TimeUtil.currentTimeMillis());
                HallServer.getInstance().getLogProducerMrg().send(playerActivityLog);
            }
        } catch (Exception e) {
            LOGGER.error("ReqLuckSpinReferralWithdrawHandler", e);
            res.setError(ErrorCode.Internal_Server_Error.getCode());
            MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
        }
    }

}
