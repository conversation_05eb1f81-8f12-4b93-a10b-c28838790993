package com.game.handler.tcp.hall;

import com.game.c_entity.middleplatform.C_BaseExchangeRate;
import com.game.engine.io.handler.TcpHandler;
import com.game.engine.math.BigDecimalUtils;
import com.game.engine.script.IHandlerEntity;
import com.game.engine.utils.MsgUtil;
import com.game.entity.player.Player;
import com.game.entity.player.stats.Stats;
import com.game.entity.player.stats.StatsInfo;
import com.game.enums.ErrorCode;
import com.game.hall.mrg.DataHallMrg;
import com.game.hall.mrg.MerchantData;
import com.game.hall.mrg.player.PlayerMrg;
import com.proto.CommonMessage;
import com.proto.HallMessage;
import com.proto.MIDMessage;

import java.util.Map;

@IHandlerEntity(mid = MIDMessage.MID.ReqAccountData_VALUE, msg = HallMessage.ReqAccountDataMessage.class)
public class ReqAccountDataHandler extends TcpHandler {
    @Override
    public void run() {
        final HallMessage.ResAccountDataMessage.Builder res = HallMessage.ResAccountDataMessage.newBuilder();
        res.setMsgID(MIDMessage.MID.ResAccountData_VALUE);

        final Player player = PlayerMrg.getInstance().getOnlinePlayer(pid, this.getClass().getSimpleName());
        if (player == null) {
            res.setError(ErrorCode.Player_Offline.getCode());
            MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
            return;
        }

        final MerchantData merchantData = DataHallMrg.getInstance().findMerchantData(this.getClass().getSimpleName(), player.getBusiness_no());
        if (merchantData == null) {
            res.setError(ErrorCode.Data_Error.getCode());
            MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
            return;
        }

        final boolean isManyCurrency = merchantData.getC_currencyMap().size() > 1;

        final StatsInfo statsInfo = player.getStatsInfo();
        final Map<Integer, Stats> statsMap = statsInfo.getStatsMap();
        double totalWin = 0;
        double totalBet = 0;
        double todayWin = 0;
        double todayBet = 0;
        for (Map.Entry<Integer, Stats> entry : statsMap.entrySet()) {
            final Stats stats = entry.getValue();

            if (isManyCurrency) {
                final C_BaseExchangeRate c_baseExchangeRate = DataHallMrg.getInstance().findC_BaseExchangeRate(this.getClass().getSimpleName(), entry.getKey());
                if (c_baseExchangeRate == null) {
                    continue;
                }
                totalWin = BigDecimalUtils.add(totalWin, BigDecimalUtils.mul(stats.getTotalWin(), c_baseExchangeRate.getExchangeRate(), 9), 9);
                totalBet = BigDecimalUtils.add(totalBet, BigDecimalUtils.mul(stats.getTotalBetAmount(), c_baseExchangeRate.getExchangeRate(), 9), 9);

                todayWin = BigDecimalUtils.add(todayWin, BigDecimalUtils.mul(stats.getDailyWin(), c_baseExchangeRate.getExchangeRate(), 9), 9);
                todayBet = BigDecimalUtils.add(todayBet, BigDecimalUtils.mul(stats.getDailyBetAmount(), c_baseExchangeRate.getExchangeRate(), 9), 9);
            } else {
                totalWin = BigDecimalUtils.add(totalWin, stats.getTotalWin(), 9);
                totalBet = BigDecimalUtils.add(totalBet, stats.getTotalBetAmount(), 9);

                todayWin = BigDecimalUtils.add(todayWin, stats.getDailyWin(), 9);
                todayBet = BigDecimalUtils.add(todayBet, stats.getDailyBetAmount(), 9);
            }
        }
        res.setAccountInfo(buildAccount(totalWin, totalBet, todayWin, todayBet));
        MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
    }

    private CommonMessage.AccountInfo buildAccount(double totalWin, double totalBet, double todayWin, double todayBet) {
        final CommonMessage.AccountInfo.Builder accountInfo = CommonMessage.AccountInfo.newBuilder();
        accountInfo.setTotalWin(totalWin)
                .setTotalBet(totalBet)
                .setTodayWin(todayWin)
                .setTodayBet(todayBet);
        return accountInfo.build();
    }
}
