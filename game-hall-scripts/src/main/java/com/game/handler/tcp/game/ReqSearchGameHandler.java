package com.game.handler.tcp.game;

import com.game.c_entity.merchant.C_GameApi;
import com.game.c_entity.middleplatform.C_BaseMerchant;
import com.game.engine.io.handler.TcpHandler;
import com.game.engine.script.IHandlerEntity;
import com.game.engine.utils.MsgUtil;
import com.game.enums.ErrorCode;
import com.game.hall.mrg.CommonMrg;
import com.game.hall.mrg.DataHallMrg;
import com.game.hall.mrg.MerchantData;
import com.proto.HallMessage;
import com.proto.MIDMessage;
import io.netty.util.internal.StringUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.List;

@IHandlerEntity(mid = MIDMessage.MID.ReqSearchGameData_VALUE, msg = HallMessage.ReqSearchGameDataMessage.class)
public class ReqSearchGameHandler extends TcpHandler {
    private static final Logger LOGGER = LoggerFactory.getLogger(ReqSearchGameHandler.class);

    @Override
    public void run() {
        final HallMessage.ResSearchGameDataMessage.Builder res = HallMessage.ResSearchGameDataMessage.newBuilder();
        res.setMsgID(MIDMessage.MID.ResSearchGameData_VALUE);

        try {
            final HallMessage.ReqSearchGameDataMessage req = (HallMessage.ReqSearchGameDataMessage) getMessage();
            final String host = req.getHost();
            final C_BaseMerchant c_baseHostMerchant = DataHallMrg.getInstance().findC_BaseHostMerchant(this.getClass().getSimpleName(), host);
            if (c_baseHostMerchant == null) {
                res.setError(ErrorCode.Data_Error.getCode());
                MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                return;
            }

            final String business_no = c_baseHostMerchant.getBusiness_no();
            final MerchantData merchantData = DataHallMrg.getInstance().findMerchantData(this.getClass().getSimpleName(), business_no);
            if (merchantData == null) {
                res.setError(ErrorCode.Data_Error.getCode());
                MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                return;
            }

            final int gameId = req.getGameId();
            final String gameName = req.getGameName();
            final int platformId = req.getPlatformId();
            final int language = req.getLanguage();

            if (StringUtil.isNullOrEmpty(gameName) && platformId == 0 && gameId == 0) {
                MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                return;
            }

            final List<C_GameApi> c_gameApis = new ArrayList<>();
            if (!StringUtil.isNullOrEmpty(gameName)) {
                final List<C_GameApi> c_gameApiList = merchantData.getC_gameApiMap().values().stream()
                        .filter(c_gameApi -> c_gameApi.getGameName().toLowerCase().contains(gameName.toLowerCase()))
                        .toList();
                c_gameApis.addAll(c_gameApiList);
            }

            if (platformId != 0) {
                final List<C_GameApi> c_gameApiList = merchantData.findC_platformId_gameApi(this.getClass().getSimpleName(), platformId);
                if (c_gameApiList != null && !c_gameApiList.isEmpty()) {
                    c_gameApis.addAll(c_gameApiList);
                }
            }

            if (gameId != 0) {
                final C_GameApi c_gameApi = merchantData.findC_GameApi(this.getClass().getSimpleName(), gameId);
                if (c_gameApi != null) {
                    c_gameApis.add(c_gameApi);
                }
            }

            for (C_GameApi c_gameApi : c_gameApis) {
                if (!c_gameApi.setLanguageGameApiData(language)) {
                    continue;
                }
                res.addGameApiInfo(CommonMrg.buildGameApiInfo(null, c_gameApi));
            }
            MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
        } catch (Exception e) {
            LOGGER.error("ReqSearchGameHandler", e);
            res.setError(ErrorCode.Internal_Server_Error.getCode());
            MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
        }
    }

}
