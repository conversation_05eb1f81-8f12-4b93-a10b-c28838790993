package com.game.handler.http.backstage;

import com.game.c_entity.merchant.C_RechargeWithdrawLimit;
import com.game.engine.io.handler.HttpHandler;
import com.game.engine.script.IHandlerEntity;
import com.game.engine.script.ScriptLoader;
import com.game.engine.util.misc.Tuple2;
import com.game.entity.player.Player;
import com.game.entity.player.WithdrawStandard;
import com.game.enums.ErrorCode;
import com.game.hall.mrg.DataHallMrg;
import com.game.hall.mrg.MerchantData;
import com.game.hall.mrg.currency.CurrencyMrg;
import com.game.hall.mrg.player.PlayerMrg;
import com.game.hall.script.IBillingScript;
import io.netty.util.internal.StringUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.HashMap;
import java.util.Map;

//http://127.0.0.1:8380/gmHall/reloadPlayerRealData?playerId=1000004&currencyId&type=1
@IHandlerEntity(path = "/gmHall/reloadPlayerRealData", desc = "获取玩家实时数据")
public class GmHall_PlayerRealHandler extends HttpHandler {
    private static final Logger LOGGER = LoggerFactory.getLogger(GmHall_PlayerRealHandler.class);

    @Override
    public void run() {
        try {
            final String playerId = (String) paramsMap.get("playerId");
            final String currencyId = (String) paramsMap.get("currencyId");
            final String type = (String) paramsMap.get("type");

            if (StringUtil.isNullOrEmpty(currencyId) || StringUtil.isNullOrEmpty(playerId)
                    || StringUtil.isNullOrEmpty(type)) {
                PlayerMrg.responseHttp(ErrorCode.Parameter_Error.getCode(), session, pid);
                return;
            }

            final Player player = PlayerMrg.getInstance().findDbPlayer(Long.parseLong(playerId));
            if (player == null) {
                PlayerMrg.responseHttp(ErrorCode.Account_NotExist.getCode(), session, pid);
                return;
            }

            final Map<String, Object> resMap = new HashMap<>();
            resMap.put("type", type);
            switch (Integer.parseInt(type)) {
                case 1://货币
                    final double amount = CurrencyMrg.getInstance().getCurrencyValue(player, Integer.parseInt(currencyId));
                    final double bonusAmount = CurrencyMrg.getInstance().getCurrencyValue(player, Integer.parseInt(currencyId) * 10);
                    resMap.put("currencyId", currencyId);
                    resMap.put("amount", amount);
                    resMap.put("bonusAmount", bonusAmount);

                    final MerchantData merchantData = DataHallMrg.getInstance().findMerchantData(this.getClass().getSimpleName(), player.getBusiness_no());
                    if (merchantData == null) {
                        PlayerMrg.responseHttp(ErrorCode.Data_Error.getCode(), session, pid);
                        return;
                    }
                    final C_RechargeWithdrawLimit c_limit = merchantData.findC_RechargeWithdrawLimit(this.getClass().getSimpleName(), Integer.parseInt(currencyId));
                    if (c_limit == null) {
                        PlayerMrg.responseHttp(ErrorCode.Data_Error.getCode(), session, pid);
                        return;
                    }
                    final Tuple2<Double, Double> tuple2 = ScriptLoader.getInstance().functionScript("BillingScript",
                            (IBillingScript script) -> script.calculateAvailableAmount(player, Integer.parseInt(currencyId), c_limit.getWithdrawType()));
                    final double available = tuple2.getFirst();
                    resMap.put("availableAmount", available);
                    break;
                case 2://打码
                    final WithdrawStandard withdrawStandard = player.getWithdrawStandard(Integer.parseInt(currencyId));
                    resMap.put("currencyId", currencyId);
                    resMap.put("drawStandard", withdrawStandard.getDrawStandard());
                    resMap.put("bettingVolume", withdrawStandard.getBettingVolume());
                    break;
            }
            PlayerMrg.responseHttp(resMap, session, pid);
        } catch (Exception e) {
            LOGGER.error("GmHall_ReloadPlayerRealDataHandler", e);
            PlayerMrg.responseHttp(ErrorCode.Internal_Server_Error.getCode(), session, pid);
        }
    }
}
