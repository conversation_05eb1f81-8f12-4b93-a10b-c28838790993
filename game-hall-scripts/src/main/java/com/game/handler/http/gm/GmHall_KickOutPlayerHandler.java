package com.game.handler.http.gm;

import com.game.engine.io.handler.HttpHandler;
import com.game.engine.script.IHandlerEntity;
import com.game.engine.script.ScriptLoader;
import com.game.entity.player.Player;
import com.game.enums.ErrorCode;
import com.game.hall.mrg.player.PlayerMrg;
import com.game.hall.script.IPlayerScript;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

//http://127.0.0.1:8380/gmHall/kickOutPlayer?playerId=
@IHandlerEntity(path = "/gmHall/kickOutPlayer", desc = "踢出玩家")
public class GmHall_KickOutPlayerHandler extends HttpHandler {
    private static final Logger LOGGER = LoggerFactory.getLogger(GmHall_KickOutPlayerHandler.class);

    @Override
    public void run() {
        try {
            final String playerId = (String) paramsMap.get("playerId");

            final Player player = PlayerMrg.getInstance().getOnlinePlayer(Long.parseLong(playerId), this.getClass().getSimpleName());
            if (player == null) {
                PlayerMrg.responseHttp(ErrorCode.Account_NotExist.getCode(), session, pid);
                return;
            }

            PlayerMrg.getInstance().sendKickOutPlayerMsg(player);
            PlayerMrg.getInstance().signOut(player);

            PlayerMrg.responseHttp(ErrorCode.Success.getCode(), session, pid);

        } catch (Exception e) {
            LOGGER.error("GmHall_KickOutPlayerHandler", e);
            PlayerMrg.responseHttp(ErrorCode.Internal_Server_Error.getCode(), session, pid);
        }
    }

}
