package com.game.handler.http.gm;

import com.game.engine.io.conf.ConstantConfig;
import com.game.engine.io.handler.HttpHandler;
import com.game.engine.script.IHandlerEntity;
import com.game.engine.utils.Config;
import com.game.enums.ErrorCode;
import com.game.hall.mrg.player.PlayerMrg;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

//http://127.0.0.1:8380/gmHall/reloadConstantConfig?default=true
@IHandlerEntity(path = "/gmHall/reloadConstantConfig", desc = "重新加载常量配置")
public class GmHall_ReloadConstantConfigHandler extends HttpHandler {
    private static final Logger LOGGER = LoggerFactory.getLogger(GmHall_ReloadConstantConfigHandler.class);

    @Override
    public void run() {
        try {
            ConstantConfig.reloadConstantConfig(Config.path, "constantConfig.xml");

            PlayerMrg.responseHttp(ErrorCode.Success.getCode(), session, pid);
        } catch (Exception e) {
            LOGGER.error("GmHall_ReloadConstantConfigHandler", e);
            PlayerMrg.responseHttp(ErrorCode.Internal_Server_Error.getCode(), session, pid);
        }
    }
}
