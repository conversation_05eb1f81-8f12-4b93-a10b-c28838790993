//package com.game.handler.http.backstage;
//
//import com.game.c_entity.merchant.C_RechargeWithdrawLimit;
//import com.game.engine.io.handler.HttpHandler;
//import com.game.engine.script.IHandlerEntity;
//import com.game.engine.script.ScriptLoader;
//import com.game.engine.util.misc.Tuple2;
//import com.game.engine.utils.JsonUtils;
//import com.game.entity.player.Player;
//import com.game.entity.player.WithdrawStandard;
//import com.game.enums.ErrorCode;
//import com.game.hall.mrg.DataHallMrg;
//import com.game.hall.mrg.MerchantData;
//import com.game.hall.mrg.currency.CurrencyMrg;
//import com.game.hall.mrg.player.PlayerMrg;
//import com.game.hall.script.IBillingScript;
//import io.netty.util.internal.StringUtil;
//import org.slf4j.Logger;
//import org.slf4j.LoggerFactory;
//
//import java.util.ArrayList;
//import java.util.HashMap;
//import java.util.List;
//import java.util.Map;
//
////http://127.0.0.1:8380/gmHall/reloadPlayerAllRealData?playerId=1000004&type=1
//@IHandlerEntity(path = "/gmHall/reloadPlayerAllRealData", desc = "获取玩家实时数据")
//public class GmHall_PlayerAllRealDataHandler extends HttpHandler {
//    private static final Logger LOGGER = LoggerFactory.getLogger(GmHall_PlayerAllRealDataHandler.class);
//
//    @Override
//    public void run() {
//        try {
//            final String playerId = (String) paramsMap.get("playerId");
//            final String type = (String) paramsMap.get("type");
//
//            if (StringUtil.isNullOrEmpty(playerId)
//                    || StringUtil.isNullOrEmpty(type)) {
//                PlayerMrg.responseHttp(ErrorCode.Parameter_Error.getCode(), session, pid);
//                return;
//            }
//
//            final Player player = PlayerMrg.getInstance().findDbPlayer(Long.parseLong(playerId));
//            if (player == null) {
//                PlayerMrg.responseHttp(ErrorCode.Account_NotExist.getCode(), session, pid);
//                return;
//            }
//
//            final Map<String, Object> resMap = new HashMap<>();
//            resMap.put("type", type);
//            switch (Integer.parseInt(type)) {
//                case 1://货币
//                    final List<CurrencyData> currencyDataList = new ArrayList<>();
//                    for (Map.Entry<Integer, Double> entry : player.getCurrencyMap().entrySet()) {
//                        final int currencyId = entry.getKey();
//                        if (String.valueOf(currencyId).length() == 5) {
//                            continue;
//                        }
//
//                        final double amount = CurrencyMrg.getInstance().getCurrencyValue(player, currencyId);
//                        final double bonusAmount = CurrencyMrg.getInstance().getCurrencyValue(player, currencyId * 10);
//                        final MerchantData merchantData = DataHallMrg.getInstance().findMerchantData(this.getClass().getSimpleName(), player.getBusiness_no());
//                        if (merchantData == null) {
//                            PlayerMrg.responseHttp(ErrorCode.Data_Error.getCode(), session, pid);
//                            return;
//                        }
//                        final C_RechargeWithdrawLimit c_limit = merchantData.findC_RechargeWithdrawLimit(this.getClass().getSimpleName(), currencyId);
//                        if (c_limit == null) {
//                            PlayerMrg.responseHttp(ErrorCode.Data_Error.getCode(), session, pid);
//                            return;
//                        }
//                        final Tuple2<Double, Double> tuple2 = ScriptLoader.getInstance().functionScript("BillingScript",
//                                (IBillingScript script) -> script.calculateAvailableAmount(player, currencyId, c_limit.getWithdrawType()));
//                        final double available = tuple2.getFirst();
//
//                        final CurrencyData currencyData = new CurrencyData();
//                        currencyData.setCurrencyId(currencyId);
//                        currencyData.setAmount(amount);
//                        currencyData.setBonusAmount(bonusAmount);
//                        currencyData.setAvailableAmount(available);
//                        currencyDataList.add(currencyData);
//                    }
//                    resMap.put("currencyData", JsonUtils.writeAsJson(currencyDataList));
//                    break;
//                case 2://打码
//                    final List<WithdrawData> withdrawDataList = new ArrayList<>();
//                    for (Map.Entry<Integer, Double> entry : player.getCurrencyMap().entrySet()) {
//                        final int currencyId = entry.getKey();
//                        if (String.valueOf(currencyId).length() == 5) {
//                            continue;
//                        }
//
//                        final WithdrawStandard withdrawStandard = player.getWithdrawStandard(currencyId);
//
//                        final WithdrawData withdrawData = new WithdrawData();
//                        withdrawData.setCurrencyId(currencyId);
//                        withdrawData.setDrawStandard(withdrawStandard.getDrawStandard());
//                        withdrawData.setBettingVolume(withdrawStandard.getBettingVolume());
//                        withdrawDataList.add(withdrawData);
//                    }
//                    resMap.put("withdrawData", JsonUtils.writeAsJson(withdrawDataList));
//                    break;
//            }
//            PlayerMrg.responseHttp(resMap, session, pid);
//        } catch (Exception e) {
//            LOGGER.error("GmHall_ReloadPlayerRealDataHandler", e);
//            PlayerMrg.responseHttp(ErrorCode.Internal_Server_Error.getCode(), session, pid);
//        }
//    }
//
//    public static class CurrencyData {
//        public int currencyId;
//        public double amount;
//        public double bonusAmount;
//        public double availableAmount;
//
//        public double getAmount() {
//            return amount;
//        }
//
//        public void setAmount(double amount) {
//            this.amount = amount;
//        }
//
//        public int getCurrencyId() {
//            return currencyId;
//        }
//
//        public void setCurrencyId(int currencyId) {
//            this.currencyId = currencyId;
//        }
//
//        public double getBonusAmount() {
//            return bonusAmount;
//        }
//
//        public void setBonusAmount(double bonusAmount) {
//            this.bonusAmount = bonusAmount;
//        }
//
//        public double getAvailableAmount() {
//            return availableAmount;
//        }
//
//        public void setAvailableAmount(double availableAmount) {
//            this.availableAmount = availableAmount;
//        }
//    }
//
//    public static class WithdrawData {
//        public int currencyId;
//        public double drawStandard;
//        public double bettingVolume;
//
//        public int getCurrencyId() {
//            return currencyId;
//        }
//
//        public void setCurrencyId(int currencyId) {
//            this.currencyId = currencyId;
//        }
//
//        public double getDrawStandard() {
//            return drawStandard;
//        }
//
//        public void setDrawStandard(double drawStandard) {
//            this.drawStandard = drawStandard;
//        }
//
//        public double getBettingVolume() {
//            return bettingVolume;
//        }
//
//        public void setBettingVolume(double bettingVolume) {
//            this.bettingVolume = bettingVolume;
//        }
//    }
//}
