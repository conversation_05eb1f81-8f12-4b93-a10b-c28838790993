package com.game.handler.http.gmtest;


import com.game.dao.player.PlayerDao;
import com.game.engine.io.handler.HttpHandler;
import com.game.engine.script.IHandlerEntity;
import com.game.entity.player.Player;
import com.game.entity.player.activity.weeklyRaffle.WeeklyRaffleInfo;
import com.game.enums.ErrorCode;
import com.game.hall.mrg.player.PlayerMrg;
import com.game.manager.EntityDaoMrg;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

//http://127.0.0.1:8380/hall/test/weeklyRaffle?playerId=200100011
@IHandlerEntity(path = "/hall/test/weeklyRaffle", desc = "")
public class GmHallTestWeeklyRaffleHandler extends HttpHandler {
    private static final Logger LOGGER = LoggerFactory.getLogger(GmHallTestWeeklyRaffleHandler.class);

    @Override
    public void run() {
        try {
            final String playerId = (String) paramsMap.get("playerId");

            final Player player = PlayerMrg.getInstance().findDbPlayer(Long.parseLong(playerId));
            if (player == null) {
                PlayerMrg.responseHttp(ErrorCode.Account_NotExist.getCode(), session, pid);
                return;
            }

            final WeeklyRaffleInfo weeklyRaffleInfo = player.getWeeklyRaffleInfo();

            weeklyRaffleInfo.reset();
            EntityDaoMrg.getInstance().getDao(PlayerDao.class)
                    .updateWeeklyRaffleInfo(Long.parseLong(playerId), weeklyRaffleInfo);

            PlayerMrg.responseHttp(ErrorCode.Success.getCode(), session, pid);
        } catch (Exception e) {
            LOGGER.error("GmHallTestWeeklyRaffleHandler", e);
            PlayerMrg.responseHttp(ErrorCode.Internal_Server_Error.getCode(), session, pid);
        }
    }

}
