package com.game.handler.http.gmtest;


import com.game.dao.player.PlayerDao;
import com.game.engine.io.handler.HttpHandler;
import com.game.engine.script.IHandlerEntity;
import com.game.engine.utils.MsgUtil;
import com.game.entity.player.Player;
import com.game.entity.player.PlayerFields;
import com.game.enums.ErrorCode;
import com.game.hall.mrg.player.PlayerMrg;
import com.game.manager.EntityDaoMrg;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.mongodb.core.query.Update;

//http://127.0.0.1:8480/hall/test/playerLastRefreshTime?playerId=92036950&time=
@IHandlerEntity(path = "/hall/test/playerLastRefreshTime", desc = "")
public class GmHallTestPlayerLastRefreshTimeHandler extends HttpHandler {
    private static final Logger LOGGER = LoggerFactory.getLogger(GmHallTestPlayerLastRefreshTimeHandler.class);

    @Override
    public void run() {
        try {
            final String playerId = (String) paramsMap.get("playerId");
            final String time = (String) paramsMap.get("time");

            final Player player = PlayerMrg.getInstance().findDbPlayer(Long.parseLong(playerId));
            if (player == null) {
                PlayerMrg.responseHttp(ErrorCode.Account_NotExist.getCode(), session, pid);
                return;
            }

            player.setLastRefreshTime(Long.parseLong(time));
            final Update update = new Update();
            update.set(PlayerFields.lastRefreshTime, player.getLastRefreshTime());
            EntityDaoMrg.getInstance().getDao(PlayerDao.class)
                    .updatePlayer(player.getPlayerId(), update);

            PlayerMrg.responseHttp(ErrorCode.Success.getCode(), session, pid);
        } catch (Exception e) {
            LOGGER.error("", e);
            PlayerMrg.responseHttp(ErrorCode.Internal_Server_Error.getCode(), session, pid);
        }
    }

}
