package com.game.handler.http.gmtest;


import com.game.dao.player.PlayerDao;
import com.game.engine.io.handler.HttpHandler;
import com.game.engine.script.IHandlerEntity;
import com.game.entity.player.Player;
import com.game.entity.player.vip.VipClub;
import com.game.enums.ErrorCode;
import com.game.hall.mrg.player.PlayerMrg;
import com.game.manager.EntityDaoMrg;
import io.netty.util.internal.StringUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

//http://127.0.0.1:8380/hall/test/vipSin?playerId=200100011&activationTime=
@IHandlerEntity(path = "/hall/test/vipSin", desc = "")
public class GmHallTestVipSinHandler extends HttpHandler {
    private static final Logger LOGGER = LoggerFactory.getLogger(GmHallTestVipSinHandler.class);

    @Override
    public void run() {
        try {
            final String playerId = (String) paramsMap.get("playerId");
            final String activationTime = (String) paramsMap.get("activationTime");

            if (StringUtil.isNullOrEmpty(playerId) || StringUtil.isNullOrEmpty(activationTime)) {
                PlayerMrg.responseHttp(ErrorCode.Parameter_Error.getCode(), session, pid);
                return;
            }

            final Player player = PlayerMrg.getInstance().findDbPlayer(Long.parseLong(playerId));
            if (player == null) {
                PlayerMrg.responseHttp(ErrorCode.Account_NotExist.getCode(), session, pid);
                return;
            }

            final VipClub vipClub = player.getVipClub();
            vipClub.setActivationTime(Long.parseLong(activationTime));
            EntityDaoMrg.getInstance().getDao(PlayerDao.class).updateVipClubInfo(pid, vipClub);

            PlayerMrg.responseHttp(ErrorCode.Success.getCode(), session, pid);
        } catch (Exception e) {
            LOGGER.error("GmHallTestVipSinHandler", e);
            PlayerMrg.responseHttp(ErrorCode.Internal_Server_Error.getCode(), session, pid);
        }
    }

}
