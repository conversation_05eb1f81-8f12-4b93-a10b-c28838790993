package com.game.handler.http.gmtest;


import com.game.dao.player.PlayerDao;
import com.game.engine.io.handler.HttpHandler;
import com.game.engine.script.IHandlerEntity;
import com.game.engine.utils.MsgUtil;
import com.game.entity.player.GameInfo;
import com.game.entity.player.FreeGameInfo;
import com.game.entity.player.Player;
import com.game.enums.ErrorCode;
import com.game.hall.mrg.player.PlayerMrg;
import com.game.manager.EntityDaoMrg;
import it.unimi.dsi.fastutil.ints.IntLists;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

//http://127.0.0.1:8480/hall/testFreeGame?playerId=92036950&currency=1000&gameId=300002&freeTimes=5
@IHandlerEntity(path = "/hall/testFreeGame", desc = "")
public class GmHallTestFreeGameHandler extends HttpHandler {
    private static final Logger LOGGER = LoggerFactory.getLogger(GmHallTestFreeGameHandler.class);

    @Override
    public void run() {
        try {
            final String playerId = (String) paramsMap.get("playerId");
            final String currency = (String) paramsMap.get("currency");
            final String gameId = (String) paramsMap.get("gameId");
            final String freeTimes = (String) paramsMap.get("freeTimes");

            final Player player = PlayerMrg.getInstance().findDbPlayer(Long.parseLong(playerId));
            if (player == null) {
                MsgUtil.responseHttp(ErrorCode.Account_NotExist.getCode(), session);
                return;
            }

            final FreeGameInfo freeGameInfo = player.getFreeGameInfo(Integer.parseInt(currency));
            final GameInfo gameInfo = freeGameInfo.getFreeGame(Integer.parseInt(gameId));
            gameInfo.incFreeTimes(Integer.parseInt(freeTimes));
            EntityDaoMrg.getInstance().getDao(PlayerDao.class)
                    .updateFreeGameInfo(player, IntLists.singleton(Integer.parseInt(currency)));

            MsgUtil.responseHttp(ErrorCode.Success.getCode(), session);
        } catch (Exception e) {
            LOGGER.error("", e);
            MsgUtil.responseHttp(ErrorCode.Internal_Server_Error.getCode(), session);
        }
    }

}
