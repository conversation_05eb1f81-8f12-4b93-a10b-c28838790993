package com.game.handler.http.backstage;

import com.game.dao.player.PlayerDao;
import com.game.engine.io.handler.HttpHandler;
import com.game.engine.script.IHandlerEntity;
import com.game.engine.script.ScriptLoader;
import com.game.entity.player.Player;
import com.game.enums.ErrorCode;
import com.game.hall.mrg.player.PlayerMrg;
import com.game.hall.script.activity.IWeeklyRaffleScript;
import com.game.manager.EntityDaoMrg;
import io.netty.util.internal.StringUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.HashMap;
import java.util.Map;

//http://127.0.0.1:8380/gmHall/generateTicketsNote?playerId=1000004
@IHandlerEntity(path = "/gmHall/generateTicketsNote", desc = "生成中奖号码")
public class GmHall_GenerateTicketsNoteHandler extends HttpHandler {
    private static final Logger LOGGER = LoggerFactory.getLogger(GmHall_GenerateTicketsNoteHandler.class);

    @Override
    public void run() {
        try {
            final String playerId = (String) paramsMap.get("playerId");

            if (StringUtil.isNullOrEmpty(playerId)) {
                PlayerMrg.responseHttp(ErrorCode.Parameter_Error.getCode(), session, pid);
                return;
            }

            final Player player = PlayerMrg.getInstance().findDbPlayer(Long.parseLong(playerId));
            if (player == null) {
                PlayerMrg.responseHttp(ErrorCode.Account_NotExist.getCode(), session, pid);
                return;
            }

            final int ticketNumbers = ScriptLoader.getInstance().functionScript("WeeklyRaffleScript",
                    (IWeeklyRaffleScript script) -> script.generateTicketsNote(player, player.getWeeklyRaffleInfo()));
            EntityDaoMrg.getInstance().getDao(PlayerDao.class)
                    .updateWeeklyRaffleInfo(player.getPlayerId(), player.getWeeklyRaffleInfo());

            final Map<String, Object> resMap = new HashMap<>();
            resMap.put("ticketNumbers", ticketNumbers);

            PlayerMrg.responseHttp(resMap, session, pid);
        } catch (Exception e) {
            LOGGER.error("", e);
            PlayerMrg.responseHttp(ErrorCode.Internal_Server_Error.getCode(), session, pid);
        }
    }
}
