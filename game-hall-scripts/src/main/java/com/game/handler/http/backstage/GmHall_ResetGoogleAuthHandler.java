package com.game.handler.http.backstage;

import com.game.dao.player.PlayerDao;
import com.game.engine.io.handler.HttpHandler;
import com.game.engine.script.IHandlerEntity;
import com.game.entity.player.Player;
import com.game.entity.player.PlayerFields;
import com.game.enums.ErrorCode;
import com.game.hall.mrg.player.PlayerMrg;
import com.game.manager.EntityDaoMrg;
import io.netty.util.internal.StringUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.mongodb.core.query.Update;

//http://127.0.0.1:8380/gmHall/resetGoogleAuth?playerId=1000004
@IHandlerEntity(path = "/gmHall/resetGoogleAuth", desc = "重置谷歌认证")
public class GmHall_ResetGoogleAuthHandler extends HttpHandler {
    private static final Logger LOGGER = LoggerFactory.getLogger(GmHall_ResetGoogleAuthHandler.class);

    @Override
    public void run() {
        try {
            final String playerId = (String) paramsMap.get("playerId");

            if (StringUtil.isNullOrEmpty(playerId)) {
                PlayerMrg.responseHttp(ErrorCode.Parameter_Error.getCode(), session, pid);
                return;
            }

            final Player player = PlayerMrg.getInstance().findDbPlayer(Long.parseLong(playerId));
            if (player == null) {
                PlayerMrg.responseHttp(ErrorCode.Account_NotExist.getCode(), session, pid);
                return;
            }

            player.setSecretKey("");
            player.setVerify2FA(false);

            final Update update = new Update();
            update.set(PlayerFields.secretKey, player.getSecretKey())
                    .set(PlayerFields.verify2FA, player.isVerify2FA());
            EntityDaoMrg.getInstance().getDao(PlayerDao.class)
                    .updatePlayer(player.getPlayerId(), update);

            PlayerMrg.responseHttp(ErrorCode.Success.getCode(), session, pid);
        } catch (Exception e) {
            LOGGER.error("GmHall_ResetGoogleAuthHandler", e);
            PlayerMrg.responseHttp(ErrorCode.Internal_Server_Error.getCode(), session, pid);
        }
    }
}
