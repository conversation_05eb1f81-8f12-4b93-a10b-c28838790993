package com.game.handler.http.gmtest;


import com.game.dao.player.PlayerDao;
import com.game.engine.io.handler.HttpHandler;
import com.game.engine.script.IHandlerEntity;
import com.game.entity.player.Player;
import com.game.entity.player.activity.luckSpin.LuckSpinData;
import com.game.entity.player.activity.luckSpin.LuckSpinInfo;
import com.game.enums.ErrorCode;
import com.game.hall.mrg.player.PlayerMrg;
import com.game.manager.EntityDaoMrg;
import io.netty.util.internal.StringUtil;
import it.unimi.dsi.fastutil.ints.IntLists;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

//http://127.0.0.1:8380/hall/test/LuckSpin?playerId=200100011&activityId=&vipSubType=
@IHandlerEntity(path = "/hall/test/LuckSpin", desc = "")
public class GmHallTestLuckSpinHandler extends HttpHandler {
    private static final Logger LOGGER = LoggerFactory.getLogger(GmHallTestLuckSpinHandler.class);

    @Override
    public void run() {
        try {
            final String playerId = (String) paramsMap.get("playerId");
            final String activityId = (String) paramsMap.get("activityId");
            final String vipSubType = (String) paramsMap.get("vipSubType");

            final Player player = PlayerMrg.getInstance().findDbPlayer(Long.parseLong(playerId));
            if (player == null) {
                PlayerMrg.responseHttp(ErrorCode.Account_NotExist.getCode(), session, pid);
                return;
            }

            final int id = StringUtil.isNullOrEmpty(vipSubType) ? Integer.parseInt(activityId) : Integer.parseInt(activityId + "" + vipSubType);
            final LuckSpinInfo luckSpinInfo = player.getLuckSpinInfo();
            final LuckSpinData luckSpinData = luckSpinInfo.getLuckSpinData(id);
            if (luckSpinData == null) {
                LOGGER.warn("playerId：{}，activityId：{}， not init", player.getPlayerId(), id);
                return;
            }

            luckSpinData.reset();
            luckSpinData.setUniqueId(0);
            EntityDaoMrg.getInstance().getDao(PlayerDao.class).luckSpinDao
                    .updateLuckSpin(Long.parseLong(playerId), luckSpinInfo, IntLists.singleton(id));
            PlayerMrg.responseHttp(ErrorCode.Success.getCode(), session, pid);
        } catch (Exception e) {
            LOGGER.error("GmHallTestLuckSpinHandler", e);
            PlayerMrg.responseHttp(ErrorCode.Internal_Server_Error.getCode(), session, pid);
        }
    }

}
