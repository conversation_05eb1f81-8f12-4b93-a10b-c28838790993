package com.game.handler.http.backstage;

import com.game.dao.player.PlayerDao;
import com.game.engine.io.handler.HttpHandler;
import com.game.engine.script.IHandlerEntity;
import com.game.engine.utils.Config;
import com.game.engine.utils.Symbol;
import com.game.entity.player.Player;
import com.game.entity.player.PlayerFields;
import com.game.enums.ErrorCode;
import com.game.hall.main.HallServer;
import com.game.hall.mrg.player.PlayerMrg;
import com.game.manager.EntityDaoMrg;
import io.netty.util.internal.StringUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.mongodb.core.query.Update;

//http://127.0.0.1:8380/gmHall/batchPlayerTitle?playerIds=1000004,10000,100000&state=2
@IHandlerEntity(path = "/gmHall/batchPlayerTitle", desc = "封号,冻结")
public class GmHall_BatchPlayerTitleDataHandler extends HttpHandler {
    private static final Logger LOGGER = LoggerFactory.getLogger(GmHall_BatchPlayerTitleDataHandler.class);

    @Override
    public void run() {
        try {
            if (Config.SERVER_ID != 4000) {
                return;
            }

            final String playerId = (String) paramsMap.get("playerIds");
            final String state = (String) paramsMap.get("state");

            if (StringUtil.isNullOrEmpty(playerId) || StringUtil.isNullOrEmpty(state)) {
                PlayerMrg.responseHttp(ErrorCode.Parameter_Error.getCode(), session, pid);
                return;
            }

            final String[] playerIds = playerId.split(Symbol.DOUHAO_REG);
            for (String id : playerIds) {
                //更新数据库
                final long pId = Long.parseLong(id.trim());
                final Update update = new Update();
                update.set(PlayerFields.state, Integer.parseInt(state));
                EntityDaoMrg.getInstance().getDao(PlayerDao.class)
                        .updatePlayer(pId, update);

                final Player player = PlayerMrg.getInstance().getOnlinePlayerMap().get(pId);
                if (player == null) {
                    continue;
                }

                HallServer.getInstance().asyncExecute(player.getPlayerId(), () -> {
                    PlayerMrg.getInstance().sendKickOutPlayerMsg(player);
                    PlayerMrg.getInstance().signOut(player);
                });
            }

            PlayerMrg.responseHttp(ErrorCode.Success.getCode(), session, pid);
        } catch (Exception e) {
            LOGGER.error("", e);
            PlayerMrg.responseHttp(ErrorCode.Internal_Server_Error.getCode(), session, pid);
        }
    }
}
