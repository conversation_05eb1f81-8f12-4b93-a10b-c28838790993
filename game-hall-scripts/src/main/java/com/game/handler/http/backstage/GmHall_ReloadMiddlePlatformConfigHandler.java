package com.game.handler.http.backstage;


import com.game.c_entity.middleplatform.C_BaseCurrency;
import com.game.engine.io.handler.HttpHandler;
import com.game.engine.script.IHandlerEntity;
import com.game.engine.script.ScriptLoader;
import com.game.enums.ErrorCode;
import com.game.enums.MiddleConfigType;
import com.game.hall.mrg.DataHallMrg;
import com.game.hall.mrg.currency.CurrencyMrg;
import com.game.hall.mrg.player.PlayerMrg;
import com.game.hall.script.INoticeScript;
import com.game.utils.VirtualThreadUtils;
import io.netty.util.internal.StringUtil;
import it.unimi.dsi.fastutil.ints.Int2ObjectMap;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.HashSet;
import java.util.Map;
import java.util.Set;

//http://127.0.0.1:8380/gmHall/middlePlatformReloadConfig?business_no=&reloadType=101
@IHandlerEntity(path = "/gmHall/middlePlatformReloadConfig", desc = "重新加载中台配置")
public class GmHall_ReloadMiddlePlatformConfigHandler extends HttpHandler {
    private static final Logger LOGGER = LoggerFactory.getLogger(GmHall_ReloadMiddlePlatformConfigHandler.class);

    @Override
    public void run() {
        try {
            final String reloadType = (String) paramsMap.get("reloadType");
            final String business_no = (String) paramsMap.get("business_no");

            if (StringUtil.isNullOrEmpty(reloadType)) {
                PlayerMrg.responseHttp(ErrorCode.Parameter_Error.getCode(), session, pid);
                return;
            }

            switch (MiddleConfigType.valueOf(Integer.parseInt(reloadType))) {
                case Merchant:
                    if (StringUtil.isNullOrEmpty(business_no)) {
                        PlayerMrg.responseHttp(ErrorCode.Parameter_Error.getCode(), session, pid);
                        return;
                    }
                    DataHallMrg.getInstance().loadMerchant(business_no);
                    break;
                case Currency:
                    DataHallMrg.getInstance().loadBaseCurrency();
                    final Map<Integer, C_BaseCurrency> c_baseCurrencyMap = DataHallMrg.getInstance().getC_baseCurrencyMap();
                    final Set<Integer> currencyIds = new HashSet<>(c_baseCurrencyMap.keySet());
                    CurrencyMrg.getInstance().registerCurrencyHandler(currencyIds);
                    break;
                case PaymentMethod:
                    DataHallMrg.getInstance().loadBasePaymentMethod();
                    break;
                case ExchangeRate:
                    DataHallMrg.getInstance().loadBaseExchangeRate();
                    break;
                case Inbox:
                    DataHallMrg.getInstance().loadBaseInbox();
                    break;
                case GameType:
                    DataHallMrg.getInstance().loadBaseGameType();
                    break;
                case Language:
                    DataHallMrg.getInstance().loadBaseLanguage();
                    break;
                case Head:
                    DataHallMrg.getInstance().loadHead();
                    break;
                case MaintainNotice:
                    DataHallMrg.getInstance().loadBaseMaintainNotice();
                    VirtualThreadUtils.execute(() -> {
                        ScriptLoader.getInstance().consumerScript("NoticeScript",
                                INoticeScript::notifyMiddlePlatformNotice
                        );
                    });
                    break;
                case ServerConfig:
                    DataHallMrg.getInstance().loadBaseServerConfig();
                    break;
                default:
                    throw new IllegalArgumentException(reloadType + "，not exist");
            }

            LOGGER.info("reloadMiddlePlatformConfig，business_no：{}，reloadType：{}， success ...", business_no, reloadType);
            PlayerMrg.responseHttp(ErrorCode.Success.getCode(), session, pid);
        } catch (Exception e) {
            LOGGER.error("", e);
            PlayerMrg.responseHttp(ErrorCode.Internal_Server_Error.getCode(), session, pid);
        }
    }
}
