package com.game.handler.http.gmtest;


import com.alibaba.fastjson.JSONObject;
import com.game.c_entity.merchant.C_PlatformRecharge;
import com.game.engine.io.conf.ConstantConfig;
import com.game.engine.io.handler.HttpHandler;
import com.game.engine.script.IHandlerEntity;
import com.game.engine.utils.HttpUtils11;
import com.game.engine.utils.JsonUtils;
import com.game.engine.utils.MsgUtil;
import com.game.enums.ErrorCode;
import com.game.enums.redis.RedisRanking;
import com.proto.BillingMessage;
import com.proto.MIDMessage;
import org.apache.http.HttpStatus;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.net.http.HttpResponse;
import java.util.Objects;

//http://127.0.0.1:8480/hall/test/walletAddress?playerId=200100016&currencyId=2000&=network="TRC20"
@IHandlerEntity(path = "/hall/test/walletAddress", desc = "")
public class GmHall_TestWalletAddressHandler extends HttpHandler {
    private static final Logger LOGGER = LoggerFactory.getLogger(GmHall_TestWalletAddressHandler.class);

    @Override
    public void run() {
        try {
            final String playerId = (String) paramsMap.get("playerId");
            final String currencyId = (String) paramsMap.get("currencyId");
            final String network = (String) paramsMap.get("network");

            final String url = "http://" + "*************" + "?"
                    + "playerId=" + playerId + "&"
                    + "currencyId=" + currencyId + "&"
                    + "network=" + network;
            HttpUtils11.sendGetAsyncHttp(url)
                    .whenComplete((this::notifyClient));

            MsgUtil.responseHttp(ErrorCode.Success.getCode(), session);
        } catch (Exception e) {
            LOGGER.error("", e);
            MsgUtil.responseHttp(ErrorCode.Internal_Server_Error.getCode(), session);
        }
    }

    private void notifyClient(HttpResponse<String> httpResponse, Throwable throwable) {
        final BillingMessage.ResDepositDataMessage.Builder res = BillingMessage.ResDepositDataMessage.newBuilder();
        res.setMsgID(MIDMessage.MID.ResDepositData_VALUE);

        if (throwable != null || httpResponse.statusCode() != HttpStatus.SC_OK) {
            res.setError(ErrorCode.Data_Error.getCode());
            throw new IllegalArgumentException(throwable);
        }

        try {
            final String body = httpResponse.body();
            LOGGER.warn("body：{}", body);
            final JSONObject dataMap = JsonUtils.readFromJson(body, JSONObject.class);
            final String code = dataMap.getString("code");
            if (!Objects.equals(code, "0")) {
                res.setError(ErrorCode.Data_Error.getCode());
                return;
            }
            final String walletAddress = dataMap.getString("walletAddress");


        } catch (Exception e) {
            LOGGER.error("ReqDepositDataHandler", e);
            res.setError(ErrorCode.Internal_Server_Error.getCode());
        }
    }
}
