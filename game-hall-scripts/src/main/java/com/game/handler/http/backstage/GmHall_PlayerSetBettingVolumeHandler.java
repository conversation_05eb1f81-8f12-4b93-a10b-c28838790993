package com.game.handler.http.backstage;

import com.game.dao.player.PlayerDao;
import com.game.engine.io.handler.HttpHandler;
import com.game.engine.script.IHandlerEntity;
import com.game.engine.script.ScriptLoader;
import com.game.entity.player.Player;
import com.game.enums.ErrorCode;
import com.game.enums.TurnoverReason;
import com.game.hall.mrg.player.PlayerMrg;
import com.game.hall.script.IPlayerScript;
import com.game.manager.EntityDaoMrg;
import io.netty.util.internal.StringUtil;
import it.unimi.dsi.fastutil.ints.IntLists;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

//http://127.0.0.1:8380/gmHall/playerSetBettingVolume?playerId=1000004&currencyId=&drawStandard=2&bettingVolume=
@IHandlerEntity(path = "/gmHall/playerSetBettingVolume", desc = "设置打码量")
public class GmHall_PlayerSetBettingVolumeHandler extends HttpHandler {
    private static final Logger LOGGER = LoggerFactory.getLogger(GmHall_PlayerSetBettingVolumeHandler.class);

    @Override
    public void run() {
        try {
            final String playerId = (String) paramsMap.get("playerId");
            final String currencyId = (String) paramsMap.get("currencyId");
            final String drawStandard = (String) paramsMap.get("drawStandard");
            final String bettingVolume = (String) paramsMap.get("bettingVolume");

            if (StringUtil.isNullOrEmpty(playerId) || StringUtil.isNullOrEmpty(currencyId)
                    || (StringUtil.isNullOrEmpty(drawStandard) && StringUtil.isNullOrEmpty(bettingVolume))) {
                PlayerMrg.responseHttp(ErrorCode.Parameter_Error.getCode(), session, pid);
                return;
            }

            final Player player = PlayerMrg.getInstance().findDbPlayer(Long.parseLong(playerId));
            if (player == null) {
                PlayerMrg.responseHttp(ErrorCode.Account_NotExist.getCode(), session, pid);
                return;
            }

//            final WithdrawStandard withdrawStandard = player.getWithdrawStandard(Integer.parseInt(currencyId));
            if (!StringUtil.isNullOrEmpty(drawStandard)) {
//                withdrawStandard.incDrawStandard(Double.parseDouble(drawStandard));
                ScriptLoader.getInstance().consumerScript("PlayerWithdrawStandardScript",
                        (IPlayerScript script) -> script.drawStandard(player, TurnoverReason.BackgroundSettings, Integer.parseInt(currencyId), 0,
                                Double.parseDouble(drawStandard)));
            }
            if (!StringUtil.isNullOrEmpty(bettingVolume)) {
//                withdrawStandard.incBettingTurnover(Double.parseDouble(bettingVolume));
                ScriptLoader.getInstance().consumerScript("PlayerWithdrawStandardScript",
                        (IPlayerScript script) -> script.bettingTurnover(player, TurnoverReason.BackgroundSettings, Integer.parseInt(currencyId),
                                Double.parseDouble(bettingVolume)));
            }
            EntityDaoMrg.getInstance().getDao(PlayerDao.class)
                    .updateWithdrawStandard(player, IntLists.singleton(Integer.parseInt(currencyId)));

            PlayerMrg.responseHttp(ErrorCode.Success.getCode(), session, pid);
        } catch (Exception e) {
            LOGGER.error("GmHall_PlayerSetBettingVolumeHandler", e);
            PlayerMrg.responseHttp(ErrorCode.Internal_Server_Error.getCode(), session, pid);
        }
    }
}
