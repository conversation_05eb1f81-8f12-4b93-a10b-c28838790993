package com.game.handler.http.gmtest;


import com.game.dao.player.PlayerDao;
import com.game.engine.io.handler.HttpHandler;
import com.game.engine.script.IHandlerEntity;
import com.game.engine.utils.MsgUtil;
import com.game.engine.utils.TimeUtil;
import com.game.entity.player.Player;
import com.game.entity.player.bonus.BonusInfo;
import com.game.entity.player.bonus.BonusInfoFields;
import com.game.enums.BonusDetail;
import com.game.enums.ErrorCode;
import com.game.hall.mrg.player.PlayerMrg;
import com.game.manager.EntityDaoMrg;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

//http://127.0.0.1:8480/hall/test/bonus?playerId=29511584&bonusType=6,7,8
@IHandlerEntity(path = "/hall/test/bonus", desc = "")
public class GmHallTestBonusHandler extends HttpHandler {
    private static final Logger LOGGER = LoggerFactory.getLogger(GmHallTestBonusHandler.class);

    @Override
    public void run() {
        try {
            final String playerId = (String) paramsMap.get("playerId");
            final String bonusType = (String) paramsMap.get("bonusType");

            final Player player = PlayerMrg.getInstance().findDbPlayer(Long.parseLong(playerId));
            if (player == null) {
                MsgUtil.responseHttp(ErrorCode.Account_NotExist.getCode(), session);
                return;
            }

            final BonusInfo bonusInfo = player.getBonusInfo();

            //充电
            if (Integer.parseInt(bonusType) == BonusDetail.Recharge.getType()) {
                bonusInfo.setRechargeEndTime(TimeUtil.currentTimeMillis());
                EntityDaoMrg.getInstance().getDao(PlayerDao.class)
                        .bonusInfoDao.updateBonusInfo(player.getPlayerId(), BonusInfoFields.rechargeEndTime, bonusInfo.getRechargeEndTime());
            }

            //周返水
            if (Integer.parseInt(bonusType) == BonusDetail.WeeklyCashBack.getType()) {
                bonusInfo.setWeeklyEndTime(TimeUtil.currentTimeMillis());
                EntityDaoMrg.getInstance().getDao(PlayerDao.class)
                        .bonusInfoDao.updateBonusInfo(player.getPlayerId(), BonusInfoFields.weeklyEndTime, bonusInfo.getWeeklyEndTime());
            }

            //月返水
            if (Integer.parseInt(bonusType) == BonusDetail.MonthlyCashBack.getType()) {
                bonusInfo.setMonthlyEndTime(TimeUtil.currentTimeMillis());
                EntityDaoMrg.getInstance().getDao(PlayerDao.class)
                        .bonusInfoDao.updateBonusInfo(player.getPlayerId(), BonusInfoFields.monthlyEndTime, bonusInfo.getMonthlyEndTime());
            }
            MsgUtil.responseHttp(ErrorCode.Success.getCode(), session);
        } catch (Exception e) {
            LOGGER.error("", e);
            MsgUtil.responseHttp(ErrorCode.Internal_Server_Error.getCode(), session);
        }
    }
}
