package com.game.handler.http.backstage;

import com.game.engine.io.handler.HttpHandler;
import com.game.engine.script.IHandlerEntity;
import com.game.engine.script.ScriptLoader;
import com.game.entity.player.Player;
import com.game.enums.ErrorCode;
import com.game.hall.main.HallServer;
import com.game.hall.mrg.player.PlayerMrg;
import com.game.hall.script.IPlayerScript;
import io.netty.util.internal.StringUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Iterator;
import java.util.Map;
import java.util.Objects;

//http://127.0.0.1:8380/gmHall/kickOutAllPlayer?business_no=
@IHandlerEntity(path = "/gmHall/kickOutAllPlayer", desc = "踢出所有玩家")
public class GmHall_KickOutAllPlayerHandler extends HttpHandler {
    private static final Logger LOGGER = LoggerFactory.getLogger(GmHall_KickOutAllPlayerHandler.class);

    @Override
    public void run() {
        try {
//            Config.serverState = ServerState.MAINTAIN;
            final String business_no = (String) paramsMap.get("business_no");

            if (PlayerMrg.getInstance().getOnlinePlayerMap().isEmpty()) {
                PlayerMrg.responseHttp(ErrorCode.Success.getCode(), session, pid);
                return;
            }

            final Map<Long, Player> onlinePlayerMap = PlayerMrg.getInstance().getOnlinePlayerMap();
            final Iterator<Map.Entry<Long, Player>> iterator = onlinePlayerMap.entrySet().iterator();
            while (iterator.hasNext()) {
                Map.Entry<Long, Player> entry = iterator.next();
                final Player player = entry.getValue();
                if (!StringUtil.isNullOrEmpty(business_no) && !Objects.equals(player.getBusiness_no(), business_no)) {
                    continue;
                }
                iterator.remove();  // Use iterator's remove method to avoid ConcurrentModificationException
                HallServer.getInstance().asyncExecute(player.getPlayerId(), () -> {
                    PlayerMrg.getInstance().sendKickOutPlayerMsg(player);

                    ScriptLoader.getInstance().consumerScript("PlayerQuitScript",
                            (IPlayerScript script) -> script.quitHall(player));
                });
            }

            PlayerMrg.responseHttp(ErrorCode.Success.getCode(), session, pid);
        } catch (Exception e) {
            LOGGER.error("GmHall_KickOutAllHandler", e);
            PlayerMrg.responseHttp(ErrorCode.Internal_Server_Error.getCode(), session, pid);
        }
    }

}
