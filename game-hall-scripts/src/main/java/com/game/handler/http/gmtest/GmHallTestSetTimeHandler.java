package com.game.handler.http.gmtest;


import com.game.dao.player.PlayerDao;
import com.game.engine.io.handler.HttpHandler;
import com.game.engine.script.IHandlerEntity;
import com.game.engine.utils.MsgUtil;
import com.game.entity.player.Player;
import com.game.entity.player.PlayerFields;
import com.game.entity.player.activity.ActivityData;
import com.game.entity.player.activity.ActivityInfo;
import com.game.enums.ErrorCode;
import com.game.hall.mrg.ActivityMrg;
import com.game.hall.mrg.player.PlayerMrg;
import com.game.manager.EntityDaoMrg;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.mongodb.core.query.Update;

//http://127.0.0.1:8480/hall/test/setTime?playerId=92036950&time=&type=
@IHandlerEntity(path = "/hall/test/setTime", desc = "")
public class GmHallTestSetTimeHandler extends HttpHandler {
    private static final Logger LOGGER = LoggerFactory.getLogger(GmHallTestSetTimeHandler.class);

    @Override
    public void run() {
        try {
            final String playerId = (String) paramsMap.get("playerId");
            final String time = (String) paramsMap.get("time");
            final String type = (String) paramsMap.get("type");

            final Player player = PlayerMrg.getInstance().findDbPlayer(Long.parseLong(playerId));
            if (player == null) {
                PlayerMrg.responseHttp(ErrorCode.Account_NotExist.getCode(), session, pid);
                return;
            }

            final ActivityInfo activityInfo = player.getActivityInfo();
            final int activityId = Integer.parseInt(ActivityMrg.COMPENSATE + type);
            final ActivityData activityData = activityInfo.getActivityData(activityId);

            activityData.getEndTimeMap().put(Integer.parseInt(type), Long.parseLong(time));
            EntityDaoMrg.getInstance().getDao(PlayerDao.class).updateActivity(player, activityInfo);

            PlayerMrg.responseHttp(ErrorCode.Success.getCode(), session, pid);
        } catch (Exception e) {
            LOGGER.error("", e);
            PlayerMrg.responseHttp(ErrorCode.Internal_Server_Error.getCode(), session, pid);
        }
    }

}
