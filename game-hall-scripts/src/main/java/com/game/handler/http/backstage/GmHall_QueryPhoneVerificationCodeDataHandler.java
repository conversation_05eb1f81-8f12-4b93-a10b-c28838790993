//package com.game.handler.http.backstage;
//
//import com.game.engine.io.handler.HttpHandler;
//import com.game.engine.io.redis.RedisPoolManager;
//import com.game.engine.script.IHandlerEntity;
//import com.game.entity.player.Player;
//import com.game.enums.ErrorCode;
//import com.game.enums.VerifyCode;
//import com.game.enums.redis.RedisLogin;
//import com.game.hall.mrg.player.PlayerMrg;
//import org.slf4j.Logger;
//import org.slf4j.LoggerFactory;
//
//import java.util.HashMap;
//import java.util.Map;
//
////http://127.0.0.1:8380/gmHall/queryPhoneVerificationCode?playerId=1000004&phone=55-
//@IHandlerEntity(path = "/gmHall/queryPhoneVerificationCode", desc = "查询手机验证码")
//public class GmHall_QueryPhoneVerificationCodeDataHandler extends HttpHandler {
//    private static final Logger LOGGER = LoggerFactory.getLogger(GmHall_QueryPhoneVerificationCodeDataHandler.class);
//
//    @Override
//    public void run() {
//        try {
//            final String playerId = (String) paramsMap.get("playerId");
//            final String phone = (String) paramsMap.get("phone");
//
//            final Player player = PlayerMrg.getInstance().findVirtualDbPlayer(Long.parseLong(playerId));
//            if (player == null) {
//                PlayerMrg.responseHttp(ErrorCode.Account_NotExist.getCode(), session, pid);
//                return;
//            }
//
//            final String code = RedisPoolManager.getInstance().function(jedis ->
//                    jedis.get(RedisLogin.Platform_LG_Account_VerifyCode.getKey(player.getBusiness_no(), VerifyCode.Register.getType(), phone)));
//
//            final Map<String, Object> resMap = new HashMap<>();
//            resMap.put("code", code);
//            PlayerMrg.responseHttp(resMap, session, pid);
//        } catch (Exception e) {
//            LOGGER.error("GmHall_QueryPhoneVerificationCodeHandler", e);
//            PlayerMrg.responseHttp(ErrorCode.Internal_Server_Error.getCode(), session, pid);
//        }
//    }
//}
