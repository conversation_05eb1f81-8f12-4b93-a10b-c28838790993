package com.game.handler.http.gmtest;


import com.game.c_entity.merchant.C_RedEnvelopeRain;
import com.game.dao.player.PlayerDao;
import com.game.engine.io.handler.HttpHandler;
import com.game.engine.io.redis.RedisPoolManager;
import com.game.engine.math.BigDecimalUtils;
import com.game.engine.mongo.DBConnectionMrg;
import com.game.engine.script.IHandlerEntity;
import com.game.engine.script.ScriptLoader;
import com.game.engine.utils.*;
import com.game.entity.game.GameNote;
import com.game.entity.game.GameNoteFields;
import com.game.entity.player.Player;
import com.game.entity.player.PlayerFields;
import com.game.enums.ErrorCode;
import com.game.enums.redis.RedisLogin;
import com.game.hall.main.HallServer;
import com.game.hall.mrg.MerchantData;
import com.game.hall.mrg.player.PlayerMrg;
import com.game.hall.script.activity.IActivityScript;
import com.game.manager.EntityDaoMrg;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;

import java.io.UnsupportedEncodingException;
import java.net.URI;
import java.net.http.HttpClient;
import java.net.http.HttpRequest;
import java.net.http.HttpResponse;
import java.nio.charset.Charset;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.security.*;
import java.security.spec.KeySpec;
import java.security.spec.PKCS8EncodedKeySpec;
import java.security.spec.X509EncodedKeySpec;
import java.time.Duration;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.Function;
import java.util.function.Predicate;

//http://127.0.0.1:8180/hall/test?playerId=65803549&code=
@IHandlerEntity(path = "/hall/test", desc = "")
public class GmHallTestHandler extends HttpHandler {
    private static final Logger LOGGER = LoggerFactory.getLogger(GmHallTestHandler.class);

    @Override
    public void run() {
        try {
            final String playerId = (String) paramsMap.get("playerId");
            final String code = (String) paramsMap.get("code");

            for (int i = 1; i <= 10; i++) {
                final Player player = new Player(i);
                player.setPlayerName(i + "");
                player.setLastRefreshTime(1750129200000L);
                player.setTimeZone("UTC-3");
                player.setHeartbeat(TimeUtil.currentTimeMillis() + 15 * TimeUtil.MIN);
                PlayerMrg.getInstance().addOnlinePlayer(player);
            }

            MsgUtil.responseHttp(ErrorCode.Success.getCode(), session);
        } catch (Exception e) {
            LOGGER.error("", e);
            MsgUtil.responseHttp(ErrorCode.Internal_Server_Error.getCode(), session);
        }
    }

    private C_RedEnvelopeRain findOpen(Player player, long time, MerchantData merchantData) {
        final Map<Integer, C_RedEnvelopeRain> c_redEnvelopeRainMap = merchantData.getC_redEnvelopeRainDayMap();
        for (C_RedEnvelopeRain c_redEnvelopeRain : c_redEnvelopeRainMap.values()) {
            if (player != null && !c_redEnvelopeRain.getLanguages().contains(player.getLanguage())) {
                continue;
            }

            final String timeZone = c_redEnvelopeRain.getTimeZone();
            final int day = TimeUtil.getDayOfWeek(time, timeZone);
            if (c_redEnvelopeRain.getCycles().contains(day)) {
                return c_redEnvelopeRain;
            }
        }
        return null;
    }

    public List<GameNote> loadGameNote(long playerId, long start, long end) {
        final MongoTemplate ops = DBConnectionMrg.getInstance().getMongoTemplate();
        final Query query = new Query();
        query.addCriteria(Criteria.where(GameNoteFields.playerId).is(playerId)
                .and(GameNoteFields.createTime).gte(start).lt(end)
        );

        return ops.find(query.with(Sort.by(Sort.Direction.DESC, GameNoteFields.createTime)), GameNote.class);
    }

    private static <T> Predicate<T> distinctByKey(Function<? super T, ?> keyExtractor) {
        final Set<Object> seen = ConcurrentHashMap.newKeySet();
        return t -> seen.add(keyExtractor.apply(t));
    }

    private String idKey = "player:id:seq";
    private int randomDigits = 5; // 随机部分的位数


    public static void main(String[] args) throws NoSuchAlgorithmException, InvalidKeyException, UnsupportedEncodingException, SignatureException {
        String test_str = "test";

        PublicKey pub = publicKey();
        PrivateKey priv = privateKey();

        Signature ecdsa_priv = Signature.getInstance("SHA256withECDSA");

        ecdsa_priv.initSign(priv);

        byte[] strByte = test_str.getBytes("UTF-8");
        ecdsa_priv.update(strByte);

        byte[] realSig = ecdsa_priv.sign();
        System.out.println("Signature: " + new String(Base64.getEncoder().encode(realSig)));


        Signature ecdsa_pub = Signature.getInstance("SHA256withECDSA");

        ecdsa_pub.initVerify(pub);
        ecdsa_pub.update(test_str.getBytes("UTF-8"));

        boolean valid = ecdsa_pub.verify(realSig);

        System.out.println("Valid: " + valid);
    }

    private static PublicKey publicKey() {
        try {
            Path keyPath = Paths.get("../ec.pub");
            String key = new String(Files.readAllBytes(keyPath), Charset.defaultCharset());

            String publicKeyPEM = key
                    .replace("-----BEGIN PUBLIC KEY-----", "")
                    .replaceAll(System.lineSeparator(), "")
                    .replace("-----END PUBLIC KEY-----", "");

            byte[] encoded = Base64.getDecoder().decode(publicKeyPEM);

            KeyFactory keyFactory = KeyFactory.getInstance("EC");
            KeySpec keySpec = new X509EncodedKeySpec(encoded);
            return keyFactory.generatePublic(keySpec);
        } catch (Exception ex) {
            return null;
        }
    }

    private static PrivateKey privateKey() {
        try {
            String key = execCmd("openssl pkcs8 -topk8 -nocrypt -in ../ec.key");

            String privateKeyPEM = key
                    .replace("-----BEGIN PRIVATE KEY-----", "")
                    .replaceAll(System.lineSeparator(), "")
                    .replace("-----END PRIVATE KEY-----", "");

            byte[] encoded = Base64.getDecoder().decode(privateKeyPEM);

            KeyFactory keyFactory = KeyFactory.getInstance("EC");
            KeySpec keySpec = new PKCS8EncodedKeySpec(encoded);
            return keyFactory.generatePrivate(keySpec);
        } catch (Exception ex) {
            return null;
        }
    }

    private static String execCmd(String cmd) throws java.io.IOException {
        java.util.Scanner s = new java.util.Scanner(Runtime.getRuntime().exec(cmd).getInputStream()).useDelimiter("\\A");
        return s.hasNext() ? s.next() : "";
    }
}
