package com.game.handler.http.backstage;

import com.game.dao.player.PlayerDao;
import com.game.engine.io.handler.HttpHandler;
import com.game.engine.io.redis.RedisPoolManager;
import com.game.engine.script.IHandlerEntity;
import com.game.entity.player.PayInfo;
import com.game.entity.player.Player;
import com.game.entity.player.WithdrawAccount;
import com.game.enums.ErrorCode;
import com.game.enums.redis.RedisAllGame;
import com.game.hall.mrg.player.PlayerMrg;
import com.game.manager.EntityDaoMrg;
import io.netty.util.internal.StringUtil;
import it.unimi.dsi.fastutil.longs.LongLists;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Map;

//http://127.0.0.1:8380/gmHall/playerDeleteWithdrawAccount?playerId=1000004&withdrawAccountId=&currencyIdPayment=
@IHandlerEntity(path = "/gmHall/playerDeleteWithdrawAccount", desc = "删除提现账号")
public class GmHall_PlayerDeleteWithdrawAccountHandler extends HttpHandler {
    private static final Logger LOGGER = LoggerFactory.getLogger(GmHall_PlayerDeleteWithdrawAccountHandler.class);

    @Override
    public void run() {
        try {
            final String playerId = (String) paramsMap.get("playerId");
            final String currencyIdPayment = (String) paramsMap.get("currencyIdPayment");
            final String withdrawAccountId = (String) paramsMap.get("withdrawAccountId");

            if (StringUtil.isNullOrEmpty(playerId) || StringUtil.isNullOrEmpty(currencyIdPayment) || (StringUtil.isNullOrEmpty(withdrawAccountId))) {
                PlayerMrg.responseHttp(ErrorCode.Parameter_Error.getCode(), session, pid);
                return;
            }

            final Player player = PlayerMrg.getInstance().findDbPlayer(Long.parseLong(playerId));
            if (player == null) {
                PlayerMrg.responseHttp(ErrorCode.Account_NotExist.getCode(), session, pid);
                return;
            }

            final WithdrawAccount withdrawAccount = player.getWithdrawAccountMap().get(currencyIdPayment);
            final Map<Long, PayInfo> payInfosMap = withdrawAccount.getPayInfoMap();
            if (payInfosMap.isEmpty()) {
                PlayerMrg.responseHttp(ErrorCode.Data_Error.getCode(), session, pid);
                return;
            }
            final PayInfo payInfo = payInfosMap.remove(Long.valueOf(withdrawAccountId));
            EntityDaoMrg.getInstance().getDao(PlayerDao.class).updateDeleteWithdrawAccount(player, currencyIdPayment, LongLists.singleton(Long.valueOf(withdrawAccountId)));

            if (payInfo != null) {
                RedisPoolManager.getInstance().executeAsync(commands ->
                        commands.srem(RedisAllGame.Platform_All_WithdrawAccount.getKey(player.getBusiness_no(), currencyIdPayment), payInfo.getExtend()));
            }

            PlayerMrg.responseHttp(ErrorCode.Success.getCode(), session, pid);
        } catch (Exception e) {
            LOGGER.error("GmHall_PlayerDeleteWithdrawAccountHandler", e);
            PlayerMrg.responseHttp(ErrorCode.Internal_Server_Error.getCode(), session, pid);
        }
    }
}
