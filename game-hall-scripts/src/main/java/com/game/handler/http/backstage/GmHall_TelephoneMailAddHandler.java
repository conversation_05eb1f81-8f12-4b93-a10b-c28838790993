package com.game.handler.http.backstage;

import com.game.dao.account.AccountDao;
import com.game.dao.player.PlayerDao;
import com.game.engine.io.handler.HttpHandler;
import com.game.engine.io.redis.RedisPoolManager;
import com.game.engine.script.IHandlerEntity;
import com.game.entity.account.Account;
import com.game.entity.account.email.Email;
import com.game.entity.account.phone.Phone;
import com.game.entity.player.Player;
import com.game.entity.player.PlayerFields;
import com.game.enums.ErrorCode;
import com.game.enums.ThreeParty;
import com.game.enums.redis.RedisLogin;
import com.game.hall.mrg.player.PlayerMrg;
import com.game.manager.EntityDaoMrg;
import com.game.utils.VirtualThreadUtils;
import io.netty.util.internal.StringUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.mongodb.core.query.Update;

//http://127.0.0.1:8380/gmHall/telephoneMailAdd?playerId=1000004&type=&areaCode=&phone=&email=
@IHandlerEntity(path = "/gmHall/telephoneMailAdd", desc = "玩家电话邮件添加")
public class GmHall_TelephoneMailAddHandler extends HttpHandler {
    private static final Logger LOGGER = LoggerFactory.getLogger(GmHall_TelephoneMailAddHandler.class);

    @Override
    public void run() {
        try {
            final String playerId = (String) paramsMap.get("playerId");
            final String type = (String) paramsMap.get("type");
            final String areaCode = (String) paramsMap.get("areaCode");
            final String phone = (String) paramsMap.get("phone");
            final String email = (String) paramsMap.get("email");

            if (StringUtil.isNullOrEmpty(playerId) || StringUtil.isNullOrEmpty(type)) {
                PlayerMrg.responseHttp(ErrorCode.Parameter_Error.getCode(), session, pid);
                return;
            }

            final Player player = PlayerMrg.getInstance().findDbPlayer(Long.parseLong(playerId));
            if (player == null) {
                PlayerMrg.responseHttp(ErrorCode.Account_NotExist.getCode(), session, pid);
                return;
            }

            final Account ac = PlayerMrg.getInstance().findDbAccount(Long.parseLong(playerId));
            if (ac == null) {
                PlayerMrg.responseHttp(ErrorCode.Account_NotExist.getCode(), session, pid);
                return;
            }

            switch (Integer.parseInt(type)) {
                case 1://电话
                {
                    if (StringUtil.isNullOrEmpty(areaCode) || StringUtil.isNullOrEmpty(phone)) {
                        PlayerMrg.responseHttp(ErrorCode.Parameter_Error.getCode(), session, pid);
                        return;
                    }

                    final String accountId = RedisPoolManager.getInstance().function(jedis ->
                            jedis.sync().hget(RedisLogin.Platform_LG_Map_AccountID.getKey(player.getBusiness_no(), ThreeParty.Phone.getThreeParty()), areaCode + "-" + phone));
                    if (!StringUtil.isNullOrEmpty(accountId)) {
                        LOGGER.warn("account：{}，already registered", areaCode + "-" + phone);
                        PlayerMrg.responseHttp(ErrorCode.Account_AlreadyExists.getCode(), session, pid);
                        return;
                    }

                    final Phone phoneInfo = ac.getPhoneInfo();
                    if (StringUtil.isNullOrEmpty(phoneInfo.getAreaCode())) {
                        PlayerMrg.responseHttp(ErrorCode.Account_NotExist.getCode(), session, pid);
                        return;
                    }

                    phoneInfo.setAreaCode(areaCode);
                    phoneInfo.setPhone(phone);
                    EntityDaoMrg.getInstance().getDao(AccountDao.class).updatePhone(ac.getAccountId(), phoneInfo);

                    player.setPhoneBind(true);
                    player.setAreaCode(areaCode);
                    player.setPhone(phone);
                    final Update update = new Update();
                    update.set(PlayerFields.phoneBind, player.isPhoneBind())
                            .set(PlayerFields.areaCode, player.getAreaCode())
                            .set(PlayerFields.phone, player.getPhone());
                    EntityDaoMrg.getInstance().getDao(PlayerDao.class)
                            .updatePlayer(player.getPlayerId(), update);

                    RedisPoolManager.getInstance().executeAsync(commands ->
                            commands.hset(RedisLogin.Platform_LG_Map_AccountID.getKey(player.getBusiness_no(), ThreeParty.Phone.getThreeParty()), areaCode + "-" + phone, playerId)
                    );
                }
                break;
                case 2://邮箱
                {
                    if (StringUtil.isNullOrEmpty(email)) {
                        PlayerMrg.responseHttp(ErrorCode.Parameter_Error.getCode(), session, pid);
                        return;
                    }

                    final String accountId = RedisPoolManager.getInstance().function(jedis ->
                            jedis.sync().hget(RedisLogin.Platform_LG_Map_AccountID.getKey(player.getBusiness_no(), ThreeParty.Email.getThreeParty()), email));
                    if (!StringUtil.isNullOrEmpty(accountId)) {
                        LOGGER.warn("account：{}，already registered", email);
                        PlayerMrg.responseHttp(ErrorCode.Account_AlreadyExists.getCode(), session, pid);
                        return;
                    }

                    final Email emailInfo = ac.getEmailInfo();
                    if (StringUtil.isNullOrEmpty(emailInfo.getEmail())) {
                        PlayerMrg.responseHttp(ErrorCode.Account_NotExist.getCode(), session, pid);
                        return;
                    }

                    emailInfo.setEmail(email);
                    EntityDaoMrg.getInstance().getDao(AccountDao.class).updateEmail(ac.getAccountId(), emailInfo);

                    player.setEmailBind(true);
                    player.setEmail(email);
                    final Update update = new Update();
                    update.set(PlayerFields.emailBind, player.isEmailBind())
                            .set(PlayerFields.email, player.getEmail());
                    EntityDaoMrg.getInstance().getDao(PlayerDao.class)
                            .updatePlayer(player.getPlayerId(), update);

                    RedisPoolManager.getInstance().executeAsync(commands ->
                            commands.hset(RedisLogin.Platform_LG_Map_AccountID.getKey(player.getBusiness_no(), ThreeParty.Email.getThreeParty()), email, playerId)
                    );
                }
                break;
            }

            PlayerMrg.responseHttp(ErrorCode.Success.getCode(), session, pid);
        } catch (Exception e) {
            LOGGER.error("GmHall_TelephoneMailBindHandler", e);
            PlayerMrg.responseHttp(ErrorCode.Internal_Server_Error.getCode(), session, pid);
        }
    }

}
