package com.game.handler.http.gmtest;


import com.game.engine.io.handler.HttpHandler;
import com.game.engine.script.IHandlerEntity;
import com.game.engine.script.ScriptLoader;
import com.game.engine.utils.MsgUtil;
import com.game.enums.ErrorCode;
import com.game.hall.mrg.player.PlayerMrg;
import com.game.hall.script.activity.IDailyContestScript;
import com.game.hall.script.activity.IWeeklyRaffleScript;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

//http://127.0.0.1:8380/hall/test/DailyContestSettlement?default=true
@IHandlerEntity(path = "/hall/test/DailyContestSettlement", desc = "")
public class GmHallTestDailyContestSettlementHandler extends HttpHandler {
    private static final Logger LOGGER = LoggerFactory.getLogger(GmHallTestDailyContestSettlementHandler.class);

    @Override
    public void run() {
        try {

            ScriptLoader.getInstance().consumerScript("DailyContestScript",
                    IDailyContestScript::settlement);

            PlayerMrg.responseHttp(ErrorCode.Success.getCode(), session, pid);
        } catch (Exception e) {
            LOGGER.error("GmHallTestDailyContestSettlementHandler", e);
            MsgUtil.responseHttp(ErrorCode.Internal_Server_Error.getCode(), session);
        }
    }

}
