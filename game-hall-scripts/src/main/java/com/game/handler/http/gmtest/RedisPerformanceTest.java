package com.game.handler.http.gmtest;

import com.game.engine.io.redis.RedisPoolManager;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.atomic.AtomicInteger;

public class RedisPerformanceTest {
    private static final Logger LOGGER = LoggerFactory.getLogger(RedisPerformanceTest.class);

    private static final int THREADS = 100;  // 并发线程数
    private static final int REQUESTS = 10000;  // 总请求数
    private static final AtomicInteger successCount = new AtomicInteger();
    private static final AtomicInteger failureCount = new AtomicInteger();

    public static void redisTest() {
        ExecutorService executor = Executors.newFixedThreadPool(THREADS);


        executor.shutdown();
        while (!executor.isTerminated()) {}

        LOGGER.warn("压测完成！成功请求：{}，失败请求：{}", successCount.get(), failureCount.get());
    }
}

