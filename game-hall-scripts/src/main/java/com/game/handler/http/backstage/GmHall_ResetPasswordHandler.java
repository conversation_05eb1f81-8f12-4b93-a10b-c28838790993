package com.game.handler.http.backstage;

import com.game.dao.account.AccountDao;
import com.game.engine.io.handler.HttpHandler;
import com.game.engine.script.IHandlerEntity;
import com.game.engine.utils.MD5;
import com.game.engine.utils.MsgUtil;
import com.game.entity.account.Account;
import com.game.entity.account.AccountFields;
import com.game.enums.ErrorCode;
import com.game.hall.mrg.player.PlayerMrg;
import com.game.manager.EntityDaoMrg;
import io.netty.util.internal.StringUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

//http://127.0.0.1:8380/gmHall/resetPassword?playerId=1000004&password=2
@IHandlerEntity(path = "/gmLogin/resetPassword", desc = "重置密码")
public class GmHall_ResetPasswordHandler extends HttpHandler {
    private static final Logger LOGGER = LoggerFactory.getLogger(GmHall_ResetPasswordHandler.class);

    @Override
    public void run() {
        try {
            final String playerId = (String) paramsMap.get("playerId");
            final String password = (String) paramsMap.get("password");

            if (StringUtil.isNullOrEmpty(playerId)
                    || StringUtil.isNullOrEmpty(password)) {
                PlayerMrg.responseHttp(ErrorCode.Parameter_Error.getCode(), session, pid);
                return;
            }

            if (password.length() < 6 || password.length() > 16) {
                PlayerMrg.responseHttp(ErrorCode.PassWard_Length.getCode(), session, pid);
                return;
            }

            final Account account = PlayerMrg.getInstance().findDbAccount(Long.parseLong(playerId));
            if (account == null) {
                PlayerMrg.responseHttp(ErrorCode.Account_NotExist.getCode(), session, pid);
                return;
            }

            account.setPassword(MD5.MD5Encode(password));
            EntityDaoMrg.getInstance().getDao(AccountDao.class)
                    .updateAccountField(account.getAccountId(), AccountFields.password, account.getPassword());


            PlayerMrg.responseHttp(ErrorCode.Success.getCode(), session, pid);
        } catch (Exception e) {
            LOGGER.error("GmHall_ResetPasswordHandler", e);
            PlayerMrg.responseHttp(ErrorCode.Internal_Server_Error.getCode(), session, pid);
        }
    }
}
