package com.game.handler.http.gm;

import com.game.engine.io.handler.HttpHandler;
import com.game.engine.script.IHandlerEntity;
import com.game.engine.utils.GeoLite2Util;
import com.game.enums.ErrorCode;
import com.game.hall.mrg.player.PlayerMrg;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

//http://127.0.0.1:8380/gmHall/updateGeoLite?default=true
@IHandlerEntity(path = "/gmHall/updateGeoLite", desc = "更新ip库")
public class GmHall_UpdateGeoLiteHandler extends HttpHandler {
    private static final Logger LOGGER = LoggerFactory.getLogger(GmHall_UpdateGeoLiteHandler.class);

    @Override
    public void run() {
        try {
            GeoLite2Util.initGeoLite2();

            PlayerMrg.responseHttp(ErrorCode.Success.getCode(), session, pid);
        } catch (Exception e) {
            LOGGER.error("GmHall_ReloadConstantConfigHandler", e);
            PlayerMrg.responseHttp(ErrorCode.Internal_Server_Error.getCode(), session, pid);
        }
    }
}
