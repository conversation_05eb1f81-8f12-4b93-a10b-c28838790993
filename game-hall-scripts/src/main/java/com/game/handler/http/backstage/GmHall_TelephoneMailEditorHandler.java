package com.game.handler.http.backstage;

import com.game.dao.account.AccountDao;
import com.game.dao.player.PlayerDao;
import com.game.engine.io.handler.HttpHandler;
import com.game.engine.io.redis.RedisPoolManager;
import com.game.engine.script.IHandlerEntity;
import com.game.entity.account.Account;
import com.game.entity.account.email.Email;
import com.game.entity.account.email.EmailFields;
import com.game.entity.account.phone.Phone;
import com.game.entity.player.Player;
import com.game.entity.player.PlayerFields;
import com.game.enums.ErrorCode;
import com.game.enums.ThreeParty;
import com.game.enums.redis.RedisLogin;
import com.game.hall.mrg.player.PlayerMrg;
import com.game.manager.EntityDaoMrg;
import com.game.utils.VirtualThreadUtils;
import io.lettuce.core.api.async.RedisAsyncCommands;
import io.netty.util.internal.StringUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.mongodb.core.query.Update;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.CompletableFuture;


//http://127.0.0.1:8380/gmHall/telephoneMailEditor?playerId=1000004&type=&areaCode=&phone=&email=
@IHandlerEntity(path = "/gmHall/telephoneMailEditor", desc = "玩家电话邮件编辑")
public class GmHall_TelephoneMailEditorHandler extends HttpHandler {
    private static final Logger LOGGER = LoggerFactory.getLogger(GmHall_TelephoneMailEditorHandler.class);

    @Override
    public void run() {
        try {
            final String playerId = (String) paramsMap.get("playerId");
            final String type = (String) paramsMap.get("type");
            final String areaCode = (String) paramsMap.get("areaCode");
            final String phone = (String) paramsMap.get("phone");
            final String email = (String) paramsMap.get("email");

            if (StringUtil.isNullOrEmpty(playerId) || StringUtil.isNullOrEmpty(type)) {
                PlayerMrg.responseHttp(ErrorCode.Parameter_Error.getCode(), session, pid);
                return;
            }

            final Player player = PlayerMrg.getInstance().findDbPlayer(Long.parseLong(playerId));
            if (player == null) {
                PlayerMrg.responseHttp(ErrorCode.Account_NotExist.getCode(), session, pid);
                return;
            }

            final Account ac = PlayerMrg.getInstance().findDbAccount(Long.parseLong(playerId));
            if (ac == null) {
                PlayerMrg.responseHttp(ErrorCode.Account_NotExist.getCode(), session, pid);
                return;
            }

            switch (Integer.parseInt(type)) {
                case 1://电话
                {
                    if (StringUtil.isNullOrEmpty(areaCode) || StringUtil.isNullOrEmpty(phone)) {
                        PlayerMrg.responseHttp(ErrorCode.Parameter_Error.getCode(), session, pid);
                        return;
                    }

                    final String accountId = RedisPoolManager.getInstance().function(jedis -> jedis.sync().hget(RedisLogin.Platform_LG_Map_AccountID.getKey(player.getBusiness_no(), ThreeParty.Phone.getThreeParty()), areaCode + "-" + phone));
                    if (!StringUtil.isNullOrEmpty(accountId)) {
                        LOGGER.warn("account：{}，already registered", areaCode + "-" + phone);
                        PlayerMrg.responseHttp(ErrorCode.Account_AlreadyExists.getCode(), session, pid);
                        return;
                    }

                    final String oldAccount = player.getAreaCode() + "-" + player.getPhone();
                    player.setAreaCode(areaCode);
                    player.setPhone(phone);
                    final Update update = new Update();
                    update.set(PlayerFields.areaCode, player.getAreaCode()).set(PlayerFields.phone, player.getPhone());
                    EntityDaoMrg.getInstance().getDao(PlayerDao.class).updatePlayer(player.getPlayerId(), update);

                    final Phone phoneInfo = ac.getPhoneInfo();
                    phoneInfo.setAreaCode(areaCode);
                    phoneInfo.setPhone(phone);
                    EntityDaoMrg.getInstance().getDao(AccountDao.class).updatePhone(ac.getAccountId(), phoneInfo);

                    RedisPoolManager.getInstance().asyncPipeline(commands -> {
                        final List<CompletableFuture<?>> futures = new ArrayList<>();
                        futures.add(commands.hdel(RedisLogin.Platform_LG_Map_AccountID.getKey(player.getBusiness_no(), ThreeParty.Phone.getThreeParty()), oldAccount)
                                .toCompletableFuture());
                        futures.add(commands.hset(RedisLogin.Platform_LG_Map_AccountID.getKey(player.getBusiness_no(), ThreeParty.Phone.getThreeParty()), areaCode + "-" + phone, playerId)
                                .toCompletableFuture());
                        return futures;
                    });
                }
                break;
                case 2://邮箱
                    if (StringUtil.isNullOrEmpty(email)) {
                        PlayerMrg.responseHttp(ErrorCode.Parameter_Error.getCode(), session, pid);
                        return;
                    }

                    final String accountId = RedisPoolManager.getInstance().function(jedis -> jedis.sync().hget(RedisLogin.Platform_LG_Map_AccountID.getKey(player.getBusiness_no(), ThreeParty.Email.getThreeParty()), email));
                    if (!StringUtil.isNullOrEmpty(accountId)) {
                        LOGGER.warn("account：{}，already registered", email);
                        PlayerMrg.responseHttp(ErrorCode.Account_AlreadyExists.getCode(), session, pid);
                        return;
                    }

                    final String oldAccount = player.getEmail();
                    player.setEmail(email);
                    EntityDaoMrg.getInstance().getDao(PlayerDao.class).updatePlayerField(Long.parseLong(playerId), PlayerFields.email, player.getEmail());

                    final Email emailInfo = ac.getEmailInfo();
                    emailInfo.setEmail(email);
                    EntityDaoMrg.getInstance().getDao(AccountDao.class).updateMail2LField(ac.getAccountId(), EmailFields.email, emailInfo.getEmail());

                    RedisPoolManager.getInstance().asyncPipeline(commands -> {
                                final List<CompletableFuture<?>> futures = new ArrayList<>();
                                futures.add(commands.hdel(RedisLogin.Platform_LG_Map_AccountID.getKey(player.getBusiness_no(), ThreeParty.Email.getThreeParty()), oldAccount)
                                        .toCompletableFuture());
                                futures.add(commands.hset(RedisLogin.Platform_LG_Map_AccountID.getKey(player.getBusiness_no(), ThreeParty.Email.getThreeParty()), email, playerId)
                                        .toCompletableFuture());
                                return futures;
                            }
                    );
                    break;
            }

            PlayerMrg.responseHttp(ErrorCode.Success.getCode(), session, pid);
        } catch (Exception e) {
            LOGGER.error("GmHall_TelephoneMailEditorHandler", e);
            PlayerMrg.responseHttp(ErrorCode.Internal_Server_Error.getCode(), session, pid);
        }
    }

}
