package com.game.handler.http.gm;

import com.game.engine.io.handler.HttpHandler;
import com.game.engine.script.IHandlerEntity;
import com.game.engine.script.ScriptLoader;
import com.game.engine.utils.Symbol;
import com.game.enums.ErrorCode;
import com.game.hall.mrg.player.PlayerMrg;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.HashMap;
import java.util.Map;

//http://127.0.0.1:8380/gmHall/reloadScripts?scriptsName=HallInnerNotifyCurrencyUpdateHandler
@IHandlerEntity(path = "/gmHall/reloadScripts", desc = "重新加载脚本")
public class GmHall_ReloadScriptsHandler extends HttpHandler {
    private static Logger logger = LoggerFactory.getLogger(GmHall_ReloadScriptsHandler.class);

    @Override
    public void run() {
        try {
            final Map<String, Object> paramsMap = getParamsMap();
            final String scriptsName = (String) paramsMap.get("scriptsName");
            final String[] scriptsNames = scriptsName.split(Symbol.DOUHAO_REG);
            final String sourceDir = ScriptLoader.getInstance().getSourceDir();
            final String data = ScriptLoader.getInstance().loadJava(sourceDir, scriptsNames);

            final Map<String, Object> res = new HashMap<>();
            res.put("error", ErrorCode.Success.getCode());
            res.put("data", data);
            PlayerMrg.responseHttp(res, session, pid);
        } catch (Exception e) {
            logger.error("GmHall_ReloadScriptsHandler", e);
            PlayerMrg.responseHttp(ErrorCode.Internal_Server_Error.getCode(), session, pid);
        }
    }

}
