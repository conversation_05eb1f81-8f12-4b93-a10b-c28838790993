package com.game.handler.http.gm;

import com.game.dao.player.PlayerDao;
import com.game.engine.io.handler.HttpHandler;
import com.game.engine.script.IHandlerEntity;
import com.game.engine.utils.Config;
import com.game.engine.utils.JsonUtils;
import com.game.entity.player.Player;
import com.game.entity.player.PlayerFields;
import com.game.enums.ErrorCode;
import com.game.hall.mrg.player.PlayerMrg;
import com.game.manager.EntityDaoMrg;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.mongodb.core.query.Update;

import java.util.List;

//http://127.0.0.1:8380/gmHall/setOffline?online=true&updateOnline=false
@IHandlerEntity(path = "/gmHall/setOffline", desc = "设置离线")
public class GmHall_SetOfflineDataHandler extends HttpHandler {
    private static final Logger LOGGER = LoggerFactory.getLogger(GmHall_SetOfflineDataHandler.class);

    @Override
    public void run() {
        try {
            if (Config.SERVER_ID != 4000) {
                return;
            }

            final String online = (String) paramsMap.get("online");
            final String updateOnline = (String) paramsMap.get("updateOnline");

            LOGGER.warn("paramsMap：{}", JsonUtils.writeAsJson(paramsMap));

            final List<Player> players = EntityDaoMrg.getInstance().getDao(PlayerDao.class)
                    .loadPlayer(Boolean.parseBoolean(online));

            LOGGER.warn("players: {}", players.size());

            for (Player player : players) {
                //更新数据库
                final Update update = new Update();
                update.set(PlayerFields.online, Boolean.parseBoolean(updateOnline));
                EntityDaoMrg.getInstance().getDao(PlayerDao.class)
                        .updatePlayer(player.getPlayerId(), update);
            }

            PlayerMrg.responseHttp(ErrorCode.Success.getCode(), session, pid);
        } catch (Exception e) {
            LOGGER.error("GmHall_SetOfflineDataHandler", e);
            PlayerMrg.responseHttp(ErrorCode.Internal_Server_Error.getCode(), session, pid);
        }

    }
}
