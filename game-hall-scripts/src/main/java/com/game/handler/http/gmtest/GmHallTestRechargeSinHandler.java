package com.game.handler.http.gmtest;


import com.game.dao.player.PlayerDao;
import com.game.engine.io.handler.HttpHandler;
import com.game.engine.script.IHandlerEntity;
import com.game.entity.player.Player;
import com.game.entity.player.activity.firstChargeSignIn.FirstChargeSignInInfo;
import com.game.enums.ErrorCode;
import com.game.hall.mrg.player.PlayerMrg;
import com.game.manager.EntityDaoMrg;
import io.netty.util.internal.StringUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

//http://127.0.0.1:8380/hall/test/rechargeSin?playerId=200100011&rechargeTime=
@IHandlerEntity(path = "/hall/test/rechargeSin", desc = "")
public class GmHallTestRechargeSinHandler extends HttpHandler {
    private static final Logger LOGGER = LoggerFactory.getLogger(GmHallTestRechargeSinHandler.class);

    @Override
    public void run() {
        try {
            final String playerId = (String) paramsMap.get("playerId");
            final String rechargeTime = (String) paramsMap.get("rechargeTime");

            if (StringUtil.isNullOrEmpty(playerId) || StringUtil.isNullOrEmpty(rechargeTime)) {
                PlayerMrg.responseHttp(ErrorCode.Parameter_Error.getCode(), session, pid);
                return;
            }

            final Player player = PlayerMrg.getInstance().findDbPlayer(Long.parseLong(playerId));
            if (player == null) {
                PlayerMrg.responseHttp(ErrorCode.Account_NotExist.getCode(), session, pid);
                return;
            }

            final FirstChargeSignInInfo firstChargeSignInInfo = player.getFirstChargeSignInInfo();
            firstChargeSignInInfo.setRechargeTime(Long.parseLong(rechargeTime));
            EntityDaoMrg.getInstance().getDao(PlayerDao.class)
                    .updateFirstChargeSignInInfo(pid, firstChargeSignInInfo);

            PlayerMrg.responseHttp(ErrorCode.Success.getCode(), session, pid);
        } catch (Exception e) {
            LOGGER.error("GmHallTestRechargeSinHandler", e);
            PlayerMrg.responseHttp(ErrorCode.Internal_Server_Error.getCode(), session, pid);
        }
    }

}
