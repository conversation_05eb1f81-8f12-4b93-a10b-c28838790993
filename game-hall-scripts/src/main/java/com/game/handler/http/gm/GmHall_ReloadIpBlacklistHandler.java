package com.game.handler.http.gm;

import com.game.engine.io.handler.HttpHandler;
import com.game.engine.script.IHandlerEntity;
import com.game.engine.utils.Config;
import com.game.engine.utils.FileUtil;
import com.game.engine.utils.MsgUtil;
import com.game.engine.utils.Symbol;
import com.game.enums.ErrorCode;
import com.game.hall.mrg.UdpMsgAttackResistMrg;
import com.game.hall.mrg.player.PlayerMrg;
import io.netty.util.internal.StringUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.File;

//http://127.0.0.1:8180/gmHall/reloadIpBlacklist?default=true
@IHandlerEntity(path = "/gmHall/reloadIpBlacklist", desc = "重置黑名单")
public class GmHall_ReloadIpBlacklistHandler extends HttpHandler {
    private static Logger logger = LoggerFactory.getLogger(GmHall_ReloadIpBlacklistHandler.class);

    @Override
    public void run() {
        try {
            try {
                UdpMsgAttackResistMrg.getInstance().getIpBlacklist().clear();
                final File file = new File(Config.path + File.separatorChar + "IP_blacklist.text");
                if (!file.exists()) {
                    file.createNewFile();
                }
                final String ip = FileUtil.readTxtFile(Config.path, "IP_blacklist.text", "UTF-8");
                final String[] data = ip.split(Symbol.ENTER);
                for (String s : data) {
                    if (StringUtil.isNullOrEmpty(s)) {
                        continue;
                    }
                    UdpMsgAttackResistMrg.getInstance().getIpBlacklist().add(s);
                }
            } catch (Exception e) {
                logger.error("read IP_blacklist failed", e);
            }

            PlayerMrg.responseHttp(ErrorCode.Success.getCode(), session, pid);
        } catch (Exception e) {
            logger.error("GmHall_ReloadIpBlacklistHandler", e);
            PlayerMrg.responseHttp(ErrorCode.Internal_Server_Error.getCode(), session, pid);
        }
    }

}
