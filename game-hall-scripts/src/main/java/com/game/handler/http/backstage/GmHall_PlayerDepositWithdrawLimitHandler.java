package com.game.handler.http.backstage;

import com.game.dao.player.PlayerDao;
import com.game.engine.io.handler.HttpHandler;
import com.game.engine.script.IHandlerEntity;
import com.game.entity.player.Player;
import com.game.entity.player.PlayerFields;
import com.game.enums.ErrorCode;
import com.game.hall.mrg.player.PlayerMrg;
import com.game.manager.EntityDaoMrg;
import io.netty.util.internal.StringUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

//http://127.0.0.1:8380/gmHall/playerDepositWithdrawLimit?playerId=1000004&limit=2&status=
@IHandlerEntity(path = "/gmHall/playerDepositWithdrawLimit", desc = "玩家充提限制")
public class GmHall_PlayerDepositWithdrawLimitHandler extends HttpHandler {
    private static final Logger LOGGER = LoggerFactory.getLogger(GmHall_PlayerDepositWithdrawLimitHandler.class);

    @Override
    public void run() {
        try {
            final String playerId = (String) paramsMap.get("playerId");
            final String limit = (String) paramsMap.get("limit");
            final String status = (String) paramsMap.get("status");

            if (StringUtil.isNullOrEmpty(playerId) || StringUtil.isNullOrEmpty(limit)
                    || StringUtil.isNullOrEmpty(status)) {
                PlayerMrg.responseHttp(ErrorCode.Parameter_Error.getCode(), session, pid);
                return;
            }

            final Player player = PlayerMrg.getInstance().findDbPlayer(Long.parseLong(playerId));
            if (player == null) {
                PlayerMrg.responseHttp(ErrorCode.Account_NotExist.getCode(), session, pid);
                return;
            }

            if (Integer.parseInt(limit) == 1) {//充值
                player.setDepositLimit(Boolean.parseBoolean(status));
                EntityDaoMrg.getInstance().getDao(PlayerDao.class)
                        .updatePlayerField(player.getPlayerId(), PlayerFields.depositLimit, player.isDepositLimit());
            } else {//提现
                player.setWithdrawalLimit(Boolean.parseBoolean(status));
                EntityDaoMrg.getInstance().getDao(PlayerDao.class)
                        .updatePlayerField(player.getPlayerId(), PlayerFields.withdrawalLimit, player.isWithdrawalLimit());
            }

            PlayerMrg.responseHttp(ErrorCode.Success.getCode(), session, pid);
        } catch (Exception e) {
            LOGGER.error("GmHall_PlayerDepositWithdrawLimitHandler", e);
            PlayerMrg.responseHttp(ErrorCode.Internal_Server_Error.getCode(), session, pid);
        }
    }
}
