package com.game.handler.http.gm;

import com.game.engine.enums.state.ServerState;
import com.game.engine.io.handler.HttpHandler;
import com.game.engine.script.IHandlerEntity;
import com.game.engine.utils.Config;
import com.game.enums.ErrorCode;
import com.game.hall.mrg.player.PlayerMrg;
import io.netty.util.internal.StringUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Map;

//http://127.0.0.1:8380/gmHall/updateServerState?serverState=-1
@IHandlerEntity(path = "/gmHall/updateServerState", desc = "设置大厅服务器状态")
public class GmHall_UpdateServerStateHandler extends HttpHandler {
    private static Logger LOGGER = LoggerFactory.getLogger(GmHall_UpdateServerStateHandler.class);

    @Override
    public void run() {
        try {
            final Map<String, Object> paramsMap = getParamsMap();
            final String serverState = (String) paramsMap.get("serverState");

            if (StringUtil.isNullOrEmpty(serverState)) {
                PlayerMrg.responseHttp(ErrorCode.Parameter_Error.getCode(), session, pid);
                return;
            }

            Config.serverState = ServerState.valueof(Integer.parseInt(serverState));

            PlayerMrg.responseHttp(ErrorCode.Success.getCode(), session, pid);
        } catch (Exception e) {
            LOGGER.error("GmHall_UpdateServerStateHandler", e);
            PlayerMrg.responseHttp(ErrorCode.Internal_Server_Error.getCode(), session, pid);
        }
    }
}
