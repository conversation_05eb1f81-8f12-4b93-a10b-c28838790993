package com.game.handler.http.backstage;

import com.game.engine.io.handler.HttpHandler;
import com.game.engine.script.IHandlerEntity;
import com.game.engine.script.ScriptLoader;
import com.game.engine.utils.Config;
import com.game.enums.ErrorCode;
import com.game.enums.MerchantConfigType;
import com.game.hall.mrg.DataHallMrg;
import com.game.hall.mrg.MerchantData;
import com.game.hall.mrg.player.PlayerMrg;
import com.game.hall.script.INoticeScript;
import com.game.utils.VirtualThreadUtils;
import io.netty.util.internal.StringUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.HashSet;
import java.util.Map;
import java.util.Objects;
import java.util.Set;

//http://127.0.0.1:8380/gmHall/merchantReloadConfig?business_no=&reloadType=
@IHandlerEntity(path = "/gmHall/merchantReloadConfig", desc = "重新加载商户配置")
public class GmHall_ReloadMerchantConfigHandler extends HttpHandler {
    private static final Logger LOGGER = LoggerFactory.getLogger(GmHall_ReloadMerchantConfigHandler.class);

    @Override
    public void run() {
        try {
            final String business_no = (String) paramsMap.get("business_no");
            final String reloadType = (String) paramsMap.get("reloadType");

            if (StringUtil.isNullOrEmpty(business_no) || StringUtil.isNullOrEmpty(reloadType)) {
                PlayerMrg.responseHttp(ErrorCode.Parameter_Error.getCode(), session, pid);
                return;
            }

            final Set<String> business_nos = DataHallMrg.getInstance().getC_serverIdBusiness_noMap().getOrDefault(Config.SERVER_ID, new HashSet<>());

            if (Objects.equals(business_no, "ALL")) {
                for (Map.Entry<String, MerchantData> entry : DataHallMrg.getInstance().getMerchantDataMap().entrySet()) {
                    if (!business_nos.contains(entry.getKey())) {
                        continue;
                    }
                    loadConfig(entry.getKey(), entry.getValue(), Integer.parseInt(reloadType));
                }
            } else {
                if (!business_nos.contains(business_no)) {
                    PlayerMrg.responseHttp(ErrorCode.Success.getCode(), session, pid);
                    return;
                }

                final MerchantData merchantData = DataHallMrg.getInstance().findMerchantData(this.getClass().getSimpleName(), business_no);
                if (merchantData == null) {
                    PlayerMrg.responseHttp(ErrorCode.Parameter_Error.getCode(), session, pid);
                    return;
                }
                loadConfig(business_no, merchantData, Integer.parseInt(reloadType));
            }

            LOGGER.info("merchantReloadConfig，business_no：{}，reloadType：{}， success ...", business_no, reloadType);
            PlayerMrg.responseHttp(ErrorCode.Success.getCode(), session, pid);
        } catch (Exception e) {
            LOGGER.error("", e);
            PlayerMrg.responseHttp(ErrorCode.Internal_Server_Error.getCode(), session, pid);
        }
    }

    private void loadConfig(String business_no, MerchantData merchantData, int reloadType) throws Exception {
        switch (MerchantConfigType.valueOf(reloadType)) {
            case Activity:
                merchantData.loadActivity(business_no);
                break;
            case ActivityTag:
                merchantData.loadActivityTag(business_no);
                break;
            case Banner:
                merchantData.loadBanner(business_no);
                break;
            case MaintainNotice:
                merchantData.loadMaintainNotice(business_no);
                VirtualThreadUtils.execute(() -> {
                    ScriptLoader.getInstance().consumerScript("NoticeScript",
                            INoticeScript::notifyMerchantNotice
                    );
                });
                break;
            case CollectionWallet:
                merchantData.loadCollectionWallet(business_no);
                break;
            case Currency:
                merchantData.loadCurrency(business_no);
                break;
            case GameApi:
                merchantData.loadGameApi(business_no);
                break;
            case GameChannel:
                merchantData.loadGameChannel(business_no);
                break;
            case SubChannelGameApi:
                merchantData.loadSubChannelGameApi(business_no);
                break;
            case GamePlatform:
                merchantData.loadGamePlatform(business_no);
                break;
            case Global:
                merchantData.loadGlobal(business_no);
                break;
            case PlatformRecharge:
                merchantData.loadPlatformRecharge(business_no);
                break;
            case PlatformWithdraw:
                merchantData.loadPlatformWithdraw(business_no);
                break;
            case Popup:
                merchantData.loadPopup(business_no);
                break;
            case PubMail:
                merchantData.loadPubMail(business_no);
                break;
            case RechargeTurnover:
                merchantData.loadRechargeTurnover(business_no);
                break;
            case RechargeWithdrawLimit:
                merchantData.loadRechargeWithdrawLimit(business_no);
                break;
            case Region:
                merchantData.loadRegion(business_no);
                break;
            case VipClub:
                merchantData.loadVipClub(business_no);
                break;
            case HelpCenter:
                merchantData.loadHelpCenter(business_no);
                break;
            case ReferralReward:
                merchantData.loadReferralReward(business_no);
                break;
            case InvitationCashBack:
                merchantData.loadCashBack(business_no);
                break;
            case LuckSpin:
                merchantData.loadLuckSpin(business_no);
                break;
            case DailyContest:
                merchantData.loadDailyContest(business_no);
                break;
            case WeeklyRaffle:
                merchantData.loadWeeklyRaffle(business_no);
                break;
            case Quest:
                merchantData.loadQuest(business_no);
                break;
            case GameTurnover:
                merchantData.loadGameTurnover(business_no);
                break;
            case CustomerService:
                merchantData.loadCustomerService(business_no);
                break;
            case WinTicketNumbers:
                merchantData.loadWinTicketNumbers(business_no);
                break;
            case BottomMenu:
                merchantData.loadBottomMenu(business_no);
                break;
            case WebSiteInfo:
                merchantData.loadWebSiteInfo(business_no);
                break;
            case QualityAssurance:
                merchantData.loadQualityAssurance(business_no);
                break;
            case InvitationPoster:
                merchantData.loadInvitationPoster(business_no);
                break;
            case FreeGameTurnOver:
                merchantData.loadFreeGameTurnover(business_no);
                break;
            case InvitationLinks:
                merchantData.loadInvitationLinks(business_no);
                break;
            case RedemptionCode:
                merchantData.loadRedemptionCode(business_no);
                break;
            case ThreePartyLogin:
                merchantData.loadThreePartyLogin(business_no);
                break;
            case FunctionEnabled:
                merchantData.loadFunctionEnabled(business_no);
                break;
            case QuickAccess:
                merchantData.loadQuickAccess(business_no);
                break;
            case RedEnvelopeRain:
                merchantData.loadRedEnvelopeRain(business_no);
                break;
            case RewardBox:
                merchantData.loadRewardBox(business_no);
                break;
            case MysteryBonus:
                merchantData.loadMysteryBonus(business_no);
                break;
            case PiggyBank:
                merchantData.loadPiggyBank(business_no);
                break;
            case ContinuousDeposit:
                merchantData.loadContinuousDeposit(business_no);
                break;
            case Pwa:
                merchantData.loadPwa(business_no);
                break;
            case BigWin:
                merchantData.loadBigWin(business_no);
                break;
            case RegisterRetrievePop:
                merchantData.loadRegisterRetrievePop(business_no);
                break;
            case DailyRechargePop:
                merchantData.loadDailyRechargePop(business_no);
                break;
            case FirstChargePop:
                merchantData.loadFirstChargePop(business_no);
                break;
            case GamePop:
                merchantData.loadGamePop(business_no);
                break;
            case FirstChargeSignIn:
                merchantData.loadFirstChargeSignIn(business_no);
                break;
            case CrazyBox:
                merchantData.loadCrazyBox(business_no);
                break;
            case CrazyBoxQuest:
                merchantData.loadCrazyBoxQuest(business_no);
                break;
            case RechargeRecover:
                merchantData.loadRechargeRecover(business_no);
                break;
            case GameKillRate:
                merchantData.loadGameKillRate(business_no);
                break;
            case WageredRebates:
                merchantData.loadWageredRebates(business_no);
                break;
            case FirstDepositInviteBonus:
                merchantData.loadFirstDepositInviteBonus(business_no);
                break;
            default:
                throw new IllegalArgumentException(reloadType + "，not exist");
        }
    }
}
