//package com.game.handler.http.gmtest;
//
//import com.game.engine.io.handler.HttpHandler;
//import com.game.engine.script.IHandlerEntity;
//import com.game.engine.utils.TimeUtil;
//import com.game.enums.ErrorCode;
//import com.game.hall.manager.ServerMrg;
//import io.netty.util.internal.StringUtil;
//import org.slf4j.Logger;
//import org.slf4j.LoggerFactory;
//
//import java.util.LinkedHashMap;
//import java.util.Map;
//
////http://**************:8780/GmHall/ModifyServerTime?datetime=2023-03-18 10:45:41
//@IHandlerEntity(path = "/GmHall/ModifyServerTime", desc = "修改服务器时间")
//public class GmHall_ModifyServerTimeHandler extends HttpHandler {
//    private static final Logger LOGGER = LoggerFactory.getLogger(GmHall_ModifyServerTimeHandler.class);
//
//    @Override
//    public void run() {
//        final LinkedHashMap<String, Object> res = ServerMrg.httpCachePool.get();
//        try {
//            final Map<String, String> paramsMap = getParamsMap();
//            final String datetime = paramsMap.get("datetime");
//
//            if (StringUtil.isNullOrEmpty(datetime)) {
//                res.put("error", ErrorCode.Request_Parameters.getCode());
//                res.put("description", "please check the request parameters");
//                response(res);
//                return;
//            }
//
//            TimeUtil.setCurrentDateTime(datetime);
//            final String curTime = TimeUtil.getDateTimeFormat(TimeUtil.currentTimeMillis(), TimeUtil.YYYYMMDDHHMMSS);
//            LOGGER.warn("修改服务器时间成功：{}", curTime);
//
//            res.put("error", ErrorCode.Success.getCode());
//            res.put("description", "ok");
//            res.put("time", curTime);
//            response(res);
//        } catch (Exception e) {
//            LOGGER.error("", e);
//            res.put("error", ErrorCode.Internal_Error.getCode());
//            res.put("description", "internal error");
//            response(res);
//        } finally {
//            ServerMrg.httpCachePool.returnOne(res);
//        }
//    }
//}
