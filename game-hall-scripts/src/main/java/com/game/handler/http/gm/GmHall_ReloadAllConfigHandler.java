package com.game.handler.http.gm;

import com.game.engine.io.handler.HttpHandler;
import com.game.engine.script.IHandlerEntity;
import com.game.enums.ErrorCode;
import com.game.hall.mrg.DataHallMrg;
import com.game.hall.mrg.player.PlayerMrg;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

//http://127.0.0.1:8380/gmHall/reloadAllConfig?default=true
@IHandlerEntity(path = "/gmHall/reloadAllConfig", desc = "重新加载配置")
public class GmHall_ReloadAllConfigHandler extends HttpHandler {
    private static Logger LOGGER = LoggerFactory.getLogger(GmHall_ReloadAllConfigHandler.class);

    @Override
    public void run() {
        try {
            DataHallMrg.getInstance().loadConfigData();

            PlayerMrg.responseHttp(ErrorCode.Success.getCode(), session, pid);
            LOGGER.info("reloadAllConfig success ...");
        } catch (Exception e) {
            LOGGER.error("GmHall_ReloadAllConfigHandler", e);
            PlayerMrg.responseHttp(ErrorCode.Internal_Server_Error.getCode(), session, pid);
        }
    }

}
