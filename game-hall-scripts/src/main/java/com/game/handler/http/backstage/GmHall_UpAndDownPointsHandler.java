package com.game.handler.http.backstage;

import com.game.c_entity.merchant.C_RechargeWithdrawLimit;
import com.game.dao.player.PlayerDao;
import com.game.engine.io.handler.HttpHandler;
import com.game.engine.math.BigDecimalUtils;
import com.game.engine.script.IHandlerEntity;
import com.game.engine.script.ScriptLoader;
import com.game.entity.player.Player;
import com.game.enums.ErrorCode;
import com.game.enums.RewardReason;
import com.game.enums.SpendReason;
import com.game.enums.TurnoverReason;
import com.game.hall.mrg.DataHallMrg;
import com.game.hall.mrg.MerchantData;
import com.game.hall.mrg.currency.CurrencyMrg;
import com.game.hall.mrg.currency.RewardRequest;
import com.game.hall.mrg.currency.SpendRequest;
import com.game.hall.mrg.player.PlayerMrg;
import com.game.hall.script.IPlayerScript;
import com.game.manager.EntityDaoMrg;
import io.netty.util.internal.StringUtil;
import it.unimi.dsi.fastutil.ints.IntLists;
import it.unimi.dsi.fastutil.longs.LongArrayList;
import it.unimi.dsi.fastutil.longs.LongList;
import it.unimi.dsi.fastutil.longs.LongLists;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

//http://127.0.0.1:8380/gmHall/upAndDownPoints?playerId=&currencyId=&changeType=&changeAmount&bettingTurnoverMul=&rewardType=
@IHandlerEntity(path = "/gmHall/upAndDownPoints", desc = "上下分")
public class GmHall_UpAndDownPointsHandler extends HttpHandler {
    private static final Logger LOGGER = LoggerFactory.getLogger(GmHall_UpAndDownPointsHandler.class);

    @Override
    public void run() {
        try {
            final String playerId = (String) paramsMap.get("playerId");
            final String rewardType = (String) paramsMap.get("rewardType");
            final String currencyIdStr = (String) paramsMap.get("currencyId");
            final String changeType = (String) paramsMap.get("changeType");
            final String changeAmount = (String) paramsMap.get("changeAmount");
            final String bettingTurnoverMul = (String) paramsMap.get("bettingTurnoverMul");

            if (StringUtil.isNullOrEmpty(playerId) || StringUtil.isNullOrEmpty(rewardType)
                    || StringUtil.isNullOrEmpty(currencyIdStr) || StringUtil.isNullOrEmpty(changeType)
                    || StringUtil.isNullOrEmpty(changeAmount) || StringUtil.isNullOrEmpty(bettingTurnoverMul)) {
                PlayerMrg.responseHttp(ErrorCode.Parameter_Error.getCode(), session, pid);
                return;
            }

            final Player player = PlayerMrg.getInstance().findDbPlayer(Long.parseLong(playerId));
            if (player == null) {
                LOGGER.warn("playerId：{}，not exist", playerId);
                PlayerMrg.responseHttp(ErrorCode.Account_NotExist.getCode(), session, pid);
                return;
            }

            int currencyId = Integer.parseInt(currencyIdStr);
            final double amount = Double.parseDouble(changeAmount);
            ErrorCode errorCode = ErrorCode.None;
//            final WithdrawStandard withdrawStandard = player.getWithdrawStandard(currencyId);
            if (amount > 0) {
                if (Integer.parseInt(rewardType) == 2) {//1.cash 2.bonus
                    currencyId *= 10;
                }

                final RewardReason rewardReason = RewardReason.Up_Points;
                rewardReason.setSource(changeType);
                final RewardRequest rewardRequest = new RewardRequest();
                rewardRequest.addCurrency(currencyId, amount);
                errorCode = CurrencyMrg.getInstance().rewardNoTest(player, rewardRequest.getCurrencyMap(), rewardReason);
                EntityDaoMrg.getInstance().getDao(PlayerDao.class)
                        .updateCurrency(player, IntLists.singleton(currencyId));

                //withdrawStandard.incDrawStandard(BigDecimalUtils.mul(amount, Double.parseDouble(bettingTurnoverMul), 4));
                ScriptLoader.getInstance().consumerScript("PlayerWithdrawStandardScript",
                        (IPlayerScript script) -> script.drawStandard(player, TurnoverReason.Up_Points, Integer.parseInt(currencyIdStr), amount,
                                BigDecimalUtils.mul(amount, Double.parseDouble(bettingTurnoverMul), 4)));

                final MerchantData merchantData = DataHallMrg.getInstance().findMerchantData(this.getClass().getSimpleName(), player.getBusiness_no());
                if (merchantData != null) {
                    final C_RechargeWithdrawLimit c_limit = merchantData.findC_RechargeWithdrawLimit(this.getClass().getSimpleName(), currencyId);
                    if (c_limit != null) {
                        switch (c_limit.getWithdrawType()) {
                            case 1:
                            case 2:
                                if (player.getWithdrawStandardMap().containsKey(currencyId)) {
                                    EntityDaoMrg.getInstance().getDao(PlayerDao.class)
                                            .updateWithdrawStandard(player, IntLists.singleton(Integer.parseInt(currencyIdStr)));
                                }
                                break;
                            case 3:
                                final LongList updateOrderIdList = new LongArrayList(player.getTurnoverRecordMap().keySet());
                                EntityDaoMrg.getInstance().getDao(PlayerDao.class)
                                        .updateTurnoverRecord(player, updateOrderIdList, LongLists.EMPTY_LIST);
                                break;
                        }
                    }
                }
            }

            if (amount < 0) {
                if (Integer.parseInt(rewardType) == 2) {//1.cash 2.bonus
                    currencyId *= 10;
                }

                final SpendReason spendReason = SpendReason.Down_Points;
                spendReason.setSource(changeType);
                final SpendRequest spendRequest = new SpendRequest();
                spendRequest.addCurrency(currencyId, Math.abs(amount));
                errorCode = CurrencyMrg.getInstance().spendTest(player, spendRequest.getCurrencyMap());
                if (errorCode == ErrorCode.Success) {
                    CurrencyMrg.getInstance().spend(player, spendRequest.getCurrencyMap(), spendReason);
                    EntityDaoMrg.getInstance().getDao(PlayerDao.class)
                            .updateCurrency(player, IntLists.singleton(currencyId));

                    //withdrawStandard.incDrawStandard(BigDecimalUtils.mul(amount, Double.parseDouble(bettingTurnoverMul), 4));
                    ScriptLoader.getInstance().consumerScript("PlayerWithdrawStandardScript",
                            (IPlayerScript script) -> script.bettingTurnover(player, TurnoverReason.Down_Points, Integer.parseInt(currencyIdStr),
                                    BigDecimalUtils.mul(amount, Double.parseDouble(bettingTurnoverMul), 4)));
                    if (player.getWithdrawStandardMap().containsKey(currencyId)) {
                        EntityDaoMrg.getInstance().getDao(PlayerDao.class)
                                .updateWithdrawStandard(player, IntLists.singleton(Integer.parseInt(currencyIdStr)));
                    }
                }
            }

            PlayerMrg.responseHttp(errorCode.getCode(), session, pid);
        } catch (Exception e) {
            LOGGER.error("GmHall_UpAndDownPointsHandler", e);
            PlayerMrg.responseHttp(ErrorCode.Internal_Server_Error.getCode(), session, pid);
        }
    }
}
