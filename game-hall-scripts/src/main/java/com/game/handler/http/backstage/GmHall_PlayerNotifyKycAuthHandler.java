package com.game.handler.http.backstage;

import com.game.dao.player.PlayerDao;
import com.game.engine.io.handler.HttpHandler;
import com.game.engine.script.IHandlerEntity;
import com.game.entity.player.Player;
import com.game.entity.player.PlayerFields;
import com.game.enums.ErrorCode;
import com.game.hall.mrg.player.PlayerMrg;
import com.game.manager.EntityDaoMrg;
import io.netty.util.internal.StringUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

//http://127.0.0.1:8380/gmHall/playerNotifyKycAuth?playerId=1000004&authType=&status=0.未认证 1.认证中 2.认证成功 3.认证失败
@IHandlerEntity(path = "/gmHall/playerNotifyKycAuth", desc = "玩家通知kyc认证")
public class GmHall_PlayerNotifyKycAuthHandler extends HttpHandler {
    private static final Logger LOGGER = LoggerFactory.getLogger(GmHall_PlayerNotifyKycAuthHandler.class);

    @Override
    public void run() {
        try {
            final String playerId = (String) paramsMap.get("playerId");
            final String authType = (String) paramsMap.get("authType");
            final String status = (String) paramsMap.get("status");

            if (StringUtil.isNullOrEmpty(playerId) || StringUtil.isNullOrEmpty(authType) || StringUtil.isNullOrEmpty(status)) {
                PlayerMrg.responseHttp(ErrorCode.Parameter_Error.getCode(), session, pid);
                return;
            }

            final Player player = PlayerMrg.getInstance().findDbPlayer(Long.parseLong(playerId));
            if (player == null) {
                PlayerMrg.responseHttp(ErrorCode.Account_NotExist.getCode(), session, pid);
                return;
            }

            switch (Integer.parseInt(authType)) {
                case 1://基础认证
                    player.setBasicVerification(Integer.parseInt(status));
                    EntityDaoMrg.getInstance().getDao(PlayerDao.class)
                            .updatePlayerField(player.getPlayerId(), PlayerFields.basicVerification, player.getBasicVerification());
                    break;
                case 2://高级认证
                    player.setAdvancedVerification(Integer.parseInt(status));
                    EntityDaoMrg.getInstance().getDao(PlayerDao.class)
                            .updatePlayerField(player.getPlayerId(), PlayerFields.advancedVerification, player.getAdvancedVerification());
                    break;
            }

            PlayerMrg.responseHttp(ErrorCode.Success.getCode(), session, pid);
        } catch (Exception e) {
            LOGGER.error("GmHall_PlayerNotifyKycAuthHandler", e);
            PlayerMrg.responseHttp(ErrorCode.Internal_Server_Error.getCode(), session, pid);
        }
    }

}
