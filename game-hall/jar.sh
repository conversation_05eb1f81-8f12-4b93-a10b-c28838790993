#!/bin/bash
#usage: ./script_name -p [port] {start|stop|status|restart}

# Source function library.
. /etc/rc.d/init.d/functions

# Source networking configuration.
. /etc/sysconfig/network

# Check networking is up.
[ "$NETWORKING" = "no" ] && exit 0

##########################启动配置##########################
OPTS="-jar -server -Xmx1G -Xms1G -Xss256k
      -XX:+UseG1GC
      -XX:InitiatingHeapOccupancyPercent=35
      -XX:+HeapDumpOnOutOfMemoryError
      -XX:HeapDumpPath=../heapDump/hall.hprof
      -Xlog:gc:file=../gc_logs/hall_gc-%t.log:utctime,level,tags:filecount=10,filesize=100M
      -ea"
JAVA=${JAVA_HOME}/bin/java
#OPTS=${OPTS}" -Xdebug -Xnoagent -Djava.compiler=NONE -Xrunjdwp:transport=dt_socket,server=y,address=2002,suspend=n "
##########################启动配置##########################

LOGNAME=../log/hall/hall.log

RETVAL=0
PID=
DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" && pwd )"
echo $DIR
JAVA_DIR="${DIR}"
NAME=`basename "$DIR"`
PROG="java-server:"$NAME
JAR="${JAVA_DIR}/game-hall-1.0-SNAPSHOT.jar"

if [ ! -f $JAR ]; then
   echo -n $"$JAR not exist.";warning;echo
   exit 1
fi

cd $JAVA_DIR

PID_FILE="${DIR}/${PROG}.pid"
LOCKFILE="${DIR}/${PROG}.lock"

start() {    
    $JAVA $OPTS $JAR >/dev/null &
    echo $! > $PID_FILE
    RETVAL=$?
    if [ $RETVAL -eq 0 ]; then
        success;echo;touch $LOCKFILE
    else
        failure;echo
    fi
    return $RETVAL
}

stop() {

    echo -n $"Stopping $PROG: "$PID_FILE

    if [ -f $PID_FILE ] ;then
       read PID < "$PID_FILE" 
    else 
       failure;echo;
       echo -n $"$PID_FILE not found.";failure;echo
       return 1;
    fi

    if checkpid $PID; then
     kill -TERM $PID >/dev/null 2>&1
        RETVAL=$?
        if [ $RETVAL -eq 0 ] ;then
                success;echo 
                echo -n "Waiting for JAR to shutdown .."
         while checkpid $PID;do
                 echo -n "."
                 sleep 1;
                done
                success;echo;rm -f $LOCKFILE
		rm -f $PID_FILE
        else 
                failure;echo
        fi
    else
        echo -n $"JAR is dead and $PID_FILE exists.";failure;echo
        RETVAL=7
    fi    
    return $RETVAL
}

restart() {
    stop
    start
}

rhstatus() {
    status -p ${PID_FILE} $PROG
}

hid_status() {
    rhstatus >/dev/null 2>&1
}

case "$1" in
    starts)
        hid_status && exit 0
        start
        ;;
    start)
        hid_status && exit 0
        start
	cd $DIR
	tail -F $LOGNAME
        ;;
    stop)
        rhstatus || exit 0
        stop
        ;;
    restart)
        restart
        ;;
    status)
        rhstatus
        RETVAL=$?
        ;;
    *)
        echo $"Usage: $0 [name] [serverID] {start|stop|status|restart}"
        RETVAL=1
esac

exit $RETVAL