package com.game;

import com.game.engine.io.handler.TcpHandler;
import com.game.engine.script.IHandlerEntity;
import com.game.engine.utils.MsgUtil;
import com.game.entity.player.Player;
import com.game.enums.ErrorCode;
import com.game.hall.mrg.player.PlayerMrg;
import com.proto.AffiliateMessage;
import com.proto.MIDMessage;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@IHandlerEntity(mid = MIDMessage.MID.ReqDashboardData_VALUE, msg = AffiliateMessage.ReqDashboardDataMessage.class)
public class ModelHandler extends TcpHandler {
    private static final Logger LOGGER = LoggerFactory.getLogger(ModelHandler.class);

    @Override
    public void run() {
        final AffiliateMessage.ResDashboardDataMessage.Builder res = AffiliateMessage.ResDashboardDataMessage.newBuilder();
        res.setMsgID(MIDMessage.MID.ResDashboardData_VALUE);
        try {
            final Player player = PlayerMrg.getInstance().getOnlinePlayer(pid, this.getClass().getSimpleName());
            if (player == null) {
                res.setError(ErrorCode.Player_Offline.getCode());
                MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                return;
            }

        } catch (Exception e) {
            LOGGER.error("ReqDashboardDataHandler", e);
            res.setError(ErrorCode.Internal_Server_Error.getCode());
            MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
        }
    }
}
