package com.game.hall.mrg;

import com.game.engine.utils.TimeUtil;
import com.game.user.ResistUserMsg;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.HashSet;
import java.util.LinkedHashMap;
import java.util.Map;
import java.util.Set;

public class UdpMsgAttackResistMrg {
    private static final Logger LOGGER = LoggerFactory.getLogger(UdpMsgAttackResistMrg.class);

    private static final UdpMsgAttackResistMrg instance = new UdpMsgAttackResistMrg();

    private static final long DURATION = TimeUtil.SEC;

    private final Map<String, ResistUserMsg> resistUserMsgMap = new LinkedHashMap<>();

    private final Set<String> ipBlacklist = new HashSet<>();

    public static UdpMsgAttackResistMrg getInstance() {
        return instance;
    }

    public Map<String, ResistUserMsg> getResistUserMsgMap() {
        return resistUserMsgMap;
    }

    public Set<String> getIpBlacklist() {
        return ipBlacklist;
    }
}
