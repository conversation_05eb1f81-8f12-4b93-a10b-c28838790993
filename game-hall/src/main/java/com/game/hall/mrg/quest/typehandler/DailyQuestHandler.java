package com.game.hall.mrg.quest.typehandler;

import com.game.c_entity.merchant.C_Quest;
import com.game.engine.script.ScriptLoader;
import com.game.engine.utils.TimeUtil;
import com.game.entity.player.Player;
import com.game.entity.player.quest.QuestInfo;
import com.game.entity.player.quest.SingleQuestInfo;
import com.game.enums.FunctionEnabled;
import com.game.enums.QuestType;
import com.game.hall.mrg.DataHallMrg;
import com.game.hall.mrg.MerchantData;
import com.game.hall.script.IFunctionEnabledScript;

import java.util.ArrayList;

public class DailyQuestHandler extends AbsQuestTypeHandler {


    @Override
    public QuestType getType() {
        return QuestType.DAILY;
    }

    @Override
    protected boolean checkCanAccept(Player player, QuestInfo questInfo, C_Quest c_quest) {
        if (player.getVipClub().getVipLevel() < c_quest.getVipLevelLimit()) {
            return false;
        }
        final int dayOfWeek = TimeUtil.getDayOfWeek();
        if (!c_quest.getDayOfWeek().isEmpty() && !c_quest.getDayOfWeek().contains(dayOfWeek)) {
            return false;
        }
        return true;
    }

    /**
     * 每日任务信息重置
     */
    public void dailyReset(Player player) {
        final boolean functionEnabled = ScriptLoader.getInstance().functionScript("FunctionEnabledScript", (IFunctionEnabledScript script)
                -> script.functionEnabled(player.getBusiness_no(), FunctionEnabled.Quest.getType()));
        if (!functionEnabled) {
            return;
        }

        final QuestInfo questInfo = player.getQuestInfo();
        final ArrayList<SingleQuestInfo> dailyQuestList = new ArrayList<>();
        // 获取所有的日常任务
        for (SingleQuestInfo info : questInfo.getQuestInfoMap().values()) {
            if (info.getQuestType() == QuestType.DAILY.getNumber()) {
                dailyQuestList.add(info);
            }
        }

        // 放弃所有日常任务
        giveUpQuest(player, dailyQuestList);
//        questListPool.returnOne(dailyQuestList);

        // 重新接取每日任务
        checkAndAcceptQuest(player);
    }

}