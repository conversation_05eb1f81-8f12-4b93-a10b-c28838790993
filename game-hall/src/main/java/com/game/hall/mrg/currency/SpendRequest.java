package com.game.hall.mrg.currency;

import com.game.engine.math.BigDecimalUtils;
import com.game.engine.utils.Symbol;
import it.unimi.dsi.fastutil.ints.Int2DoubleArrayMap;
import it.unimi.dsi.fastutil.ints.Int2DoubleMap;

/**
 * Q: 为什么使用{@code ArrayMap}?
 * A: 因为数据量小啊！
 */
public class SpendRequest {

    /**
     * 没有使用{@link java.util.EnumMap}是故意的，货币类型后期会非常多，但是使用量很少，避免空间浪费。
     */
    private final Int2DoubleMap currencyMap = new Int2DoubleArrayMap(2);

    /**
     * Q: 为什么这里没有拆分？？
     * A: 1. 是要在这里支持读表。
     * 2. 多数情况下同时消耗的道具很少，也方便业务逻辑自己去拆分。
     * 3. 兼容多页签和单页签的背包.
     */

    /**
     * @param currencyType 货币类型
     * @param num          消耗数量
     * @return this
     */
    public SpendRequest addCurrency(int currencyType, double num) {
        if (num < 0) {
            throw new IllegalArgumentException("currencyType: " + currencyType + ", num: " + num);
        }
        double sum = BigDecimalUtils.add(currencyMap.getOrDefault(currencyType, 0L), num, 9);
        if (sum < 0) {
            throw new IllegalArgumentException("currencyType: " + currencyType + ", sum: " + sum);
        }
        currencyMap.put(currencyType, sum);
        return this;
    }

    public Int2DoubleMap getCurrencyMap() {
        return currencyMap;
    }

    public boolean isEmpty() {
        return currencyMap.isEmpty();
    }

    public void reset() {
        currencyMap.clear();
    }
}
