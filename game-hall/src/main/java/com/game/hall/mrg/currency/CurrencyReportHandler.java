package com.game.hall.mrg.currency;

import it.unimi.dsi.fastutil.ints.IntArrayList;
import it.unimi.dsi.fastutil.ints.IntList;

/**
 * Q: 目的？
 * A: 整合多个货币的更新和同步。
 */
public class CurrencyReportHandler {

    /** 改变的货币 */
    private final IntList changedCurrencies = new IntArrayList(8);

    public void add(int currencyType) {
        changedCurrencies.add(currencyType);
    }

    public IntList getChangedCurrencies() {
        return changedCurrencies;
    }

    public void reset() {
        changedCurrencies.clear();
    }

}