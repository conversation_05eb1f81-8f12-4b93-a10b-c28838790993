package com.game.hall.mrg.currency;

import com.game.c_entity.merchant.C_Currency;
import com.game.dao.player.PlayerDao;
import com.game.engine.utils.FastCollectionsUtils;
import com.game.engine.utils.TimeUtil;
import com.game.entity.player.Player;
import com.game.enums.*;
import com.game.hall.script.CurrencyHandler;
import com.game.manager.EntityDaoMrg;
import com.proto.CommonMessage;
import com.proto.MIDMessage;
import com.proto.TcpMessage;
import it.unimi.dsi.fastutil.ints.*;
import it.unimi.dsi.fastutil.objects.ObjectIterator;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Set;

public class CurrencyMrg {
    private static final Logger LOGGER = LoggerFactory.getLogger(CurrencyMrg.class);

    private static final CurrencyMrg instance = new CurrencyMrg();

    //private static final SingleObjectPool<CurrencyReportHandler> reportHandlerPool = new SingleObjectPool<>(CurrencyReportHandler::new, CurrencyReportHandler::reset);
    //private static final SingleObjectPool<SpendRequest> requestCachePool = new SingleObjectPool<>(SpendRequest::new, SpendRequest::reset);

    private final Int2ObjectMap<CurrencyHandler> handlerMap = new Int2ObjectOpenHashMap<>();

    public static CurrencyMrg getInstance() {
        return instance;
    }

    public void registerCurrencyHandler(Set<Integer> currencys) {
        if (currencys.isEmpty()) {
            throw new IllegalArgumentException("注册货币失败");
        }
        for (int currencyType : currencys) {
            if (handlerMap.containsKey(currencyType)) {
                continue;
            }
            handlerMap.put(currencyType, new DefaultCurrencyHandler(currencyType));
            //赠金
            handlerMap.put(currencyType * 10, new DefaultCurrencyHandler(currencyType * 10));
            LOGGER.info("register currency handler: {}", currencyType);
        }
    }

    private CurrencyHandler getCurrencyHandler(int currencyType) {
        final CurrencyHandler currencyHandler = handlerMap.get(currencyType);
        if (null == currencyHandler) {
            throw new IllegalArgumentException("unexpected currencyType: " + currencyType);
        }
        return currencyHandler;
    }

    /**
     * 获取货币数量
     */
    public double getCurrencyValue(Player player, int currencyType) {
        return getCurrencyHandler(currencyType).getValue(player);
    }

    // region 奖励
    public ErrorCode reward(Player player, Int2DoubleMap currencyMap, RewardReason reason) {
        return rewardImpl(player, currencyMap, reason, true);
    }

    public ErrorCode rewardNoTest(Player player, Int2DoubleMap currencyMap, RewardReason reason) {
        return rewardImpl(player, currencyMap, reason, false);
    }

    public ErrorCode rewardTest(Player player, Int2DoubleMap currencyMap) {
        for (ObjectIterator<Int2DoubleMap.Entry> itr = FastCollectionsUtils.fastIterator(currencyMap); itr.hasNext(); ) {
            final Int2DoubleMap.Entry entry = itr.next();
            final CurrencyHandler currencyHandler = handlerMap.get(entry.getIntKey());
            final ErrorCode testResult = currencyHandler.rewardTest(player, entry.getDoubleValue());
            if (testResult != ErrorCode.Success) {
                return testResult;
            }
        }
        return ErrorCode.Success;
    }

    private ErrorCode rewardImpl(Player player, Int2DoubleMap currencyMap, RewardReason reason, boolean test) {
        if (currencyMap.isEmpty()) {
            return ErrorCode.Success;
        }

        if (test) {
            final ErrorCode testResult = rewardTest(player, currencyMap);
            if (testResult != ErrorCode.Success) {
                return testResult;
            }
        }

        final CurrencyReportHandler reportHandler = new CurrencyReportHandler();
        try {
            for (ObjectIterator<Int2DoubleMap.Entry> itr = FastCollectionsUtils.fastIterator(currencyMap); itr.hasNext(); ) {
                final Int2DoubleMap.Entry entry = itr.next();
                final CurrencyHandler currencyHandler = handlerMap.get(entry.getIntKey());
                currencyHandler.rewardNoTest(player, entry.getDoubleValue(), reportHandler, reason);
            }
            updateToDBAndSyncToClient(player, reportHandler, reason);
        } finally {
//            reportHandlerPool.returnOne(reportHandler);
        }
        return ErrorCode.Success;
    }

    // endregion


    // region 消耗
    public ErrorCode spendTest(Player player, Int2DoubleMap originCurrencyMap) {
        final SpendRequest spendRequest = new SpendRequest();
        final ErrorCode result;
        try {
            calFinalSpend(originCurrencyMap, spendRequest);

            result = spendTestImpl(player, spendRequest.getCurrencyMap());
        } finally {
//            requestCachePool.returnOne(spendRequest);
        }
        return result;
    }

    /**
     * 处理金币代换逻辑
     */
    public ErrorCode spend(Player player, Int2DoubleMap currencyMap, SpendReason reason) {
        return spendImpl(player, currencyMap, reason, true);
    }

    public ErrorCode spendNonTest(Player player, Int2DoubleMap currencyMap, SpendReason reason) {
        return spendImpl(player, currencyMap, reason, false);
    }

    private ErrorCode spendTestImpl(Player player, Int2DoubleMap currencyMap) {
        for (ObjectIterator<Int2DoubleMap.Entry> itr = FastCollectionsUtils.fastIterator(currencyMap); itr.hasNext(); ) {
            final Int2DoubleMap.Entry entry = itr.next();
            final CurrencyHandler currencyHandler = handlerMap.get(entry.getIntKey());
            final ErrorCode testResult = currencyHandler.spendTest(player, entry.getDoubleValue());
            if (testResult != ErrorCode.Success) {
                return testResult;
            }
        }
        return ErrorCode.Success;
    }

    private ErrorCode spendImpl(Player player, Int2DoubleMap originCurrencyMap, SpendReason reason, boolean test) {
        if (originCurrencyMap.isEmpty()) {
            return ErrorCode.Success;
        }

        final SpendRequest spendRequest = new SpendRequest();
        final CurrencyReportHandler reportHandler = new CurrencyReportHandler();
        try {
            calFinalSpend(originCurrencyMap, spendRequest);
            final Int2DoubleMap currencyMap = spendRequest.getCurrencyMap();

            if (test) {
                final ErrorCode testResult = spendTestImpl(player, currencyMap);
                if (testResult != ErrorCode.Success) {
                    return testResult;
                }
            }

            for (ObjectIterator<Int2DoubleMap.Entry> itr = FastCollectionsUtils.fastIterator(currencyMap); itr.hasNext(); ) {
                final Int2DoubleMap.Entry entry = itr.next();
                final CurrencyHandler currencyHandler = handlerMap.get(entry.getIntKey());
                currencyHandler.spendNoTest(player, entry.getDoubleValue(), reportHandler, reason);
            }
            updateToDBAndSyncToClient(player, reportHandler, reason);
        } finally {
//            requestCachePool.returnOne(spendRequest);
//            reportHandlerPool.returnOne(reportHandler);
        }
        return ErrorCode.Success;
    }

    private void calFinalSpend(Int2DoubleMap currencyMap, SpendRequest out) {
        for (ObjectIterator<Int2DoubleMap.Entry> itr = FastCollectionsUtils.fastIterator(currencyMap); itr.hasNext(); ) {
            final Int2DoubleMap.Entry next = itr.next();
            final int currencyType = next.getIntKey();
            final double value = next.getDoubleValue();
            out.addCurrency(currencyType, value);
        }
    }

    // endregion

    // region 辅助方法
    private void updateToDBAndSyncToClient(Player player, CurrencyReportHandler reportHandler, Object reason) {
        final IntList changedCurrencies = reportHandler.getChangedCurrencies();
        if (changedCurrencies.isEmpty()) {
            return;
        }
        player.updateCurrency(changedCurrencies);
        if (!player.isOnline()) {
            EntityDaoMrg.getInstance().getDao(PlayerDao.class)
                    .updateCurrency(player, changedCurrencies);
        }
        player.setLastChangeTime(TimeUtil.currentTimeMillis());
        // 同步 - 一定只有存储在currencyMap里的货币
        final TcpMessage.ResTcpCurrencyUpdateMessage.Builder res = TcpMessage.ResTcpCurrencyUpdateMessage.newBuilder();
        res.setMsgID(MIDMessage.MID.ResTcpCurrencyUpdate_VALUE);
        for (int i = 0; i < changedCurrencies.size(); i++) {
            final int currencyType = changedCurrencies.getInt(i);
            int currencyId = 0;
            if (String.valueOf(currencyType).length() == 4) {
                currencyId = currencyType;
            } else {
                currencyId = currencyType / 10;
            }
            final double currencyValue = player.getCurrencyMap().getOrDefault(currencyId, 0d);
            final double bonus = player.getCurrencyMap().getOrDefault(currencyId * 10, 0d);
            res.addCItem(buildCurrency(currencyId, currencyValue, bonus));
        }
        if (reason instanceof RewardReason) {
            res.setReason(1)
                    .setSource(((RewardReason) reason).getReason());
        }
        if (reason instanceof SpendReason) {
            res.setReason(2)
                    .setSource(((SpendReason) reason).getReason());
        }
        player.sendMsg(res.build());
    }

    public CommonMessage.DCurrencyItem buildCurrency(int currencyId, double currencyValue, double bonus) {
        return buildCurrency(currencyId, currencyValue, bonus, null);
    }

    public CommonMessage.DCurrencyItem buildCurrency(int currencyId, double currencyValue) {
        return buildCurrency(currencyId, currencyValue, 0, null);
    }

    public CommonMessage.DCurrencyItem buildCurrency(int currencyId, double currencyValue, double bonus, C_Currency c_currency) {
        final CommonMessage.DCurrencyItem.Builder builder = CommonMessage.DCurrencyItem.newBuilder();
        builder.setCurrencyId(currencyId)
                .setValue(currencyValue)
                .setBonusValue(bonus);
        if (c_currency != null) {
            builder.setName(c_currency.getCurrencyName())
                    .setIcon(c_currency.getIcon())
                    .setSymbol(c_currency.getSymbol())
                    .setShow(c_currency.isShow());
        }
        return builder.build();
    }

}
