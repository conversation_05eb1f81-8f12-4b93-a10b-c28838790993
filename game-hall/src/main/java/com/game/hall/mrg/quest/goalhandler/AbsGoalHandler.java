package com.game.hall.mrg.quest.goalhandler;

import com.game.c_entity.merchant.C_Quest;
import com.game.engine.math.BigDecimalUtils;
import com.game.engine.util.MathUtils;
import com.game.entity.player.Player;
import com.game.entity.player.quest.GoalCallback;
import com.game.entity.player.quest.SingleQuestInfo;
import com.game.enums.QuestGoalType;
import com.game.hall.mrg.DataHallMrg;
import com.game.hall.mrg.MerchantData;
import com.game.hall.mrg.quest.Blackboard;
import com.game.hall.mrg.quest.QuestState;

/**
 * 进度类型：累计型进度，递减型进度，达成型进度，激活型任务进度...
 * 虽然我们可以极度抽象为几个Handler，但如果进度不是从任务db获取的话，还是不建议使用公共的handler。
 */
public abstract class AbsGoalHandler {

    /**
     * 任务目标
     */
    public abstract QuestGoalType goalType();


    /**
     * 是否是累计类型的任务，需要累计计数的目标需要覆写此方法
     */
    public boolean isAccumulatedType() {
        return false;
    }

    /**
     * Q: 为什么使用黑板，而不是直接传入事件对象？
     * A: 直接使用事件对象会造成强依赖，造成难以测试等问题；而如果为每个类型定义一个辅助类的话，又会产生大量的类型。
     *
     * @param singleQuestInfo 任务信息
     * @param blackboard      用于获取事件参数
     * @return 如果事件有效，则返回true (校验参数)
     */
    public abstract boolean update(Player player, SingleQuestInfo singleQuestInfo, GoalCallback callback, Blackboard blackboard);

    /**
     * 直接设置进度
     */
    public void gmSetGoalProgressive(Player player, SingleQuestInfo singleQuestInfo, int progressive, GoalCallback callback) {
        final int totalProgressive = getQuest(player, singleQuestInfo.getQuestId()).getQuestCondition().totalProgressive;
        progressive = MathUtils.clamp(progressive, 0, totalProgressive);
        updateProgressive(player, singleQuestInfo, progressive, callback);
    }

    /**
     * 是否是进度小于等于目标值才能完成的类型，如排名，如果是覆写此方法
     */
    protected boolean isSmallerProgressBetterGoals() {
        return false;
    }

    /**
     * 任务的进度值是否已达标
     */
    public boolean isProgressCompleted(Player player, SingleQuestInfo singleQuestInfo) {
        C_Quest c_quest = getQuest(player, singleQuestInfo.getQuestId());
        if (c_quest == null) {
            return false;
        }
        double curProgressive = singleQuestInfo.getProgressive();
        int targetProgressive = c_quest.getQuestCondition().getTotalProgressive();
        if (singleQuestInfo.getProgressive() == 0) {
            return false;
        }
        boolean minBetter = isSmallerProgressBetterGoals();
        return minBetter ? curProgressive <= targetProgressive : curProgressive >= targetProgressive;
    }

    /**
     * 增加进度值
     */
    protected void incProgressive(Player player, SingleQuestInfo singleQuestInfo, double deltaProgressive, GoalCallback callback) {
        if (deltaProgressive == 0) {
            return;
        }
        final double curProgressive = BigDecimalUtils.add(singleQuestInfo.getProgressive(), deltaProgressive, 9);
        updateProgressive(player, singleQuestInfo, curProgressive, callback);
    }

    /**
     * 更新进度值, 检测完成状态
     */
    protected final void updateProgressive(Player player, SingleQuestInfo singleQuestInfo, double progressive, GoalCallback callback) {
        if (progressive < 0) {
            throw new IllegalArgumentException(String.format("progressive must be positive, oldProgressive: %s, curProgressive: %s",
                    singleQuestInfo.getProgressive() + "", progressive + ""));
        }
        if (singleQuestInfo.getProgressive() == progressive) {
            return;
        }

        if (!isAccumulatedType() && singleQuestInfo.getProgressive() > 0) {
            return;
        }

        singleQuestInfo.setProgressive(progressive);
        final boolean progressCompleted = isProgressCompleted(player, singleQuestInfo);
        final boolean newComplete = singleQuestInfo.getState() < QuestState.FINISHED && progressCompleted;

        callback.onGoalInfoChanged(player, singleQuestInfo, newComplete);
    }

    /**
     * 辅助方法，获取任务配置数据
     */
    protected C_Quest getQuest(Player player, int questId) {
        final MerchantData merchantData = DataHallMrg.getInstance().findMerchantData(this.getClass().getSimpleName(), player.getBusiness_no());
        if (merchantData == null) {
            return null;
        }
        return merchantData.findC_Quest(this.getClass().getSimpleName(), questId);
    }

}