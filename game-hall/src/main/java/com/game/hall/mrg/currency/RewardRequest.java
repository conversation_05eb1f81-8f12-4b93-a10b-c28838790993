package com.game.hall.mrg.currency;

import com.game.engine.math.BigDecimalUtils;
import com.game.engine.util.misc.Tuple2;
import com.game.engine.utils.Symbol;
import com.proto.CommonMessage;
import io.netty.util.internal.StringUtil;
import it.unimi.dsi.fastutil.ints.Int2DoubleArrayMap;
import it.unimi.dsi.fastutil.ints.Int2DoubleMap;
import it.unimi.dsi.fastutil.ints.Int2DoubleMaps;
import it.unimi.dsi.fastutil.objects.ObjectIterator;

import java.util.function.Consumer;

/**
 * 注意：对于复杂的背包，实现原子的放入还是很困难的，检测算法要十分小心，尤其是一个奖励可能跨越多个背包的情况。
 */
public class RewardRequest {

    /**
     * 没有使用{@link java.util.EnumMap}是故意的，货币类型后期会非常多，但是同时使用的类型很少，避免空间浪费。
     * （不能每创建一个奖励请求就分配一个超大数组）
     */
    private final Int2DoubleMap currencyMap = new Int2DoubleArrayMap(4);

    private final Int2DoubleMap bonusCurrencyMap = new Int2DoubleArrayMap(4);

    public RewardRequest addCurrency(int currencyType, double num) {
        return addCurrency(currencyType, num, true);
    }

    /**
     * 添加货币
     *
     * @param currencyType 货币类型
     * @param num          数量
     * @return this
     */
    public RewardRequest addCurrency(int currencyType, double num, boolean bonus) {
        if (num < 0) {
            throw new IllegalArgumentException("num: " + num);
        }
        if (num == 0) {
            return this;
        }
        final double sum = BigDecimalUtils.add(currencyMap.getOrDefault(currencyType, 0), num, 9);
        currencyMap.put(currencyType, sum);

        if (bonus) {
            //添加赠金
            final double bonusSum = BigDecimalUtils.add(bonusCurrencyMap.getOrDefault(currencyType * 10, 0), num, 9);
            bonusCurrencyMap.put(currencyType * 10, bonusSum);
        }
        return this;
    }

    public Int2DoubleMap getCurrencyMap() {
        return currencyMap;
    }

    public Int2DoubleMap getBonusCurrencyMap() {
        return bonusCurrencyMap;
    }

    public void reset() {
        currencyMap.clear();
        bonusCurrencyMap.clear();
    }

    //id:num,id:num
    public void writeToItemShowMsg(Consumer<CommonMessage.DItemShow> out) {
        if (!currencyMap.isEmpty()) {
            for (ObjectIterator<Int2DoubleMap.Entry> itr = Int2DoubleMaps.fastIterator(currencyMap); itr.hasNext(); ) {
                final Int2DoubleMap.Entry entry = itr.next();
                final int currencyType = entry.getIntKey();
                out.accept(buildItemShow(currencyType, entry.getDoubleValue()));
            }
        }
    }

    private CommonMessage.DItemShow buildItemShow(int itemId, double num) {
        return CommonMessage.DItemShow.newBuilder()
                .setItemId(itemId)
                .setNum(num).build();
    }

    public Tuple2<Integer, Double> getReward() {
        final Int2DoubleMap.Entry firstEntry = this.currencyMap.int2DoubleEntrySet().iterator().next();
        final int firstKey = firstEntry.getIntKey();
        final double firstValue = firstEntry.getDoubleValue();
        return new Tuple2<>(firstKey, firstValue);
    }
}