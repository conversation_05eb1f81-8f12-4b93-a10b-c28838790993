package com.game.hall.mrg;

import com.game.dao.player.PlayerDao;
import com.game.engine.script.ScriptLoader;
import com.game.engine.util.async.DefaultSameThreadScheduledExecutor;
import com.game.engine.util.async.SameThreadScheduledExecutor;
import com.game.engine.utils.TimeHelper;
import com.game.engine.utils.TimeUtil;
import com.game.engine.utils.TimeUtils;
import com.game.entity.player.Player;
import com.game.entity.player.PlayerFields;
import com.game.enums.ResetType;
import com.game.hall.main.HallServer;
import com.game.hall.mrg.player.PlayerMrg;
import com.game.hall.script.IGameScript;
import com.game.hall.script.IPlayerResetHandler;
import com.game.hall.script.IResetHandler;
import com.game.manager.EntityDaoMrg;
import com.game.utils.VirtualThreadUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.ThreadLocalRandom;

/**
 * 每日刷新管理器（重要事件统一管理）
 * （还是需要每个玩家单独检测，否则所有玩家会集中在一起跨天，瞬时写操作过多）
 * 不基于事件执行关键逻辑，保证顺序。
 */
public class GameResetMrg {

    private static final Logger logger = LoggerFactory.getLogger(GameResetMrg.class);

    private final SameThreadScheduledExecutor timerSystem;

    private final List<IPlayerResetHandler> dailyZeroHandlerList = new ArrayList<>();
    private final List<IPlayerResetHandler> dailyTwoHandlerList = new ArrayList<>();
    private final List<IPlayerResetHandler> weeklyZeroHandlerList = new ArrayList<>();
    private final List<IPlayerResetHandler> weeklyFiveHandlerList = new ArrayList<>();
    private final List<IPlayerResetHandler> mouthZeroHandlerList = new ArrayList<>();

    // 比如特定功能的跨天逻辑
    private final List<IResetHandler> gameDailyZeroHandlerList = new ArrayList<>();
    private final List<IResetHandler> gameWeeklyZeroHandlerList = new ArrayList<>();

    public GameResetMrg() {
        this.timerSystem = new DefaultSameThreadScheduledExecutor(4);

        final long currentTimeMillis = TimeUtil.currentTimeMillis();
        timerSystem.scheduleRun(nextZeroDelay(currentTimeMillis), this::dailyResetZero);
        timerSystem.scheduleRun(nextTwoDelay(currentTimeMillis), this::dailyResetTwo);
        timerSystem.scheduleRun(nextWeekZeroDelay(currentTimeMillis), this::weeklyResetZero);
        timerSystem.scheduleRun(nextMouthZeroDelay(currentTimeMillis), this::mouthResetZero);

        this.timerSystem.scheduleAtFixedDelay(5 * TimeUtil.SEC, 2 * TimeUtil.SEC,
                () -> {
                    VirtualThreadUtils.execute(() -> ScriptLoader.getInstance().consumerScript("GameScript",
                            IGameScript::notifyDailyContestPrizePool));
                });

        this.timerSystem.scheduleAtFixedDelay(5 * TimeUtil.SEC, 5 * TimeUtil.SEC,
                () -> {
                    ConnectionAttackResistMrg.getInstance().checkConnectionAttack();
                    UserTcpMrg.getInstance().timerCheckSession();
                    HallServer.serverHeartCheck();
                    PlayerMrg.getInstance().timerSaveRealPlayer();
                    VirtualThreadUtils.execute(() -> ScriptLoader.getInstance().consumerScript("GameScript",
                            IGameScript::notifyGameNoteBet));
                });

//        this.timerSystem.scheduleAtFixedDelay(5 * TimeUtil.SEC, 10 * TimeUtil.SEC,
//                () -> VirtualThreadUtils.execute(() -> ScriptLoader.getInstance().consumerScript("GameScript",
//                        IGameScript::notifyGameNoteHigh)
//                ));

        this.timerSystem.scheduleAtFixedDelay(5 * TimeUtil.SEC, TimeUtil.MIN,
                () -> {
                    PlayerMrg.getInstance().everyMinuteCheck();
                    PlayerMrg.getInstance().timerCheckHear();
                    PlayerMrg.getInstance().timerSavePlayer();
                });
    }

    public void registerHandlers() {
        //每天凌晨0点
        registerGameHandler(ResetType.DAILY_ZERO, ActivityMrg::gameEveryDayReset);
        //每周凌晨0点
        registerGameHandler(ResetType.WEEKLY_ZERO, ActivityMrg::gameEveryWeeklyReset);

        //每天凌晨0点
        registerHandler(ResetType.DAILY_ZERO, PlayerMrg::everyDayReset);
        //每周凌晨0点
        registerHandler(ResetType.WEEKLY_ZERO, PlayerMrg::everyWeeklyReset);
        //每月凌晨0点
        registerHandler(ResetType.MOUTH_ZERO, PlayerMrg::everyMonthReset);
    }

    private List<IPlayerResetHandler> getHandlerList(ResetType resetType) {
        switch (resetType) {
            case DAILY_ZERO:
                return dailyZeroHandlerList;
            case DAILY_TWO:
                return dailyTwoHandlerList;
            case WEEKLY_ZERO:
                return weeklyZeroHandlerList;
            case WEEKLY_FIVE:
                return weeklyFiveHandlerList;
            case MOUTH_ZERO:
                return mouthZeroHandlerList;
            default:
                throw new AssertionError(resetType);
        }
    }

    private List<IResetHandler> getGameHandlerList(ResetType resetType) {
        switch (resetType) {
            case DAILY_ZERO:
                return gameDailyZeroHandlerList;
            case WEEKLY_ZERO:
                return gameWeeklyZeroHandlerList;
            default:
                throw new AssertionError(resetType);
        }
    }

    private void registerHandler(ResetType resetType, IPlayerResetHandler resetHandler) {
        getHandlerList(resetType).add(resetHandler);
    }

    private void registerGameHandler(ResetType resetType, IResetHandler resetHandler) {
        getGameHandlerList(resetType).add(resetHandler);
    }

    /**
     * 一个随机延迟，避免所有game过于集中更新数据
     */
    private static int randomDelay() {
        return ThreadLocalRandom.current().nextInt(1000, 5000);
    }

    private static long nextZeroDelay(long curTimeMillis) {
        final long timeBeginOfToday = TimeHelper.SYSTEM.getTimeBeginOfToday(curTimeMillis);
        return timeBeginOfToday + TimeUtils.DAY + randomDelay() - curTimeMillis;
    }

    private static long nextTwoDelay(long curTimeMillis) {
        final long todayTwoClock = TimeHelper.SYSTEM.getTimeBeginOfToday(curTimeMillis) + 2 * TimeUtils.HOUR;
        if (curTimeMillis < todayTwoClock) {
            // 今日2点还未过去
            return todayTwoClock + randomDelay() - curTimeMillis;
        } else {
            return todayTwoClock + TimeUtils.DAY + randomDelay() - curTimeMillis;
        }
    }

    private static long nextWeekZeroDelay(long curTimeMillis) {
        final long timeBeginOfWeek = TimeHelper.SYSTEM.getTimeBeginOfWeek(curTimeMillis);
        return timeBeginOfWeek + TimeUtils.WEEK + randomDelay() - curTimeMillis;
    }

    private static long nextWeekFiveDelay(long curTimeMillis) {
        final long timeBeginOfWeek = TimeHelper.SYSTEM.getTimeBeginOfWeek(curTimeMillis) + 5 * TimeUtils.HOUR;
        if (curTimeMillis < timeBeginOfWeek) {
            //这次周一刷新还没过
            return timeBeginOfWeek + randomDelay() - curTimeMillis;
        } else {
            return timeBeginOfWeek + TimeUtils.WEEK + randomDelay() - curTimeMillis;
        }
    }

    private static long nextMouthZeroDelay(long curTimeMillis) {
        final long timeBeginOfMouth = TimeHelper.SYSTEM.getTimeBeginOfMonth(curTimeMillis);
        if (curTimeMillis < timeBeginOfMouth) {
            return timeBeginOfMouth + randomDelay() - curTimeMillis;
        } else {
            final long timeEndOfMouth = TimeHelper.SYSTEM.getTimeEndOfMonth(curTimeMillis);
            return timeEndOfMouth + randomDelay() - curTimeMillis;
        }
    }

    public void tick() {
        timerSystem.tick();
    }

    private void dailyResetZero() {
        timerSystem.scheduleRun(nextZeroDelay(TimeUtil.currentTimeMillis()), this::dailyResetZero);
        invokeGameHandlers(gameDailyZeroHandlerList);
//        PlayerMrg.getInstance().forEachPlayer(this::checkPlayer); 不走此方法
    }

    private void dailyResetTwo() {
        timerSystem.scheduleRun(nextTwoDelay(TimeUtil.currentTimeMillis()), this::dailyResetTwo);
        PlayerMrg.getInstance().forEachPlayer(this::checkPlayer);
    }

    private void weeklyResetZero() {
        timerSystem.scheduleRun(nextWeekZeroDelay(TimeUtil.currentTimeMillis()), this::weeklyResetZero);
        invokeGameHandlers(gameWeeklyZeroHandlerList);
//        PlayerMrg.getInstance().forEachPlayer(this::checkPlayer); 不走此方法
    }

    private void weeklyResetFive() {
        timerSystem.scheduleRun(nextWeekFiveDelay(TimeUtil.currentTimeMillis()), this::weeklyResetFive);
        PlayerMrg.getInstance().forEachPlayer(this::checkPlayer);
    }

    private void mouthResetZero() {
        timerSystem.scheduleRun(nextMouthZeroDelay(TimeUtil.currentTimeMillis()), this::mouthResetZero);
//        PlayerMrg.getInstance().forEachPlayer(this::checkPlayer); 不走此方法
    }

    private void checkPlayer(Player player) {
        final long curTimeMillis = TimeUtil.currentTimeMillis();
        final long lastRefreshTime = player.getLastRefreshTime();
        if (curTimeMillis > lastRefreshTime) {
            // 先更新时间戳，避免各种异常情况
            player.setLastRefreshTime(curTimeMillis);
            EntityDaoMrg.getInstance().getDao(PlayerDao.class)
                    .updatePlayerField(player.getPlayerId(), PlayerFields.lastRefreshTime, curTimeMillis);
        }

        long nextZeroTime = TimeUtil.getTimeBeginOfToday(lastRefreshTime, player.getTimeZone()) + TimeUtils.DAY;
        if (nextZeroTime < curTimeMillis) {
            // 0点事件
            invokeHandlers(player, dailyZeroHandlerList);
        }

        long nextTwoTime = TimeUtil.getTimeBeginOfToday(lastRefreshTime, player.getTimeZone()) + 2 * TimeUtils.HOUR;
        if (lastRefreshTime > nextTwoTime) {
            //因为上次就是在今天大于2点的时间更新的所以
            nextTwoTime += TimeUtils.DAY;
        }
        if (nextTwoTime < curTimeMillis) {
            // 2点事件
            invokeHandlers(player, dailyTwoHandlerList);
        }

        // 理论上本周的开始时间一定小于当前时间，但保险起见，仍然进行判定，避免依赖特定的时间库
        // 有些库的一周的起始是反人类的周日....
        final long nextWeekZero = TimeUtil.getTimeBeginOfWeek(lastRefreshTime, player.getTimeZone()) + TimeUtils.WEEK;
        if (nextWeekZero < curTimeMillis) {
            // 周一事件
            invokeHandlers(player, weeklyZeroHandlerList);
        }

        //周一5点事件
        long nextWeekFive = TimeUtil.getTimeBeginOfWeek(lastRefreshTime, player.getTimeZone()) + 5 * TimeUtils.HOUR;
        if (lastRefreshTime > nextWeekFive) {
            nextWeekFive += TimeUtils.WEEK;
        }
        if (nextWeekFive < curTimeMillis) {
            invokeHandlers(player, weeklyFiveHandlerList);
        }

        //每月1号
        final long nextMonth = TimeUtil.getTimeBeginOfMonth(curTimeMillis, player.getTimeZone());
        if (lastRefreshTime < nextMonth && nextMonth < curTimeMillis) {
            invokeHandlers(player, mouthZeroHandlerList);
        }
    }

    /**
     * 登录
     */
    public void checkDailyReset(Player player) {
        if (player.getLastRefreshTime() == 0) { // 兼容老号
            final long curTime = TimeUtil.currentTimeMillis();
            player.setLastRefreshTime(curTime);
            EntityDaoMrg.getInstance().getDao(PlayerDao.class)
                    .updatePlayerField(player.getPlayerId(), PlayerFields.lastRefreshTime, curTime);
            return;
        }
        checkPlayer(player);
    }

    private void invokeHandlers(Player player, List<IPlayerResetHandler> handlerList) {
        for (IPlayerResetHandler handler : handlerList) {
            try {
                handler.reset(player);
            } catch (Exception e) {
                logger.warn("reset caught exception, playerGuid: {}, playerName: {}", player.getPlayerId(), player.getPlayerName(), e);
            }
        }
    }

    private void invokeGameHandlers(List<IResetHandler> handlerList) {
        for (IResetHandler handler : handlerList) {
            try {
                handler.reset();
            } catch (Exception e) {
                logger.warn("reset caught exception", e);
            }
        }
    }

    public void shutdownNow() {
        timerSystem.shutdownNow();
    }

    public void scheduleRun(long timeout, Runnable task) {
        timerSystem.scheduleRun(timeout, task);
    }
}
