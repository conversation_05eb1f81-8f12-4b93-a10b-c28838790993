package com.game.hall.mrg;

import com.game.c_entity.merchant.C_BaseServerConfig;
import com.game.c_entity.middleplatform.C_BaseExchangeRate;
import com.game.c_entity.middleplatform.C_BasePaymentMethod;
import com.game.c_entity.middleplatform.*;
import com.game.engine.io.conf.NettyServerConfig;
import com.game.engine.mongo.DBConnectionMrg;
import com.game.engine.utils.Config;
import com.game.hall.main.HallServer;
import com.mongodb.client.MongoClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

public class DataHallMrg {
    private static final Logger LOGGER = LoggerFactory.getLogger(DataHallMrg.class);

    private static final DataHallMrg instance = new DataHallMrg();

    public static DataHallMrg getInstance() {
        return instance;
    }

    static MongoTemplate mongoTemplate;

    //中台
    private final Map<Integer, C_BaseCurrency> c_baseCurrencyMap = new ConcurrentHashMap<>(8);
    private final Map<Integer, C_BasePaymentMethod> c_basePaymentMethodMap = new ConcurrentHashMap<>(8);
    private final Map<Integer, C_BaseExchangeRate> c_baseExchangeRateMap = new ConcurrentHashMap<>(8);
    private final Map<Integer, C_BaseInbox> c_baseInboxMap = new ConcurrentHashMap<>(8);
    private final Map<Integer, C_BaseGameType> c_gameTypeMap = new ConcurrentHashMap<>(8);
    private final Map<Integer, C_BaseLanguage> c_languageMap = new ConcurrentHashMap<>(8);
    private final Map<Integer, C_BaseHead> c_headMap = new ConcurrentHashMap<>(8);
    private final Map<Integer, C_BaseMaintainNotice> c_maintainNoticeMap = new ConcurrentHashMap<>(8);
    private final Map<String, C_BaseServerConfig> c_baseServerConfigMap = new ConcurrentHashMap<>(8);
    private final Map<Integer, Set<String>> c_serverIdBusiness_noMap = new ConcurrentHashMap<>(8);

    //商户数据
    private final Map<String, MerchantData> merchantDataMap = new ConcurrentHashMap<>(8);
    private final Map<String, C_BaseMerchant> c_baseHostMerchantMap = new ConcurrentHashMap<>(8);

    public void initMongoConfig() {
        final NettyServerConfig nettyServerConfig = HallServer.getInstance().getNettyServerConfig();
        final String gameData = nettyServerConfig.getMongo_config_database();
        final MongoClient mongoClient = DBConnectionMrg.getInstance().getMongoClient();
        mongoTemplate = new MongoTemplate(mongoClient, gameData);
    }

    public void loadConfigData() throws Exception {
        //中台
        loadBaseCurrency();
        loadBasePaymentMethod();
        loadBaseExchangeRate();
        loadBaseInbox();
        loadBaseGameType();
        loadBaseLanguage();
        loadHead();
        loadBaseMaintainNotice();
        loadBaseServerConfig();

        loadAllMerchant();
    }

    private Query queryBusiness_no(String business_no) {
        return new Query(Criteria.where("business_no").is(business_no));
    }

    public void loadBaseCurrency() throws Exception {
        //货币
        c_baseCurrencyMap.clear();
        List<C_BaseCurrency> c_baseCurrencies = mongoTemplate.findAll(C_BaseCurrency.class);
        for (C_BaseCurrency c_baseCurrency : c_baseCurrencies) {
            c_baseCurrency.check();
            c_baseCurrencyMap.put(c_baseCurrency.getCurrencyId(), c_baseCurrency);
        }
    }

    public void loadBasePaymentMethod() throws Exception {
        //支付方式
        c_basePaymentMethodMap.clear();
        List<C_BasePaymentMethod> c_basePaymentMethods = DataHallMrg.mongoTemplate.findAll(C_BasePaymentMethod.class);
        for (C_BasePaymentMethod c_basePaymentMethod : c_basePaymentMethods) {
            c_basePaymentMethod.check();
            c_basePaymentMethodMap.put(c_basePaymentMethod.getPayId(), c_basePaymentMethod);
        }
    }

    public void loadBaseExchangeRate() throws Exception {
        //实时汇率
        c_baseExchangeRateMap.clear();
        List<C_BaseExchangeRate> c_Base_exchangeRates = DataHallMrg.mongoTemplate.findAll(C_BaseExchangeRate.class);
        for (C_BaseExchangeRate c_Base_exchangeRate : c_Base_exchangeRates) {
            c_Base_exchangeRate.check();
            c_baseExchangeRateMap.put(c_Base_exchangeRate.getCurrencyId(), c_Base_exchangeRate);
        }
    }

    public void loadBaseInbox() throws Exception {
        final Query query = new Query();
        query.addCriteria(Criteria.where("status").is(true));
        //邮件
        c_baseInboxMap.clear();
        List<C_BaseInbox> c_baseInboxes = DataHallMrg.mongoTemplate.find(query, C_BaseInbox.class);
        for (C_BaseInbox c_baseInbox : c_baseInboxes) {
            c_baseInbox.check();
            c_baseInboxMap.put(c_baseInbox.getInboxId(), c_baseInbox);
        }
    }

    public void loadBaseGameType() throws Exception {
        //游戏类型
        c_gameTypeMap.clear();
        List<C_BaseGameType> c_Base_gameTypes = DataHallMrg.mongoTemplate.findAll(C_BaseGameType.class);
        for (C_BaseGameType c_Base_gameType : c_Base_gameTypes) {
            c_Base_gameType.check();
            c_gameTypeMap.put(c_Base_gameType.getGameType(), c_Base_gameType);
        }
    }

    public void loadBaseLanguage() throws Exception {
        //语言
        c_languageMap.clear();
        List<C_BaseLanguage> c_baseLanguages = mongoTemplate.findAll(C_BaseLanguage.class);
        for (C_BaseLanguage c_baseLanguage : c_baseLanguages) {
            c_baseLanguage.check();
            c_languageMap.put(c_baseLanguage.getLanguageId(), c_baseLanguage);
        }
    }

    public void loadHead() throws Exception {
        //头像
        c_headMap.clear();
        List<C_BaseHead> c_heads = DataHallMrg.mongoTemplate.findAll(C_BaseHead.class);
        for (C_BaseHead c_head : c_heads) {
            c_head.check();
            c_headMap.put(c_head.getHeadId(), c_head);
        }
    }

    public void loadBaseMaintainNotice() throws Exception {
        //维护公告
        c_maintainNoticeMap.clear();
        final Query query = new Query();
        query.addCriteria(Criteria.where("status").is(true));
        List<C_BaseMaintainNotice> c_baseMaintainNotices = DataHallMrg.mongoTemplate.find(query, C_BaseMaintainNotice.class);
        for (C_BaseMaintainNotice c_baseMaintainNotice : c_baseMaintainNotices) {
            c_baseMaintainNotice.check();
            c_maintainNoticeMap.put(c_baseMaintainNotice.getC_id(), c_baseMaintainNotice);
        }
    }

    public void loadBaseServerConfig() throws Exception {
        //服务器配置
        c_baseServerConfigMap.clear();
        c_serverIdBusiness_noMap.clear();
        final List<C_BaseServerConfig> c_baseServerConfigs = DataHallMrg.mongoTemplate.findAll(C_BaseServerConfig.class);
        for (C_BaseServerConfig c_baseServerConfig : c_baseServerConfigs) {
            if (c_baseServerConfig.getServerId().contains(Config.SERVER_ID)) {
                c_baseServerConfig.check();
                c_baseServerConfigMap.put(c_baseServerConfig.getBusiness_no(), c_baseServerConfig);
                Set<String> business_nos = c_serverIdBusiness_noMap.putIfAbsent(Config.SERVER_ID, new HashSet<>());
                if (business_nos == null) {
                    business_nos = c_serverIdBusiness_noMap.get(Config.SERVER_ID);
                }
                business_nos.add(c_baseServerConfig.getBusiness_no());
            }
        }
    }

    private void loadAllMerchant() throws Exception {
        final Query query = new Query();
        query.addCriteria(Criteria.where("status").is(1)
                .and("deleteTime").is(0));
        //商户
        c_baseHostMerchantMap.clear();
        merchantDataMap.clear();

        final Set<String> business_nos = c_serverIdBusiness_noMap.getOrDefault(Config.SERVER_ID, new HashSet<>());

        List<C_BaseMerchant> c_baseMerchants = mongoTemplate.find(query, C_BaseMerchant.class);
        for (C_BaseMerchant c_baseMerchant : c_baseMerchants) {
            if (!business_nos.contains(c_baseMerchant.getBusiness_no())) {
                continue;
            }
            c_baseMerchant.check();
            for (String domain : c_baseMerchant.getMerchantDomain()) {
                c_baseHostMerchantMap.put(domain, c_baseMerchant);
            }

            MerchantData merchantData = merchantDataMap.putIfAbsent(c_baseMerchant.getBusiness_no(), new MerchantData());
            if (merchantData == null) {
                merchantData = merchantDataMap.get(c_baseMerchant.getBusiness_no());
                merchantDataMap.put(c_baseMerchant.getBusiness_no(), merchantData);
            }
            merchantData.loadData(c_baseMerchant.getBusiness_no());
        }
    }

    public void loadMerchant(String business_no) throws Exception {
        //商户
        final C_BaseMerchant c_baseMerchant = mongoTemplate.findOne(queryBusiness_no(business_no), C_BaseMerchant.class);
        if (c_baseMerchant == null) {
            LOGGER.warn("merchant ：{}，not exist", business_no);
            return;
        }

        c_baseMerchant.check();

        if (c_baseMerchant.getDeleteTime() > 0 || c_baseMerchant.getStatus() == 0) {
            merchantDataMap.remove(c_baseMerchant.getBusiness_no());
            for (String domain : c_baseMerchant.getMerchantDomain()) {
                c_baseHostMerchantMap.remove(domain);
            }
            return;
        }

        for (String domain : c_baseMerchant.getMerchantDomain()) {
            c_baseHostMerchantMap.put(domain, c_baseMerchant);
        }

        MerchantData merchantData = merchantDataMap.putIfAbsent(c_baseMerchant.getBusiness_no(), new MerchantData());
        if (merchantData == null) {
            merchantData = merchantDataMap.get(c_baseMerchant.getBusiness_no());
            merchantDataMap.put(c_baseMerchant.getBusiness_no(), merchantData);
            merchantData.loadData(c_baseMerchant.getBusiness_no());
            LOGGER.info("business_no：{}，add success", c_baseMerchant.getBusiness_no());
        }
    }

    public C_BaseMerchant findC_BaseHostMerchant(String className, String host) {
        final C_BaseMerchant c_baseMerchant = c_baseHostMerchantMap.get(host);
        if (c_baseMerchant == null) {
            LOGGER.warn("className：{}，C_BaseMerchant，host：{}，not exits", className, host);
            return null;
        }
        return c_baseMerchant;
    }

    public MerchantData findMerchantData(String className, String business_no) {
        final MerchantData merchantData = merchantDataMap.get(business_no);
        if (merchantData == null) {
            LOGGER.warn("className：{}，MerchantData，business_no：{}，not exits", className, business_no);
            return null;
        }
        return merchantData;
    }

    public C_BasePaymentMethod findC_BasePaymentMethod(String className, int payId) {
        final C_BasePaymentMethod c_Base_paymentMethod = c_basePaymentMethodMap.get(payId);
        if (c_Base_paymentMethod == null) {
            LOGGER.warn("className：{}，config，C_PaymentMethod，payId：{}，not exits", className, payId);
            return null;
        }
        return c_Base_paymentMethod;
    }

    public C_BaseExchangeRate findC_BaseExchangeRate(String className, int currencyId) {
        final C_BaseExchangeRate c_Base_exchangeRate = c_baseExchangeRateMap.get(currencyId);
        if (c_Base_exchangeRate == null) {
            LOGGER.warn("className：{}，config，C_BaseExchangeRate，currencyId：{}，not exits", className, currencyId);
            return null;
        }
        return c_Base_exchangeRate;
    }

    public C_BaseInbox findC_BaseInbox(String className, int inboxId) {
        final C_BaseInbox c_baseInbox = c_baseInboxMap.get(inboxId);
        if (c_baseInbox == null) {
            // LOGGER.warn("className：{}，config，C_BaseInbox，inboxId：{}，not exits", className, inboxId);
            return null;
        }
        return c_baseInbox;
    }

    public C_BaseGameType findC_GameType(String className, int gameType) {
        final C_BaseGameType c_Base_gameType = c_gameTypeMap.get(gameType);
        if (c_Base_gameType == null) {
            LOGGER.warn("className：{}，config，C_BaseGameType，gameType：{}，not exits", className, gameType);
            return null;
        }
        return c_Base_gameType;
    }

    public C_BaseLanguage findC_Language(String className, int languageId) {
        final C_BaseLanguage c_baseLanguage = c_languageMap.get(languageId);
        if (c_baseLanguage == null) {
            LOGGER.warn("className：{}，config，C_BaseLanguage，languageId：{}，not exits", className, languageId);
            return null;
        }
        return c_baseLanguage;
    }

    public C_BaseCurrency findC_BaseCurrency(String className, int currencyId) {
        final C_BaseCurrency c_baseCurrency = c_baseCurrencyMap.get(currencyId);
        if (c_baseCurrency == null) {
            LOGGER.warn("className：{}，C_BaseCurrency，currencyId：{}，not exits", className, currencyId);
            return null;
        }
        return c_baseCurrency;
    }

    public C_BaseServerConfig findC_BaseServerConfig(String className, String business_no) {
        final C_BaseServerConfig c_serverConfig = c_baseServerConfigMap.get(business_no);
        if (c_serverConfig == null) {
            LOGGER.warn("className：{}，C_BaseServerConfig，business_no：{}，not exist", className, business_no);
            return null;
        }
        return c_serverConfig;
    }

    public Map<Integer, C_BaseCurrency> getC_baseCurrencyMap() {
        return c_baseCurrencyMap;
    }

    public Map<Integer, C_BasePaymentMethod> getC_basePaymentMethodMap() {
        return c_basePaymentMethodMap;
    }

    public Map<Integer, C_BaseExchangeRate> getC_baseExchangeRateMap() {
        return c_baseExchangeRateMap;
    }

    public Map<Integer, C_BaseInbox> getC_baseInboxMap() {
        return c_baseInboxMap;
    }

    public Map<Integer, C_BaseGameType> getC_gameTypeMap() {
        return c_gameTypeMap;
    }

    public Map<Integer, C_BaseLanguage> getC_languageMap() {
        return c_languageMap;
    }

    public Map<Integer, C_BaseHead> getC_headMap() {
        return c_headMap;
    }

    public Map<Integer, C_BaseMaintainNotice> getC_maintainNoticeMap() {
        return c_maintainNoticeMap;
    }

    public Map<String, MerchantData> getMerchantDataMap() {
        return merchantDataMap;
    }

    public Map<String, C_BaseMerchant> getC_baseHostMerchantMap() {
        return c_baseHostMerchantMap;
    }

    public Map<String, C_BaseServerConfig> getC_baseServerConfigMap() {
        return c_baseServerConfigMap;
    }

    public Map<Integer, Set<String>> getC_serverIdBusiness_noMap() {
        return c_serverIdBusiness_noMap;
    }
}
