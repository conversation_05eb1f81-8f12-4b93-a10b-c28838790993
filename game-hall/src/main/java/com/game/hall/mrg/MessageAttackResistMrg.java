package com.game.hall.mrg;

import com.game.engine.io.conf.ConstantConfig;
import com.game.engine.utils.MsgUtil;
import com.game.engine.utils.TimeUtil;
import com.game.hall.server.handler.UserWebSocketServerHandler;
import com.game.user.ResistUserMsg;
import com.game.user.UserSession;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class MessageAttackResistMrg {
    private static final Logger LOGGER = LoggerFactory.getLogger(MessageAttackResistMrg.class);

    private static final MessageAttackResistMrg instance = new MessageAttackResistMrg();

    private static final long DURATION = TimeUtil.SEC;

    public static MessageAttackResistMrg getInstance() {
        return instance;
    }

    public void onResistUserMsg(UserSession userSession) {
        final ResistUserMsg resistUserMsg = userSession.getUserSession().attr(UserWebSocketServerHandler.CHANNEL_USER_MESSAGE_KEY).get();
        resistUserMsg.incSendTimes();
        final long curTime = TimeUtil.currentTimeMillis();
        if (curTime < resistUserMsg.getStartTime() + DURATION) {
            return;
        }
//        LOGGER.warn("发送消息个数：{}", resistUserMsg.getSendTimes());
        if (resistUserMsg.getSendTimes() >= ConstantConfig.getInstance().getMaxMsgSendTimes()) {
            long accountId = userSession.getAccountId();
            if (accountId == 0) {
                accountId = userSession.getSessionID();
            }
            LOGGER.warn("用户id：{}，非法发送恶意消息，ip：{}", accountId, MsgUtil.getIp(userSession.getUserSession()));
            userSession.destroy(true);
            return;
        }
        resistUserMsg.reset();
    }

}