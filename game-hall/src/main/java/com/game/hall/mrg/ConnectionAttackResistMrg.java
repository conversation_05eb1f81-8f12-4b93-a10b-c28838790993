package com.game.hall.mrg;

import com.game.engine.utils.TimeUtil;
import io.netty.channel.Channel;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.HashMap;
import java.util.Iterator;
import java.util.Map;

/**
 * 连接池管理器
 */
public class ConnectionAttackResistMrg {

    private static final ConnectionAttackResistMrg instance = new ConnectionAttackResistMrg();
    private static final Logger logger = LoggerFactory.getLogger(ConnectionAttackResistMrg.class);
    private final HashMap<String, Session> channelIdAndChannelSession = new HashMap<>();

    public static ConnectionAttackResistMrg getInstance() {
        return instance;
    }

    public void checkConnectionAttack() {
        try {
            if (channelIdAndChannelSession.size() > 10000) {
                logger.error("ConnectionAttackResistMrg.checkConnectionAttack connection num is too much: {}", channelIdAndChannelSession.size());
            }
            for (Iterator<Map.Entry<String, Session>> iter = channelIdAndChannelSession.entrySet().iterator(); iter.hasNext(); ) {
                Map.Entry<String, Session> pair = iter.next();
                Session session = pair.getValue();
                if (TimeUtil.currentTimeMillis() > session.getNeedCloseTime()) {
                    iter.remove();
                    closeChannel(session.getChannel());
                }
            }
        } catch (Exception e) {
            logger.error("checkConnectionAttack", e);
        }
    }

    private void closeChannel(Channel channel) {
        try {
            if (channel != null) {
                channel.close();
            }
        } catch (Exception e) {
            logger.error("", e);
        }
    }

    public void onConnect(Channel channel) {
        String channelId = channel.id().asLongText();
        if (channelIdAndChannelSession.containsKey(channelId)) {
            logger.error("ConnectionAttackResistMrg.onConnect channelIdAndChannelSession.containsKey(channelId)");
            return;
        }
        channelIdAndChannelSession.put(channelId, new Session(channel, TimeUtil.currentTimeMillis() + 15 * TimeUtil.SEC));
    }

    public void removeChannelResist(Channel channel) {
        String channelId = channel.id().asLongText();
//        if (!channelIdAndChannelSession.containsKey(channelId)) {
//            //logger.error("ConnectionAttackResistMrg.removeChannelResist !channelIdAndChannelSession.containsKey(channelId)");
//            return;
//        }
        channelIdAndChannelSession.remove(channelId);
    }

    private static class Session {
        private final Channel channel;

        private final long needCloseTime;

        public Session(Channel channel, long needCloseTime) {
            super();
            this.channel = channel;
            this.needCloseTime = needCloseTime;
        }

        public Channel getChannel() {
            return channel;
        }

        public long getNeedCloseTime() {
            return needCloseTime;
        }


    }

}
