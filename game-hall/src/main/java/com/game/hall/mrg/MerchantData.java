package com.game.hall.mrg;

import com.game.c_entity.merchant.*;
import com.game.c_entity.merchant.C_Global;
import com.game.engine.utils.TimeUtil;
import it.unimi.dsi.fastutil.ints.IntArrayList;
import it.unimi.dsi.fastutil.ints.IntList;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

public class MerchantData {
    private static final Logger LOGGER = LoggerFactory.getLogger(MerchantData.class);

    private String business_no;

    //平台索引
    private final Map<Integer, C_GamePlatform> c_platformIdGameType_platformMap = new ConcurrentHashMap<>(8);
    private final Map<Integer, C_GamePlatform> c_gamePlatformMap = new ConcurrentHashMap<>(8);

    //游戏存索引字段
    private final Map<Integer, C_GameApi> c_gameApiMap = new ConcurrentHashMap<>(8);
    //platformId+gameType
    private final Map<Integer, List<C_GameApi>> c_platformIdGameType_gameApiMap = new ConcurrentHashMap<>(8);
    private final Map<Integer, List<C_GameApi>> c_platformId_gameApiMap = new ConcurrentHashMap<>(8);

    //频道
    private final Map<Integer, List<C_GameChannel>> c_gameChannelListMap = new ConcurrentHashMap<>(8);
    private final Map<Integer, C_GameChannel> c_gameChannelMap = new ConcurrentHashMap<>(8);
    private final Map<Integer, C_GameChannel> c_gameSubChannelMap = new ConcurrentHashMap<>(8);
    //频道游戏
    private final Map<Integer, List<C_SubChannelGameApi>> c_subChannelGameApi = new ConcurrentHashMap<>(8);

    private final Map<String, String> c_globalMap = new ConcurrentHashMap<>(8);
    private final Map<String, List<C_Banner>> c_bannerMap = new ConcurrentHashMap<>(8);
    private final Map<String, List<C_CollectionWallet>> c_collectionWalletMap = new ConcurrentHashMap<>(8);
    private final Map<Integer, C_Currency> c_currencyMap = new ConcurrentHashMap<>(8);

    //充值
    private final Map<Integer, Map<String, C_PlatformRecharge>> c_platformRecharge_fiatMap = new ConcurrentHashMap<>(8);
    private final Map<Integer, List<C_PlatformRecharge>> c_platformRecharge_cryptoMap = new ConcurrentHashMap<>(8);
    private final Map<Integer, Map<String, C_PlatformWithdraw>> c_platformWithdraw_fiatMap = new ConcurrentHashMap<>(8);
    private final Map<Integer, List<C_PlatformWithdraw>> c_platformWithdraw_cryptoMap = new ConcurrentHashMap<>(8);

    private final Map<Integer, List<C_News>> c_newsMap = new ConcurrentHashMap<>(8);
    private final Map<Integer, List<C_Popup>> c_popupMap = new ConcurrentHashMap<>(8);
    private final Map<Integer, C_PubMail> c_pubMailMap = new ConcurrentHashMap<>(8);
    private final Map<Integer, C_PubMail> c_bulletinMap = new ConcurrentHashMap<>(8);
    private final Map<Integer, List<C_PubMail>> c_eventPubMailMap = new ConcurrentHashMap<>(8);
    private final Map<Integer, List<C_PubMail>> c_timingPubMailMap = new ConcurrentHashMap<>(8);
    private final Map<Integer, C_PubMail> c_timelyPubMailMap = new ConcurrentHashMap<>(8);
    private final Map<Integer, List<C_HelpCenter>> c_helpCenterMap = new ConcurrentHashMap<>(8);
    private final Map<Integer, C_RechargeTurnover> c_rechargeTurnoverMap = new ConcurrentHashMap<>(8);
    private final Map<Integer, C_RechargeWithdrawLimit> c_rechargeWithdrawLimitMap = new ConcurrentHashMap<>(8);
    private final Map<String, C_Region> c_regionMap = new ConcurrentHashMap<>(8);
    private final Map<Integer, C_VipClub> c_vipClubMap = new ConcurrentHashMap<>(8);
    private final Map<Integer, C_ReferralReward> c_referralRewardMap = new ConcurrentHashMap<>(8);
    private final Map<Integer, C_CashBack> c_cashBackMap = new ConcurrentHashMap<>(8);
    private final Map<Integer, C_GameTurnover> c_gameTurnoverMap = new ConcurrentHashMap<>(8);
    private final Map<Integer, C_CustomerService> c_customerServiceMap = new ConcurrentHashMap<>(8);
    private final Map<Integer, C_BottomMenu> c_bottomMenuMap = new ConcurrentHashMap<>(8);
    private final Map<Integer, C_QualityAssurance> c_qualityAssuranceMap = new ConcurrentHashMap<>(8);
    private final Map<Integer, List<C_InvitationPoster>> c_invitationPosterMap = new ConcurrentHashMap<>(8);
    private final Map<Integer, C_FreeGameTurnover> c_freeGameTurnoverMap = new ConcurrentHashMap<>(8);
    private final Map<Integer, C_InvitationLinks> c_invitationLinksMap = new ConcurrentHashMap<>(8);
    private final Map<String, List<C_ThreePartyLogin>> c_threePartyLoginMap = new ConcurrentHashMap<>(8);
    private final Map<Integer, C_FunctionEnabled> c_functionEnabledMap = new ConcurrentHashMap<>(8);
    private final Map<String, C_WebSiteInfo> c_webSiteInfoMap = new ConcurrentHashMap<>(8);
    private final Map<String, List<C_QuickAccess>> c_quickAccessMap = new ConcurrentHashMap<>(8);
    private final Map<Integer, C_MaintainNotice> c_maintainNoticeMap = new ConcurrentHashMap<>(8);
    private final Map<Integer, C_BigWin> c_bigWinMap = new ConcurrentHashMap<>(8);
    private final Map<Integer, C_RegisterRetrievePop> c_registerRetrieveMap = new ConcurrentHashMap<>(8);
    private final Map<Integer, C_DailyRechargePop> c_dailyRechargePopMap = new ConcurrentHashMap<>(8);
    private final Map<Integer, C_FirstChargePop> c_firstChargePopMap = new ConcurrentHashMap<>(8);
    private final Map<Integer, C_GamePop> c_gamePopMap = new ConcurrentHashMap<>(8);

    //活动
    private final Map<Integer, List<C_Activity>> c_activityListMap = new ConcurrentHashMap<>();
    private final Map<Integer, C_Activity> c_activityMap = new ConcurrentHashMap<>();
    private final Map<Integer, C_ActivityTag> c_activityTagMap = new ConcurrentHashMap<>();
    private final Map<Integer, List<C_LuckSpin>> c_luckSpinMap = new ConcurrentHashMap<>(8);
    private final Map<Integer, C_DailyContest> c_dailyContestMap = new ConcurrentHashMap<>(8);
    private final Map<Integer, C_WeeklyRaffle> c_weeklyRaffleMap = new ConcurrentHashMap<>(8);
    private final Map<String, C_WinTicketNumbers> c_winTicketNumbersMap = new ConcurrentHashMap<>(8);
    private final Map<String, List<C_WinTicketNumbers>> c_winTicketNumbersListMap = new ConcurrentHashMap<>(8);
    private final Map<String, C_RedemptionCode> c_redemptionCodeMap = new ConcurrentHashMap<>(8);
    private final Map<Integer, C_RedEnvelopeRain> c_redEnvelopeRainDayMap = new ConcurrentHashMap<>(8);
    private final Map<Integer, C_RedEnvelopeRain> c_redEnvelopeRainWeekMap = new ConcurrentHashMap<>(8);
    private final Map<Integer, C_RewardBox> c_rewardBoxMap = new ConcurrentHashMap<>(8);
    private final Map<Integer, C_MysteryBonus> c_mysteryBonusMap = new ConcurrentHashMap<>(8);
    private final Map<Integer, C_PiggyBank> c_piggyBankMap = new ConcurrentHashMap<>(8);
    private final Map<Integer, C_ContinuousDeposit> c_continuousDepositMap = new ConcurrentHashMap<>(8);
    private final Map<Integer, C_Pwa> c_pwaMap = new ConcurrentHashMap<>(8);
    private final Map<Integer, C_FirstChargeSignIn> c_firstChargeSignInMap = new ConcurrentHashMap<>(8);
    private final Map<Integer, C_CrazyBox> c_crazyBoxMap = new ConcurrentHashMap<>(8);
    private final Map<Integer, C_CrazyBoxQuest> c_crazyBoxQuestMap = new ConcurrentHashMap<>(8);
    private final Map<Integer, C_RechargeRecover> c_rechargeRecoverMap = new ConcurrentHashMap<>(8);
    private final Map<Integer, C_WageredRebates> c_wageredRebatesMap = new ConcurrentHashMap<>(8);
    private final Map<Integer, C_FirstDepositInviteBonus> c_firstDepositInviteBonusMap = new ConcurrentHashMap<>(8);

    //任务
    private final Map<Integer, C_Quest> c_questMap = new ConcurrentHashMap<>(8);
    private final Map<Integer, IntList> c_type2QuestMap = new ConcurrentHashMap<>(8);

    private final Map<Integer, List<C_GameKillRate>> c_gameKillRateMap = new ConcurrentHashMap<>(8);

    private Query queryBusiness_no(String business_no) {
        final Criteria criteria = new Criteria();
        criteria.and("business_no").is(business_no);
        return new Query(criteria);
    }

    void loadData(String business_no) throws Exception {
        //后台
        this.business_no = business_no;
        loadGlobal(business_no);
        loadActivity(business_no);
        loadActivityTag(business_no);
        loadBanner(business_no);
        loadMaintainNotice(business_no);
        loadPopup(business_no);
        loadCollectionWallet(business_no);
        loadCurrency(business_no);
        loadGameApi(business_no);
        loadGameChannel(business_no);
        loadSubChannelGameApi(business_no);
        loadGamePlatform(business_no);
        loadNews(business_no);
        loadPlatformRecharge(business_no);
        loadPlatformWithdraw(business_no);
        loadPubMail(business_no);
        loadRechargeTurnover(business_no);
        loadRechargeWithdrawLimit(business_no);
        loadRegion(business_no);
        loadVipClub(business_no);
        loadHelpCenter(business_no);
        loadReferralReward(business_no);
        loadCashBack(business_no);
        loadLuckSpin(business_no);
        loadDailyContest(business_no);
        loadWeeklyRaffle(business_no);
        loadWinTicketNumbers(business_no);
        loadQuest(business_no);
        loadGameTurnover(business_no);
        loadCustomerService(business_no);
        loadBottomMenu(business_no);
        loadQualityAssurance(business_no);
        loadInvitationPoster(business_no);
        loadFreeGameTurnover(business_no);
        loadInvitationLinks(business_no);
        loadRedemptionCode(business_no);
        loadThreePartyLogin(business_no);
        loadFunctionEnabled(business_no);
        loadWebSiteInfo(business_no);
        loadQuickAccess(business_no);
        loadRedEnvelopeRain(business_no);
        loadRewardBox(business_no);
        loadMysteryBonus(business_no);
        loadPiggyBank(business_no);
        loadContinuousDeposit(business_no);
        loadFirstChargeSignIn(business_no);
        loadPwa(business_no);
        loadBigWin(business_no);
        loadRegisterRetrievePop(business_no);
        loadDailyRechargePop(business_no);
        loadFirstChargePop(business_no);
        loadGamePop(business_no);
        loadCrazyBox(business_no);
        loadCrazyBoxQuest(business_no);
        loadRechargeRecover(business_no);
        loadGameKillRate(business_no);
        loadWageredRebates(business_no);
        loadFirstDepositInviteBonus(business_no);
        LOGGER.info("business_no：{}，merchantData load success ...", business_no);
    }

    public void loadGlobal(String business_no) throws Exception {
        //全局
        c_globalMap.clear();
        List<C_Global> c_globals = DataHallMrg.mongoTemplate.find(queryBusiness_no(business_no), C_Global.class);
        for (C_Global c_global : c_globals) {
            c_global.check();
            c_globalMap.put(c_global.getName(), c_global.getValue());
        }
    }

    public void loadActivity(String business_no) throws Exception {
        //活动
        c_activityListMap.clear();
        c_activityMap.clear();
        final Query query = queryBusiness_no(business_no);
        query.addCriteria(Criteria.where("status").is(true)
                .and("deleteTime").is(0));
        List<C_Activity> c_activities = DataHallMrg.mongoTemplate.find(query, C_Activity.class);
        for (C_Activity c_activity : c_activities) {
            c_activity.check();
            c_activityMap.put(c_activity.getC_id(), c_activity);

            List<C_Activity> c_activityList = c_activityListMap.putIfAbsent(c_activity.getActivityId(), new ArrayList<>());
            if (c_activityList == null) {
                c_activityList = c_activityListMap.get(c_activity.getActivityId());
            }
            c_activityList.add(c_activity);
        }
    }

    public void loadActivityTag(String business_no) throws Exception {
        //活动标签
        c_activityTagMap.clear();
        List<C_ActivityTag> c_activityTags = DataHallMrg.mongoTemplate.find(queryBusiness_no(business_no), C_ActivityTag.class);
        for (C_ActivityTag c_activityTag : c_activityTags) {
            c_activityTag.check();
            c_activityTagMap.put(c_activityTag.getActivityTag(), c_activityTag);
        }
    }

    public void loadBanner(String business_no) throws Exception {
        //广告
        c_bannerMap.clear();
        final Query query = queryBusiness_no(business_no);
        query.addCriteria(Criteria.where("status").is(1)
                .and("endTime")
                .gt(TimeUtil.currentTimeMillis()));
        List<C_Banner> c_banners = DataHallMrg.mongoTemplate.find(query, C_Banner.class);
        for (C_Banner c_banner : c_banners) {
            c_banner.check();
            for (String webSite : c_banner.getWebSites()) {
                List<C_Banner> c_bannerList = c_bannerMap.putIfAbsent(webSite, new ArrayList<>());
                if (c_bannerList == null) {
                    c_bannerList = c_bannerMap.get(webSite);
                }
                c_bannerList.add(c_banner);
            }
        }
    }

    public void loadPopup(String business_no) throws Exception {
        //弹窗
        c_popupMap.clear();
        final Query query = queryBusiness_no(business_no);
        query.addCriteria(Criteria.where("status").is(true)
                .and("endTime")
                .gt(TimeUtil.currentTimeMillis()));
        List<C_Popup> c_popups = DataHallMrg.mongoTemplate.find(query, C_Popup.class);
        for (C_Popup c_popup : c_popups) {
            c_popup.check();
            List<C_Popup> c_popupList = c_popupMap.putIfAbsent(c_popup.getLanguage(), new ArrayList<>());
            if (c_popupList == null) {
                c_popupList = c_popupMap.get(c_popup.getLanguage());
            }
            c_popupList.add(c_popup);
        }
    }

    public void loadCollectionWallet(String business_no) throws Exception {
        //钱包地址
        c_collectionWalletMap.clear();
        final Query query = queryBusiness_no(business_no);
        List<C_CollectionWallet> c_collectionWallets = DataHallMrg.mongoTemplate.find(query, C_CollectionWallet.class);
        for (C_CollectionWallet c_collectionWallet : c_collectionWallets) {
            c_collectionWallet.check();
            final String id = c_collectionWallet.getCurrencyId() + "_" + c_collectionWallet.getNetwork();
            List<C_CollectionWallet> list = c_collectionWalletMap.putIfAbsent(id, new ArrayList<>());
            if (list == null) {
                list = c_collectionWalletMap.get(id);
            }
            list.add(c_collectionWallet);
        }
    }

    public void loadCurrency(String business_no) throws Exception {
        //货币
        c_currencyMap.clear();
        List<C_Currency> c_currencies = DataHallMrg.mongoTemplate.find(queryBusiness_no(business_no), C_Currency.class);
        for (C_Currency c_currency : c_currencies) {
            c_currency.check();
            c_currencyMap.put(c_currency.getCurrencyId(), c_currency);
        }
    }

    public void loadGameApi(String business_no) throws Exception {
        //游戏列表
        c_gameApiMap.clear();
        c_platformIdGameType_gameApiMap.clear();
        c_platformId_gameApiMap.clear();
        final Query query = queryBusiness_no(business_no);
        query.addCriteria(Criteria.where("status").is(1)
                .and("baseStatus").is(1)
                .and("deleteTime").is(0));
        List<C_GameApi> c_gameApis = DataHallMrg.mongoTemplate.find(query, C_GameApi.class);
        for (C_GameApi c_gameApi : c_gameApis) {
            c_gameApi.check();
            c_gameApiMap.put(c_gameApi.getGameId(), c_gameApi);

            //platformId+gameType
            final int id = Integer.parseInt(c_gameApi.getPlatformId() + "" + c_gameApi.getType());
            List<C_GameApi> platformIdGameType = c_platformIdGameType_gameApiMap.putIfAbsent(id, new ArrayList<>());
            if (platformIdGameType == null) {
                platformIdGameType = c_platformIdGameType_gameApiMap.get(id);
            }
            platformIdGameType.add(c_gameApi);

            //platformId
            List<C_GameApi> platformId = c_platformId_gameApiMap.putIfAbsent(c_gameApi.getPlatformId(), new ArrayList<>());
            if (platformId == null) {
                platformId = c_platformId_gameApiMap.get(c_gameApi.getPlatformId());
            }
            platformId.add(c_gameApi);
        }
    }

    public void loadGameChannel(String business_no) throws Exception {
        //游戏频道
        c_gameChannelListMap.clear();
        c_gameChannelMap.clear();
        c_gameSubChannelMap.clear();
        final Query query = queryBusiness_no(business_no);
        query.addCriteria(Criteria.where("open").is(true));
        List<C_GameChannel> c_gameChannels = DataHallMrg.mongoTemplate.find(query, C_GameChannel.class);
        for (C_GameChannel c_gameChannel : c_gameChannels) {
            c_gameChannel.check();
            List<C_GameChannel> c_gameChannelList = this.c_gameChannelListMap.putIfAbsent(c_gameChannel.getChannelType(), new ArrayList<>());
            if (c_gameChannelList == null) {
                c_gameChannelList = this.c_gameChannelListMap.get(c_gameChannel.getChannelType());
            }
            c_gameChannelList.add(c_gameChannel);

            this.c_gameChannelMap.put(c_gameChannel.getChannelType(), c_gameChannel);
            this.c_gameSubChannelMap.putIfAbsent(c_gameChannel.getSubChannel(), c_gameChannel);
        }
    }

    public void loadSubChannelGameApi(String business_no) throws Exception {
        //子频道游戏
        c_subChannelGameApi.clear();
        final Query query = queryBusiness_no(business_no);
        query.addCriteria(Criteria.where("status").is(true)
                .and("baseStatus").is(1));
        List<C_SubChannelGameApi> c_subChannelGameApis = DataHallMrg.mongoTemplate.find(query, C_SubChannelGameApi.class);
        for (C_SubChannelGameApi c_subChannelGameApi : c_subChannelGameApis) {
            c_subChannelGameApi.check();
            List<C_SubChannelGameApi> c_subChannelGameApiList = this.c_subChannelGameApi.putIfAbsent(Integer.parseInt(c_subChannelGameApi.getChannelType() + "" + c_subChannelGameApi.getSubChannel()), new ArrayList<>());
            if (c_subChannelGameApiList == null) {
                c_subChannelGameApiList = this.c_subChannelGameApi.get(Integer.parseInt(c_subChannelGameApi.getChannelType() + "" + c_subChannelGameApi.getSubChannel()));
            }
            c_subChannelGameApiList.add(c_subChannelGameApi);
        }
    }

    public void loadGamePlatform(String business_no) throws Exception {
        //游戏平台
        c_gamePlatformMap.clear();
        c_platformIdGameType_platformMap.clear();
        final Query query = queryBusiness_no(business_no);
        query.addCriteria(Criteria.where("status").is(1));
        List<C_GamePlatform> c_gamePlatforms = DataHallMrg.mongoTemplate.find(query, C_GamePlatform.class);
        for (C_GamePlatform c_gamePlatform : c_gamePlatforms) {
            c_gamePlatform.check();
            c_gamePlatformMap.put(c_gamePlatform.getPlatformId(), c_gamePlatform);
            c_platformIdGameType_platformMap.put(Integer.parseInt(c_gamePlatform.getPlatformId() + "" + c_gamePlatform.getType()), c_gamePlatform);
        }
    }

    public void loadNews(String business_no) throws Exception {
        //web新闻
        c_newsMap.clear();
        final Query query = queryBusiness_no(business_no);
        query.addCriteria(Criteria.where("status").is(1));
        List<C_News> c_news = DataHallMrg.mongoTemplate.find(query, C_News.class);
        for (C_News c_new : c_news) {
            c_new.check();
            List<C_News> c_newsList = c_newsMap.putIfAbsent(c_new.getLanguage(), new ArrayList<>());
            if (c_newsList == null) {
                c_newsList = c_newsMap.get(c_new.getLanguage());
            }
            c_newsList.add(c_new);
        }
    }

    public void loadPlatformRecharge(String business_no) throws Exception {
        //充值
        c_platformRecharge_fiatMap.clear();
        c_platformRecharge_cryptoMap.clear();
        final Query query = queryBusiness_no(business_no);
        query.addCriteria(Criteria.where("open").is(true));
        final List<C_PlatformRecharge> c_platformRecharges = DataHallMrg.mongoTemplate.find(query, C_PlatformRecharge.class);
        for (C_PlatformRecharge c_platformRecharge : c_platformRecharges) {
            c_platformRecharge.check();

            if (c_platformRecharge.getCurrencyId() / 1000 == 1) {
                Map<String, C_PlatformRecharge> rechargeFiatMap = c_platformRecharge_fiatMap.putIfAbsent(c_platformRecharge.getCurrencyId(), new ConcurrentHashMap<>());
                if (rechargeFiatMap == null) {
                    rechargeFiatMap = c_platformRecharge_fiatMap.get(c_platformRecharge.getCurrencyId());
                }
                rechargeFiatMap.put(c_platformRecharge.getChannel() + "_" + c_platformRecharge.getPaymentMethod(), c_platformRecharge);
                continue;
            }

            List<C_PlatformRecharge> rechargeCryptoList = c_platformRecharge_cryptoMap.putIfAbsent(c_platformRecharge.getCurrencyId(), new ArrayList<>());
            if (rechargeCryptoList == null) {
                rechargeCryptoList = c_platformRecharge_cryptoMap.get(c_platformRecharge.getCurrencyId());
            }
            rechargeCryptoList.add(c_platformRecharge);
        }
    }

    public void loadPlatformWithdraw(String business_no) throws Exception {
        //提现
        c_platformWithdraw_fiatMap.clear();
        c_platformWithdraw_cryptoMap.clear();
        final Query query = queryBusiness_no(business_no);
        query.addCriteria(Criteria.where("open").is(true));
        final List<C_PlatformWithdraw> c_platformWithdraws = DataHallMrg.mongoTemplate.find(query, C_PlatformWithdraw.class);
        for (C_PlatformWithdraw c_platformWithdraw : c_platformWithdraws) {
            c_platformWithdraw.check();
            if (c_platformWithdraw.getCurrencyId() / 1000 == 1) {
                Map<String, C_PlatformWithdraw> withdrawMap = c_platformWithdraw_fiatMap.putIfAbsent(c_platformWithdraw.getCurrencyId(), new ConcurrentHashMap<>());
                if (withdrawMap == null) {
                    withdrawMap = c_platformWithdraw_fiatMap.get(c_platformWithdraw.getCurrencyId());
                }
                withdrawMap.put(c_platformWithdraw.getChannel() + "_" + c_platformWithdraw.getPaymentMethod(), c_platformWithdraw);
                continue;
            }

            List<C_PlatformWithdraw> withdrawList = c_platformWithdraw_cryptoMap.putIfAbsent(c_platformWithdraw.getCurrencyId(), new ArrayList<>());
            if (withdrawList == null) {
                withdrawList = c_platformWithdraw_cryptoMap.get(c_platformWithdraw.getCurrencyId());
            }
            withdrawList.add(c_platformWithdraw);
        }
    }

    public void loadPubMail(String business_no) throws Exception {
        //邮件
        c_pubMailMap.clear();
        c_bulletinMap.clear();
        c_eventPubMailMap.clear();
        final Query query = queryBusiness_no(business_no);
        Criteria criteria = Criteria.where("status").is(1)
                .andOperator(
                        Criteria.where("endTime").gt(TimeUtil.currentTimeMillis()),
                        Criteria.where("deleteTime").is(0)
                );
        query.addCriteria(criteria);
        List<C_PubMail> c_pubMails = DataHallMrg.mongoTemplate.find(query, C_PubMail.class);
        for (C_PubMail c_pubMail : c_pubMails) {
            c_pubMail.check();
            if (c_pubMail.getMailType() == 3) {
                c_bulletinMap.put(c_pubMail.getMailId(), c_pubMail);
                continue;
            }
            {
                if (c_pubMail.getSendCondition() == 4) {//发送条件 定时
                    List<C_PubMail> c_pubMailList = c_timingPubMailMap.putIfAbsent(c_pubMail.getTimingType(), new ArrayList<>());
                    if (c_pubMailList == null) {
                        c_pubMailList = c_timingPubMailMap.get(c_pubMail.getTimingType());
                    }
                    c_pubMailList.add(c_pubMail);
                    continue;
                }
            }
            {
                if (c_pubMail.getSendEvent() > 0) {//发送事件
                    List<C_PubMail> c_pubMailList = c_eventPubMailMap.putIfAbsent(c_pubMail.getSendEvent(), new ArrayList<>());
                    if (c_pubMailList == null) {
                        c_pubMailList = c_eventPubMailMap.get(c_pubMail.getSendEvent());
                    }
                    c_pubMailList.add(c_pubMail);
                    continue;
                }
            }
            {
                if (c_pubMail.getSendCondition() == 3) {//立即发送
                    c_timelyPubMailMap.put(c_pubMail.getMailId(), c_pubMail);
                    continue;
                }
            }
            c_pubMailMap.put(c_pubMail.getMailId(), c_pubMail);
        }
    }

    public void loadRechargeTurnover(String business_no) throws Exception {
        //充值打码量
        c_rechargeTurnoverMap.clear();
        List<C_RechargeTurnover> c_rechargeTurnovers = DataHallMrg.mongoTemplate.find(queryBusiness_no(business_no), C_RechargeTurnover.class);
        for (C_RechargeTurnover c_rechargeTurnover : c_rechargeTurnovers) {
            c_rechargeTurnover.check();
            c_rechargeTurnoverMap.put(c_rechargeTurnover.getCurrencyId(), c_rechargeTurnover);
        }
    }

    public void loadRechargeWithdrawLimit(String business_no) throws Exception {
        //充提限制
        c_rechargeWithdrawLimitMap.clear();
        List<C_RechargeWithdrawLimit> c_rechargeWithdrawLimits = DataHallMrg.mongoTemplate.find(queryBusiness_no(business_no), C_RechargeWithdrawLimit.class);
        for (C_RechargeWithdrawLimit c_rechargeWithdrawLimit : c_rechargeWithdrawLimits) {
            c_rechargeWithdrawLimit.check();
            c_rechargeWithdrawLimitMap.put(c_rechargeWithdrawLimit.getCurrencyId(), c_rechargeWithdrawLimit);
        }
    }

    public void loadRegion(String business_no) throws Exception {
        //地区
        c_regionMap.clear();
        List<C_Region> c_regions = DataHallMrg.mongoTemplate.find(queryBusiness_no(business_no), C_Region.class);
        for (C_Region c_region : c_regions) {
            c_region.check();
            c_regionMap.put(c_region.getRegionId(), c_region);
        }
    }

    public void loadVipClub(String business_no) throws Exception {
        //vipClub
        c_vipClubMap.clear();
        List<C_VipClub> c_vipClubs = DataHallMrg.mongoTemplate.find(queryBusiness_no(business_no), C_VipClub.class);
        for (C_VipClub c_vipClub : c_vipClubs) {
            c_vipClub.check();
            c_vipClubMap.put(c_vipClub.getVipLevel(), c_vipClub);
        }
    }

    public void loadHelpCenter(String business_no) throws Exception {
        //帮助中心
        final Query query = queryBusiness_no(business_no);
        query.addCriteria(Criteria.where("status").is(1));
        c_helpCenterMap.clear();
        List<C_HelpCenter> c_helpCenters = DataHallMrg.mongoTemplate.find(query, C_HelpCenter.class);
        for (C_HelpCenter c_helpCenter : c_helpCenters) {
            c_helpCenter.check();
            List<C_HelpCenter> c_helpCenterList = c_helpCenterMap.putIfAbsent(c_helpCenter.getLanguage(), new ArrayList<>());
            if (c_helpCenterList == null) {
                c_helpCenterList = c_helpCenterMap.get(c_helpCenter.getLanguage());
            }
            c_helpCenterList.add(c_helpCenter);
        }
    }

    public void loadReferralReward(String business_no) throws Exception {
        //推荐奖励
        c_referralRewardMap.clear();
        List<C_ReferralReward> c_referralRewards = DataHallMrg.mongoTemplate.find(queryBusiness_no(business_no), C_ReferralReward.class);
        for (C_ReferralReward c_referralReward : c_referralRewards) {
            c_referralReward.check();
            c_referralRewardMap.put(c_referralReward.getVipLevel(), c_referralReward);
        }
    }

    public void loadCashBack(String business_no) throws Exception {
        //返水
        final Query query = queryBusiness_no(business_no);
        query.addCriteria(Criteria.where("open").is(true));
        c_cashBackMap.clear();
        List<C_CashBack> c_cashBacks = DataHallMrg.mongoTemplate.find(query, C_CashBack.class);
        for (C_CashBack c_cashBack : c_cashBacks) {
            c_cashBack.check();
            c_cashBackMap.put(c_cashBack.getGameType(), c_cashBack);
        }
    }

    public void loadLuckSpin(String business_no) throws Exception {
        //幸运转盘
        c_luckSpinMap.clear();
        final Query query = queryBusiness_no(business_no);
        query.addCriteria(Criteria.where("open").is(true)
                .and("endTime")
                .gt(TimeUtil.currentTimeMillis())
                .and("deleteTime").is(0));
        List<C_LuckSpin> c_luckSpins = DataHallMrg.mongoTemplate.find(query, C_LuckSpin.class);
        for (C_LuckSpin c_luckSpin : c_luckSpins) {
            c_luckSpin.check();
            List<C_LuckSpin> c_luckSpinList = c_luckSpinMap.putIfAbsent(c_luckSpin.getActivityId(), new ArrayList<>());
            if (c_luckSpinList == null) {
                c_luckSpinList = c_luckSpinMap.get(c_luckSpin.getActivityId());
            }
            c_luckSpinList.add(c_luckSpin);
        }
    }

    public void loadDailyContest(String business_no) throws Exception {
        //每日竞赛
        c_dailyContestMap.clear();
        List<C_DailyContest> c_dailyContests = DataHallMrg.mongoTemplate.find(queryBusiness_no(business_no), C_DailyContest.class);
        for (C_DailyContest c_dailyContest : c_dailyContests) {
            c_dailyContest.check();
            c_dailyContestMap.put(c_dailyContest.getActivityId(), c_dailyContest);
        }
    }

    public void loadWeeklyRaffle(String business_no) throws Exception {
        //每周抽奖
        c_weeklyRaffleMap.clear();
        List<C_WeeklyRaffle> c_weeklyRaffles = DataHallMrg.mongoTemplate.find(queryBusiness_no(business_no), C_WeeklyRaffle.class);
        for (C_WeeklyRaffle c_weeklyRaffle : c_weeklyRaffles) {
            c_weeklyRaffle.check();
            c_weeklyRaffleMap.put(c_weeklyRaffle.getActivityId(), c_weeklyRaffle);
        }
    }

    public void loadWinTicketNumbers(String business_no) throws Exception {
        //每周中奖号码
        c_winTicketNumbersMap.clear();
        c_winTicketNumbersListMap.clear();
        List<C_WinTicketNumbers> c_winTicketNumbers = DataHallMrg.mongoTemplate.find(queryBusiness_no(business_no), C_WinTicketNumbers.class);
        for (C_WinTicketNumbers winTicketNumbers : c_winTicketNumbers) {
            winTicketNumbers.check();
            final String id = winTicketNumbers.getDate() + "_" + winTicketNumbers.getRewardLevel();
            c_winTicketNumbersMap.put(id, winTicketNumbers);

            List<C_WinTicketNumbers> c_winTicketNumbersList = c_winTicketNumbersListMap.putIfAbsent(winTicketNumbers.getDate(), new ArrayList<>());
            if (c_winTicketNumbersList == null) {
                c_winTicketNumbersList = c_winTicketNumbersListMap.get(winTicketNumbers.getDate());
            }
            c_winTicketNumbersList.add(winTicketNumbers);
        }
    }

    public void loadQuest(String business_no) throws Exception {
        //任务
        final Query query = queryBusiness_no(business_no);
        query.addCriteria(Criteria.where("open").is(true)
                .and("deleteTime").is(0));
        c_questMap.clear();
        c_type2QuestMap.clear();
        List<C_Quest> c_quests = DataHallMrg.mongoTemplate.find(query, C_Quest.class);
        for (C_Quest c_quest : c_quests) {
            c_quest.check();
            c_questMap.put(c_quest.getQuestId(), c_quest);
            IntList questIdList = c_type2QuestMap.putIfAbsent(c_quest.getQuestType(), new IntArrayList());
            if (questIdList == null) {
                questIdList = c_type2QuestMap.get(c_quest.getQuestType());
            }
            questIdList.add(c_quest.getQuestId());
        }
    }

    public void loadGameTurnover(String business_no) throws Exception {
        //游戏打码
        c_gameTurnoverMap.clear();
        List<C_GameTurnover> c_gameTurnovers = DataHallMrg.mongoTemplate.find(queryBusiness_no(business_no), C_GameTurnover.class);
        for (C_GameTurnover c_gameTurnover : c_gameTurnovers) {
            c_gameTurnover.check();
            c_gameTurnoverMap.put(c_gameTurnover.getGameType(), c_gameTurnover);
        }
    }

    public void loadCustomerService(String business_no) throws Exception {
        //客服
        c_customerServiceMap.clear();
        final Query query = queryBusiness_no(business_no);
        query.addCriteria(Criteria.where("open").is(true));
        List<C_CustomerService> c_customerServices = DataHallMrg.mongoTemplate.find(query, C_CustomerService.class);
        for (C_CustomerService c_customerService : c_customerServices) {
            c_customerService.check();
            c_customerServiceMap.put(c_customerService.getCustomerServiceId(), c_customerService);
        }
    }

    public List<C_BigWinRobot> loadBigWinRobot(String business_no, int gameType, int limit) throws Exception {
        final Query query = queryBusiness_no(business_no);
        if (gameType > 0) {
            query.addCriteria(Criteria.where("gameType").is(gameType));
        }
        query.limit(limit);
        return DataHallMrg.mongoTemplate.find(query.with(Sort.by(Sort.Direction.DESC, "amount", "createTime")), C_BigWinRobot.class);
    }

    public void loadBottomMenu(String business_no) throws Exception {
        //底部菜单
        c_bottomMenuMap.clear();
        final Query query = queryBusiness_no(business_no);
        List<C_BottomMenu> c_bottomMenus = DataHallMrg.mongoTemplate.find(query, C_BottomMenu.class);
        for (C_BottomMenu c_bottomMenu : c_bottomMenus) {
            c_bottomMenu.check();
            c_bottomMenuMap.put(c_bottomMenu.getMenuId(), c_bottomMenu);
        }
    }

    public void loadQualityAssurance(String business_no) throws Exception {
        //QA
        c_qualityAssuranceMap.clear();
        final Query query = queryBusiness_no(business_no);
        List<C_QualityAssurance> c_qualityAssurances = DataHallMrg.mongoTemplate.find(query, C_QualityAssurance.class);
        for (C_QualityAssurance c_qualityAssurance : c_qualityAssurances) {
            c_qualityAssurance.check();
            c_qualityAssuranceMap.put(c_qualityAssurance.getQualityAssuranceId(), c_qualityAssurance);
        }
    }

    public void loadInvitationPoster(String business_no) throws Exception {
        //邀请海报
        c_invitationPosterMap.clear();
        final Query query = queryBusiness_no(business_no);
        query.addCriteria(Criteria.where("open").is(true));
        List<C_InvitationPoster> c_invitationPosters = DataHallMrg.mongoTemplate.find(query, C_InvitationPoster.class);
        for (C_InvitationPoster c_invitationPoster : c_invitationPosters) {
            c_invitationPoster.check();
            List<C_InvitationPoster> c_invitationPosterList = c_invitationPosterMap.putIfAbsent(c_invitationPoster.getLanguage(), new ArrayList<>());
            if (c_invitationPosterList == null) {
                c_invitationPosterList = c_invitationPosterMap.get(c_invitationPoster.getLanguage());
            }
            c_invitationPosterList.add(c_invitationPoster);
        }
    }

    public void loadFreeGameTurnover(String business_no) throws Exception {
        //免费游戏打码
        c_freeGameTurnoverMap.clear();
        List<C_FreeGameTurnover> c_currencies = DataHallMrg.mongoTemplate.find(queryBusiness_no(business_no), C_FreeGameTurnover.class);
        for (C_FreeGameTurnover c_freeGameTurnover : c_currencies) {
            c_freeGameTurnover.check();
            c_freeGameTurnoverMap.put(c_freeGameTurnover.getPlatformId(), c_freeGameTurnover);
        }
    }

    public void loadInvitationLinks(String business_no) throws Exception {
        //邀请连接
        c_invitationLinksMap.clear();
        final Query query = queryBusiness_no(business_no);
        query.addCriteria(Criteria.where("open").is(true));
        List<C_InvitationLinks> c_invitationLinks = DataHallMrg.mongoTemplate.find(query, C_InvitationLinks.class);
        for (C_InvitationLinks invitationLinks : c_invitationLinks) {
            invitationLinks.check();
            c_invitationLinksMap.put(invitationLinks.getInvitationLinksId(), invitationLinks);
        }
    }

    public void loadRedemptionCode(String business_no) throws Exception {
        //兑换码
        c_redemptionCodeMap.clear();
        final Query query = queryBusiness_no(business_no);
        query.addCriteria(Criteria.where("open").is(true)
                .and("deleteTime").is(0));
        List<C_RedemptionCode> c_redemptionCodes = DataHallMrg.mongoTemplate.find(query, C_RedemptionCode.class);
        for (C_RedemptionCode c_redemptionCode : c_redemptionCodes) {
            c_redemptionCode.check();
            for (String redemptionCode : c_redemptionCode.getRedeemCodes()) {
                c_redemptionCodeMap.put(redemptionCode, c_redemptionCode);
            }
        }
    }

    public void loadThreePartyLogin(String business_no) throws Exception {
        //三方登录配置
        c_threePartyLoginMap.clear();
        final Query query = queryBusiness_no(business_no);
        query.addCriteria(Criteria.where("open").is(true));
        List<C_ThreePartyLogin> c_threePartyLogins = DataHallMrg.mongoTemplate.find(query, C_ThreePartyLogin.class);
        for (C_ThreePartyLogin c_threePartyLogin : c_threePartyLogins) {
            c_threePartyLogin.check();
            List<C_ThreePartyLogin> c_threePartyLoginList = c_threePartyLoginMap.putIfAbsent(c_threePartyLogin.getDomainName(), new ArrayList<>());
            if (c_threePartyLoginList == null) {
                c_threePartyLoginList = c_threePartyLoginMap.get(c_threePartyLogin.getDomainName());
            }
            c_threePartyLoginList.add(c_threePartyLogin);
        }
    }

    public void loadFunctionEnabled(String business_no) throws Exception {
        //功能开启
        c_functionEnabledMap.clear();
        final Query query = queryBusiness_no(business_no);
        query.addCriteria(Criteria.where("open").is(true));
        List<C_FunctionEnabled> c_functionEnableds = DataHallMrg.mongoTemplate.find(query, C_FunctionEnabled.class);
        for (C_FunctionEnabled c_functionEnabled : c_functionEnableds) {
            c_functionEnabled.check();
            c_functionEnabledMap.put(c_functionEnabled.getFunctionId(), c_functionEnabled);
        }
    }

    public void loadWebSiteInfo(String business_no) throws Exception {
        //站点信息
        c_webSiteInfoMap.clear();
        final Query query = queryBusiness_no(business_no);
        List<C_WebSiteInfo> c_webSiteInfos = DataHallMrg.mongoTemplate.find(query, C_WebSiteInfo.class);
        for (C_WebSiteInfo c_webSiteInfo : c_webSiteInfos) {
            c_webSiteInfo.check();
            for (String domainName : c_webSiteInfo.getDomainName()) {
                c_webSiteInfoMap.put(domainName, c_webSiteInfo);
            }
        }
    }

    public void loadQuickAccess(String business_no) throws Exception {
        //快速入口
        c_quickAccessMap.clear();
        final Query query = queryBusiness_no(business_no);
        List<C_QuickAccess> c_quickAccesses = DataHallMrg.mongoTemplate.find(query, C_QuickAccess.class);
        for (C_QuickAccess c_quickAccess : c_quickAccesses) {
            c_quickAccess.check();
            for (String webSite : c_quickAccess.getWebSites()) {
                List<C_QuickAccess> c_quickAccessList = c_quickAccessMap.putIfAbsent(webSite, new ArrayList<>());
                if (c_quickAccessList == null) {
                    c_quickAccessList = c_quickAccessMap.get(webSite);
                }
                c_quickAccessList.add(c_quickAccess);
            }
        }
    }

    public void loadMaintainNotice(String business_no) throws Exception {
        //维护公告
        c_maintainNoticeMap.clear();
        final Query query = queryBusiness_no(business_no);
        query.addCriteria(Criteria.where("status").is(true));
        List<C_MaintainNotice> c_maintainNotices = DataHallMrg.mongoTemplate.find(query, C_MaintainNotice.class);
        for (C_MaintainNotice c_maintainNotice : c_maintainNotices) {
            c_maintainNotice.check();
            c_maintainNoticeMap.put(c_maintainNotice.getC_id(), c_maintainNotice);
        }
    }

    public void loadBigWin(String business_no) throws Exception {
        //大赢家
        c_bigWinMap.clear();
        final Query query = queryBusiness_no(business_no);
        List<C_BigWin> c_bigWins = DataHallMrg.mongoTemplate.find(query, C_BigWin.class);
        for (C_BigWin c_bigWin : c_bigWins) {
            c_bigWin.check();
            c_bigWinMap.put(c_bigWin.getGameId(), c_bigWin);
        }
    }

    public void loadRegisterRetrievePop(String business_no) throws Exception {
        //注册挽回
        c_registerRetrieveMap.clear();
        final Query query = queryBusiness_no(business_no);
        query.addCriteria(Criteria.where("status").is(true));
        List<C_RegisterRetrievePop> c_registerRetrieves = DataHallMrg.mongoTemplate.find(query, C_RegisterRetrievePop.class);
        for (C_RegisterRetrievePop c_registerRetrieve : c_registerRetrieves) {
            c_registerRetrieve.check();
            c_registerRetrieveMap.put(c_registerRetrieve.getC_id(), c_registerRetrieve);
        }
    }

    public void loadDailyRechargePop(String business_no) throws Exception {
        //每日充值
        c_dailyRechargePopMap.clear();
        final Query query = queryBusiness_no(business_no);
        List<C_DailyRechargePop> c_dailyRechargePops = DataHallMrg.mongoTemplate.find(query, C_DailyRechargePop.class);
        for (C_DailyRechargePop c_dailyRechargePop : c_dailyRechargePops) {
            c_dailyRechargePop.check();
            c_dailyRechargePopMap.put(c_dailyRechargePop.getC_id(), c_dailyRechargePop);
        }
    }

    public void loadFirstChargePop(String business_no) throws Exception {
        //首充值
        c_firstChargePopMap.clear();
        final Query query = queryBusiness_no(business_no);
        List<C_FirstChargePop> c_firstChargePops = DataHallMrg.mongoTemplate.find(query, C_FirstChargePop.class);
        for (C_FirstChargePop c_firstChargePop : c_firstChargePops) {
            c_firstChargePop.check();
            c_firstChargePopMap.put(c_firstChargePop.getC_id(), c_firstChargePop);
        }
    }

    public void loadGamePop(String business_no) throws Exception {
        //游戏
        c_gamePopMap.clear();
        final Query query = queryBusiness_no(business_no);
        List<C_GamePop> c_gamePops = DataHallMrg.mongoTemplate.find(query, C_GamePop.class);
        for (C_GamePop c_gamePop : c_gamePops) {
            c_gamePop.check();
            c_gamePopMap.put(c_gamePop.getC_id(), c_gamePop);
        }
    }

    public void loadRedEnvelopeRain(String business_no) throws Exception {
        //红包雨
        c_redEnvelopeRainDayMap.clear();
        c_redEnvelopeRainWeekMap.clear();
        final Query query = queryBusiness_no(business_no);
        query.addCriteria(Criteria.where("open").is(true)
                .and("deleteTime").is(0));
        List<C_RedEnvelopeRain> c_redEnvelopeRains = DataHallMrg.mongoTemplate.find(query, C_RedEnvelopeRain.class);
        for (C_RedEnvelopeRain c_redEnvelopeRain : c_redEnvelopeRains) {
            c_redEnvelopeRain.check();
            for (int cycle : c_redEnvelopeRain.getCycles()) {
                c_redEnvelopeRainDayMap.put(cycle, c_redEnvelopeRain);
            }
            for (int cycle : c_redEnvelopeRain.getMonthlys()) {
                c_redEnvelopeRainWeekMap.put(cycle, c_redEnvelopeRain);
            }
        }
    }

    public void loadRewardBox(String business_no) throws Exception {
        //推荐宝箱
        c_rewardBoxMap.clear();
        final Query query = queryBusiness_no(business_no);
        query.addCriteria(Criteria.where("open").is(true)
                .and("deleteTime").is(0));
        List<C_RewardBox> c_rewardBoxes = DataHallMrg.mongoTemplate.find(query, C_RewardBox.class);
        for (C_RewardBox c_rewardBox : c_rewardBoxes) {
            c_rewardBox.check();
            c_rewardBoxMap.put(c_rewardBox.getActivityId(), c_rewardBox);
        }
    }

    public void loadMysteryBonus(String business_no) throws Exception {
        //神秘奖金
        c_mysteryBonusMap.clear();
        final Query query = queryBusiness_no(business_no);
        query.addCriteria(Criteria.where("open").is(true)
                .and("deleteTime").is(0));
        List<C_MysteryBonus> c_mysteryBonuses = DataHallMrg.mongoTemplate.find(query, C_MysteryBonus.class);
        for (C_MysteryBonus c_mysteryBonus : c_mysteryBonuses) {
            c_mysteryBonus.check();
            c_mysteryBonusMap.put(c_mysteryBonus.getActivityId(), c_mysteryBonus);
        }
    }

    public void loadPiggyBank(String business_no) throws Exception {
        //存钱罐
        c_piggyBankMap.clear();
        final Query query = queryBusiness_no(business_no);
        query.addCriteria(Criteria.where("open").is(true));
        List<C_PiggyBank> c_piggyBanks = DataHallMrg.mongoTemplate.find(query, C_PiggyBank.class);
        for (C_PiggyBank c_piggyBank : c_piggyBanks) {
            c_piggyBank.check();
            c_piggyBankMap.put(c_piggyBank.getActivityId(), c_piggyBank);
        }
    }

    public void loadContinuousDeposit(String business_no) throws Exception {
        //连续充值
        c_continuousDepositMap.clear();
        final Query query = queryBusiness_no(business_no);
        query.addCriteria(Criteria.where("open").is(true)
                .and("deleteTime").is(0));
        List<C_ContinuousDeposit> c_continuousDeposits = DataHallMrg.mongoTemplate.find(query, C_ContinuousDeposit.class);
        for (C_ContinuousDeposit c_continuousDeposit : c_continuousDeposits) {
            c_continuousDeposit.check();
            c_continuousDepositMap.put(c_continuousDeposit.getActivityId(), c_continuousDeposit);
        }
    }

    public void loadPwa(String business_no) throws Exception {
        //pwa
        c_pwaMap.clear();
        final Query query = queryBusiness_no(business_no);
        query.addCriteria(Criteria.where("open").is(true));
        List<C_Pwa> c_pwas = DataHallMrg.mongoTemplate.find(query, C_Pwa.class);
        for (C_Pwa c_pwa : c_pwas) {
            c_pwa.check();
            c_pwaMap.put(c_pwa.getC_id(), c_pwa);
        }
    }

    public void loadFirstChargeSignIn(String business_no) throws Exception {
        //首充签到
        c_firstChargeSignInMap.clear();
        final Query query = queryBusiness_no(business_no);
        query.addCriteria(Criteria.where("open").is(true));
        List<C_FirstChargeSignIn> c_firstChargeSignIns = DataHallMrg.mongoTemplate.find(query, C_FirstChargeSignIn.class);
        for (C_FirstChargeSignIn c_firstChargeSignIn : c_firstChargeSignIns) {
            c_firstChargeSignIn.check();
            c_firstChargeSignInMap.put(c_firstChargeSignIn.getActivityId(), c_firstChargeSignIn);
        }
    }

    public void loadCrazyBox(String business_no) throws Exception {
        //crazyBox
        c_crazyBoxMap.clear();
        final Query query = queryBusiness_no(business_no);
        query.addCriteria(Criteria.where("open").is(true));
        List<C_CrazyBox> c_crazyBoxes = DataHallMrg.mongoTemplate.find(query, C_CrazyBox.class);
        for (C_CrazyBox c_crazyBox : c_crazyBoxes) {
            c_crazyBox.check();
            c_crazyBoxMap.put(c_crazyBox.getActivityId(), c_crazyBox);
        }
    }

    public void loadCrazyBoxQuest(String business_no) throws Exception {
        //crazyBoxQuest
        c_crazyBoxQuestMap.clear();
        final Query query = queryBusiness_no(business_no);
        query.addCriteria(Criteria.where("open").is(true));
        List<C_CrazyBoxQuest> c_crazyBoxQuests = DataHallMrg.mongoTemplate.find(query, C_CrazyBoxQuest.class);
        for (C_CrazyBoxQuest c_crazyBoxQuest : c_crazyBoxQuests) {
            c_crazyBoxQuest.check();
            c_crazyBoxQuestMap.put(c_crazyBoxQuest.getQuestId(), c_crazyBoxQuest);
        }
    }

    public void loadRechargeRecover(String business_no) throws Exception {
        //rechargeRecover
        c_rechargeRecoverMap.clear();
        final Query query = queryBusiness_no(business_no);
        query.addCriteria(Criteria.where("open").is(true));
        List<C_RechargeRecover> c_rechargeRecovers = DataHallMrg.mongoTemplate.find(query, C_RechargeRecover.class);
        for (C_RechargeRecover c_rechargeRecover : c_rechargeRecovers) {
            c_rechargeRecover.check();
            c_rechargeRecoverMap.put(c_rechargeRecover.getActivityId(), c_rechargeRecover);
        }
    }

    public void loadWageredRebates(String business_no) throws Exception {
        //wageredRebates
        c_wageredRebatesMap.clear();
        final Query query = queryBusiness_no(business_no);
        query.addCriteria(Criteria.where("open").is(true));
        List<C_WageredRebates> c_wageredRebates = DataHallMrg.mongoTemplate.find(query, C_WageredRebates.class);
        for (C_WageredRebates wageredRebates : c_wageredRebates) {
            wageredRebates.check();
            c_wageredRebatesMap.put(wageredRebates.getActivityId(), wageredRebates);
        }
    }

    public void loadFirstDepositInviteBonus(String business_no) throws Exception {
        //firstDepositInviteBonus
        c_firstDepositInviteBonusMap.clear();
        final Query query = queryBusiness_no(business_no);
        query.addCriteria(Criteria.where("open").is(true));
        List<C_FirstDepositInviteBonus> c_firstDepositInviteBonuses = DataHallMrg.mongoTemplate.find(query, C_FirstDepositInviteBonus.class);
        for (C_FirstDepositInviteBonus c_firstDepositInviteBonus : c_firstDepositInviteBonuses) {
            c_firstDepositInviteBonus.check();
            c_firstDepositInviteBonusMap.put(c_firstDepositInviteBonus.getActivityId(), c_firstDepositInviteBonus);
        }
    }

    public void loadGameKillRate(String business_no) throws Exception {
        //gameKillRate
        c_gameKillRateMap.clear();
        final Query query = queryBusiness_no(business_no);
        List<C_GameKillRate> c_gameKillRates = DataHallMrg.mongoTemplate.find(query, C_GameKillRate.class);
        for (C_GameKillRate c_gameKillRate : c_gameKillRates) {
            c_gameKillRate.check();
            List<C_GameKillRate> c_gameKillRateList = c_gameKillRateMap.putIfAbsent(c_gameKillRate.getControlType(), new ArrayList<>());
            if (c_gameKillRateList == null) {
                c_gameKillRateList = c_gameKillRateMap.get(c_gameKillRate.getControlType());
            }
            c_gameKillRateList.add(c_gameKillRate);
        }
    }

    /****************************************************************************************************************************************************************************/

    public String findC_GlobalValue(String className, String name) {
        final String value = c_globalMap.get(name);
        if (value == null) {
            //LOGGER.warn("className：{}，config，C_Global，business_no：{}，name：{}，not exits", className, business_no, name);
            return "";
        }
        return value;
    }

    public List<C_Activity> findC_ActivityList(String className, int activityId) {
        final List<C_Activity> c_activity = c_activityListMap.get(activityId);
        if (c_activity == null) {
            //LOGGER.warn("className：{}，config，C_Activity，business_no：{}，activityId：{}，not exits", className, business_no, activityId);
            return new ArrayList<>();
        }
        return c_activity;
    }

    public C_Activity findC_Activity(String className, int activityId) {
        final List<C_Activity> c_activity = c_activityListMap.get(activityId);
        if (c_activity == null) {
            //LOGGER.warn("className：{}，config，C_Activity，business_no：{}，activityId：{}，not exits", className, business_no, activityId);
            return null;
        }
        return c_activity.getFirst();
    }

    public C_ActivityTag findC_ActivityTag(String className, int activityTag) {
        final C_ActivityTag c_activityTag = c_activityTagMap.get(activityTag);
        if (c_activityTag == null) {
            //LOGGER.warn("className：{}，config，C_ActivityTag，business_no：{}，activityTag：{}，not exits", className, business_no, activityTag);
            return null;
        }
        return c_activityTag;
    }

    public C_Activity findByIdC_Activity(String className, int cId) {
        final C_Activity c_activity = c_activityMap.get(cId);
        if (c_activity == null) {
            //LOGGER.warn("className：{}，config，C_Activity，business_no：{}，cId：{}，not exits", className, business_no, cId);
            return null;
        }
        return c_activity;
    }

    public C_CollectionWallet findC_CollectionWallet(String className, String id) {
        final List<C_CollectionWallet> c_collectionWallets = c_collectionWalletMap.get(id);
        if (c_collectionWallets.isEmpty()) {
            LOGGER.warn("className：{}，config，C_CollectionWallet，id：{}，not exits", className, id);
            return null;
        }
        return c_collectionWallets.getFirst();
    }

    public C_Currency findC_Currency(String className, int currencyId) {
        final C_Currency c_currency = c_currencyMap.get(currencyId);
        if (c_currency == null) {
            LOGGER.warn("className：{}，config，business_no：{}，C_Currency，currencyId：{}，not exits", className, business_no, currencyId);
            return null;
        }
        return c_currency;
    }

    public Map<String, C_PlatformRecharge> findC_PlatformRechargeFiat(String className, int currencyId) {
        final Map<String, C_PlatformRecharge> c_platformRecharge = c_platformRecharge_fiatMap.get(currencyId);
        if (c_platformRecharge == null) {
            LOGGER.warn("className：{}，config，C_PlatformRecharge，business_no：{}，currencyId：{}，fiat not exits", className, business_no, currencyId);
            return null;
        }
        return c_platformRecharge;
    }

    public List<C_PlatformRecharge> findC_PlatformRechargeCrypto(String className, int currencyId) {
        final List<C_PlatformRecharge> c_platformRecharge = c_platformRecharge_cryptoMap.get(currencyId);
        if (c_platformRecharge == null) {
            LOGGER.warn("className：{}，config，C_PlatformRecharge，business_no：{}，currencyId：{}，crypto not exits", className, business_no, currencyId);
            return null;
        }
        return c_platformRecharge;
    }

    public Map<String, C_PlatformWithdraw> findC_PlatformWithdrawFiat(String className, int currencyId) {
        final Map<String, C_PlatformWithdraw> c_platformWithdraw = c_platformWithdraw_fiatMap.get(currencyId);
        if (c_platformWithdraw == null) {
            LOGGER.warn("className：{}，config，C_PlatformWithdraw，business_no：{}，currencyId：{}，fiat not exits", className, business_no, currencyId);
            return null;
        }
        return c_platformWithdraw;
    }

    public List<C_PlatformWithdraw> findC_PlatformWithdrawCrypto(String className, int currencyId) {
        final List<C_PlatformWithdraw> c_platformWithdraw = c_platformWithdraw_cryptoMap.get(currencyId);
        if (c_platformWithdraw == null) {
            LOGGER.warn("className：{}，config，C_PlatformWithdraw，business_no：{}，currencyId：{}，crypto not exits", className, business_no, currencyId);
            return null;
        }
        return c_platformWithdraw;
    }

    public C_RechargeWithdrawLimit findC_RechargeWithdrawLimit(String className, int currencyId) {
        final C_RechargeWithdrawLimit c_rechargeWithdrawLimit = c_rechargeWithdrawLimitMap.get(currencyId);
        if (c_rechargeWithdrawLimit == null) {
            LOGGER.warn("className：{}，config，C_RechargeWithdrawLimit，business_no：{}，currencyId：{}，not exits", className, business_no, currencyId);
            return null;
        }
        return c_rechargeWithdrawLimit;
    }

    public C_VipClub findC_VipClub(String className, int vipLevel) {
        final C_VipClub c_vipClub = c_vipClubMap.get(vipLevel);
        if (c_vipClub == null) {
            //LOGGER.warn("className：{}，config，C_VipClub，business_no：{}，vipLevel：{}，not exits", className, business_no, vipLevel);
            return null;
        }
        return c_vipClub;
    }

    public C_GameChannel findC_GameChannel(String className, int gameChannel) {
        final C_GameChannel c_gameChannel = c_gameChannelMap.get(gameChannel);
        if (c_gameChannel == null) {
            //LOGGER.warn("className：{}，config，C_GameChannel，business_no：{}，gameChannel：{}，not exits", className, business_no, gameChannel);
            return null;
        }
        return c_gameChannel;
    }

    public C_GameChannel findC_SubGameChannel(String className, int subChannelType) {
        final C_GameChannel c_gameChannel = c_gameSubChannelMap.get(subChannelType);
        if (c_gameChannel == null) {
            //LOGGER.warn("className：{}，config，C_GameChannel，business_no：{}，subChannelType：{}，not exits", className, business_no, subChannelType);
            return null;
        }
        return c_gameChannel;
    }

    public List<C_GameChannel> findC_GameChannelList(String className, int gameChannel) {
        final List<C_GameChannel> c_gameChannelList = this.c_gameChannelListMap.get(gameChannel);
        if (c_gameChannelList == null) {
            LOGGER.warn("className：{}，config，C_GameChannel，business_no：{}，gameChannel：{}，not exits", className, business_no, gameChannel);
            return null;
        }
        return c_gameChannelList;
    }

    public List<C_SubChannelGameApi> findC_SubChannelGameApi(String className, int channelType) {
        final List<C_SubChannelGameApi> c_subChannelGameApi = this.c_subChannelGameApi.get(channelType);
        if (c_subChannelGameApi == null) {
//            LOGGER.warn("className：{}，config，C_SubChannelGameApi，business_no：{}，channelType：{}，not exits", className, business_no, channelType);
            return new ArrayList<>();
        }
        return c_subChannelGameApi;
    }

    public C_GameApi findC_GameApi(String className, int gameId) {
        final C_GameApi c_gameApi = c_gameApiMap.get(gameId);
        if (c_gameApi == null) {
            //LOGGER.warn("className：{}，config，C_GameApi，business_no：{}，gameId：{}，not exits", className, business_no, gameId);
            return null;
        }
        return c_gameApi;
    }

    public List<C_GameApi> findC_platformIdGameType_gameApi(String className, int platformIdGameType) {
        final List<C_GameApi> c_gameApis = c_platformIdGameType_gameApiMap.get(platformIdGameType);
        if (c_gameApis == null) {
            //LOGGER.warn("className：{}，config，C_GameApi，business_no：{}，platformIdGameType：{}，not exits", className, business_no, platformIdGameType);
            return null;
        }
        return c_gameApis;
    }

    public List<C_GameApi> findC_platformId_gameApi(String className, int platformId) {
        final List<C_GameApi> c_gameApis = c_platformId_gameApiMap.get(platformId);
        if (c_gameApis == null) {
            //LOGGER.warn("className：{}，config，C_GameApi，business_no：{}，platformId：{}，not exits", className, business_no, platformId);
            return null;
        }
        return c_gameApis;
    }

    public C_PubMail findC_PubMail(String className, int mailId) {
        final C_PubMail c_pubMail = c_pubMailMap.get(mailId);
        if (c_pubMail == null) {
            //LOGGER.warn("className：{}，config，C_PubMail，business_no：{}，mailId：{}，not exits", className, business_no, mailId);
            return null;
        }
        return c_pubMail;
    }

    public C_GamePlatform findC_GamePlatform(String className, int platformId) {
        final C_GamePlatform c_gamePlatform = c_gamePlatformMap.get(platformId);
        if (c_gamePlatform == null) {
//            LOGGER.warn("className：{}，config，C_GamePlatform，platformId：{}，not exits", className, platformId);
            return null;
        }
        return c_gamePlatform;
    }

    public C_GamePlatform findC_GamePlatformIdGameType(String className, int platformIdGameType) {
        final C_GamePlatform c_gamePlatform = c_platformIdGameType_platformMap.get(platformIdGameType);
        if (c_gamePlatform == null) {
            //LOGGER.warn("className：{}，config，C_GamePlatform，platformIdGameType：{}，not exits", className, platformIdGameType);
            return null;
        }
        return c_gamePlatform;
    }

    public C_ReferralReward findC_ReferralReward(String className, int vipLevel) {
        final C_ReferralReward c_referralReward = c_referralRewardMap.get(vipLevel);
        if (c_referralReward == null) {
            //LOGGER.warn("className：{}，config，C_ReferralReward，business_no：{}，vipLevel：{}，not exits", className, business_no, vipLevel);
            return null;
        }
        return c_referralReward;
    }

    public C_CashBack findC_CashBack(String className, int gameType) {
        final C_CashBack c_cashBack = c_cashBackMap.get(gameType);
        if (c_cashBack == null) {
//            LOGGER.warn("className：{}，config，C_CashBack，business_no：{}，gameType：{}，not exits", className, business_no, gameType);
            return null;
        }
        return c_cashBack;
    }

    public C_RechargeTurnover findC_RechargeTurnover(String className, int currencyId) {
        final C_RechargeTurnover c_rechargeTurnover = c_rechargeTurnoverMap.get(currencyId);
        if (c_rechargeTurnover == null) {
            LOGGER.warn("className：{}，config，C_RechargeTurnover，business_no：{}，currencyId：{}，not exits", className, business_no, currencyId);
            return null;
        }
        return c_rechargeTurnover;
    }

    public C_LuckSpin findC_LuckSpin(String className, int activityId) {
        final List<C_LuckSpin> c_luckSpin = c_luckSpinMap.get(activityId);
        if (c_luckSpin == null) {
            //LOGGER.warn("className：{}，config，C_LuckSpin，business_no：{}，activityId：{}，not exits", className, business_no, activityId);
            return null;
        }
        return c_luckSpin.getFirst();
    }

    public List<C_LuckSpin> findC_VipSpinList(String className, int activityId) {
        final List<C_LuckSpin> c_luckSpin = c_luckSpinMap.get(activityId);
        if (c_luckSpin == null) {
            //LOGGER.warn("className：{}，config，C_LuckSpin，business_no：{}，activityId：{}，not exits", className, business_no, activityId);
            return new ArrayList<>();
        }
        return c_luckSpin;
    }

    public C_DailyContest findC_DailyContest(String className, int activityId) {
        final C_DailyContest c_dailyContest = c_dailyContestMap.get(activityId);
        if (c_dailyContest == null) {
            //LOGGER.warn("className：{}，config，C_DailyContest，business_no：{}，activityId：{}，not exits", className, business_no, activityId);
            return null;
        }
        return c_dailyContest;
    }

    public C_WeeklyRaffle findC_WeeklyRaffle(String className, int activityId) {
        final C_WeeklyRaffle c_weeklyRaffle = c_weeklyRaffleMap.get(activityId);
        if (c_weeklyRaffle == null) {
            //LOGGER.warn("className：{}，config，C_WeeklyRaffle，business_no：{}，activityId：{}，not exits", className, business_no, activityId);
            return null;
        }
        return c_weeklyRaffle;
    }

    public C_WinTicketNumbers findC_WinTicketNumbers(String className, String dateRewardLevel) {
        final C_WinTicketNumbers c_winTicketNumbers = c_winTicketNumbersMap.get(dateRewardLevel);
        if (c_winTicketNumbers == null) {
            LOGGER.warn("className：{}，config，C_WinTicketNumbers，business_no：{}，dateRewardLevel：{}，not exits", className, business_no, dateRewardLevel);
            return null;
        }
        return c_winTicketNumbers;
    }

    public C_Quest findC_Quest(String className, int questId) {
        final C_Quest c_quest = c_questMap.get(questId);
        if (c_quest == null) {
            LOGGER.warn("className：{}，config，C_Quest，business_no：{}，questId：{}，not exits", className, business_no, questId);
            return null;
        }
        return c_quest;
    }

    public C_GameTurnover findC_GameTurnover(String className, int gameType) {
        final C_GameTurnover c_gameTurnover = c_gameTurnoverMap.get(gameType);
        if (c_gameTurnover == null) {
            LOGGER.warn("className：{}，config，C_GameTurnover，business_no：{}，gameType：{}，not exits", className, business_no, gameType);
            return null;
        }
        return c_gameTurnover;
    }

    public C_FreeGameTurnover findC_FreeGameTurnover(String className, int platformId) {
        final C_FreeGameTurnover c_freeGameTurnover = c_freeGameTurnoverMap.get(platformId);
        if (c_freeGameTurnover == null) {
            LOGGER.warn("className：{}，config，C_FreeGameTurnover，business_no：{}，platformId：{}，not exits", className, business_no, platformId);
            return null;
        }
        return c_freeGameTurnover;
    }

    public C_RedemptionCode findC_RedemptionCode(String className, String redemptionCode) {
        final C_RedemptionCode c_redemptionCode = c_redemptionCodeMap.get(redemptionCode);
        if (c_redemptionCode == null) {
            LOGGER.warn("className：{}，config，C_RedemptionCode，business_no：{}，redemptionCode：{}，not exits", className, business_no, redemptionCode);
            return null;
        }
        return c_redemptionCode;
    }

    public List<C_ThreePartyLogin> findC_ThreePartyLogin(String className, String domainName) {
        final List<C_ThreePartyLogin> c_threePartyLogins = c_threePartyLoginMap.get(domainName);
        if (c_threePartyLogins == null) {
            //LOGGER.warn("className：{}，config，C_ThreePartyLogin，business_no：{}，domainName：{}，not exits", className, business_no, domainName);
            return new ArrayList<>();
        }
        return c_threePartyLogins;
    }

    public C_WebSiteInfo findC_WebSiteInfo(String className, String domainName) {
        final C_WebSiteInfo c_webSiteInfo = c_webSiteInfoMap.get(domainName);
        if (c_webSiteInfo == null) {
            //LOGGER.warn("className：{}，config，C_WebSiteInfo，business_no：{}，domainName：{}，not exits", className, business_no, domainName);
            return null;
        }
        return c_webSiteInfo;
    }

    public List<C_QuickAccess> findC_QuickAccess(String className, String domainName) {
        final List<C_QuickAccess> c_quickAccesses = c_quickAccessMap.get(domainName);
        if (c_quickAccesses == null) {
            //LOGGER.warn("className：{}，config，C_QuickAccess，business_no：{}，domainName：{}，not exits", className, business_no, domainName);
            return new ArrayList<>();
        }
        return c_quickAccesses;
    }

    public C_RewardBox findC_RewardBox(String className, int activityId) {
        final C_RewardBox c_rewardBox = c_rewardBoxMap.get(activityId);
        if (c_rewardBox == null) {
            //LOGGER.warn("className：{}，config，C_RewardBox，business_no：{}，activityId：{}，not exits", className, business_no, activityId);
            return null;
        }
        return c_rewardBox;
    }

    public C_MysteryBonus findC_MysteryBonus(String className, int activityId) {
        final C_MysteryBonus c_mysteryBonus = c_mysteryBonusMap.get(activityId);
        if (c_mysteryBonus == null) {
            //LOGGER.warn("className：{}，config，C_MysteryBonus，business_no：{}，activityId：{}，not exits", className, business_no, activityId);
            return null;
        }
        return c_mysteryBonus;
    }

    public C_PiggyBank findC_PiggyBank(String className, int activityId) {
        final C_PiggyBank c_piggyBank = c_piggyBankMap.get(activityId);
        if (c_piggyBank == null) {
            //LOGGER.warn("className：{}，config，C_PiggyBank，business_no：{}，activityId：{}，not exits", className, business_no, activityId);
            return null;
        }
        return c_piggyBank;
    }

    public C_ContinuousDeposit findC_ContinuousDeposit(String className, int activityId) {
        final C_ContinuousDeposit c_continuousDeposit = c_continuousDepositMap.get(activityId);
        if (c_continuousDeposit == null) {
            //LOGGER.warn("className：{}，config，C_ContinuousDeposit，business_no：{}，activityId：{}，not exits", className, business_no, activityId);
            return null;
        }
        return c_continuousDeposit;
    }

    public C_FirstChargeSignIn findC_FirstChargeSignIn(String className, int activityId) {
        final C_FirstChargeSignIn c_firstChargeSignIn = c_firstChargeSignInMap.get(activityId);
        if (c_firstChargeSignIn == null) {
            //LOGGER.warn("className：{}，config，C_FirstChargeSignIn，business_no：{}，activityId：{}，not exits", className, business_no, activityId);
            return null;
        }
        return c_firstChargeSignIn;
    }

    public C_CrazyBox findC_CrazyBox(String className, int activityId) {
        final C_CrazyBox c_crazyBox = c_crazyBoxMap.get(activityId);
        if (c_crazyBox == null) {
            return null;
        }
        return c_crazyBox;
    }

    public C_RechargeRecover findC_RechargeRecover(String className, int activityId) {
        final C_RechargeRecover c_rechargeRecover = c_rechargeRecoverMap.get(activityId);
        if (c_rechargeRecover == null) {
            //LOGGER.warn("className：{}，config，C_RechargeRecover，business_no：{}，activityId：{}，not exits", className, business_no, activityId);
            return null;
        }
        return c_rechargeRecover;
    }

    public C_WageredRebates findC_WageredRebates(String className, int activityId) {
        final C_WageredRebates c_wageredRebates = c_wageredRebatesMap.get(activityId);
        if (c_wageredRebates == null) {
            return null;
        }
        return c_wageredRebates;
    }

    public C_FirstDepositInviteBonus findC_FirstDepositInviteBonus(String className, int activityId) {
        final C_FirstDepositInviteBonus c_firstDepositInviteBonus = c_firstDepositInviteBonusMap.get(activityId);
        if (c_firstDepositInviteBonus == null) {
            return null;
        }
        return c_firstDepositInviteBonus;
    }

    public Map<Integer, C_GamePlatform> getC_gamePlatformMap() {
        return c_gamePlatformMap;
    }

    public Map<Integer, C_GamePlatform> getC_platformIdGameType_platformMap() {
        return c_platformIdGameType_platformMap;
    }

    public Map<Integer, C_GameApi> getC_gameApiMap() {
        return c_gameApiMap;
    }

    public Map<Integer, List<C_GameApi>> getC_platformIdGameType_gameApiMap() {
        return c_platformIdGameType_gameApiMap;
    }

    public Map<Integer, List<C_GameApi>> getC_platformId_gameApiMap() {
        return c_platformId_gameApiMap;
    }

    public Map<Integer, List<C_GameChannel>> getC_gameChannelListMap() {
        return c_gameChannelListMap;
    }

    public Map<Integer, C_GameChannel> getC_gameChannelMap() {
        return c_gameChannelMap;
    }

    public Map<Integer, C_GameChannel> getC_gameSubChannelMap() {
        return c_gameSubChannelMap;
    }

    public Map<Integer, List<C_SubChannelGameApi>> getC_subChannelGameApi() {
        return c_subChannelGameApi;
    }

    public Map<String, String> getC_globalMap() {
        return c_globalMap;
    }

    public Map<String, List<C_Banner>> getC_bannerMap() {
        return c_bannerMap;
    }

    public Map<String, List<C_CollectionWallet>> getC_collectionWalletMap() {
        return c_collectionWalletMap;
    }

    public Map<Integer, C_Currency> getC_currencyMap() {
        return c_currencyMap;
    }

    public Map<Integer, Map<String, C_PlatformRecharge>> getC_platformRecharge_fiatMap() {
        return c_platformRecharge_fiatMap;
    }

    public Map<Integer, List<C_PlatformRecharge>> getC_platformRecharge_cryptoMap() {
        return c_platformRecharge_cryptoMap;
    }

    public Map<Integer, Map<String, C_PlatformWithdraw>> getC_platformWithdraw_fiatMap() {
        return c_platformWithdraw_fiatMap;
    }

    public Map<Integer, List<C_PlatformWithdraw>> getC_platformWithdraw_cryptoMap() {
        return c_platformWithdraw_cryptoMap;
    }

    public Map<Integer, List<C_News>> getC_newsMap() {
        return c_newsMap;
    }

    public Map<Integer, List<C_Popup>> getC_popupMap() {
        return c_popupMap;
    }

    public Map<Integer, C_PubMail> getC_pubMailMap() {
        return c_pubMailMap;
    }

    public Map<Integer, C_PubMail> getC_timelyPubMailMap() {
        return c_timelyPubMailMap;
    }

    public Map<Integer, List<C_PubMail>> getC_eventPubMailMap() {
        return c_eventPubMailMap;
    }

    public Map<Integer, List<C_HelpCenter>> getC_helpCenterMap() {
        return c_helpCenterMap;
    }

    public Map<Integer, C_RechargeTurnover> getC_rechargeTurnoverMap() {
        return c_rechargeTurnoverMap;
    }

    public Map<Integer, C_RechargeWithdrawLimit> getC_rechargeWithdrawLimitMap() {
        return c_rechargeWithdrawLimitMap;
    }

    public Map<String, C_Region> getC_regionMap() {
        return c_regionMap;
    }

    public Map<Integer, C_VipClub> getC_vipClubMap() {
        return c_vipClubMap;
    }

    public Map<Integer, C_ReferralReward> getC_referralRewardMap() {
        return c_referralRewardMap;
    }

    public Map<Integer, C_CashBack> getC_cashBackMap() {
        return c_cashBackMap;
    }

    public Map<Integer, C_GameTurnover> getC_gameTurnoverMap() {
        return c_gameTurnoverMap;
    }

    public Map<Integer, C_CustomerService> getC_customerServiceMap() {
        return c_customerServiceMap;
    }

    public Map<Integer, C_BottomMenu> getC_bottomMenuMap() {
        return c_bottomMenuMap;
    }

    public Map<Integer, C_QualityAssurance> getC_qualityAssuranceMap() {
        return c_qualityAssuranceMap;
    }

    public Map<Integer, List<C_InvitationPoster>> getC_invitationPosterMap() {
        return c_invitationPosterMap;
    }

    public Map<Integer, C_FreeGameTurnover> getC_freeGameTurnoverMap() {
        return c_freeGameTurnoverMap;
    }

    public Map<Integer, C_InvitationLinks> getC_invitationLinksMap() {
        return c_invitationLinksMap;
    }

    public Map<String, List<C_ThreePartyLogin>> getC_threePartyLoginMap() {
        return c_threePartyLoginMap;
    }

    public Map<Integer, C_FunctionEnabled> getC_functionEnabledMap() {
        return c_functionEnabledMap;
    }

    public Map<String, C_WebSiteInfo> getC_webSiteInfoMap() {
        return c_webSiteInfoMap;
    }

    public Map<String, List<C_QuickAccess>> getC_quickAccessMap() {
        return c_quickAccessMap;
    }

    public Map<Integer, C_MaintainNotice> getC_maintainNoticeMap() {
        return c_maintainNoticeMap;
    }

    public Map<Integer, C_BigWin> getC_bigWinMap() {
        return c_bigWinMap;
    }

    public Map<Integer, C_RegisterRetrievePop> getC_registerRetrieveMap() {
        return c_registerRetrieveMap;
    }

    public Map<Integer, List<C_Activity>> getC_activityListMap() {
        return c_activityListMap;
    }

    public Map<Integer, C_Activity> getC_activityMap() {
        return c_activityMap;
    }

    public Map<Integer, C_ActivityTag> getC_activityTagMap() {
        return c_activityTagMap;
    }

    public Map<Integer, List<C_LuckSpin>> getC_luckSpinMap() {
        return c_luckSpinMap;
    }

    public Map<Integer, C_DailyContest> getC_dailyContestMap() {
        return c_dailyContestMap;
    }

    public Map<Integer, C_WeeklyRaffle> getC_weeklyRaffleMap() {
        return c_weeklyRaffleMap;
    }

    public Map<String, C_WinTicketNumbers> getC_winTicketNumbersMap() {
        return c_winTicketNumbersMap;
    }

    public Map<String, List<C_WinTicketNumbers>> getC_winTicketNumbersListMap() {
        return c_winTicketNumbersListMap;
    }

    public Map<String, C_RedemptionCode> getC_redemptionCodeMap() {
        return c_redemptionCodeMap;
    }

    public Map<Integer, C_RedEnvelopeRain> getC_redEnvelopeRainDayMap() {
        return c_redEnvelopeRainDayMap;
    }

    public Map<Integer, C_RedEnvelopeRain> getC_redEnvelopeRainWeekMap() {
        return c_redEnvelopeRainWeekMap;
    }

    public Map<Integer, C_RewardBox> getC_rewardBoxMap() {
        return c_rewardBoxMap;
    }

    public Map<Integer, C_MysteryBonus> getC_mysteryBonusMap() {
        return c_mysteryBonusMap;
    }

    public Map<Integer, C_PiggyBank> getC_piggyBankMap() {
        return c_piggyBankMap;
    }

    public Map<Integer, C_ContinuousDeposit> getC_continuousDepositMap() {
        return c_continuousDepositMap;
    }

    public Map<Integer, C_Pwa> getC_pwaMap() {
        return c_pwaMap;
    }

    public Map<Integer, C_Quest> getC_questMap() {
        return c_questMap;
    }

    public Map<Integer, IntList> getC_type2QuestMap() {
        return c_type2QuestMap;
    }

    public Map<Integer, C_DailyRechargePop> getC_dailyRechargePopMap() {
        return c_dailyRechargePopMap;
    }

    public Map<Integer, C_FirstChargePop> getC_firstChargePopMap() {
        return c_firstChargePopMap;
    }

    public Map<Integer, C_GamePop> getC_gamePopMap() {
        return c_gamePopMap;
    }

    public Map<Integer, C_CrazyBox> getC_crazyBoxMap() {
        return c_crazyBoxMap;
    }

    public Map<Integer, C_CrazyBoxQuest> getC_crazyBoxQuestMap() {
        return c_crazyBoxQuestMap;
    }

    public Map<Integer, C_PubMail> getC_bulletinMap() {
        return c_bulletinMap;
    }

    public Map<Integer, C_FirstChargeSignIn> getC_firstChargeSignInMap() {
        return c_firstChargeSignInMap;
    }

    public Map<Integer, List<C_GameKillRate>> getC_gameKillRateMap() {
        return c_gameKillRateMap;
    }

    public Map<Integer, C_RechargeRecover> getC_rechargeRecoverMap() {
        return c_rechargeRecoverMap;
    }

    public Map<Integer, C_WageredRebates> getC_wageredRebatesMap() {
        return c_wageredRebatesMap;
    }

    public Map<Integer, List<C_PubMail>> getC_timingPubMailMap() {
        return c_timingPubMailMap;
    }

    public Map<Integer, C_FirstDepositInviteBonus> getC_firstDepositInviteBonusMap() {
        return c_firstDepositInviteBonusMap;
    }

    public String getBusiness_no() {
        return business_no;
    }
}
