package com.game.hall.mrg;

import com.game.engine.enums.ServerType;
import com.game.engine.struct.ServerInfo;
import com.game.entity.AgentGameInfo;
import com.proto.InnerMessage;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.*;

/**
 * 连接的服务器管理
 */
public class ServerMrg {

    private static final Logger LOGGER = LoggerFactory.getLogger(ServerMrg.class);

    private static final ServerMrg INSTANCE = new ServerMrg();

    public static ServerMrg getInstance() {
        return INSTANCE;
    }

    //所有游戏
    private final Map<ServerType, Map<Integer, ServerInfo>> serversMap = new HashMap<>(5);

    private final Map<Integer, AgentGameInfo> gameInfoMap = new HashMap<>(8);

    public ServerInfo getServerInfo(ServerType serverType, int serverId) {
        final Map<Integer, ServerInfo> serverMap = serversMap.get(serverType);
        if (serverMap != null) {
            return serverMap.get(serverId);
        }
        return null;
    }

    public ServerInfo getServerType(ServerType serverType, long accountId, int serverId) {
        final Map<Integer, ServerInfo> serverInfoMap = getServersMap(serverType);
        if (serverInfoMap.isEmpty()) {
            return null;
        }

        final ServerInfo serverInfo = serverInfoMap.get(serverId);
        if (serverInfo != null) {
            return serverInfo;
        }

        final int index = (int) (accountId % serverInfoMap.size());
        final List<ServerInfo> serverInfos = new ArrayList<>(serverInfoMap.values());
        return serverInfos.get(index);
    }

    public Map<Integer, ServerInfo> getServersMap(ServerType serverType) {
        return serversMap.get(serverType);
    }

    private void addServerInfo(ServerType serverType, ServerInfo serverInfo) {
        Map<Integer, ServerInfo> serverMap = serversMap.putIfAbsent(serverType, new TreeMap<>(Comparator.comparingInt(o -> o)));
        if (serverMap == null) {
            serverMap = serversMap.get(serverType);
        }
        serverMap.put(serverInfo.getId(), serverInfo);
    }

    public void updateServer(InnerMessage.InnerServerInfo info) {
        boolean isRegister = false;
        ServerInfo serverInfo = getServerInfo(ServerType.valueOf(info.getType()), info.getId());
        if (serverInfo == null) {
            serverInfo = new ServerInfo();
            serverInfo.setId(info.getId());
            serverInfo.setServerType(info.getType());
            addServerInfo(ServerType.valueOf(info.getType()), serverInfo);
            isRegister = true;
        }
        serverInfo.setIp(info.getIp());
        serverInfo.setPort(info.getPort());
        serverInfo.setGameState(info.getGameState());
        serverInfo.setVersion(info.getVersion());
        serverInfo.setContent(info.getContent());
        serverInfo.setPower(info.getPower());
        serverInfo.setOnline(info.getOnline());
        serverInfo.setHttpPort(info.getHttpPort());
        serverInfo.setName(info.getName());
        serverInfo.setWwwip(info.getWwwIp());
        if (isRegister) {
            LOGGER.info("register server，{}", serverInfo.toString());
        }
    }

    public AgentGameInfo addAgentGameInfo(int gameId) {
        AgentGameInfo gameInfo = this.gameInfoMap.putIfAbsent(gameId, new AgentGameInfo());
        if (gameInfo == null) {
            gameInfo = gameInfoMap.get(gameId);
            gameInfo.setGameId(gameId);
            gameInfo.setOnline(1);
        }
        return gameInfo;
    }

    public AgentGameInfo getAgentGameInfo(int gameId) {
        return this.gameInfoMap.get(gameId);
    }
}
