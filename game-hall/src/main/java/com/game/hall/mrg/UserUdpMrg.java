package com.game.hall.mrg;

import com.game.engine.io.conf.ConstantConfig;
import com.game.engine.net.LogicEvent;
import com.game.engine.utils.MsgUtil;
import io.netty.channel.Channel;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Iterator;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

public class UserUdpMrg {

    private static final Logger LOGGER = LoggerFactory.getLogger(UserUdpMrg.class);

    private static final UserUdpMrg INSTANCE = new UserUdpMrg();

    public static UserUdpMrg getInstance() {
        return INSTANCE;
    }

    private final Map<Long, Channel> udpUserSession = new ConcurrentHashMap<>();

    public void removeUdpSession(long sessionId) {
        udpUserSession.remove(sessionId);
    }

    public Map<Long, Channel> getUdpUserSession() {
        return udpUserSession;
    }

    public void addUdpSession(long sessionId, Channel session) {
        udpUserSession.put(sessionId, session);
    }

    public Channel getHttpSession(long sessionId) {
        return udpUserSession.get(sessionId);
    }

    public void sendClient(Channel userSession, LogicEvent event, long pid) {
        final int msgId = event.getIntParamB();
        final byte[] bytes = (byte[]) event.getParamA();// 消息内容
        MsgUtil.responseHttp(bytes, msgId, userSession);
//        LOGGER.warn("userId：{}，send msg to client：{}", pid, msgId);
    }

    public void timerCheckUdpActive() {
        try {
            final Iterator<Map.Entry<Long, Channel>> iterator = udpUserSession.entrySet().iterator();
            while (iterator.hasNext()) {
                final Map.Entry<Long, Channel> entry = iterator.next();
                final Channel session = entry.getValue();
                if (!session.isActive()) {
                    iterator.remove();
                    LOGGER.warn("timerCheckUpd remove sessionId；{}", entry.getKey());
                }
            }
        } catch (Exception e) {
            LOGGER.error("", e);
        }
    }


}
