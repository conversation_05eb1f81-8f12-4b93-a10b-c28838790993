package com.game.hall.mrg.quest;

import com.game.c_entity.merchant.C_Quest;
import com.game.engine.script.ScriptLoader;
import com.game.entity.player.Player;
import com.game.entity.player.quest.*;
import com.game.enums.FunctionEnabled;
import com.game.enums.QuestGoalType;
import com.game.hall.mrg.DataHallMrg;
import com.game.hall.mrg.MerchantData;
import com.game.hall.mrg.quest.goalhandler.*;
import com.game.hall.mrg.quest.typehandler.AbsQuestTypeHandler;
import com.game.hall.mrg.quest.typehandler.DailyQuestHandler;
import com.game.hall.mrg.quest.typehandler.WeeklyQuestHandler;
import com.game.hall.script.IFunctionEnabledScript;
import com.proto.CommonMessage;
import com.proto.QuestMessage;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.LinkedHashMap;
import java.util.Map;

public class QuestMrg {
    private static final Logger LOGGER = LoggerFactory.getLogger(QuestMrg.class);

    private static final QuestMrg instance = new QuestMrg();

    private final NoneGoalHandler defaultHandler = new NoneGoalHandler();
    private final Map<Integer, AbsGoalHandler> goalHandlerMap = new LinkedHashMap<>();

    private final Map<Integer, AbsQuestTypeHandler> questTypeHandlerMap = new LinkedHashMap<>();

    public static QuestMrg getInstance() {
        return instance;
    }

    public void initRegisterGoalHandler() {
        registerHandler(defaultHandler);
        registerHandler(new LoginDaysHandler());
        registerHandler(new RechargeHandler());
        registerHandler(new SingleRechargeHandler());
        registerHandler(new WageredAmountHandler());
        registerHandler(new WageredTimesHandler());
    }

    private void registerHandler(AbsGoalHandler goalHandler) {
        assert !goalHandlerMap.containsKey(goalHandler.goalType().getNumber()) : goalHandler.goalType();
        goalHandlerMap.put(goalHandler.goalType().getNumber(), goalHandler);
    }

    public AbsGoalHandler getGoalHandler(int goalType) {
        return goalHandlerMap.getOrDefault(goalType, defaultHandler);
    }


    public void initRegisterQuestHandler() {
        registerQuestTypeHandler(new DailyQuestHandler());
        registerQuestTypeHandler(new WeeklyQuestHandler());
    }

    private void registerQuestTypeHandler(AbsQuestTypeHandler goalHandler) {
        assert !questTypeHandlerMap.containsKey(goalHandler.getType().getNumber()) : goalHandler.getType();
        questTypeHandlerMap.put(goalHandler.getType().getNumber(), goalHandler);
    }

    public AbsQuestTypeHandler getQuestTypeHandler(int questType) {
        return questTypeHandlerMap.get(questType);
    }


    // region 注册目标
    public void registerGoals(Player player, SingleQuestInfo singleQuestInfo, GoalCallback callback) {
        final C_Quest c_quest = getQuest(player, singleQuestInfo.getQuestId());
        if (c_quest == null) {
            return;
        }
        if (!goalHandlerMap.containsKey(c_quest.getGoalType())) {
            LOGGER.warn(String.format("unsupported goal type, questId: %d, goalType: %s",
                    singleQuestInfo.getQuestId(), c_quest.getGoalType()));
            return;
        }
        final QuestGoalComponent questGoalComponent = player.getQuestGoalComponent();
        if (questGoalComponent.getHandlerContextMap().containsKey(singleQuestInfo.getQuestId())) {
//            LOGGER.warn("repeated register quest goal：{}", singleQuestInfo.getQuestId());
            return;
        }

        final WrappedGoalContext wrappedGoalContext = new WrappedGoalContext(singleQuestInfo, c_quest.getGoalType(), callback);
        questGoalComponent.getHandlerContextMap().put(singleQuestInfo.getQuestId(), wrappedGoalContext);
    }

    public void removeGoals(Player player, SingleQuestInfo singleQuestInfo) {
        final QuestGoalComponent questGoalComponent = player.getQuestGoalComponent();
        questGoalComponent.getHandlerContextMap().remove(singleQuestInfo.getQuestId());
    }

    public C_Quest getQuest(Player player, int questId) {
        final MerchantData merchantData = DataHallMrg.getInstance().findMerchantData(this.getClass().getSimpleName(), player.getBusiness_no());
        if (merchantData == null) {
            return null;
        }
        return merchantData.findC_Quest(this.getClass().getSimpleName(), questId);
    }

    /**
     * 注册玩家的所有任务目标的监听器
     */
    public void registerGoalListeners(Player player) {
        final boolean functionEnabled = ScriptLoader.getInstance().functionScript("FunctionEnabledScript", (IFunctionEnabledScript script)
                -> script.functionEnabled(player.getBusiness_no(), FunctionEnabled.Quest.getType()));
        if (!functionEnabled) {
            return;
        }

        final QuestInfo questInfo = player.getQuestInfo();
        for (SingleQuestInfo singleQuestInfo : questInfo.getQuestInfoMap().values()) {
            final AbsQuestTypeHandler questTypeHandler = getQuestTypeHandler(singleQuestInfo.getQuestType());
            final AbsGoalHandler goalHandler = getGoalHandler(singleQuestInfo.getGoalType());

            // 累计型的任务就算完成了仍然需要注册该目标
            final boolean needRegister = singleQuestInfo.getState() == QuestState.ACCEPTED
                    || goalHandler.isAccumulatedType();
            if (needRegister) {
                registerGoals(player, singleQuestInfo, questTypeHandler);
            }
        }
    }

    /**
     * 事件触发更新一类任务
     */
    public void updateGoal(Player player, QuestGoalType goalType, Blackboard blackboard) {
        final AbsGoalHandler goalHandler = goalHandlerMap.get(goalType.getNumber());
        if (goalHandler == null) {
            return;
        }
        final QuestGoalComponent questGoalComponent = player.getQuestGoalComponent();
        if (questGoalComponent.getHandlerContextMap().isEmpty()) {
            return;
        }

        for (WrappedGoalContext context : questGoalComponent.getHandlerContextMap().values()) {
            if (context.goalType != goalType.getNumber()) {
                continue;
            }
            goalHandler.update(player, context.singleQuestInfo, context.callback, blackboard);
        }
    }

    public QuestMessage.QuestInfo buildQuestInfo(Player player, SingleQuestInfo singleQuestInfo) {
        final QuestMessage.QuestInfo.Builder questInfo = QuestMessage.QuestInfo.newBuilder();
        final C_Quest c_quest = getQuest(player, singleQuestInfo.getQuestId());
        if (c_quest == null) {
            return null;
        }
        final C_Quest.DescInfo descInfo = c_quest.getDescInfoMap().get(player.getLanguage());
        if (descInfo == null) {
            return null;
        }
        if (!c_quest.isOpen()) {
            return null;
        }

        final C_Quest.QuestCondition questCondition = c_quest.getQuestCondition();
        final C_Quest.RewardCurrency rewardCurrency = c_quest.getRewardCurrency();
        questInfo.setQuestId(c_quest.getQuestId())
                .setQuestType(c_quest.getQuestType())
                .setIcon(c_quest.getIcon())
                .setGoalType(c_quest.getGoalType())
                .setState(singleQuestInfo.getState())
                .setProgressive(singleQuestInfo.getProgressive())
                .setFinishedTime(singleQuestInfo.getFinishedTime())
                .setConditionInfo(buildConditionInfo(questCondition))
                .setRewards(buildDItemShow(rewardCurrency.currencyId, rewardCurrency.amount))
                .setQuestName(descInfo.getQuestName())
                .setDesc(descInfo.getDesc());
        if (c_quest.getGameTarget() != null) {
            questInfo.setQuestTarget(buildQuestTarget(c_quest.getGameTarget()));
        }

        return questInfo.build();
    }

    private QuestMessage.QuestTarget buildQuestTarget(C_Quest.GameTarget gameTarget) {
        final QuestMessage.QuestTarget.Builder questTarget = QuestMessage.QuestTarget.newBuilder();
        questTarget.setChannelId(gameTarget.channelId);
        return questTarget.build();
    }

    private QuestMessage.ConditionInfo buildConditionInfo(C_Quest.QuestCondition questCondition) {
        return QuestMessage.ConditionInfo.newBuilder()
                .setTotalProgressive(questCondition.getTotalProgressive())
                .setParam1(questCondition.getParam1())
                .setParam2(questCondition.getParam2())
                .addAllParam3(questCondition.getParam3())
                .build();
    }

    public CommonMessage.DItemShow buildDItemShow(int currencyId, double amount) {
        return CommonMessage.DItemShow.newBuilder()
                .setItemId(currencyId)
                .setNum(amount)
                .build();
    }
}
