package com.game.hall.mrg;

import com.game.enums.ErrorCode;
import com.game.user.UserSession;
import com.proto.MIDMessage;
import com.proto.TcpMessage;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Iterator;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.ConcurrentHashMap;

public class UserTcpMrg {

    private static final Logger LOGGER = LoggerFactory.getLogger(UserTcpMrg.class);

    private static final UserTcpMrg INSTANCE = new UserTcpMrg();

    public static UserTcpMrg getInstance() {
        return INSTANCE;
    }

    /**
     * 所有用户：id,user
     */
    private final Map<Long, UserSession> userMap = new ConcurrentHashMap<>();

    /**
     * 所有会话：id,session
     */
    private final Map<Long, UserSession> sessionMap = new ConcurrentHashMap<>();

    public void onUserConnected(UserSession user) {
        if (user.getSessionID() > 0 && !sessionMap.containsKey(user.getSessionID())) {
            sessionMap.put(user.getSessionID(), user);
//            LOGGER.warn("客户端，连接人数，{}", sessionMap.size());
        }
    }

    public UserSession getUserBySessionID(long id) {
        return sessionMap.get(id);
    }

    public UserSession getUserByAccountId(long id) {
        return userMap.get(id);
    }

    /**
     * 用户注册登录
     *
     * @param newUser
     */
    public void addOnlineUser(UserSession newUser) {
        if (newUser.getUserSession() != null) {
            final UserSession oldUser = this.userMap.get(newUser.getAccountId());
            //被其他用户顶替
            if (oldUser != null && oldUser.getUserSession() != null &&
                    !Objects.equals(newUser.getBrowserId(), oldUser.getBrowserId()) && !oldUser.getUserSession().equals(newUser.getUserSession())) {
                final TcpMessage.ResTcpSysErrorMessage.Builder error = TcpMessage.ResTcpSysErrorMessage.newBuilder();
                error.setMsgID(MIDMessage.MID.ResTcpSysError_VALUE)
                        .setError(ErrorCode.Account_Replaced.getCode());
                oldUser.sendToUser(error.build());
                LOGGER.warn("userId：{}，send msg to client：{}", oldUser.getAccountId(), error.getMsgID());

                LOGGER.warn("account replace，newUser：{} -> oldUser：{}", newUser.getSessionID(), oldUser.getSessionID());
                oldUser.destroy(false);
            }
            userMap.put(newUser.getAccountId(), newUser);
//            LOGGER.info("login userId：{}， online num：{}", newUser.getAccountId(), userMap.size());
        }
    }

    /**
     * 整个登出
     *
     * @param user
     */
    public void onUserLogout(UserSession user) {
        sessionMap.remove(user.getSessionID());
//        LOGGER.warn("客户端，连接人数，{}", sessionMap.size());
        if (user.getAccountId() > 0) {
            this.userMap.remove(user.getAccountId());
//            LOGGER.info("logout userId：{}， online num：{}", user.getAccountId(), userMap.size());
        }
    }

    public Map<Long, UserSession> getSessionMap() {
        return sessionMap;
    }

    public void timerCheckSession() {
        final Iterator<Map.Entry<Long, UserSession>> iterator = sessionMap.entrySet().iterator();
        while (iterator.hasNext()) {
            final Map.Entry<Long, UserSession> entry = iterator.next();
            final UserSession userSession = entry.getValue();
            if (!userSession.getUserSession().isActive()) {
                iterator.remove();
                LOGGER.warn("timerCheckSession remove userSessionId；{}", userSession.getAccountId());
            }
        }
    }

}
