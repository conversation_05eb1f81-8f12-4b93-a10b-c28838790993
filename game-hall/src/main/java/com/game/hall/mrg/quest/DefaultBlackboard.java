package com.game.hall.mrg.quest;


import io.netty.util.AttributeKey;

import javax.annotation.Nonnull;
import javax.annotation.Nullable;
import java.util.HashMap;
import java.util.IdentityHashMap;
import java.util.Map;
import java.util.stream.Stream;

/**
 * 暂时决定使用{@link IdentityHashMap}，待后期测试看看是否比{@link HashMap}高效
 */
public class DefaultBlackboard implements Blackboard {

    private final Map<AttributeKey<?>, Object> map;

    public DefaultBlackboard() {
        map = new IdentityHashMap<>(8);
    }

    public DefaultBlackboard(int expectedSize) {
        map = new IdentityHashMap<>(expectedSize);
    }

    public DefaultBlackboard(DefaultBlackboard src) {
        this.map = new IdentityHashMap<>(src.map);
    }

    @SuppressWarnings("unchecked")
    @Nullable
    @Override
    public <T> T get(@Nonnull AttributeKey<T> key) {
        return (T) map.get(key);
    }

    @SuppressWarnings("unchecked")
    @Override
    public <T> T getOrDefault(@Nonnull AttributeKey<T> key, T defaultValue) {
        return (T) map.getOrDefault(key, defaultValue);
    }

    @SuppressWarnings("unchecked")
    @Override
    public <T> T set(@Nonnull AttributeKey<T> key, @Nullable T value) {
        return (T) map.put(key, value);
    }

    @SuppressWarnings("unchecked")
    @Override
    public <T> T setIfAbsent(@Nonnull AttributeKey<T> key, @Nullable T value) {
        return (T) map.putIfAbsent(key, value);
    }

    @Override
    public boolean containsKey(@Nonnull AttributeKey<?> key) {
        return map.containsKey(key);
    }

    @SuppressWarnings("unchecked")
    @Nullable
    @Override
    public <T> T remove(@Nonnull AttributeKey<T> key) {
        return (T) map.remove(key);
    }

    @Override
    public boolean isEmpty() {
        return map.isEmpty();
    }

    @Override
    public int size() {
        return map.size();
    }

    @Override
    public Stream<AttributeKey<?>> keyStream() {
        return map.keySet().stream();
    }

    @Override
    public Stream<Map.Entry<AttributeKey<?>, Object>> entryStream() {
        return map.entrySet().stream();
    }

    @Override
    public void resetForRestart() {
        map.clear();
    }

    @Override
    public void resetPoolable() {
        map.clear();
    }

    @Override
    public void putAll(@Nonnull Map<AttributeKey<?>, Object> map) {
        this.map.putAll(map);
    }

    @Override
    public void putAll(@Nonnull Blackboard other) {
        if (other instanceof DefaultBlackboard) {
            this.map.putAll(((DefaultBlackboard) other).map);
        } else {
            Blackboard.super.putAll(other);
        }
    }

}