package com.game.hall.mrg.quest.goalhandler;


import com.game.entity.player.Player;
import com.game.entity.player.quest.GoalCallback;
import com.game.entity.player.quest.SingleQuestInfo;
import com.game.enums.QuestGoalType;
import com.game.hall.mrg.quest.Blackboard;

public class LoginDaysHandler extends AbsGoalHandler {


    @Override
    public QuestGoalType goalType() {
        return QuestGoalType.DailyLogin;
    }

    @Override
    public boolean update(Player player, SingleQuestInfo singleQuestInfo, GoalCallback callback, Blackboard blackboard) {
        incProgressive(player, singleQuestInfo, 1, callback);
        return true;
    }

}