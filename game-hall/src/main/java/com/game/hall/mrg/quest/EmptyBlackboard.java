package com.game.hall.mrg.quest;

import io.netty.util.AttributeKey;

import javax.annotation.Nonnull;
import javax.annotation.Nullable;
import java.util.Map;
import java.util.stream.Stream;

class EmptyBlackboard implements Blackboard {

    static final EmptyBlackboard INSTANCE = new EmptyBlackboard();

    private EmptyBlackboard() {
    }

    @Nullable
    @Override
    public <T> T get(@Nonnull AttributeKey<T> key) {
        return null;
    }

    @Override
    public <T> T getOrDefault(@Nonnull AttributeKey<T> key, T defaultValue) {
        return defaultValue;
    }

    @Override
    public <T> T set(@Nonnull AttributeKey<T> key, @Nullable T value) {
        throw new UnsupportedOperationException();
    }

    @Override
    public <T> T setIfAbsent(@Nonnull AttributeKey<T> key, @Nullable T value) {
        throw new UnsupportedOperationException();
    }

    @Nullable
    @Override
    public <T> T remove(@Nonnull AttributeKey<T> key) {
        throw new UnsupportedOperationException();
    }

    @Override
    public boolean containsKey(@Nonnull AttributeKey<?> key) {
        return false;
    }

    @Override
    public boolean isEmpty() {
        return true;
    }

    @Override
    public int size() {
        return 0;
    }

    @Override
    public Stream<AttributeKey<?>> keyStream() {
        return Stream.empty();
    }

    @Override
    public Stream<Map.Entry<AttributeKey<?>, Object>> entryStream() {
        return Stream.empty();
    }

    @Override
    public void resetForRestart() {

    }

    @Override
    public void resetPoolable() {

    }

    @Override
    public void copyTo(Blackboard out) {

    }

}