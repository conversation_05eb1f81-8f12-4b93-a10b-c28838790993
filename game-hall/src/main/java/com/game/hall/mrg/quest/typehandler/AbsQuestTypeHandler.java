package com.game.hall.mrg.quest.typehandler;

import com.game.c_entity.merchant.C_Quest;
import com.game.dao.player.PlayerDao;
import com.game.dao.quest.QuestNoteDao;
import com.game.engine.script.ScriptLoader;
import com.game.engine.utils.TimeUtil;
import com.game.engine.utils.UniqueIDGenerator;
import com.game.entity.player.Player;
import com.game.entity.player.quest.GoalCallback;
import com.game.entity.player.quest.QuestInfo;
import com.game.entity.player.quest.QuestNote;
import com.game.entity.player.quest.SingleQuestInfo;
import com.game.enums.*;
import com.game.hall.main.HallServer;
import com.game.hall.mrg.DataHallMrg;
import com.game.hall.mrg.MerchantData;
import com.game.hall.mrg.quest.QuestMrg;
import com.game.hall.mrg.quest.QuestState;
import com.game.hall.script.IFunctionEnabledScript;
import com.game.hall.script.IQuestScript;
import com.game.manager.EntityDaoMrg;
import it.unimi.dsi.fastutil.ints.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;

public abstract class AbsQuestTypeHandler implements GoalCallback {

    private final Logger logger = LoggerFactory.getLogger(AbsQuestTypeHandler.class);
    //protected static ObjectPool<ArrayList<SingleQuestInfo>> questListPool = new SingleObjectPool<>(ArrayList::new, ArrayList::clear);


    /**
     * 任务处理器类型
     */
    public abstract QuestType getType();

    /**
     * 任务进度变化更新
     */
    public void onGoalInfoChanged(Player player, SingleQuestInfo singleQuestInfo, boolean newComplete) {
        // 消耗道具类任务不能自动切入完成状态，服务器不应该监听不能自动完成的任务目标的事件
        final QuestInfo questInfo = player.getQuestInfo();
        if (newComplete) {
            final C_Quest c_quest = QuestMrg.getInstance().getQuest(player, singleQuestInfo.getQuestId());
            singleQuestInfo.setState(QuestState.FINISHED);
            if (c_quest.getReceive() == 2) {
                singleQuestInfo.setState(QuestState.RECEIVED);
            }
            singleQuestInfo.setFinishedTime(TimeUtil.currentTimeMillis());

            if (c_quest.getReceive() == 2) {
                ScriptLoader.getInstance().consumerScript("QuestScript",
                        (IQuestScript script) -> script.questReceive(player, c_quest, singleQuestInfo));
            }
        }
        EntityDaoMrg.getInstance().getDao(PlayerDao.class).questDao.updateQuest(player.getPlayerId(), questInfo,
                IntLists.EMPTY_LIST, IntLists.EMPTY_LIST, IntLists.singleton(singleQuestInfo.getQuestId()));

        // 移除目标
        if (newComplete) {
            QuestMrg.getInstance().removeGoals(player, singleQuestInfo);
        }
    }

    /**
     * 上线或升级等时间节点
     * 检查此类型的所有任务配置，是否需要自动接取
     */
    public void checkAndAcceptQuest(Player player) {
        final boolean functionEnabled = ScriptLoader.getInstance().functionScript("FunctionEnabledScript", (IFunctionEnabledScript script)
                -> script.functionEnabled(player.getBusiness_no(), FunctionEnabled.Quest.getType()));
        if (!functionEnabled) {
            return;
        }

        final MerchantData merchantData = DataHallMrg.getInstance().findMerchantData(this.getClass().getSimpleName(), player.getBusiness_no());
        if (merchantData == null) {
            return;
        }

        final QuestInfo questInfo = player.getQuestInfo();
        final IntList questIdList = merchantData.getC_type2QuestMap().get(getType().getNumber());
        if (questIdList == null || questIdList.isEmpty()) {
            return;
        }

        Int2ObjectMap<SingleQuestInfo> allInsertQuestList = null;
        for (int questId : questIdList) {
            final C_Quest c_quest = merchantData.findC_Quest(this.getClass().getSimpleName(), questId);
            if (c_quest == null) {
                continue;
            }
            if (!c_quest.isOpen()) {
                continue;
            }
            if (questInfo.getQuestInfoMap().containsKey(c_quest.getQuestId())) {
                continue;//进行中的任务
            }
            if (!checkCanAccept(player, questInfo, c_quest)) {
                continue;
            }
            if (allInsertQuestList == null) {
                allInsertQuestList = new Int2ObjectOpenHashMap<>();
            }
            allInsertQuestList.putAll(createQuest(player, c_quest));
        }

        if (allInsertQuestList != null) {
            acceptCommon(player, allInsertQuestList);
        }
    }


    /**
     * 检查对应类型的任务是否可接，默认不可接，对应处理器可覆写此方法
     */
    protected boolean checkCanAccept(Player player, QuestInfo questInfo, C_Quest c_quest) {
        return false;
    }

    /**
     * 接取任务公共方法，需要提前创建好任务数据
     */
    private void acceptCommon(Player player, Int2ObjectMap<SingleQuestInfo> insertQuestList) {
        final QuestInfo questInfo = player.getQuestInfo();
        questInfo.getQuestInfoMap().putAll(insertQuestList);

        // 入库
        final IntList insertList = new IntArrayList(insertQuestList.keySet());
        EntityDaoMrg.getInstance().getDao(PlayerDao.class).questDao
                .updateQuest(player.getPlayerId(), questInfo,
                        insertList, IntLists.EMPTY_LIST, IntLists.EMPTY_LIST);

        // 注册目标
        registerGoal(player, insertQuestList);

        // 接取任务事件、记录日志
    }

    /**
     * 放弃任务公共逻辑
     */
    protected void giveUpQuest(Player player, List<SingleQuestInfo> removableQuestList) {
        if (removableQuestList.isEmpty()) {
            return;
        }
        final QuestInfo questInfo = player.getQuestInfo();
        // 更新数据库
        {
            final IntArrayList removedQuests = new IntArrayList();
            for (SingleQuestInfo singleQuestInfo : removableQuestList) {
                final IntList removeList = removeQuest(player, singleQuestInfo.getQuestId());
                removedQuests.addAll(removeList);

                final MerchantData merchantData = DataHallMrg.getInstance().findMerchantData(this.getClass().getSimpleName(), player.getBusiness_no());
                if (merchantData == null) {
                    continue;
                }

                final C_Quest c_quest = merchantData.findC_Quest(this.getClass().getSimpleName(), singleQuestInfo.getQuestId());
                if (c_quest == null) {
                    continue;
                }

                if (singleQuestInfo.getState() == QuestState.ACCEPTED || singleQuestInfo.getState() == QuestState.RECEIVED) {
                    continue;
                }

                //任务记录
                final UniqueIDGenerator uniqueIDGenerator = HallServer.getInstance().getUniqueIDGenerator();
                final QuestNote questNote = new QuestNote(uniqueIDGenerator.nextId());
                questNote.setBusiness_no(player.getBusiness_no());
                questNote.setPlayerId(player.getPlayerId());
                questNote.setQuestId(singleQuestInfo.getQuestId());
                questNote.setState(singleQuestInfo.getState());
                questNote.setFinishedTime(singleQuestInfo.getFinishedTime());
                EntityDaoMrg.getInstance().getDao(QuestNoteDao.class).insert(questNote);
            }
            EntityDaoMrg.getInstance().getDao(PlayerDao.class).questDao
                    .updateQuest(player.getPlayerId(),
                    questInfo, IntLists.EMPTY_LIST, removedQuests, IntLists.EMPTY_LIST);
        }
    }

    /**
     * 创建任务，初始化数据
     */
    protected Int2ObjectMap<SingleQuestInfo> createQuest(Player player, C_Quest c_quest) {
        final Int2ObjectMap<SingleQuestInfo> insertQuestMap = new Int2ObjectOpenHashMap<>();

        final SingleQuestInfo singleQuestInfo = initQuest(player, c_quest);
        insertQuestMap.put(singleQuestInfo.getQuestId(), singleQuestInfo);
        return insertQuestMap;
    }

    /**
     * 初始化任务信息
     */
    public SingleQuestInfo initQuest(Player player, C_Quest c_quest) {
        final SingleQuestInfo singleQuestInfo = new SingleQuestInfo();
        singleQuestInfo.setQuestId(c_quest.getQuestId());
        singleQuestInfo.setQuestType(c_quest.getQuestType());
        singleQuestInfo.setGoalType(c_quest.getGoalType());
        singleQuestInfo.setState(QuestState.ACCEPTED);
        initOther(player, singleQuestInfo, c_quest);
        return singleQuestInfo;
    }

    /**
     * 具体类型的初始化工作 -- 任务类型可能也有初始化逻辑
     */
    protected void initOther(Player player, SingleQuestInfo singleQuestInfo, C_Quest c_quest) {

    }

    /**
     * 移除任务
     */
    protected IntList removeQuest(Player player, int questId) {
        final QuestInfo questInfo = player.getQuestInfo();
        if (!questInfo.getQuestInfoMap().containsKey(questId)) {
            return IntLists.EMPTY_LIST;
        }

        final IntList removeList = new IntArrayList();
        final SingleQuestInfo removedQuest = questInfo.getQuestInfoMap().remove(questId);
        QuestMrg.getInstance().removeGoals(player, removedQuest);
        removeList.add(questId);
        return removeList;
    }

    /**
     * 注册新添加的任务目标
     */
    protected void registerGoal(Player player, Int2ObjectMap<SingleQuestInfo> insertQuestMap) {
        for (SingleQuestInfo singleQuestInfo : insertQuestMap.values()) {
            QuestMrg.getInstance().registerGoals(player, singleQuestInfo, this);
        }
    }

}
