package com.game.hall.mrg.quest.typehandler;

import com.game.c_entity.merchant.C_Quest;
import com.game.dao.player.PlayerDao;
import com.game.engine.script.ScriptLoader;
import com.game.entity.player.Player;
import com.game.entity.player.quest.QuestInfo;
import com.game.entity.player.quest.SingleQuestInfo;
import com.game.enums.FunctionEnabled;
import com.game.enums.QuestType;
import com.game.hall.script.IFunctionEnabledScript;
import com.game.manager.EntityDaoMrg;

import java.util.ArrayList;

public class WeeklyQuestHandler extends AbsQuestTypeHandler {


    @Override
    public QuestType getType() {
        return QuestType.WEEKLY;
    }

    @Override
    protected boolean checkCanAccept(Player player, QuestInfo questInfo, C_Quest c_quest) {
        if (player.getVipClub().getVipLevel() < c_quest.getVipLevelLimit()) {
            return false;
        }
        return true;
    }

    /**
     * 每周任务信息重置
     */
    public void weeklyReset(Player player) {
        final boolean functionEnabled = ScriptLoader.getInstance().functionScript("FunctionEnabledScript", (IFunctionEnabledScript script)
                -> script.functionEnabled(player.getBusiness_no(), FunctionEnabled.Quest.getType()));
        if (!functionEnabled) {
            return;
        }

        final QuestInfo questInfo = player.getQuestInfo();
        final ArrayList<SingleQuestInfo> dailyQuestList = new ArrayList<>();
        // 获取所有的日常任务
        for (SingleQuestInfo info : questInfo.getQuestInfoMap().values()) {
            if (info.getQuestType() == QuestType.WEEKLY.getNumber()) {
                dailyQuestList.add(info);
            }
        }

        // 放弃所有周任务
        giveUpQuest(player, dailyQuestList);
//        questListPool.returnOne(dailyQuestList);

        // 重新接取每周任务
        checkAndAcceptQuest(player);
    }

}