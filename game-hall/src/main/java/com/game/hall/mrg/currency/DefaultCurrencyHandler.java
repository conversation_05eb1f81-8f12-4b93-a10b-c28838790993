package com.game.hall.mrg.currency;

import com.game.engine.math.BigDecimalUtils;
import com.game.entity.player.Player;
import com.game.enums.ErrorCode;
import com.game.enums.RewardReason;
import com.game.enums.SpendReason;
import com.game.hall.mrg.player.PlayerMrg;
import com.game.hall.script.CurrencyHandler;

/**
 * 通用货币处理器
 */
public class DefaultCurrencyHandler implements CurrencyHandler {

    private final int currencyType;

    DefaultCurrencyHandler(int currencyType) {
        this.currencyType = currencyType;
    }

    @Override
    public int getCurrencyType() {
        return currencyType;
    }

    @Override
    public double getValue(Player player) {
        return player.getCurrencyMap().getOrDefault(getCurrencyType(), 0d);
    }

    @Override
    public void setValueAndReport(Player player, double value, CurrencyReportHandler reportHandler) {
        player.getCurrencyMap().put(getCurrencyType(), value);
        reportHandler.add(currencyType);
    }

    @Override
    public ErrorCode spendTest(Player player, double value) {
        if (value < 0) {
            return ErrorCode.Currency_Num_Negative_Error;
        }
        // 我们没负数
        return getValue(player) >= value ? ErrorCode.Success : ErrorCode.Currency_Not_Enough_Error;
    }

    @Override
    public ErrorCode spendNoTest(Player player, double value, CurrencyReportHandler reportHandler, SpendReason reason) {
        if (value <= 0) {
            return ErrorCode.Success;
        }
        double beforeValue = getValue(player);
        double afterValue = BigDecimalUtils.sub(beforeValue, value, 9);
        if (afterValue < 0) {
            afterValue = 0;
        }
        setValueAndReport(player, afterValue, reportHandler);

        if (String.valueOf(this.currencyType).length() == 4) {
            final double bonus = player.getCurrencyMap().getOrDefault(this.currencyType * 10, 0d);
            beforeValue = BigDecimalUtils.add(bonus, beforeValue, 9);
            afterValue = BigDecimalUtils.add(bonus, afterValue, 9);
        } else {
            final double cash = player.getCurrencyMap().getOrDefault(this.currencyType / 10, 0d);
            beforeValue = BigDecimalUtils.add(cash, beforeValue, 9);
            afterValue = BigDecimalUtils.add(cash, afterValue, 9);
        }

        //记录日志
        int currencyId = 0;
        if (String.valueOf(this.currencyType).length() == 4) {//cash
            currencyId = this.currencyType;
        } else {//bonus
            currencyId = this.currencyType / 10;
        }
        PlayerMrg.getInstance().playerGoldChangeLog(player, currencyId, beforeValue, -value, afterValue, reason);
        return ErrorCode.Success;
    }

    @Override
    public ErrorCode rewardTest(Player player, double value) {
        if (value < 0) {
            return ErrorCode.Currency_Num_Negative_Error;
        }
        // 暂时没特殊逻辑 (我们没负数)
        return BigDecimalUtils.add(getValue(player), value, 9) > 0 ? ErrorCode.Success : ErrorCode.Currency_Num_Negative_Error;
    }

    @Override
    public ErrorCode rewardNoTest(Player player, double value, CurrencyReportHandler reportHandler, RewardReason reason) {
        if (value <= 0) {
            return ErrorCode.Success;
        }
        double beforeValue = getValue(player);
        double afterValue = Math.min(CURRENCY_MAX_VALUE, BigDecimalUtils.add(beforeValue, value, 9));
        setValueAndReport(player, afterValue, reportHandler);

        if (String.valueOf(this.currencyType).length() == 4) {
            final double bonus = player.getCurrencyMap().getOrDefault(this.currencyType * 10, 0d);
            beforeValue = BigDecimalUtils.add(bonus, beforeValue, 9);
            afterValue = BigDecimalUtils.add(bonus, afterValue, 9);
        } else {
            final double cash = player.getCurrencyMap().getOrDefault(this.currencyType / 10, 0d);
            beforeValue = BigDecimalUtils.add(cash, beforeValue, 9);
            afterValue = BigDecimalUtils.add(cash, afterValue, 9);
        }

        //记录日志
        int currencyId = 0;
        if (String.valueOf(this.currencyType).length() == 4) {
            currencyId = this.currencyType;
        } else {
            currencyId = this.currencyType / 10;
        }
        PlayerMrg.getInstance().playerGoldChangeLog(player, currencyId, beforeValue, value, afterValue, reason);
        return ErrorCode.Success;
    }

}