package com.game.hall.mrg.quest.goalhandler;


import com.game.c_entity.merchant.C_Quest;
import com.game.entity.player.Player;
import com.game.entity.player.quest.GoalCallback;
import com.game.entity.player.quest.SingleQuestInfo;
import com.game.enums.QuestGoalType;
import com.game.hall.mrg.DataHallMrg;
import com.game.hall.mrg.MerchantData;
import com.game.hall.mrg.quest.Blackboard;
import com.game.hall.mrg.quest.DefaultBlackboard;
import io.netty.util.AttributeKey;

public class WageredTimesHandler extends AbsGoalHandler {


    @Override
    public QuestGoalType goalType() {
        return QuestGoalType.WageredTimes;
    }

    @Override
    public boolean isAccumulatedType() {
        return true;
    }

    @Override
    public boolean update(Player player, SingleQuestInfo singleQuestInfo, GoalCallback callback, Blackboard blackboard) {
        final MerchantData merchantData = DataHallMrg.getInstance().findMerchantData(this.getClass().getSimpleName(), player.getBusiness_no());
        if (merchantData == null) {
            return false;
        }
        final C_Quest c_quest = merchantData.findC_Quest(this.getClass().getSimpleName(), singleQuestInfo.getQuestId());
        if (c_quest == null) {
            return false;
        }


        final DefaultBlackboard defaultBlackboard = (DefaultBlackboard) blackboard;
        final int gameType = defaultBlackboard.getOrDefault(AttributeKey.valueOf("gameType"), 0);

        if (c_quest.getQuestCondition().param1 != 0 && c_quest.getQuestCondition().param1 != gameType) {
            return false;
        }

        incProgressive(player, singleQuestInfo, 1, callback);
        return true;
    }

}