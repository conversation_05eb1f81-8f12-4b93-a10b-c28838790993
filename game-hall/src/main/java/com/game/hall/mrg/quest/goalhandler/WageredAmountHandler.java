package com.game.hall.mrg.quest.goalhandler;


import com.game.c_entity.merchant.C_Quest;
import com.game.c_entity.middleplatform.C_BaseExchangeRate;
import com.game.engine.math.BigDecimalUtils;
import com.game.entity.player.Player;
import com.game.entity.player.quest.GoalCallback;
import com.game.entity.player.quest.SingleQuestInfo;
import com.game.enums.Currency;
import com.game.enums.QuestGoalType;
import com.game.hall.mrg.DataHallMrg;
import com.game.hall.mrg.MerchantData;
import com.game.hall.mrg.quest.Blackboard;
import com.game.hall.mrg.quest.DefaultBlackboard;
import io.netty.util.AttributeKey;

public class WageredAmountHandler extends AbsGoalHandler {


    @Override
    public QuestGoalType goalType() {
        return QuestGoalType.WageredAmount;
    }

    @Override
    public boolean isAccumulatedType() {
        return true;
    }

    @Override
    public boolean update(Player player, SingleQuestInfo singleQuestInfo, GoalCallback callback, Blackboard blackboard) {
        final MerchantData merchantData = DataHallMrg.getInstance().findMerchantData(this.getClass().getSimpleName(), player.getBusiness_no());
        if (merchantData == null) {
            return false;
        }
        final C_Quest c_quest = merchantData.findC_Quest(this.getClass().getSimpleName(), singleQuestInfo.getQuestId());
        if (c_quest == null) {
            return false;
        }

        final DefaultBlackboard defaultBlackboard = (DefaultBlackboard) blackboard;
        final int gameType = defaultBlackboard.getOrDefault(AttributeKey.valueOf("gameType"), 0);
        final int currencyId = defaultBlackboard.getOrDefault(AttributeKey.valueOf("currencyId"), 0);
        final double wagered = defaultBlackboard.getOrDefault(AttributeKey.valueOf("wagered"), 0d);

        if (c_quest.getQuestCondition().param3.isEmpty() || !c_quest.getQuestCondition().param3.contains(gameType)) {
            return false;
        }

        if (c_quest.getQuestCondition().param2 == Currency.USD.getCurrencyId()) {
            final C_BaseExchangeRate c_baseExchangeRate = DataHallMrg.getInstance().findC_BaseExchangeRate(this.getClass().getSimpleName(), currencyId);
            if (c_baseExchangeRate == null) {
                return false;
            }
            final double evUsd = BigDecimalUtils.mul(wagered, c_baseExchangeRate.getExchangeRate(), 4);
            incProgressive(player, singleQuestInfo, evUsd, callback);
        } else {
            if (currencyId != c_quest.getQuestCondition().param2) {
                return false;
            }
            incProgressive(player, singleQuestInfo, wagered, callback);
        }
        return true;
    }

}