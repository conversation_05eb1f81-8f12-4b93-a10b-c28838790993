package com.game.hall.mrg.quest;

import io.netty.util.AttributeKey;

import javax.annotation.Nonnull;
import javax.annotation.Nullable;
import java.util.Map;
import java.util.stream.Stream;

/**
 * 动态参数
 * (上下文黑板，可选参数)
 */
public interface Blackboard {

    /**
     * 注意：如果null是一个合理的值，那么请使用{@link #containsKey(AttributeKey)}判断是否存在，而不要根据null判断
     *
     * @return key关联的值
     */
    <T> T get(@Nonnull AttributeKey<T> key);

    /**
     * API同{@link Map#getOrDefault(Object, Object)}
     * 即：仅当不包含该key时返回给定的默认值，否则返回关联的值。
     * 也就是说：如果key关联了null，则会返回null，而不是给定的默认值。
     * 也就是说：你可能更期望用的是{@link #getOrElse(AttributeKey, Object)}
     *
     * @return key关联的值
     */
    default <T> T getOrDefault(@Nonnull AttributeKey<T> key, T defaultValue) {
        final T r = get(key);
        if (r != null || containsKey(key)) {
            return r;
        } else {
            return defaultValue;
        }
    }

    /**
     * 如果key没有关联值或关联的值为null，则返回默认值
     * API同{@link java.util.Optional#orElse(Object)}
     */
    default <T> T getOrElse(@Nonnull AttributeKey<T> key, T other) {
        final T value = get(key);
        return value != null ? value : other;
    }

    /**
     * 如果指定键关联的值不存在或为null，则抛出异常
     */
    @Nonnull
    default <T> T checkedGet(@Nonnull AttributeKey<T> key) {
        final T result = get(key);
        if (null == result) {
            throw new IllegalArgumentException(key + " is absent");
        }
        return result;
    }

    /**
     * @param value 对应的value - null是合理的值
     * @return key之前绑定的值
     */
    <T> T set(@Nonnull AttributeKey<T> key, @Nullable T value);

    /**
     * 如果key关联了一个非null值，则该操作无效
     *
     * @return 如果key关联了一个非null值，则返回关联的值，否则返回null
     */
    default <T> T setIfAbsent(@Nonnull AttributeKey<T> key, @Nullable T value) {
        T v = get(key);
        if (v == null) {
            v = set(key, value);
        }
        return v;
    }

    /**
     * 当给定的参数不为null的时候才放入 - 通常用于过渡数据。
     */
    default <T> void setIfParamNotNull(@Nonnull AttributeKey<T> key, @Nullable T value) {
        if (value != null) {
            set(key, value);
        }
    }

    /**
     * @return key当前关联的值
     */
    @Nullable
    <T> T remove(@Nonnull AttributeKey<T> key);

    /**
     * 如果key关联了null值也返回true
     *
     * @return 如果包含该键则返回true
     */
    boolean containsKey(@Nonnull AttributeKey<?> key);

    /**
     * @return 如果黑板为空则返回true
     */
    boolean isEmpty();

    /**
     * @return 当前包含的属性数量
     */
    int size();

    /**
     * 返回值为为stream既可以禁止外部修改，也避免了复杂黑板的高开销
     *
     * @return 所有的键的流
     */
    Stream<AttributeKey<?>> keyStream();

    /**
     * 返回值为stream既可以禁止外部修改，也避免了复杂黑板的高开销。
     * 注意：不要修改entry关联的数据，为保证效率，我们没有将返回的entry封装为不可变。
     *
     * @return 所有的键值对的流
     */
    Stream<Map.Entry<AttributeKey<?>, Object>> entryStream();

    /**
     * 清理本次运行期间产生的数据
     */
    void resetForRestart();

    /**
     * 进行彻底的清理
     * （多用于复用的时候）
     */
    void resetPoolable();

    default void putAll(@Nonnull Map<AttributeKey<?>, Object> map) {
        for (Map.Entry<AttributeKey<?>, Object> entry : map.entrySet()) {
            @SuppressWarnings("unchecked") final AttributeKey<Object> key = (AttributeKey<Object>) entry.getKey();
            set(key, entry.getValue());
        }
    }

    default void putAll(@Nonnull Blackboard other) {
        other.entryStream().forEach(entry -> {
            @SuppressWarnings("unchecked") final AttributeKey<Object> key = (AttributeKey<Object>) entry.getKey();
            set(key, entry.getValue());
        });
    }

    /** 拷贝所有的数据到指定黑板 */
    default void copyTo(Blackboard out) {
        entryStream().forEach(entry -> {
            @SuppressWarnings("unchecked") final AttributeKey<Object> key = (AttributeKey<Object>) entry.getKey();
            out.set(key, entry.getValue());
        });
    }

    static Blackboard emptyBlackboard() {
        return EmptyBlackboard.INSTANCE;
    }
}