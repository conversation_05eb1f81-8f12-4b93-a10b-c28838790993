package com.game.hall.mrg;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class InboxMrg {
    public static final Logger LOGGER = LoggerFactory.getLogger(InboxMrg.class);

    private static final InboxMrg instance = new InboxMrg();

    /**
     * 事件
     */
    public static final int FIRST_LOGIN = 1;//首次登录
    public static final int RECHARGE = 3;//充值
    public static final int WITHDRAW = 4;//提现

    /**
     * 模板
     */
    public static final int WITHDRAW_PASS = 100;//提现通过
    public static final int WITHDRAW_REJECT = 101;//提现拒绝
    public static final int DEPOSIT_SUCCESS = 102;//充值成功
    public static final int DEPOSIT_FAIL = 103;//充值失败
    public static final int ACTIVITY_REWARD = 104;//活动奖励
    public static final int UP_POINTS = 105;//上分

    public static InboxMrg getInstance() {
        return instance;
    }

//    public void sendMail(Player player, int mailId, boolean isSend) {
//        final MerchantData merchantData = DataHallMrg.getInstance().findMerchantData(this.getClass().getSimpleName(), player.getBusiness_no());
//        if (merchantData == null) {
//            return;
//        }
//        final C_PubMail c_pubMail = merchantData.findC_PubMail(this.getClass().getSimpleName(), mailId);
//        if (c_pubMail == null) {
//            return;
//        }
//        final Inbox inbox = addInbox(player, c_pubMail);
//        EntityDaoMrg.getInstance().getDao(PlayerDao.class).inboxDao.updateInsertInbox(player.getPlayerId(), player.getInboxInfo(), LongLists.singleton(inbox.getInboxId()));
//        if (!isSend) {
//            return;
//        }
//        final TcpMessage.ResTcpReceiveInboxMessage.Builder res = TcpMessage.ResTcpReceiveInboxMessage.newBuilder();
//        res.setMsgID(MIDMessage.MID.ResTcpReceiveInbox_VALUE)
//                .addInboxList(InboxMrg.getInstance().builderInboxInfo(inbox));
//        player.sendMsg(res.build());
//    }

}
