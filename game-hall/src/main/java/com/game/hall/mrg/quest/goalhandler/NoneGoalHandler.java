package com.game.hall.mrg.quest.goalhandler;

import com.game.entity.player.Player;
import com.game.entity.player.quest.GoalCallback;
import com.game.entity.player.quest.SingleQuestInfo;
import com.game.enums.QuestGoalType;
import com.game.hall.mrg.quest.Blackboard;

public class NoneGoalHandler extends AbsGoalHandler {


    @Override
    public QuestGoalType goalType() {
        return QuestGoalType.None;
    }

    @Override
    public boolean update(Player player, SingleQuestInfo singleQuestInfo, GoalCallback callback, Blackboard blackboard) {
        return false;
    }
}