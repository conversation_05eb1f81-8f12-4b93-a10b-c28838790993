package com.game.hall.main;

import com.game.c_entity.middleplatform.C_BaseCurrency;
import com.game.engine.BlockingTaskSchedulerMrg;
import com.game.engine.HttpClientMrg;
import com.game.engine.enums.MsgType;
import com.game.engine.enums.ServerType;
import com.game.engine.enums.state.ServerState;
import com.game.engine.io.conf.*;
import com.game.engine.io.handler.IHandler;
import com.game.engine.io.handler.TcpHandler;
import com.game.engine.io.message.HttpMessageBean;
import com.game.engine.io.message.MessageBean;
import com.game.engine.kafka.LogProducerMrg;
import com.game.engine.mongo.DBConnectionMrg;
import com.game.engine.net.*;
import com.game.engine.script.ScriptLoader;
import com.game.engine.util.concurrent.DefaultThreadFactory;
import com.game.engine.util.concurrent.RejectedExecutionHandlers;
import com.game.engine.utils.*;
import com.game.engine.io.redis.RedisPoolManager;
import com.game.entity.player.Player;
import com.game.hall.mrg.*;
import com.game.hall.mrg.currency.CurrencyMrg;
import com.game.hall.mrg.player.PlayerMrg;
import com.game.hall.mrg.quest.QuestMrg;
import com.game.hall.script.IMessageScript;
import com.game.hall.script.IMsgAttackResist;
import com.game.hall.script.IPlayerScript;
import com.game.hall.server.handler.UserWebSocketServerHandler;
import com.game.hall.server.http.HallHttpServer;
import com.game.hall.server.tcp.*;
import com.game.manager.DBHandlerRegisterMrg;
import com.game.user.UserSession;
import com.game.utils.VirtualThreadUtils;
import com.google.protobuf.Message;
import com.proto.InnerMessage;
import com.proto.MIDMessage;
import io.netty.channel.Channel;
import io.netty.util.internal.StringUtil;
import org.slf4j.LoggerFactory;
import org.slf4j.Logger;

import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.function.Consumer;

public class HallServer extends World {

    private static final Logger LOGGER = LoggerFactory.getLogger(HallServer.class);

    private static HallServer hallServer;

    // hallServer
    private HallTcpAgentGameServer hallTcpAgentGameServer;
    // 链接ProxyServer
    private HallTcpClient2Proxy hallTcpClient2Proxy;
    // 链接BillingServer
    private HallTcpClient2Billing hallTcpClient2Billing;

    private GateWebSocketUserServer gateWebSocketUserServer;

    //http服务器
    private HallHttpServer hallHttpServer;

    private NettyServerConfig nettyServerConfig;
    private NettyClientConfig nettyClientConfig_proxy;
    private NettyClientConfig nettyClientConfig_billing;
    private NettyServerConfig nettyServerConfig_user;

    private NettyServerConfig nettyServerConfig_http;

    private GameResetMrg gameResetMrg;
    private HttpClientMrg httpClientMrg;
    private GameEventLoop configEventLoop;
    private LogProducerMrg logProducerMrg;
    private UniqueIDGenerator uniqueIDGenerator;
    private BlockingTaskSchedulerMrg blockingTaskSchedulerMrg;

    private List<GameEventLoop> eventLoops = new ArrayList<>();

    public HallServer() {
        super(1000 / 30);
    }

    public static HallServer getInstance() {
        return hallServer;
    }

    public HallTcpAgentGameServer getHallTcpAgentGameServer() {
        return hallTcpAgentGameServer;
    }

    public HallTcpClient2Proxy getHallTcpClient2Proxy() {
        return hallTcpClient2Proxy;
    }

    public HallTcpClient2Billing getHallTcpClient2Billing() {
        return hallTcpClient2Billing;
    }

    public GateWebSocketUserServer getGateWebSocketUserServer() {
        return gateWebSocketUserServer;
    }

    public NettyServerConfig getNettyServerConfig() {
        return nettyServerConfig;
    }

    public NettyClientConfig getNettyClientConfig_proxy() {
        return nettyClientConfig_proxy;
    }

    public LogProducerMrg getLogProducerMrg() {
        return logProducerMrg;
    }

    public HttpClientMrg getHttpClientMrg() {
        return httpClientMrg;
    }

    public GameEventLoop getConfigEventLoop() {
        return configEventLoop;
    }

    public void setConfigEventLoop(GameEventLoop configEventLoop) {
        this.configEventLoop = configEventLoop;
    }

    public List<GameEventLoop> getEventLoops() {
        return eventLoops;
    }

    public void setEventLoops(List<GameEventLoop> eventLoops) {
        this.eventLoops = eventLoops;
    }

    public GameResetMrg getGameResetMrg() {
        return gameResetMrg;
    }

    public BlockingTaskSchedulerMrg getBlockingTaskSchedulerMrg() {
        return blockingTaskSchedulerMrg;
    }

    public UniqueIDGenerator getUniqueIDGenerator() {
        return uniqueIDGenerator;
    }

    public static void main(String[] args) {
        LOGGER.info("服务器启动时间：{}", TimeUtil.getDateTimeFormat(TimeUtil.YYYYMMDDHHMMSS));

        Config.path = FileUtil.getMainPath();
        LOGGER.info("配置路径为：" + Config.path);

        ScriptLoader.getInstance().init((str) -> SysUtil.exit(HallServer.class, null, "脚本加载错误"));

        hallServer = new HallServer();
        final ExecutorService service = Executors.newFixedThreadPool(1, (r) -> new Thread(r, "LOGIC_THREAD"));
        final EventConsumer<LogicEvent> eventConsumer = new EventConsumer<>(hallServer);
        final GlobalQueue<LogicEvent> queue = new GlobalQueue<>(service, eventConsumer);
        GlobalQueueContainerMrg.getInstance().setGlobalQueue(queue);

        Runtime.getRuntime().addShutdownHook(new Thread(HallServer::stops));
    }

    private void initServerConfig() throws Exception {
        ConstantConfig.reloadConstantConfig(Config.path, "constantConfig.xml");
        NettyServerConfig nettyServerConfig_hall = FileUtil.getConfigXML(Config.path, "nettyServerConfig.xml", NettyServerConfig.class);
        if (nettyServerConfig_hall == null) {
            SysUtil.exit(HallServer.class, null, "nettyServerConfig_hall");
            return;
        }

        NettyServerConfig nettyServerConfig_user = FileUtil.getConfigXML(Config.path, "nettyServerConfig_user.xml", NettyServerConfig.class);
        if (nettyServerConfig_user == null) {
            SysUtil.exit(HallServer.class, null, "nettyServerConfig_user");
            return;
        }

        NettyClientConfig nettyClientConfig_proxy = FileUtil.getConfigXML(Config.path, "nettyClientConfig_proxy.xml", NettyClientConfig.class);
        if (nettyClientConfig_proxy == null) {
            SysUtil.exit(HallServer.class, null, "nettyClientConfig_proxy");
            return;
        }

        NettyClientConfig nettyClientConfig_billing = FileUtil.getConfigXML(Config.path, "nettyClientConfig_billing.xml", NettyClientConfig.class);
        if (nettyClientConfig_billing == null) {
            SysUtil.exit(HallServer.class, null, "nettyClientConfig_billing");
            return;
        }

        NettyServerConfig nettyServerConfig_http = FileUtil.getConfigXML(Config.path, "nettyServerConfig_http.xml", NettyServerConfig.class);
        if (nettyServerConfig_http == null) {
            SysUtil.exit(HallServer.class, null, "nettyServerConfig_http");
            return;
        }

        this.nettyServerConfig = nettyServerConfig_hall;
        this.nettyClientConfig_proxy = nettyClientConfig_proxy;
        this.nettyClientConfig_billing = nettyClientConfig_billing;
        this.nettyServerConfig_user = nettyServerConfig_user;

        this.nettyServerConfig_http = nettyServerConfig_http;

        Config.SERVER_ID = nettyServerConfig_hall.getId(); // 设置ID
        Config.SERVER_NAME = nettyServerConfig_hall.getName(); // 设置SERVERNAME
        Config.SERVER_CHANNEL = nettyServerConfig_hall.getChannel(); // 设置渠道
        Config.serverState = ServerState.NORMAL;
    }

    @Override
    protected void registerSeri() {

    }

    @Override
    protected void registerProtoHandler() {

    }

    @Override
    protected void listenOrConnect() throws Exception {
        startServer();
    }

    @Override
    protected void initWhenThreadStartImpl() throws Exception {
        initServerConfig();

        this.configEventLoop = new GameEventLoop(null, new DefaultThreadFactory("Hall_Config_Event_Loop"), RejectedExecutionHandlers.abort());
        for (int i = 1; i <= 5; i++) {
            final GameEventLoop eventLoop = new GameEventLoop(null, new DefaultThreadFactory("Hall_Event_Loop_" + i), RejectedExecutionHandlers.abort());
            this.eventLoops.add(eventLoop);
        }
        this.gameResetMrg = new GameResetMrg();
        this.httpClientMrg = new HttpClientMrg();
        this.logProducerMrg = new LogProducerMrg();
        final String serverId = Config.SERVER_ID + "";
        this.uniqueIDGenerator = new UniqueIDGenerator(Integer.parseInt(serverId));
        this.blockingTaskSchedulerMrg = new BlockingTaskSchedulerMrg();

        final JedisClusterConfig jpc = FileUtil.getConfigXML(Config.path, "jedisClusterConfig.xml", JedisClusterConfig.class);
        final RedisPoolManager redisPoolManager = new RedisPoolManager(jpc);

        final String mongoUrl = nettyServerConfig.getMongo_connection_string();
        final String mongoData = nettyServerConfig.getMongo_read_write_database();
        DBConnectionMrg.getInstance().dBConnection(mongoUrl, mongoData);
        DBHandlerRegisterMrg.getInstance().dBHandlerRegister();

        //加载配置
        DataHallMrg.getInstance().initMongoConfig();
        DataHallMrg.getInstance().loadConfigData();

        //启动kafka
        logProducerMrg.start(nettyServerConfig.getKafka_connection_string());
        //凌晨重置
        this.gameResetMrg.registerHandlers();

        //注册货币handler
        final Map<Integer, C_BaseCurrency> c_baseCurrencyMap = DataHallMrg.getInstance().getC_baseCurrencyMap();
        final Set<Integer> currencyIds = new HashSet<>(c_baseCurrencyMap.keySet());
        CurrencyMrg.getInstance().registerCurrencyHandler(currencyIds);

        //任务
        QuestMrg.getInstance().initRegisterGoalHandler();
        QuestMrg.getInstance().initRegisterQuestHandler();
    }

    @Override
    protected void tickImpl(long curTime) throws Exception {
        DBHandlerRegisterMrg.getInstance().tick(curTime);
        this.gameResetMrg.tick();
        this.httpClientMrg.tick();
        this.blockingTaskSchedulerMrg.tick();
    }

    private void startServer() {
        hallTcpAgentGameServer = new HallTcpAgentGameServer(nettyServerConfig);
        hallTcpClient2Proxy = new HallTcpClient2Proxy(nettyClientConfig_proxy);
        hallTcpClient2Billing = new HallTcpClient2Billing(nettyClientConfig_billing);
        gateWebSocketUserServer = new GateWebSocketUserServer(nettyServerConfig_user);

        hallHttpServer = new HallHttpServer(nettyServerConfig_http);
        {
            hallTcpAgentGameServer.start();
            hallTcpClient2Proxy.start();
            hallTcpClient2Billing.start();
            gateWebSocketUserServer.start();

            hallHttpServer.start();
        }
    }

    private static void stops() {
        try {
            Config.serverState = ServerState.MAINTAIN;
            //踢玩家下线
            final Map<Long, Player> onlinePlayerMap = PlayerMrg.getInstance().getOnlinePlayerMap();
            if (!onlinePlayerMap.isEmpty()) {
                final Iterator<Map.Entry<Long, Player>> iterator = onlinePlayerMap.entrySet().iterator();
                while (iterator.hasNext()) {
                    Map.Entry<Long, Player> entry = iterator.next();
                    final Player player = entry.getValue();
                    iterator.remove();  // Use iterator's remove method to avoid ConcurrentModificationException
                    HallServer.getInstance().asyncExecute(player.getPlayerId(), () -> {
                        PlayerMrg.getInstance().sendKickOutPlayerMsg(player);

                        ScriptLoader.getInstance().consumerScript("PlayerQuitScript",
                                (IPlayerScript script) -> script.quitHall(player));
                    });
                }
                Thread.sleep(5000);
                LOGGER.info("kickOutAllPlayer success ！！！");
            }

            LOGGER.info("close gameResetMrg");
            if (hallServer.gameResetMrg != null) {
                hallServer.gameResetMrg.shutdownNow();
            }
            LOGGER.info("close configEventLoop");
            if (hallServer.configEventLoop != null) {
                hallServer.configEventLoop.shutdownNow();
            }
            LOGGER.info("close eventLoop");
            if (!hallServer.eventLoops.isEmpty()) {
                for (GameEventLoop gameEventLoop : hallServer.eventLoops) {
                    gameEventLoop.shutdownNow();
                }
            }
            LOGGER.info("close logProducerMrg");
            if (hallServer.logProducerMrg != null) {
                hallServer.logProducerMrg.shutdown();
            }

            LOGGER.info("close virtualThread");
            VirtualThreadUtils.shutdown();

            Thread.sleep(1000);
            RedisPoolManager.getInstance().destroy();
            hallServer.stop();
        } catch (Exception e) {
            LOGGER.error("释放大厅资源异常", e);
        }
    }

    private void stop() {
        LOGGER.info("关闭大厅服务器");
        try {
            if (this.hallTcpAgentGameServer != null) {
                this.hallTcpAgentGameServer.stop();
            }
        } catch (Exception e) {
            LOGGER.error("关闭大厅服务器", e);
        }

        LOGGER.info("关闭大厅连接代理服务器");
        try {
            if (this.hallTcpClient2Proxy != null) {
                this.hallTcpClient2Proxy.stop();
            }
        } catch (Exception e) {
            LOGGER.error("关闭大厅连接代理服务器", e);
        }

        LOGGER.info("关闭大厅连接充值服务器");
        try {
            if (this.hallTcpClient2Billing != null) {
                this.hallTcpClient2Billing.stop();
            }
        } catch (Exception e) {
            LOGGER.error("关闭大厅连接充值服务器", e);
        }

        LOGGER.info("关闭大厅连接用户服务器");
        try {
            if (this.gateWebSocketUserServer != null) {
                this.gateWebSocketUserServer.stop();
            }
        } catch (Exception e) {
            LOGGER.error("关闭大厅连接网关服务器", e);
        }

        LOGGER.info("关闭大厅HTTP服务器");
        try {
            if (this.hallHttpServer != null) {
                this.hallHttpServer.stop();
            }
        } catch (Exception e) {
            LOGGER.error("关闭大厅HTTP服务器", e);
        }

    }

    @Override
    protected void onLogicEvent(LogicEvent evt) {
        switch (evt.getLogicEventType()) {
            case G_USERSERVER_ON_TCP_CONNECT: {
                ConnectionAttackResistMrg.getInstance().onConnect(evt.getChannel());
                break;
            }
            case G_USERSERVER_ON_DISCONNECT: {
                gateWebSocketUserServer.onIoSessionClosed(evt.getChannel());
                final long sessionId = MsgUtil.getSessionID(evt.getChannel());
                final UserSession userSession = UserTcpMrg.getInstance().getUserBySessionID(sessionId);
                if (userSession != null) {
                    userSession.destroy(false);
                }
                break;
            }
            case G_USERSERVER_MESSAGE_EVENT_S_RECV: {
                ConnectionAttackResistMrg.getInstance().removeChannelResist(evt.getChannel());
                gateWebSocketUserServer.onIoSessionConnect(evt.getChannel());
                final UserSession userSession = evt.getChannel().attr(UserWebSocketServerHandler.CHANNEL_USER_SESSION_KEY).get();
                if (userSession != null) {
                    UserTcpMrg.getInstance().onUserConnected(userSession);
                } else {
                    LOGGER.warn("接收到客户端消息，userSession没有创建！！！");
                    break;
                }
                MessageAttackResistMrg.getInstance().onResistUserMsg(userSession);
                ScriptLoader.getInstance().consumerScript("MessageScript",
                        (IMessageScript script) -> script.tcpUserMessageHandler(evt));
                break;
            }

            /**
             * 监听代理游戏服
             */
            case H_AGENTGAMESERVER_ON_TCP_CONNECT: {
                hallTcpAgentGameServer.onIoSessionConnect(evt.getChannel());
                break;
            }
            case H_AGENTGAMESERVER_ON_DISCONNECT: {
                hallTcpAgentGameServer.onIoSessionClosed(evt.getChannel());
                break;
            }

            /**
             * 监听代理
             */
            case H_PROXYCLIENT_ON_TCP_CONNECT: {
                hallTcpClient2Proxy.onIoSessionConnect(evt.getChannel());
                break;
            }
            case H_PROXYCLIENT_ON_DISCONNECT: {
                hallTcpClient2Proxy.onIoSessionClosed(evt.getChannel());
                break;
            }

            /**
             * 监听充值
             */
            case H_BILLINGCLIENT_ON_TCP_CONNECT: {
                hallTcpClient2Billing.onIoSessionConnect(evt.getChannel());
                break;
            }
            case H_BILLINGCLIENT_ON_DISCONNECT: {
                hallTcpClient2Billing.onIoSessionClosed(evt.getChannel());
                break;
            }

            case UDP_MESSAGE_EVENT_S_RECV:
                ScriptLoader.getInstance().functionScript("MsgAttackResist",
                        (IMsgAttackResist script) -> script.checkMessageResist(evt));

                ///_api/graphql
                if (Objects.equals(evt.getParamB(), "/_api/graphql")) {
                    ScriptLoader.getInstance().consumerScript("MessageScript", (IMessageScript script) ->
                            script.udpUserMessageHandler(evt));
                } else {
                    httpMessageHandler(evt.getParamA(), evt.getParamB(), evt.getChannel());
                }
                break;

            case H_AGENTGAMESERVER_MESSAGE_EVENT_S_RECV:
            case H_PROXYCLIENT_MESSAGE_EVENT_C_RECV:
            case H_BILLINGCLIENT_MESSAGE_EVENT_C_RECV:
                final int msgId = evt.getIntParamB();
                if (msgId / 100000 == 1) {//内部消息
                    messageHandler(evt);
                } else {
                    udpInnerMessageHandler(evt);
                }
                break;
            default: {
                throw new RuntimeException();
            }
        }
    }

    private void httpMessageHandler(Object msg, Object uri, Channel session) {
        @SuppressWarnings("unchecked") final Map<String, Object> paramsMap = (Map<String, Object>) msg;
        final String requestPath = (String) uri;
        if (StringUtil.isNullOrEmpty(requestPath) || paramsMap.isEmpty()) {
            LOGGER.warn("ip address：{}，http request address error：{}", MsgUtil.getClientIp(session), requestPath);
            return;
        }
        final HttpMessageBean httpMessageBean = ScriptLoader.getInstance().getHttpMessageBean(requestPath);
        if (httpMessageBean == null) {
            LOGGER.error("HttpMessagePoll，未能找到，content = {} 的 httpMessageBean", requestPath);
            return;
        }
        try {
            long time = TimeUtil.currentTimeMillis();
            IHandler handler = httpMessageBean.newHandler();
            handler.setSession(session);
            handler.setCreateTime(TimeUtil.currentTimeMillis());
            handler.setParamsMap(paramsMap);
            handler.run();

            time = TimeUtil.currentTimeMillis() - time;
            if (time > 5) {
                LOGGER.warn("{}，处理时间超过，{}", handler.getClass().getSimpleName(), time);
            }
        } catch (Exception e) {
            LOGGER.error("", e);
        }
    }

    private void udpInnerMessageHandler(LogicEvent event) {
        final int msgType = event.getIntParamA();
        final long pid = event.getLongParamA();
        final long sessionId = event.getLongParamB();
        try {
            if (msgType == MsgType.IDMESSAGE.getType()) {// 数据结构:msgId:pfbytes
                final int msgId = event.getIntParamB();
                final byte[] bytes = (byte[]) event.getParamA();// 消息内容

                final Channel userSession = UserUdpMrg.getInstance().getHttpSession(sessionId);
                if (userSession == null) {
                    LOGGER.warn("userId：{}，msgId {} send fail", pid, msgId);
                    return;
                }

                // 在本地注册，必须预处理
                final MessageBean messageBean = ScriptLoader.getInstance().getMessageBean(msgId);
                if (messageBean == null) {
                    // 转发给客户端
                    UserUdpMrg.getInstance().sendClient(userSession, event, pid);
                    return;
                }

                final Message message = messageBean.buildMessage(bytes);
                final TcpHandler handler = (TcpHandler) messageBean.newHandler();
                if (handler != null) {
                    handler.setPid(pid);
                    handler.setMsgBytes(bytes);
                    handler.setMessage(message);
                    handler.setSession(userSession);
                    HallServer.getInstance().asyncExecute(Math.abs(pid), handler);
//                    LOGGER.info("userId：{}，send msg to client：{}", pid, msgId);
                }
            } else {
                LOGGER.warn("消息类型{}未实现,玩家：{}，消息发送失败", msgType, pid);
            }
        } catch (Exception e) {
            LOGGER.error("消息处理", e);
        } finally {
            UserUdpMrg.getInstance().removeUdpSession(sessionId);
//            LOGGER.info("udp remove sessionId；{}", UserUdpMrg.getInstance().getUdpUserSession().size());
        }
    }

    private void messageHandler(LogicEvent event) {
        try {
            int msgType = event.getIntParamA();
            final long id = event.getLongParamA();
            final long udpSessionId = event.getLongParamB();
            final int msgId = event.getIntParamB();
            final byte[] bytes = (byte[]) event.getParamA();

            if (msgType == MsgType.IDMESSAGE.getType()) {// 数据结构:msgId:pfbytes
                // 在本地注册，必须预处理
                final MessageBean messageBean = ScriptLoader.getInstance().getMessageBean(msgId);
                if (messageBean != null) {
                    final Message message = messageBean.buildMessage(bytes);
                    final TcpHandler handler = (TcpHandler) messageBean.newHandler();
                    if (handler != null) {
                        handler.setPid(id);
                        handler.setUdpSessionId(udpSessionId);
                        handler.setMsgId(msgId);
                        handler.setMsgBytes(bytes);
                        handler.setMessage(message);
                        handler.setSession(event.getChannel());

                        HallServer.getInstance().asyncExecute(Math.abs(id), handler);
                    }
                    return;
                }
                LOGGER.warn("消息[{}]代码未实现逻辑", msgId);
            } else {
                LOGGER.warn("消息类型{}未实现,玩家{}消息发送失败", msgType, id);
            }
        } catch (Exception e) {
            LOGGER.error("channelRead", e);
        }
    }

    public static void serverHeartCheck() {
        try {
            HallServer.getInstance().getHallTcpClient2Proxy().checkStatus();
            HallServer.getInstance().getHallTcpClient2Billing().checkStatus();

            HallServer.getInstance().buildRegisterUpdateMessage(msg -> {
                HallServer.getInstance().getHallTcpClient2Proxy().broadcastMsgAllSessions(msg);
                HallServer.getInstance().getHallTcpClient2Billing().broadcastMsgAllSessions(msg);
            });

            final InnerMessage.InnerReqServerListMessage.Builder req = InnerMessage.InnerReqServerListMessage.newBuilder();
            req.setMsgID(MIDMessage.MID.InnerReqServerList_VALUE).addType(ServerType.BILLING.getType()).addType(ServerType.AGENT_GAME.getType());
            HallServer.getInstance().getHallTcpClient2Proxy().sendMsg(req.build());
        } catch (Exception e) {
            LOGGER.error("serverHeartCheck", e);
        }
    }

    /**
     * 构建大厅信息
     *
     * @param action
     */
    private void buildRegisterUpdateMessage(Consumer<InnerMessage.InnerReqRegisterUpdateMessage> action) {
        final InnerMessage.InnerReqRegisterUpdateMessage.Builder req = InnerMessage.InnerReqRegisterUpdateMessage.newBuilder();
        req.setMsgID(MIDMessage.MID.InnerReqRegisterUpdate_VALUE).setServerInfo(buildServerInfo(nettyServerConfig));
        if (action != null) {
            action.accept(req.build());
        }
    }

    /**
     * 构建服务器信息
     *
     * @param config
     * @return
     */
    private InnerMessage.InnerServerInfo buildServerInfo(NettyServerConfig config) {
        final InnerMessage.InnerServerInfo.Builder builder = InnerMessage.InnerServerInfo.newBuilder();
        builder.setId(config.getId())
//                .setIp(config.getIp() == null ? "" : config.getIp())
                .setType(config.getType().getType()).setPort(config.getPort()).setGameState(Config.serverState.getState()).setPower(config.getPower()).setOnline(PlayerMrg.getInstance().getOnlinePlayerMap().size()).setHttpPort(config.getHttpPort()).setWwwIp(config.getIp()).setName(config.getName());
        return builder.build();
    }

    public void asyncExecute(IHandler handler) {
        VirtualThreadUtils.execute(handler);
    }

    public void asyncExecute(long playerId, Runnable runnable) {
        final int index = (int) (playerId % this.eventLoops.size());
        this.eventLoops.get(index).execute(runnable);
    }
}
