package com.game.hall.script;

import com.game.engine.script.IBaseScript;
import com.game.engine.util.misc.Tuple2;
import com.game.entity.player.Player;

public interface IBillingScript extends IBaseScript {

    default Tuple2<Double, Double> calculateAvailableAmount(Player player, int currencyId, int withdrawType) {
        return null;
    }

    default void recalculateWithdrawStandard(Player player, int currencyId, double available, double withdrawAmount, int withdrawType) {

    }
}
