package com.game.hall.script;

import com.game.engine.script.IBaseScript;
import com.game.entity.player.Player;
import com.game.enums.TurnoverReason;
import com.proto.HallMessage;

import java.util.function.Consumer;

public interface IPlayerScript extends IBaseScript {
    /**
     * 登录游戏
     *
     * @param player
     * @param ip
     * @param region
     * @param req
     */
    default void loginHall(Player player, String ip, String region, HallMessage.ReqPlayerEntryHallMessage req) {

    }

    /**
     * 退出大厅
     *
     * @param player
     */
    default void quitHall(Player player) {

    }

    /**
     * 创建玩家
     *
     * @param req
     * @param callback
     * @return
     */
    default Player createPlayer(HallMessage.ReqPlayerEntryHallMessage req, long accountId, int threeParty, Consumer<Player> callback) {
        return null;
    }


    default void everyMinuteCheck() {

    }

    default void everyDayReset(Player player) {

    }

    default void everyWeeklyReset(Player player) {

    }

    default void everyMonthReset(Player player) {

    }

    default void savePlayer(Player player) {

    }

    default void saveRealPlayer(Player player) {

    }

    default void drawStandard(Player player, TurnoverReason turnoverReason, int currencyId, double amount, double turnover) {

    }

    default void bettingTurnover(Player player, TurnoverReason turnoverReason, int currencyId, double turnover) {

    }

    default String generateInvitationCode(long playerId) {
        return "";
    }
}
