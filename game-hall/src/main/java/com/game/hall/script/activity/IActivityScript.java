package com.game.hall.script.activity;

import com.game.c_entity.merchant.C_Activity;
import com.game.c_entity.merchant.C_GameApi;
import com.game.engine.script.IBaseScript;
import com.game.engine.util.misc.Tuple2;
import com.game.entity.player.Player;
import com.game.hall.mrg.currency.RewardRequest;

public interface IActivityScript extends IBaseScript {

    default void clearData(Player player, C_Activity c_activity) {

    }

    default void initData(Player player, C_Activity c_activity) {

    }

    default void resetData(Player player, C_Activity c_activity) {

    }

    default void resetData(Player player, int cycleType) {

    }

    default int receiveStatus(Player player, C_Activity c_activity) {
        return 0;
    }

    default RewardRequest receiveReward(Player player, C_Activity c_activity) {
        return null;
    }

    default RewardRequest receiveSettlementReward(Player player, C_Activity c_activity) {
        return null;
    }

    default Tuple2<Double, Double> executeBack(Player player, int currencyId, double progress) {
        return new Tuple2<>(0d, 0d);
    }

    default void execute(Player player, int currencyId, double progress) {

    }

    default void execute(Player player, int rankType, int currencyId, double validAmount, double progress) {

    }

    default void execute(Player player, int currencyId, double wageredAmount, double rechargeAmount) {

    }

    default void execute(Player player, C_GameApi c_gameApi, int currencyId, double betAmount, double validAmount, double progress) {

    }

    default void freeGiveExecute(Player player, int freeGiveType) {

    }

    default void sendActivityLog(Player player, int type, C_Activity c_activity, RewardRequest rewardRequest) {

    }

    default void settlement() {

    }

    default void insertRobot() {

    }

    default boolean isSameNetworkOrDevice(Player player, Player superiorPlayer, int activityId) {
        return false;
    }
}
