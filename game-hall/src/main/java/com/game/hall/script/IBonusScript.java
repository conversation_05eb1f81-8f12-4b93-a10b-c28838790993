package com.game.hall.script;

import com.game.engine.script.IBaseScript;
import com.game.entity.player.Player;
import com.game.enums.TransactionFrom;

public interface IBonusScript extends IBaseScript {

    default void initData(Player player) {

    }

    default void statsWager(Player player, int currencyId, double validBet) {

    }

    default void settlementBonus(Player player) {

    }

    default void addBonusNote(TransactionFrom transaction, Player player, int currencyId, double amount) {

    }
}
