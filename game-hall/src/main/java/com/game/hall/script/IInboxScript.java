package com.game.hall.script;

import com.game.engine.script.IBaseScript;
import com.game.entity.player.Player;
import com.game.entity.player.inbox.Inbox;
import com.proto.CommonMessage;

import java.util.List;

public interface IInboxScript extends IBaseScript {

    default void sendPubMail(Player player) {

    }

    default void deleteExpireInbox(Player player) {

    }

    default void sendEventPubMail(Player player, int event, boolean send) {

    }

    default void sendTimelyPubMail(Player player) {

    }

    default void sendDayPubMail(Player player) {

    }

    default void sendWeeklyPubMail(Player player) {

    }

    default CommonMessage.InboxInfo builderInboxInfo(Inbox inbox) {
        return null;
    }

    default void sendInboxMail(Player player, int inboxId, List<String> params) {

    }
}
