package com.game.hall.script;

import com.game.engine.script.IBaseScript;
import com.game.entity.player.Player;

public interface IGameScript extends IBaseScript {
    default void notifyGameNoteBet() {

    }

    default void notifyGameNoteHigh() {

    }

    default void notifyDailyContestPrizePool() {

    }

    default double calculateValidBettingVolume(Player player, int gameType, int currencyId, double betAmount) {
        return 0;
    }

    default void gameEveryMinReset() {

    }

    default void gameEveryDayReset() {

    }

    default void gameEveryWeeklyReset() {
    }
}
