package com.game.hall.script;

import com.game.engine.script.IBaseScript;
import com.game.entity.player.Player;

public interface IVipClubScript extends IBaseScript {

    default void addExp(Player player, int currencyId, double validBet) {

    }

    default void playerDayBackCash(Player player, int gameType, int currencyId, double validBet) {

    }

    default void addRecharge(Player player, int currencyId, double recharge) {

    }

    default void vipCheckRelegation(Player player) {

    }

    default void resetVipSignIn(Player player) {

    }

    default void resetDaily(Player player) {

    }

    default void resetWeekly(Player player) {

    }

    default void resetMonthly(Player player) {

    }
}
