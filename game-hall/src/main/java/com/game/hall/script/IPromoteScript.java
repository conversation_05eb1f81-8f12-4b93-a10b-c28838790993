package com.game.hall.script;

import com.game.c_entity.merchant.C_GameApi;
import com.game.engine.script.IBaseScript;
import com.game.entity.player.Player;
import com.game.enums.ErrorCode;

public interface IPromoteScript extends IBaseScript {

    default void calculateSuperiors(Player player) {

    }

    default ErrorCode bindSuperiorId(Player player, String source, String referrerCode, boolean save) {
        return ErrorCode.Success;
    }

    default ErrorCode unbindSuperiorId(Player player) {
        return ErrorCode.Success;
    }

    default void calculateSuperiorCommission(Player player, C_GameApi c_gameApi, String noteId, int currencyId, double betAmount, double validBet) {
    }

    default void calculateTeamCommission(Player player, C_GameApi c_gameApi, String noteId, int currencyId, double betAmount, double validBet) {
    }

    default void calculateThreeLevelCommission(Player player, C_GameApi c_gameApi, String noteId, int currencyId, double betAmount, double validBet) {
    }
}
