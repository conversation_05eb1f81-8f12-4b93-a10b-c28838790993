package com.game.hall.script;

import com.game.engine.script.IBaseScript;
import com.game.entity.game.GameCommissionStat;
import com.game.entity.game.GameWagerStat;
import com.game.hall.mrg.MerchantData;

public interface IAffiliateScript extends IBaseScript {

    default GameCommissionStat calculateSuperiorCommissionStat(GameWagerStat gameWagerStat, MerchantData merchantData) {
        return null;
    }

    default GameCommissionStat calculateTeamCommissionStat(GameWagerStat gameWagerStat, MerchantData merchantData) {
        return null;
    }

    default GameCommissionStat calculateThreeLevelCommissionStat(GameWagerStat gameWagerStat, MerchantData merchantData) {
        return null;
    }
}
