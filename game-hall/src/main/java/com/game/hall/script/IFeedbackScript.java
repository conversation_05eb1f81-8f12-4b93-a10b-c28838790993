package com.game.hall.script;

import com.game.engine.script.IBaseScript;
import com.game.entity.player.Player;
import com.proto.CommonMessage;

public interface IFeedbackScript extends IBaseScript {

    default void sendFbFeedback(CommonMessage.FbInfo fbInfo, Player player, int currencyId, double totalRecharge, double recharge) {

    }

    default void sendKWaiFeedback(Player player, int currencyId, double totalRecharge, double recharge) {
    }
}
