package com.game.hall.script.activity;

import com.game.c_entity.merchant.C_LuckSpin;
import com.game.engine.script.IBaseScript;
import com.game.entity.player.Player;

public interface ILuckSpinScript extends IBaseScript {

    default void initData(Player player, C_LuckSpin c_luckSpin) {

    }

    default void resetData(Player player, C_LuckSpin c_luckSpin) {

    }

    default void luckSpinExecute(Player player, C_LuckSpin c_luckSpin, int currencyId, double wageredAmount, double rechargeAmount) {

    }

    default void referralIntervalAddTimes(Player player) {

    }
}
