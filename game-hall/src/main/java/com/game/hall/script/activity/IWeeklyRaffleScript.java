package com.game.hall.script.activity;

import com.game.engine.script.IBaseScript;
import com.game.entity.player.Player;
import com.game.entity.player.activity.weeklyRaffle.WeeklyRaffleInfo;

public interface IWeeklyRaffleScript extends IBaseScript {

    default void initData(Player player) {

    }

    default void resetData(Player player) {
    }

    default void settlement() {
    }

    default void weeklyRaffleExecute(Player player, int currencyId, double validBet) {

    }

    default int generateTicketsNote(Player player, WeeklyRaffleInfo weeklyRaffleInfo) {
        return 0;
    }
}
