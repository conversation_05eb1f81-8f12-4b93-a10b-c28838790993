package com.game.hall.script;

import com.game.entity.player.Player;
import com.game.enums.ErrorCode;
import com.game.enums.RewardReason;
import com.game.enums.SpendReason;
import com.game.hall.mrg.currency.CurrencyReportHandler;

/**
 * 货币处理器(不可对外)
 * 注意：这个处理器是不能处于与其它货币的转换关系的
 */
public interface CurrencyHandler {
    /**
     * 默认的货币最大值
     */
    double CURRENCY_MAX_VALUE = Double.MAX_VALUE;

    int getCurrencyType();

    /**
     * 获取当前货币值
     */
    double getValue(Player player);

    /**
     * 直接修改货币的值（不执行任何特殊处理），同时更新数据库或上报给{@link CurrencyReportHandler}。
     * 对于统一管理的货币，应该上报给{@link CurrencyReportHandler}，由外部统一入库更新，有利于节省开销。
     * 对于特殊的货币，应直接入库和同步给客户端。
     * (该方法外部慎用)
     */
    void setValueAndReport(Player player, double value, CurrencyReportHandler reportHandler);

    /**
     * 消耗测试
     */
    ErrorCode spendTest(Player player, double value);

    /**
     * 能消耗多少就消耗多少 -- 慎用
     */
    ErrorCode spendNoTest(Player player, double value, CurrencyReportHandler reportHandler, SpendReason reason);

    /**
     * 注意：该接口的实现通常应该调用{@link #setValueAndReport(Player, double, CurrencyReportHandler)}执行最终修改
     */
    default ErrorCode spend(Player player, double value, CurrencyReportHandler reportHandler, SpendReason reason) {
        final ErrorCode testResult = spendTest(player, value);
        if (testResult != ErrorCode.Success) {
            return testResult;
        }
        return spendNoTest(player, value, reportHandler, reason);
    }

    /**
     * 奖励测试
     */
    ErrorCode rewardTest(Player player, double value);

    /**
     * 能给多少就给多少 -- 慎用
     */
    ErrorCode rewardNoTest(Player player, double value, CurrencyReportHandler reportHandler, RewardReason reason);

    /**
     * 注意：该接口的实现通常应该调用{@link #setValueAndReport(Player, double, CurrencyReportHandler)}执行最终修改
     */
    default ErrorCode reward(Player player, double value, CurrencyReportHandler reportHandler, RewardReason reason) {
        final ErrorCode testResult = rewardTest(player, value);
        if (testResult != ErrorCode.Success) {
            return testResult;
        }
        return rewardNoTest(player, value, reportHandler, reason);
    }
}
