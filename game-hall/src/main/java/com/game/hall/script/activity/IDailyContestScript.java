package com.game.hall.script.activity;

import com.game.engine.script.IBaseScript;
import com.game.entity.player.Player;

public interface IDailyContestScript extends IBaseScript {

    default void settlement() {
    }

    default void initData(Player player) {

    }


    default void resetData(Player player) {
    }

    default void dailyContestExecute(Player player, int gameType, int currencyId, double validBet) {

    }
}
