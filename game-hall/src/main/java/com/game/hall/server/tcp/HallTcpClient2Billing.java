package com.game.hall.server.tcp;

import com.game.engine.enums.ServerType;
import com.game.engine.io.conf.NettyClientConfig;
import com.game.engine.io.netty.TcpClient;
import com.game.engine.io.netty.code.ProtocolCoder;
import com.game.engine.net.GlobalQueueContainerMrg;
import com.game.engine.service.TcpService;
import com.game.hall.server.handler.HallBillingClientHandler;
import com.game.manager.ServersMrg;
import io.netty.channel.ChannelInitializer;
import io.netty.channel.socket.SocketChannel;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 链接BillingServer
 *
 */
public class HallTcpClient2Billing extends TcpService {

    private static final Logger log = LoggerFactory.getLogger(HallTcpClient2Billing.class);

    private final TcpClient nettyClient;
    private final ServersMrg serversMrg;
    private final NettyClientConfig nettyClientConfig;

    public HallTcpClient2Billing(NettyClientConfig nettyClientConfig) {
        super(ServerType.BILLING, GlobalQueueContainerMrg.getInstance().getGlobalQueue().getLogicQueue());
        this.nettyClientConfig = nettyClientConfig;
        this.nettyClient = new TcpClient(nettyClientConfig, new BillingClientInitializer(this));
        this.serversMrg = new ServersMrg(nettyClient, nettyClientConfig);
    }

    public ServersMrg getServersMrg() {
        return serversMrg;
    }

    @Override
    public void broadcastMsg(Object msg) {
    }

    @Override
    public void start() {
        nettyClient.start();
    }

    @Override
    public void stop() {
        nettyClient.stop();
    }

    @Override
    public void checkStatus() {
        nettyClient.checkStatus();
    }

    @Override
    public String toString() {
        return nettyClientConfig.getName();
    }

    public TcpClient getNettyClient() {
        return nettyClient;
    }

    static class BillingClientInitializer extends ChannelInitializer<SocketChannel> {

        private TcpService tcpService;

        public BillingClientInitializer(TcpService tcpService) {
            this.tcpService = tcpService;
        }

        @Override
        protected void initChannel(SocketChannel ch) throws Exception {
            ch.pipeline().addLast(new ProtocolCoder());
            ch.pipeline().addLast(new HallBillingClientHandler(tcpService));
        }

    }
}
