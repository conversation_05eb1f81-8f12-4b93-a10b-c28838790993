package com.game.hall.server.tcp;

import com.game.engine.enums.ServerType;
import com.game.engine.io.conf.NettyClientConfig;
import com.game.engine.io.message.MassMessage;
import com.game.engine.io.netty.code.ProtocolCoder;
import com.game.engine.net.GlobalQueueContainerMrg;
import com.game.engine.service.TcpService;
import com.game.engine.io.netty.TcpClient;
import com.game.engine.utils.Config;
import com.game.engine.utils.MsgUtil;
import com.game.hall.server.handler.HallGateClientHandler;


import com.game.manager.ServersMrg;
import com.google.protobuf.Message;
import io.netty.channel.Channel;
import io.netty.channel.ChannelInitializer;
import io.netty.channel.socket.SocketChannel;
import org.slf4j.LoggerFactory;
import org.slf4j.Logger;

/**
 * 连接网关服务器
 */
public class HallTcpClient2Gate extends TcpService {

    private static final Logger LOGGER = LoggerFactory.getLogger(HallTcpClient2Gate.class);

    private final TcpClient tcpClient;
    private final ServersMrg serversMrg;
    private final NettyClientConfig nettyClientConfig;

    public HallTcpClient2Gate(NettyClientConfig config) {
        super(ServerType.GATE, GlobalQueueContainerMrg.getInstance().getGlobalQueue().getLogicQueue());
        this.nettyClientConfig = config;
        this.tcpClient = new TcpClient(nettyClientConfig, new GateClientInitializer(this));
        this.serversMrg = new ServersMrg(tcpClient, nettyClientConfig);
    }

    public ServersMrg getServersMrg() {
        return serversMrg;
    }

    @Override
    public void start() {
        tcpClient.start();
    }

    @Override
    public void stop() {
        try {
            tcpClient.stop();
        } catch (Exception e) {
            LOGGER.error("主线程关闭 异常", e);
        }
    }

    @Override
    public String toString() {
        return nettyClientConfig.getName();
    }

    /**
     * 广播所有服务器消息：注意，这里并不是向每个session广播，因为有可能有多个连接到同一个服务器
     *
     * @param msg byte[] 必须【msgid|data】；IoBuffer
     *            必须【msgid|data】；Message；IDMessage;MassMessage
     */
    @Override
    public void broadcastMsg(Object msg) {
        for (NettyClientConfig nettyClientConfig : tcpClient.getConfigMap().values()) {
            try {
                final Channel s = nettyClientConfig.getSession();
                MsgUtil.sendInnerMsg(s, msg, Config.SERVER_ID);
            } catch (Exception e) {
                LOGGER.error("发送广播消息", e);
            }
        }
    }

    @Override
    public void checkStatus() {
        tcpClient.checkStatus();
    }

    public TcpClient getTcpClient() {
        return tcpClient;
    }

    static class GateClientInitializer extends ChannelInitializer<SocketChannel> {
        private final TcpService tcpService;

        public GateClientInitializer(TcpService tcpService) {
            this.tcpService = tcpService;
        }

        @Override
        protected void initChannel(SocketChannel ch) throws Exception {
            ch.pipeline().addLast(new ProtocolCoder());
            ch.pipeline().addLast(new HallGateClientHandler(tcpService));
        }

    }

}
