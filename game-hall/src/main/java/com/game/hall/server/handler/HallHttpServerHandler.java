/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */

package com.game.hall.server.handler;

import com.game.engine.io.netty.handler.HttpServerIoHandler;
import com.game.engine.service.HttpService;

public class HallHttpServerHandler extends HttpServerIoHandler{

    public HallHttpServerHandler(HttpService httpService) {
        this.httpService = httpService;
    }
    
    @Override
    protected HttpService getMessagePool() {
        return httpService;
    }

}
