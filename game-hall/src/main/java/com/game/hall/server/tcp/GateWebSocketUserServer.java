package com.game.hall.server.tcp;

import com.game.engine.enums.ServerType;
import com.game.engine.io.conf.NettyServerConfig;
import com.game.engine.io.netty.WebTcpServer;
import com.game.engine.io.netty.code.WebSocketDecoder;
import com.game.engine.io.netty.code.WebSocketEncoder;
import com.game.engine.net.GlobalQueueContainerMrg;
import com.game.engine.service.TcpService;
import com.game.hall.server.handler.UserWebSocketServerHandler;
import io.netty.channel.ChannelInitializer;
import io.netty.channel.ChannelPipeline;
import io.netty.channel.socket.SocketChannel;
import io.netty.handler.codec.http.HttpObjectAggregator;
import io.netty.handler.codec.http.HttpServerCodec;
import io.netty.handler.codec.http.websocketx.WebSocketServerProtocolHandler;
import io.netty.handler.stream.ChunkedWriteHandler;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 监听用户的连接并根据消息类型进行消息分发到：登录、游戏等
 */
public final class GateWebSocketUserServer extends TcpService {

    private static final Logger LOGGER = LoggerFactory.getLogger(GateWebSocketUserServer.class);
    private final WebTcpServer webTcpServer;
    private final NettyServerConfig nettyServerConfig;

    public GateWebSocketUserServer(NettyServerConfig nettyServerConfig_user) {
        super(ServerType.GATE, GlobalQueueContainerMrg.getInstance().getGlobalQueue().getLogicQueue());
        this.nettyServerConfig = nettyServerConfig_user;
        webTcpServer = new WebTcpServer(nettyServerConfig, new UserWebSocketInitializer(this));
    }

    @Override
    public String toString() {
        return getNettyServerConfig().getName();
    }

    public NettyServerConfig getNettyServerConfig() {
        return nettyServerConfig;
    }

    public WebTcpServer getNettyServer() {
        return webTcpServer;
    }

    @Override
    public void checkStatus() {
        webTcpServer.start();
    }

    public void start() {
        webTcpServer.start();
    }

    @Override
    public void stop() {
        webTcpServer.stop();
    }

    @Override
    public void broadcastMsg(Object msg) {

    }

    static class UserWebSocketInitializer extends ChannelInitializer<SocketChannel> {


        private final TcpService tcpService;

        public UserWebSocketInitializer(TcpService tcpService) {
            this.tcpService = tcpService;
        }

        @Override
        protected void initChannel(SocketChannel ch) throws Exception {
            final ChannelPipeline pipeline = ch.pipeline();

            //websocket协议本身是基于http协议的，所以这边也要使用http解编码器
            pipeline.addLast(new HttpServerCodec());
            //以块的方式来写的处理器
            pipeline.addLast(new ChunkedWriteHandler());
            //netty是基于分段请求的，HttpObjectAggregator的作用是将请求分段再聚合,参数是聚合字节的最大长度
            pipeline.addLast(new HttpObjectAggregator(1024 * 1024 * 1024));

            pipeline.addLast(new WebSocketServerProtocolHandler("/ws"));

            pipeline.addLast(new WebSocketDecoder());
            pipeline.addLast(new WebSocketEncoder());

            pipeline.addLast(new UserWebSocketServerHandler(tcpService));
        }

    }
}
