package com.game.hall.server.handler;

import com.game.engine.io.conf.ConstantConfig;
import com.game.engine.net.LogicEventType;
import com.game.engine.service.TcpService;
import com.game.engine.utils.MsgUtil;
import com.game.user.ResistUserMsg;
import com.game.user.UserAttributeChannelData;
import com.game.user.UserSession;
import com.proto.MIDMessage;
import io.netty.buffer.ByteBuf;
import io.netty.channel.Channel;
import io.netty.channel.ChannelHandlerContext;
import io.netty.channel.SimpleChannelInboundHandler;
import io.netty.util.Attribute;
import io.netty.util.AttributeKey;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 处理user到gate的消息
 */
public class UserWebSocketServerHandler extends SimpleChannelInboundHandler<Object> {

    private static final Logger log = LoggerFactory.getLogger(UserWebSocketServerHandler.class);

    public static final AttributeKey<UserAttributeChannelData> CHANNEL_USER_ATTRIBUTE_KEY = AttributeKey.valueOf("CHANNEL_USER_ATTRIBUTE_KEY");
    public static final AttributeKey<UserSession> CHANNEL_USER_SESSION_KEY = AttributeKey.valueOf("CHANNEL_USER_SESSION_KEY");
    public static final AttributeKey<ResistUserMsg> CHANNEL_USER_MESSAGE_KEY = AttributeKey.valueOf("CHANNEL_USER_MESSAGE_KEY");

    private final TcpService tcpService;

    public UserWebSocketServerHandler(TcpService tcpService) {
        this.tcpService = tcpService;
    }

    @Override
    public void channelActive(ChannelHandlerContext ctx) {
        final Channel session = ctx.channel();
//        log.warn("已打开连接，{}", session.toString());

        final Channel channel = ctx.channel();
        final Attribute<UserAttributeChannelData> attributeData = channel.attr(CHANNEL_USER_ATTRIBUTE_KEY);
        if (attributeData.get() == null) {
            attributeData.setIfAbsent(new UserAttributeChannelData());
        }

        MsgUtil.flushQueue(tcpService.getRingBuffer(), session, LogicEventType.G_USERSERVER_ON_TCP_CONNECT, null);

    }

    @Override
    protected void channelRead0(ChannelHandlerContext ctx, Object msg) {
        final Channel channel = ctx.channel();
        final Attribute<UserAttributeChannelData> attributeData = channel.attr(CHANNEL_USER_ATTRIBUTE_KEY);
        if (!attributeData.get().isConnected()) {
            close(ctx, "未建立连接");
            return;
        }

        final Attribute<UserSession> attr = channel.attr(CHANNEL_USER_SESSION_KEY);
        if (attr.get() == null) {
            attr.set(new UserSession(channel));
        }

        final Attribute<ResistUserMsg> resistUserMsg = channel.attr(CHANNEL_USER_MESSAGE_KEY);
        if (resistUserMsg.get() == null) {
            resistUserMsg.set(new ResistUserMsg());
        }

        //请求token登录直接通过
        final ByteBuf byteBuf = (ByteBuf) msg;
        final int msgId = byteBuf.getInt(byteBuf.readerIndex());
        if (MIDMessage.MID.ReqTcpHeartBeat.getNumber() != msgId) {
            long accountId = attr.get().getAccountId();
            if (accountId == 0) {
                accountId = MsgUtil.getSessionID(channel);
            }
            if (ConstantConfig.getInstance().isDebug()) {
//                log.warn("userId：{}，receive client msg：{}", accountId, msgId);
            }
        }

        //验证token是否成功
        final UserAttributeChannelData userAttributeChannelData = channel.attr(CHANNEL_USER_ATTRIBUTE_KEY).get();
        if (!userAttributeChannelData.isTokenCheckSuccess() && msgId != MIDMessage.MID.ReqTcpTokenAuth_VALUE) {
            close(ctx, "illegal connect，token Unauthorized success！！！");
            return;
        }

        MsgUtil.flushUserQueue(tcpService.getRingBuffer(), channel, LogicEventType.G_USERSERVER_MESSAGE_EVENT_S_RECV, byteBuf);
    }

    @Override
    public void channelInactive(ChannelHandlerContext ctx) {
        channelClose(ctx);
    }

    @Override
    public void exceptionCaught(ChannelHandlerContext ctx, Throwable cause) {
        // 走到这里的原因有多种
        close(ctx, "exceptionCaught");
    }

    private void channelClose(ChannelHandlerContext ctx) {
        final Channel session = ctx.channel();
//        log.warn("连接{}已关闭sessionClosed", session);
        try {
            MsgUtil.flushQueue(tcpService.getRingBuffer(), session, LogicEventType.G_USERSERVER_ON_DISCONNECT, null);
        } catch (Exception e) {
            log.error("", e);
        } finally {
            close(ctx, "channelInactive");
        }
    }

    private void close(ChannelHandlerContext ctx, String reason) {
        try {
            Channel channel = ctx.channel();
            UserAttributeChannelData attributeData = channel.attr(CHANNEL_USER_ATTRIBUTE_KEY).get();
            attributeData.setConnected(false);
            attributeData.setTokenCheckSuccess(false);
//            MsgUtil.close(channel, reason);
            channel.close();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public TcpService getService() {
        return tcpService;
    }

}
