package com.game.hall.server.tcp;

import com.game.engine.enums.ServerType;
import com.game.engine.io.conf.NettyServerConfig;
import com.game.engine.io.netty.TcpServer;
import com.game.engine.io.netty.code.ProtocolCoder;
import com.game.engine.net.GlobalQueueContainerMrg;
import com.game.engine.service.TcpService;
import com.game.hall.server.handler.HallAgentGameServerHandler;
import io.netty.channel.ChannelInitializer;
import io.netty.channel.socket.SocketChannel;

public class HallTcpAgentGameServer extends TcpService {

    private final TcpServer nettyServer;

    private final NettyServerConfig nettyServerConfig;

    public HallTcpAgentGameServer(NettyServerConfig nettyServerConfig) {
        super(ServerType.HALL, GlobalQueueContainerMrg.getInstance().getGlobalQueue().getLogicQueue());
        this.nettyServerConfig = nettyServerConfig;
        this.nettyServer = new TcpServer(nettyServerConfig, new HallAgentGameServerInitializer(this));
    }

    @Override
    public void broadcastMsg(Object msg) {

    }

    public NettyServerConfig getNettyServerConfig() {
        return nettyServerConfig;
    }

    @Override
    public void start() {
        this.nettyServer.start();
    }

    @Override
    public void stop() {
        this.nettyServer.stop();
    }

    public TcpServer getNettyServer() {
        return nettyServer;
    }

    static class HallAgentGameServerInitializer extends ChannelInitializer<SocketChannel> {

        private final TcpService tcpService;

        public HallAgentGameServerInitializer(TcpService tcpService) {
            this.tcpService = tcpService;
        }

        @Override
        protected void initChannel(SocketChannel ch) throws Exception {
            ch.pipeline().addLast(new ProtocolCoder());
            ch.pipeline().addLast(new HallAgentGameServerHandler(tcpService));
        }

    }
}
