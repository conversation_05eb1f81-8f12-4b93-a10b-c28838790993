package com.game.hall.server.tcp;

import com.game.engine.enums.ServerType;
import com.game.engine.io.conf.NettyClientConfig;
import com.game.engine.io.netty.TcpClient;
import com.game.engine.io.netty.code.ProtocolCoder;
import com.game.engine.net.GlobalQueueContainerMrg;
import com.game.engine.service.TcpService;
import com.game.hall.server.handler.HallLogClientHandler;
import io.netty.channel.ChannelInitializer;
import io.netty.channel.socket.SocketChannel;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 链接LogServer
 *
 */
public class HallTcpClient2Log extends TcpService {

    private static final Logger log = LoggerFactory.getLogger(HallTcpClient2Log.class);

    private final NettyClientConfig nettyClientConfig;
    private final TcpClient nettyClient;

    public HallTcpClient2Log(NettyClientConfig nettyClientConfig) {
        super(ServerType.LOG, GlobalQueueContainerMrg.getInstance().getGlobalQueue().getLogicQueue());
        this.nettyClientConfig = nettyClientConfig;
        this.nettyClient = new TcpClient(nettyClientConfig, new LogClientInitializer(this));
    }

    @Override
    public void broadcastMsg(Object msg) {
    }

    @Override
    public void start() {
        nettyClient.start();
    }

    @Override
    public void stop() {
        nettyClient.stop();
    }

    @Override
    public void checkStatus() {
        nettyClient.checkStatus();
    }

    @Override
    public String toString() {
        return nettyClientConfig.getName();
    }


    public TcpClient getNettyClient() {
        return nettyClient;
    }

    static class LogClientInitializer extends ChannelInitializer<SocketChannel> {

        private TcpService tcpService;

        public LogClientInitializer(TcpService tcpService) {
            this.tcpService = tcpService;
        }

        @Override
        protected void initChannel(SocketChannel ch) throws Exception {
            ch.pipeline().addLast(new ProtocolCoder());
            ch.pipeline().addLast(new HallLogClientHandler(tcpService));
        }

    }
}
