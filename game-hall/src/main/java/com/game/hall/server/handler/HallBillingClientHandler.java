/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.game.hall.server.handler;

import com.game.engine.net.LogicEventType;
import com.game.engine.service.TcpService;
import com.game.engine.utils.MsgUtil;
import io.netty.buffer.ByteBuf;
import io.netty.channel.Channel;
import io.netty.channel.ChannelHandlerContext;
import io.netty.channel.ChannelInboundHandlerAdapter;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class HallBillingClientHandler extends ChannelInboundHandlerAdapter {

    private static final Logger LOGGER = LoggerFactory.getLogger(HallBillingClientHandler.class);
    private final TcpService service;

    public HallBillingClientHandler(TcpService tcpService) {
        this.service = tcpService;
    }


    @Override
    public void channelActive(ChannelHandlerContext ctx) {
        Channel session = ctx.channel();
        LOGGER.warn("已打开连接，{}", session.toString());

        MsgUtil.flushQueue(service.getRingBuffer(), session, LogicEventType.H_BILLINGCLIENT_ON_TCP_CONNECT, null);
    }

    @Override
    public void channelInactive(ChannelHandlerContext ctx) {
        final Channel session = ctx.channel();
//        LOGGER.warn("连接{}，已关闭sessionClosed", session);

        MsgUtil.flushQueue(service.getRingBuffer(), session, LogicEventType.H_BILLINGCLIENT_ON_DISCONNECT, null);
        MsgUtil.close(session, "channelInactive");
    }

    @Override
    public void channelRead(ChannelHandlerContext ctx, Object msg) {
        final Channel session = ctx.channel();
        final ByteBuf byteBuf = (ByteBuf) msg;
        MsgUtil.flushQueue(service.getRingBuffer(), session, LogicEventType.H_BILLINGCLIENT_MESSAGE_EVENT_C_RECV, byteBuf);
    }

    @Override
    public void exceptionCaught(ChannelHandlerContext ctx, Throwable cause) {
        Channel session = ctx.channel();
        LOGGER.error("连接{}异常：{}", session, cause);
        MsgUtil.close(session, "exceptionCaught");
    }

}
