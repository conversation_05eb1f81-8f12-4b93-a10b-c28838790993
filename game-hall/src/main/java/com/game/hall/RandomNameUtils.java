package com.game.hall;

public class RandomNameUtils {

    // 英文字母表
    private static final String ALPHABET = "abcdefghijklmnopqrstuvwxyz123456789";

    public static String generateRandomName() {
//        // 随机生成名字的长度（5到9个字符）
//        int length = random.nextInt(5) + 5; // 5 到 9
//        StringBuilder name = new StringBuilder(length);
//        // 生成名字的第一个字母（大写）
//        name.append(Character.toUpperCase(ALPHABET.charAt(random.nextInt(ALPHABET.length()))));
//        // 生成名字的剩余字母（小写）
//        for (int i = 1; i < length; i++) {
//            name.append(ALPHABET.charAt(random.nextInt(ALPHABET.length())));
//        }
        final String accountId = String.valueOf((long) (Math.random() * 9000000000L) + 1000000000L);
        StringBuilder name = new StringBuilder();
        name.append("WG")
                .append(accountId);
        return name.toString();
    }

}
