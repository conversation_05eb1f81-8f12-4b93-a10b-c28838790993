package com.game.user;

import com.game.engine.utils.TimeUtil;

public class ResistUserMsg {

    /**
     * 发送次数
     */
    private int sendTimes;

    private long startTime;

    public ResistUserMsg() {
        this.startTime = TimeUtil.currentTimeMillis();
    }

    public int getSendTimes() {
        return sendTimes;
    }

    public void setSendTimes(int sendTimes) {
        this.sendTimes = sendTimes;
    }

    public long getStartTime() {
        return startTime;
    }

    public void setStartTime(long startTime) {
        this.startTime = startTime;
    }

    public void incSendTimes() {
        this.sendTimes++;
    }

    public void reset() {
        this.startTime = TimeUtil.currentTimeMillis();
        this.sendTimes = 0;
    }
}
