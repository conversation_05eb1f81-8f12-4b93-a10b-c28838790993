package com.game.user;

public class UserAttributeChannelData {

    private boolean connected = true;

    private boolean tokenCheckSuccess = false;

    private long remoteGuid;

    public boolean isConnected() {
        return connected;
    }

    public void setConnected(boolean connected) {
        this.connected = connected;
    }

    public boolean isTokenCheckSuccess() {
        return tokenCheckSuccess;
    }

    public void setTokenCheckSuccess(boolean tokenCheckSuccess) {
        this.tokenCheckSuccess = tokenCheckSuccess;
    }

    public long getRemoteGuid() {
        return remoteGuid;
    }

    public void setRemoteGuid(long remoteGuid) {
        this.remoteGuid = remoteGuid;
    }
}
