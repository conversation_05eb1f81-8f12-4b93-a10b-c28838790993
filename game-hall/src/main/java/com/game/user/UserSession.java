package com.game.user;

import com.game.engine.enums.NetObject;
import com.game.engine.utils.MsgUtil;
import com.game.entity.player.Player;
import com.game.enums.ErrorCode;
import com.game.hall.main.HallServer;
import com.game.hall.mrg.UserTcpMrg;
import com.game.hall.mrg.player.PlayerMrg;
import com.proto.TcpMessage;
import io.netty.channel.Channel;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Objects;


public final class UserSession implements NetObject {

    private final static Logger LOGGER = LoggerFactory.getLogger(UserSession.class);

    //上次记录的服务器不能连接错误回报时间间隔
    private final static int SERVERERRORTIME = 3000;

    //上次记录的服务器不能连接错误回报时间（间隔时间发）
    private long lastServerErrorTime;

    private String business_no;
    private String account;
    private long accountId;
    //会话id
    private long sessionId;
    //所在大厅服务器
    private int onHallServerId;
    //浏览器标识
    private String browserId;
    //ip
    private String ip;

    private Channel userSession;

    public UserSession() {

    }

    public UserSession(Channel userSession) {
        this.userSession = userSession;
        this.sessionId = MsgUtil.getSessionID(this.userSession);
        this.ip = MsgUtil.getIp(userSession);
    }

    public Channel getUserSession() {
        return userSession;
    }

    public void setAccountInfo(long userId, String account, String business_no, String browserId) {
        this.accountId = userId;
        this.account = account;
        this.business_no = business_no;
        this.browserId = browserId;
    }

    public long getAccountId() {
        return accountId;
    }

    public String getBusiness_no() {
        return business_no;
    }

    public void setBusiness_no(String business_no) {
        this.business_no = business_no;
    }

    public String getBrowserId() {
        return browserId;
    }

    public void setBrowserId(String browserId) {
        this.browserId = browserId;
    }

    /**
     * 发送退出大厅消息
     */
    private void sendLoginOutHall() {
        if (this.accountId < 1) {
            return;
        }
        HallServer.getInstance().asyncExecute(this.accountId, () -> {
            final Player player = PlayerMrg.getInstance().getOnlinePlayerMap().get(this.accountId);
            if (player == null) {
                return;
            }
            PlayerMrg.getInstance().sendKickOutPlayerMsg(player);
            PlayerMrg.getInstance().signOut(player);
        });
    }

    public void destroy(boolean quit) {
        UserTcpMrg.getInstance().onUserLogout(this);
        if (quit) {
            this.sendLoginOutHall();
        }
    }

    /**
     * 发送消息
     *
     * @param msg
     * @return
     */
    public boolean sendToUser(Object msg) {
        try {
            return MsgUtil.sendClientMsg(userSession, msg);
        } catch (Exception e) {
            LOGGER.error("sendToUser:", e);
        }
        return false;
    }

    @Override
    public boolean sendMsg(Object message, long id) {
        return sendToUser(message);
    }

    @Override
    public long getSessionID() {
        return sessionId;
    }

    @Override
    public long getId() {
        return accountId;
    }

    public int getOnHallServerId() {
        return onHallServerId;
    }

    public void setOnHallServerId(int onHallServerId) {
        this.onHallServerId = onHallServerId;
    }

    public String getAccount() {
        return account;
    }

    public void setAccount(String account) {
        this.account = account;
    }

    public void setAccountId(long accountId) {
        this.accountId = accountId;
    }

    public long getSessionId() {
        return sessionId;
    }

    public void setSessionId(long sessionId) {
        this.sessionId = sessionId;
    }

    public String getIp() {
        return ip;
    }

    public void setIp(String ip) {
        this.ip = ip;
    }

    public void setUserSession(Channel userSession) {
        this.userSession = userSession;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        UserSession that = (UserSession) o;
        return sessionId == that.sessionId;
    }

    @Override
    public int hashCode() {
        return Objects.hash(sessionId);
    }
}
