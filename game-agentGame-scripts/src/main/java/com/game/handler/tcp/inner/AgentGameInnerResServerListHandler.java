package com.game.handler.tcp.inner;

import com.game.engine.enums.ServerType;
import com.game.engine.io.handler.TcpHandler;
import com.game.engine.script.IHandlerEntity;
import com.game.engine.struct.ServerInfo;
import com.game.gamesr.main.AgentGameServer;
import com.game.manager.ServersMrg;
import com.proto.InnerMessage;
import com.proto.MIDMessage;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.*;

@IHandlerEntity(mid = MIDMessage.MID.InnerResServerList_VALUE, msg = InnerMessage.InnerResServerListMessage.class)
public class AgentGameInnerResServerListHandler extends TcpHandler {
    private static final Logger LOGGER = LoggerFactory.getLogger(AgentGameInnerResServerListHandler.class);

    @Override
    public void run() {
        try {
            final InnerMessage.InnerResServerListMessage resMessage = (InnerMessage.InnerResServerListMessage) getMessage();
            final List<InnerMessage.ServerInfoList> serverList = resMessage.getServerListList();

            final Set<Integer> serverIds = new HashSet<>();
            for (InnerMessage.ServerInfoList serverInfo : serverList) {
                if (serverInfo.getType() == ServerType.HALL.getType()) {
                    final ServersMrg serversMrg = AgentGameServer.getInstance().getAgentGameTcpClient2Hall().getServersMrg();
                    for (InnerMessage.InnerServerInfo s : serverInfo.getServerInfosList()) {
                        serversMrg.updateServerInfo(s);
                        serverIds.add(s.getId());
                    }

                    //移除关闭的hall
                    final Map<Integer, ServerInfo> hallServers = serversMrg.getServerMap();
                    if (hallServers.size() != serverInfo.getServerInfosList().size()) {
                        final ArrayList<Integer> ids = new ArrayList<>(hallServers.keySet());
                        ids.removeAll(serverIds);
                        for (int id : ids) {
                            serversMrg.removeTcpClient(id);
                        }
                    }
                }
            }

        } catch (Exception e) {
            LOGGER.error("BillingInnerResServerListHandler ", e);
        }
    }

}
