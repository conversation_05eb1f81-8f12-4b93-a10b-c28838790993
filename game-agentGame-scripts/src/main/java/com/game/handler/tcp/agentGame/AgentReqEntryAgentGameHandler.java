package com.game.handler.tcp.agentGame;

import com.game.c_entity.middleplatform.C_BaseGamePlatform;
import com.game.dao.player.PlayerDao;
import com.game.engine.io.handler.TcpHandler;
import com.game.engine.script.IHandlerEntity;
import com.game.engine.script.ScriptLoader;
import com.game.entity.player.Player;
import com.game.enums.ErrorCode;
import com.game.gamesr.main.AgentGameServer;
import com.game.gamesr.manager.AgentGameMrg;
import com.game.gamesr.manager.DataAgentGameMrg;
import com.game.gamesr.scripts.IAgentGameLogicScript;
import com.game.manager.EntityDaoMrg;
import com.game.utils.VirtualThreadUtils;
import com.proto.HallMessage;
import com.proto.MIDMessage;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@IHandlerEntity(mid = MIDMessage.MID.ReqEntryAgentGame_VALUE, msg = HallMessage.ReqEntryAgentGameMessage.class)
public class AgentReqEntryAgentGameHandler extends TcpHandler {
    private static final Logger LOGGER = LoggerFactory.getLogger(AgentReqEntryAgentGameHandler.class);

    @Override
    public void run() {
        final HallMessage.ResEntryAgentGameMessage.Builder res = HallMessage.ResEntryAgentGameMessage.newBuilder();
        res.setMsgID(MIDMessage.MID.ResEntryAgentGame_VALUE);
        try {
            AgentGameServer.getInstance().getBlockingTaskSchedulerMrg().submitCall(() ->
                            EntityDaoMrg.getInstance().getDao(PlayerDao.class).getById(pid))
                    .thenAccept((Player player) -> {
                        if (player == null) {
                            res.setError(ErrorCode.Data_Error.getCode());
                            replyWithUdpSessionId(res.build());
                            return;
                        }

                        final HallMessage.ReqEntryAgentGameMessage req = (HallMessage.ReqEntryAgentGameMessage) getMessage();
                        final int platformId = req.getPlatformId();
                        final int gameId = req.getGameId();
                        final C_BaseGamePlatform c_baseGamePlatform = DataAgentGameMrg.getInstance()
                                .findC_BaseGamePlatformId(this.getClass().getSimpleName(), platformId);
                        if (c_baseGamePlatform == null) {
                            LOGGER.info("playerId：{}，entry gameId：{}，fail", pid, gameId);
                            res.setError(ErrorCode.Data_Error.getCode());
                            replyWithUdpSessionId(res.build());
                            return;
                        }

                        AgentGameMrg.getInstance().addAgentGamePlayer(req, player);
                        final String scriptName = "AgentGameLogic_" + c_baseGamePlatform.getSupplierId() + "_Script";
                        VirtualThreadUtils.execute(() -> {
                            ScriptLoader.getInstance().functionScript(scriptName,
                                    (IAgentGameLogicScript script) -> script.entryAgentGame(req, player, session, udpSessionId));
                        });
                        LOGGER.info("playerId：{}，entry platformName：{}，game：{} ", pid, c_baseGamePlatform.getPlatformName(), req.getGameId());
                        LOGGER.info("online num；{}", AgentGameMrg.getInstance().getAgentGamePlayerMap().size());
                    });
        } catch (Exception e) {
            LOGGER.error("AgentReqEntryAgentGameHandler", e);
            res.setError(ErrorCode.Internal_Server_Error.getCode());
            replyWithUdpSessionId(res.build());
        }
    }

}
