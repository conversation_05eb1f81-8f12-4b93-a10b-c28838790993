package com.game.handler.tcp.inner;

import com.game.engine.io.handler.TcpHandler;
import com.game.engine.script.IHandlerEntity;
import com.game.engine.script.ScriptLoader;
import com.game.gamesr.scripts.IAgentGameLogicScript;
import com.proto.InnerMessage;
import com.proto.MIDMessage;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@IHandlerEntity(mid = MIDMessage.MID.InnerNotifyCurrencyUpdate_VALUE, msg = InnerMessage.InnerNotifyCurrencyUpdateMessage.class)
public class AgentInnerNotifyCurrencyUpdateHandler extends TcpHandler {
    private static final Logger LOGGER = LoggerFactory.getLogger(AgentInnerNotifyCurrencyUpdateHandler.class);

    @Override
    public void run() {
        final InnerMessage.InnerNotifyCurrencyUpdateMessage req = (InnerMessage.InnerNotifyCurrencyUpdateMessage) getMessage();
        final InnerMessage.NotifyData notifyData = req.getNotifyData();
        final int supplierId = notifyData.getSupplierId();
        final String scriptName = "AgentGameLogic_" + supplierId + "_Script";
        ScriptLoader.getInstance().consumerScript(scriptName, (IAgentGameLogicScript script)
                -> script.notifyUpdateCurrency(notifyData, pid));
    }
}
