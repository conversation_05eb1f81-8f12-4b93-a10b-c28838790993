//package com.game.handler.http.agentGame_1004_pg;
//
//import com.alibaba.fastjson.JSONObject;
//import com.game.c_entity.middleplatform.C_BaseGamePlatform;
//import com.game.engine.io.handler.HttpHandler;
//import com.game.engine.math.BigDecimalUtils;
//import com.game.engine.script.IHandlerEntity;
//import com.game.engine.script.ScriptLoader;
//import com.game.engine.utils.JsonUtils;
//import com.game.engine.utils.MsgUtil;
//import com.game.entity.game.GameNote;
//import com.game.entity.player.Player;
//import com.game.enums.ChangeType;
//import com.game.gamesr.NotifyData;
//import com.game.gamesr.manager.AgentGameMrg;
//import com.game.gamesr.manager.DataAgentGameMrg;
//import com.game.gamesr.scripts.IAgentGameScript;
//import org.slf4j.Logger;
//import org.slf4j.LoggerFactory;
//
//import java.util.LinkedHashMap;
//import java.util.Map;
//import java.util.Objects;
//
////http://127.0.0.1:8680/pg/Cash/Adjustment
//@IHandlerEntity(path = "/pg/Cash/Adjustment", desc = "调整余额")
//public class ReqAdjustmentHandler extends HttpHandler {
//    private static final Logger LOGGER = LoggerFactory.getLogger(ReqAdjustmentHandler.class);
//
//    @Override
//    public void run() {
//        final Map<String, Object> responseMap = new LinkedHashMap<>();
//        try {
//            LOGGER.info("ReqAdjustmentHandler，params：{}", JsonUtils.writeAsJson(paramsMap));
//
//            final String operator_token = (String) paramsMap.get("operator_token");
//            final String secret_key = (String) paramsMap.get("secret_key");
//
//            final String adjustment_id = (String) paramsMap.get("adjustment_id");
//
//            final String transfer_amount = (String) paramsMap.get("transfer_amount");
//            final String real_transfer_amount = (String) paramsMap.get("real_transfer_amount");
//            final String adjustment_time = (String) paramsMap.get("adjustment_time");
//
//            final String player_name = (String) paramsMap.get("player_name");
//            final String currency_code = (String) paramsMap.get("currency_code");
//
//            if (Double.parseDouble(transfer_amount) != Double.parseDouble(real_transfer_amount)) {
//                final JSONObject error = new JSONObject();
//                error.put("code", 3107);
//
//                responseMap.put("data", null);
//                responseMap.put("error", error);
//                MsgUtil.responseHttp(responseMap, session);
//                return;
//            }
//
//            final String currency = player_name.substring(0, 3);
//            if (!Objects.equals(currency_code, currency)) {
//                final JSONObject error = new JSONObject();
//                error.put("code", 1034);
//
//                responseMap.put("data", null);
//                responseMap.put("error", error);
//                MsgUtil.responseHttp(responseMap, session);
//                return;
//            }
//
//            final long playerId = AgentGameMrg.getInstance().getAgentGamePlayerId(player_name);
//            final Player player = AgentGameMrg.getInstance().findDbPlayer(playerId);
//            if (player == null) {
//                final JSONObject error = new JSONObject();
//                error.put("code", 3004);
//
//                responseMap.put("data", null);
//                responseMap.put("error", error);
//                MsgUtil.responseHttp(responseMap, session);
//                return;
//            }
//
//            final C_BaseGamePlatform c_baseGamePlatform = DataAgentGameMrg.getInstance().findC_BaseGamePlatform(this.getClass().getSimpleName(), 2);
//            if (c_baseGamePlatform == null) {
//                final JSONObject error = new JSONObject();
//                error.put("code", 1034);
//
//                responseMap.put("data", null);
//                responseMap.put("error", error);
//                MsgUtil.responseHttp(responseMap, session);
//                return;
//            }
//
//            if (!Objects.equals(c_baseGamePlatform.getToken(), operator_token)) {
//                final JSONObject error = new JSONObject();
//                error.put("code", 1034);
//
//                responseMap.put("data", null);
//                responseMap.put("error", error);
//                MsgUtil.responseHttp(responseMap, session);
//                return;
//            }
//
//            if (!Objects.equals(c_baseGamePlatform.getSecretKey(), secret_key)) {
//                final JSONObject error = new JSONObject();
//                error.put("code", 1034);
//
//                responseMap.put("data", null);
//                responseMap.put("error", error);
//                MsgUtil.responseHttp(responseMap, session);
//                return;
//            }
//
//            final double realBalance = ScriptLoader.getInstance().functionScript("AgentGameScript",
//                    (IAgentGameScript script) -> script.realBalance(player));
//
//            double balance = ScriptLoader.getInstance().functionScript("AgentGameScript",
//                    (IAgentGameScript script) -> script.amountTransformUsd(player, realBalance));
//            final double beforeBalance = balance;
//
//            final GameNote gameNote = AgentGameMrg.getInstance().getTempPgMap().get(adjustment_id);
//            if (gameNote != null) {
//                final GameNote finalGameNote = gameNote;
//                final double usdBeforeBalance = ScriptLoader.getInstance().functionScript("AgentGameScript",
//                        (IAgentGameScript script) -> script.amountTransformUsd(player, finalGameNote.getBeforeBalance()));
//
//                final double usdBalance = ScriptLoader.getInstance().functionScript("AgentGameScript",
//                        (IAgentGameScript script) -> script.amountTransformUsd(player, finalGameNote.getBalance()));
//
//                final JSONObject data = new JSONObject();
//                data.put("adjust_amount", transfer_amount);
//                data.put("balance_before", BigDecimalUtils.round(usdBeforeBalance, 2));
//                data.put("balance_after", BigDecimalUtils.round(usdBalance, 2));
//                data.put("updated_time", adjustment_time);
//                data.put("real_transfer_amount", real_transfer_amount);
//
//                responseMap.put("data", data);
//                responseMap.put("error", null);
//                return;
//            }
//
//            balance = BigDecimalUtils.add(balance, Double.parseDouble(transfer_amount), 2);
//
//            if (balance < 0) {
//                final JSONObject error = new JSONObject();
//                error.put("code", 3202);
//
//                responseMap.put("data", null);
//                responseMap.put("error", error);
//                MsgUtil.responseHttp(responseMap, session);
//                return;
//            }
//
//            final GameNote newGameNote = new GameNote();
//            newGameNote.setRoundId(adjustment_id);
//            newGameNote.setBeforeBalance(BigDecimalUtils.round(beforeBalance, 2));
//            newGameNote.setBalance(BigDecimalUtils.round(balance, 2));
//            AgentGameMrg.getInstance().addPgGameNote(newGameNote);
//
//            final JSONObject data = new JSONObject();
//            data.put("adjust_amount", transfer_amount);
//            data.put("balance_before", BigDecimalUtils.round(beforeBalance, 2));
//            data.put("balance_after", BigDecimalUtils.round(balance, 2));
//            data.put("updated_time", adjustment_time);
//            data.put("real_transfer_amount", real_transfer_amount);
//
//            responseMap.put("data", data);
//            responseMap.put("error", null);
//            MsgUtil.responseHttp(responseMap, session);
//
//            final double amount = ScriptLoader.getInstance().functionScript("AgentGameScript",
//                    (IAgentGameScript script) -> script.usdTransformAmount(player, Double.parseDouble(transfer_amount)));
//
//            int changeType = 0;
//            if (Double.parseDouble(transfer_amount) > 0) {
//                changeType = ChangeType.win.getChange();
//            } else {
//                changeType = ChangeType.cost.getChange();
//            }
//
//            final NotifyData notifyData = new NotifyData();
//            notifyData.setType(changeType)
//                    .setPid(playerId)
//                    .setNoteId("");
//            if (changeType == ChangeType.win.getChange()) {
//                notifyData.setWin(amount);
//            } else {
//                notifyData.setBetAmount(Math.abs(amount));
//            }
//            ScriptLoader.getInstance().consumerScript("AgentGameScript",
//                    (IAgentGameScript script) -> script.updateCurrency(notifyData));
//        } catch (Exception e) {
//            LOGGER.error("ReqAdjustmentHandler", e);
//            final JSONObject error = new JSONObject();
//            error.put("code", 1034);
//
//            responseMap.put("data", null);
//            responseMap.put("error", error);
//            MsgUtil.responseHttp(responseMap, session);
//        }
//    }
//
//}
