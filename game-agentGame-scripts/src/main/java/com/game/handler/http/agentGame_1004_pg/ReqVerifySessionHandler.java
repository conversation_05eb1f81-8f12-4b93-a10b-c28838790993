package com.game.handler.http.agentGame_1004_pg;


import com.alibaba.fastjson.JSONObject;
import com.game.c_entity.middleplatform.C_BaseGamePlatform;
import com.game.engine.io.conf.ConstantConfig;
import com.game.engine.io.handler.HttpHandler;
import com.game.engine.script.IHandlerEntity;
import com.game.engine.utils.JsonUtils;
import com.game.engine.utils.MsgUtil;
import com.game.entity.player.Player;
import com.game.enums.AgentGame;
import com.game.gamesr.manager.AgentGameMrg;
import com.game.gamesr.manager.DataAgentGameMrg;
import com.game.gamesr.utils.JWTUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.LinkedHashMap;
import java.util.Map;
import java.util.Objects;

//http://127.0.0.1:8680/pg/VerifySession
@IHandlerEntity(path = "/pg/VerifySession", desc = "令牌验证")
public class ReqVerifySessionHandler extends HttpHandler {
    private static final Logger LOGGER = LoggerFactory.getLogger(ReqVerifySessionHandler.class);

    @Override
    public void run() {
        final Map<String, Object> responseMap = new LinkedHashMap<>();
        try {
            if (ConstantConfig.getInstance().isAgentDebug()) {
                LOGGER.info("ReqVerifySessionHandler，params：{}", JsonUtils.writeAsJson(paramsMap));
            }

            final String operator_token = (String) paramsMap.get("operator_token");
            final String secret_key = (String) paramsMap.get("secret_key");
            final String operator_player_session = (String) paramsMap.get("operator_player_session");

            final Map<String, Object> jwtMap = JWTUtils.verify(operator_player_session);

            if (jwtMap.isEmpty() || !Objects.isNull(jwtMap.get("error"))) {
                LOGGER.warn("ReqVerifySessionHandler，Invalid token");
                final JSONObject error = new JSONObject();
                error.put("code", "1034");
                error.put("message", "Invalid request");

                responseMap.put("data", null);
                responseMap.put("error", error);
                MsgUtil.responseHttp(responseMap, session);
                return;
            }

            final long playerId = (long) jwtMap.get("userId");
            final String playerName = (String) jwtMap.get("playerName");
            final String currency = (String) jwtMap.get("currency");

            final Player player = AgentGameMrg.getInstance().findDbPlayer(playerId);
            if (player == null) {
                LOGGER.warn("playerId；{}，player not exist", playerId);

                final JSONObject error = new JSONObject();
                error.put("code", "3004");
                error.put("message", "Player does not exist");

                responseMap.put("data", null);
                responseMap.put("error", error);
                MsgUtil.responseHttp(responseMap, session);
                return;
            }

            final C_BaseGamePlatform c_baseGamePlatform = DataAgentGameMrg.getInstance()
                    .findC_BaseGamePlatformSupplierId(this.getClass().getSimpleName(), AgentGame.PG.getType());
            if (c_baseGamePlatform == null) {
                final JSONObject error = new JSONObject();
                error.put("code", "1034");
                error.put("message", "Invalid request");

                responseMap.put("data", null);
                responseMap.put("error", error);
                MsgUtil.responseHttp(responseMap, session);
                return;
            }

            if (!Objects.equals(c_baseGamePlatform.getToken(), operator_token)) {
                LOGGER.warn("ReqVerifySessionHandler，playerId；{}，Invalid operator_token", playerId);
                final JSONObject error = new JSONObject();
                error.put("code", "1034");
                error.put("message", "Invalid request");

                responseMap.put("data", null);
                responseMap.put("error", error);
                MsgUtil.responseHttp(responseMap, session);
                return;
            }

            if (!Objects.equals(c_baseGamePlatform.getSecretKey(), secret_key)) {
                LOGGER.warn("ReqVerifySessionHandler，playerId；{}，Invalid secret_key", playerId);
                final JSONObject error = new JSONObject();
                error.put("code", "1034");
                error.put("message", "Invalid request");

                responseMap.put("data", null);
                responseMap.put("error", error);
                MsgUtil.responseHttp(responseMap, session);
                return;
            }

            final JSONObject data = new JSONObject();
            data.put("player_name", playerName);
            data.put("nickname", playerName);
            data.put("currency", currency);

            responseMap.put("data", data);
            responseMap.put("error", null);
            MsgUtil.responseHttp(responseMap, session);
        } catch (Exception e) {
            LOGGER.error("ReqVerifySessionHandler", e);
            final JSONObject error = new JSONObject();
            error.put("code", "1034");
            error.put("message", "Invalid request");

            responseMap.put("data", null);
            responseMap.put("error", error);
            MsgUtil.responseHttp(responseMap, session);
        }
    }

}
