package com.game.handler.http.agentGame_1002_st8;

import com.game.engine.io.handler.HttpHandler;
import com.game.engine.math.BigDecimalUtils;
import com.game.engine.script.IHandlerEntity;
import com.game.engine.script.ScriptLoader;
import com.game.engine.utils.JsonUtils;
import com.game.engine.utils.MsgUtil;
import com.game.entity.game.GameNote;
import com.game.entity.player.Player;
import com.game.enums.ChangeType;
import com.game.gamesr.NotifyData;
import com.game.gamesr.manager.AgentGameMrg;
import com.game.gamesr.scripts.IAgentGameScript;
import io.netty.util.internal.StringUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.LinkedHashMap;
import java.util.Map;

//http://127.0.0.1:8680/st8/cancel
@IHandlerEntity(path = "/st8/cancel", desc = "退款")
public class ReqCancelHand<PERSON> extends HttpHandler {
    private static final Logger LOGGER = LoggerFactory.getLogger(ReqCancelHandler.class);

    @Override
    public void run() {
        final Map<String, Object> responseMap = new LinkedHashMap<>();
        try {
            LOGGER.info("ReqCancelHandler，params：{}", JsonUtils.writeAsJson(paramsMap));

            final String round = (String) paramsMap.get("round");

            final String players = (String) paramsMap.get("player");
            final String currency = (String) paramsMap.get("currency");
            final String amount = (String) paramsMap.get("amount");
            final long playerId = AgentGameMrg.getInstance().getAgentGamePlayerId(players);

            final Player player = AgentGameMrg.getInstance().findDbPlayer(playerId);
            if (player == null) {
                responseMap.put("status", "error");
                responseMap.put("reason", "player_not_found");
                MsgUtil.responseHttp(responseMap, session);
                return;
            }

            final double realBalance = ScriptLoader.getInstance().functionScript("AgentGameScript",
                    (IAgentGameScript script) -> script.realBalance(player));

            double balance = ScriptLoader.getInstance().functionScript("AgentGameScript",
                    (IAgentGameScript script) -> script.amountTransformUsd(player, realBalance));


            final GameNote gameNote = AgentGameMrg.getInstance().findRoundIdGameNote(player, round);
            if (gameNote == null) {
                LOGGER.warn("round：{}，is not exist", round);
                if (StringUtil.isNullOrEmpty(round)) {
                    responseMap.put("status", "ok");
                    responseMap.put("currency", currency);
                    responseMap.put("balance", BigDecimalUtils.round(balance + Double.parseDouble(amount), 2) + "");
                    MsgUtil.responseHttp(responseMap, session);
                    return;
                }

                responseMap.put("status", "ok");
                responseMap.put("currency", currency);
                responseMap.put("balance", BigDecimalUtils.round(balance, 2) + "");
                MsgUtil.responseHttp(responseMap, session);
                return;
            }

            if (gameNote.getStatus() == 3) {
                LOGGER.warn("Already refunded");
                final double usdBalance = ScriptLoader.getInstance().functionScript("AgentGameScript",
                        (IAgentGameScript script) -> script.amountTransformUsd(player, gameNote.getBalance()));

                responseMap.put("status", "ok");
                responseMap.put("currency", currency);
                responseMap.put("balance", BigDecimalUtils.round(BigDecimalUtils.add(usdBalance, Double.parseDouble(amount), 2), 2) + "");
                MsgUtil.responseHttp(responseMap, session);
                return;
            }

//            final double win = ScriptLoader.getInstance().functionScript("AgentGameScript",
//                    (IAgentGameScript script) -> script.amountTransformUsd(player, gameNote.getWin()));

//            balance = BigDecimalUtils.sub(BigDecimalUtils.add(balance, Double.parseDouble(amount), 2), win, 2);

//            responseMap.put("status", "ok");
//            responseMap.put("currency", currency);
//            responseMap.put("balance", BigDecimalUtils.round(balance, 2) + "");
//            MsgUtil.responseHttp(responseMap, session);

            final NotifyData notifyData = new NotifyData();
            notifyData.setType(ChangeType.refund.getType())
                    .setPid(playerId)
                    .setBetAmount(gameNote.getBetAmount())
                    .setWin(gameNote.getWin())
                    .setCurrency(currency)
                    .setUpdSessionId(MsgUtil.getSessionID(session))
                    .setGameId(gameNote.getGameId())
                    .setCurrencyId(gameNote.getCurrencyId());

            ScriptLoader.getInstance().consumerScript("AgentGameScript",
                    (IAgentGameScript script) -> script.updateCurrency(notifyData));

            ScriptLoader.getInstance().consumerScript("AgentGameScript",
                    (IAgentGameScript script) -> script.updateGameNoteStatus(player, round, 3));
        } catch (Exception e) {
            LOGGER.error("ReqCancelHandler", e);
            responseMap.put("status", "error");
            responseMap.put("reason", "unknown");
            MsgUtil.responseHttp(responseMap, session);
        }
    }

}
