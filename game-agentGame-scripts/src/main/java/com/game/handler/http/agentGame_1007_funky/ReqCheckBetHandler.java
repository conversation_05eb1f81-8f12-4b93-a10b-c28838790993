package com.game.handler.http.agentGame_1007_funky;

import com.alibaba.fastjson.JSONObject;
import com.game.engine.io.conf.ConstantConfig;
import com.game.engine.io.handler.HttpHandler;
import com.game.engine.math.BigDecimalUtils;
import com.game.engine.script.IHandlerEntity;
import com.game.engine.script.ScriptLoader;
import com.game.engine.utils.MsgUtil;
import com.game.entity.game.GameNote;
import com.game.entity.player.Player;
import com.game.enums.AgentGame;
import com.game.gamesr.manager.AgentGameMrg;
import com.game.gamesr.scripts.IAgentGameScript;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.LinkedHashMap;
import java.util.Map;
import java.util.TimeZone;

@IHandlerEntity(path = FunkyConstants.Paths.CHECK_BET, desc = "Funky查询下注状态接口")
public class ReqCheckBetHandler extends HttpHandler {
    private static final Logger LOGGER = LoggerFactory.getLogger(ReqCheckBetHandler.class);

    // 日期格式化器
    private static final SimpleDateFormat DATE_FORMAT = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss'Z'");

    static {
        DATE_FORMAT.setTimeZone(TimeZone.getTimeZone("UTC"));
    }

    /**
     * 查询下注请求数据
     */
    private static class CheckBetRequest {
        Player player;
        String id;

        CheckBetRequest(Player player, String id) {
            this.player = player;
            this.id = id;
        }
    }

    @Override
    public void run() {
        try {
            // 获取请求参数
            final Map<String, Object> paramsMap = getParamsMap();
            if (ConstantConfig.getInstance().isAgentDebug()) {
                LOGGER.info("ReqCheckBetHandler params: {}", paramsMap);
            }

            // 1. 校验请求
            CheckBetRequest request = validateRequest(paramsMap);
            if (request == null) {
                return; // 校验失败，错误响应已发送
            }

            // 2. 处理业务逻辑
            processCheckBet(request);

        } catch (Exception e) {
            LOGGER.error("ReqCheckBetHandler error", e);
            FunkyUtils.sendErrorResponse(session, FunkyConstants.ErrorCode.INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * 校验请求参数和权限
     *
     * @param paramsMap 请求参数
     * @return 校验通过返回CheckBetRequest对象，失败返回null
     */
    private CheckBetRequest validateRequest(Map<String, Object> paramsMap) {
        // 1. 验证请求头信息
        if (FunkyHeaderValidator.hasInvalidHeaders(paramsMap)) {
            FunkyUtils.sendErrorResponse(session, FunkyConstants.ErrorCode.PLAYER_NOT_LOGIN);
            return null;
        }

        // 2. 获取请求体参数
        final String id = (String) paramsMap.get("id");  // 对应PlaceBet的refNo
        final String playerId = (String) paramsMap.get("playerId");

        // 3. 验证必要参数
        if (id == null || id.trim().isEmpty()) {
            LOGGER.warn("Missing required id parameter");
            FunkyUtils.sendErrorResponse(session, FunkyConstants.ErrorCode.INVALID_INPUT);
            return null;
        }

        if (playerId == null || playerId.trim().isEmpty()) {
            LOGGER.warn("Missing required playerId parameter");
            FunkyUtils.sendErrorResponse(session, FunkyConstants.ErrorCode.INVALID_INPUT);
            return null;
        }

        if (!FunkyUtils.isValidPlayerId(playerId)) {
            LOGGER.warn("Invalid playerId format: {} (must be at least 3 characters)", playerId);
            FunkyUtils.sendErrorResponse(session, FunkyConstants.ErrorCode.INVALID_INPUT);
            return null;
        }

        // 4. 查找玩家
        long playerIdLong;
        try {
            playerIdLong = Long.parseLong(playerId.trim());
        } catch (NumberFormatException e) {
            LOGGER.warn("Invalid playerId format: {}", playerId);
            FunkyUtils.sendErrorResponse(session, FunkyConstants.ErrorCode.INVALID_INPUT);
            return null;
        }

        final Player player = AgentGameMrg.getInstance().findDbPlayer(playerIdLong);
        if (player == null) {
            FunkyUtils.sendErrorResponse(session, FunkyConstants.ErrorCode.PLAYER_NOT_LOGIN);
            return null;
        }

        // 5. IP黑名单检查
        final boolean ipBlackList = ScriptLoader.getInstance().functionScript("AgentGameScript",
                (IAgentGameScript script) -> script.isIpBlacklist(MsgUtil.getClientIp(session), AgentGame.FUNKY.getType()));
        if (!ipBlackList) {
            FunkyUtils.sendErrorResponse(session, FunkyConstants.ErrorCode.PLAYER_NOT_LOGIN);
            return null;
        }

        return new CheckBetRequest(player, id);
    }

    /**
     * 处理查询下注状态业务逻辑
     *
     * @param request 已验证的查询请求
     */
    private void processCheckBet(CheckBetRequest request) {
        // 查找下注记录
        GameNote gameNote = AgentGameMrg.getInstance().findRoundIdGameNote(request.player, request.id);
        if (gameNote == null) {
            LOGGER.warn("Bet not found: id={}, playerId={}", request.id, request.player.getPlayerId());
            FunkyUtils.sendErrorResponse(session, FunkyConstants.ErrorCode.BET_WAS_NOT_FOUND);
            return;
        }

        final JSONObject data = new JSONObject();

        // 获取下注金额（转换为USD）
        final double stake = ScriptLoader.getInstance().functionScript("AgentGameScript",
                (IAgentGameScript script) -> script.amountTransformUsd(request.player, gameNote.getBetAmount()));

        // 获取赢取金额（转换为USD）
        final double winAmount = ScriptLoader.getInstance().functionScript("AgentGameScript",
                (IAgentGameScript script) -> script.amountTransformUsd(request.player, gameNote.getWin()));

        data.put("stake", BigDecimalUtils.round(stake, 2));
        data.put("winAmount", BigDecimalUtils.round(winAmount, 2));

        // 确定下注状态
        String status = determineBetStatus(gameNote, stake, winAmount);
        data.put("status", status);

        // 设置结算日期
        String statementDate = getStatementDate(gameNote, status);
        data.put("statementDate", statementDate);

        // 发送成功响应
        FunkyUtils.sendSuccessResponse(session, data);
    }

    /**
     * 确定下注状态
     * R: 下注仍在进行中
     * W: 玩家赢了
     * L: 玩家输了
     * C: 下注被取消
     * D: 平局，玩家输赢为0
     */
    private String determineBetStatus(GameNote gameNote, double stake, double winAmount) {
        // 检查是否被取消
        if (gameNote.getStatus() == 3) { // 假设3表示取消状态
            return "C";
        }
        
        // 检查是否已结算
        if (gameNote.getStatus() == 2) { // 假设2表示已结算
            if (winAmount > stake) {
                return "W"; // 玩家赢了
            } else if (winAmount < stake) {
                return "L"; // 玩家输了
            } else {
                return "D"; // 平局
            }
        }
        
        // 默认为进行中
        return "R";
    }

    /**
     * 获取结算日期
     * 对于R和C状态，返回空字符串
     * 对于其他状态，返回结算时间
     */
    private String getStatementDate(GameNote gameNote, String status) {
        if ("R".equals(status) || "C".equals(status)) {
            return "";
        }
        
        // 返回结算时间，如果没有则返回创建时间
        long timestamp = gameNote.getCreateTime();
        return DATE_FORMAT.format(new Date(timestamp));
    }


}
