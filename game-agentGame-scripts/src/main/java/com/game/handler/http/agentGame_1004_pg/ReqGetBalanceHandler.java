package com.game.handler.http.agentGame_1004_pg;

import com.alibaba.fastjson.JSONObject;
import com.game.c_entity.middleplatform.C_BaseGamePlatform;
import com.game.engine.io.conf.ConstantConfig;
import com.game.engine.io.handler.HttpHandler;
import com.game.engine.math.BigDecimalUtils;
import com.game.engine.script.IHandlerEntity;
import com.game.engine.script.ScriptLoader;
import com.game.engine.utils.JsonUtils;
import com.game.engine.utils.MsgUtil;
import com.game.engine.utils.TimeUtil;
import com.game.entity.player.Player;
import com.game.enums.AgentGame;
import com.game.gamesr.manager.AgentGameMrg;
import com.game.gamesr.manager.DataAgentGameMrg;
import com.game.gamesr.scripts.IAgentGameScript;
import com.game.gamesr.utils.JWTUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.LinkedHashMap;
import java.util.Map;
import java.util.Objects;

//http://127.0.0.1:8680/pg/Cash/Get
@IHandlerEntity(path = "/pg/Cash/Get", desc = "获取余额")
public class ReqGetBalanceHandler extends HttpHandler {
    private static final Logger LOGGER = LoggerFactory.getLogger(ReqGetBalanceHandler.class);

    @Override
    public void run() {
        final Map<String, Object> responseMap = new LinkedHashMap<>();
        try {
            if (ConstantConfig.getInstance().isAgentDebug()) {
                LOGGER.info("ReqGetBalanceHandler，params：{}", JsonUtils.writeAsJson(paramsMap));
            }

            final String operator_token = (String) paramsMap.get("operator_token");
            final String secret_key = (String) paramsMap.get("secret_key");
            final String operator_player_session = (String) paramsMap.get("operator_player_session");

            final String player_name = (String) paramsMap.get("player_name");

            final Map<String, Object> jwtMap = JWTUtils.verify(operator_player_session);
            if (jwtMap.isEmpty() || !Objects.isNull(jwtMap.get("error"))) {
                final JSONObject error = new JSONObject();
                error.put("code", "1034");
                error.put("message", "Invalid request");

                responseMap.put("data", null);
                responseMap.put("error", error);
                MsgUtil.responseHttp(responseMap, session);
                return;
            }

            final String currency = (String) jwtMap.get("currency");
            final String playerName = (String) jwtMap.get("playerName");
            if (!Objects.equals(playerName, player_name)) {
                final JSONObject error = new JSONObject();
                error.put("code", "1034");
                error.put("message", "Invalid request");

                responseMap.put("data", null);
                responseMap.put("error", error);
                MsgUtil.responseHttp(responseMap, session);
                return;
            }

            final long playerId = AgentGameMrg.getInstance().getAgentGamePlayerId(playerName);
            final Player player = AgentGameMrg.getInstance().findDbPlayer(playerId);
            if (player == null) {
                final JSONObject error = new JSONObject();
                error.put("code", "3004");
                error.put("message", "Player does not exist");

                responseMap.put("data", null);
                responseMap.put("error", error);
                MsgUtil.responseHttp(responseMap, session);
                return;
            }

            final C_BaseGamePlatform c_baseGamePlatform = DataAgentGameMrg.getInstance()
                    .findC_BaseGamePlatformSupplierId(this.getClass().getSimpleName(), AgentGame.PG.getType());
            if (c_baseGamePlatform == null) {
                final JSONObject error = new JSONObject();
                error.put("code", "1034");
                error.put("message", "Invalid request");

                responseMap.put("data", null);
                responseMap.put("error", error);
                MsgUtil.responseHttp(responseMap, session);
                return;
            }

            if (!Objects.equals(c_baseGamePlatform.getToken(), operator_token)) {
                final JSONObject error = new JSONObject();
                error.put("code", "1034");
                error.put("message", "Invalid request");

                responseMap.put("data", null);
                responseMap.put("error", error);
                MsgUtil.responseHttp(responseMap, session);
                return;
            }

            if (!Objects.equals(c_baseGamePlatform.getSecretKey(), secret_key)) {
                final JSONObject error = new JSONObject();
                error.put("code", "1034");
                error.put("message", "Invalid request");

                responseMap.put("data", null);
                responseMap.put("error", error);
                MsgUtil.responseHttp(responseMap, session);
                return;
            }

            final double realBalance = ScriptLoader.getInstance().functionScript("AgentGameScript",
                    (IAgentGameScript script) -> script.realBalance(player));

            final double balance = ScriptLoader.getInstance().functionScript("AgentGameScript",
                    (IAgentGameScript script) -> script.amountTransformUsd(player, realBalance));

            final JSONObject data = new JSONObject();
            data.put("currency_code", currency);
            data.put("balance_amount", BigDecimalUtils.round(balance, 2));
            data.put("updated_time", TimeUtil.currentTimeMillis());

            responseMap.put("data", data);
            responseMap.put("error", null);
            MsgUtil.responseHttp(responseMap, session);
        } catch (Exception e) {
            LOGGER.error("ReqGetBalanceHandler", e);
            final JSONObject error = new JSONObject();
            error.put("code", "1034");
            error.put("message", "Invalid request");

            responseMap.put("data", null);
            responseMap.put("error", error);
            MsgUtil.responseHttp(responseMap, session);
        }
    }
}




