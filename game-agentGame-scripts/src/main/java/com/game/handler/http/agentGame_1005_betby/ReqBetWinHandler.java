package com.game.handler.http.agentGame_1005_betby;

import com.alibaba.fastjson.JSON;
import com.game.engine.io.conf.ConstantConfig;
import com.game.engine.io.handler.HttpHandler;
import com.game.engine.script.IHandlerEntity;
import com.game.engine.script.ScriptLoader;
import com.game.engine.utils.JsonUtils;
import com.game.engine.utils.MsgUtil;
import com.game.entity.game.GameNote;
import com.game.entity.player.Player;
import com.game.enums.ChangeType;
import com.game.enums.Currency;
import com.game.gamesr.NotifyData;
import com.game.gamesr.manager.AgentGameMrg;
import com.game.gamesr.scripts.IAgentGameScript;
import com.game.handler.http.agentGame_1005_betby.BetbyConstants.ValidationResult;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.*;

@IHandlerEntity(path = "/betby/bet/win", desc = "投注结算")
public class ReqBetWinHandler extends HttpHandler {

    private static final Logger LOGGER = LoggerFactory.getLogger(ReqBetWinHandler.class);

    // 请求参数类
    private static class BetWinRequest {
        // 要转移到玩家余额的金额，单位为分
        private int amount;
        // 货币代码
        private String currency;
        // 是否取款
        private boolean isCashout;
        // 运营商分配的唯一交易ID
        private String betTransactionId;
        // 交易信息
        private TransactionInfo transaction;
        // 无风险免费投注损失标记
        private boolean isSnrLost;
        // 所有包含的选择的状态（例如，赢、输、进行中等）
        private List<SelectionItem> selections;
        // 最终计算的投注赔率（不考虑组合提升）
        private String odds;
        // Betby 奖金 ID（可选）
        private String bonusId;
        // 奖金类型（例如，freebet_refund、freebet_freemoney等）
        private String bonusType;
        // 组合提升倍数（可选）
        private String comboboostMultiplier;

        // Getter and Setter
    }

    // 交易信息类
    private static class TransactionInfo {
        // 由 Betby 分配的交易唯一编码
        private String id;
        // 由 Betby 在投注时分配的投注单唯一编码
        private String betslipId;
        // 由 Betby 分配的玩家唯一编码
        private String playerId;
        // 由 Betby 分配的运营商唯一编码
        private String operatorId;
        // 由 Betby 分配的运营商网站唯一编码
        private String operatorBrandId;
        // 运营商端分配给玩家的唯一编码
        private String extPlayerId;
        // 交易的 Unix 时间戳
        private double timestamp;
        // 退还的金额，以分为单位表示
        private int amount;
        // 货币代码
        private String currency;
        // 操作类型（投注、赢、退款等）
        private String operation;
        // Betby 奖金 ID（可选）
        private String bonusId;
        // 玩家货币与欧元的汇率
        private String crossRateEuro;
        // 由 Betby 分配的上一交易的ID
        private String parentTransactionId;

        // Getter and Setter
    }

    // 选择项类
    private static class SelectionItem {
        // Betby 在处理投注请求时分配的投注唯一编码
        private String id;
        // 事件的唯一编码
        private String eventId;
        // 选择的状态：开放、已赢、已输等
        private String status;
        // 当前选择的赔率（可选）
        private String odds;

        // Getter and Setter
    }

    @Override
    public void run() {
        final Map<String, Object> responseMap = new LinkedHashMap<>();
        try {
            if (ConstantConfig.getInstance().isAgentDebug()) {
                LOGGER.info("betby BET_WIN request: {}", JsonUtils.writeAsJson(paramsMap));
            }

//            final boolean ipBlackList = ScriptLoader.getInstance().functionScript("AgentGameScript",
//                    (IAgentGameScript script) -> script.isIpBlacklist(MsgUtil.getClientIp(session), AgentGame.BETBY.getType()));
//            if (!ipBlackList) {
//                // 其他错误
//                responseMap.put("code", 3000);
//                MsgUtil.responseHttp(responseMap, session);
//                return;
//            }

            // 1. 解析请求参数
            final BetWinRequest request = parseRequest((String) paramsMap.get("payload"));
            // 2. 验证请求
            ValidationResult validationResult = validateRequest(request);
            if (!validationResult.isSuccess()) {
                BetbyConstants.sendErrorResponse(session, validationResult.getErrorCode(), validationResult.getErrorMessage());
                return;
            }

            Currency currency = BetbyConstants.mapToCurrency(request.currency);
            // 检查货币是否支持
            if (currency == null) {
                LOGGER.warn("Unsupported currency: {}", request.currency);
                BetbyConstants.sendErrorResponse(session, 2002, "Unsupported currency: " + request.currency);
                return;
            }

            // 3. 处理投注结算逻辑
            processWin(request, validationResult.getPlayer());
        } catch (Exception e) {
            LOGGER.error("ReqBetWinHandle error", e);
            BetbyConstants.sendErrorResponse(session, 2004, "Bad request");
        }
    }

    // 解析请求
    private BetWinRequest parseRequest(String payLoad) {
        try {
            Map<String, Object> requestMap = JwtVerifier.verifyAndDecodeJWT(payLoad);
            LOGGER.info("betby BET_WIN payload: {}", requestMap);

            @SuppressWarnings("unchecked")
            Map<String, Object> paramsMap = (Map<String, Object>) requestMap.get("payload");

            BetWinRequest request = new BetWinRequest();
            request.amount = (Integer) paramsMap.get("amount");
            request.currency = (String) paramsMap.get("currency");
            request.isCashout = (Boolean) paramsMap.get("is_cashout");
            request.betTransactionId = (String) paramsMap.get("bet_transaction_id");

            // Parse transaction info
            @SuppressWarnings("unchecked")
            Map<String, Object> transactionMap = (Map<String, Object>) paramsMap.get("transaction");
            request.transaction = parseTransactionInfo(transactionMap);

            request.isSnrLost = (Boolean) paramsMap.get("is_snr_lost");

            // Parse selections
            @SuppressWarnings("unchecked")
            List<Map<String, Object>> selectionsList = (List<Map<String, Object>>) paramsMap.get("selections");
            request.selections = parseSelections(selectionsList);

            request.odds = (String) paramsMap.get("odds");
            request.bonusId = (String) paramsMap.get("bonus_id");
            request.bonusType = (String) paramsMap.get("bonus_type");
            request.comboboostMultiplier = (String) paramsMap.get("comboboost_multiplier");

            return request;
        } catch (Exception e) {
            LOGGER.error("Error parsing request: {}", e.getMessage());
            throw e;
        }
    }

    // 解析 Transaction 信息
    private TransactionInfo parseTransactionInfo(Map<String, Object> transactionMap) {
        TransactionInfo transaction = new TransactionInfo();
        transaction.id = (String) transactionMap.get("id");
        transaction.betslipId = (String) transactionMap.get("betslip_id");
        transaction.playerId = (String) transactionMap.get("player_id");
        transaction.operatorId = (String) transactionMap.get("operator_id");
        transaction.operatorBrandId = (String) transactionMap.get("operator_brand_id");
        transaction.extPlayerId = (String) transactionMap.get("ext_player_id");
        transaction.timestamp = (Double) transactionMap.get("timestamp");
        transaction.amount = (Integer) transactionMap.get("amount");
        transaction.currency = (String) transactionMap.get("currency");
        transaction.operation = (String) transactionMap.get("operation");
        transaction.bonusId = (String) transactionMap.get("bonus_id");
        transaction.crossRateEuro = (String) transactionMap.get("cross_rate_euro");
        transaction.parentTransactionId = (String) transactionMap.get("parent_transaction_id");

        return transaction;
    }

    // 解析 Selections 信息
    private List<SelectionItem> parseSelections(List<Map<String, Object>> selectionsList) {
        List<SelectionItem> selections = new ArrayList<>();
        for (Map<String, Object> selectionMap : selectionsList) {
            SelectionItem selection = new SelectionItem();
            selection.id = (String) selectionMap.get("id");
            selection.eventId = (String) selectionMap.get("event_id");
            selection.status = (String) selectionMap.get("status");
            selection.odds = (String) selectionMap.get("odds");
            selections.add(selection);
        }
        return selections;
    }

    // 验证请求
    private ValidationResult validateRequest(BetWinRequest request) {
        try {
            // 验证bet_transaction_id和其他参数
            if (request.betTransactionId == null || request.transaction == null) {
                LOGGER.info("Invalid request parameters");
                return ValidationResult.failure(2004, "Invalid request parameters");
            }

            // 验证玩家是否存在
            final String playerId = BetbyConstants.getPlayerId(request.transaction.extPlayerId);
            final Player player = AgentGameMrg.getInstance().findDbPlayer(Long.parseLong(playerId));
            if (player == null) {
                LOGGER.info("Player not found: {}", playerId);
                return ValidationResult.failure(1006, "Player not found");
            }

            // 返回验证结果
            return ValidationResult.success(player);
        } catch (Exception e) {
            LOGGER.error("Validation error", e);
            return ValidationResult.failure(2004, "Validation error");
        }
    }

    // 处理投注结算
    private void processWin(BetWinRequest request, Player player) {
        try {
            // 查找相应的游戏记录
            GameNote gameNote = AgentGameMrg.getInstance().findRoundIdGameNote(player, request.betTransactionId);
            if (gameNote == null) {
                LOGGER.warn("bet transaction id not found: {}", request.betTransactionId);
                BetbyConstants.sendErrorResponse(session, 2004, "bet transaction id not found");
                return;
            }

            // 获取当前余额，用于构建响应
            final double realBalance = ScriptLoader.getInstance().functionScript("AgentGameScript",
                    (IAgentGameScript script) -> script.realBalance(player));
            final double finalBalance = ScriptLoader.getInstance().functionScript("AgentGameScript",
                    (IAgentGameScript script) -> script.amountTransformUsd(player, realBalance));

            // 检查是否是重复请求
            if (gameNote.getTransactionIds().contains(request.transaction.id)) {
                LOGGER.info("Duplicate transaction detected: {}. Returning idempotent response.", request.transaction.id);

                // 直接构建响应并返回，参考handleWinUpdateCurrency方法的格式
                final Map<String, Object> responseMap = new LinkedHashMap<>();
                int balanceInCents = (int) Math.round(finalBalance * 100);
                int amountInCents = request.amount; // 使用请求中的金额

                responseMap.put("id", request.transaction.id);
                responseMap.put("ext_transaction_id", request.transaction.id);
                responseMap.put("parent_transaction_id", request.transaction.parentTransactionId);
                responseMap.put("user_id", request.transaction.extPlayerId);
                responseMap.put("operation", "win");
                responseMap.put("amount", amountInCents);
                responseMap.put("currency", request.currency);
                responseMap.put("balance", balanceInCents);

                LOGGER.info("Duplicate win transaction response: {}", responseMap);
                MsgUtil.responseHttp(responseMap, session);
                return;
            }


            double winAmount = request.amount / 100.0;  // 将分转为元
            final double winValue = ScriptLoader.getInstance().functionScript("AgentGameScript",
                    (IAgentGameScript script) -> script.usdTransformAmount(player, winAmount));

            // 更新游戏记录，添加当前交易ID
            GameNote gameNote2 = ScriptLoader.getInstance().functionScript("AgentGameScript",
                    (IAgentGameScript script) -> script.updateGameNote(player, gameNote.getRoundId(), request.transaction.id, 2, winValue, realBalance));

            // 构建通知数据并执行货币更新
            Map<String, Object> dataMap = new HashMap<>();
            dataMap.put("extPlayerId", request.transaction.extPlayerId);
            dataMap.put("parentTransactionId", request.transaction.parentTransactionId);
            dataMap.put("operation", request.transaction.operation);

            String dataJson = JSON.toJSONString(dataMap);

            final NotifyData notifyData = new NotifyData();
            notifyData.setType(ChangeType.win.getType())
                    .setPid(player.getPlayerId())
                    .setPlayerName(player.getPlayerName())
                    .setNoteId(request.transaction.id)
                    .setCurrency(request.currency)
                    .setBetAmount(gameNote2.getBetAmount())
                    .setValidBets(gameNote2.getValidBets())
                    .setWin(winValue)
                    .setTotalWin(winValue)
                    .setData(dataJson)
                    .setUpdSessionId(MsgUtil.getSessionID(session));

            // 打印notifyData的所有参数
            LOGGER.info("NotifyData parameters - Type: {}, Pid: {}, PlayerName: {}, NoteId: {}, Currency: {}, BetAmount: {}, ValidBets: {}, Win: {}, TotalWin: {}, Data: {}, UpdSessionId: {}",
                notifyData.getType(),
                notifyData.getPid(),
                notifyData.getPlayerName(),
                notifyData.getNoteId(),
                notifyData.getCurrency(),
                notifyData.getBetAmount(),
                notifyData.getValidBets(),
                notifyData.getWin(),
                notifyData.getTotalWin(),
                notifyData.getData(),
                notifyData.getUpdSessionId());

            // 执行货币更新并发送响应
            ScriptLoader.getInstance().consumerScript("AgentGameScript",
                    (IAgentGameScript script) -> script.updateCurrency(notifyData));
        } catch (Exception e) {
            LOGGER.error("Win processing error", e);
            BetbyConstants.sendErrorResponse(session, 2004, "Win processing failed");
        }
    }
}
