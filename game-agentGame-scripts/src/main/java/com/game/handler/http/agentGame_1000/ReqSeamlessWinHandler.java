//package com.game.handler.http.agentGame_1000;
//
//import com.game.engine.io.handler.HttpHandler;
//import com.game.engine.io.redis.RedisPoolManager;
//import com.game.engine.math.BigDecimalUtils;
//import com.game.engine.script.IHandlerEntity;
//import com.game.engine.script.ScriptLoader;
//import com.game.engine.utils.JsonUtils;
//import com.game.engine.utils.MsgUtil;
//import com.game.engine.utils.TimeUtil;
//import com.game.entity.game.GameNote;
//import com.game.entity.player.Player;
//import com.game.enums.ChangeType;
//import com.game.enums.redis.RedisHall;
//import com.game.gamesr.NotifyData;
//import com.game.gamesr.manager.AgentGameMrg;
//import com.game.gamesr.scripts.IAgentGameScript;
//import com.game.redis.RedisUtils;
//import io.netty.util.internal.StringUtil;
//import org.slf4j.Logger;
//import org.slf4j.LoggerFactory;
//
//import java.util.LinkedHashMap;
//import java.util.Map;
//
////http://127.0.0.1:8680/seamless/win
//@IHandlerEntity(path = "/seamless/win", desc = "派彩")
//public class ReqSeamlessWinHandler extends HttpHandler {
//    private static final Logger LOGGER = LoggerFactory.getLogger(ReqSeamlessWinHandler.class);
//
//    @Override
//    public void run() {
//        try {
//            LOGGER.info("ReqSeamlessWinHandler，params：{}", JsonUtils.writeAsJson(paramsMap));
//            final String playerStr = (String) paramsMap.get("player");//玩家识别码
//            final String amount = (String) paramsMap.get("amount");//派彩金额
//            final String roundId = (String) paramsMap.get("round");//注单id
//
//            final Map<String, Object> responseMap = new LinkedHashMap<>();
//            if (StringUtil.isNullOrEmpty(playerStr) || StringUtil.isNullOrEmpty(amount)
//                    || StringUtil.isNullOrEmpty(roundId)) {
//                responseMap.put("message", "INVALID_ARGUMENT");
//                responseMap.put("timestamp", TimeUtil.currentTimeMillis());
//                MsgUtil.responseHttp(responseMap, session);
//                return;
//            }
//
//            final long playerId = AgentGameMrg.getInstance().getAgentGamePlayerId(playerStr);
//            final Player player = AgentGameMrg.getInstance().findDbPlayer(playerId);
//            if (player == null) {
//                responseMap.put("message", "INVALID_ARGUMENT");
//                responseMap.put("timestamp", TimeUtil.currentTimeMillis());
//                MsgUtil.responseHttp(responseMap, session);
//                return;
//            }
//
//            final boolean isIpBlacklist = ScriptLoader.getInstance().functionScript("AgentGameScript", (IAgentGameScript script) -> script.isIpBlacklist(player, MsgUtil.getClientIp(session)));
//            if (!isIpBlacklist) {
//                return;
//            }
//
//            final int currencyId = player.getCurrencyId();
//            final String balanceStr = RedisPoolManager.getInstance().function(jedis -> jedis.hget(RedisHall.Platform_Role_Map_Currency.getKey(playerId), String.valueOf(currencyId)));
//            final double balance = RedisUtils.getInstance().convert(balanceStr);
//
//            responseMap.put("balance", BigDecimalUtils.add(balance, Double.parseDouble(amount), 4));
//            responseMap.put("timestamp", TimeUtil.currentTimeMillis());
//            MsgUtil.responseHttp(responseMap, session);
//
//            final GameNote gameNote = AgentGameMrg.getInstance().findRoundIdGameNote(player.getPlayerId(), player.getPlatformId(), roundId);
//            if (gameNote == null) {
//                LOGGER.warn("playerId：{}，betId：{}，not exist", playerId, roundId);
//                return;
//            }
//
//            final NotifyData notifyData = new NotifyData();
//            notifyData.setType(ChangeType.win.getChange())
//                    .setPid(playerId)
//                    .setCurrencyId(player.getCurrencyId())
//                    .setNoteId(gameNote.getNoteId() + "")
//                    .setValidBets(gameNote.getValidBets())
//                    .setWin(Double.parseDouble(amount))
//                    .setTotalWin(gameNote.getWin() + Double.parseDouble(amount))
//                    .setUdpSessionId(MsgUtil.getSessionID(session));
//            ScriptLoader.getInstance().consumerScript("AgentGameScript", (IAgentGameScript script) -> script.updateCurrency(notifyData));
//
//            ScriptLoader.getInstance().consumerScript("AgentGameScript", (IAgentGameScript script) -> script.updateGameNote(player, roundId, gameNote.getValidBets(), Double.parseDouble(amount), 2));
//        } catch (Exception e) {
//            LOGGER.error("ReqSeamlessWinHandler", e);
//        }
//    }
//
//}
