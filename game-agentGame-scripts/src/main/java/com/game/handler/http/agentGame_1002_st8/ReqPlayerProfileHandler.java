//package com.game.handler.http.agentGame_1002;
//
//import com.game.engine.io.handler.HttpHandler;
//import com.game.engine.script.IHandlerEntity;
//import com.game.engine.script.ScriptLoader;
//import com.game.engine.utils.JsonUtils;
//import com.game.engine.utils.MsgUtil;
//import com.game.entity.player.Player;
//import com.game.gamesr.manager.AgentGameMrg;
//import com.game.gamesr.scripts.IAgentGameScript;
//import org.slf4j.Logger;
//import org.slf4j.LoggerFactory;
//
//import java.util.LinkedHashMap;
//import java.util.Map;
//
////http://127.0.0.1:8680/st8/player_profile
//@IHandlerEntity(path = "/st8/player_profile", desc = "玩家资料")
//public class ReqPlayerProfileHandler extends HttpHandler {
//    private static final Logger LOGGER = LoggerFactory.getLogger(ReqPlayerProfileHandler.class);
//
//    @Override
//    public void run() {
//        final Map<String, Object> responseMap = new LinkedHashMap<>();
//        try {
//            LOGGER.info("ReqPlayerProfileHandler，params：{}", JsonUtils.writeAsJson(paramsMap));
//            final String playerStr = (String) paramsMap.get("player");
//
//            final long playerId = AgentGameMrg.getInstance().getAgentGamePlayerId(playerStr);
//            final Player player = AgentGameMrg.getInstance().findDbPlayer(playerId);
//            if (player == null) {
//                responseMap.put("status", "player_not_found");
//                MsgUtil.responseHttp(responseMap, session);
//                return;
//            }
//
//            final boolean isIpBlacklist = ScriptLoader.getInstance().functionScript("AgentGameScript", (IAgentGameScript script) -> script.isIpBlacklist(player, MsgUtil.getClientIp(session)));
//            if (!isIpBlacklist) {
//                responseMap.put("status", "unknown");
//                MsgUtil.responseHttp(responseMap, session);
//                return;
//            }
//
//            responseMap.put("status", "ok");
//            responseMap.put("id", playerStr);
//            responseMap.put("jurisdiction", "BR");
//            responseMap.put("default_currency", "BRL");
//            responseMap.put("reg_country", "BR");
//            MsgUtil.responseHttp(responseMap, session);
//        } catch (Exception e) {
//            LOGGER.error("ReqPlayerProfileHandler", e);
//            responseMap.put("status", "unknown");
//            MsgUtil.responseHttp(responseMap, session);
//        }
//    }
//
//}
