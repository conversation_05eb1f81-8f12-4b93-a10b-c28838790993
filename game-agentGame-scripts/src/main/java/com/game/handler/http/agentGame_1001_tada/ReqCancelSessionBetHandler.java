package com.game.handler.http.agentGame_1001_tada;

import com.game.engine.io.handler.HttpHandler;
import com.game.engine.math.BigDecimalUtils;
import com.game.engine.script.IHandlerEntity;
import com.game.engine.script.ScriptLoader;
import com.game.engine.utils.JsonUtils;
import com.game.engine.utils.MsgUtil;
import com.game.entity.game.GameNote;
import com.game.entity.player.Player;
import com.game.enums.AgentGame;
import com.game.enums.ChangeType;
import com.game.gamesr.NotifyData;
import com.game.gamesr.manager.AgentGameMrg;
import com.game.gamesr.scripts.IAgentGameScript;
import com.game.gamesr.utils.JWTUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.LinkedHashMap;
import java.util.Map;
import java.util.Objects;

//http://127.0.0.1:8680/cancelBet
@IHandlerEntity(path = "/tada/cancelSessionBet", desc = "取消下注")
public class ReqCancelSessionBetHandler extends HttpHandler {
    private static final Logger LOGGER = LoggerFactory.getLogger(ReqCancelSessionBetHandler.class);

    @Override
    public void run() {
        final Map<String, Object> responseMap = new LinkedHashMap<>();
        try {
            LOGGER.info("ReqCancelSessionBetHandler，params：{}", JsonUtils.writeAsJson(paramsMap));

            final String reqId = (String) paramsMap.get("reqId");//玩家识别码
            final String token = (String) paramsMap.get("token");
            final long sessionId = (long) paramsMap.get("sessionId");//注单id
            final String currency = (String) paramsMap.get("currency");//货币
            final String betAmount = paramsMap.get("betAmount") + "";//押注金额
            final String winloseAmount = paramsMap.get("winloseAmount") + "";//派彩

            final String preserve = paramsMap.get("preserve") + "";//预扣

            final Map<String, Object> jwtMap = JWTUtils.verify(token);

            if (jwtMap.isEmpty() || !Objects.isNull(jwtMap.get("error"))) {
                responseMap.put("errorCode", 4);
                responseMap.put("message", "Token expired");
                responseMap.put("balance", 0);
                MsgUtil.responseHttp(responseMap, session);
                return;
            }

            final long playerId = (long) jwtMap.get("userId");
            final String playerName = (String) jwtMap.get("playerName");

            final Player player = AgentGameMrg.getInstance().findDbPlayer(playerId);
            if (player == null) {
                responseMap.put("errorCode", 5);
                responseMap.put("message", "Invalid Player");
                responseMap.put("username", playerName);
                responseMap.put("currency", currency);
                responseMap.put("balance", 0);
                MsgUtil.responseHttp(responseMap, session);
                return;
            }

            final boolean ipBlackList = ScriptLoader.getInstance().functionScript("AgentGameScript",
                    (IAgentGameScript script) -> script.isIpBlacklist(MsgUtil.getClientIp(session), AgentGame.TADA.getType()));
            if (!ipBlackList) {
                responseMap.put("errorCode", 5);
                responseMap.put("message", "Invalid Player");
                responseMap.put("username", playerName);
                responseMap.put("currency", currency);
                responseMap.put("balance", 0);
                MsgUtil.responseHttp(responseMap, session);
                return;
            }

            final double realBalance = ScriptLoader.getInstance().functionScript("AgentGameScript",
                    (IAgentGameScript script) -> script.realBalance(player));
            final double balance = ScriptLoader.getInstance().functionScript("AgentGameScript",
                    (IAgentGameScript script) -> script.amountTransformUsd(player, realBalance));

//            double betValue = 0;
//            if (Double.parseDouble(preserve) == 0) {
//                betValue = Double.parseDouble(betAmount);
//            } else {
//                betValue = Double.parseDouble(preserve);
//            }

            final GameNote gameNote = AgentGameMrg.getInstance().findRoundIdGameNote(player, sessionId + "");
            if (gameNote == null) {
                responseMap.put("errorCode", 0);
                responseMap.put("message", "success");
                responseMap.put("username", playerName);
                responseMap.put("currency", currency);
                responseMap.put("balance", BigDecimalUtils.round(balance, 2));
                MsgUtil.responseHttp(responseMap, session);
                return;
            }

            if (gameNote.getStatus() == 3) {
                final double usdBalance = ScriptLoader.getInstance().functionScript("AgentGameScript",
                        (IAgentGameScript script) -> script.amountTransformUsd(player, gameNote.getBalance()));

                responseMap.put("errorCode", 0);
                responseMap.put("message", "success");
                responseMap.put("username", playerName);
                responseMap.put("currency", currency);
                responseMap.put("balance", BigDecimalUtils.add(usdBalance, gameNote.getBetAmount(), 2));
                MsgUtil.responseHttp(responseMap, session);
                return;
            }

//            if (Double.parseDouble(preserve) == 0) {
//                balance = BigDecimalUtils.add(balance, Double.parseDouble(betAmount), 2);
//            } else {
//                balance = BigDecimalUtils.add(balance, Double.parseDouble(preserve), 2);
//            }

//            responseMap.put("errorCode", 0);
//            responseMap.put("message", "success");
//            responseMap.put("username", playerName);
//            responseMap.put("currency", currency);
//            responseMap.put("balance", BigDecimalUtils.round(balance, 2));
//            MsgUtil.responseHttp(responseMap, session);

            final NotifyData notifyData = new NotifyData();
            notifyData.setType(ChangeType.refund.getType())
                    .setPid(playerId)
                    .setNoteId(sessionId + "")
                    .setBetAmount(gameNote.getBetAmount())
                    .setWin(gameNote.getWin())
                    .setPlayerName(playerName)
                    .setCurrency(currency)
                    .setUpdSessionId(MsgUtil.getSessionID(session));
            ScriptLoader.getInstance().consumerScript("AgentGameScript",
                    (IAgentGameScript script) -> script.updateCurrency(notifyData));

            ScriptLoader.getInstance().consumerScript("AgentGameScript",
                    (IAgentGameScript script) -> script.updateGameNoteStatus(player, sessionId + "", 3));
        } catch (Exception e) {
            LOGGER.error("ReqCancelSessionBetHandler", e);
            responseMap.put("errorCode", 5);
            responseMap.put("message", "server error");
            MsgUtil.responseHttp(responseMap, session);
        }
    }

}
