package com.game.handler.http.agentGame_1002_st8;

import com.game.c_entity.middleplatform.C_BaseGamePlatform;
import com.game.engine.io.conf.ConstantConfig;
import com.game.engine.io.handler.HttpHandler;
import com.game.engine.math.BigDecimalUtils;
import com.game.engine.script.IHandlerEntity;
import com.game.engine.script.ScriptLoader;
import com.game.engine.utils.JsonUtils;
import com.game.engine.utils.MsgUtil;
import com.game.entity.player.Player;
import com.game.enums.AgentGame;
import com.game.gamesr.manager.AgentGameMrg;
import com.game.gamesr.manager.DataAgentGameMrg;
import com.game.gamesr.scripts.IAgentGameScript;
import com.game.gamesr.utils.JWTUtils;
import com.game.gamesr.utils.SHAUtils;
import io.netty.util.internal.StringUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.LinkedHashMap;
import java.util.Map;
import java.util.Objects;

//http://127.0.0.1:8680/st8/balance
@IHandlerEntity(path = "/st8/balance", desc = "余额")
public class ReqBalanceHandler extends HttpHandler {
    private static final Logger LOGGER = LoggerFactory.getLogger(ReqBalanceHandler.class);

    @Override
    public void run() {
        final Map<String, Object> responseMap = new LinkedHashMap<>();
        try {
            if (ConstantConfig.getInstance().isAgentDebug()) {
                LOGGER.info("ReqBalanceHandler，params：{}", JsonUtils.writeAsJson(paramsMap));
            }

            final String players = (String) paramsMap.get("player");
            final String currency = (String) paramsMap.get("currency");
            final String token = (String) paramsMap.get("token");

            final String xst8sign = (String) paramsMap.remove("x-st8-sign");

            final String body = JsonUtils.writeAsJson(paramsMap);

            final long playerId = AgentGameMrg.getInstance().getAgentGamePlayerId(players);
            final Player player = AgentGameMrg.getInstance().findDbPlayer(playerId);
            if (player == null) {
                responseMap.put("status", "error");
                responseMap.put("reason", "player_not_found");
                MsgUtil.responseHttp(responseMap, session);
                return;
            }

            final C_BaseGamePlatform c_baseGamePlatform = DataAgentGameMrg.getInstance()
                    .findC_BaseGamePlatformSupplierId(this.getClass().getSimpleName(), AgentGame.ST8.getType());
            if (c_baseGamePlatform == null) {
                responseMap.put("status", "error");
                responseMap.put("reason", "unknown");
                MsgUtil.responseHttp(responseMap, session);
                return;
            }

            if (!SHAUtils.verify(body, xst8sign, c_baseGamePlatform.getToken())) {
                responseMap.put("status", "auth_failed");
                MsgUtil.responseHttp(responseMap, session);
                return;
            }

            final double realBalance = ScriptLoader.getInstance().functionScript("AgentGameScript",
                    (IAgentGameScript script) -> script.realBalance(player));

            final double balance = ScriptLoader.getInstance().functionScript("AgentGameScript",
                    (IAgentGameScript script) -> script.amountTransformUsd(player, realBalance));

            if (StringUtil.isNullOrEmpty(token)) {
                responseMap.put("status", "ok");
                responseMap.put("currency", currency);
                responseMap.put("balance", BigDecimalUtils.round(balance, 2) + "");
                MsgUtil.responseHttp(responseMap, session);
                return;
            }

            final Map<String, Object> jwtMap = JWTUtils.verify(token);
            if (jwtMap.isEmpty() || !Objects.isNull(jwtMap.get("error"))) {
                responseMap.put("status", "auth_failed");
                MsgUtil.responseHttp(responseMap, session);
                return;
            }

            responseMap.put("status", "ok");
            responseMap.put("currency", currency);
            responseMap.put("balance", BigDecimalUtils.round(balance, 2) + "");
            MsgUtil.responseHttp(responseMap, session);
        } catch (Exception e) {
            LOGGER.error("ReqBalanceHandler", e);
            responseMap.put("status", "error");
            responseMap.put("reason", "unknown");
            MsgUtil.responseHttp(responseMap, session);
        }
    }

}
