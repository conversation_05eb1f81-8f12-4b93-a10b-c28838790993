package com.game.handler.http.agentGame_1007_funky;

import com.game.c_entity.middleplatform.C_BaseGamePlatform;
import com.game.enums.AgentGame;
import com.game.gamesr.manager.DataAgentGameMrg;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Map;

/**
 * Funky Games API 头信息验证工具类
 * 用于验证1007协议中必需的HTTP头信息
 */
public class FunkyHeaderValidator {

    private static final Logger LOGGER = LoggerFactory.getLogger(FunkyHeaderValidator.class);

    /**
     * 检查头信息是否无效
     *
     * @param paramsMap 请求参数Map
     * @return true表示头信息无效，false表示头信息有效
     */
    public static boolean hasInvalidHeaders(Map<String, Object> paramsMap) {
        // 获取配置
        final C_BaseGamePlatform config = DataAgentGameMrg.getInstance()
                .findC_BaseGamePlatformSupplierId("FunkyHandler", AgentGame.FUNKY.getType());

        if (config == null) {
            LOGGER.error("Failed to get C_BaseGamePlatform config");
            return true;
        }

        // 验证User-Agent
        String userAgent = (String) paramsMap.get("userAgent");
        if (userAgent == null || !config.getAgent().equals(userAgent.trim())) {
            LOGGER.warn("Invalid User-Agent: expected={}, actual={}", config.getAgent(), userAgent);
            return true;
        }

        // 验证Authentication
        String auth = (String) paramsMap.get("authentication");
        if (auth == null || !config.getToken().equals(auth.trim())) {
            LOGGER.warn("Invalid Authentication token");
            return true;
        }

        // 验证X-Request-ID存在
        String xRequestId = (String) paramsMap.get("xRequestId");
        if (xRequestId == null || xRequestId.trim().isEmpty()) {
            LOGGER.warn("Missing X-Request-ID");
            return true;
        }

        return false;
    }
}
