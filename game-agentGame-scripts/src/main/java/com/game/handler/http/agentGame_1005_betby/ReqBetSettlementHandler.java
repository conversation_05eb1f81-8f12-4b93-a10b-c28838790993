package com.game.handler.http.agentGame_1005_betby;

import com.game.engine.io.conf.ConstantConfig;
import com.game.engine.io.handler.HttpHandler;
import com.game.engine.script.IHandlerEntity;
import com.game.engine.script.ScriptLoader;
import com.game.engine.utils.JsonUtils;
import com.game.engine.utils.MsgUtil;
import com.game.entity.game.GameNote;
import com.game.gamesr.manager.AgentGameMrg;
import com.game.gamesr.scripts.IAgentGameScript;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.LinkedHashMap;
import java.util.Map;

@IHandlerEntity(path = "/betby/bet/settlement", desc = "投注结算")
public class ReqBetSettlementHandler extends HttpHandler {

    private static final Logger LOGGER = LoggerFactory.getLogger(ReqBetSettlementHandler.class);

    @Override
    public void run() {
        final Map<String, Object> responseMap = new LinkedHashMap<>();
        try {
            Map<String, Object> requestMap = JwtVerifier.verifyAndDecodeJWT((String)paramsMap.get("payload"));
            if (ConstantConfig.getInstance().isAgentDebug()) {
                LOGGER.info("betby BET_SETTLEMENT request: {}", JsonUtils.writeAsJson(paramsMap));
                LOGGER.info("betby BET_SETTLEMENT payload: {}", requestMap);
            }

//            final boolean ipBlackList = ScriptLoader.getInstance().functionScript("AgentGameScript",
//                    (IAgentGameScript script) -> script.isIpBlacklist(MsgUtil.getClientIp(session), AgentGame.BETBY.getType()));
//            if (!ipBlackList) {
//                // 其他错误
//                responseMap.put("code", 3000);
//                MsgUtil.responseHttp(responseMap, session);
//                return;
//            }

            @SuppressWarnings("unchecked")
            Map<String, Object> paramsMap = (Map<String, Object>)requestMap.get("payload");

            // 解析请求参数
            String betTransactionId = (String) paramsMap.get("bet_transaction_id");
            String status = (String) paramsMap.get("status");

            // 验证请求参数
            if (betTransactionId == null || status == null) {
                BetbyConstants.sendErrorResponse(session);
                return;
            }

            // 查找投注记录并进行结算处理
            boolean settlementResult = processSettlement(betTransactionId, status);
            if (!settlementResult) {
                BetbyConstants.sendErrorResponse(session);
                return;
            }

            MsgUtil.responseHttp(responseMap, session);

        } catch (Exception e) {
            LOGGER.error("ReqBetSettlementHandle error", e);
            BetbyConstants.sendErrorResponse(session);
        }
    }

    private boolean processSettlement(String betTransactionId, String status) {
        try {
            // 这里实现结算的业务逻辑，比如更新数据库中的投注记录、余额等
            // 查找之前的投注记录
            GameNote gameNote = AgentGameMrg.getInstance().findByTransactionId(betTransactionId);
            if (gameNote == null) {
                LOGGER.error("Transaction not found: {}", betTransactionId);
                return false;
            }

            ScriptLoader.getInstance().functionScript("AgentGameScript",
                    (IAgentGameScript script) -> script.updateGameNoteSportClose(betTransactionId));

            return true;
        } catch (Exception e) {
            LOGGER.error("Error processing settlement", e);
            return false;
        }
    }
}
