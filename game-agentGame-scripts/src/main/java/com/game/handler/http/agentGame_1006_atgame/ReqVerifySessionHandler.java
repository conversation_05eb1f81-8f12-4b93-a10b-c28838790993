package com.game.handler.http.agentGame_1006_atgame;

import com.alibaba.fastjson.JSONObject;
import com.game.engine.io.conf.ConstantConfig;
import com.game.engine.io.handler.HttpHandler;
import com.game.engine.math.BigDecimalUtils;
import com.game.engine.script.IHandlerEntity;
import com.game.engine.script.ScriptLoader;
import com.game.engine.utils.MsgUtil;
import com.game.entity.player.Player;
import com.game.gamesr.manager.AgentGameMrg;
import com.game.gamesr.scripts.IAgentGameScript;
import com.game.gamesr.utils.JWTUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.LinkedHashMap;
import java.util.Map;
import java.util.Objects;

@IHandlerEntity(path = "/atgame/VerifySession", desc = "玩家身份验证")
public class ReqVerifySessionHandler extends HttpHandler {
    private static final Logger LOGGER = LoggerFactory.getLogger(ReqVerifySessionHandler.class);

    public enum ErrorCode {
        PLAYER_BANNED(2000),     // 玩家被禁止
        INVALID_TOKEN(2001),     // 玩家token验证错误
        OTHER_ERROR(3000);       // 其他错误

        private final int code;  // 错误代码

        // 枚举构造函数，用于初始化错误代码
        ErrorCode(int code) {
            this.code = code;
        }

        // 获取错误代码
        public int getCode() {
            return code;
        }
    }

    @Override
    public void run() {
        final Map<String, Object> responseMap = new LinkedHashMap<>();
        try {
            // 打印请求信息
            final Map<String, Object> paramsMap = getParamsMap();
            if (ConstantConfig.getInstance().isAgentDebug()) {
                LOGGER.info("ReqVerifySessionHandler: {}", paramsMap);
            }

            final String token = (String) paramsMap.get("token");

            final Map<String, Object> jwtMap = JWTUtils.verify(token);
            if (jwtMap.isEmpty() || !Objects.isNull(jwtMap.get("error"))) {
                responseMap.put("code", ErrorCode.INVALID_TOKEN.getCode());
                MsgUtil.responseHttp(responseMap, session);
                return;
            }

            final long playerId = (long) jwtMap.get("userId");
            final String playerName = (String) jwtMap.get("playerName");

            final Player player = AgentGameMrg.getInstance().findDbPlayer(playerId);
            if (player == null) {
                responseMap.put("code", ErrorCode.PLAYER_BANNED.getCode());
                MsgUtil.responseHttp(responseMap, session);
                return;
            }

            final double realBalance = ScriptLoader.getInstance().functionScript("AgentGameScript",
                    (IAgentGameScript script) -> script.realBalance(player));

            final double balance = ScriptLoader.getInstance().functionScript("AgentGameScript",
                    (IAgentGameScript script) -> script.amountTransformUsd(player, realBalance));

            final JSONObject data = new JSONObject();
            data.put("uname", playerName);
            data.put("balance", BigDecimalUtils.round(balance, 2));

            responseMap.put("code", 0);
            responseMap.put("data", data);
            MsgUtil.responseHttp(responseMap, session);
        } catch (Exception e) {
            LOGGER.error("ReqVerifySessionHandler", e);
            // 根据实际需求处理错误情况
            responseMap.put("code", ErrorCode.OTHER_ERROR.getCode());
            MsgUtil.responseHttp(responseMap, session);
        }
    }
}