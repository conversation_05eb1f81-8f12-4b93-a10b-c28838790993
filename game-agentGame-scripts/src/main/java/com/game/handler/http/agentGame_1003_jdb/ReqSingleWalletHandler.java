package com.game.handler.http.agentGame_1003_jdb;

import com.alibaba.fastjson.JSONObject;
import com.game.c_entity.middleplatform.C_BaseGamePlatform;
import com.game.engine.io.conf.ConstantConfig;
import com.game.engine.io.handler.HttpHandler;
import com.game.engine.math.BigDecimalUtils;
import com.game.engine.script.IHandlerEntity;
import com.game.engine.script.ScriptLoader;
import com.game.engine.utils.JsonUtils;
import com.game.engine.utils.MsgUtil;
import com.game.entity.game.GameNote;
import com.game.entity.player.FreeGameInfo;
import com.game.entity.player.GameInfo;
import com.game.entity.player.Player;
import com.game.enums.AgentGame;
import com.game.enums.ChangeType;
import com.game.gamesr.NotifyData;
import com.game.gamesr.manager.AgentGameMrg;
import com.game.gamesr.manager.DataAgentGameMrg;
import com.game.gamesr.scripts.IAgentGameScript;
import com.game.gamesr.utils.AESUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.LinkedHashMap;
import java.util.Map;

//http://127.0.0.1:8680/jdb/singleWallet
@IHandlerEntity(path = "/jdb/singleWallet", desc = "")
public class ReqSingleWalletHandler extends HttpHandler {
    private static final Logger LOGGER = LoggerFactory.getLogger(ReqSingleWalletHandler.class);

    @Override
    public void run() {
        final Map<String, Object> responseMap = new LinkedHashMap<>();
        try {
            final C_BaseGamePlatform c_baseGamePlatform = DataAgentGameMrg.getInstance()
                    .findC_BaseGamePlatformSupplierId(this.getClass().getSimpleName(), AgentGame.JDB.getType());
            final String x = (String) paramsMap.get("x");
            final String data = AESUtils.decrypt(x, c_baseGamePlatform.getSecretKey(), c_baseGamePlatform.getToken());

            final JSONObject jsonObject = JsonUtils.readFromJson(data, JSONObject.class);
            final int action = jsonObject.getInteger("action");
            final String uid = jsonObject.getString("uid");

            if (ConstantConfig.getInstance().isAgentDebug()) {
                if (action == 4 || action == 16) {
                    LOGGER.info("ReqSingleWalletHandler，params：{}", data);
                }
            }

            final long playerId = AgentGameMrg.getInstance().getAgentGamePlayerId(uid);
            final Player player = AgentGameMrg.getInstance().findDbPlayer(playerId);
            if (player == null) {
                responseMap.put("status", 7501);
                responseMap.put("err_text", "User ID cannot be found");
                MsgUtil.responseHttp(responseMap, session);
                return;
            }

            final boolean ipBlackList = ScriptLoader.getInstance().functionScript("AgentGameScript",
                    (IAgentGameScript script) -> script.isIpBlacklist(MsgUtil.getClientIp(session), AgentGame.JDB.getType()));
            if (!ipBlackList) {
                responseMap.put("status", 7501);
                responseMap.put("err_text", "User ID cannot be found");
                MsgUtil.responseHttp(responseMap, session);
                return;
            }

            final double realBalance = ScriptLoader.getInstance().functionScript("AgentGameScript",
                    (IAgentGameScript script) -> script.realBalance(player));

            double balance = ScriptLoader.getInstance().functionScript("AgentGameScript",
                    (IAgentGameScript script) -> script.amountTransformUsd(player, realBalance));

            switch (action) {
                case 6://余额
                    responseMap.put("status", "0000");
                    responseMap.put("balance", BigDecimalUtils.round(balance, 2));
                    responseMap.put("err_text", "");
                    MsgUtil.responseHttp(responseMap, session);
                    break;
                case 4://取消下注并取消结算
                {
                    final long transferId = jsonObject.getLong("transferId");
                    final GameNote gameNote = AgentGameMrg.getInstance().findRoundIdGameNote(player, transferId + "");
                    if (gameNote == null) {
                        responseMap.put("status", "0000");
                        responseMap.put("balance", BigDecimalUtils.round(balance, 2));
                        responseMap.put("err_text", "");
                        MsgUtil.responseHttp(responseMap, session);
                        return;
                    }

                    if (gameNote.getStatus() == 3) {
                        final double usdBalance = ScriptLoader.getInstance().functionScript("AgentGameScript",
                                (IAgentGameScript script) -> script.amountTransformUsd(player, gameNote.getBalance()));

                        final double betAmount = ScriptLoader.getInstance().functionScript("AgentGameScript",
                                (IAgentGameScript script) -> script.amountTransformUsd(player, gameNote.getBetAmount()));

                        responseMap.put("status", "0000");
                        responseMap.put("balance", BigDecimalUtils.add(usdBalance, betAmount, 2));
                        responseMap.put("err_text", "");
                        MsgUtil.responseHttp(responseMap, session);
                        return;
                    }

//                    final double betAmount = ScriptLoader.getInstance().functionScript("AgentGameScript",
//                            (IAgentGameScript script) -> script.amountTransformUsd(player, gameNote.getBetAmount()));
//
//                    final double win = ScriptLoader.getInstance().functionScript("AgentGameScript",
//                            (IAgentGameScript script) -> script.amountTransformUsd(player, gameNote.getWin()));

//                    balance = BigDecimalUtils.sub(balance + betAmount, win, 2);

//                    responseMap.put("status", "0000");
//                    responseMap.put("balance", BigDecimalUtils.round(balance, 2));
//                    responseMap.put("err_text", "");
//                    MsgUtil.responseHttp(responseMap, session);

                    final NotifyData notifyData = new NotifyData();
                    notifyData.setType(ChangeType.refund.getType())
                            .setPid(playerId)
                            .setBetAmount(gameNote.getBetAmount())
                            .setWin(gameNote.getWin())
                            .setUpdSessionId(MsgUtil.getSessionID(session))
                            .setGameId(gameNote.getGameId())
                            .setCurrencyId(gameNote.getCurrencyId());

                    ScriptLoader.getInstance().consumerScript("AgentGameScript",
                            (IAgentGameScript script) -> script.updateGameNoteStatus(player, transferId + "", 3));
                    ScriptLoader.getInstance().consumerScript("AgentGameScript",
                            (IAgentGameScript script) -> script.updateCurrency(notifyData));
                    break;
                }
                case 8://下注并结算
                {
                    final long transferId = jsonObject.getLong("transferId");
                    final String historyId = jsonObject.getString("historyId");
                    final double betAmount = Math.abs(jsonObject.getDouble("bet"));
                    final double win = jsonObject.getDouble("win");

                    GameNote gameNote = AgentGameMrg.getInstance().findRoundIdGameNote(player, transferId + "");
                    if (gameNote != null && gameNote.getTransactionIds().contains(historyId)) {
                        responseMap.put("status", "0000");
                        responseMap.put("balance", BigDecimalUtils.round(balance, 2));
                        responseMap.put("err_text", "");
                        MsgUtil.responseHttp(responseMap, session);
                        return;
                    }

                    if (betAmount > balance) {
                        responseMap.put("status", 6006);
                        responseMap.put("balance", BigDecimalUtils.round(balance, 2));
                        responseMap.put("err_text", "Insufficient player balance");
                        MsgUtil.responseHttp(responseMap, session);
                        return;
                    }

                    balance = BigDecimalUtils.add(BigDecimalUtils.sub(balance, betAmount, 2), win, 2);

//                    responseMap.put("status", "0000");
//                    responseMap.put("balance", BigDecimalUtils.round(balance, 2));
//                    responseMap.put("err_text", "");
//                    MsgUtil.responseHttp(responseMap, session);

                    final double bet = ScriptLoader.getInstance().functionScript("AgentGameScript",
                            (IAgentGameScript script) -> script.usdTransformAmount(player, betAmount));

                    final double wins = ScriptLoader.getInstance().functionScript("AgentGameScript",
                            (IAgentGameScript script) -> script.usdTransformAmount(player, win));

                    final double finalBalance = balance;
                    final double balances = ScriptLoader.getInstance().functionScript("AgentGameScript",
                            (IAgentGameScript script) -> script.usdTransformAmount(player, finalBalance));

                    if (gameNote == null) {
                        gameNote = ScriptLoader.getInstance().functionScript("AgentGameScript",
                                (IAgentGameScript script) -> script.addGameNote(player, transferId + "", historyId, 2, bet, bet, wins, balances));
                    } else {
                        gameNote = ScriptLoader.getInstance().functionScript("AgentGameScript",
                                (IAgentGameScript script) -> script.updateGameNote(player, transferId + "", historyId, 2, wins, balances));
                    }

                    final NotifyData notifyData = new NotifyData();
                    notifyData.setType(ChangeType.special.getType())
                            .setPid(playerId)
                            .setNoteId(gameNote.getNoteId() + "")
                            .setBetAmount(gameNote.getBetAmount())
                            .setValidBets(gameNote.getBetAmount())
                            .setWin(wins)
                            .setTotalWin(gameNote.getWin())
                            .setUpdSessionId(MsgUtil.getSessionID(session))
                            .setGameId(gameNote.getGameId())
                            .setCurrencyId(gameNote.getCurrencyId());

                    ScriptLoader.getInstance().consumerScript("AgentGameScript",
                            (IAgentGameScript script) -> script.updateCurrency(notifyData));
                    break;
                }
                case 16://免费场次派彩
                {
                    final long transferId = jsonObject.getLong("transferId");
                    final long eventId = jsonObject.getLong("eventId");
                    final double win = jsonObject.getDouble("amount");

                    final GameNote gameNote = AgentGameMrg.getInstance().findRoundIdGameNote(player, transferId + "");
                    if (gameNote != null && gameNote.getTransactionIds().contains(eventId + "")) {
                        responseMap.put("status", "0000");
                        responseMap.put("balance", BigDecimalUtils.round(balance, 2));
                        responseMap.put("err_text", "");
                        MsgUtil.responseHttp(responseMap, session);
                        return;
                    }

//                    balance = BigDecimalUtils.add(balance, win, 2);

//                    responseMap.put("status", "0000");
//                    responseMap.put("balance", BigDecimalUtils.round(balance, 2));
//                    responseMap.put("err_text", "");
//                    MsgUtil.responseHttp(responseMap, session);

                    final double wins = ScriptLoader.getInstance().functionScript("AgentGameScript",
                            (IAgentGameScript script) -> script.usdTransformAmount(player, win));

                    if (wins > 0) {
                        ScriptLoader.getInstance().consumerScript("AgentGameScript",
                                (IAgentGameScript script) -> script.freeAddGameNote(player, transferId + "", eventId + "", 2, wins, realBalance));
                    }

                    final FreeGameInfo freeGameInfo = player.getFreeGameInfo(player.getPlayerCurrencyId());
                    final GameInfo gameInfo = freeGameInfo.getFreeGame(player.getGameId());

                    final NotifyData notifyData = new NotifyData();
                    notifyData.setType(ChangeType.Free.getType())
                            .setPid(playerId)
                            .setWin(wins)
                            .setFreeTimes(-gameInfo.getFreeTimes())
                            .setUpdSessionId(MsgUtil.getSessionID(session))
                            .setGameId(player.getGameId())
                            .setCurrencyId(player.getPlayerCurrencyId());

                    ScriptLoader.getInstance().consumerScript("AgentGameScript",
                            (IAgentGameScript script) -> script.updateCurrency(notifyData));
                }
                break;
                default:
                    break;
            }
        } catch (Exception e) {
            LOGGER.error("ReqSingleWalletHandler", e);
            responseMap.put("status", 9999);
            responseMap.put("err_text", "server error");
            MsgUtil.responseHttp(responseMap, session);
        }
    }

    //                case 9://下注
//                {
//                    final String roundId = jsonObject.getString("gameRoundSeqNo");
//                    final long transferId = jsonObject.getLong("transferId");
//                    final double betAmount = Math.abs(jsonObject.getDouble("amount"));
//
//                    GameNote gameNote = AgentGameMrg.getInstance().findRoundIdGameNote(player.getPlayerId(), player.getPlatformId(), roundId + "");
//                    if (gameNote != null && gameNote.getTransactionIds().contains(transferId + "")) {
//                        responseMap.put("status", 9999);
//                        responseMap.put("err_text", "transferId is exist");
//                        MsgUtil.responseHttp(responseMap, session);
//                        return;
//                    }
//
//                    if (betAmount > balance) {
//                        responseMap.put("status", 6006);
//                        responseMap.put("balance", balance);
//                        responseMap.put("err_text", "Insufficient player balance");
//                        MsgUtil.responseHttp(responseMap, session);
//                        return;
//                    }
//
//                    balance = BigDecimalUtils.sub(balance, betAmount, 4);
//
//                    responseMap.put("status", "0000");
//                    responseMap.put("balance", balance);
//                    responseMap.put("err_text", "");
//                    MsgUtil.responseHttp(responseMap, session);
//
//                    final double bet = ScriptLoader.getInstance().functionScript("AgentGameScript",
//                            (IAgentGameScript script) -> script.usdTransformAmount(player, player.getGameCurrencyId(), betAmount));
//
//                    final double finalBalance1 = balance;
//                    final double balances = ScriptLoader.getInstance().functionScript("AgentGameScript",
//                            (IAgentGameScript script) -> script.usdTransformAmount(player, player.getGameCurrencyId(), finalBalance1));
//
//                    if (gameNote == null) {
//                        gameNote = ScriptLoader.getInstance().functionScript("AgentGameScript",
//                                (IAgentGameScript script) -> script.addGameNote(player, roundId + "", transferId + "", bet, 0, balances));
//                    }
//
//                    final NotifyData notifyData = new NotifyData();
//                    notifyData.setType(ChangeType.cost.getChange())
//                            .setPid(playerId)
//                            .setNoteId(gameNote.getNoteId() + "")
//                            .setBetAmount(gameNote.getBetAmount());
//                    ScriptLoader.getInstance().consumerScript("AgentGameScript",
//                            (IAgentGameScript script) -> script.updateCurrency(notifyData));
//                    break;
//                }
//                case 10://结算
//                {
//                    final long transferId = jsonObject.getLong("gameRoundSeqNo");
//                    final double validBet = jsonObject.getDouble("validBet");
//                    final double win = jsonObject.getDouble("win");
//
//                    balance = BigDecimalUtils.add(balance, win, 4);
//
//                    responseMap.put("status", "0000");
//                    responseMap.put("balance", balance);
//                    responseMap.put("err_text", "");
//                    MsgUtil.responseHttp(responseMap, session);
//
//                    final double validBets = ScriptLoader.getInstance().functionScript("AgentGameScript",
//                            (IAgentGameScript script) -> script.usdTransformAmount(player, player.getGameCurrencyId(), validBet));
//
//                    final double wins = ScriptLoader.getInstance().functionScript("AgentGameScript",
//                            (IAgentGameScript script) -> script.usdTransformAmount(player, player.getGameCurrencyId(), win));
//
//                    final GameNote gameNote = ScriptLoader.getInstance().functionScript("AgentGameScript", (IAgentGameScript script) ->
//                            script.updateGameNote(player, transferId + "", , 2, validBets, wins, realBalance));
//
//                    final NotifyData notifyData = new NotifyData();
//                    notifyData.setType(ChangeType.win.getChange())
//                            .setPid(playerId)
//                            .setNoteId(gameNote.getNoteId() + "")
//                            .setBetAmount(gameNote.getBetAmount())
//                            .setValidBets(validBets)
//                            .setWin(wins);
//                    ScriptLoader.getInstance().consumerScript("AgentGameScript", (IAgentGameScript script) ->
//                            script.updateCurrency(notifyData));
//                    break;
//                }
//                case 11://取消下注
//                {
//                    final long transferId = jsonObject.getLong("gameRoundSeqNo");
//                    final GameNote gameNote = AgentGameMrg.getInstance().findRoundIdGameNote(playerId, player.getPlatformId(), transferId + "");
//                    if (gameNote == null) {
//                        responseMap.put("status", 9999);
//                        responseMap.put("err_text", "transferId not found");
//                        MsgUtil.responseHttp(responseMap, session);
//                        return;
//                    }
//
//                    if (gameNote.getStatus() == 2) {
//                        responseMap.put("status", 9999);
//                        responseMap.put("err_text", "Already accepted and cannot be canceled");
//                        MsgUtil.responseHttp(responseMap, session);
//                        return;
//                    }
//
//                    if (gameNote.getStatus() == 3) {
//                        responseMap.put("status", 9999);
//                        responseMap.put("err_text", "Already canceled");
//                        MsgUtil.responseHttp(responseMap, session);
//                        return;
//                    }
//
//                    final double betAmount = ScriptLoader.getInstance().functionScript("AgentGameScript",
//                            (IAgentGameScript script) -> script.amountTransformUsd(player, player.getGameCurrencyId(), gameNote.getBetAmount()));
//
//                    final double win = ScriptLoader.getInstance().functionScript("AgentGameScript",
//                            (IAgentGameScript script) -> script.amountTransformUsd(player, player.getGameCurrencyId(), gameNote.getWin()));
//
//                    balance = BigDecimalUtils.sub(balance + betAmount, win, 4);
//
//                    responseMap.put("status", "0000");
//                    responseMap.put("balance", balance);
//                    responseMap.put("err_text", "");
//                    MsgUtil.responseHttp(responseMap, session);
//
//                    final NotifyData notifyData = new NotifyData();
//                    notifyData.setType(ChangeType.refund.getChange())
//                            .setPid(playerId)
//                            .setBetAmount(gameNote.getBetAmount())
//                            .setWin(gameNote.getWin());
//
//                    ScriptLoader.getInstance().consumerScript("AgentGameScript", (IAgentGameScript script) ->
//                            script.updateCurrency(notifyData));
//                    ScriptLoader.getInstance().consumerScript("AgentGameScript", (IAgentGameScript script) ->
//                            script.updateGameNoteStatus(player, transferId + "", 3));
//                    break;
//                }
//                case 13://提款
//                {
//                    final long transferId = jsonObject.getLong("transferId");
//                    final double amount = Math.abs(jsonObject.getDouble("amount"));
//
//                    if (amount > balance) {
//                        responseMap.put("status", 6006);
//                        responseMap.put("balance", balance);
//                        responseMap.put("err_text", "Insufficient player balance");
//                        MsgUtil.responseHttp(responseMap, session);
//                        return;
//                    }
//
//                    balance = BigDecimalUtils.sub(balance, amount, 4);
//
//                    responseMap.put("status", "0000");
//                    responseMap.put("balance", balance);
//                    responseMap.put("err_text", "");
//                    MsgUtil.responseHttp(responseMap, session);
//
//                    final double betAmount = ScriptLoader.getInstance().functionScript("AgentGameScript",
//                            (IAgentGameScript script) -> script.usdTransformAmount(player, player.getGameCurrencyId(), amount));
//
//                    final GameNote gameNote = ScriptLoader.getInstance().functionScript("AgentGameScript",
//                            (IAgentGameScript script) -> script.addGameNote(player, transferId + "", , betAmount, 0, realBalance));
//
//                    final NotifyData notifyData = new NotifyData();
//                    notifyData.setType(ChangeType.cost.getChange())
//                            .setPid(playerId)
//                            .setNoteId(gameNote.getNoteId() + "")
//                            .setBetAmount(betAmount);
//                    ScriptLoader.getInstance().consumerScript("AgentGameScript", (IAgentGameScript script) ->
//                            script.updateCurrency(notifyData));
//                    break;
//                }
//                case 14://存款
//                {
//                    final long transferId = jsonObject.getLong("transferId");
//                    final double win = jsonObject.getDouble("amount");
//
//                    balance = BigDecimalUtils.add(balance, win, 4);
//
//                    responseMap.put("status", "0000");
//                    responseMap.put("balance", balance);
//                    responseMap.put("err_text", "");
//                    MsgUtil.responseHttp(responseMap, session);
//
//                    final double wins = ScriptLoader.getInstance().functionScript("AgentGameScript",
//                            (IAgentGameScript script) -> script.usdTransformAmount(player, player.getGameCurrencyId(), win));
//
//                    final GameNote gameNote = ScriptLoader.getInstance().functionScript("AgentGameScript", (IAgentGameScript script) ->
//                            script.updateGameNote(player, transferId + "", , 2, 0, wins, realBalance));
//
//                    final NotifyData notifyData = new NotifyData();
//                    notifyData.setType(ChangeType.win.getChange())
//                            .setPid(playerId)
//                            .setNoteId(gameNote.getNoteId() + "")
//                            .setBetAmount(gameNote.getBetAmount())
//                            .setValidBets(gameNote.getBetAmount())
//                            .setWin(wins);
//                    ScriptLoader.getInstance().consumerScript("AgentGameScript", (IAgentGameScript script) ->
//                            script.updateCurrency(notifyData));
//                    break;
//                }
//                case 15://取消退款
//                {
//                    final long transferId = jsonObject.getLong("transferId");
//                    final GameNote gameNote = AgentGameMrg.getInstance().findRoundIdGameNote(playerId, player.getPlatformId(), transferId + "");
//                    if (gameNote == null) {
//                        responseMap.put("status", 9999);
//                        responseMap.put("err_text", "transferId not found");
//                        MsgUtil.responseHttp(responseMap, session);
//                        return;
//                    }
//
//                    if (gameNote.getStatus() == 2) {
//                        responseMap.put("status", 9999);
//                        responseMap.put("err_text", "Already accepted and cannot be canceled");
//                        MsgUtil.responseHttp(responseMap, session);
//                        return;
//                    }
//
//                    if (gameNote.getStatus() == 3) {
//                        responseMap.put("status", 9999);
//                        responseMap.put("err_text", "Already canceled");
//                        MsgUtil.responseHttp(responseMap, session);
//                        return;
//                    }
//
//                    final double betAmount = ScriptLoader.getInstance().functionScript("AgentGameScript",
//                            (IAgentGameScript script) -> script.amountTransformUsd(player, player.getGameCurrencyId(), gameNote.getBetAmount()));
//
//                    final double win = ScriptLoader.getInstance().functionScript("AgentGameScript",
//                            (IAgentGameScript script) -> script.amountTransformUsd(player, player.getGameCurrencyId(), gameNote.getWin()));
//
//                    balance = BigDecimalUtils.sub(balance + betAmount, win, 4);
//
//                    responseMap.put("status", "0000");
//                    responseMap.put("balance", balance);
//                    responseMap.put("err_text", "");
//                    MsgUtil.responseHttp(responseMap, session);
//
//                    final NotifyData notifyData = new NotifyData();
//                    notifyData.setType(ChangeType.refund.getChange())
//                            .setPid(playerId)
//                            .setBetAmount(gameNote.getBetAmount())
//                            .setWin(gameNote.getWin());
//
//                    ScriptLoader.getInstance().consumerScript("AgentGameScript", (IAgentGameScript script) ->
//                            script.updateCurrency(notifyData));
//                    ScriptLoader.getInstance().consumerScript("AgentGameScript", (IAgentGameScript script) ->
//                            script.updateGameNoteStatus(player, transferId + "", 3));
//                    break;
//                }
}
