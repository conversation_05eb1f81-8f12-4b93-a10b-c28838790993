//package com.game.handler.http.agentGame_1000;
//
//import com.game.engine.io.handler.HttpHandler;
//import com.game.engine.script.IHandlerEntity;
//import com.game.engine.script.ScriptLoader;
//import com.game.engine.utils.JsonUtils;
//import com.game.engine.utils.MsgUtil;
//import com.game.engine.utils.TimeUtil;
//import com.game.entity.player.Player;
//import com.game.gamesr.main.AgentGameServer;
//import com.game.gamesr.manager.AgentGameMrg;
//import com.game.gamesr.scripts.IAgentGameLogicScript;
//import com.game.manager.ServersMrg;
//import com.proto.InnerMessage;
//import com.proto.MIDMessage;
//import io.netty.channel.Channel;
//import io.netty.util.internal.StringUtil;
//import org.slf4j.Logger;
//import org.slf4j.LoggerFactory;
//
//import java.util.LinkedHashMap;
//import java.util.Map;
//
////http://127.0.0.1:8680/seamless/logout
//@IHandlerEntity(path = "/seamless/logout", desc = "登出")
//public class ReqSeamlessLogoutHandler extends HttpHandler {
//    private static final Logger LOGGER = LoggerFactory.getLogger(ReqSeamlessLogoutHandler.class);
//
//    @Override
//    public void run() {
//        try {
//            LOGGER.info("ReqSeamlessLogoutHandler，params：{}", JsonUtils.writeAsJson(paramsMap));
//            final boolean isIpBlacklist = ScriptLoader.getInstance().functionScript("AgentGameLogic_1000_Script", (IAgentGameLogicScript script) -> script.isIpBlacklist(MsgUtil.getClientIp(session)));
//            if (!isIpBlacklist) {
//                return;
//            }
//
//            final String player = (String) paramsMap.get("player");
//
//            final Map<String, Object> responseMap = new LinkedHashMap<>();
//            if (StringUtil.isNullOrEmpty(player)) {
//                responseMap.put("message", "INVALID_ARGUMENT");
//                MsgUtil.responseHttp(responseMap, session);
//                return;
//            }
//            responseMap.put("timestamp", TimeUtil.currentTimeMillis());
//            MsgUtil.responseHttp(responseMap, session);
//
//            final long playerId = AgentGameMrg.getInstance().getAgentGamePlayerId(player);
//
//            final InnerMessage.InnerNotifyLogoutAgentGameMessage.Builder res = InnerMessage.InnerNotifyLogoutAgentGameMessage.newBuilder();
//            res.setMsgID(MIDMessage.MID.InnerNotifyLogoutAgentGame_VALUE)
//                    .setPlayerId(playerId);
//            final Player dbPlayer = AgentGameMrg.getInstance().findDbPlayer(playerId);
//            final ServersMrg serversMrg = AgentGameServer.getInstance().getAgentGameTcpClient2Hall().getServersMrg();
//            final Channel hallSession = serversMrg.getSession(dbPlayer.getHallId());
//            MsgUtil.sendInnerMsg(hallSession, res.build(), pid);
//        } catch (Exception e) {
//            LOGGER.error("ReqSeamlessLogoutHandler", e);
//        }
//    }
//
//}
