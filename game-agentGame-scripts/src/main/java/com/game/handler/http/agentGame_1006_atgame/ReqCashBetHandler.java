package com.game.handler.http.agentGame_1006_atgame;

import com.alibaba.fastjson.JSONObject;
import com.game.engine.io.conf.ConstantConfig;
import com.game.engine.io.handler.HttpHandler;
import com.game.engine.math.BigDecimalUtils;
import com.game.engine.script.IHandlerEntity;
import com.game.engine.script.ScriptLoader;
import com.game.engine.utils.MsgUtil;
import com.game.entity.game.GameNote;
import com.game.entity.player.Player;
import com.game.enums.AgentGame;
import com.game.enums.ChangeType;
import com.game.gamesr.NotifyData;
import com.game.gamesr.manager.AgentGameMrg;
import com.game.gamesr.scripts.IAgentGameScript;
import com.game.gamesr.utils.JWTUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.LinkedHashMap;
import java.util.Map;
import java.util.Objects;

@IHandlerEntity(path = "/atgame/Cash/Bet", desc = "玩家投注")
public class ReqCashBetHandler extends HttpHandler {
    private static final Logger LOGGER = LoggerFactory.getLogger(ReqCashBetHandler.class);

    public enum ErrorCode {
        PLAYER_BANNED(2000),     // 玩家被禁止
        INSUFFICIENT_BALANCE(2010),  // 投注失败，如余额不足
        OTHER_ERROR(3000);       // 其他错误

        private final int code;  // 错误代码

        // 枚举构造函数，用于初始化错误代码
        ErrorCode(int code) {
            this.code = code;
        }

        // 获取错误代码
        public int getCode() {
            return code;
        }
    }

    @Override
    public void run() {
        final Map<String, Object> responseMap = new LinkedHashMap<>();
        try {
            // 打印请求信息
            final Map<String, Object> paramsMap = getParamsMap();
            if (ConstantConfig.getInstance().isAgentDebug()) {
                LOGGER.info("ReqCashBetHandler: {}", paramsMap);
            }

            final String uname = (String) paramsMap.get("uname");
            final String token = (String) paramsMap.get("token");
            final String betid = (String) paramsMap.get("betid");
            final String sessionid = (String) paramsMap.get("sessionid");
            final String gameid = (String) paramsMap.get("gameid");
            final double bet = (double) paramsMap.get("bet");
            final double award = (double) paramsMap.get("award");
            final boolean is_end_round = (boolean) paramsMap.get("is_end_round");
            final int ctime = (int) paramsMap.get("ctime");

            final Map<String, Object> jwtMap = JWTUtils.verify(token);
            if (jwtMap.isEmpty() || !Objects.isNull(jwtMap.get("error"))) {
                responseMap.put("code", ErrorCode.OTHER_ERROR.getCode());
                MsgUtil.responseHttp(responseMap, session);
                return;
            }

            final long playerId = (long) jwtMap.get("userId");
            final String currency = (String) jwtMap.get("currency");

            final Player player = AgentGameMrg.getInstance().findDbPlayer(playerId);
            if (player == null) {
                responseMap.put("code", ErrorCode.PLAYER_BANNED.getCode());
                MsgUtil.responseHttp(responseMap, session);
                return;
            }

            final boolean ipBlackList = ScriptLoader.getInstance().functionScript("AgentGameScript",
                    (IAgentGameScript script) -> script.isIpBlacklist(MsgUtil.getClientIp(session), AgentGame.ATGAME.getType()));
            if (!ipBlackList) {
                responseMap.put("code", ErrorCode.OTHER_ERROR.getCode());
                MsgUtil.responseHttp(responseMap, session);
                return;
            }

            final double realBalance = ScriptLoader.getInstance().functionScript("AgentGameScript",
                    (IAgentGameScript script) -> script.realBalance(player));
            final double balance = ScriptLoader.getInstance().functionScript("AgentGameScript",
                    (IAgentGameScript script) -> script.amountTransformUsd(player, realBalance));

            // 1. 查找是否存在重复下注
            GameNote gameNote = AgentGameMrg.getInstance().findRoundIdGameNote(player, sessionid);

            if (gameNote != null && gameNote.getTransactionIds().contains(betid)) {
                // 如果收到重复betid的请求，需返回首次响应该betid时的相同内容
                LOGGER.warn("Duplicate bet detected: {}", betid);
                final JSONObject data = new JSONObject();
                data.put("uname", uname);
                data.put("betid", betid);
                data.put("balance", BigDecimalUtils.round(balance, 2));

                responseMap.put("code", 0);
                responseMap.put("data", data);
                MsgUtil.responseHttp(responseMap, session);
                return;
            }

            // 3. 检查余额是否足够
            if (bet > balance) {
                LOGGER.warn("Not enough money. bet {} balance: {}", bet, balance);
                responseMap.put("code", ErrorCode.INSUFFICIENT_BALANCE.getCode());
                MsgUtil.responseHttp(responseMap, session);
                return;
            }

            // 4. 扣除玩家余额 // 平台是分币
            final double midBalance = BigDecimalUtils.sub(balance, bet, 2);
            final double finalBalance = BigDecimalUtils.add(midBalance, award, 2);

            // 5. 转换金额为实际金额
            final double betValue = ScriptLoader.getInstance().functionScript("AgentGameScript",
                    (IAgentGameScript script) -> script.usdTransformAmount(player, bet));

            final double win = ScriptLoader.getInstance().functionScript("AgentGameScript",
                    (IAgentGameScript script) -> script.usdTransformAmount(player, award));

            final double balances = ScriptLoader.getInstance().functionScript("AgentGameScript",
                    (IAgentGameScript script) -> script.usdTransformAmount(player, finalBalance));

            // 6. 创建或更新游戏记录
            if (gameNote == null) {
                gameNote = ScriptLoader.getInstance().functionScript("AgentGameScript",
                        (IAgentGameScript script) -> script.addGameNote(player, sessionid, betid, 2, betValue, betValue, win, balances));
            } else {
                gameNote = ScriptLoader.getInstance().functionScript("AgentGameScript",
                        (IAgentGameScript script) -> script.updateGameNote(player, sessionid, betid, 2, win, balances));
            }

            NotifyData notifyData = new NotifyData();
            notifyData.setType(ChangeType.special.getType())
                    .setPid(player.getPlayerId())
                    .setPlayerName(uname)
                    .setNoteId(gameNote.getNoteId() + "")
                    .setBetAmount(betValue)
                    .setValidBets(betValue)
                    .setWin(win)
                    .setTotalWin(gameNote.getWin())
                    .setCurrency(currency)
                    .setUpdSessionId(MsgUtil.getSessionID(session));

            ScriptLoader.getInstance().consumerScript("AgentGameScript",
                    (IAgentGameScript script) -> script.updateCurrency(notifyData));
        } catch (Exception e) {
            LOGGER.error("ReqCashBetHandler", e);
            // 根据实际需求处理错误情况
            responseMap.put("code", ErrorCode.OTHER_ERROR.getCode());
            MsgUtil.responseHttp(responseMap, session);
        }
    }
}
