package com.game.handler.http.agentGame_1007_funky;

import com.alibaba.fastjson.JSONObject;
import com.game.engine.io.conf.ConstantConfig;
import com.game.engine.io.handler.HttpHandler;
import com.game.engine.math.BigDecimalUtils;
import com.game.engine.script.IHandlerEntity;
import com.game.engine.script.ScriptLoader;
import com.game.engine.utils.MsgUtil;
import com.game.entity.game.GameNote;
import com.game.entity.player.Player;
import com.game.enums.AgentGame;
import com.game.enums.ChangeType;

import com.game.enums.Currency;
import com.game.gamesr.NotifyData;
import com.game.gamesr.manager.AgentGameMrg;
import com.game.gamesr.scripts.IAgentGameScript;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.LinkedHashMap;
import java.util.Map;

@IHandlerEntity(path = FunkyConstants.Paths.PLACE_BET, desc = "Funky下注接口")
public class ReqPlaceBetHandler extends HttpHandler {
    private static final Logger LOGGER = LoggerFactory.getLogger(ReqPlaceBetHandler.class);

    /**
     * 下注请求数据
     */
    private static class PlaceBetRequest {
        Player player;
        String sessionId;
        String gameCode;
        String refNo;
        double stake;
        double balance;

        PlaceBetRequest(Player player, String sessionId, String gameCode, String refNo, double stake, double balance) {
            this.player = player;
            this.sessionId = sessionId;
            this.gameCode = gameCode;
            this.refNo = refNo;
            this.stake = stake;
            this.balance = balance;
        }
    }

    @Override
    public void run() {
        try {
            // 获取请求参数
            final Map<String, Object> paramsMap = getParamsMap();
            if (ConstantConfig.getInstance().isAgentDebug()) {
                LOGGER.info("ReqPlaceBetHandler params: {}", paramsMap);
            }

            // 1. 校验请求
            PlaceBetRequest request = validateRequest(paramsMap);
            if (request == null) {
                return; // 校验失败，错误响应已发送
            }

            // 2. 处理业务逻辑
            processPlaceBet(request);

        } catch (Exception e) {
            LOGGER.error("ReqPlaceBetHandler error", e);
            FunkyUtils.sendErrorResponse(session, FunkyConstants.ErrorCode.INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * 校验请求参数和权限
     *
     * @param paramsMap 请求参数
     * @return 校验通过返回PlaceBetRequest对象，失败返回null
     */
    private PlaceBetRequest validateRequest(Map<String, Object> paramsMap) {
        // 1. 验证请求头信息
        if (FunkyHeaderValidator.hasInvalidHeaders(paramsMap)) {
            FunkyUtils.sendErrorResponse(session, FunkyConstants.ErrorCode.PLAYER_NOT_LOGIN);
            return null;
        }

        // 2. 获取请求体参数
        final String playerId = (String) paramsMap.get("playerId");
        final String sessionId = (String) paramsMap.get("sessionId");

        // 获取bet对象
        @SuppressWarnings("unchecked")
        final Map<String, Object> betMap = (Map<String, Object>) paramsMap.get("bet");
        if (betMap == null) {
            LOGGER.warn("Missing required bet parameter");
            FunkyUtils.sendErrorResponse(session, FunkyConstants.ErrorCode.INVALID_INPUT);
            return null;
        }

        final String gameCode = (String) betMap.get("gameCode");
        final String refNo = (String) betMap.get("refNo");
        final Object stakeObj = betMap.get("stake");

        // 3. 验证必要参数
        if (playerId == null || playerId.trim().isEmpty()) {
            LOGGER.warn("Missing required playerId parameter");
            FunkyUtils.sendErrorResponse(session, FunkyConstants.ErrorCode.INVALID_INPUT);
            return null;
        }

        if (!FunkyUtils.isValidPlayerId(playerId)) {
            LOGGER.warn("Invalid playerId format: {} (must be at least 3 characters)", playerId);
            FunkyUtils.sendErrorResponse(session, FunkyConstants.ErrorCode.INVALID_INPUT);
            return null;
        }

        if (sessionId == null || sessionId.trim().isEmpty()) {
            LOGGER.warn("Missing required sessionId parameter");
            FunkyUtils.sendErrorResponse(session, FunkyConstants.ErrorCode.INVALID_INPUT);
            return null;
        }

        if (gameCode == null || gameCode.trim().isEmpty()) {
            LOGGER.warn("Missing required gameCode parameter");
            FunkyUtils.sendErrorResponse(session, FunkyConstants.ErrorCode.INVALID_INPUT);
            return null;
        }

        if (refNo == null || refNo.trim().isEmpty()) {
            LOGGER.warn("Missing required refNo parameter");
            FunkyUtils.sendErrorResponse(session, FunkyConstants.ErrorCode.INVALID_INPUT);
            return null;
        }

        // 4. 验证stake金额
        double stake;
        try {
            if (stakeObj instanceof Number) {
                stake = ((Number) stakeObj).doubleValue();
            } else if (stakeObj instanceof String) {
                stake = Double.parseDouble((String) stakeObj);
            } else {
                throw new NumberFormatException("Invalid stake format");
            }

            if (stake < 0) {
                throw new NumberFormatException("Stake cannot be negative");
            }
        } catch (NumberFormatException e) {
            LOGGER.warn("Invalid stake format: {}", stakeObj);
            FunkyUtils.sendErrorResponse(session, FunkyConstants.ErrorCode.INVALID_INPUT);
            return null;
        }

        // 5. 查找玩家
        long playerIdLong;
        try {
            playerIdLong = Long.parseLong(playerId.trim());
        } catch (NumberFormatException e) {
            LOGGER.warn("Invalid playerId format: {}", playerId);
            FunkyUtils.sendErrorResponse(session, FunkyConstants.ErrorCode.INVALID_INPUT);
            return null;
        }

        final Player player = AgentGameMrg.getInstance().findDbPlayer(playerIdLong);
        if (player == null) {
            FunkyUtils.sendErrorResponse(session, FunkyConstants.ErrorCode.PLAYER_NOT_LOGIN);
            return null;
        }

        // 6. IP黑名单检查
//        final boolean ipBlackList = ScriptLoader.getInstance().functionScript("AgentGameScript",
//                (IAgentGameScript script) -> script.isIpBlacklist(MsgUtil.getClientIp(session), AgentGame.FUNKY.getType()));
//        if (!ipBlackList) {
//            FunkyUtils.sendErrorResponse(session, FunkyConstants.ErrorCode.PLAYER_NOT_LOGIN);
//            return null;
//        }

        // 7. 获取玩家余额
        final double realBalance = ScriptLoader.getInstance().functionScript("AgentGameScript",
                (IAgentGameScript script) -> script.realBalance(player));
        final double balance = ScriptLoader.getInstance().functionScript("AgentGameScript",
                (IAgentGameScript script) -> script.amountTransformUsd(player, realBalance));

        return new PlaceBetRequest(player, sessionId, gameCode, refNo, stake, balance);
    }

    /**
     * 处理下注业务逻辑
     *
     * @param request 已验证的下注请求
     */
    private void processPlaceBet(PlaceBetRequest request) {
        // 1. 检查是否存在重复下注
        GameNote gameNote = AgentGameMrg.getInstance().findRoundIdGameNote(request.player, request.refNo);
        if (gameNote != null && gameNote.getTransactionIds().contains(request.refNo)) {
            // 如果收到重复refNo的请求，需返回首次响应该refNo时的相同内容
            LOGGER.warn("Duplicate bet detected: {}", request.refNo);
            final JSONObject data = new JSONObject();
            data.put("balance", BigDecimalUtils.round(request.balance, 2));
            FunkyUtils.sendSuccessResponse(session, data);
            return;
        }

        // 2. 检查余额是否足够
        if (request.stake > request.balance) {
            LOGGER.warn("Insufficient balance. stake: {} balance: {}", request.stake, request.balance);
            FunkyUtils.sendErrorResponse(session, FunkyConstants.ErrorCode.INSUFFICIENT_BALANCE);
            return;
        }

        // 3. 转换金额为实际金额
        final double betValue = ScriptLoader.getInstance().functionScript("AgentGameScript",
                (IAgentGameScript script) -> script.usdTransformAmount(request.player, request.stake));

        // 4. 扣除玩家余额并创建游戏记录
        final double finalBalance = BigDecimalUtils.sub(request.balance, request.stake, 2);
        final double balances = ScriptLoader.getInstance().functionScript("AgentGameScript",
                (IAgentGameScript script) -> script.usdTransformAmount(request.player, finalBalance));

        // 5. 创建或更新游戏记录
        if (gameNote == null) {
            gameNote = ScriptLoader.getInstance().functionScript("AgentGameScript",
                    (IAgentGameScript script) -> script.addGameNote(request.player, request.sessionId, request.refNo, 2, betValue, betValue, 0, balances));
        } else {
            gameNote = ScriptLoader.getInstance().functionScript("AgentGameScript",
                    (IAgentGameScript script) -> script.updateGameNote(request.player, request.sessionId, request.refNo, 2, 0, balances));
        }

        // 获取货币名称
        Currency currency = Currency.valueOf(gameNote.getCurrencyId());
        String currencyName = "";
        if (currency != null) {
            currencyName = currency.name();
        }

        // 6. 通知余额变更
        NotifyData notifyData = new NotifyData();
        notifyData.setType(ChangeType.cost.getType())
                .setPid(request.player.getPlayerId())
                .setPlayerName(request.playerId)
                .setNoteId(gameNote.getNoteId() + "")//request.refNo
                .setBetAmount(betValue)
                .setValidBets(betValue)
                .setWin(0)
                .setTotalWin(gameNote.getWin())
                .setCurrency(currencyName)
                .setUpdSessionId(MsgUtil.getSessionID(session));

        ScriptLoader.getInstance().consumerScript("AgentGameScript",
                (IAgentGameScript script) -> script.updateCurrency(notifyData));

        // 7. 构建成功响应
        final JSONObject data = new JSONObject();
        data.put("balance", BigDecimalUtils.round(finalBalance, 2));
        FunkyUtils.sendSuccessResponse(session, data);
    }
}
