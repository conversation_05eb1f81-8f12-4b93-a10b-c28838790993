package com.game.handler.http.agentGame_1005_betby;

import com.alibaba.fastjson.JSON;
import com.game.engine.io.conf.ConstantConfig;
import com.game.engine.io.handler.HttpHandler;
import com.game.engine.script.IHandlerEntity;
import com.game.engine.script.ScriptLoader;
import com.game.engine.utils.JsonUtils;
import com.game.engine.utils.MsgUtil;
import com.game.entity.game.GameNote;
import com.game.entity.player.Player;
import com.game.enums.ChangeType;
import com.game.enums.Currency;
import com.game.gamesr.NotifyData;
import com.game.gamesr.manager.AgentGameMrg;
import com.game.gamesr.scripts.IAgentGameScript;
import com.game.handler.http.agentGame_1005_betby.BetbyConstants.ValidationResult;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.Map;

@IHandlerEntity(path = "/betby/bet/refund", desc = "投注退款")
public class ReqBetRefundHandler extends HttpHandler {

    private static final Logger LOGGER = LoggerFactory.getLogger(ReqBetRefundHandler.class);

    private static class BetRefundRequest {
        /**
         * 由运营商分配的要取消的交易ID
         */
        private String betTransactionId;
        /**
         * 处理退款的原因
         */
        private String reason;
        /**
         * (可选)Betby 奖金ID
         */
        private String bonusId;
        /**
         * 包含交易描述的对象
         */
        private TransactionInfo transaction;

        // Getter and Setter
    }


    private static class TransactionInfo {
        /**
         * 由 Betby 分配的交易唯一编码
         */
        private String id;
        /**
         * 下注时 Betby 分配的投注单唯一编码
         */
        private String betslipId;
        /**
         * 由 Betby 分配的玩家唯一编码
         */
        private String playerId;
        /**
         * 运营商唯一编码
         */
        private String operatorId;
        /**
         * 运营商网站唯一编码
         */
        private String operatorBrandId;
        /**
         * 运营商侧分配给玩家的唯一编码
         */
        private String extPlayerId;
        /**
         * 交易时间戳，单位为 Unix 时间
         */
        private double timestamp;
        /**
         * 从玩家余额中退还的金额，单位为分
         */
        private int amount;
        /**
         * 货币代码，表示退款的货币类型
         */
        private String currency;
        /**
         * 玩家货币与欧元的汇率
         */
        private String crossRateEuro;
        /**
         * 操作类型（退款操作）
         */
        private String operation;
        /**
         * 前一交易的ID，用于关联退款的父交易
         */
        private String parentTransactionId;

        // Getter and Setter
    }


    private static class BetRefundResponse {
        /**
         * 由运营商分配的交易唯一编码
         */
        private String id;
        /**
         * 由 Betby 分配的交易唯一编码
         */
        private String extTransactionId;
        /**
         * 由运营商分配的前一交易的ID
         */
        private String parentTransactionId;
        /**
         * 由运营商分配的玩家唯一编码
         */
        private String userId;
        /**
         * 处理的操作类型（退款）
         */
        private String operation;
        /**
         * 退款金额，单位为分
         */
        private int amount;
        /**
         * 货币代码，表示退款的货币类型
         */
        private String currency;
        /**
         * 处理交易后玩家的余额，单位为分
         */
        private int balance;

        // Getter and Setter
    }

    @Override
    public void run() {
        final Map<String, Object> responseMap = new LinkedHashMap<>();
        try {
            if (ConstantConfig.getInstance().isAgentDebug()) {
                LOGGER.info("betby BET_REFUND request: {}", JsonUtils.writeAsJson(paramsMap));
            }

//            final boolean ipBlackList = ScriptLoader.getInstance().functionScript("AgentGameScript",
//                    (IAgentGameScript script) -> script.isIpBlacklist(MsgUtil.getClientIp(session), AgentGame.BETBY.getType()));
//            if (!ipBlackList) {
//                // 其他错误
//                responseMap.put("code", 3000);
//                MsgUtil.responseHttp(responseMap, session);
//                return;
//            }

            // 4. 解析请求参数
            final BetRefundRequest request = parseRequest((String) paramsMap.get("payload"));

            // 5. 验证请求
            ValidationResult validationResult = validateRequest(request);
            if (!validationResult.isSuccess()) {
                BetbyConstants.sendErrorResponse(session, validationResult.getErrorCode(), validationResult.getErrorMessage());
                return;
            }

            Currency currency = BetbyConstants.mapToCurrency(request.transaction.currency);
            // 检查货币是否支持
            if (currency == null) {
                LOGGER.warn("Unsupported currency: {}", request.transaction.currency);
                BetbyConstants.sendErrorResponse(session, 2002, "Unsupported currency: " + request.transaction.currency);
                return;
            }

            // 6. 处理退款逻辑
            processRefund(request, validationResult.getPlayer());
        } catch (Exception e) {
            LOGGER.error("ReqBetRefundHandle error", e);
            BetbyConstants.sendErrorResponse(session, 2004, "Bad request");
        }
    }

    // 8. 解析请求
    private BetRefundRequest parseRequest(String payLoad) {
        try {
            Map<String, Object> requestMap = JwtVerifier.verifyAndDecodeJWT(payLoad);
            if (ConstantConfig.getInstance().isAgentDebug()) {
                LOGGER.info("betby BET_REFUND payload: {}", requestMap);
            }

            @SuppressWarnings("unchecked")
            Map<String, Object> paramsMap = (Map<String, Object>) requestMap.get("payload");

            BetRefundRequest request = new BetRefundRequest();
            request.betTransactionId = (String) paramsMap.get("bet_transaction_id");
            request.reason = (String) paramsMap.get("reason");
            request.bonusId = (String) paramsMap.get("bonus_id");

            // Parse transaction info
            @SuppressWarnings("unchecked")
            Map<String, Object> transactionMap = (Map<String, Object>) paramsMap.get("transaction");
            request.transaction = parseTransactionInfo(transactionMap);

            return request;
        } catch (Exception e) {
            LOGGER.error("Error parsing request: {}", e.getMessage());
            throw e;
        }
    }

    // 9. 解析 Transaction 信息
    private TransactionInfo parseTransactionInfo(Map<String, Object> transactionMap) {
        TransactionInfo transaction = new TransactionInfo();
        transaction.id = (String) transactionMap.get("id");
        transaction.betslipId = (String) transactionMap.get("betslip_id");
        transaction.playerId = (String) transactionMap.get("player_id");
        transaction.operatorId = (String) transactionMap.get("operator_id");
        transaction.operatorBrandId = (String) transactionMap.get("operator_brand_id");
        transaction.extPlayerId = (String) transactionMap.get("ext_player_id");
        transaction.timestamp = (Double) transactionMap.get("timestamp");
        transaction.amount = (Integer) transactionMap.get("amount");
        transaction.currency = (String) transactionMap.get("currency");
        transaction.crossRateEuro = (String) transactionMap.get("cross_rate_euro");
        transaction.operation = (String) transactionMap.get("operation");
        transaction.parentTransactionId = (String) transactionMap.get("parent_transaction_id");

        return transaction;
    }

    // 10. 验证请求
    private ValidationResult validateRequest(BetRefundRequest request) {
        try {
            // 验证bet_transaction_id和其他参数
            if (request.betTransactionId == null || request.transaction == null) {
                return ValidationResult.failure(2004, "Invalid request parameters");
            }

            // 验证玩家是否存在
            final String playerId = BetbyConstants.getPlayerId(request.transaction.extPlayerId);
            final Player player = AgentGameMrg.getInstance().findDbPlayer(Long.parseLong(playerId));
            if (player == null) {
                return ValidationResult.failure(1006, "Player not found");
            }

            // 返回验证结果
            return ValidationResult.success(player);
        } catch (Exception e) {
            LOGGER.error("Validation error", e);
            return ValidationResult.failure(2004, "Validation error");
        }
    }

    // 11. 处理退款
    private void processRefund(BetRefundRequest request, Player player) {
        try {
            // 退款处理逻辑
            GameNote gameNote = AgentGameMrg.getInstance().findRoundIdGameNote(player, request.betTransactionId);
            if (gameNote == null) {
                LOGGER.warn("bet transaction id not found: {}", request.betTransactionId);
                BetbyConstants.sendErrorResponse(session, 2004, "bet transaction id not found");
                return;
            }

            // 更新玩家余额或其他逻辑
            final double realBalance = ScriptLoader.getInstance().functionScript("AgentGameScript",
                    (IAgentGameScript script) -> script.realBalance(player));
            final double balance = ScriptLoader.getInstance().functionScript("AgentGameScript",
                    (IAgentGameScript script) -> script.amountTransformUsd(player, realBalance));

            // 检查是否是重复请求
            if (gameNote.getStatus() == 3) {
//            if (gameNote.getTransactionIds().contains(request.transaction.id)) {
                LOGGER.warn("Duplicate refund transaction detected: {}. Returning idempotent response.", request.transaction.id);

                // 直接构建响应并返回
                final Map<String, Object> responseMap = new LinkedHashMap<>();
                int balanceInCents = (int) Math.round(balance * 100);

                responseMap.put("id", request.transaction.id);
                responseMap.put("ext_transaction_id", request.transaction.id);
                responseMap.put("parent_transaction_id", request.transaction.parentTransactionId);
                responseMap.put("user_id", request.transaction.extPlayerId);
                responseMap.put("operation", "refund");
                responseMap.put("amount", request.transaction.amount);
                responseMap.put("currency", request.transaction.currency);

                responseMap.put("balance", balanceInCents);

                MsgUtil.responseHttp(responseMap, session);
                return;
            }

            double refundAmount = request.transaction.amount / 100.0;  // 将分转为元
            final double finalRefundAmount = ScriptLoader.getInstance().functionScript("AgentGameScript",
                    (IAgentGameScript script) -> script.usdTransformAmount(player, refundAmount));

            ScriptLoader.getInstance().consumerScript("AgentGameScript",
                    (IAgentGameScript script) -> script.updateGameNoteStatus(player, request.transaction.parentTransactionId, 3));

            Map<String, Object> dataMap = new HashMap<>();
            dataMap.put("extPlayerId", request.transaction.extPlayerId);
            dataMap.put("parentTransactionId", request.transaction.parentTransactionId);
            dataMap.put("transactionAmount", request.transaction.amount);
            dataMap.put("transactionId", request.transaction.id);

            String dataJson = JSON.toJSONString(dataMap);

            final NotifyData notifyData = new NotifyData();
            notifyData.setType(ChangeType.refund.getType())
                    .setPid(player.getPlayerId())
                    .setPlayerName(player.getPlayerName())
                    .setNoteId(gameNote.getNoteId() + "")
                    .setCurrency(request.transaction.currency)
                    .setBetAmount(gameNote.getBetAmount())
                    .setWin(gameNote.getBetAmount() - finalRefundAmount)
                    .setData(dataJson)
                    .setUpdSessionId(MsgUtil.getSessionID(session));
            ScriptLoader.getInstance().consumerScript("AgentGameScript",
                    (IAgentGameScript script) -> script.updateCurrency(notifyData));
        } catch (Exception e) {
            LOGGER.error("Refund processing error", e);
            BetbyConstants.sendErrorResponse(session, 2004, "Refund processing failed");
        }
    }
}
