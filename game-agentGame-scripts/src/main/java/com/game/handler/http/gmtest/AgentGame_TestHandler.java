package com.game.handler.http.gmtest;

import com.game.dao.player.PlayerDao;
import com.game.engine.io.handler.HttpHandler;
import com.game.engine.script.IHandlerEntity;
import com.game.engine.utils.JsonUtils;
import com.game.engine.utils.MsgUtil;
import com.game.engine.utils.TimeUtil;
import com.game.entity.player.PlayerFields;
import com.game.gamesr.manager.AgentGameMrg;
import com.game.gamesr.utils.SHAUtils;
import com.game.manager.EntityDaoMrg;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.mongodb.core.query.Update;

import java.net.URI;
import java.net.URLDecoder;
import java.net.http.HttpClient;
import java.net.http.HttpRequest;
import java.net.http.HttpResponse;
import java.nio.charset.StandardCharsets;
import java.time.Duration;
import java.util.Base64;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

//http://127.0.0.1:8880/agentGame/test?game=&gameId=&playerId=
@IHandlerEntity(path = "/agentGame/test", desc = "余额查询")
public class AgentGame_TestHandler extends HttpHandler {
    private static final Logger LOGGER = LoggerFactory.getLogger(AgentGame_TestHandler.class);

    @Override
    public void run() {
        try {
            final String game = (String) paramsMap.get("game");
            final String gameId = (String) paramsMap.get("gameId");
            final String playerId = (String) paramsMap.get("playerId");

            LOGGER.info("params：{}", JsonUtils.writeAsJson(paramsMap));

            final Map<String, Object> paramsMap = new LinkedHashMap<>();
            paramsMap.put("game", game);//游戏代码
            paramsMap.put("player", "PG_200100017");
            paramsMap.put("player_name", "WG200100017");
            paramsMap.put("currency", "brl");
            paramsMap.put("lang", "en");
            paramsMap.put("device", "web");
            paramsMap.put("lobby", "https://www.wingaming.com/");
            paramsMap.put("fun", false);

            final String api_key = "winsaas_stg";
            final String secretKey = "S1WbiBeKhGZhYC6vBFRW";
            final long timestamp = TimeUtil.currentTimeMillis() / 1000;
            final List<Map.Entry<String, Object>> sortMap = AgentGameMrg.sortMap(paramsMap);
            final String formData = AgentGameMrg.formData(sortMap) + ":" + secretKey + "" + timestamp;
            LOGGER.info("formData：{}", formData);
            final String sign = Base64.getEncoder().encodeToString(SHAUtils.hmacSha256(secretKey, formData).getBytes()).replaceAll("=", "");
            LOGGER.info("sign：{}", sign);

            final String uri = "https://rgsservice-stage.ob.games/seamless/game_url";
            final String paramsJson = JsonUtils.writeAsJson(paramsMap);
            final HttpRequest request = HttpRequest.newBuilder()
                    .timeout(Duration.ofSeconds(15))
                    .uri(URI.create(uri))
                    .version(HttpClient.Version.HTTP_1_1)
                    .header("Content-Type", "application/json; charset=utf-8")
                    .header("Signature", api_key + ":" + sign)
                    .header("Timestamp", timestamp + "")
                    .POST(HttpRequest.BodyPublishers.ofString(paramsJson))
                    .build();
            final HttpClient client = HttpClient.newBuilder()
                    .connectTimeout(Duration.ofSeconds(15))
                    .build();
            final HttpResponse<String> httpResponse = client.send(request, HttpResponse.BodyHandlers.ofString());
            LOGGER.info("url：{}，params：{}", uri, paramsJson);

            final String body = httpResponse.body();
            LOGGER.info("response：{}", body);


            final Map<String, Object> responseMap = new LinkedHashMap<>();
            responseMap.put("data", body);
            response(responseMap);

            final Update update = new Update();
            update.set(PlayerFields.gameId, Integer.parseInt(gameId));
//            EntityDaoMrg.getInstance().getDao(PlayerDao.class).updatePlayer(Long.parseLong(playerId), update);
        } catch (Exception e) {
            LOGGER.error("", e);
        }
    }

    public static void main(String[] args) {
        String data = "https://gameproxy-stage.ob.games/launcher?currency=brl&token=npx7.RRKGrY1l906A3rVOdVpPp7fwa7hEU3R_v9n3cS3NSoqQbPzffRjxdFfxA6P40oLP8zjUHaRrk_T34Tqhn9NtNQ&game=gmag-pgsoft_1635221&lang=en&player_name=WG200100018&device=web&lobby=https%3A%2F%2Fwww.wingaming.com%2F";
        System.out.println(URLDecoder.decode(data, StandardCharsets.UTF_8));
    }
}
