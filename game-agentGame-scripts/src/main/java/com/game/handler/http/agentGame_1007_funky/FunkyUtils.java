package com.game.handler.http.agentGame_1007_funky;

import com.game.engine.utils.MsgUtil;
import io.netty.channel.Channel;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.LinkedHashMap;
import java.util.Map;

/**
 * Funky Games API 通用工具类
 */
public class FunkyUtils {
    private static final Logger LOGGER = LoggerFactory.getLogger(FunkyUtils.class);

    /**
     * 验证玩家ID格式是否无效
     * 根据Funky官方文档：至少需要3个字符
     *
     * @param playerId 玩家ID
     * @return 是否为无效的玩家ID
     */
    public static boolean isInvalidPlayerId(String playerId) {
        if (playerId == null || playerId.trim().isEmpty()) {
            return true;
        }

        // 根据官方文档，至少需要3个字符
        if (playerId.trim().length() < 3) {
            return true;
        }

        // 检查是否为有效的数字ID（如果是数字格式）
        try {
            long id = Long.parseLong(playerId.trim());
            return id <= 0;
        } catch (NumberFormatException e) {
            // 如果不是数字，检查是否为有效的字符串ID
            // 只允许字母、数字、下划线、连字符
            return !playerId.trim().matches("^[a-zA-Z0-9_-]+$");
        }
    }

    /**
     * 发送Funky协议的错误响应
     * 统一的1007协议错误响应封装方法，替代BetbyConstants.sendErrorResponse
     *
     * @param session 会话对象
     * @param errorCode Funky错误代码
     */
    public static void sendErrorResponse(Channel session, FunkyConstants.ErrorCode errorCode) {
        Map<String, Object> responseMap = new LinkedHashMap<>();
        responseMap.put("errorCode", errorCode.getCode());
        responseMap.put("errorMessage", errorCode.getMessage());
        responseMap.put("data", null);

        LOGGER.info("sendErrorResponse params: {}", responseMap);

        MsgUtil.responseHttp(responseMap, session);
    }

    /**
     * 发送Funky协议的成功响应
     * 统一的1007协议成功响应封装方法
     *
     * @param session 会话对象
     * @param data 响应数据
     */
    public static void sendSuccessResponse(Channel session, Object data) {
        Map<String, Object> responseMap = new LinkedHashMap<>();
        responseMap.put("errorCode", FunkyConstants.ErrorCode.SUCCESS.getCode());
        responseMap.put("errorMessage", FunkyConstants.ErrorCode.SUCCESS.getMessage());
        responseMap.put("data", data);

        LOGGER.info("sendSuccessResponse params: {}", responseMap);

        MsgUtil.responseHttp(responseMap, session);
    }

    public static void sendSuccessResponse(Channel session, Object data, FunkyConstants.ErrorCode code) {
        Map<String, Object> responseMap = new LinkedHashMap<>();
        responseMap.put("errorCode", code.getCode());
        responseMap.put("errorMessage", code.getMessage());
        responseMap.put("data", data);

        LOGGER.info("sendSuccessResponse params: {}", responseMap);

        MsgUtil.responseHttp(responseMap, session);
    }
}
