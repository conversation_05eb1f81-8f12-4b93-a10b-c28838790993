//package com.game.handler.http.agentGame_1002_st8;
//
//import com.game.c_entity.middleplatform.C_BaseGamePlatform;
//import com.game.engine.io.handler.HttpHandler;
//import com.game.engine.script.IHandlerEntity;
//import com.game.engine.utils.HttpUtils11;
//import com.game.engine.utils.JsonUtils;
//import com.game.engine.utils.MsgUtil;
//import com.game.enums.GameType;
//import com.game.gamesr.main.AgentGameServer;
//import com.game.gamesr.manager.DataAgentGameMrg;
//import com.game.gamesr.utils.SHAUtils;
//import com.game.utils.VirtualThreadUtils;
//import org.slf4j.Logger;
//import org.slf4j.LoggerFactory;
//
//import java.net.URI;
//import java.net.http.HttpClient;
//import java.net.http.HttpRequest;
//import java.net.http.HttpResponse;
//import java.time.Duration;
//import java.util.LinkedHashMap;
//import java.util.Map;
//
//@IHandlerEntity(path = "/st8/bonusOffers", desc = "获取筹码组")
//public class ReqBonusOffersHandler extends HttpHandler {
//    private static final Logger LOGGER = LoggerFactory.getLogger(ReqBonusOffersHandler.class);
//
//    @Override
//    public void run() {
//        VirtualThreadUtils.execute(() -> {
//            LOGGER.info("ReqBonusOffersHandler，params：{}", JsonUtils.writeAsJson(paramsMap));
//            final String platformGameId = (String) paramsMap.get("platformGameId");
//            final int platformId = (int) paramsMap.get("platformId");
//
//            final String data = bonusOffers(platformGameId, platformId);
//
//            final Map<String, Object> paramsMap = new LinkedHashMap<>();
//            paramsMap.put("error", 0);
//            paramsMap.put("data", data);
//            MsgUtil.responseHttp(paramsMap, session);
//        });
//    }
//
//    public String bonusOffers(String platformGameId, int platformId) {
//        try {
//            final C_BaseGamePlatform c_baseGamePlatform = DataAgentGameMrg.getInstance().findC_BaseGamePlatform(this.getClass().getSimpleName(), platformId, GameType.Casino_Slots.getType());
//            if (c_baseGamePlatform == null) {
//                return "";
//            }
//
//            final Map<String, String> paramsMap = new LinkedHashMap<>();
//            paramsMap.put("game_codes", platformGameId);
//
//            final String params = MsgUtil.createGetUrl(paramsMap);
//            LOGGER.warn("body：{}", params);
//
//            final String uri = c_baseGamePlatform.getApiUrl() + "/api/operator/v1/bonus/offers?" + params;
//            LOGGER.warn("url：{}", uri);
//
//            final String sign = SHAUtils.sign(c_baseGamePlatform.getSecretKey(), params);
//            LOGGER.warn("sign：{}", sign);
//
//            final HttpRequest request = HttpRequest.newBuilder()
//                    .timeout(Duration.ofSeconds(15))
//                    .uri(URI.create(uri))
//                    .version(HttpClient.Version.HTTP_1_1)
//                    .header("x-st8-sign", sign)
//                    .header("Content-Type", HttpUtils11.HTTP_CONTENT_TYPE_FORM)
//                    .GET()
//                    .build();
//            final HttpResponse<String> httpResponse = AgentGameServer.getInstance().getHttpClientMrg().send(request, HttpResponse.BodyHandlers.ofString());
//            LOGGER.warn("AgentGameLogic_1002_Script，bonusOffers：{}", httpResponse.body());
//            return httpResponse.body();
//        } catch (Exception e) {
//            LOGGER.error("AgentGameLogic_1002_Script，bonusOffers", e);
//        }
//        return "";
//    }
//}
