package com.game.handler.http.agentGame_1005_betby;

import com.game.engine.io.conf.ConstantConfig;
import com.game.engine.io.handler.HttpHandler;
import com.game.engine.script.IHandlerEntity;
import com.game.engine.utils.JsonUtils;
import com.game.engine.utils.MsgUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.LinkedHashMap;
import java.util.Map;


@IHandlerEntity(path = "/betby/bet/commit", desc = "commit")
public class ReqBetCommitHandler extends HttpHandler {

    private static final Logger LOGGER = LoggerFactory.getLogger(ReqBetDiscardHandler.class);

    @Override
    public void run() {
        final Map<String, Object> responseMap = new LinkedHashMap<>();
        try {
            // 打印请求信息
            Map<String, Object> requestMap = JwtVerifier.verifyAndDecodeJWT((String)paramsMap.get("payload"));
            if (ConstantConfig.getInstance().isAgentDebug()) {
                LOGGER.info("betby BET_COMMIT request: {}", JsonUtils.writeAsJson(paramsMap));
                LOGGER.info("betby BET_COMMIT payload: {}", requestMap);
            }

//            final boolean ipBlackList = ScriptLoader.getInstance().functionScript("AgentGameScript",
//                    (IAgentGameScript script) -> script.isIpBlacklist(MsgUtil.getClientIp(session), AgentGame.BETBY.getType()));
//            if (!ipBlackList) {
//                // 其他错误
//                responseMap.put("code", 3000);
//                MsgUtil.responseHttp(responseMap, session);
//                return;
//            }

            // 构建成功响应
            MsgUtil.responseHttp(responseMap, session);
        } catch (Exception e) {
            LOGGER.error("ReqBetDiscardHandle error", e);
        }
    }
}
