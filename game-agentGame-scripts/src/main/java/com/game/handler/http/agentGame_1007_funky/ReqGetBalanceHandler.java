package com.game.handler.http.agentGame_1007_funky;

import com.alibaba.fastjson.JSONObject;
import com.game.engine.io.conf.ConstantConfig;
import com.game.engine.io.handler.HttpHandler;
import com.game.engine.math.BigDecimalUtils;
import com.game.engine.script.IHandlerEntity;
import com.game.engine.script.ScriptLoader;
import com.game.engine.utils.MsgUtil;
import com.game.entity.player.Player;
import com.game.enums.AgentGame;
import com.game.gamesr.manager.AgentGameMrg;
import com.game.gamesr.scripts.IAgentGameScript;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.LinkedHashMap;
import java.util.Map;

@IHandlerEntity(path = FunkyConstants.Paths.GET_BALANCE, desc = "Funky获取玩家余额")
public class ReqGetBalanceHandler extends HttpHandler {
    private static final Logger LOGGER = LoggerFactory.getLogger(ReqGetBalanceHandler.class);

    @Override
    public void run() {
        try {
            // 获取请求参数（HTTP头信息已自动添加到paramsMap中）
            final Map<String, Object> paramsMap = getParamsMap();

            if (ConstantConfig.getInstance().isAgentDebug()) {
                LOGGER.info("ReqGetBalanceHandler params: {}", paramsMap);
            }

            // 1. 校验请求
            Player player = validateRequest(paramsMap);
            if (player == null) {
                return; // 校验失败，错误响应已发送
            }

            // 2. 处理业务逻辑
            processGetBalance(player);

        } catch (Exception e) {
            LOGGER.error("ReqGetBalanceHandler error", e);
            FunkyUtils.sendErrorResponse(session, FunkyConstants.ErrorCode.INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * 校验请求参数和权限
     *
     * @param paramsMap 请求参数
     * @return 校验通过返回Player对象，失败返回null
     */
    private Player validateRequest(Map<String, Object> paramsMap) {
        // 1. 验证请求头信息
        if (FunkyHeaderValidator.hasInvalidHeaders(paramsMap)) {
            FunkyUtils.sendErrorResponse(session, FunkyConstants.ErrorCode.PLAYER_NOT_LOGIN);
            return null;
        }

        // 2. 获取请求体参数
        final String playerId = (String) paramsMap.get("playerId");
        final String sessionId = (String) paramsMap.get("sessionId");

        // 3. 验证必要参数
        if (playerId == null || playerId.trim().isEmpty()) {
            LOGGER.warn("Missing required playerId parameter");
            FunkyUtils.sendErrorResponse(session, FunkyConstants.ErrorCode.INVALID_INPUT);
            return null;
        }

        if (!FunkyUtils.isValidPlayerId(playerId)) {
            LOGGER.warn("Invalid playerId format: {} (must be at least 3 characters)", playerId);
            FunkyUtils.sendErrorResponse(session, FunkyConstants.ErrorCode.INVALID_INPUT);
            return null;
        }

        if (sessionId == null || sessionId.trim().isEmpty()) {
            LOGGER.warn("Missing required sessionId parameter");
            FunkyUtils.sendErrorResponse(session, FunkyConstants.ErrorCode.INVALID_INPUT);
            return null;
        }

        // 4. 查找玩家
        long playerIdLong;
        try {
            playerIdLong = Long.parseLong(playerId.trim());
        } catch (NumberFormatException e) {
            LOGGER.warn("Invalid playerId format: {}", playerId);
            FunkyUtils.sendErrorResponse(session, FunkyConstants.ErrorCode.INVALID_INPUT);
            return null;
        }

        final Player player = AgentGameMrg.getInstance().findDbPlayer(playerIdLong);
        if (player == null) {
            FunkyUtils.sendErrorResponse(session, FunkyConstants.ErrorCode.PLAYER_NOT_LOGIN);
            return null;
        }

        // 5. IP黑名单检查
//        final boolean ipBlackList = ScriptLoader.getInstance().functionScript("AgentGameScript",
//                (IAgentGameScript script) -> script.isIpBlacklist(MsgUtil.getClientIp(session), AgentGame.FUNKY.getType()));
//        if (!ipBlackList) {
//            FunkyUtils.sendErrorResponse(session, FunkyConstants.ErrorCode.PLAYER_NOT_LOGIN);
//            return null;
//        }

        return player;
    }

    /**
     * 处理获取余额业务逻辑
     *
     * @param player 已验证的玩家对象
     */
    private void processGetBalance(Player player) {
        // 获取玩家余额
        final double realBalance = ScriptLoader.getInstance().functionScript("AgentGameScript",
                (IAgentGameScript script) -> script.realBalance(player));
        final double balance = ScriptLoader.getInstance().functionScript("AgentGameScript",
                (IAgentGameScript script) -> script.amountTransformUsd(player, realBalance));

        // 构建响应数据
        final JSONObject data = new JSONObject();
        data.put("balance", BigDecimalUtils.round(balance, 2));

        // 发送成功响应
        FunkyUtils.sendSuccessResponse(session, data);
    }


}
