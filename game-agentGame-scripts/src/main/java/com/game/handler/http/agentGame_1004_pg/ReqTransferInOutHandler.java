package com.game.handler.http.agentGame_1004_pg;


import com.alibaba.fastjson.JSONObject;
import com.game.c_entity.middleplatform.C_BaseGamePlatform;
import com.game.engine.io.conf.ConstantConfig;
import com.game.engine.io.handler.HttpHandler;
import com.game.engine.math.BigDecimalUtils;
import com.game.engine.script.IHandlerEntity;
import com.game.engine.script.ScriptLoader;
import com.game.engine.utils.JsonUtils;
import com.game.engine.utils.MsgUtil;
import com.game.entity.game.GameNote;
import com.game.entity.player.FreeGameInfo;
import com.game.entity.player.GameInfo;
import com.game.entity.player.Player;
import com.game.enums.AgentGame;
import com.game.enums.ChangeType;
import com.game.gamesr.NotifyData;
import com.game.gamesr.manager.AgentGameMrg;
import com.game.gamesr.manager.DataAgentGameMrg;
import com.game.gamesr.scripts.IAgentGameScript;
import com.game.gamesr.utils.JWTUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.LinkedHashMap;
import java.util.Map;
import java.util.Objects;

//http://127.0.0.1:8680/pg/Cash/TransferInOut
@IHandlerEntity(path = "/pg/Cash/TransferInOut", desc = "下注并派彩")
public class ReqTransferInOutHandler extends HttpHandler {
    private static final Logger LOGGER = LoggerFactory.getLogger(ReqTransferInOutHandler.class);

    @Override
    public void run() {
        final Map<String, Object> responseMap = new LinkedHashMap<>();
        try {
            if (ConstantConfig.getInstance().isAgentDebug()) {
                LOGGER.info("ReqTransferInOutHandler，params：{}", JsonUtils.writeAsJson(paramsMap));
            }

            final String operator_token = (String) paramsMap.get("operator_token");
            final String secret_key = (String) paramsMap.get("secret_key");
            final String operator_player_session = (String) paramsMap.get("operator_player_session");
            String transaction_id = (String) paramsMap.get("transaction_id");
            final String parent_bet_id = (String) paramsMap.get("parent_bet_id");
            final String bet_id = (String) paramsMap.get("bet_id");

            final String is_minus_count = (String) paramsMap.get("is_minus_count");
            final String updated_time = (String) paramsMap.get("updated_time");

            final String player_name = (String) paramsMap.get("player_name");
            final String currency_code = (String) paramsMap.get("currency_code");

            final String bet_amount = (String) paramsMap.get("bet_amount");
            final String win_amount = (String) paramsMap.get("win_amount");
            final String transfer_amount = (String) paramsMap.get("transfer_amount");
            final String real_transfer_amount = (String) paramsMap.get("real_transfer_amount");

            final String is_validate_bet = (String) paramsMap.get("is_validate_bet");
            final String is_adjustment = (String) paramsMap.get("is_adjustment");

            if (Double.parseDouble(transfer_amount) != Double.parseDouble(real_transfer_amount)) {
                final JSONObject error = new JSONObject();
                error.put("code", "3107");
                error.put("message", "Invalid configuration");

                responseMap.put("data", null);
                responseMap.put("error", error);
                MsgUtil.responseHttp(responseMap, session);
                return;
            }

            if (BigDecimalUtils.sub(Double.parseDouble(win_amount), Double.parseDouble(bet_amount), 2)
                    != Double.parseDouble(transfer_amount)) {
                final JSONObject error = new JSONObject();
                error.put("code", "3073");
                error.put("message", "BetFailedException");

                responseMap.put("data", null);
                responseMap.put("error", error);
                MsgUtil.responseHttp(responseMap, session);
                return;
            }

            if (!Boolean.parseBoolean(is_validate_bet) && !Boolean.parseBoolean(is_adjustment)) {
                final Map<String, Object> jwtMap = JWTUtils.verify(operator_player_session);
                if (jwtMap.isEmpty() || !Objects.isNull(jwtMap.get("error"))) {
                    final JSONObject error = new JSONObject();
                    error.put("code", "1034");
                    error.put("message", "Invalid request");

                    responseMap.put("data", null);
                    responseMap.put("error", error);
                    MsgUtil.responseHttp(responseMap, session);
                    return;
                }

                final String playerName = (String) jwtMap.get("playerName");
                if (!Objects.equals(player_name, playerName)) {
                    final JSONObject error = new JSONObject();
                    error.put("code", "1034");
                    error.put("message", "Invalid request");

                    responseMap.put("data", null);
                    responseMap.put("error", error);
                    MsgUtil.responseHttp(responseMap, session);
                    return;
                }

                final String currency = (String) jwtMap.get("currency");
                if (!Objects.equals(currency, currency_code)) {
                    final JSONObject error = new JSONObject();
                    error.put("code", "1034");
                    error.put("message", "Invalid request");

                    responseMap.put("data", null);
                    responseMap.put("error", error);
                    MsgUtil.responseHttp(responseMap, session);
                    return;
                }
            }

            final long playerId = AgentGameMrg.getInstance().getAgentGamePlayerId(player_name);
            final Player player = AgentGameMrg.getInstance().findDbPlayer(playerId);
            if (player == null) {
                final JSONObject error = new JSONObject();
                error.put("code", "3004");
                error.put("message", "Player does not exist");

                responseMap.put("data", null);
                responseMap.put("error", error);
                MsgUtil.responseHttp(responseMap, session);
                return;
            }

            final boolean ipBlackList = ScriptLoader.getInstance().functionScript("AgentGameScript",
                    (IAgentGameScript script) -> script.isIpBlacklist(MsgUtil.getClientIp(session), AgentGame.PG.getType()));
            if (!ipBlackList) {
                final JSONObject error = new JSONObject();
                error.put("code", "3004");
                error.put("message", "Player does not exist");

                responseMap.put("data", null);
                responseMap.put("error", error);
                MsgUtil.responseHttp(responseMap, session);
                return;
            }

            final C_BaseGamePlatform c_baseGamePlatform = DataAgentGameMrg.getInstance()
                    .findC_BaseGamePlatformSupplierId(this.getClass().getSimpleName(), AgentGame.PG.getType());
            if (c_baseGamePlatform == null) {
                final JSONObject error = new JSONObject();
                error.put("code", "1034");
                error.put("message", "Invalid request");

                responseMap.put("data", null);
                responseMap.put("error", error);
                MsgUtil.responseHttp(responseMap, session);
                return;
            }

            if (!Objects.equals(c_baseGamePlatform.getToken(), operator_token)) {
                final JSONObject error = new JSONObject();
                error.put("code", "1034");
                error.put("message", "Invalid request");

                responseMap.put("data", null);
                responseMap.put("error", error);
                MsgUtil.responseHttp(responseMap, session);
                return;
            }

            if (!Objects.equals(c_baseGamePlatform.getSecretKey(), secret_key)) {
                final JSONObject error = new JSONObject();
                error.put("code", "1034");
                error.put("message", "Invalid request");

                responseMap.put("data", null);
                responseMap.put("error", error);
                MsgUtil.responseHttp(responseMap, session);
                return;
            }

            final double realBalance = ScriptLoader.getInstance().functionScript("AgentGameScript",
                    (IAgentGameScript script) -> script.realBalance(player));

            double balance = ScriptLoader.getInstance().functionScript("AgentGameScript",
                    (IAgentGameScript script) -> script.amountTransformUsd(player, realBalance));

            if (Double.parseDouble(bet_amount) > balance) {
                final JSONObject error = new JSONObject();
                error.put("code", "3202");
                error.put("message", "Insufficient player balance");

                responseMap.put("data", null);
                responseMap.put("error", error);
                MsgUtil.responseHttp(responseMap, session);
                return;
            }

            balance = BigDecimalUtils.add(balance, Double.parseDouble(transfer_amount), 2);
            if (balance < 0) {
                final JSONObject error = new JSONObject();
                error.put("code", "3202");
                error.put("message", "Insufficient player balance");

                responseMap.put("data", null);
                responseMap.put("error", error);
                MsgUtil.responseHttp(responseMap, session);
                return;
            }

            if (Double.parseDouble(bet_amount) == 0) {
                //1878643662938833920-1878643662938833920-106-0
                final String[] id = transaction_id.split("-");
                transaction_id = parent_bet_id + "-" + parent_bet_id + "-" + id[2] + "-" + id[3];
            }

            GameNote gameNote = AgentGameMrg.getInstance().findRoundIdGameNote(player, transaction_id);
            if (gameNote != null && gameNote.getTransactionIds().contains(bet_id)) {
                LOGGER.warn("bet_id：{}，is exist", bet_id);

                final JSONObject data = new JSONObject();
                data.put("currency_code", currency_code);
                data.put("balance_amount", BigDecimalUtils.round(gameNote.getBalance(), 2));
                data.put("updated_time", Long.parseLong(updated_time));
                data.put("real_transfer_amount", Double.parseDouble(real_transfer_amount));

                responseMap.put("data", data);
                responseMap.put("error", null);
                MsgUtil.responseHttp(responseMap, session);
                return;
            }

//            final JSONObject data = new JSONObject();
//            data.put("currency_code", currency_code);
//            data.put("balance_amount", BigDecimalUtils.round(balance, 2));
//            data.put("updated_time", updated_time);
//            data.put("real_transfer_amount", real_transfer_amount);
//
//            responseMap.put("data", data);
//            responseMap.put("error", null);
//            MsgUtil.responseHttp(responseMap, session);

            final double betAmount = ScriptLoader.getInstance().functionScript("AgentGameScript",
                    (IAgentGameScript script) -> script.usdTransformAmount(player, Double.parseDouble(bet_amount)));

            final double win = ScriptLoader.getInstance().functionScript("AgentGameScript",
                    (IAgentGameScript script) -> script.usdTransformAmount(player, Double.parseDouble(win_amount)));

            final double finalBalance = balance;
            final double balances = ScriptLoader.getInstance().functionScript("AgentGameScript",
                    (IAgentGameScript script) -> script.usdTransformAmount(player, finalBalance));

            final String finalTransaction_id = transaction_id;
            if (Boolean.parseBoolean(is_minus_count)) {//免费
                if (win > 0) {
                    ScriptLoader.getInstance().consumerScript("AgentGameScript",
                            (IAgentGameScript script) -> script.freeAddGameNote(player, finalTransaction_id, bet_id, 2, win, balances));
                }

                final FreeGameInfo freeGameInfo = player.getFreeGameInfo(player.getPlayerCurrencyId());
                final GameInfo gameInfo = freeGameInfo.getFreeGame(player.getGameId());

                final NotifyData notifyData = new NotifyData();
                notifyData.setType(ChangeType.Free.getType())
                        .setPid(playerId)
                        .setWin(win)
                        .setFreeTimes(-gameInfo.getFreeTimes())
                        .setUpdated_time(Long.parseLong(updated_time))
                        .setReal_transfer_amount(Double.parseDouble(real_transfer_amount))
                        .setUpdSessionId(MsgUtil.getSessionID(session))
                        .setGameId(player.getGameId())
                        .setCurrencyId(player.getPlayerCurrencyId());

                ScriptLoader.getInstance().consumerScript("AgentGameScript",
                        (IAgentGameScript script) -> script.updateCurrency(notifyData));

            } else {
                int type = 0;
                if (gameNote == null) {
                    gameNote = ScriptLoader.getInstance().functionScript("AgentGameScript",
                            (IAgentGameScript script) -> script.addGameNote(player, finalTransaction_id, bet_id, 2, betAmount, betAmount, win, balances));
                    type = ChangeType.special.getType();
                } else {
                    gameNote = ScriptLoader.getInstance().functionScript("AgentGameScript",
                            (IAgentGameScript script) -> script.updateGameNote(player, finalTransaction_id, bet_id, 2, win, balances));
                    type = ChangeType.win.getType();
                }
                final NotifyData notifyData = new NotifyData();
                notifyData.setType(type)
                        .setPid(playerId)
                        .setNoteId(gameNote.getNoteId() + "")
                        .setBetAmount(betAmount)
                        .setWin(win)
                        .setTotalWin(gameNote.getWin())
                        .setCurrency(currency_code)
                        .setUpdated_time(Long.parseLong(updated_time))
                        .setReal_transfer_amount(Double.parseDouble(real_transfer_amount))
                        .setUpdSessionId(MsgUtil.getSessionID(session))
                        .setGameId(gameNote.getGameId())
                        .setCurrencyId(gameNote.getCurrencyId());

                ScriptLoader.getInstance().consumerScript("AgentGameScript",
                        (IAgentGameScript script) -> script.updateCurrency(notifyData));
            }

        } catch (Exception e) {
            LOGGER.error("ReqTransferInOutHandler", e);
            final JSONObject error = new JSONObject();
            error.put("code", "1034");
            error.put("message", "Invalid request");

            responseMap.put("data", null);
            responseMap.put("error", error);
            MsgUtil.responseHttp(responseMap, session);
        }
    }

}
