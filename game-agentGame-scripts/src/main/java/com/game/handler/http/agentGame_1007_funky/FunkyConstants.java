package com.game.handler.http.agentGame_1007_funky;

/**
 * Funky Games API 常量定义
 * 包含错误代码、认证信息等常量
 */
public class FunkyConstants {

    /**
     * Funky API 错误代码枚举
     * 基于官方文档定义
     */
    public enum ErrorCode {
        SUCCESS(0, "Success"),                                    // 成功
        INVALID_INPUT(400, "Invalid Input"),                      // 无效输入
        PLAYER_NOT_LOGIN(401, "Player Not Login"),               // 玩家未登录
        INSUFFICIENT_BALANCE(402, "Insufficient Balance"),        // 余额不足
        BET_ALREADY_EXISTS(403, "Bet already exists"),           // 投注已存在
        BET_WAS_NOT_FOUND(404, "Bet Was Not Found"),            // 投注未找到
        API_SUSPENDED(405, "Api Suspended"),                     // API暂停
        OVER_MAX_WINNING(406, "Over Max Winning"),               // 超过最大赢取
        OVER_MAX_LOSE(407, "Over Max Lose"),                     // 超过最大损失
        BET_ALREADY_SETTLED(409, "Bet Already Settled"),         // 投注已结算
        BET_ALREADY_CANCELLED(410, "Bet Already Cancelled"),     // 投注已取消
        VOUCHER_ALREADY_EXISTS(601, "Voucher Already Exists"),   // 凭证已存在
        VOUCHER_INVALID(602, "VoucherInvalid"),                  // 凭证无效
        VOUCHER_IS_EXHAUSTED(603, "VoucherIsExhausted"),         // 凭证已用尽
        VOUCHER_OPERATION_FAILED(604, "VoucherOperationFailed"), // 凭证操作失败
        REPORT_INVALID_INPUT(3002, "Report Invalid Input"),      // 报告无效输入
        REPORT_PAGE_NOT_FOUND(3003, "Report Page Not Found"),    // 报告页面未找到
        REPORT_GAMECODE_NOT_FOUND(3004, "Report GameCode Not Found"), // 报告游戏代码未找到
        GAMECODE_NOT_ALLOWED(10005, "GameCode is not allowed"),  // 游戏代码不允许
        INTERNAL_SERVER_ERROR(9999, "Internal Server Error");    // 内部服务器错误

        private final int code;
        private final String message;

        ErrorCode(int code, String message) {
            this.code = code;
            this.message = message;
        }

        public int getCode() {
            return code;
        }

        public String getMessage() {
            return message;
        }

    }

    /**
     * Funky API 路径常量
     */
    public static class Paths {
        // 注意：实际部署时需要替换 {operatorUrl} 为具体的运营商URL
        public static final String GET_BALANCE = "/Funky/User/GetBalance";
        public static final String PLACE_BET = "/Funky/Bet/PlaceBet";
        public static final String CHECK_BET = "/Funky/Bet/CheckBet";
        public static final String SETTLE_BET = "/Funky/Bet/SettleBet";
        public static final String CANCEL_BET = "/Funky/Bet/CancelBet";
    }
}
