//package com.game.handler.http.agentGame_1000;
//
//import com.game.engine.io.handler.HttpHandler;
//import com.game.engine.script.IHandlerEntity;
//import com.game.engine.script.ScriptLoader;
//import com.game.engine.utils.JsonUtils;
//import com.game.engine.utils.MsgUtil;
//import com.game.gamesr.NotifyData;
//import com.game.gamesr.manager.AgentGameMrg;
//import com.game.gamesr.scripts.IAgentGameScript;
//import com.game.scripts.CurrencyScript;
//import io.netty.util.internal.StringUtil;
//import org.slf4j.Logger;
//import org.slf4j.LoggerFactory;
//
//import java.util.LinkedHashMap;
//import java.util.Map;
//import java.util.Objects;
//
////http://127.0.0.1:8680/seamless/refund
//@IHandlerEntity(path = "/seamless/refund", desc = "退款")
//public class ReqSeamlessRefundHandler extends HttpHandler {
//    private static final Logger LOGGER = LoggerFactory.getLogger(ReqSeamlessRefundHandler.class);
//
//    @Override
//    public void run() {
//        try {
//            final String transaction_id = (String) paramsMap.get("transaction_id");//交易编号
//            final String round = (String) paramsMap.get("round");//下注编码
//            final String parent_round = (String) paramsMap.get("parent_round");//上游回合单号
//            final String game = (String) paramsMap.get("game");//游戏代码
//            final String player = (String) paramsMap.get("player");//玩家识别码
//            final String currency = (String) paramsMap.get("currency");//货币识别码
//            final String amount = (String) paramsMap.get("amount");//下注金额
//
//            final Map<String, Object> responseMap = new LinkedHashMap<>();
//            if (StringUtil.isNullOrEmpty(player) || StringUtil.isNullOrEmpty(currency)
//                    || StringUtil.isNullOrEmpty(amount)) {
//                responseMap.put("message", "INVALID_ARGUMENT");
//                MsgUtil.responseHttp(responseMap, session);
//                return;
//            }
//
//            final int currencyId = Objects.requireNonNull(CurrencyScript.Currency.valueOfs(currency)).getCurrencyId();
//            final long playerId = AgentGameMrg.getInstance().getAgentGamePlayerId(player);
//
//            final NotifyData notifyData = new NotifyData();
//            notifyData.setPid(playerId)
//                    .setCurrencyId(currencyId)
//                    .setType(3)
//                    .setAmount(Double.parseDouble(amount))
//                    .setData(JsonUtils.writeAsJson(paramsMap))
//                    .setUdpSessionId(MsgUtil.getSessionID(session));
//            ScriptLoader.getInstance().consumerScript("AgentGameScript", (IAgentGameScript script) -> script.updateCurrency(notifyData));
//        } catch (Exception e) {
//            LOGGER.error("ReqSeamlessWinHandler", e);
//        }
//    }
//
//}
