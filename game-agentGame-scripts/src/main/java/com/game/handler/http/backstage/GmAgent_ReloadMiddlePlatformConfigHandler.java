package com.game.handler.http.backstage;


import com.game.engine.io.handler.HttpHandler;
import com.game.engine.script.IHandlerEntity;
import com.game.engine.utils.MsgUtil;
import com.game.enums.ErrorCode;
import com.game.enums.MiddleConfigType;
import com.game.gamesr.manager.DataAgentGameMrg;
import io.netty.util.internal.StringUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;


//http://127.0.0.1:8880/gmAgent/middlePlatformReloadConfig?reloadType=101
@IHandlerEntity(path = "/gmAgent/middlePlatformReloadConfig", desc = "重新加载中台配置")
public class GmAgent_ReloadMiddlePlatformConfigHandler extends HttpHandler {
    private static final Logger LOGGER = LoggerFactory.getLogger(GmAgent_ReloadMiddlePlatformConfigHandler.class);

    @Override
    public void run() {
        try {
            final String reloadType = (String) paramsMap.get("reloadType");

            if (StringUtil.isNullOrEmpty(reloadType)) {
                MsgUtil.responseHttp(ErrorCode.Parameter_Error.getCode(), session);
                return;
            }

            switch (MiddleConfigType.valueOf(Integer.parseInt(reloadType))) {
                case GamePlatform:
                    DataAgentGameMrg.getInstance().loadBaseGamePlatform();
                    break;
                case ExchangeRate:
                    DataAgentGameMrg.getInstance().loadBaseExchangeRate();
                    break;
                default:
                    throw new IllegalArgumentException(reloadType + "，not exist");
            }

            LOGGER.info("reloadMiddlePlatformConfig，reloadType：{}， success ...", reloadType);
            MsgUtil.responseHttp(ErrorCode.Success.getCode(), session);
        } catch (Exception e) {
            LOGGER.error("", e);
            MsgUtil.responseHttp(ErrorCode.Internal_Server_Error.getCode(), session);
        }
    }
}
