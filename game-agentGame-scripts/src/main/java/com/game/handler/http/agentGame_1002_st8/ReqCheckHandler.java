//package com.game.handler.http.agentGame_1002;
//
//import com.alibaba.fastjson.JSONObject;
//import com.game.engine.io.handler.HttpHandler;
//import com.game.engine.script.IHandlerEntity;
//import com.game.engine.script.ScriptLoader;
//import com.game.engine.utils.JsonUtils;
//import com.game.engine.utils.MsgUtil;
//import com.game.entity.player.Player;
//import com.game.gamesr.manager.AgentGameMrg;
//import com.game.gamesr.scripts.IAgentGameScript;
//import com.game.gamesr.utils.JWTUtils;
//import org.slf4j.Logger;
//import org.slf4j.LoggerFactory;
//
//import java.util.LinkedHashMap;
//import java.util.Map;
//import java.util.Objects;
//
////http://127.0.0.1:8680/st8/check
//@IHandlerEntity(path = "/st8/check", desc = "检查")
//public class ReqCheckHandler extends HttpHandler {
//    private static final Logger LOGGER = LoggerFactory.getLogger(ReqCheckHandler.class);
//
//    @Override
//    public void run() {
//        final Map<String, Object> responseMap = new LinkedHashMap<>();
//        try {
//            LOGGER.info("ReqCheckHandler，params：{}", JsonUtils.writeAsJson(paramsMap));
//            final String token = (String) paramsMap.get("token");
//
//            final Map<String, Object> jwtMap = JWTUtils.verify(token);
//            final long playerId = (long) jwtMap.get("userId");
//            final String currency = (String) jwtMap.get("currency");
//            final String platformGameId = (String) jwtMap.get("platformGameId");
//            final String platformName = (String) jwtMap.get("platformName");
//
//            if (jwtMap.isEmpty() || !Objects.isNull(jwtMap.get("error"))) {
//                responseMap.put("status", "session_expired");
//                MsgUtil.responseHttp(responseMap, session);
//                return;
//            }
//
//            final Player player = AgentGameMrg.getInstance().findDbPlayer(playerId);
//            if (player == null) {
//                responseMap.put("status", "player_not_found");
//                MsgUtil.responseHttp(responseMap, session);
//                return;
//            }
//
//            final boolean isIpBlacklist = ScriptLoader.getInstance().functionScript("AgentGameScript", (IAgentGameScript script) -> script.isIpBlacklist(player, MsgUtil.getClientIp(session)));
//            if (!isIpBlacklist) {
//                responseMap.put("status", "unknown");
//                MsgUtil.responseHttp(responseMap, session);
//                return;
//            }
//
//            final JSONObject site = new JSONObject();
//            site.put("id", "cashcat.staging");
//            site.put("lobby", "https://example.cashcat.club/");
//
//            responseMap.put("status", "ok");
//            responseMap.put("token", token);
//            responseMap.put("currency", currency);
//            responseMap.put("game_code", platformGameId);
//            responseMap.put("country", "BR");
//            responseMap.put("player", AgentGameMrg.getPlatformUserName(platformName, playerId));
//            responseMap.put("site", site.toJSONString());
//            MsgUtil.responseHttp(responseMap, session);
//        } catch (Exception e) {
//            LOGGER.error("ReqCheckHandler", e);
//            responseMap.put("status", "unknown");
//            MsgUtil.responseHttp(responseMap, session);
//        }
//    }
//
//}
