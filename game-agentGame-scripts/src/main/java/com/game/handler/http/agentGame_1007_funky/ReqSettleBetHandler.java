package com.game.handler.http.agentGame_1007_funky;

import com.alibaba.fastjson.JSONObject;
import com.game.engine.io.conf.ConstantConfig;
import com.game.engine.io.handler.HttpHandler;
import com.game.engine.math.BigDecimalUtils;
import com.game.engine.script.IHandlerEntity;
import com.game.engine.script.ScriptLoader;
import com.game.engine.utils.MsgUtil;
import com.game.entity.game.GameNote;
import com.game.entity.player.Player;
import com.game.enums.AgentGame;
import com.game.enums.ChangeType;

import com.game.enums.Currency;
import com.game.gamesr.NotifyData;
import com.game.gamesr.manager.AgentGameMrg;
import com.game.gamesr.scripts.IAgentGameScript;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.LinkedHashMap;
import java.util.Map;
import java.util.TimeZone;

@IHandlerEntity(path = FunkyConstants.Paths.SETTLE_BET, desc = "Funky结算下注接口")
public class ReqSettleBetHandler extends HttpHandler {
    private static final Logger LOGGER = LoggerFactory.getLogger(ReqSettleBetHandler.class);

    // 日期格式化器
    private static final SimpleDateFormat DATE_FORMAT = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss'Z'");

    static {
        DATE_FORMAT.setTimeZone(TimeZone.getTimeZone("UTC"));
    }

    /**
     * 结算下注请求数据
     */
    private static class SettleBetRequest {
        Player player;
        String refNo;
        String playerId;
        String gameCode;
        double stake;
        double winAmount;
        double effectiveStake;

        SettleBetRequest(Player player, String refNo, String playerId, String gameCode,
                        double stake, double winAmount, double effectiveStake) {
            this.player = player;
            this.refNo = refNo;
            this.playerId = playerId;
            this.gameCode = gameCode;
            this.stake = stake;
            this.winAmount = winAmount;
            this.effectiveStake = effectiveStake;
        }
    }

    @Override
    public void run() {
        try {
            // 获取请求参数
            final Map<String, Object> paramsMap = getParamsMap();
            if (ConstantConfig.getInstance().isAgentDebug()) {
                LOGGER.info("ReqSettleBetHandler params: {}", paramsMap);
            }

            // 1. 校验请求
            SettleBetRequest request = validateRequest(paramsMap);
            if (request == null) {
                return; // 校验失败，错误响应已发送
            }

            // 2. 处理业务逻辑
            processSettleBet(request);

        } catch (Exception e) {
            LOGGER.error("ReqSettleBetHandler error", e);
            FunkyUtils.sendErrorResponse(session, FunkyConstants.ErrorCode.INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * 校验请求参数和权限
     *
     * @param paramsMap 请求参数
     * @return 校验通过返回SettleBetRequest对象，失败返回null
     */
    private SettleBetRequest validateRequest(Map<String, Object> paramsMap) {
        // 1. 验证请求头信息
        if (FunkyHeaderValidator.hasInvalidHeaders(paramsMap)) {
            FunkyUtils.sendErrorResponse(session, FunkyConstants.ErrorCode.PLAYER_NOT_LOGIN);
            return null;
        }

        // 2. 获取请求体参数
        final String refNo = (String) paramsMap.get("refNo");

        // 获取betResultReq对象
        @SuppressWarnings("unchecked")
        final Map<String, Object> betResultReq = (Map<String, Object>) paramsMap.get("betResultReq");
        if (betResultReq == null) {
            LOGGER.warn("Missing required betResultReq parameter");
            FunkyUtils.sendErrorResponse(session, FunkyConstants.ErrorCode.INVALID_INPUT);
            return null;
        }

        final String playerId = (String) betResultReq.get("playerId");
        final String gameCode = (String) betResultReq.get("gameCode");
        final Object stakeObj = betResultReq.get("stake");
        final Object winAmountObj = betResultReq.get("winAmount");
        final Object effectiveStakeObj = betResultReq.get("effectiveStake");

        // 3. 验证必要参数
        if (refNo == null || refNo.trim().isEmpty()) {
            LOGGER.warn("Missing required refNo parameter");
            FunkyUtils.sendErrorResponse(session, FunkyConstants.ErrorCode.INVALID_INPUT);
            return null;
        }

        if (playerId == null || playerId.trim().isEmpty()) {
            LOGGER.warn("Missing required playerId parameter");
            FunkyUtils.sendErrorResponse(session, FunkyConstants.ErrorCode.INVALID_INPUT);
            return null;
        }

        if (!FunkyUtils.isValidPlayerId(playerId)) {
            LOGGER.warn("Invalid playerId format: {} (must be at least 3 characters)", playerId);
            FunkyUtils.sendErrorResponse(session, FunkyConstants.ErrorCode.INVALID_INPUT);
            return null;
        }

        // 4. 验证金额参数
        double stake, winAmount, effectiveStake;
        try {
            stake = parseAmount(stakeObj, "stake");
            winAmount = parseAmount(winAmountObj, "winAmount");
            effectiveStake = parseAmount(effectiveStakeObj, "effectiveStake");
        } catch (NumberFormatException e) {
            LOGGER.warn("Invalid amount format: {}", e.getMessage());
            FunkyUtils.sendErrorResponse(session, FunkyConstants.ErrorCode.INVALID_INPUT);
            return null;
        }

        // 5. 查找玩家
        long playerIdLong;
        try {
            playerIdLong = Long.parseLong(playerId.trim());
        } catch (NumberFormatException e) {
            LOGGER.warn("Invalid playerId format: {}", playerId);
            FunkyUtils.sendErrorResponse(session, FunkyConstants.ErrorCode.INVALID_INPUT);
            return null;
        }

        final Player player = AgentGameMrg.getInstance().findDbPlayer(playerIdLong);
        if (player == null) {
            FunkyUtils.sendErrorResponse(session, FunkyConstants.ErrorCode.PLAYER_NOT_LOGIN);
            return null;
        }

        // 6. IP黑名单检查
//        final boolean ipBlackList = ScriptLoader.getInstance().functionScript("AgentGameScript",
//                (IAgentGameScript script) -> script.isIpBlacklist(MsgUtil.getClientIp(session), AgentGame.FUNKY.getType()));
//        if (!ipBlackList) {
//            FunkyUtils.sendErrorResponse(session, FunkyConstants.ErrorCode.PLAYER_NOT_LOGIN);
//            return null;
//        }

        return new SettleBetRequest(player, refNo, playerId, gameCode, stake, winAmount, effectiveStake);
    }

    /**
     * 处理结算下注业务逻辑
     *
     * @param request 已验证的结算请求
     */
    private void processSettleBet(SettleBetRequest request) {
        // 1. 查找下注记录
        GameNote gameNote = AgentGameMrg.getInstance().findRoundIdGameNote(request.player, request.refNo);
        if (gameNote == null) {
            LOGGER.warn("Bet not found: refNo={}, playerId={}", request.refNo, request.playerId);
            FunkyUtils.sendErrorResponse(session, FunkyConstants.ErrorCode.BET_WAS_NOT_FOUND);
            return;
        }

        // 2. 检查下注是否已经结算
        if (gameNote.getStatus() == 2) { // 假设2表示已结算
            LOGGER.warn("Bet already settled: refNo={}, playerId={}", request.refNo, request.playerId);
            FunkyUtils.sendErrorResponse(session, FunkyConstants.ErrorCode.BET_ALREADY_SETTLED);
            return;
        }

        // 3. 转换金额为实际金额
        final double winValue = ScriptLoader.getInstance().functionScript("AgentGameScript",
                (IAgentGameScript script) -> script.usdTransformAmount(request.player, request.winAmount));

        // 4. 获取当前余额
        final double realBalance = ScriptLoader.getInstance().functionScript("AgentGameScript",
                (IAgentGameScript script) -> script.realBalance(request.player));
        final double currentBalance = ScriptLoader.getInstance().functionScript("AgentGameScript",
                (IAgentGameScript script) -> script.amountTransformUsd(request.player, realBalance));

        // 5. 计算结算后余额
        final double finalBalance = BigDecimalUtils.add(currentBalance, request.winAmount, 2);
        final double balances = ScriptLoader.getInstance().functionScript("AgentGameScript",
                (IAgentGameScript script) -> script.usdTransformAmount(request.player, finalBalance));

        // 6. 更新游戏记录
        // todo 有问题的
        gameNote = ScriptLoader.getInstance().functionScript("AgentGameScript",
                (IAgentGameScript script) -> script.updateGameNote(request.player, request.refNo, request.refNo, 2, winValue, realBalance));

        // 获取货币名称
        Currency currency = Currency.valueOf(gameNote.getCurrencyId());
        String currencyName = "";
        if (currency != null) {
            currencyName = currency.name();
        }

        // 7. 通知余额变更
        NotifyData notifyData = new NotifyData();
        notifyData.setType(ChangeType.win.getType())
                .setPid(request.player.getPlayerId())
                .setPlayerName(request.player.getPlayerName())
                .setNoteId(gameNote.getNoteId() + "")//request.refNo
                .setBetAmount(gameNote.getBetAmount())
                .setValidBets(gameNote.getValidBets())
                .setWin(winValue)
                .setTotalWin(gameNote.getWin())
                .setCurrency(currencyName)
                .setUpdSessionId(MsgUtil.getSessionID(session));

        ScriptLoader.getInstance().consumerScript("AgentGameScript",
                (IAgentGameScript script) -> script.updateCurrency(notifyData));

        // 8. 构建成功响应
        final JSONObject data = new JSONObject();
        data.put("refNo", request.refNo);
        data.put("balance", BigDecimalUtils.round(finalBalance, 2));
        data.put("playerId", String.valueOf(request.player.getPlayerId()));
        data.put("currency", currencyName);

        // 设置结算日期
        long timestamp = gameNote.getCreateTime();
        data.put("statementDate", DATE_FORMAT.format(new Date(timestamp)));

        // 发送成功响应
        FunkyUtils.sendSuccessResponse(session, data);
    }

    /**
     * 解析金额参数
     */
    private double parseAmount(Object amountObj, String fieldName) throws NumberFormatException {
        if (amountObj == null) {
            throw new NumberFormatException(fieldName + " is required");
        }

        double amount;
        if (amountObj instanceof Number) {
            amount = ((Number) amountObj).doubleValue();
        } else if (amountObj instanceof String) {
            amount = Double.parseDouble((String) amountObj);
        } else {
            throw new NumberFormatException("Invalid " + fieldName + " format");
        }

        if (amount < 0) {
            throw new NumberFormatException(fieldName + " cannot be negative");
        }

        return amount;
    }
}
