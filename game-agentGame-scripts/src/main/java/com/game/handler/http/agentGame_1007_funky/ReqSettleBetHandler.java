package com.game.handler.http.agentGame_1007_funky;

import com.alibaba.fastjson.JSONObject;
import com.game.engine.io.conf.ConstantConfig;
import com.game.engine.io.handler.HttpHandler;
import com.game.engine.math.BigDecimalUtils;
import com.game.engine.script.IHandlerEntity;
import com.game.engine.script.ScriptLoader;
import com.game.engine.utils.MsgUtil;
import com.game.entity.game.GameNote;
import com.game.entity.player.Player;
import com.game.enums.AgentGame;
import com.game.enums.ChangeType;

import com.game.enums.Currency;
import com.game.gamesr.NotifyData;
import com.game.gamesr.manager.AgentGameMrg;
import com.game.gamesr.scripts.IAgentGameScript;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.LinkedHashMap;
import java.util.Map;
import java.util.TimeZone;

@IHandlerEntity(path = FunkyConstants.Paths.SETTLE_BET, desc = "Funky结算下注接口")
public class ReqSettleBetHandler extends HttpHandler {
    private static final Logger LOGGER = LoggerFactory.getLogger(ReqSettleBetHandler.class);

    // 日期格式化器
    private static final SimpleDateFormat DATE_FORMAT = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss'Z'");

    static {
        DATE_FORMAT.setTimeZone(TimeZone.getTimeZone("UTC"));
    }

    /**
     * 结算下注请求数据 - 严格按照官方文档定义
     */
    private static class SettleBetRequest {
        /**
         * betResultReq对象
         */
        private BetResultReq betResultReq;

        // getter/setter
        public BetResultReq getBetResultReq() { return betResultReq; }
        public void setBetResultReq(BetResultReq betResultReq) { this.betResultReq = betResultReq; }
    }

    /**
     * betResultReq对象定义 - 严格按照官方文档
     */
    private static class BetResultReq {
        /**
         * 有效投注额
         */
        private Double effectiveStake;
        /**
         * 游戏代码
         */
        private String gameCode;
        /**
         * 玩家唯一ID
         */
        private String playerId;
        /**
         * 投注金额
         */
        private Double stake;
        /**
         * 玩家赢得的金额
         */
        private Double winAmount;
        /**
         * 代金券ID（可选）
         */
        private String voucherId;
        /**
         * 免费旋转主投注（可选）
         */
        private String freeSpinMainBet;
        /**
         * 投注参考号，始终唯一
         */
        private String refNo;
        /**
         * 用户会话ID（可选）
         */
        private String sessionId;

        // getter/setter
        public Double getEffectiveStake() { return effectiveStake; }
        public void setEffectiveStake(Double effectiveStake) { this.effectiveStake = effectiveStake; }
        public String getGameCode() { return gameCode; }
        public void setGameCode(String gameCode) { this.gameCode = gameCode; }
        public String getPlayerId() { return playerId; }
        public void setPlayerId(String playerId) { this.playerId = playerId; }
        public Double getStake() { return stake; }
        public void setStake(Double stake) { this.stake = stake; }
        public Double getWinAmount() { return winAmount; }
        public void setWinAmount(Double winAmount) { this.winAmount = winAmount; }
        public String getVoucherId() { return voucherId; }
        public void setVoucherId(String voucherId) { this.voucherId = voucherId; }
        public String getFreeSpinMainBet() { return freeSpinMainBet; }
        public void setFreeSpinMainBet(String freeSpinMainBet) { this.freeSpinMainBet = freeSpinMainBet; }
        public String getRefNo() { return refNo; }
        public void setRefNo(String refNo) { this.refNo = refNo; }
        public String getSessionId() { return sessionId; }
        public void setSessionId(String sessionId) { this.sessionId = sessionId; }
    }

    /**
     * 校验结果类
     */
    private static class ValidationResult {
        private boolean success;
        private Player player;
        private FunkyConstants.ErrorCode errorCode;
        private String errorMessage;

        private ValidationResult(boolean success, Player player, FunkyConstants.ErrorCode errorCode, String errorMessage) {
            this.success = success;
            this.player = player;
            this.errorCode = errorCode;
            this.errorMessage = errorMessage;
        }

        public static ValidationResult success(Player player) {
            return new ValidationResult(true, player, null, null);
        }

        public static ValidationResult failure(FunkyConstants.ErrorCode errorCode, String errorMessage) {
            return new ValidationResult(false, null, errorCode, errorMessage);
        }

        public boolean isSuccess() { return success; }
        public Player getPlayer() { return player; }
        public FunkyConstants.ErrorCode getErrorCode() { return errorCode; }
        public String getErrorMessage() { return errorMessage; }
    }

    @Override
    public void run() {
        try {
            // 获取请求参数
            final Map<String, Object> paramsMap = getParamsMap();
            if (ConstantConfig.getInstance().isAgentDebug()) {
                LOGGER.info("ReqSettleBetHandler params: {}", paramsMap);
            }

            // 1. 解析请求
            SettleBetRequest request = parseRequest(paramsMap);
            if (request == null) {
                FunkyUtils.sendErrorResponse(session, FunkyConstants.ErrorCode.INVALID_INPUT);
                return;
            }

            // 2. 校验请求
            ValidationResult validationResult = validateRequest(request);
            if (!validationResult.isSuccess()) {
                FunkyUtils.sendErrorResponse(session, validationResult.getErrorCode());
                return;
            }

            Player player = validationResult.getPlayer();

            // 3. 处理业务逻辑
            processSettleBet(request, player);

        } catch (Exception e) {
            LOGGER.error("ReqSettleBetHandler error", e);
            FunkyUtils.sendErrorResponse(session, FunkyConstants.ErrorCode.INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * 解析请求参数
     *
     * @param paramsMap 请求参数
     * @return 解析成功返回SettleBetRequest对象，失败返回null
     */
    private SettleBetRequest parseRequest(Map<String, Object> paramsMap) {
        try {
            SettleBetRequest request = new SettleBetRequest();

            // 解析betResultReq对象 - betResultReq对象是必需的
            @SuppressWarnings("unchecked")
            final Map<String, Object> betResultReqMap = (Map<String, Object>) paramsMap.get("betResultReq");
            if (betResultReqMap == null) {
                LOGGER.warn("Missing required betResultReq object");
                return null;
            }

            BetResultReq betResultReq = new BetResultReq();

            // effectiveStake是必需的
            Object effectiveStakeObj = betResultReqMap.get("effectiveStake");
            if (effectiveStakeObj == null) {
                LOGGER.warn("Missing required effectiveStake in betResultReq object");
                return null;
            }
            try {
                if (effectiveStakeObj instanceof Number) {
                    betResultReq.setEffectiveStake(((Number) effectiveStakeObj).doubleValue());
                } else if (effectiveStakeObj instanceof String) {
                    betResultReq.setEffectiveStake(Double.parseDouble((String) effectiveStakeObj));
                } else {
                    LOGGER.warn("Invalid effectiveStake type: {}", effectiveStakeObj.getClass().getSimpleName());
                    return null;
                }
            } catch (NumberFormatException e) {
                LOGGER.warn("Invalid effectiveStake format: {}", effectiveStakeObj);
                return null;
            }

            // gameCode是必需的
            String gameCode = (String) betResultReqMap.get("gameCode");
            if (gameCode == null) {
                LOGGER.warn("Missing required gameCode in betResultReq object");
                return null;
            }
            betResultReq.setGameCode(gameCode);

            // playerId是必需的
            String playerId = (String) betResultReqMap.get("playerId");
            if (playerId == null) {
                LOGGER.warn("Missing required playerId in betResultReq object");
                return null;
            }
            betResultReq.setPlayerId(playerId);

            // stake是必需的
            Object stakeObj = betResultReqMap.get("stake");
            if (stakeObj == null) {
                LOGGER.warn("Missing required stake in betResultReq object");
                return null;
            }
            try {
                if (stakeObj instanceof Number) {
                    betResultReq.setStake(((Number) stakeObj).doubleValue());
                } else if (stakeObj instanceof String) {
                    betResultReq.setStake(Double.parseDouble((String) stakeObj));
                } else {
                    LOGGER.warn("Invalid stake type: {}", stakeObj.getClass().getSimpleName());
                    return null;
                }
            } catch (NumberFormatException e) {
                LOGGER.warn("Invalid stake format: {}", stakeObj);
                return null;
            }

            // winAmount是必需的
            Object winAmountObj = betResultReqMap.get("winAmount");
            if (winAmountObj == null) {
                LOGGER.warn("Missing required winAmount in betResultReq object");
                return null;
            }
            try {
                if (winAmountObj instanceof Number) {
                    betResultReq.setWinAmount(((Number) winAmountObj).doubleValue());
                } else if (winAmountObj instanceof String) {
                    betResultReq.setWinAmount(Double.parseDouble((String) winAmountObj));
                } else {
                    LOGGER.warn("Invalid winAmount type: {}", winAmountObj.getClass().getSimpleName());
                    return null;
                }
            } catch (NumberFormatException e) {
                LOGGER.warn("Invalid winAmount format: {}", winAmountObj);
                return null;
            }

            // refNo是必需的
            String refNo = (String) betResultReqMap.get("refNo");
            if (refNo == null) {
                LOGGER.warn("Missing required refNo in betResultReq object");
                return null;
            }
            betResultReq.setRefNo(refNo);

            // 可选字段
            betResultReq.setVoucherId((String) betResultReqMap.get("voucherId"));
            betResultReq.setFreeSpinMainBet((String) betResultReqMap.get("freeSpinMainBet"));
            betResultReq.setSessionId((String) betResultReqMap.get("sessionId"));

            request.setBetResultReq(betResultReq);
            return request;

        } catch (Exception e) {
            LOGGER.error("Error parsing request", e);
            return null;
        }
    }

    /**
     * 校验请求参数和权限
     *
     * @param request 解析后的请求对象
     * @return 校验结果
     */
    private ValidationResult validateRequest(SettleBetRequest request) {
        try {
            // 1. 验证请求头信息
            final Map<String, Object> paramsMap = getParamsMap();
            if (FunkyHeaderValidator.hasInvalidHeaders(paramsMap)) {
                return ValidationResult.failure(FunkyConstants.ErrorCode.PLAYER_NOT_LOGIN, "Invalid headers");
            }

            BetResultReq betResultReq = request.getBetResultReq();

            // 2. 验证必要参数
            if (betResultReq.getPlayerId() == null || betResultReq.getPlayerId().trim().isEmpty()) {
                LOGGER.warn("Missing required playerId parameter");
                return ValidationResult.failure(FunkyConstants.ErrorCode.INVALID_INPUT, "Missing playerId");
            }

            if (!FunkyUtils.isValidPlayerId(betResultReq.getPlayerId())) {
                LOGGER.warn("Invalid playerId format: {} (must be at least 3 characters)", betResultReq.getPlayerId());
                return ValidationResult.failure(FunkyConstants.ErrorCode.INVALID_INPUT, "Invalid playerId format");
            }

            // 3. 验证金额参数
            if (betResultReq.getEffectiveStake() == null || betResultReq.getEffectiveStake() < 0) {
                LOGGER.warn("Invalid effectiveStake: {}", betResultReq.getEffectiveStake());
                return ValidationResult.failure(FunkyConstants.ErrorCode.INVALID_INPUT, "Invalid effectiveStake");
            }

            if (betResultReq.getStake() == null || betResultReq.getStake() < 0) {
                LOGGER.warn("Invalid stake: {}", betResultReq.getStake());
                return ValidationResult.failure(FunkyConstants.ErrorCode.INVALID_INPUT, "Invalid stake");
            }

            if (betResultReq.getWinAmount() == null || betResultReq.getWinAmount() < 0) {
                LOGGER.warn("Invalid winAmount: {}", betResultReq.getWinAmount());
                return ValidationResult.failure(FunkyConstants.ErrorCode.INVALID_INPUT, "Invalid winAmount");
            }

            // 4. 查找玩家
            long playerIdLong;
            try {
                playerIdLong = Long.parseLong(betResultReq.getPlayerId().trim());
            } catch (NumberFormatException e) {
                LOGGER.warn("Invalid playerId format: {}", betResultReq.getPlayerId());
                return ValidationResult.failure(FunkyConstants.ErrorCode.INVALID_INPUT, "Invalid playerId format");
            }

            final Player player = AgentGameMrg.getInstance().findDbPlayer(playerIdLong);
            if (player == null) {
                return ValidationResult.failure(FunkyConstants.ErrorCode.PLAYER_NOT_LOGIN, "Player not found");
            }

            // 5. IP黑名单检查
//            final boolean ipBlackList = ScriptLoader.getInstance().functionScript("AgentGameScript",
//                    (IAgentGameScript script) -> script.isIpBlacklist(MsgUtil.getClientIp(session), AgentGame.FUNKY.getType()));
//            if (!ipBlackList) {
//                return ValidationResult.failure(FunkyConstants.ErrorCode.PLAYER_NOT_LOGIN, "IP blacklisted");
//            }

            return ValidationResult.success(player);

        } catch (Exception e) {
            LOGGER.error("Validation error", e);
            return ValidationResult.failure(FunkyConstants.ErrorCode.INTERNAL_SERVER_ERROR, "Validation error");
        }
    }

    /**
     * 处理结算下注业务逻辑
     *
     * @param request 已验证的结算请求
     * @param player 已验证的玩家对象
     */
    private void processSettleBet(SettleBetRequest request, Player player) {
        BetResultReq betResultReq = request.getBetResultReq();

        // 1. 查找下注记录
        GameNote gameNote = AgentGameMrg.getInstance().findRoundIdGameNote(player, betResultReq.getRefNo());
        if (gameNote == null) {
            LOGGER.warn("Bet not found: refNo={}, playerId={}", betResultReq.getRefNo(), betResultReq.getPlayerId());
            FunkyUtils.sendErrorResponse(session, FunkyConstants.ErrorCode.BET_WAS_NOT_FOUND);
            return;
        }

        // 2. 检查下注是否已经结算
        if (gameNote.getStatus() == 2) { // 假设2表示已结算
            LOGGER.warn("Bet already settled: refNo={}, playerId={}", betResultReq.getRefNo(), betResultReq.getPlayerId());

            // 获取当前余额用于重复请求响应
            final double realBalance = ScriptLoader.getInstance().functionScript("AgentGameScript",
                    (IAgentGameScript script) -> script.realBalance(player));
            final double balance = ScriptLoader.getInstance().functionScript("AgentGameScript",
                    (IAgentGameScript script) -> script.amountTransformUsd(player, realBalance));

            final JSONObject data = new JSONObject();
            data.put("balance", BigDecimalUtils.round(balance, 2));
            FunkyUtils.sendSuccessResponse(session, data);
            return;
        }

        // 3. 转换金额为实际金额
        final double winValue = ScriptLoader.getInstance().functionScript("AgentGameScript",
                (IAgentGameScript script) -> script.usdTransformAmount(player, betResultReq.getWinAmount()));

        // 4. 获取当前余额
        final double realBalance = ScriptLoader.getInstance().functionScript("AgentGameScript",
                (IAgentGameScript script) -> script.realBalance(player));
        final double currentBalance = ScriptLoader.getInstance().functionScript("AgentGameScript",
                (IAgentGameScript script) -> script.amountTransformUsd(player, realBalance));

        // 5. 计算结算后余额
        final double finalBalance = BigDecimalUtils.add(currentBalance, betResultReq.getWinAmount(), 2);
        final double balances = ScriptLoader.getInstance().functionScript("AgentGameScript",
                (IAgentGameScript script) -> script.usdTransformAmount(player, finalBalance));

        // 6. 更新游戏记录
        gameNote = ScriptLoader.getInstance().functionScript("AgentGameScript",
                (IAgentGameScript script) -> script.updateGameNote(player, betResultReq.getRefNo(), betResultReq.getRefNo(), 2, winValue, balances));

        // 获取货币名称
        Currency currency = Currency.valueOf(gameNote.getCurrencyId());
        String currencyName = "";
        if (currency != null) {
            currencyName = currency.name();
        }

        // 7. 通知余额变更
        NotifyData notifyData = new NotifyData();
        notifyData.setType(ChangeType.win.getType())
                .setPid(player.getPlayerId())
                .setPlayerName(betResultReq.getPlayerId())
                .setNoteId(gameNote.getNoteId() + "")
                .setBetAmount(gameNote.getBetAmount())
                .setValidBets(gameNote.getValidBets())
                .setWin(winValue)
                .setTotalWin(gameNote.getWin())
                .setCurrency(currencyName)
                .setData(betResultReq.getRefNo())
                .setUpdSessionId(MsgUtil.getSessionID(session));

        ScriptLoader.getInstance().consumerScript("AgentGameScript",
                (IAgentGameScript script) -> script.updateCurrency(notifyData));
    }
}
