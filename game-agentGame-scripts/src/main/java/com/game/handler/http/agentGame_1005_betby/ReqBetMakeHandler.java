package com.game.handler.http.agentGame_1005_betby;

import com.alibaba.fastjson.JSON;
import com.game.engine.io.conf.ConstantConfig;
import com.game.engine.io.handler.HttpHandler;
import com.game.engine.io.redis.RedisPoolManager;
import com.game.engine.math.BigDecimalUtils;
import com.game.engine.script.IHandlerEntity;
import com.game.engine.script.ScriptLoader;
import com.game.engine.utils.JsonUtils;
import com.game.engine.utils.MsgUtil;
import com.game.entity.game.GameNote;
import com.game.entity.player.Player;
import com.game.enums.ChangeType;
import com.game.enums.Currency;
import com.game.gamesr.NotifyData;
import com.game.gamesr.manager.AgentGameMrg;
import com.game.gamesr.scripts.IAgentGameScript;
import com.game.handler.http.agentGame_1005_betby.BetbyConstants.ValidationResult;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.*;


// 下注消息，先比对 要扣除用户钱包对应金额，不足则下注失败
@IHandlerEntity(path = "/betby/bet/make", desc = "投注")
public class ReqBetMakeHandler extends HttpHandler {
    private static final Logger LOGGER = LoggerFactory.getLogger(ReqBetMakeHandler.class);

    // 将所有的类声明为private static内部类
    private static class BetMakeRequest {
        /**
         * 下注后从玩家余额中扣除的金额
         */
        private int amount;
        /**
         * 货币代码
         */
        private String currency;
        /**
         * 运营商给玩家的唯一编码
         */
        private String playerId;
        /**
         * 运营商在初始化方法中提供的唯一token ID
         */
        private String sessionId;
        /**
         * (可选)该交易相关的奖金唯一编码
         */
        private String bonusId;
        /**
         * (可选)使用的奖金类型(freebet_refund, freebet_freemoney, freebet_no_risk, global_comboboost, comboboost)
         */
        private String bonusType;
        /**
         * 潜在的获胜金额，不包含复式奖励(comboboost)
         */
        private int potentialWin;
        /**
         * 潜在的额外赢取金额，仅出现在使用复式奖励时
         */
        private int potentialComboboostWin;
        /**
         * 包含交易信息的对象
         */
        private TransactionInfo transaction;
        /**
         * 包含玩家投注单信息的对象
         */
        private BetslipInfo betslip;

        // getter/setter
    }

    private static class TransactionInfo {
        /**
         * 由 Betby 分配的交易唯一编码
         */
        private String id;
        /**
         * 下注时 Betby 分配的投注单唯一编码
         */
        private String betslipId;
        /**
         * 由 Betby 分配的玩家唯一编码
         */
        private String playerId;
        /**
         * 运营商唯一编码
         */
        private String operatorId;
        /**
         * 运营商网站唯一编码
         */
        private String operatorBrandId;
        /**
         * 运营商侧分配给玩家的唯一编码
         */
        private String extPlayerId;
        /**
         * 交易的 Unix 时间戳
         */
        private double timestamp;
        /**
         * 从玩家余额中扣除的金额，以分为单位
         */
        private int amount;
        /**
         * 货币代码
         */
        private String currency;
        /**
         * 玩家货币到欧元的汇率
         */
        private String crossRateEuro;
        /**
         * 交易类型
         */
        private String operation;
        /**
         * (可选)与该交易相关的奖金唯一编码
         */
        private String bonusId;

        // getter/setter
    }

    private static class BetslipInfo {
        /**
         * 下注时由 Betby 分配的投注单唯一编码
         */
        private String id;
        /**
         * 投注单的 Unix 时间戳
         */
        private double timestamp;
        /**
         * 由 Betby 分配的玩家唯一编码
         */
        private String playerId;
        /**
         * 运营商唯一编码
         */
        private String operatorId;
        /**
         * 运营商网站唯一编码
         */
        private String operatorBrandId;
        /**
         * 运营商侧分配给玩家的唯一编码
         */
        private String extPlayerId;
        /**
         * 货币代码
         */
        private String currency;
        /**
         * 投注类型的缩写
         */
        private String type;
        /**
         * 玩家下的赌注金额
         */
        private int sum;
        /**
         * 通过将所有选择的赔率相乘得出的投注单总赔率
         */
        private String k;
        /**
         * 标记投注单是否通过快速投注功能完成
         */
        private boolean isQuickBet;
        /**
         * 标记用户是否应用了"接受赔率变化"功能
         */
        private boolean acceptOddsChange;
        /**
         * 包含关于给定投注单中每个选择的字典数组
         */
        private List<BetInfo> bets;

        // getter/setter
    }

    private static class BetInfo {
        /**
         * 投注单的唯一编码，由 Betby 分配的选择
         */
        private String id;
        /**
         * 体育的唯一编码
         */
        private String sportId;
        /**
         * 赛事的唯一编码
         */
        private String eventId;
        /**
         * 赛事的唯一编码
         */
        private String tournamentId;
        /**
         * 类别的唯一编码
         */
        private String categoryId;
        /**
         * (可选)直播市场为 true，预赛为 false
         */
        private boolean live;
        /**
         * (可选)体育名称
         */
        private String sportName;
        /**
         * (可选)类别名称
         */
        private String categoryName;
        /**
         * (可选)赛事名称
         */
        private String tournamentName;
        /**
         * (可选)竞争者列表
         */
        private List<String> competitorNames;
        /**
         * (可选)市场名称
         */
        private String marketName;
        /**
         * (可选)结果名称
         */
        private String outcomeName;
        /**
         * (可选)赛事开始时间
         */
        private int scheduled;
        /**
         * (可选)投注赔率
         */
        private String odds;

        // getter/setter
    }

    /**
     * 重组的投注信息类，用于生成备注
     */
    private static class BetRemarkInfo {
        /**
         * 体育类型
         */
        private String sportName;
        /**
         * 联赛
         */
        private String tournamentName;
        /**
         * 对阵双方 (使用vs连接的字符串格式)
         */
        private String competitorNames;
        /**
         * 投注选项
         */
        private String outcomeName;
        /**
         * 赔率
         */
        private String odds;

        public BetRemarkInfo(String sportName, String tournamentName, String competitorNames, String outcomeName, String odds) {
            this.sportName = sportName;
            this.tournamentName = tournamentName;
            this.competitorNames = competitorNames;
            this.outcomeName = outcomeName;
            this.odds = odds;
        }

        // getter/setter
        public String getSportName() { return sportName; }
        public void setSportName(String sportName) { this.sportName = sportName; }
        public String getTournamentName() { return tournamentName; }
        public void setTournamentName(String tournamentName) { this.tournamentName = tournamentName; }
        public String getCompetitorNames() { return competitorNames; }
        public void setCompetitorNames(String competitorNames) { this.competitorNames = competitorNames; }
        public String getOutcomeName() { return outcomeName; }
        public void setOutcomeName(String outcomeName) { this.outcomeName = outcomeName; }
        public String getOdds() { return odds; }
        public void setOdds(String odds) { this.odds = odds; }
    }

    @Override
    public void run() {
        final Map<String, Object> responseMap = new LinkedHashMap<>();
        try {
            if (ConstantConfig.getInstance().isAgentDebug()) {
                LOGGER.info("betby BET_MAKE request: {}", JsonUtils.writeAsJson(paramsMap));
            }

//            final boolean ipBlackList = ScriptLoader.getInstance().functionScript("AgentGameScript",
//                    (IAgentGameScript script) -> script.isIpBlacklist(MsgUtil.getClientIp(session), AgentGame.BETBY.getType()));
//            if (!ipBlackList) {
//                // 其他错误
//                responseMap.put("code", 3000);
//                MsgUtil.responseHttp(responseMap, session);
//                return;
//            }

            final BetMakeRequest request = parseRequest((String) paramsMap.get("payload"));

            // 验证请求
            ValidationResult validationResult = validateRequest(request);
            if (!validationResult.isSuccess()) {
                BetbyConstants.sendErrorResponse(session, validationResult.getErrorCode(), validationResult.getErrorMessage());
                return;
            }

            Player player = validationResult.getPlayer();

            Currency currency = BetbyConstants.mapToCurrency(request.currency);
            // 检查货币是否支持
            if (currency == null) {
                LOGGER.warn("Unsupported currency: {}", request.currency);
                BetbyConstants.sendErrorResponse(session, 2002, "Unsupported currency: " + request.currency);
                return;
            }

            processBetRequest(request, player);
        } catch (Exception e) {
            LOGGER.error("ReqBetMakeHandle error", e);
            BetbyConstants.sendErrorResponse(session, 2004, "Bad request");
        }
    }

    private BetMakeRequest parseRequest(String payLoad) {
        try {
            Map<String, Object> requestMap = JwtVerifier.verifyAndDecodeJWT(payLoad);
            if (ConstantConfig.getInstance().isAgentDebug()) {
                LOGGER.info("betby BET_MAKE payload: {}", requestMap);
            }

            @SuppressWarnings("unchecked")
            Map<String, Object> paramsMap = (Map<String, Object>) requestMap.get("payload");

            BetMakeRequest request = new BetMakeRequest();

            // 基本参数
            request.amount = (Integer) paramsMap.get("amount");
            request.currency = (String) paramsMap.get("currency");
            request.playerId = (String) paramsMap.get("player_id");
            request.sessionId = (String) paramsMap.get("session_id");

            if (paramsMap.containsKey("bonus_id")) {
                request.bonusId = (String) paramsMap.get("bonus_id");
            }
            if (paramsMap.containsKey("bonus_type")) {
                request.bonusType = (String) paramsMap.get("bonus_type");
            }

            request.potentialWin = (Integer) paramsMap.get("potential_win");
            if (paramsMap.containsKey("potential_comboboost_win")) {
                request.potentialComboboostWin = (Integer) paramsMap.get("potential_comboboost_win");
            }

            @SuppressWarnings("unchecked") final Map<String, Object> transaction = (Map<String, Object>) paramsMap.get("transaction");
            request.transaction = parseTransactionInfo(transaction);

            @SuppressWarnings("unchecked") final Map<String, Object> betslip = (Map<String, Object>) paramsMap.get("betslip");
            request.betslip = parseBetslipInfo(betslip);

            return request;
        } catch (Exception e) {
            LOGGER.error("Error parsing request: {}", e.getMessage());
            throw e;
        }
    }

    private TransactionInfo parseTransactionInfo(Map<String, Object> json) {
        TransactionInfo transaction = new TransactionInfo();

        transaction.id = (String) json.get("id");
        transaction.betslipId = (String) json.get("betslip_id");
        transaction.playerId = (String) json.get("player_id");
        transaction.operatorId = (String) json.get("operator_id");
        transaction.operatorBrandId = (String) json.get("operator_brand_id");
        transaction.extPlayerId = (String) json.get("ext_player_id");
        transaction.timestamp = (Double) json.get("timestamp");
        transaction.amount = (Integer) json.get("amount");
        transaction.currency = (String) json.get("currency");
        transaction.crossRateEuro = (String) json.get("cross_rate_euro");
        transaction.operation = (String) json.get("operation");

        if (json.containsKey("bonus_id")) {
            transaction.bonusId = (String) json.get("bonus_id");
        }

        return transaction;
    }

    private BetslipInfo parseBetslipInfo(Map<String, Object> json) {
        BetslipInfo betslip = new BetslipInfo();

        betslip.id = (String) json.get("id");
        betslip.timestamp = (Double) json.get("timestamp");
        betslip.playerId = (String) json.get("player_id");
        betslip.operatorId = (String) json.get("operator_id");
        betslip.operatorBrandId = (String) json.get("operator_brand_id");
        betslip.extPlayerId = (String) json.get("ext_player_id");
        betslip.currency = (String) json.get("currency");
        betslip.type = (String) json.get("type");
        betslip.sum = (Integer) json.get("sum");
        betslip.k = (String) json.get("k");
        betslip.isQuickBet = (Boolean) json.get("is_quick_bet");
        betslip.acceptOddsChange = (Boolean) json.get("accept_odds_change");

        // 解析bets数组
        @SuppressWarnings("unchecked")
        List<Map<String, Object>> betsArray = (List<Map<String, Object>>) json.get("bets");
        betslip.bets = new ArrayList<>();
        for (Map<String, Object> betJson : betsArray) {
            BetInfo bet = parseBetInfo(betJson);
            betslip.bets.add(bet);
        }

        return betslip;
    }

    private BetInfo parseBetInfo(Map<String, Object> json) {
        BetInfo bet = new BetInfo();

        bet.id = (String) json.get("id");
        bet.sportId = (String) json.get("sport_id");
        bet.eventId = (String) json.get("event_id");
        bet.tournamentId = (String) json.get("tournament_id");
        bet.categoryId = (String) json.get("category_id");

        if (json.containsKey("live")) {
            bet.live = (Boolean) json.get("live");
        }
        if (json.containsKey("sport_name")) {
            bet.sportName = (String) json.get("sport_name");
        }
        if (json.containsKey("category_name")) {
            bet.categoryName = (String) json.get("category_name");
        }
        if (json.containsKey("tournament_name")) {
            bet.tournamentName = (String) json.get("tournament_name");
        }
        if (json.containsKey("competitor_name")) {
            @SuppressWarnings("unchecked")
            List<String> competitorNames = (List<String>) json.get("competitor_name");
            bet.competitorNames = competitorNames;
        }
        if (json.containsKey("market_name")) {
            bet.marketName = (String) json.get("market_name");
        }
        if (json.containsKey("outcome_name")) {
            bet.outcomeName = (String) json.get("outcome_name");
        }
        if (json.containsKey("scheduled")) {
            bet.scheduled = (Integer) json.get("scheduled");
        }
        if (json.containsKey("odds")) {
            bet.odds = (String) json.get("odds");
        }

        return bet;
    }

    private ValidationResult validateRequest(BetMakeRequest request) {
        try {
            final String playerId = BetbyConstants.getPlayerId(request.playerId);
            final Player player = AgentGameMrg.getInstance().findDbPlayer(Long.parseLong(playerId));
            if (player == null) {
                return ValidationResult.failure(1006, "Player not found");
            }

            return ValidationResult.success(player);

        } catch (Exception e) {
            LOGGER.error("Validation error", e);
            return ValidationResult.failure(2004, "Validation error");
        }
    }

    private void processBetRequest(BetMakeRequest request, Player player) {
        final Map<String, Object> responseMap = new LinkedHashMap<>();

        try {
            // 1. 查找是否存在重复下注
            GameNote gameNote = AgentGameMrg.getInstance().findRoundIdGameNote(player, request.transaction.id);
            if (gameNote != null) {
                // 重复下注，返回成功但不处理
                LOGGER.warn("Duplicate bet detected: {}", request.transaction.id);
                buildAndSendSuccessResponse(request, player, responseMap);
            }

            // 检查交易是否已被丢弃
            if (checkAndRemoveDiscardRecord(request.transaction.id)) {
                // 交易已被丢弃，直接返回成功但不执行投注操作
                LOGGER.warn("Transaction has been discarded: {}", request.transaction.id);

                BetbyConstants.sendErrorResponse(session, 2004, "Discard found");
                return;
            }

            final double realBalance = ScriptLoader.getInstance().functionScript("AgentGameScript",
                    (IAgentGameScript script) -> script.realBalance(player));
            double balance = ScriptLoader.getInstance().functionScript("AgentGameScript",
                    (IAgentGameScript script) -> script.amountTransformUsd(player, realBalance));
            final double reqAmount = request.amount / 100.0;
            // 3. 检查余额是否足够 // 平台是分币
            if (reqAmount > balance) {
                LOGGER.warn("bet failed. Not enough money");

                BetbyConstants.sendErrorResponse(session, 2001, "Not enough money");
                return;
            }

            // 4. 扣除玩家余额 // 平台是分币
            final double finalBalance = BigDecimalUtils.sub(balance, reqAmount, 2);

            // 5. 转换金额为实际金额
            final double betValue = ScriptLoader.getInstance().functionScript("AgentGameScript",
                    (IAgentGameScript script) -> script.usdTransformAmount(player, reqAmount));

            final double balances = ScriptLoader.getInstance().functionScript("AgentGameScript",
                    (IAgentGameScript script) -> script.usdTransformAmount(player, finalBalance));

            // 6. 提取投注信息并生成备注
            String betRemark = extractBetRemarkInfo(request.betslip);

            // 7. 创建或更新游戏记录（使用带备注的版本）
            gameNote = ScriptLoader.getInstance().functionScript("AgentGameScript",
                    (IAgentGameScript script) -> script.addGameNote(player, request.transaction.id, request.transaction.id, betValue, betValue, balances, betRemark));

            // 构建通知数据并执行货币更新（包含响应发送）
            Map<String, Object> dataMap = new HashMap<>();
            dataMap.put("transactionId", request.transaction.id);

            String dataJson = JSON.toJSONString(dataMap);

            // 8. 发送通知更新货币
            NotifyData notifyData = new NotifyData();
            notifyData.setType(ChangeType.cost.getType())
                    .setPid(player.getPlayerId())
                    .setPlayerName(request.playerId)
                    .setNoteId(gameNote.getNoteId() + "")
                    .setCurrency(request.currency)
                    .setBetAmount(betValue)
                    .setData(dataJson)
                    .setUpdSessionId(MsgUtil.getSessionID(session));

            ScriptLoader.getInstance().consumerScript("AgentGameScript",
                    (IAgentGameScript script) -> script.updateCurrency(notifyData));

        } catch (Exception e) {
            LOGGER.error("Process bet error", e);
            BetbyConstants.sendErrorResponse(session, 2004, "Server error");
        }
    }

    /**
     * 检查交易是否已被丢弃
     * 如果交易已被丢弃，会自动删除Redis中的记录
     *
     * @param transactionId 需要检查的交易ID
     * @return 如果交易已被丢弃返回true，否则返回false
     */
    private boolean checkAndRemoveDiscardRecord(String transactionId) {
        // 构建Redis键
        String redisKey = "betby:discard:" + transactionId;

        // 检查Redis中是否存在相应的丢弃记录
        Long discardExists = RedisPoolManager.getInstance().function(jedis ->
                jedis.sync().exists(redisKey)
        );

        // 如果记录存在，删除并返回true
        if (discardExists != null && discardExists > 0) {
            LOGGER.info("Discard request was processed before for transaction: {}", transactionId);

            // 删除Redis中的discard记录
            RedisPoolManager.getInstance().function(jedis ->
                    jedis.sync().del(redisKey)
            );


            LOGGER.info("Removed discard record from Redis for transaction: {}", transactionId);
            return true;
        }

        // 不存在discard记录
        return false;
    }

    // 在异常流程未结算时要求返回成功
    private void buildAndSendSuccessResponse(BetMakeRequest request, Player player, Map<String, Object> responseMap) {
        // 使用BetMakeResponse作为字段参考模板
        responseMap.put("id", request.transaction.id);                       // BetMakeResponse.id
        responseMap.put("ext_transaction_id", request.transaction.id);       // BetMakeResponse.extTransactionId
        responseMap.put("parent_transaction_id", null);                      // BetMakeResponse.parentTransactionId
        responseMap.put("user_id", request.playerId);                        // BetMakeResponse.userId
        responseMap.put("operation", "bet");                                 // BetMakeResponse.operation
        responseMap.put("amount", request.amount);                           // BetMakeResponse.amount
        responseMap.put("currency", request.currency);                       // BetMakeResponse.currency

        final double realBalance = ScriptLoader.getInstance().functionScript("AgentGameScript",
                (IAgentGameScript script) -> script.realBalance(player));

        responseMap.put("balance", realBalance * 100);               // BetMakeResponse.balance

        MsgUtil.responseHttp(responseMap, session);
    }

    /**
     * 提取投注信息并重组为JSON字符串
     * @param betslip 投注单信息
     * @return 压缩的JSON字符串
     */
    private String extractBetRemarkInfo(BetslipInfo betslip) {
        try {
            List<BetRemarkInfo> betRemarkInfos = new ArrayList<>();

            if (betslip.bets != null && !betslip.bets.isEmpty()) {
                for (int i = 0; i < betslip.bets.size(); i++) {
                    BetInfo bet = betslip.bets.get(i);

                    // 格式化对阵双方名称
                    String formattedCompetitors = formatCompetitorNames(bet.competitorNames);

                    BetRemarkInfo remarkInfo = new BetRemarkInfo(
                        bet.sportName != null ? bet.sportName : "",
                        bet.tournamentName != null ? bet.tournamentName : "",
                        formattedCompetitors,
                        bet.outcomeName != null ? bet.outcomeName : "",
                        bet.odds != null ? bet.odds : ""
                    );
                    betRemarkInfos.add(remarkInfo);
                }
            }

            // 将投注信息列表转换为JSON字符串
            String jsonResult = JsonUtils.writeAsJson(betRemarkInfos);
            return jsonResult;
        } catch (Exception e) {
            LOGGER.error("Error extracting bet remark info", e);
            return "";
        }
    }

    /**
     * 格式化对阵双方名称
     * @param competitorNames 对阵双方名称列表
     * @return 格式化后的字符串
     */
    private String formatCompetitorNames(List<String> competitorNames) {
        if (competitorNames == null || competitorNames.isEmpty()) {
            return "";
        }

        if (competitorNames.size() == 1) {
            // 只有一个队伍，直接返回
            return competitorNames.get(0);
        } else {
            // 多个队伍，使用 "vs" 连接
            return String.join(" vs ", competitorNames);
        }
    }
}
