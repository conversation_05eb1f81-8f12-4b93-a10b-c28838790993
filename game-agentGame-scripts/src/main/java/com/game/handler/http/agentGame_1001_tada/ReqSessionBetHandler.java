package com.game.handler.http.agentGame_1001_tada;

import com.game.engine.io.conf.ConstantConfig;
import com.game.engine.io.handler.HttpHandler;
import com.game.engine.math.BigDecimalUtils;
import com.game.engine.script.IHandlerEntity;
import com.game.engine.script.ScriptLoader;
import com.game.engine.utils.JsonUtils;
import com.game.engine.utils.MsgUtil;
import com.game.entity.game.GameNote;
import com.game.entity.player.Player;
import com.game.enums.ChangeType;
import com.game.gamesr.NotifyData;
import com.game.gamesr.manager.AgentGameMrg;
import com.game.gamesr.scripts.IAgentGameScript;
import com.game.gamesr.utils.JWTUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.LinkedHashMap;
import java.util.Map;
import java.util.Objects;

//http://127.0.0.1:8680/bet
@IHandlerEntity(path = "/tada/sessionBet", desc = "下注")
public class ReqSessionBetHandler extends HttpHandler {
    private static final Logger LOGGER = LoggerFactory.getLogger(ReqSessionBetHandler.class);

    @Override
    public void run() {
        final Map<String, Object> responseMap = new LinkedHashMap<>();
        try {
            if (ConstantConfig.getInstance().isAgentDebug()) {
                LOGGER.info("ReqSessionBetHandler，params：{}", JsonUtils.writeAsJson(paramsMap));
            }

            final String reqId = (String) paramsMap.get("reqId");//玩家识别码
            final String token = (String) paramsMap.get("token");
            final long sessionId = (long) paramsMap.get("sessionId");//注单id
            final String currency = (String) paramsMap.get("currency");//货币
            final String betAmount = paramsMap.get("betAmount") + "";//押注金额
            final String winLoseAmount = paramsMap.get("winloseAmount") + "";//派彩

            final int type = (int) paramsMap.get("type");//1.下注 2.结算
            final String turnover = paramsMap.get("turnover") + "";//有效下注
            final String preserve = paramsMap.get("preserve") + "";//预扣

            final Map<String, Object> jwtMap = JWTUtils.verify(token);

            if (jwtMap.isEmpty() || !Objects.isNull(jwtMap.get("error"))) {
                responseMap.put("errorCode", 4);
                responseMap.put("message", "Token expired");
                responseMap.put("balance", 0);
                MsgUtil.responseHttp(responseMap, session);
                return;
            }

            final long playerId = (long) jwtMap.get("userId");
            final String playerName = (String) jwtMap.get("playerName");

            final Player player = AgentGameMrg.getInstance().findDbPlayer(playerId);
            if (player == null) {
                responseMap.put("errorCode", 5);
                responseMap.put("message", "Invalid Player");
                responseMap.put("username", playerName);
                responseMap.put("currency", currency);
                responseMap.put("balance", 0);
                MsgUtil.responseHttp(responseMap, session);
                return;
            }

            final double realBalance = ScriptLoader.getInstance().functionScript("AgentGameScript",
                    (IAgentGameScript script) -> script.realBalance(player));

            double balance = ScriptLoader.getInstance().functionScript("AgentGameScript",
                    (IAgentGameScript script) -> script.amountTransformUsd(player, realBalance));

            final NotifyData notifyData = new NotifyData();
            switch (type) {
                case 1://下注
                {
                    GameNote gameNote = AgentGameMrg.getInstance().findRoundIdGameNote(player, sessionId + "");
                    if (gameNote != null && gameNote.getTransactionIds().contains(reqId)) {
                        responseMap.put("errorCode", 0);
                        responseMap.put("message", "success");
                        responseMap.put("username", playerName);
                        responseMap.put("currency", currency);
                        responseMap.put("balance", BigDecimalUtils.round(balance, 2));
                        MsgUtil.responseHttp(responseMap, session);
                        return;
                    }

                    if (Double.parseDouble(betAmount) > balance) {
                        responseMap.put("errorCode", 2);
                        responseMap.put("message", "Not enough balance");
                        responseMap.put("username", playerName);
                        responseMap.put("currency", currency);
                        responseMap.put("balance", BigDecimalUtils.round(balance, 2));
                        MsgUtil.responseHttp(responseMap, session);
                        return;
                    }

                    double betValue = 0;
                    if (Double.parseDouble(preserve) == 0) {
                        balance = BigDecimalUtils.sub(balance, Double.parseDouble(betAmount), 2);
                        betValue = Double.parseDouble(betAmount);
                    } else {
                        balance = BigDecimalUtils.sub(balance, Double.parseDouble(preserve), 2);
                        betValue = Double.parseDouble(preserve);
                    }

                    responseMap.put("errorCode", 0);
                    responseMap.put("message", "success");
                    responseMap.put("username", playerName);
                    responseMap.put("currency", currency);
                    responseMap.put("balance", BigDecimalUtils.round(balance, 2));
                    MsgUtil.responseHttp(responseMap, session);

                    final double finalBetValue = betValue;
                    final double bet = ScriptLoader.getInstance().functionScript("AgentGameScript",
                            (IAgentGameScript script) -> script.usdTransformAmount(player, finalBetValue));

                    final double finalBalance = balance;
                    final double balances = ScriptLoader.getInstance().functionScript("AgentGameScript",
                            (IAgentGameScript script) -> script.usdTransformAmount(player, finalBalance));

                    if (gameNote == null) {
                        gameNote = ScriptLoader.getInstance().functionScript("AgentGameScript",
                                (IAgentGameScript script) -> script.addGameNote(player, sessionId + "", reqId, bet, bet, balances));
                    }

                    notifyData.setType(ChangeType.cost.getType())
                            .setPid(playerId)
                            .setNoteId(gameNote.getNoteId() + "")
                            .setBetAmount(gameNote.getBetAmount());
                    ScriptLoader.getInstance().consumerScript("AgentGameScript",
                            (IAgentGameScript script) -> script.updateCurrency(notifyData));
                    break;
                }
                case 2://结算
                {
                    GameNote gameNote = AgentGameMrg.getInstance().findRoundIdGameNote(player, sessionId + "");
                    if (gameNote == null) {
                        responseMap.put("errorCode", 0);
                        responseMap.put("message", "success");
                        responseMap.put("username", playerName);
                        responseMap.put("currency", currency);
                        responseMap.put("balance", BigDecimalUtils.round(balance, 2));
                        MsgUtil.responseHttp(responseMap, session);
                        return;
                    }

                    if (gameNote.getTransactionIds().contains(reqId)) {
                        responseMap.put("errorCode", 0);
                        responseMap.put("message", "success");
                        responseMap.put("username", playerName);
                        responseMap.put("currency", currency);
                        responseMap.put("balance", BigDecimalUtils.round(balance, 2));
                        MsgUtil.responseHttp(responseMap, session);
                        return;
                    }

                    double win = 0;
                    if (Double.parseDouble(preserve) == 0) {
                        balance = BigDecimalUtils.add(balance, Double.parseDouble(winLoseAmount), 2);
                        win = Double.parseDouble(winLoseAmount);
                    } else {
                        final double value = BigDecimalUtils.add(balance, Double.parseDouble(preserve), 2);
                        balance = BigDecimalUtils.add(BigDecimalUtils.sub(value, Double.parseDouble(betAmount), 2), Double.parseDouble(winLoseAmount), 2);
                        win = BigDecimalUtils.add(BigDecimalUtils.sub(Double.parseDouble(preserve), Double.parseDouble(betAmount), 2), Double.parseDouble(winLoseAmount), 2);
                    }

//                    responseMap.put("errorCode", 0);
//                    responseMap.put("message", "success");
//                    responseMap.put("username", playerName);
//                    responseMap.put("currency", currency);
//                    responseMap.put("balance", BigDecimalUtils.round(balance, 2));
//                    MsgUtil.responseHttp(responseMap, session);

                    final double turnovers = ScriptLoader.getInstance().functionScript("AgentGameScript",
                            (IAgentGameScript script) -> script.usdTransformAmount(player, Double.parseDouble(turnover)));

                    final double finalWin = win;
                    final double wins = ScriptLoader.getInstance().functionScript("AgentGameScript",
                            (IAgentGameScript script) -> script.usdTransformAmount(player, finalWin));

                    final double finalBalance = balance;
                    final double balances = ScriptLoader.getInstance().functionScript("AgentGameScript",
                            (IAgentGameScript script) -> script.usdTransformAmount(player, finalBalance));

                    gameNote = ScriptLoader.getInstance().functionScript("AgentGameScript",
                            (IAgentGameScript script) -> script.updateGameNote(player, sessionId + "", reqId, 2, wins, balances));

                    notifyData.setType(ChangeType.win.getType())
                            .setPid(playerId)
                            .setNoteId(gameNote.getNoteId() + "")
                            .setBetAmount(gameNote.getBetAmount())
                            .setValidBets(gameNote.getValidBets())
                            .setWin(wins)
                            .setTotalWin(gameNote.getWin())
                            .setPlayerName(playerName)
                            .setCurrency(currency)
                            .setUpdSessionId(MsgUtil.getSessionID(session));
                    ScriptLoader.getInstance().consumerScript("AgentGameScript",
                            (IAgentGameScript script) -> script.updateCurrency(notifyData));
                    break;
                }
            }
        } catch (Exception e) {
            LOGGER.error("ReqSessionBetHandler", e);
            responseMap.put("errorCode", 5);
            responseMap.put("message", "server error");
            MsgUtil.responseHttp(responseMap, session);
        }
    }

}
