//package com.game.handler.http.agentGame_1004_pg;
//
//
//import com.alibaba.fastjson.JSONObject;
//import com.game.c_entity.middleplatform.C_BaseGamePlatform;
//import com.game.engine.io.conf.ConstantConfig;
//import com.game.engine.io.handler.HttpHandler;
//import com.game.engine.math.BigDecimalUtils;
//import com.game.engine.script.IHandlerEntity;
//import com.game.engine.script.ScriptLoader;
//import com.game.engine.utils.JsonUtils;
//import com.game.engine.utils.MsgUtil;
//import com.game.engine.utils.TimeUtil;
//import com.game.entity.game.GameNote;
//import com.game.entity.player.Player;
//import com.game.enums.ChangeType;
//import com.game.enums.GameType;
//import com.game.gamesr.NotifyData;
//import com.game.gamesr.manager.AgentGameMrg;
//import com.game.gamesr.manager.DataAgentGameMrg;
//import com.game.gamesr.scripts.IAgentGameScript;
//import com.game.gamesr.utils.JWTUtils;
//import org.slf4j.Logger;
//import org.slf4j.LoggerFactory;
//
//import java.util.LinkedHashMap;
//import java.util.Map;
//import java.util.Objects;
//
////http://127.0.0.1:8680/pg/Cash/TransferOut
//@IHandlerEntity(path = "/pg/Cash/TransferOut", desc = "下注")
//public class ReqTransferOutHandler extends HttpHandler {
//    private static final Logger LOGGER = LoggerFactory.getLogger(ReqTransferOutHandler.class);
//
//    @Override
//    public void run() {
//        final Map<String, Object> responseMap = new LinkedHashMap<>();
//        try {
//            if (ConstantConfig.getInstance().isAgentDebug()) {
//                LOGGER.info("ReqTransferOutHandler，params：{}", JsonUtils.writeAsJson(paramsMap));
//            }
//
//            final String operator_token = (String) paramsMap.get("operator_token");
//            final String secret_key = (String) paramsMap.get("secret_key");
//            final String operator_player_session = (String) paramsMap.get("operator_player_session");
//            final String parent_bet_id = (String) paramsMap.get("parent_bet_id");
//            final String bet_id = (String) paramsMap.get("bet_id");
//
//            final String transfer_amount = (String) paramsMap.get("transfer_amount");
//
//            final Map<String, Object> jwtMap = JWTUtils.verify(operator_player_session);
//
//            if (jwtMap.isEmpty() || !Objects.isNull(jwtMap.get("error"))) {
//                final JSONObject error = new JSONObject();
//                error.put("code", 1300);
//                error.put("message", "Invalid token");
//
//                responseMap.put("data", null);
//                responseMap.put("error", error);
//                MsgUtil.responseHttp(responseMap, session);
//                return;
//            }
//
//            final String currency = (String) jwtMap.get("currency");
//            final String playerName = (String) jwtMap.get("playerName");
//
//            final long playerId = AgentGameMrg.getInstance().getAgentGamePlayerId(playerName);
//
//            final Player player = AgentGameMrg.getInstance().findDbPlayer(playerId);
//            if (player == null) {
//                final JSONObject error = new JSONObject();
//                error.put("code", 3004);
//                error.put("message", "PlayerNotFoundException");
//
//                responseMap.put("data", null);
//                responseMap.put("error", error);
//                MsgUtil.responseHttp(responseMap, session);
//                return;
//            }
//
//            final C_BaseGamePlatform c_baseGamePlatform = DataAgentGameMrg.getInstance().findC_BaseGamePlatform(this.getClass().getSimpleName(), 2, GameType.Casino_Slots.getType());
//            if (c_baseGamePlatform == null) {
//                final JSONObject error = new JSONObject();
//                error.put("code", 1200);
//                error.put("message", "server error");
//
//                responseMap.put("data", null);
//                responseMap.put("error", error);
//                MsgUtil.responseHttp(responseMap, session);
//                return;
//            }
//
//            if (!Objects.equals(c_baseGamePlatform.getToken(), operator_token)) {
//                final JSONObject error = new JSONObject();
//                error.put("code", 1034);
//                error.put("message", "Invalid operator_token");
//
//                responseMap.put("data", null);
//                responseMap.put("error", error);
//                MsgUtil.responseHttp(responseMap, session);
//                return;
//            }
//
//            if (!Objects.equals(c_baseGamePlatform.getSecretKey(), secret_key)) {
//                final JSONObject error = new JSONObject();
//                error.put("code", 1034);
//                error.put("message", "Invalid secret_key");
//
//                responseMap.put("data", null);
//                responseMap.put("error", error);
//                MsgUtil.responseHttp(responseMap, session);
//                return;
//            }
//
//            final double realBalance = ScriptLoader.getInstance().functionScript("AgentGameScript",
//                    (IAgentGameScript script) -> script.realBalance(player));
//
//            double balance = ScriptLoader.getInstance().functionScript("AgentGameScript",
//                    (IAgentGameScript script) -> script.amountTransformUsd(player, realBalance));
//
//            GameNote gameNote = AgentGameMrg.getInstance().findRoundIdGameNote(player.getPlayerId(), player.getPlatformId(), parent_bet_id);
//            if (gameNote != null && gameNote.getTransactionIds().contains(bet_id)) {
//                LOGGER.warn("bet_id：{}，is exist", bet_id);
//
//                final JSONObject data = new JSONObject();
//                data.put("currency_code", currency);
//                data.put("balance_amount", BigDecimalUtils.round(balance, 2));
//                data.put("updated_time", TimeUtil.currentTimeMillis());
//
//                responseMap.put("data", data);
//                responseMap.put("error", null);
//                MsgUtil.responseHttp(responseMap, session);
//                return;
//            }
//
//            if (Double.parseDouble(transfer_amount) > balance) {
//                final JSONObject error = new JSONObject();
//                error.put("code", 3200);
//                error.put("message", "NotEnoughBalanceException");
//
//                responseMap.put("data", null);
//                responseMap.put("error", error);
//                MsgUtil.responseHttp(responseMap, session);
//                return;
//            }
//
//            balance = BigDecimalUtils.sub(balance, Double.parseDouble(transfer_amount), 4);
//
//            final JSONObject data = new JSONObject();
//            data.put("currency_code", currency);
//            data.put("balance_amount", BigDecimalUtils.round(balance, 2));
//            data.put("updated_time", TimeUtil.currentTimeMillis());
//
//            responseMap.put("data", data);
//            responseMap.put("error", null);
//            MsgUtil.responseHttp(responseMap, session);
//
//            final double betAmount = ScriptLoader.getInstance().functionScript("AgentGameScript",
//                    (IAgentGameScript script) -> script.usdTransformAmount(player, Double.parseDouble(transfer_amount)));
//
//            double finalBalance = balance;
//            final double balances = ScriptLoader.getInstance().functionScript("AgentGameScript",
//                    (IAgentGameScript script) -> script.usdTransformAmount(player, finalBalance));
//
//            if (gameNote == null) {
//                gameNote = ScriptLoader.getInstance().functionScript("AgentGameScript",
//                        (IAgentGameScript script) -> script.addGameNote(player, parent_bet_id, bet_id, betAmount, betAmount, balances));
//            }
//
//            final NotifyData notifyData = new NotifyData();
//            notifyData.setType(ChangeType.cost.getChange())
//                    .setPid(playerId)
//                    .setNoteId(gameNote.getNoteId() + "")
//                    .setBetAmount(gameNote.getBetAmount());
//            ScriptLoader.getInstance().consumerScript("AgentGameScript",
//                    (IAgentGameScript script) -> script.updateCurrency(notifyData));
//        } catch (Exception e) {
//            LOGGER.error("ReqTransferOutHandler", e);
//            final JSONObject error = new JSONObject();
//            error.put("code", 1200);
//            error.put("message", "server error");
//
//            responseMap.put("data", null);
//            responseMap.put("error", error);
//            MsgUtil.responseHttp(responseMap, session);
//        }
//    }
//}
