//package com.game.handler.http.agentGame_1000;
//
//import com.game.engine.io.handler.HttpHandler;
//import com.game.engine.io.redis.RedisPoolManager;
//import com.game.engine.math.BigDecimalUtils;
//import com.game.engine.script.IHandlerEntity;
//import com.game.engine.script.ScriptLoader;
//import com.game.engine.utils.JsonUtils;
//import com.game.engine.utils.MsgUtil;
//import com.game.engine.utils.TimeUtil;
//import com.game.entity.player.Player;
//import com.game.enums.ChangeType;
//import com.game.enums.redis.RedisHall;
//import com.game.gamesr.NotifyData;
//import com.game.gamesr.manager.AgentGameMrg;
//import com.game.gamesr.scripts.IAgentGameScript;
//import com.game.redis.RedisUtils;
//import io.netty.util.internal.StringUtil;
//import org.slf4j.Logger;
//import org.slf4j.LoggerFactory;
//
//import java.util.LinkedHashMap;
//import java.util.Map;
//
////http://127.0.0.1:8680/seamless/bet
//@IHandlerEntity(path = "/seamless/bet", desc = "下注")
//public class ReqSeamlessBetHandler extends HttpHandler {
//    private static final Logger LOGGER = LoggerFactory.getLogger(ReqSeamlessBetHandler.class);
//
//    @Override
//    public void run() {
//        try {
//            LOGGER.info("ReqSeamlessBetHandler，params：{}", JsonUtils.writeAsJson(paramsMap));
//            final String playerStr = (String) paramsMap.get("player");//玩家识别码
//            final String amount = (String) paramsMap.get("amount");//下注金额
//            final String roundId = (String) paramsMap.get("round");
//
//            final Map<String, Object> responseMap = new LinkedHashMap<>();
//            if (StringUtil.isNullOrEmpty(playerStr) || StringUtil.isNullOrEmpty(amount)) {
//                responseMap.put("message", "INVALID_ARGUMENT");
//                responseMap.put("timestamp", TimeUtil.currentTimeMillis());
//                MsgUtil.responseHttp(responseMap, session);
//                return;
//            }
//
//            final long playerId = AgentGameMrg.getInstance().getAgentGamePlayerId(playerStr);
//            final Player player = AgentGameMrg.getInstance().findDbPlayer(playerId);
//            if (player == null) {
//                responseMap.put("message", "INVALID_ARGUMENT");
//                responseMap.put("timestamp", TimeUtil.currentTimeMillis());
//                MsgUtil.responseHttp(responseMap, session);
//                return;
//            }
//
//            final boolean isIpBlacklist = ScriptLoader.getInstance().functionScript("AgentGameScript", (IAgentGameScript script) -> script.isIpBlacklist(player, MsgUtil.getClientIp(session)));
//            if (!isIpBlacklist) {
//                return;
//            }
//
//            final int currencyId = player.getCurrencyId();
//            final String balanceStr = RedisPoolManager.getInstance().function(jedis -> jedis.hget(RedisHall.Platform_Role_Map_Currency.getKey(playerId), String.valueOf(currencyId)));
//            final double balance = RedisUtils.getInstance().convert(balanceStr);
//
//            if (Double.parseDouble(amount) == 0 || Double.parseDouble(amount) > balance) {
//                responseMap.put("balance", balance);
//                responseMap.put("timestamp", TimeUtil.currentTimeMillis());
//                MsgUtil.responseHttp(responseMap, session);
//                return;
//            }
//
//            responseMap.put("balance", BigDecimalUtils.sub(balance, Double.parseDouble(amount), 4));
//            responseMap.put("timestamp", TimeUtil.currentTimeMillis());
//            MsgUtil.responseHttp(responseMap, session);
//
//            final NotifyData notifyData = new NotifyData();
//            notifyData.setType(ChangeType.cost.getChange())
//                    .setPid(playerId)
//                    .setCurrencyId(currencyId)
//                    .setValidBets(Double.parseDouble(amount))
//                    .setUdpSessionId(MsgUtil.getSessionID(session));
//            ScriptLoader.getInstance().consumerScript("AgentGameScript", (IAgentGameScript script) -> script.updateCurrency(notifyData));
//
//            ScriptLoader.getInstance().consumerScript("AgentGameScript", (IAgentGameScript script) -> script.addGameNote(player, roundId, Double.parseDouble(amount), Double.parseDouble(amount)));
//        } catch (Exception e) {
//            LOGGER.error("ReqSeamlessBetHandler", e);
//        }
//    }
//
//}
