# Funky Games API 实现

## 概述

本模块实现了Funky Games的GetBalance API接口，符合官方API文档规范。

## 文件结构

```
agentGame_1007_funky/
├── FunkyConstants.java          # Funky API常量定义
├── FunkyUtils.java              # Funky通用工具类
├── FunkyHeaderValidator.java    # 头信息验证工具类
├── ReqGetBalanceHandler.java    # GetBalance接口实现
├── ReqPlaceBetHandler.java      # PlaceBet接口实现
├── ReqCheckBetHandler.java      # CheckBet接口实现
├── FunkyGetBalanceTest.java     # GetBalance测试工具
├── FunkyPlaceBetTest.java       # PlaceBet测试工具
├── FunkyCheckBetTest.java       # CheckBet测试工具
└── README.md                    # 本文档
```

## API接口

### GetBalance - 获取玩家余额

**路径**: `/funky/User/GetBalance`  
**方法**: POST  
**描述**: 获取指定玩家的账户余额

#### 请求格式

```json
{
  "playerId": "12345",
  "sessionId": "session-123",
  "userAgent": "funky",
  "authentication": "23d828a8-0bba-49e8-90a6-9007bb50de1f",
  "xRequestId": "request-id-123"
}
```

#### 请求参数

| 参数名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| playerId | String | 是 | 玩家ID（至少3个字符） |
| sessionId | String | 是 | 会话ID |
| userAgent | String | 是 | 用户代理（从数据库配置获取） |
| authentication | String | 是 | 认证令牌（从数据库配置获取） |
| xRequestId | String | 是 | 请求ID，UUID格式，最多64字符 |

#### 响应格式

**成功响应**:
```json
{
  "errorCode": 0,
  "errorMessage": "Success",
  "data": {
    "balance": 100.50
  }
}
```

**错误响应**:
```json
{
  "errorCode": 400,
  "errorMessage": "Invalid Input",
  "data": null
}
```

#### 错误代码

| 错误代码 | 错误消息 | 描述 |
|----------|----------|------|
| 0 | Success | 成功 |
| 400 | Invalid Input | 无效输入 |
| 401 | Player Not Login | 玩家未登录 |
| 402 | Insufficient Balance | 余额不足 |
| 9999 | Internal Server Error | 内部服务器错误 |

完整的错误代码列表请参考 `FunkyConstants.ErrorCode` 枚举。

### PlaceBet - 玩家下注

**路径**: `/Funky/Bet/PlaceBet`
**方法**: POST
**描述**: 当玩家在游戏中下注时，Funky系统会发送此请求到运营商系统

#### 请求格式

```json
{
  "bet": {
    "gameCode": "GAME001",
    "refNo": "tx-1703123456789-AbC1234567",
    "stake": 10.50
  },
  "playerId": "12345",
  "sessionId": "session-1703123456789-AbC123456789",
  "playerIp": "127.0.0.1",
  "userAgent": "funky",
  "authentication": "23d828a8-0bba-49e8-90a6-9007bb50de1f",
  "xRequestId": "funky-1703123456789-AbC12345"
}
```

#### 请求参数

| 参数名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| bet | Object | 是 | 下注信息对象 |
| bet.gameCode | String | 是 | 游戏代码 |
| bet.refNo | String | 是 | 下注单号（唯一标识） |
| bet.stake | Number | 是 | 下注金额 |
| playerId | String | 是 | 玩家ID（至少3个字符） |
| sessionId | String | 是 | 会话ID |
| playerIp | String | 是 | 玩家IP地址 |
| userAgent | String | 是 | 用户代理（从数据库配置获取） |
| authentication | String | 是 | 认证令牌（从数据库配置获取） |
| xRequestId | String | 是 | 请求ID，UUID格式，最多64字符 |

#### 响应格式

**成功响应**:
```json
{
  "errorCode": 0,
  "errorMessage": "Success",
  "data": {
    "balance": 89.50
  }
}
```

**错误响应**:
```json
{
  "errorCode": 402,
  "errorMessage": "Insufficient Balance",
  "data": null
}
```

#### 业务逻辑

1. **重复下注检查**: 如果收到相同的refNo，返回首次下注时的响应
2. **余额验证**: 检查玩家余额是否足够支付下注金额
3. **余额扣除**: 成功下注后从玩家余额中扣除下注金额
4. **游戏记录**: 创建或更新游戏记录，记录下注信息
5. **通知系统**: 通知余额变更给相关系统

### CheckBet - 查询下注状态

**路径**: `/Funky/Bet/CheckBet`
**方法**: POST
**描述**: Funky系统查询运营商的下注状态

#### 请求格式

```json
{
  "id": "tx-1703123456789-AbC1234567",
  "playerId": "12345",
  "sessionId": "session-1703123456789-AbC123456789",
  "userAgent": "funky",
  "authentication": "23d828a8-0bba-49e8-90a6-9007bb50de1f",
  "xRequestId": "funky-1703123456789-AbC12345"
}
```

#### 请求参数

| 参数名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| id | String | 是 | 下注单号（对应PlaceBet的refNo） |
| playerId | String | 是 | 玩家ID（至少3个字符） |
| sessionId | String | 否 | 会话ID（可选） |
| userAgent | String | 是 | 用户代理（从数据库配置获取） |
| authentication | String | 是 | 认证令牌（从数据库配置获取） |
| xRequestId | String | 是 | 请求ID，UUID格式，最多64字符 |

#### 响应格式

**成功响应**:
```json
{
  "errorCode": 0,
  "errorMessage": "Success",
  "data": {
    "stake": 10.50,
    "winAmount": 15.75,
    "status": "W",
    "statementDate": "2023-12-21T10:30:00Z"
  }
}
```

**错误响应**:
```json
{
  "errorCode": 404,
  "errorMessage": "Bet Was Not Found",
  "data": null
}
```

#### 下注状态说明

| 状态 | 描述 | statementDate | winAmount |
|------|------|---------------|-----------|
| R | 下注仍在进行中 | 空字符串 | 0 |
| W | 玩家赢了 | 结算时间 | > stake |
| L | 玩家输了 | 结算时间 | < stake |
| C | 下注被取消 | 空字符串 | 0 |
| D | 平局 | 结算时间 | = stake |

#### 日期格式

statementDate支持以下格式：
- `yyyy-MM-dd`
- `yyyy-MM-ddThh:mm:ssZ`

## 工具类

### FunkyUtils - 通用工具类

提供Funky游戏相关的通用功能函数：

#### 随机ID生成
```java
// 生成X-Request-ID
String xRequestId = FunkyUtils.generateXRequestId();
// 输出示例: "funky-1703123456789-AbC12345"

// 生成简单随机ID
String simpleId = FunkyUtils.generateSimpleRandomId();
// 输出示例: "550e8400-e29b-41d4-a716-446655440000"

// 生成指定长度的随机字符串
String randomStr = FunkyUtils.generateRandomString(8);
// 输出示例: "AbC12345"

// 生成会话ID
String sessionId = FunkyUtils.generateSessionId();
// 输出示例: "session-1703123456789-AbC123456789"
```

#### 验证函数
```java
// 验证X-Request-ID格式
boolean isValid = FunkyUtils.isValidXRequestId("funky-123-abc");

// 验证玩家ID
boolean isValidPlayer = FunkyUtils.isValidPlayerId("12345");

// 验证会话ID
boolean isValidSession = FunkyUtils.isValidSessionId("session-123");

// 验证金额
boolean isValidAmount = FunkyUtils.isValidAmount(100.50);
```

### FunkyHeaderValidator - 头信息验证工具类

专门用于验证所有1007协议中必需的HTTP头信息，简化各个API的实现：

#### 基本验证
```java
// 验证所有必需的头信息
FunkyHeaderValidator.ValidationResult result =
    FunkyHeaderValidator.validateHeaders(paramsMap);

if (!result.isValid()) {
    // 处理验证失败
    FunkyConstants.ErrorCode errorCode = result.getErrorCode();
    String errorMessage = result.getErrorMessage();
}
```

#### 自动生成X-Request-ID
```java
// 验证头信息，如果X-Request-ID缺失或无效则自动生成
FunkyHeaderValidator.ValidationResult result =
    FunkyHeaderValidator.validateHeadersWithAutoGenerate(paramsMap);

if (result.isValid()) {
    String requestId = result.getRequestId();
    FunkyHeaderValidator.logRequestId(requestId, "GetBalance");
}
```

#### 简化验证
```java
// 只返回boolean结果
boolean isValid = FunkyHeaderValidator.isValidHeaders(paramsMap);

// 获取有效的请求ID
String requestId = FunkyHeaderValidator.getValidRequestId(paramsMap);
```

#### 在Handler中使用
```java
@Override
public void run() {
    // 1. 验证头信息（传入Handler类名以获取正确的数据库配置）
    FunkyHeaderValidator.ValidationResult headerValidation =
        FunkyHeaderValidator.validateHeadersWithAutoGenerate(paramsMap, this.getClass().getSimpleName());

    if (!headerValidation.isValid()) {
        buildErrorResponse(responseMap, headerValidation.getErrorCode());
        MsgUtil.responseHttp(responseMap, session);
        return;
    }

    // 2. 记录请求ID
    String requestId = headerValidation.getRequestId();
    FunkyHeaderValidator.logRequestId(requestId, "YourAPI");

    // 3. 继续业务逻辑...
}
```

#### 配置说明
认证信息现在从数据库动态获取：
```java
// 系统会自动从数据库获取配置
final C_BaseGamePlatform c_baseGamePlatform = DataAgentGameMrg.getInstance()
    .findC_BaseGamePlatformSupplierId(handlerClassName, AgentGame.FUNKY.getType());

String agent = c_baseGamePlatform.getAgent();        // User-Agent值
String auth = c_baseGamePlatform.getToken();         // Authentication值
```

## 使用示例

### cURL 示例

#### GetBalance API
```bash
curl -X POST http://localhost:8880/Funky/User/GetBalance \
  -H "Content-Type: application/json" \
  -d '{
    "playerId": "12345",
    "sessionId": "session-123",
    "userAgent": "funky",
    "authentication": "23d828a8-0bba-49e8-90a6-9007bb50de1f",
    "xRequestId": "test-request-123"
  }'
```

#### PlaceBet API
```bash
curl -X POST http://localhost:8880/Funky/Bet/PlaceBet \
  -H "Content-Type: application/json" \
  -d '{
    "bet": {
      "gameCode": "GAME001",
      "refNo": "tx-1703123456789-AbC1234567",
      "stake": 10.50
    },
    "playerId": "12345",
    "sessionId": "session-123",
    "playerIp": "127.0.0.1",
    "userAgent": "funky",
    "authentication": "23d828a8-0bba-49e8-90a6-9007bb50de1f",
    "xRequestId": "test-request-123"
  }'
```

#### CheckBet API
```bash
curl -X POST http://localhost:8880/Funky/Bet/CheckBet \
  -H "Content-Type: application/json" \
  -d '{
    "id": "tx-1703123456789-AbC1234567",
    "playerId": "12345",
    "sessionId": "session-123",
    "userAgent": "funky",
    "authentication": "23d828a8-0bba-49e8-90a6-9007bb50de1f",
    "xRequestId": "test-request-123"
  }'
```

### Java 示例

```java
// 使用HttpClient发送请求
HttpClient client = HttpClient.newHttpClient();

// 使用FunkyUtils生成随机ID
String xRequestId = FunkyUtils.generateXRequestId();
String sessionId = FunkyUtils.generateSessionId();

String json = String.format("""
{
  "playerId": "12345",
  "sessionId": "%s",
  "userAgent": "funky",
  "authentication": "23d828a8-0bba-49e8-90a6-9007bb50de1f",
  "xRequestId": "%s"
}
""", sessionId, xRequestId);

HttpRequest request = HttpRequest.newBuilder()
    .uri(URI.create("http://localhost:8880/funky/User/GetBalance"))
    .header("Content-Type", "application/json")
    .POST(HttpRequest.BodyPublishers.ofString(json))
    .build();

HttpResponse<String> response = client.send(request, HttpResponse.BodyHandlers.ofString());
System.out.println(response.body());
```

## 测试

### 使用测试工具

#### GetBalance 测试工具

访问测试接口来查看不同场景的示例：

```
http://localhost:8880/funky/test/getBalance
```

支持的测试类型：
- `?testType=success` - 成功场景
- `?testType=invalidAuth` - 认证失败场景
- `?testType=invalidPlayer` - 无效玩家场景
- `?testType=missingParams` - 缺少参数场景

#### PlaceBet 测试工具

访问PlaceBet测试接口：

```
http://localhost:8880/funky/test/placeBet
```

支持的测试类型：
- `?testType=success` - 成功下注场景
- `?testType=insufficientBalance` - 余额不足场景
- `?testType=duplicateBet` - 重复下注场景
- `?testType=invalidParams` - 无效参数场景

#### CheckBet 测试工具

访问CheckBet测试接口：

```
http://localhost:8880/funky/test/checkBet
```

支持的测试类型：
- `?testType=found` - 找到下注记录场景
- `?testType=notFound` - 未找到下注记录场景
- `?testType=running` - 下注进行中场景（状态R）
- `?testType=won` - 玩家赢了场景（状态W）
- `?testType=lost` - 玩家输了场景（状态L）
- `?testType=cancelled` - 下注取消场景（状态C）
- `?testType=draw` - 平局场景（状态D）

### 测试用例

#### GetBalance 测试用例

1. **成功场景测试**
   ```bash
   curl -X POST http://localhost:8880/Funky/User/GetBalance \
     -H "Content-Type: application/json" \
     -d '{"playerId": "12345", "sessionId": "session-123"}'
   ```

2. **认证失败测试**
   ```bash
   curl -X POST http://localhost:8880/Funky/User/GetBalance \
     -H "Content-Type: application/json" \
     -d '{"playerId": "12345", "sessionId": "session-123", "userAgent": "invalid"}'
   ```

#### PlaceBet 测试用例

1. **成功下注测试**
   ```bash
   curl -X POST http://localhost:8880/Funky/Bet/PlaceBet \
     -H "Content-Type: application/json" \
     -d '{
       "bet": {"gameCode": "GAME001", "refNo": "bet-123", "stake": 10.50},
       "playerId": "12345",
       "sessionId": "session-123",
       "playerIp": "127.0.0.1"
     }'
   ```

2. **余额不足测试**
   ```bash
   curl -X POST http://localhost:8880/Funky/Bet/PlaceBet \
     -H "Content-Type: application/json" \
     -d '{
       "bet": {"gameCode": "GAME001", "refNo": "bet-456", "stake": 999999.99},
       "playerId": "12345",
       "sessionId": "session-123",
       "playerIp": "127.0.0.1"
     }'
   ```

3. **重复下注测试**
   ```bash
   # 发送两次相同的refNo
   curl -X POST http://localhost:8880/Funky/Bet/PlaceBet \
     -H "Content-Type: application/json" \
     -d '{
       "bet": {"gameCode": "GAME001", "refNo": "SAME_REF_NO", "stake": 5.00},
       "playerId": "12345",
       "sessionId": "session-123",
       "playerIp": "127.0.0.1"
     }'
   ```

#### CheckBet 测试用例

1. **查询存在的下注**
   ```bash
   curl -X POST http://localhost:8880/Funky/Bet/CheckBet \
     -H "Content-Type: application/json" \
     -d '{
       "id": "tx-1703123456789-AbC1234567",
       "playerId": "12345",
       "sessionId": "session-123"
     }'
   ```

2. **查询不存在的下注**
   ```bash
   curl -X POST http://localhost:8880/Funky/Bet/CheckBet \
     -H "Content-Type: application/json" \
     -d '{
       "id": "NON_EXISTENT_BET_ID",
       "playerId": "12345"
     }'
   ```

3. **查询进行中的下注**
   ```bash
   curl -X POST http://localhost:8880/Funky/Bet/CheckBet \
     -H "Content-Type: application/json" \
     -d '{
       "id": "RUNNING_BET_ID",
       "playerId": "12345"
     }'
   ```

## 注意事项

### 头信息处理

由于当前架构限制，HTTP头信息验证通过请求体参数实现。在生产环境中，建议：

1. **网关层验证**: 在API网关或负载均衡器层验证HTTP头信息
2. **中间件处理**: 使用中间件将HTTP头信息转换为请求体参数
3. **架构升级**: 后续可考虑升级架构以支持直接的HTTP头信息访问

### 安全考虑

1. **认证令牌**: 生产环境中应使用动态生成的认证令牌
2. **IP白名单**: 建议配置IP白名单限制访问
3. **请求限流**: 实施请求频率限制防止滥用
4. **日志记录**: 记录所有API调用用于审计和监控

### 性能优化

1. **缓存策略**: 考虑对玩家余额进行适当缓存
2. **连接池**: 使用数据库连接池优化数据库访问
3. **异步处理**: 对于非关键操作考虑异步处理

## 扩展开发

### 添加新的API接口

1. 在 `FunkyConstants.Paths` 中添加新的路径常量
2. 创建新的Handler类继承 `HttpHandler`
3. 使用 `@IHandlerEntity` 注解指定路径
4. 实现业务逻辑并使用统一的错误处理

### 错误代码管理

所有错误代码统一在 `FunkyConstants.ErrorCode` 中管理，添加新错误代码时：

1. 在枚举中添加新的错误代码
2. 确保错误代码与官方文档一致
3. 提供清晰的错误消息
4. 更新相关文档

## 联系信息

如有问题或建议，请联系开发团队。
