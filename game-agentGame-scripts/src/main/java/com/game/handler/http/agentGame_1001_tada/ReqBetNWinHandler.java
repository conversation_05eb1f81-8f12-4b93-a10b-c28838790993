package com.game.handler.http.agentGame_1001_tada;

import com.game.engine.io.conf.ConstantConfig;
import com.game.engine.io.handler.HttpHandler;
import com.game.engine.math.BigDecimalUtils;
import com.game.engine.script.IHandlerEntity;
import com.game.engine.script.ScriptLoader;
import com.game.engine.utils.JsonUtils;
import com.game.engine.utils.MsgUtil;
import com.game.entity.game.GameNote;
import com.game.entity.player.Player;
import com.game.enums.AgentGame;
import com.game.enums.ChangeType;
import com.game.gamesr.NotifyData;
import com.game.gamesr.manager.AgentGameMrg;
import com.game.gamesr.scripts.IAgentGameScript;
import com.game.gamesr.utils.JWTUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.LinkedHashMap;
import java.util.Map;
import java.util.Objects;

/**
 * slots
 */
//http://127.0.0.1:8680/bet
@IHandlerEntity(path = "/tada/bet", desc = "下注")
public class ReqBetNWinHandler extends HttpHandler {
    private static final Logger LOGGER = LoggerFactory.getLogger(ReqBetNWinHandler.class);

    @Override
    public void run() {
        final Map<String, Object> responseMap = new LinkedHashMap<>();
        try {
            if (ConstantConfig.getInstance().isAgentDebug()) {
                LOGGER.info("ReqBetNWinHandler，params：{}", JsonUtils.writeAsJson(paramsMap));
            }

            final String reqId = (String) paramsMap.get("reqId");//玩家识别码
            final String token = (String) paramsMap.get("token");
            final long round = (long) paramsMap.get("round");//注单id
            final String currency = (String) paramsMap.get("currency");//货币
            final String betAmount = paramsMap.get("betAmount") + "";//押注金额
            final String winLoseAmount = paramsMap.get("winloseAmount") + "";//派彩

            final Map<String, Object> jwtMap = JWTUtils.verify(token);
            if (jwtMap.isEmpty() || !Objects.isNull(jwtMap.get("error"))) {
                responseMap.put("errorCode", 4);
                responseMap.put("message", "Token expired");
                responseMap.put("balance", 0);
                MsgUtil.responseHttp(responseMap, session);
                return;
            }

            final long playerId = (long) jwtMap.get("userId");
            final String playerName = (String) jwtMap.get("playerName");

            final Player player = AgentGameMrg.getInstance().findDbPlayer(playerId);
            if (player == null) {
                responseMap.put("errorCode", 5);
                responseMap.put("message", "Invalid Player");
                responseMap.put("username", playerName);
                responseMap.put("currency", currency);
                responseMap.put("balance", 0);
                MsgUtil.responseHttp(responseMap, session);
                return;
            }

            final boolean ipBlackList = ScriptLoader.getInstance().functionScript("AgentGameScript",
                    (IAgentGameScript script) -> script.isIpBlacklist(MsgUtil.getClientIp(session), AgentGame.TADA.getType()));
            if (!ipBlackList) {
                responseMap.put("errorCode", 5);
                responseMap.put("message", "Invalid Player");
                responseMap.put("username", playerName);
                responseMap.put("currency", currency);
                responseMap.put("balance", 0);
                MsgUtil.responseHttp(responseMap, session);
                return;
            }

            final double realBalance = ScriptLoader.getInstance().functionScript("AgentGameScript",
                    (IAgentGameScript script) -> script.realBalance(player));

            double balance = ScriptLoader.getInstance().functionScript("AgentGameScript",
                    (IAgentGameScript script) -> script.amountTransformUsd(player, realBalance));

            GameNote gameNote = AgentGameMrg.getInstance().findRoundIdGameNote(player, round + "");
            if (gameNote != null && gameNote.getTransactionIds().contains(reqId)) {
                responseMap.put("errorCode", 0);
                responseMap.put("message", "success");
                responseMap.put("username", playerName);
                responseMap.put("currency", currency);
                responseMap.put("balance", BigDecimalUtils.round(balance, 2));
                MsgUtil.responseHttp(responseMap, session);
                return;
            }

            if (Double.parseDouble(betAmount) > balance) {
                responseMap.put("errorCode", 2);
                responseMap.put("message", "Not enough balance");
                responseMap.put("username", playerName);
                responseMap.put("currency", currency);
                responseMap.put("balance", BigDecimalUtils.round(balance, 2));
                MsgUtil.responseHttp(responseMap, session);
                return;
            }

            balance = BigDecimalUtils.add(BigDecimalUtils.sub(balance, Double.parseDouble(betAmount), 2)
                    , Double.parseDouble(winLoseAmount), 2);

//            responseMap.put("errorCode", 0);
//            responseMap.put("message", "success");
//            responseMap.put("username", playerName);
//            responseMap.put("currency", currency);
//            responseMap.put("balance", BigDecimalUtils.round(balance, 2));
//            MsgUtil.responseHttp(responseMap, session);

            final double bet = ScriptLoader.getInstance().functionScript("AgentGameScript",
                    (IAgentGameScript script) -> script.usdTransformAmount(player, Double.parseDouble(betAmount)));

            final double winLose = ScriptLoader.getInstance().functionScript("AgentGameScript",
                    (IAgentGameScript script) -> script.usdTransformAmount(player, Double.parseDouble(winLoseAmount)));

            final double finalBalance = balance;
            final double balances = ScriptLoader.getInstance().functionScript("AgentGameScript",
                    (IAgentGameScript script) -> script.usdTransformAmount(player, finalBalance));

            if (paramsMap.get("freeSpinData") != null) {
                if (winLose > 0) {
                    ScriptLoader.getInstance().consumerScript("AgentGameScript",
                            (IAgentGameScript script) -> script.freeAddGameNote(player, round + "", reqId, 2, winLose, balances));
                }

                final NotifyData notifyData = new NotifyData();
                notifyData.setType(ChangeType.Free.getType())
                        .setPid(playerId)
                        .setWin(winLose)
                        .setFreeTimes(-1)
                        .setPlayerName(playerName)
                        .setCurrency(currency)
                        .setUpdSessionId(MsgUtil.getSessionID(session));
                ScriptLoader.getInstance().consumerScript("AgentGameScript",
                        (IAgentGameScript script) -> script.updateCurrency(notifyData));
            } else {
                if (gameNote == null) {
                    gameNote = ScriptLoader.getInstance().functionScript("AgentGameScript",
                            (IAgentGameScript script) -> script.addGameNote(player, round + "", reqId, 2, bet, bet, winLose, balances));
                } else {
                    gameNote = ScriptLoader.getInstance().functionScript("AgentGameScript",
                            (IAgentGameScript script) -> script.updateGameNote(player, round + "", reqId, 2, winLose, balances));
                }

                final NotifyData notifyData = new NotifyData();
                notifyData.setType(ChangeType.special.getType())
                        .setPid(playerId)
                        .setNoteId(gameNote.getNoteId() + "")
                        .setBetAmount(gameNote.getBetAmount())
                        .setValidBets(gameNote.getValidBets())
                        .setWin(winLose)
                        .setTotalWin(gameNote.getWin())
                        .setPlayerName(playerName)
                        .setCurrency(currency)
                        .setUpdSessionId(MsgUtil.getSessionID(session));
                ScriptLoader.getInstance().consumerScript("AgentGameScript",
                        (IAgentGameScript script) -> script.updateCurrency(notifyData));
            }
        } catch (Exception e) {
            LOGGER.error("ReqBetNWinHandler", e);
            responseMap.put("errorCode", 5);
            responseMap.put("message", "server error");
            MsgUtil.responseHttp(responseMap, session);
        }
    }

}
