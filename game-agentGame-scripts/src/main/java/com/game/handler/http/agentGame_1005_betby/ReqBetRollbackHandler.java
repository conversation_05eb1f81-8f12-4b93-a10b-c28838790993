package com.game.handler.http.agentGame_1005_betby;

import com.alibaba.fastjson.JSON;
import com.game.engine.io.conf.ConstantConfig;
import com.game.engine.io.handler.HttpHandler;
import com.game.engine.script.IHandlerEntity;
import com.game.engine.script.ScriptLoader;
import com.game.engine.utils.JsonUtils;
import com.game.engine.utils.MsgUtil;
import com.game.entity.game.GameNote;
import com.game.entity.player.Player;
import com.game.enums.ChangeType;
import com.game.enums.Currency;
import com.game.gamesr.NotifyData;
import com.game.gamesr.manager.AgentGameMrg;
import com.game.gamesr.scripts.IAgentGameScript;
import com.game.handler.http.agentGame_1005_betby.BetbyConstants.ValidationResult;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.Map;


@IHandlerEntity(path = "/betby/bet/rollback", desc = "投注回滚")
public class ReqBetRollbackHandler extends HttpHandler {

    private static final Logger LOGGER = LoggerFactory.getLogger(ReqBetRollbackHandler.class);

    // 请求参数类
    private static class BetRollbackRequest {
        // 由运营商分配的交易标识符，在 /bet_make 调用中接收
        private String betTransactionId;
        // 由运营商分配的上一交易的ID
        private String parentTransactionId;
        // 交易信息
        private TransactionInfo transaction;

        // Getter and Setter
    }

    // 交易信息类
    private static class TransactionInfo {
        // 由 Betby 分配的交易唯一编码
        private String id;
        // 由 Betby 分配的投注单唯一编码
        private String betslipId;
        // 由 Betby 分配的玩家唯一编码
        private String playerId;
        // 由 Betby 分配的运营商唯一编码
        private String operatorId;
        // 由 Betby 分配的运营商网站唯一编码
        private String operatorBrandId;
        // 由运营商端分配给玩家的唯一编码
        private String extPlayerId;
        // 交易的 Unix 时间戳
        private double timestamp;
        // 要从玩家余额中退还的金额，以分表示
        private int amount;
        // 货币代码
        private String currency;
        // 操作类型（回滚）
        private String operation;
        // Betby 奖金 ID（可选）
        private String bonusId;
        // 玩家货币与欧元的汇率
        private String crossRateEuro;
        // 由 Betby 分配的上一交易的ID
        private String parentTransactionId;

        // Getter and Setter
    }

    @Override
    public void run() {
        final Map<String, Object> responseMap = new LinkedHashMap<>();
        try {
            if (ConstantConfig.getInstance().isAgentDebug()) {
                LOGGER.info("betby BET_ROLLBACK request: {}", JsonUtils.writeAsJson(paramsMap));
            }

//            final boolean ipBlackList = ScriptLoader.getInstance().functionScript("AgentGameScript",
//                    (IAgentGameScript script) -> script.isIpBlacklist(MsgUtil.getClientIp(session), AgentGame.BETBY.getType()));
//            if (!ipBlackList) {
//                // 其他错误
//                responseMap.put("code", 3000);
//                MsgUtil.responseHttp(responseMap, session);
//                return;
//            }

            // 直接使用paramsMap解析请求
            final BetRollbackRequest request = parseRequest((String) paramsMap.get("payload"));

            // 验证请求
            ValidationResult validationResult = validateRequest(request);
            if (!validationResult.isSuccess()) {
                BetbyConstants.sendErrorResponse(session, validationResult.getErrorCode(), validationResult.getErrorMessage());
                return;
            }

            // 获取验证后的玩家对象（从validationResult中获取）
            Player player = validationResult.getPlayer();

            Currency currency = BetbyConstants.mapToCurrency(request.transaction.currency);
            // 检查货币是否支持
            if (currency == null) {
                LOGGER.warn("Unsupported currency: {}", request.transaction.currency);
                BetbyConstants.sendErrorResponse(session, 2002, "Unsupported currency: " + request.transaction.currency);
                return;
            }

            processRollbackRequest(request, player);
        } catch (Exception e) {
            LOGGER.error("ReqBetRollbackHandle error", e);
            BetbyConstants.sendErrorResponse(session, 2004, "Bad request");
        }
    }

    private BetRollbackRequest parseRequest(String payLoad) {
        try {
            Map<String, Object> requestMap = JwtVerifier.verifyAndDecodeJWT(payLoad);
            if (ConstantConfig.getInstance().isAgentDebug()) {
                LOGGER.info("betby BET_ROLLBACK payload: {}", requestMap);
            }

            @SuppressWarnings("unchecked")
            Map<String, Object> paramsMap = (Map<String, Object>) requestMap.get("payload");

            BetRollbackRequest request = new BetRollbackRequest();

            // 基本参数
            request.betTransactionId = (String) paramsMap.get("bet_transaction_id");
            request.parentTransactionId = (String) paramsMap.get("parent_transaction_id");

            // 解析transaction对象
            @SuppressWarnings("unchecked") final Map<String, Object> transaction = (Map<String, Object>) paramsMap.get("transaction");
            request.transaction = parseTransactionInfo(transaction);

            return request;
        } catch (Exception e) {
            LOGGER.error("Error parsing request: {}", e.getMessage());
            throw e;
        }
    }

    private TransactionInfo parseTransactionInfo(Map<String, Object> json) {
        TransactionInfo transaction = new TransactionInfo();

        transaction.id = (String) json.get("id");
        transaction.betslipId = (String) json.get("betslip_id");
        transaction.playerId = (String) json.get("player_id");
        transaction.operatorId = (String) json.get("operator_id");
        transaction.operatorBrandId = (String) json.get("operator_brand_id");
        transaction.extPlayerId = (String) json.get("ext_player_id");
        transaction.timestamp = (Double) json.get("timestamp");
        transaction.amount = (Integer) json.get("amount");
        transaction.currency = (String) json.get("currency");
        transaction.operation = (String) json.get("operation");
        transaction.bonusId = (String) json.get("bonus_id");
        transaction.crossRateEuro = (String) json.get("cross_rate_euro");
        transaction.parentTransactionId = (String) json.get("parent_transaction_id");

        return transaction;
    }

    private ValidationResult validateRequest(BetRollbackRequest request) {
        try {
            // 验证玩家是否存在
            final String playerId = BetbyConstants.getPlayerId(request.transaction.extPlayerId);
            final Player player = AgentGameMrg.getInstance().findDbPlayer(Long.parseLong(playerId));
            if (player == null) {
                return ValidationResult.failure(1006, "Player not found");
            }

            // 所有验证通过
            return ValidationResult.success(player);

        } catch (Exception e) {
            LOGGER.error("Validation error", e);
            return ValidationResult.failure(2004, "Validation error");
        }
    }

    private void processRollbackRequest(BetRollbackRequest request, Player player) {
        try {
            // 1. 查找是否存在重复回滚
            GameNote gameNote = AgentGameMrg.getInstance().findRoundIdGameNote(player, request.betTransactionId);
            if (gameNote == null) {
                LOGGER.error("Transaction not found: {}", request.betTransactionId);
                BetbyConstants.sendErrorResponse(session, 2004, "Transaction not found");
                return;
            }

            // 获取当前余额，用于构建响应
            final double realBalance = ScriptLoader.getInstance().functionScript("AgentGameScript",
                    (IAgentGameScript script) -> script.realBalance(player));
            final double balance = ScriptLoader.getInstance().functionScript("AgentGameScript",
                    (IAgentGameScript script) -> script.amountTransformUsd(player, realBalance));

            if (gameNote.getTransactionIds().contains(request.transaction.id)) {
                LOGGER.warn("Duplicate transaction detected: {}. Returning idempotent response.", request.transaction.id);

                final Map<String, Object> responseMap = new LinkedHashMap<>();
                int balanceInCents = (int) Math.round(balance * 100);
                int amountInCents = request.transaction.amount; // 使用请求中的金额

                responseMap.put("id", request.transaction.id);
                responseMap.put("ext_transaction_id", request.transaction.id);
                responseMap.put("parent_transaction_id", request.transaction.parentTransactionId);
                responseMap.put("user_id", request.transaction.extPlayerId);
                responseMap.put("operation", "rollback");
                responseMap.put("amount", amountInCents);
                responseMap.put("currency", request.transaction.currency);

                responseMap.put("balance", balanceInCents);

                MsgUtil.responseHttp(responseMap, session);
                return;
            }

            if (gameNote.isSportClose()) {
                LOGGER.warn("Transaction already settled: {}", request.betTransactionId);
                final Map<String, Object> responseMap = new LinkedHashMap<>();
                MsgUtil.responseHttp(responseMap, session);
                return;
            }

            double rollBackAmountTmp = request.transaction.amount / 100.0;  // 将分转为元

            double rollBackAmount;
            if (rollBackAmountTmp < balance) {
                // todo 要做禁止提款标记或者其他处理
                rollBackAmount = rollBackAmountTmp;
            } else {
                rollBackAmount = balance;
            }

            final double rollBackValue = ScriptLoader.getInstance().functionScript("AgentGameScript",
                    (IAgentGameScript script) -> script.usdTransformAmount(player, rollBackAmount));

            ScriptLoader.getInstance().functionScript("AgentGameScript",
                    (IAgentGameScript script) -> script.updateGameNote(player, gameNote.getRoundId(), request.transaction.id, 5, rollBackValue, realBalance));

            Map<String, Object> dataMap = new HashMap<>();
            dataMap.put("extPlayerId", request.transaction.extPlayerId);
            dataMap.put("parentTransactionId", request.transaction.parentTransactionId);
            dataMap.put("transactionAmount", request.transaction.amount);
            dataMap.put("transactionId", request.transaction.id);

            String dataJson = JSON.toJSONString(dataMap);

            final NotifyData notifyData = new NotifyData();
            notifyData.setType(ChangeType.Change.getType())
                    .setPid(player.getPlayerId())
                    .setPlayerName(player.getPlayerName())
                    .setNoteId(gameNote.getNoteId() + "")
                    .setCurrency(request.transaction.currency)
                    .setWin(rollBackValue)
                    .setData(dataJson)
                    .setUpdSessionId(MsgUtil.getSessionID(session));
            ScriptLoader.getInstance().consumerScript("AgentGameScript",
                    (IAgentGameScript script) -> script.updateCurrency(notifyData));
        } catch (Exception e) {
            LOGGER.error("Process rollback error", e);
            BetbyConstants.sendErrorResponse(session, 2004, "Server error");
        }
    }
}
