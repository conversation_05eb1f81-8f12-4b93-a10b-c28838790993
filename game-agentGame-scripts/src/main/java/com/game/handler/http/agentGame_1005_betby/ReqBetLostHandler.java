package com.game.handler.http.agentGame_1005_betby;

import com.alibaba.fastjson.JSON;
import com.game.engine.io.conf.ConstantConfig;
import com.game.engine.io.handler.HttpHandler;
import com.game.engine.script.IHandlerEntity;
import com.game.engine.script.ScriptLoader;
import com.game.engine.utils.JsonUtils;
import com.game.engine.utils.MsgUtil;
import com.game.entity.game.GameNote;
import com.game.entity.player.Player;
import com.game.enums.ChangeType;
import com.game.enums.Currency;
import com.game.gamesr.NotifyData;
import com.game.gamesr.manager.AgentGameMrg;
import com.game.gamesr.scripts.IAgentGameScript;
import com.game.handler.http.agentGame_1005_betby.BetbyConstants.ValidationResult;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.*;

@IHandlerEntity(path = "/betby/bet/lost", desc = "投注失败")
public class ReqBetLostHandler extends HttpHandler {

    private static final Logger LOGGER = LoggerFactory.getLogger(ReqBetLostHandler.class);

    // 请求类，用于保存接收到的请求参数
    private static class BetLostRequest {
        // 由运营商分配的唯一交易ID
        private String betTransactionId;
        // 转移金额，单位为分
        private int amount;
        // 货币代码，表示金额的币种
        private String currency;
        // 交易信息，包含交易的详细描述
        private TransactionInfo transaction;
        // 所有选择的状态，包含投注的每个选择（输/赢/进行中等）
        private List<SelectionItem> selections;

        // Getter and Setter
    }

    // 交易信息，描述每个交易的详细内容
    private static class TransactionInfo {
        // 由 Betby 分配的交易唯一编码
        private String id;
        // 由 Betby 在投注时分配的投注单唯一编码
        private String betslipId;
        // 由 Betby 分配的玩家唯一编码
        private String playerId;
        // 由 Betby 分配的运营商唯一编码
        private String operatorId;
        // 由 Betby 分配的运营商网站唯一编码
        private String operatorBrandId;
        // 在运营商端分配给玩家的唯一编码
        private String extPlayerId;
        // 交易的 Unix 时间戳，表示交易发生的时间
        private double timestamp;
        // 要从玩家余额退回的金额，单位为分
        private int amount;
        // 货币代码，表示金额的币种
        private String currency;
        // 操作类型，如投注、赢、退款
        private String operation;
        // 可选参数，Betby 奖金 ID
        private String bonusId;
        // 玩家货币到欧元的汇率，用于兑换金额
        private String crossRateEuro;
        // 由 Betby 分配的上一交易的 ID，用于追踪
        private String parentTransactionId;

        // Getter and Setter
    }

    // 选择项，用于保存每个选择的状态和赔率
    private static class SelectionItem {
        // 当 bet_make 请求处理时由 Betby 分配的投注唯一编码
        private String id;
        // 赛事的唯一编码，表示投注的具体事件
        private String eventId;
        // 选择的状态（输/赢/进行中等），用于标记选择的结果
        private String status;
        // 提供当前选择的赔率
        private String odds;

        // Getter and Setter
    }

    @Override
    public void run() {
        final Map<String, Object> responseMap = new LinkedHashMap<>();
        try {
            if (ConstantConfig.getInstance().isAgentDebug()) {
                LOGGER.info("betby BET_LOST request: {}", JsonUtils.writeAsJson(paramsMap));
            }

//            final boolean ipBlackList = ScriptLoader.getInstance().functionScript("AgentGameScript",
//                    (IAgentGameScript script) -> script.isIpBlacklist(MsgUtil.getClientIp(session), AgentGame.BETBY.getType()));
//            if (!ipBlackList) {
//                // 其他错误
//                responseMap.put("code", 3000);
//                MsgUtil.responseHttp(responseMap, session);
//                return;
//            }

            // 1. 解析请求参数
            final BetLostRequest request = parseRequest((String) paramsMap.get("payload"));

            // 2. 验证请求
            ValidationResult validationResult = validateRequest(request);
            if (!validationResult.isSuccess()) {
                BetbyConstants.sendErrorResponse(session, validationResult.getErrorCode(), validationResult.getErrorMessage());
                return;
            }

            Currency currency = BetbyConstants.mapToCurrency(request.currency);
            // 检查货币是否支持
            if (currency == null) {
                LOGGER.warn("Unsupported currency: {}", request.currency);
                BetbyConstants.sendErrorResponse(session, 2002, "Unsupported currency: " + request.currency);
                return;
            }

            // 3. 处理投注失败逻辑
            processLost(request, validationResult.getPlayer());
        } catch (Exception e) {
            LOGGER.error("ReqBetLostHandle error", e);
            BetbyConstants.sendErrorResponse(session, 2004, "Bad request");
        }
    }

    // 解析请求
    private BetLostRequest parseRequest(String payLoad) {
        try {
            Map<String, Object> requestMap = JwtVerifier.verifyAndDecodeJWT(payLoad);
            if (ConstantConfig.getInstance().isAgentDebug()) {
                LOGGER.info("betby BET_LOST payload: {}", requestMap);
            }

            @SuppressWarnings("unchecked")
            Map<String, Object> paramsMap = (Map<String, Object>) requestMap.get("payload");

            BetLostRequest request = new BetLostRequest();
            request.betTransactionId = (String) paramsMap.get("bet_transaction_id");
            request.amount = (Integer) paramsMap.get("amount");
            request.currency = (String) paramsMap.get("currency");

            // 解析 transaction 信息
            @SuppressWarnings("unchecked")
            Map<String, Object> transactionMap = (Map<String, Object>) paramsMap.get("transaction");
            request.transaction = parseTransactionInfo(transactionMap);

            // 解析 selections 信息
            @SuppressWarnings("unchecked")
            List<Map<String, Object>> selectionsList = (List<Map<String, Object>>) paramsMap.get("selections");
            request.selections = parseSelections(selectionsList);

            return request;
        } catch (Exception e) {
            LOGGER.error("Error parsing request: {}", e.getMessage());
            throw e;
        }
    }

    // 解析 Transaction 信息
    private TransactionInfo parseTransactionInfo(Map<String, Object> transactionMap) {
        TransactionInfo transaction = new TransactionInfo();
        transaction.id = (String) transactionMap.get("id");
        transaction.betslipId = (String) transactionMap.get("betslip_id");
        transaction.playerId = (String) transactionMap.get("player_id");
        transaction.operatorId = (String) transactionMap.get("operator_id");
        transaction.operatorBrandId = (String) transactionMap.get("operator_brand_id");
        transaction.extPlayerId = (String) transactionMap.get("ext_player_id");
        transaction.timestamp = (Double) transactionMap.get("timestamp");
        transaction.amount = (Integer) transactionMap.get("amount");
        transaction.currency = (String) transactionMap.get("currency");
        transaction.operation = (String) transactionMap.get("operation");
        transaction.bonusId = (String) transactionMap.get("bonus_id");
        transaction.crossRateEuro = (String) transactionMap.get("cross_rate_euro");
        transaction.parentTransactionId = (String) transactionMap.get("parent_transaction_id");

        return transaction;
    }

    // 解析 Selections 信息
    private List<SelectionItem> parseSelections(List<Map<String, Object>> selectionsList) {
        List<SelectionItem> selections = new ArrayList<>();
        for (Map<String, Object> selectionMap : selectionsList) {
            SelectionItem selection = new SelectionItem();
            selection.id = (String) selectionMap.get("id");
            selection.eventId = (String) selectionMap.get("event_id");
            selection.status = (String) selectionMap.get("status");
            selection.odds = (String) selectionMap.get("odds");
            selections.add(selection);
        }
        return selections;
    }

    // 验证请求
    private ValidationResult validateRequest(BetLostRequest request) {
        try {
            // 验证 bet_transaction_id 和其他必要的字段
            if (request.betTransactionId == null || request.transaction == null) {
                return ValidationResult.failure(2004, "Invalid request parameters");
            }

            // 验证玩家是否存在
            final String playerId = BetbyConstants.getPlayerId(request.transaction.extPlayerId);
            final Player player = AgentGameMrg.getInstance().findDbPlayer(Long.parseLong(playerId));
            if (player == null) {
                LOGGER.warn("Player not found: {}", playerId);
                return ValidationResult.failure(1006, "Player not found");
            }

            return ValidationResult.success(player);
        } catch (Exception e) {
            LOGGER.error("Validation error", e);
            return ValidationResult.failure(2004, "Validation error");
        }
    }

    // 处理投注失败
    private void processLost(BetLostRequest request, Player player) {
        try {
            // 查找相应的游戏记录
            GameNote gameNote = AgentGameMrg.getInstance().findRoundIdGameNote(player, request.betTransactionId);
            if (gameNote == null) {
                LOGGER.warn("bet transaction id not found: {}", request.betTransactionId);
                BetbyConstants.sendErrorResponse(session, 2004, "bet transaction id not found");
                return;
            }

            player.setBonus(gameNote.isBonus());
            player.setPlayerCurrencyId(gameNote.getCurrencyId());
            player.setGameCurrencyId(gameNote.getGameCurrencyId());
            // 获取当前余额
            final double realBalance = ScriptLoader.getInstance().functionScript("AgentGameScript",
                    (IAgentGameScript script) -> script.realBalance(player));

            final double balance = ScriptLoader.getInstance().functionScript("AgentGameScript",
                    (IAgentGameScript script) -> script.amountTransformUsd(player, realBalance));

            // 检查是否是重复请求
            if (gameNote.getTransactionIds().contains(request.transaction.id)) {
                LOGGER.warn("Duplicate transaction detected: {}. Returning idempotent response.", request.transaction.id);

                // 直接构建响应并发送，不调用updateCurrency
                final Map<String, Object> responseMap = new LinkedHashMap<>();
                int balanceInCents = (int) Math.round(balance * 100);

                // 构建与handleWinUpdateCurrency相同格式的响应
                responseMap.put("id", request.transaction.id);
                responseMap.put("ext_transaction_id", request.transaction.id);
                responseMap.put("parent_transaction_id", request.transaction.parentTransactionId);
                responseMap.put("user_id", request.transaction.extPlayerId);
                responseMap.put("operation", "lost");
                responseMap.put("balance", balanceInCents);

                MsgUtil.responseHttp(responseMap, session);
                return;
            }

            // 转换SelectionItem为Map格式
            List<Map<String, Object>> selectionsMapList = new ArrayList<>();
            for (SelectionItem selection : request.selections) {
                Map<String, Object> selectionMap = new HashMap<>();
                selectionMap.put("id", selection.getId());
                selectionMap.put("event_id", selection.getEventId());
                selectionMap.put("status", selection.getStatus());
                selectionsMapList.add(selectionMap);
            }

            // 生成Lost接口的remark信息
            String betRemark = BetbyConstants.generateLostRemark(selectionsMapList);

            // 更新游戏记录，添加当前交易ID
            GameNote addGameNote = ScriptLoader.getInstance().functionScript("AgentGameScript",
                    (IAgentGameScript script) -> script.updateGameNote(player, gameNote.getRoundId(), request.transaction.id, 2, 0, realBalance, betRemark));

            // 构建通知数据并执行货币更新（包含响应发送）
            Map<String, Object> dataMap = new HashMap<>();
            dataMap.put("extPlayerId", request.transaction.extPlayerId);
            dataMap.put("parentTransactionId", request.transaction.parentTransactionId);
            dataMap.put("operation", request.transaction.operation);
            dataMap.put("transactionId", request.transaction.id);

            String dataJson = JSON.toJSONString(dataMap);

            final NotifyData notifyData = new NotifyData();
            notifyData.setType(ChangeType.win.getType())
                    .setPid(player.getPlayerId())
                    .setPlayerName(player.getPlayerName())
                    .setNoteId(gameNote.getNoteId() + "")  // 使用当前交易ID
                    .setCurrency(request.currency)
                    .setBetAmount(addGameNote.getBetAmount())
                    .setValidBets(addGameNote.getValidBets())
                    .setWin(0)
                    .setTotalWin(0)
                    .setData(dataJson)
                    .setUpdSessionId(MsgUtil.getSessionID(session))
                    .setCurrencyId(addGameNote.getCurrencyId())
                    .setGameId(addGameNote.getGameId());

            // 执行货币更新并发送响应
            ScriptLoader.getInstance().consumerScript("AgentGameScript",
                    (IAgentGameScript script) -> script.updateCurrency(notifyData));

        } catch (Exception e) {
            LOGGER.error("Lost processing error", e);
            BetbyConstants.sendErrorResponse(session, 2004, "Lost processing failed");
        }
    }
}
