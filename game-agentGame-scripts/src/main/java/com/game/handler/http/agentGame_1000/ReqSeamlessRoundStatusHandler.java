//package com.game.handler.http.agentGame_1000;
//
//import com.game.c_entity.middleplatform.C_BaseGamePlatform;
//import com.game.dao.game.GameNoteDao;
//import com.game.engine.io.handler.HttpHandler;
//import com.game.engine.script.IHandlerEntity;
//import com.game.engine.script.ScriptLoader;
//import com.game.engine.utils.MsgUtil;
//import com.game.engine.utils.TimeUtil;
//import com.game.entity.game.GameNote;
//import com.game.gamesr.manager.DataAgentGameMrg;
//import com.game.gamesr.scripts.IAgentGameLogicScript;
//import com.game.handler.tcp.agentGame.AgentGameConstant;
//import com.game.manager.EntityDaoMrg;
//import io.netty.util.internal.StringUtil;
//import org.slf4j.Logger;
//import org.slf4j.LoggerFactory;
//
//import java.util.LinkedHashMap;
//import java.util.Map;
//
////http://127.0.0.1:8680/seamless/round_status
//@IHandlerEntity(path = "/seamless/round_status", desc = "查看订单状态")
//public class ReqSeamlessRoundStatusHandler extends HttpHandler {
//    private static final Logger LOGGER = LoggerFactory.getLogger(ReqSeamlessRoundStatusHandler.class);
//
//    @Override
//    public void run() {
//        try {
//            final boolean isIpBlacklist = ScriptLoader.getInstance().functionScript("AgentGameLogic_1000_Script", (IAgentGameLogicScript script) -> script.isIpBlacklist(MsgUtil.getClientIp(session)));
//            if (!isIpBlacklist) {
//                return;
//            }
//
//            final String round = (String) paramsMap.get("round");//下注编码
//
//            final Map<String, Object> responseMap = new LinkedHashMap<>();
//            if (StringUtil.isNullOrEmpty(round)) {
//                responseMap.put("message", "INVALID_ARGUMENT");
//                responseMap.put("timestamp", TimeUtil.currentTimeMillis());
//                MsgUtil.responseHttp(responseMap, session);
//                return;
//            }
//
//            final GameNote gameNote = EntityDaoMrg.getInstance().getDao(GameNoteDao.class).findByBetIdGameNote(round);
//            if (gameNote == null) {
//                LOGGER.warn("gameNote，betId：{}，not exist", round);
//                responseMap.put("message", "INVALID_ARGUMENT");
//                responseMap.put("timestamp", TimeUtil.currentTimeMillis());
//                MsgUtil.responseHttp(responseMap, session);
//                return;
//            }
//
//            String status = "";
//            if (gameNote.getStatus() == 1) {
//                status = "settled";
//            } else if (gameNote.getStatus() == 2) {
//                status = "cancelled";
//            } else {
//                status = "running";
//            }
//
//            responseMap.put("status", status);
//            responseMap.put("timestamp", TimeUtil.currentTimeMillis());
//            MsgUtil.responseHttp(responseMap, session);
//        } catch (Exception e) {
//            LOGGER.error("ReqSeamlessRoundStatusHandler", e);
//        }
//    }
//
//}
