package com.game.handler.http.agentGame_1007_funky;

import com.alibaba.fastjson.JSONObject;
import com.game.engine.io.conf.ConstantConfig;
import com.game.engine.io.handler.HttpHandler;
import com.game.engine.math.BigDecimalUtils;
import com.game.engine.script.IHandlerEntity;
import com.game.engine.script.ScriptLoader;
import com.game.engine.utils.MsgUtil;
import com.game.entity.game.GameNote;
import com.game.entity.player.Player;
import com.game.enums.AgentGame;
import com.game.enums.ChangeType;
import com.game.enums.Currency;
import com.game.gamesr.NotifyData;
import com.game.gamesr.manager.AgentGameMrg;
import com.game.gamesr.scripts.IAgentGameScript;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.LinkedHashMap;
import java.util.Map;

@IHandlerEntity(path = FunkyConstants.Paths.CANCEL_BET, desc = "Funky取消下注接口")
public class ReqCancelBetHandler extends HttpHandler {
    private static final Logger LOGGER = LoggerFactory.getLogger(ReqCancelBetHandler.class);

    /**
     * 取消下注请求数据 - 严格按照官方文档定义
     */
    private static class CancelBetRequest {
        /**
         * 玩家唯一ID，至少3个字符
         */
        private String playerId;
        /**
         * 投注参考号，始终唯一
         */
        private String refNo;
        /**
         * 用户会话ID（可选）
         */
        private String sessionId;
        /**
         * 游戏代码（可选，当前服务不可用）
         */
        private String gameCode;
        /**
         * 游戏提供商（可选，当前服务不可用）
         */
        private String gameProvider;
        /**
         * X-Request-ID (从HTTP头中获取)
         */
        private String xRequestId;

        // getter/setter
        public String getPlayerId() { return playerId; }
        public void setPlayerId(String playerId) { this.playerId = playerId; }
        public String getRefNo() { return refNo; }
        public void setRefNo(String refNo) { this.refNo = refNo; }
        public String getSessionId() { return sessionId; }
        public void setSessionId(String sessionId) { this.sessionId = sessionId; }
        public String getGameCode() { return gameCode; }
        public void setGameCode(String gameCode) { this.gameCode = gameCode; }
        public String getGameProvider() { return gameProvider; }
        public void setGameProvider(String gameProvider) { this.gameProvider = gameProvider; }
        public String getXRequestId() { return xRequestId; }
        public void setXRequestId(String xRequestId) { this.xRequestId = xRequestId; }
    }

    /**
     * 校验结果类
     */
    private static class ValidationResult {
        private boolean success;
        private Player player;
        private FunkyConstants.ErrorCode errorCode;
        private String errorMessage;

        private ValidationResult(boolean success, Player player, FunkyConstants.ErrorCode errorCode, String errorMessage) {
            this.success = success;
            this.player = player;
            this.errorCode = errorCode;
            this.errorMessage = errorMessage;
        }

        public static ValidationResult success(Player player) {
            return new ValidationResult(true, player, null, null);
        }

        public static ValidationResult failure(FunkyConstants.ErrorCode errorCode, String errorMessage) {
            return new ValidationResult(false, null, errorCode, errorMessage);
        }

        public boolean isSuccess() { return success; }
        public Player getPlayer() { return player; }
        public FunkyConstants.ErrorCode getErrorCode() { return errorCode; }
        public String getErrorMessage() { return errorMessage; }
    }

    @Override
    public void run() {
        try {
            // 获取请求参数
            final Map<String, Object> paramsMap = getParamsMap();
            if (ConstantConfig.getInstance().isAgentDebug()) {
                LOGGER.info("ReqCancelBetHandler params: {}", paramsMap);
            }

            // 1. 解析请求
            CancelBetRequest request = parseRequest(paramsMap);
            if (request == null) {
                FunkyUtils.sendErrorResponse(session, FunkyConstants.ErrorCode.INVALID_INPUT);
                return;
            }

            // 2. 校验请求
            ValidationResult validationResult = validateRequest(request);
            if (!validationResult.isSuccess()) {
                FunkyUtils.sendErrorResponse(session, validationResult.getErrorCode());
                return;
            }

            Player player = validationResult.getPlayer();

            // 3. 处理业务逻辑
            processCancelBet(request, player);

        } catch (Exception e) {
            LOGGER.error("ReqCancelBetHandler error", e);
            FunkyUtils.sendErrorResponse(session, FunkyConstants.ErrorCode.INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * 解析请求参数
     *
     * @param paramsMap 请求参数
     * @return 解析成功返回CancelBetRequest对象，失败返回null
     */
    private CancelBetRequest parseRequest(Map<String, Object> paramsMap) {
        try {
            CancelBetRequest request = new CancelBetRequest();

            // playerId是必需的
            String playerId = (String) paramsMap.get("playerId");
            if (playerId == null) {
                LOGGER.warn("Missing required playerId parameter");
                return null;
            }
            request.setPlayerId(playerId);

            // refNo是必需的
            String refNo = (String) paramsMap.get("refNo");
            if (refNo == null) {
                LOGGER.warn("Missing required refNo parameter");
                return null;
            }
            request.setRefNo(refNo);

            // 可选字段
            request.setSessionId((String) paramsMap.get("sessionId"));
            request.setGameCode((String) paramsMap.get("gameCode"));
            request.setGameProvider((String) paramsMap.get("gameProvider"));

            // 从HTTP头中获取X-Request-ID
            request.setXRequestId((String) paramsMap.get("xRequestId"));

            return request;

        } catch (Exception e) {
            LOGGER.error("Error parsing request", e);
            return null;
        }
    }

    /**
     * 校验请求参数和权限
     *
     * @param request 解析后的请求对象
     * @return 校验结果
     */
    private ValidationResult validateRequest(CancelBetRequest request) {
        try {
            // 1. 验证请求头信息
            final Map<String, Object> paramsMap = getParamsMap();
            if (FunkyHeaderValidator.hasInvalidHeaders(paramsMap)) {
                return ValidationResult.failure(FunkyConstants.ErrorCode.PLAYER_NOT_LOGIN, "Invalid headers");
            }

            // 2. 验证必要参数
            if (request.getPlayerId() == null || request.getPlayerId().trim().isEmpty()) {
                LOGGER.warn("Missing required playerId parameter");
                return ValidationResult.failure(FunkyConstants.ErrorCode.INVALID_INPUT, "Missing playerId");
            }

            if (FunkyUtils.isInvalidPlayerId(request.getPlayerId())) {
                LOGGER.warn("Invalid playerId format: {} (must be at least 3 characters)", request.getPlayerId());
                return ValidationResult.failure(FunkyConstants.ErrorCode.INVALID_INPUT, "Invalid playerId format");
            }

            if (request.getRefNo() == null || request.getRefNo().trim().isEmpty()) {
                LOGGER.warn("Missing required refNo parameter");
                return ValidationResult.failure(FunkyConstants.ErrorCode.INVALID_INPUT, "Missing refNo");
            }

            // 3. 查找玩家
            long playerIdLong;
            try {
                playerIdLong = Long.parseLong(request.getPlayerId().trim());
            } catch (NumberFormatException e) {
                LOGGER.warn("Invalid playerId format: {}", request.getPlayerId());
                return ValidationResult.failure(FunkyConstants.ErrorCode.INVALID_INPUT, "Invalid playerId format");
            }

            final Player player = AgentGameMrg.getInstance().findDbPlayer(playerIdLong);
            if (player == null) {
                return ValidationResult.failure(FunkyConstants.ErrorCode.PLAYER_NOT_LOGIN, "Player not found");
            }

            // 4. IP黑名单检查
//            final boolean ipBlackList = ScriptLoader.getInstance().functionScript("AgentGameScript",
//                    (IAgentGameScript script) -> script.isIpBlacklist(MsgUtil.getClientIp(session), AgentGame.FUNKY.getType()));
//            if (!ipBlackList) {
//                return ValidationResult.failure(FunkyConstants.ErrorCode.PLAYER_NOT_LOGIN, "IP blacklisted");
//            }

            return ValidationResult.success(player);

        } catch (Exception e) {
            LOGGER.error("Validation error", e);
            return ValidationResult.failure(FunkyConstants.ErrorCode.INTERNAL_SERVER_ERROR, "Validation error");
        }
    }

    /**
     * 处理取消下注业务逻辑
     *
     * @param request 已验证的取消请求
     * @param player 已验证的玩家对象
     */
    private void processCancelBet(CancelBetRequest request, Player player) {
        // 记录X-Request-ID用于日志追踪
        LOGGER.info("Processing CancelBet request - X-Request-ID: {}, refNo: {}, playerId: {}",
            request.getXRequestId(), request.getRefNo(), request.getPlayerId());

        // 1. 查找下注记录
        GameNote gameNote = AgentGameMrg.getInstance().findRoundIdGameNote(player, request.getRefNo());
        if (gameNote == null) {
            LOGGER.warn("Bet not found: refNo={}, playerId={}", request.getRefNo(), request.getPlayerId());
            FunkyUtils.sendErrorResponse(session, FunkyConstants.ErrorCode.BET_WAS_NOT_FOUND);
            return;
        }

        // 已经结算的不能取消
        if (gameNote.getStatus() == 2) {
            LOGGER.warn("Settlement cancel transaction detected: {}. Returning idempotent response.", request.getXRequestId());

            final JSONObject data = new JSONObject();
            data.put("refNo", request.getRefNo());

            FunkyUtils.sendSuccessResponse(session, data, FunkyConstants.ErrorCode.BET_ALREADY_SETTLED);
            return;
        }

        // 已经取消的不能取消
        if (gameNote.getStatus() == 3) {
            LOGGER.warn("Duplicate cancel transaction detected: {}. Returning idempotent response.", request.getXRequestId());

            final JSONObject data = new JSONObject();
            data.put("refNo", request.getRefNo());

            FunkyUtils.sendSuccessResponse(session, data, FunkyConstants.ErrorCode.BET_WAS_NOT_FOUND);
            return;
        }

        // 7. 取消游戏记录
        ScriptLoader.getInstance().consumerScript("AgentGameScript",
                (IAgentGameScript script) -> script.updateGameNoteStatus(player, request.getRefNo(), 3));

        // 获取货币名称
        Currency currency = Currency.valueOf(gameNote.getCurrencyId());
        String currencyName = "";
        if (currency != null) {
            currencyName = currency.name();
        }

        // 8. 通知余额变更
        NotifyData notifyData = new NotifyData();
        notifyData.setType(ChangeType.refund.getType())
                .setPid(player.getPlayerId())
                .setPlayerName(request.getPlayerId())
                .setNoteId(gameNote.getNoteId() + "")
                .setCurrency(currencyName)
                .setBetAmount(gameNote.getBetAmount())
                .setWin(gameNote.getWin())
                .setData(request.getRefNo())
                .setUpdSessionId(MsgUtil.getSessionID(session));

        ScriptLoader.getInstance().consumerScript("AgentGameScript",
                (IAgentGameScript script) -> script.updateCurrency(notifyData));
    }
}
