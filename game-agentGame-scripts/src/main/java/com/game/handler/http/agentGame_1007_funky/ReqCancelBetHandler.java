package com.game.handler.http.agentGame_1007_funky;

import com.alibaba.fastjson.JSONObject;
import com.game.engine.io.conf.ConstantConfig;
import com.game.engine.io.handler.HttpHandler;
import com.game.engine.math.BigDecimalUtils;
import com.game.engine.script.IHandlerEntity;
import com.game.engine.script.ScriptLoader;
import com.game.engine.utils.MsgUtil;
import com.game.entity.game.GameNote;
import com.game.entity.player.Player;
import com.game.enums.AgentGame;
import com.game.enums.ChangeType;
import com.game.enums.Currency;
import com.game.gamesr.NotifyData;
import com.game.gamesr.manager.AgentGameMrg;
import com.game.gamesr.scripts.IAgentGameScript;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.LinkedHashMap;
import java.util.Map;

@IHandlerEntity(path = FunkyConstants.Paths.CANCEL_BET, desc = "Funky取消下注接口")
public class ReqCancelBetHandler extends HttpHandler {
    private static final Logger LOGGER = LoggerFactory.getLogger(ReqCancelBetHandler.class);

    /**
     * 取消下注请求数据 - 严格按照官方文档定义
     */
    private static class CancelBetRequest {
        /**
         * 玩家唯一ID，至少3个字符
         */
        private String playerId;
        /**
         * 投注参考号，始终唯一
         */
        private String refNo;
        /**
         * 用户会话ID（可选）
         */
        private String sessionId;
        /**
         * 游戏代码（可选，当前服务不可用）
         */
        private String gameCode;
        /**
         * 游戏提供商（可选，当前服务不可用）
         */
        private String gameProvider;

        // getter/setter
        public String getPlayerId() { return playerId; }
        public void setPlayerId(String playerId) { this.playerId = playerId; }
        public String getRefNo() { return refNo; }
        public void setRefNo(String refNo) { this.refNo = refNo; }
        public String getSessionId() { return sessionId; }
        public void setSessionId(String sessionId) { this.sessionId = sessionId; }
        public String getGameCode() { return gameCode; }
        public void setGameCode(String gameCode) { this.gameCode = gameCode; }
        public String getGameProvider() { return gameProvider; }
        public void setGameProvider(String gameProvider) { this.gameProvider = gameProvider; }
    }

    /**
     * 校验结果类
     */
    private static class ValidationResult {
        private boolean success;
        private Player player;
        private FunkyConstants.ErrorCode errorCode;
        private String errorMessage;

        private ValidationResult(boolean success, Player player, FunkyConstants.ErrorCode errorCode, String errorMessage) {
            this.success = success;
            this.player = player;
            this.errorCode = errorCode;
            this.errorMessage = errorMessage;
        }

        public static ValidationResult success(Player player) {
            return new ValidationResult(true, player, null, null);
        }

        public static ValidationResult failure(FunkyConstants.ErrorCode errorCode, String errorMessage) {
            return new ValidationResult(false, null, errorCode, errorMessage);
        }

        public boolean isSuccess() { return success; }
        public Player getPlayer() { return player; }
        public FunkyConstants.ErrorCode getErrorCode() { return errorCode; }
        public String getErrorMessage() { return errorMessage; }
    }

    @Override
    public void run() {
        final Map<String, Object> responseMap = new LinkedHashMap<>();
        try {
            // 获取请求参数和头信息
            final Map<String, Object> paramsMap = getParamsMap();
            if (ConstantConfig.getInstance().isAgentDebug()) {
                LOGGER.info("ReqCancelBetHandler params: {}", paramsMap);
            }

            // 1. 验证请求头信息
            if (FunkyHeaderValidator.hasInvalidHeaders(paramsMap)) {
                FunkyUtils.sendErrorResponse(session, FunkyConstants.ErrorCode.PLAYER_NOT_LOGIN);
                return;
            }

            // 2. 获取请求体参数
            final String playerId = (String) paramsMap.get("playerId");
            final String refNo = (String) paramsMap.get("refNo");
            final String sessionId = (String) paramsMap.get("sessionId");  // 可选参数

            // 3. 验证必要参数
            if (playerId == null || playerId.trim().isEmpty()) {
                LOGGER.warn("Missing required playerId parameter");
                FunkyUtils.sendErrorResponse(session, FunkyConstants.ErrorCode.INVALID_INPUT);
                return;
            }

            // 根据官方文档，playerId至少需要3个字符
            if (!FunkyUtils.isValidPlayerId(playerId)) {
                LOGGER.warn("Invalid playerId format: {} (must be at least 3 characters)", playerId);
                FunkyUtils.sendErrorResponse(session, FunkyConstants.ErrorCode.INVALID_INPUT);
                return;
            }

            if (refNo == null || refNo.trim().isEmpty()) {
                LOGGER.warn("Missing required refNo parameter");
                FunkyUtils.sendErrorResponse(session, FunkyConstants.ErrorCode.INVALID_INPUT);
                return;
            }

            // 4. 查找玩家
            long playerIdLong;
            try {
                playerIdLong = Long.parseLong(playerId.trim());
            } catch (NumberFormatException e) {
                LOGGER.warn("Invalid playerId format: {}", playerId);
                FunkyUtils.sendErrorResponse(session, FunkyConstants.ErrorCode.INVALID_INPUT);
                return;
            }

            final Player player = AgentGameMrg.getInstance().findDbPlayer(playerIdLong);
            if (player == null) {
                FunkyUtils.sendErrorResponse(session, FunkyConstants.ErrorCode.PLAYER_NOT_LOGIN);
                return;
            }

            // 5. IP黑名单检查
//            final boolean ipBlackList = ScriptLoader.getInstance().functionScript("AgentGameScript",
//                    (IAgentGameScript script) -> script.isIpBlacklist(MsgUtil.getClientIp(session), AgentGame.FUNKY.getType()));
//            if (!ipBlackList) {
//                FunkyUtils.sendErrorResponse(session, FunkyConstants.ErrorCode.PLAYER_NOT_LOGIN);
//                return;
//            }

            // 6. 查找下注记录 - 使用修正后的方法
            GameNote gameNote = AgentGameMrg.getInstance().findRoundIdGameNote(player, refNo);
            if (gameNote == null) {
                LOGGER.warn("Bet not found: refNo={}, playerId={}", refNo, playerId);
                FunkyUtils.sendErrorResponse(session, FunkyConstants.ErrorCode.BET_WAS_NOT_FOUND);
                return;
            }

            // 7. 检查下注是否已经结算
            // 根据官方文档：已成功结算的下注不能通过取消下注API取消
            if (gameNote.getStatus() == 2) { // 假设2表示已结算
                LOGGER.warn("Cannot cancel settled bet: refNo={}, playerId={}", refNo, playerId);
                FunkyUtils.sendErrorResponse(session, FunkyConstants.ErrorCode.BET_ALREADY_SETTLED);
                return;
            }

            // 8. 检查下注是否已经取消
            if (gameNote.getStatus() == 3) { // 假设3表示已取消
                LOGGER.warn("Bet already cancelled: refNo={}, playerId={}", refNo, playerId);
                // 返回成功响应，因为下注已经是取消状态
                buildSuccessResponse(responseMap, refNo);
                MsgUtil.responseHttp(responseMap, session);
                return;
            }

            // 9. 获取下注金额用于退还
            final double betAmount = ScriptLoader.getInstance().functionScript("AgentGameScript",
                    (IAgentGameScript script) -> script.amountTransformUsd(player, gameNote.getBetAmount()));

            // 10. 获取当前余额
            final double realBalance = ScriptLoader.getInstance().functionScript("AgentGameScript",
                    (IAgentGameScript script) -> script.realBalance(player));
            final double currentBalance = ScriptLoader.getInstance().functionScript("AgentGameScript",
                    (IAgentGameScript script) -> script.amountTransformUsd(player, realBalance));

            // 11. 计算退还后余额
            final double finalBalance = BigDecimalUtils.add(currentBalance, betAmount, 2);
            final double balances = ScriptLoader.getInstance().functionScript("AgentGameScript",
                    (IAgentGameScript script) -> script.usdTransformAmount(player, finalBalance));

            // 12. 取消游戏记录
            ScriptLoader.getInstance().consumerScript("AgentGameScript",
                    (IAgentGameScript script) -> script.updateGameNoteStatus(player, refNo, 3));

            // 获取货币名称
            Currency currency = Currency.valueOf(gameNote.getCurrencyId());
            String currencyName = "";
            if (currency != null) {
                currencyName = currency.name();
            }


            // 13. 通知余额变更
            NotifyData notifyData = new NotifyData();
            notifyData.setType(ChangeType.refund.getType())
                    .setPid(player.getPlayerId())
                    .setPlayerName(player.getPlayerName())
                    .setNoteId(gameNote.getNoteId() + "")//refNo
                    .setBetAmount(-gameNote.getBetAmount()) // 负数表示退还
                    .setValidBets(-gameNote.getValidBets()) // 负数表示退还
                    .setWin(0)
                    .setTotalWin(0)
                    .setCurrency(currencyName)
                    .setUpdSessionId(MsgUtil.getSessionID(session));

            ScriptLoader.getInstance().consumerScript("AgentGameScript",
                    (IAgentGameScript script) -> script.updateCurrency(notifyData));

            // 14. 构建成功响应
            buildSuccessResponse(responseMap, refNo);
            MsgUtil.responseHttp(responseMap, session);

        } catch (Exception e) {
            LOGGER.error("ReqCancelBetHandler error", e);
            FunkyUtils.sendErrorResponse(session, FunkyConstants.ErrorCode.INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * 构建成功响应
     */
    private void buildSuccessResponse(Map<String, Object> responseMap, String refNo) {
        final JSONObject data = new JSONObject();
        data.put("refNo", refNo);

        responseMap.put("errorCode", FunkyConstants.ErrorCode.SUCCESS.getCode());
        responseMap.put("errorMessage", FunkyConstants.ErrorCode.SUCCESS.getMessage());
        responseMap.put("data", data);
    }

    /**
     * 构建错误响应
     */
    private void buildErrorResponse(Map<String, Object> responseMap, FunkyConstants.ErrorCode errorCode) {
        responseMap.put("errorCode", errorCode.getCode());
        responseMap.put("errorMessage", errorCode.getMessage());
        responseMap.put("data", null);
    }
}
