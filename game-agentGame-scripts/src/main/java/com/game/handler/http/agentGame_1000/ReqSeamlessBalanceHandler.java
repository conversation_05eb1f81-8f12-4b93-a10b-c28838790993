//package com.game.handler.http.agentGame_1000;
//
//import com.game.c_entity.middleplatform.C_BaseCurrency;
//import com.game.c_entity.middleplatform.C_BaseGamePlatform;
//import com.game.engine.io.handler.HttpHandler;
//import com.game.engine.io.netty.HttpResponseHelper;
//import com.game.engine.io.redis.RedisPoolManager;
//import com.game.engine.script.IHandlerEntity;
//import com.game.engine.script.ScriptLoader;
//import com.game.engine.utils.JsonUtils;
//import com.game.engine.utils.MsgUtil;
//import com.game.engine.utils.TimeUtil;
//import com.game.entity.player.Player;
//import com.game.enums.redis.RedisHall;
//import com.game.gamesr.manager.AgentGameMrg;
//import com.game.gamesr.manager.DataAgentGameMrg;
//import com.game.gamesr.scripts.IAgentGameLogicScript;
//import com.game.gamesr.scripts.IAgentGameScript;
//import io.netty.channel.ChannelFutureListener;
//import io.netty.util.internal.StringUtil;
//import org.slf4j.Logger;
//import org.slf4j.LoggerFactory;
//
//import java.util.*;
//
//
////http://127.0.0.1:8680/seamless/balance
//@IHandlerEntity(path = "/seamless/balance", desc = "余额查询")
//public class ReqSeamlessBalanceHandler extends HttpHandler {
//    private static final Logger LOGGER = LoggerFactory.getLogger(ReqSeamlessBalanceHandler.class);
//
//    @Override
//    public void run() {
//        try {
//            LOGGER.info("ReqSeamlessBalanceHandler，params：{}", JsonUtils.writeAsJson(paramsMap));
//            final String playerStr = (String) paramsMap.get("player");
//            final String currency = (String) paramsMap.get("currency");
//
//            final Map<String, Object> responseMap = new LinkedHashMap<>();
//            if (StringUtil.isNullOrEmpty(playerStr) || StringUtil.isNullOrEmpty(currency)) {
//                responseMap.put("message", "INVALID_ARGUMENT");
//                responseMap.put("timestamp", TimeUtil.currentTimeMillis());
//                MsgUtil.responseHttp(responseMap, session);
//                return;
//            }
//
//            final long playerId = AgentGameMrg.getInstance().getAgentGamePlayerId(playerStr);
//            final Player player = AgentGameMrg.getInstance().findDbPlayer(playerId);
//            if (player == null) {
//                responseMap.put("message", "INVALID_ARGUMENT");
//                responseMap.put("timestamp", TimeUtil.currentTimeMillis());
//                MsgUtil.responseHttp(responseMap, session);
//                return;
//            }
//
//            final boolean isIpBlacklist = ScriptLoader.getInstance().functionScript("AgentGameScript", (IAgentGameScript script) -> script.isIpBlacklist(player, MsgUtil.getClientIp(session)));
//            if (!isIpBlacklist) {
//                return;
//            }
//
//            final String balance = RedisPoolManager.getInstance().function(jedis -> jedis.hget(RedisHall.Platform_Role_Map_Currency.getKey(playerId), String.valueOf(player.getCurrencyId())));
//
//            responseMap.put("balance", balance);
//            responseMap.put("timestamp", TimeUtil.currentTimeMillis());
//            MsgUtil.responseHttp(responseMap, session);
//            LOGGER.info("ResSeamlessBalanceHandler，params：{}", JsonUtils.writeAsJson(responseMap));
//        } catch (Exception e) {
//            LOGGER.error("ReqSeamlessBalanceHandler", e);
//        } finally {
//            AgentGameMrg.getInstance().removeUdpSessionMap(MsgUtil.getSessionID(session));
//        }
//    }
//
//}
