package com.game.scripts;

import com.game.c_entity.middleplatform.C_BaseExchangeRate;
import com.game.c_entity.middleplatform.C_BaseGamePlatform;
import com.game.dao.game.GameNoteDao;
import com.game.engine.io.redis.RedisPoolManager;
import com.game.engine.log.GameLog;
import com.game.engine.math.BigDecimalUtils;
import com.game.engine.utils.*;
import com.game.entity.game.GameNote;
import com.game.entity.game.GameNoteFields;
import com.game.entity.player.Player;
import com.game.enums.Currency;
import com.game.enums.redis.RedisHall;
import com.game.gamesr.NotifyData;
import com.game.gamesr.main.AgentGameServer;
import com.game.gamesr.manager.AgentGameMrg;
import com.game.gamesr.manager.DataAgentGameMrg;
import com.game.gamesr.scripts.IAgentGameScript;
import com.game.manager.EntityDaoMrg;
import com.game.manager.ServersMrg;
import com.game.redis.RedisUtils;
import com.proto.InnerMessage;
import com.proto.MIDMessage;
import io.netty.channel.Channel;
import io.netty.util.internal.StringUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.mongodb.core.query.Update;

public class AgentGameScript implements IAgentGameScript {
    private static final Logger LOGGER = LoggerFactory.getLogger(AgentGameScript.class);

    @Override
    public boolean isIpBlacklist(String ip, int supplierId) {
        final C_BaseGamePlatform c_baseGamePlatform = DataAgentGameMrg.getInstance()
                .findC_BaseGamePlatformSupplierId(this.getClass().getSimpleName(), supplierId);
        if (c_baseGamePlatform == null) {
            return false;
        }
        if (!c_baseGamePlatform.getIpBlacklist().contains(ip)) {
            LOGGER.warn("supplierId：{}，ip：{}，limit", c_baseGamePlatform.getSupplierId(), ip);
            return false;
        }
        return true;
    }

    @Override
    public void updateCurrency(NotifyData notifyData) {
        final long pid = notifyData.getPid();
        final Player player = AgentGameMrg.getInstance().findDbPlayer(pid);
        {
            //更新货币
            final InnerMessage.InnerNotifyCurrencyUpdateMessage.Builder res = InnerMessage.InnerNotifyCurrencyUpdateMessage.newBuilder();
            res.setMsgID(MIDMessage.MID.InnerNotifyCurrencyUpdate_VALUE)
                    .setNotifyData(buildNotifyData(notifyData));
            final ServersMrg serversMrg = AgentGameServer.getInstance().getAgentGameTcpClient2Hall().getServersMrg();
            final Channel hallSession = serversMrg.getSession(player.getHallId());
            MsgUtil.sendInnerMsg(hallSession, res.build(), pid);
        }
    }

    private InnerMessage.NotifyData buildNotifyData(NotifyData notifyData) {
        return InnerMessage.NotifyData.newBuilder()
                .setGameId(notifyData.getGameId())
                .setType(notifyData.getType())
                .setNoteId(StringUtil.isNullOrEmpty(notifyData.getNoteId()) ? "" : notifyData.getNoteId())
                .setCurrencyId(notifyData.getCurrencyId())
                .setBetAmount(notifyData.getBetAmount())
                .setValidBet(notifyData.getValidBets())
                .setWin(notifyData.getWin())
                .setTotalWin(notifyData.getTotalWin())
                .setFreeTimes(notifyData.getFreeTimes())
                .setUpdSessionId(notifyData.getUpdSessionId())
                .setCurrency(StringUtil.isNullOrEmpty(notifyData.getCurrency()) ? "" : notifyData.getCurrency())
                .setPlayerName(StringUtil.isNullOrEmpty(notifyData.getPlayerName()) ? "" : notifyData.getPlayerName())
                .setUpdatedTime(notifyData.getUpdated_time())
                .setRealTransferAmount(notifyData.getReal_transfer_amount())
                .setData(StringUtil.isNullOrEmpty(notifyData.getData()) ? "" : notifyData.getData())
                .build();
    }

    private GameNote createAndSaveGameNote(Player player, String roundId, String transactionId,
                                          double betAmount, double validBets, double balance, String remark) {
        final UniqueIDGenerator uniqueIDGenerator = AgentGameServer.getInstance().getUniqueIDGenerator();
        final GameNote gameNote = new GameNote(uniqueIDGenerator.nextId());
        gameNote.setBusiness_no(player.getBusiness_no());
        gameNote.setRoundId(roundId);
        gameNote.getTransactionIds().add(transactionId);
        gameNote.setHeadId(player.getHeadId());
        gameNote.setPlayerId(player.getPlayerId());
        gameNote.setPlayerName(player.getPlayerName());
        gameNote.setPlatformId(player.getPlatformId());
        gameNote.setGameId(player.getGameId());
        gameNote.setGameType(player.getGameType());
        gameNote.setCurrencyId(player.getPlayerCurrencyId());
        gameNote.setGameCurrencyId(player.getGameCurrencyId());
        gameNote.setBonus(player.isBonus());
        gameNote.setBetAmount(betAmount);
        gameNote.setValidBets(validBets);
        if (validBets != 0) {
            gameNote.setUsdValidBets(validBetsTransformUsd(player.getPlayerCurrencyId(), validBets));
        }
        gameNote.setWin(0);
        gameNote.setBalance(balance);
        gameNote.setStatus(1);
        gameNote.setRemark(remark);
        EntityDaoMrg.getInstance().getDao(GameNoteDao.class).insert(gameNote);
        AgentGameMrg.getInstance().addGameNote(player, gameNote);
        //TODO 日志
        sendAgentGameLog(player, gameNote);
        player.setHeartbeat(TimeUtil.currentTimeMillis() + 15 * TimeUtil.MIN);
        return gameNote;
    }

    @Override
    public GameNote addGameNote(Player player, String roundId, String transactionId,
                                double betAmount, double validBets, double balance) {
        return createAndSaveGameNote(player, roundId, transactionId, betAmount, validBets, balance, "");
    }

    @Override
    public GameNote addGameNote(Player player, String roundId, String transactionId,
                                double betAmount, double validBets, double balance, String remark) {
        return createAndSaveGameNote(player, roundId, transactionId, betAmount, validBets, balance, remark);
    }

    @Override
    public GameNote addGameNote(Player player, String roundId, String transactionId,
                                int status, double betAmount, double validBets,
                                double win, double balance) {
        final UniqueIDGenerator uniqueIDGenerator = AgentGameServer.getInstance().getUniqueIDGenerator();
        final GameNote gameNote = new GameNote(uniqueIDGenerator.nextId());
        gameNote.setBusiness_no(player.getBusiness_no());
        gameNote.setRoundId(roundId);
        gameNote.getTransactionIds().add(transactionId);
        gameNote.setHeadId(player.getHeadId());
        gameNote.setPlayerId(player.getPlayerId());
        gameNote.setPlayerName(player.getPlayerName());
        gameNote.setPlatformId(player.getPlatformId());
        gameNote.setGameId(player.getGameId());
        gameNote.setGameType(player.getGameType());
        gameNote.setCurrencyId(player.getPlayerCurrencyId());
        gameNote.setGameCurrencyId(player.getGameCurrencyId());
        gameNote.setBonus(player.isBonus());
        gameNote.setBetAmount(betAmount);
        gameNote.setValidBets(validBets);
        if (validBets != 0) {
            gameNote.setUsdValidBets(validBetsTransformUsd(player.getPlayerCurrencyId(), validBets));
        }
        gameNote.setWin(win);
        gameNote.setStatus(status);
        gameNote.setBalance(balance);
        EntityDaoMrg.getInstance().getDao(GameNoteDao.class).insert(gameNote);
        AgentGameMrg.getInstance().addGameNote(player, gameNote);
        //TODO 日志
        sendAgentGameLog(player, gameNote);
        player.setHeartbeat(TimeUtil.currentTimeMillis() + 15 * TimeUtil.MIN);
        return gameNote;
    }

    @Override
    public void freeAddGameNote(Player player, String roundId, String transactionId,
                                int status, double win, double balance) {
        final GameNote gameNote = AgentGameMrg.getInstance().findRoundIdGameNote(player, roundId);
        if (gameNote == null) {
            addGameNote(player, roundId, transactionId, status, 0, 0, win, balance);
            return;
        }
        if (win == 0 && gameNote.getStatus() == 2) {
            return;
        }
        gameNote.getTransactionIds().add(transactionId);
        gameNote.setWin(BigDecimalUtils.add(win, gameNote.getWin(), 9));
        gameNote.setBalance(balance);
        final Update update = new Update();
        update.set(GameNoteFields.transactionIds, gameNote.getTransactionIds())
                .set(GameNoteFields.win, gameNote.getWin())
                .set(GameNoteFields.balance, balance);
        EntityDaoMrg.getInstance().getDao(GameNoteDao.class).updateByRoundIdFiled(gameNote.getRoundId(), update);
        //TODO 日志
        sendAgentGameLog(player, gameNote);
    }

    @Override
    public GameNote updateGameNote(Player player, String roundId, String transactionId,
                                   int status, double win, double balance) {
        return updateGameNote(player, roundId, transactionId, status, win, balance, null);
    }

    @Override
    public GameNote updateGameNote(Player player, String roundId, String transactionId,
                                   int status, double win, double balance, String remark) {
        final GameNote gameNote = AgentGameMrg.getInstance().findRoundIdGameNote(player, roundId);
        if (gameNote == null) {
            LOGGER.warn("playerId：{}，roundId：{}，not exist", player.getPlayerId(), roundId);
            return new GameNote();
        }
        if (win == 0 && gameNote.getStatus() == 2) {
            return gameNote;
        }
        gameNote.getTransactionIds().add(transactionId);
        gameNote.setWin(BigDecimalUtils.add(win, gameNote.getWin(), 9));
        gameNote.setBalance(balance);
        gameNote.setStatus(status);

        // 如果提供了remark，则更新remark字段
        if (remark != null && !remark.isEmpty()) {
            gameNote.setRemark(remark);
        }

        final Update update = new Update();
        update.set(GameNoteFields.transactionIds, gameNote.getTransactionIds())
                .set(GameNoteFields.win, gameNote.getWin())
                .set(GameNoteFields.balance, gameNote.getBalance())
                .set(GameNoteFields.status, gameNote.getStatus());

        // 如果提供了remark，则同时更新数据库中的remark字段
        if (remark != null && !remark.isEmpty()) {
            update.set(GameNoteFields.remark, gameNote.getRemark());
        }

        EntityDaoMrg.getInstance().getDao(GameNoteDao.class)
                .updateByRoundIdFiled(gameNote.getRoundId(), update);
        //TODO 日志
        sendAgentGameLog(player, gameNote);
        return gameNote;
    }

    @Override
    public void updateGameNoteStatus(Player player, String roundId, int status) {
        final GameNote gameNote = AgentGameMrg.getInstance().findRoundIdGameNote(player, roundId);
        if (gameNote == null) {
            LOGGER.warn("playerId：{}，roundId：{}，not exist", player.getPlayerId(), roundId);
            return;
        }
        gameNote.setStatus(status);
        final Update update = new Update();
        update.set(GameNoteFields.status, gameNote.getStatus());
        EntityDaoMrg.getInstance().getDao(GameNoteDao.class)
                .updateByRoundIdFiled(gameNote.getRoundId(), update);
        //TODO 日志
        sendAgentGameLog(player, gameNote);
    }

    @Override
    public GameNote updateGameNoteSportClose(String transactionId) {
        final GameNote gameNote = AgentGameMrg.getInstance().findByTransactionId(transactionId);
        if (gameNote == null) {
            LOGGER.warn("transactionId：{}，not exist", transactionId);
            return new GameNote();
        }

        final Player player = AgentGameMrg.getInstance().findDbPlayer(gameNote.getPlayerId());
        if (player == null) {
            LOGGER.warn("playerId：{}，not exist", gameNote.getPlayerId());
            return new GameNote();
        }

        gameNote.setSportClose(true);
        final Update update = new Update();
        update.set(GameNoteFields.sportClose, true);
        EntityDaoMrg.getInstance().getDao(GameNoteDao.class)
                .updateByRoundIdFiled(gameNote.getRoundId(), update);
        //TODO 日志
        sendAgentGameLog(player, gameNote);
        return gameNote;
    }

    @Override
    public void sendAgentGameLog(Player player, GameNote gameNote) {
        final GameLog platform_agentGameLog = new GameLog("platform_agentGameLog_" + player.getBusiness_no())
                .append("number", GuidGeneratorUtils.generateOrderId())
                .append("site", player.getWebSite())
                .append("noteId", gameNote.getNoteId())//生成的唯一id
                .append("roundId", gameNote.getRoundId())//注单id
                .append("business_no", gameNote.getBusiness_no())//商户号
                .append("playerId", gameNote.getPlayerId())//玩家id
                .append("platformId", gameNote.getPlatformId())//平台id
                .append("gameId", gameNote.getGameId())//游戏id
                .append("gameType", gameNote.getGameType())//游戏类型
                .append("currencyId", gameNote.getCurrencyId())//货币id
                .append("betAmount", gameNote.getBetAmount())//下注金额
                .append("validBets", gameNote.getValidBets())//有效下注
                .append("status", gameNote.getStatus())
                .append("win", gameNote.getWin())//赢分
                .append("createTime", gameNote.getCreateTime())//创建时间
                .append("logTime", gameNote.getCreateTime())
                .append("playerName", player.getPlayerName())
                .append("region", player.getRegisterRegion())//地区
                .append("agentId", player.getAgentId())
                .append("channelId", player.getChannelId())
                .append("mediaId", player.getMediaId())
                .append("adId", player.getAdId())
                .append("sportClose", gameNote.isSportClose())
                .append("remark", gameNote.getRemark());
        AgentGameServer.getInstance().getLogProducerMrg().send(platform_agentGameLog);
    }

    @Override
    public double realBalance(Player player) {
        final String cashBalance = RedisPoolManager.getInstance().function(jedis ->
                jedis.sync().hget(RedisHall.Platform_Role_Map_Currency.getKey(player.getPlayerId()), String.valueOf(player.getPlayerCurrencyId())));

        String bonusBalance = "";
        if (player.isBonus()) {
            bonusBalance = RedisPoolManager.getInstance().function(jedis ->
                    jedis.sync().hget(RedisHall.Platform_Role_Map_Currency.getKey(player.getPlayerId()), String.valueOf(player.getPlayerCurrencyId() * 10)));
        }

        return BigDecimalUtils.add(RedisUtils.convert(cashBalance),
                RedisUtils.convert(bonusBalance),
                9);
    }

    @Override
    public double usdTransformAmount(Player player, double amount) {
        final int gameCurrencyId = player.getGameCurrencyId();
        if (gameCurrencyId != Currency.USD.getCurrencyId()) {
            return amount;
        }
        final C_BaseExchangeRate c_baseExchangeRate = DataAgentGameMrg.getInstance().findC_BaseExchangeRate(player.getPlayerCurrencyId());
        if (c_baseExchangeRate == null) {
            return 0;
        }
        return BigDecimalUtils.mul(amount, BigDecimalUtils.div(1, c_baseExchangeRate.getExchangeRate(), 9), 9);
    }

    @Override
    public double amountTransformUsd(Player player, double amount) {
        final int gameCurrencyId = player.getGameCurrencyId();
        if (gameCurrencyId != Currency.USD.getCurrencyId()) {
            return amount;
        }
        final C_BaseExchangeRate c_baseExchangeRate = DataAgentGameMrg.getInstance().findC_BaseExchangeRate(player.getPlayerCurrencyId());
        if (c_baseExchangeRate == null) {
            return 0;
        }
        return BigDecimalUtils.mul(amount, c_baseExchangeRate.getExchangeRate(), 9);
    }

    private double validBetsTransformUsd(int currencyId, double validBets) {
        final C_BaseExchangeRate c_baseExchangeRate = DataAgentGameMrg.getInstance().findC_BaseExchangeRate(currencyId);
        if (c_baseExchangeRate == null) {
            return 0;
        }
        return BigDecimalUtils.mul(validBets, c_baseExchangeRate.getExchangeRate(), 9);
    }


}
