package com.game.scripts.agentGame;

import com.alibaba.fastjson.JSONObject;
import com.game.c_entity.middleplatform.C_BaseGamePlatform;
import com.game.engine.math.BigDecimalUtils;
import com.game.engine.utils.*;
import com.game.entity.player.FreeGameInfo;
import com.game.entity.player.GameInfo;
import com.game.entity.player.Player;
import com.game.enums.ErrorCode;
import com.game.enums.GameType;
import com.game.gamesr.main.AgentGameServer;
import com.game.gamesr.manager.AgentGameMrg;
import com.game.gamesr.manager.DataAgentGameMrg;
import com.game.gamesr.scripts.IAgentGameLogicScript;
import com.game.gamesr.utils.JWTUtils;
import com.game.gamesr.utils.SHAUtils;
import com.proto.HallMessage;
import com.proto.InnerMessage;
import com.proto.MIDMessage;
import io.netty.channel.Channel;
import io.netty.util.internal.StringUtil;
import org.apache.http.HttpStatus;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.net.URI;
import java.net.http.HttpClient;
import java.net.http.HttpRequest;
import java.net.http.HttpResponse;
import java.time.Duration;
import java.util.*;

/**
 * St8
 */
public class AgentGameLogic_1002_Script implements IAgentGameLogicScript {
    private static final Logger LOGGER = LoggerFactory.getLogger(AgentGameLogic_1002_Script.class);

    @Override
    public boolean entryAgentGame(HallMessage.ReqEntryAgentGameMessage req, Player player, Channel session, long udpSessionId) {
        final HallMessage.ResEntryAgentGameMessage.Builder res = HallMessage.ResEntryAgentGameMessage.newBuilder();
        res.setMsgID(MIDMessage.MID.ResEntryAgentGame_VALUE);
        try {
            final int platformId = req.getPlatformId();
            final String platformGameId = req.getPlatformGameId();
            final String currency = req.getCurrencyName();

            final C_BaseGamePlatform c_baseGamePlatform = DataAgentGameMrg.getInstance()
                    .findC_BaseGamePlatformId(this.getClass().getSimpleName(), platformId);
            if (c_baseGamePlatform == null) {
                return false;
            }

            final String referenceId = executeFreeGame(req, player, c_baseGamePlatform);

            final JWTUtils.JWData jwData = new JWTUtils.JWData();
            jwData.userId = player.getPlayerId();
            jwData.currency = currency;
            jwData.expires = TimeUtil.currentTimeMillis() + 7 * TimeUtil.DAY;
            final String token = JWTUtils.jwtToken(jwData);

            final JSONObject site = new JSONObject();
            site.put("id", c_baseGamePlatform.getSiteId());
            site.put("lobby", c_baseGamePlatform.getLobby());

            final JSONObject player_profile = new JSONObject();
            player_profile.put("id", AgentGameMrg.getPlatformUserName(currency, player.getPlayerId()));
            player_profile.put("jurisdiction", "CW");
            player_profile.put("default_currency", currency);
            player_profile.put("reg_country", "BR");

            String language = "eng";
            if (player.getLanguage() == 2) {
                language = "por";
            }

            final Map<String, Object> paramsMap = new LinkedHashMap<>();
            paramsMap.put("operator_code", c_baseGamePlatform.getAgent());
            paramsMap.put("game_code", platformGameId);
            paramsMap.put("currency", currency);
            paramsMap.put("site", site);
            paramsMap.put("token", token);
            paramsMap.put("player", AgentGameMrg.getPlatformUserName(currency, player.getPlayerId()));
            paramsMap.put("player_profile", player_profile);
            paramsMap.put("lang",language);

            final String body = JsonUtils.writeAsJson(paramsMap);
            LOGGER.warn("body：{}", body);

            final String uri = c_baseGamePlatform.getApiUrl() + "/api/operator/v1/launch";
            LOGGER.warn("url：{}", uri);

            final String sign = SHAUtils.sign(c_baseGamePlatform.getSecretKey(), body);

            final HttpRequest request = HttpRequest.newBuilder()
                    .timeout(Duration.ofSeconds(15))
                    .uri(URI.create(uri))
                    .version(HttpClient.Version.HTTP_1_1)
                    .header("x-st8-sign", sign)
                    .header("Content-Type", HttpUtils11.HTTP_CONTENT_TYPE_JSON)
                    .POST(HttpRequest.BodyPublishers.ofString(body))
                    .build();
            final HttpResponse<String> httpResponse = AgentGameServer.getInstance().getHttpClientMrg().send(request, HttpResponse.BodyHandlers.ofString());
            notifyClient(req, player, session, udpSessionId, httpResponse, referenceId);
        } catch (Exception e) {
            LOGGER.error("entryAgentGame", e);
            res.setError(ErrorCode.Internal_Server_Error.getCode());
            MsgUtil.sendInnerMsg(session, res.build(), player.getPlayerId(), udpSessionId);
            return false;
        }
        return true;
    }

    private void notifyClient(HallMessage.ReqEntryAgentGameMessage req, Player player, Channel session, long udpSessionId, HttpResponse<String> httpResponse, String referenceId) {
        final HallMessage.ResEntryAgentGameMessage.Builder res = HallMessage.ResEntryAgentGameMessage.newBuilder();
        res.setMsgID(MIDMessage.MID.ResEntryAgentGame_VALUE);

        if (httpResponse.statusCode() != HttpStatus.SC_OK) {
            LOGGER.warn("exception msg：{}", httpResponse.body());
            res.setError(ErrorCode.Internal_Server_Error.getCode());
            MsgUtil.sendInnerMsg(session, res.build(), player.getPlayerId(), udpSessionId);
            throw new IllegalArgumentException("notifyClient");
        }

        final String body = httpResponse.body();
        LOGGER.info("response：{}", body);

        final JSONObject response = JsonUtils.readFromJson(body, JSONObject.class);
        final String status = response.getString("status");
        final String game_url = response.getString("game_url");

        if (!Objects.equals(status, "ok")) {//失败
            res.setError(ErrorCode.AgentGame_Error.getCode());
        } else {
            res.setGameUrl(game_url)
                    .setGameId(req.getGameId())
                    .setCurrencyId(req.getCurrencyId())
                    .setGameType(req.getGameType())
                    .setGameCurrencyId(req.getGameCurrencyId())
                    .setPlatformId(req.getPlatformId())
                    .setReferenceId(referenceId)
                    .setBonus(req.getBonus());
        }
        MsgUtil.sendInnerMsg(session, res.build(), player.getPlayerId(), udpSessionId);
    }

    private String executeFreeGame(HallMessage.ReqEntryAgentGameMessage req, Player player, C_BaseGamePlatform c_baseGamePlatform) {
        try {
            final String platformGameId = req.getPlatformGameId();
            final int gameType = req.getGameType();
            final String currency = req.getCurrencyName();

            if (gameType != GameType.Casino_Slots.getType()) {
                return "";
            }

            final FreeGameInfo freeGameInfo = player.getFreeGameInfo(req.getCurrencyId());
            final GameInfo gameInfo = freeGameInfo.getFreeGame(req.getGameId());
            if (gameInfo.getFreeTimes() == 0) {
                return "";
            }

            if (!cancelFreeGame(gameInfo.getReferenceId(), c_baseGamePlatform)) {
                return "";
            }

            final Map<String, Object> paramsMap = new LinkedHashMap<>();
            paramsMap.put("bonus_id", UUID.randomUUID());
            paramsMap.put("game_codes", Collections.singletonList(platformGameId));
            paramsMap.put("currency", currency);
            paramsMap.put("value", gameInfo.getBet());
            paramsMap.put("type", "free_bets");
            paramsMap.put("players", Collections.singletonList(AgentGameMrg.getPlatformUserName(currency, player.getPlayerId())));
            paramsMap.put("count", gameInfo.getFreeTimes());

            final String body = JsonUtils.writeAsJson(paramsMap);
            LOGGER.warn("body：{}", body);

            final String uri = c_baseGamePlatform.getApiUrl() + "/api/operator/v1/bonus/create";
            LOGGER.warn("url：{}", uri);

            final String sign = SHAUtils.sign(c_baseGamePlatform.getSecretKey(), body);

            final HttpRequest request = HttpRequest.newBuilder()
                    .timeout(Duration.ofSeconds(15))
                    .uri(URI.create(uri))
                    .version(HttpClient.Version.HTTP_1_1)
                    .header("x-st8-sign", sign)
                    .header("Content-Type", HttpUtils11.HTTP_CONTENT_TYPE_JSON)
                    .POST(HttpRequest.BodyPublishers.ofString(body))
                    .build();
            final HttpResponse<String> httpResponse = AgentGameServer.getInstance().getHttpClientMrg().send(request, HttpResponse.BodyHandlers.ofString());
            LOGGER.warn("AgentGameLogic_1002_Script，executeFreeGame：{}", httpResponse.body());
        } catch (Exception e) {
            LOGGER.error("AgentGameLogic_1002_Script，executeFreeGame", e);
        }
        return UUID.randomUUID().toString();
    }

    private boolean cancelFreeGame(String referenceId, C_BaseGamePlatform c_baseGamePlatform) {
        try {
            if (StringUtil.isNullOrEmpty(referenceId)) {
                return true;
            }

            final Map<String, String> paramsMap = new LinkedHashMap<>();
            paramsMap.put("bonus_id", referenceId);

            final String body = JsonUtils.writeAsJson(paramsMap);
            LOGGER.warn("body：{}", body);

            final String uri = c_baseGamePlatform.getApiUrl() + "/api/operator/v1/bonus/cancel";
            LOGGER.warn("url：{}", uri);

            final String sign = SHAUtils.sign(c_baseGamePlatform.getSecretKey(), body);
            LOGGER.warn("sign：{}", sign);

            final HttpRequest request = HttpRequest.newBuilder()
                    .timeout(Duration.ofSeconds(15))
                    .uri(URI.create(uri))
                    .version(HttpClient.Version.HTTP_1_1)
                    .header("x-st8-sign", sign)
                    .header("Content-Type", HttpUtils11.HTTP_CONTENT_TYPE_JSON)
                    .POST(HttpRequest.BodyPublishers.ofString(body))
                    .build();
            final HttpResponse<String> httpResponse = AgentGameServer.getInstance().getHttpClientMrg().send(request, HttpResponse.BodyHandlers.ofString());
            LOGGER.warn("AgentGameLogic_1002_Script，cancelFreeGame：{}", httpResponse.body());

            final JSONObject res = JsonUtils.readFromJson(httpResponse.body(), JSONObject.class);
            final String status = res.getString("status");
            if (Objects.equals(status, "error")) {
                return false;
            }
        } catch (Exception e) {
            LOGGER.error("AgentGameLogic_1002_Script，cancelFreeGame", e);
        }
        return true;
    }

    @Override
    public void notifyUpdateCurrency(InnerMessage.NotifyData req, long pid) {
        final Map<String, Object> responseMap = new LinkedHashMap<>();
        try {
            final double balance = req.getBalance();
            responseMap.put("status", "ok");
            responseMap.put("currency", req.getCurrency());
            responseMap.put("balance", BigDecimalUtils.round(balance, 2) + "");

            final Channel session = AgentGameMrg.getInstance().getUdpSessionMap().get(req.getUpdSessionId());
            MsgUtil.responseHttp(responseMap, session);
        } catch (Exception e) {
            LOGGER.error("updateCurrency", e);
        } finally {
            AgentGameMrg.getInstance().removeUdpSessionMap(req.getUpdSessionId());
        }
    }
}
