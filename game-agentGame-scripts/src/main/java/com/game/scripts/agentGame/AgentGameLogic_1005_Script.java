package com.game.scripts.agentGame;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.game.c_entity.middleplatform.C_BaseGamePlatform;
import com.game.engine.utils.MsgUtil;
import com.game.engine.utils.TimeUtil;
import com.game.entity.player.Player;
import com.game.enums.AgentGame;
import com.game.enums.ErrorCode;
import com.game.gamesr.manager.AgentGameMrg;
import com.game.gamesr.manager.DataAgentGameMrg;
import com.game.gamesr.scripts.IAgentGameLogicScript;
import com.game.gamesr.utils.BetbyJWTUtils;
import com.proto.HallMessage;
import com.proto.InnerMessage;
import com.proto.MIDMessage;
import io.netty.channel.Channel;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.LinkedHashMap;
import java.util.Map;

/**
 *
 */
public class AgentGameLogic_1005_Script implements IAgentGameLogicScript {
    private static final Logger LOGGER = LoggerFactory.getLogger(AgentGameLogic_1005_Script.class);

    @Override
    public boolean entryAgentGame(HallMessage.ReqEntryAgentGameMessage req, Player player, Channel session, long udpSessionId) {
        final HallMessage.ResEntryAgentGameMessage.Builder res = HallMessage.ResEntryAgentGameMessage.newBuilder();
        res.setMsgID(MIDMessage.MID.ResEntryAgentGame_VALUE);
        try {
            final String currency = req.getCurrencyName();
            String language = "en";
            if (player.getLanguage() == 2) {
                language = "pt";
            }

            final String playerIdCurrency = String.format("%d-%s", player.getPlayerId(), currency);

            final C_BaseGamePlatform c_baseGamePlatform = DataAgentGameMrg.getInstance()
                    .findC_BaseGamePlatformSupplierId(this.getClass().getSimpleName(), AgentGame.BETBY.getType());

            // 复用获得brand_id
            String brandId = c_baseGamePlatform.getAgent();

            // 使用 BetbyJWTUtils 生成 token
            final BetbyJWTUtils.JWData jwData = new BetbyJWTUtils.JWData(
                    brandId,
                    playerIdCurrency,
                    player.getPlayerName(), // playerName
                    language,   // language
                    currency,   // currency
                    System.currentTimeMillis() + 24 * TimeUtil.HOUR // 24小时后过期
            );

            // 设置feature flags
            jwData.ff.put("is_cashout_available", true);
            jwData.ff.put("is_match_tracker_available", true);

            final String token = BetbyJWTUtils.jwtToken(jwData);

            // 复用，将token放入响应消息中
            res.setUrlType(3);
            res.setGameUrl(token);
            LOGGER.info(token);
            // 向前端返回 token
            MsgUtil.sendInnerMsg(session, res.build(), player.getPlayerId(), udpSessionId);

        } catch (Exception e) {
            LOGGER.error("Betby entryAgentGame error", e);
            res.setError(ErrorCode.Internal_Server_Error.getCode());
            MsgUtil.sendInnerMsg(session, res.build(), player.getPlayerId(), udpSessionId);
            return false;
        }
        return true;
    }

    @Override
    public void notifyUpdateCurrency(InnerMessage.NotifyData req, long pid) {
        try {
            switch (req.getType()) {
                case 1 -> handleBetUpdateCurrency(req);
                case 2 -> handleWinUpdateCurrency(req);
                case 3 -> {
                    String playerName = req.getPlayerName();
                    if (playerName.isEmpty()) {
                        handleDiscardUpdateCurrency(req);
                    } else {
                        handleRefundUpdateCurrency(req);
                    }
                }
                case 5 -> handleRollbackUpdateCurrency(req);
                default -> LOGGER.warn("Unknown updateCurrency type: {}", req.getType());
            }
        } catch (Exception e) {
            LOGGER.error("updateCurrency error", e);
        } finally {
            AgentGameMrg.getInstance().removeUdpSessionMap(req.getUpdSessionId());
        }
    }


    private void handleBetUpdateCurrency(InnerMessage.NotifyData req) {
        final Map<String, Object> responseMap = new LinkedHashMap<>();
        int amountInCents = (int) Math.round(req.getBetAmount() * 100);
        int balanceInCents = (int) Math.round(req.getBalance() * 100);

        String dataJson = req.getData();
        JSONObject json = JSON.parseObject(dataJson);
        String transactionId = json.getString("transactionId");

        responseMap.put("id", transactionId);
        responseMap.put("ext_transaction_id", transactionId);
        responseMap.put("parent_transaction_id", null);
        responseMap.put("user_id", req.getPlayerName());
        responseMap.put("operation", "bet");
        responseMap.put("amount", amountInCents);
        responseMap.put("currency", req.getCurrency());
        responseMap.put("balance", balanceInCents);

        LOGGER.info("handleBetUpdateCurrency response: {}", responseMap);
        Channel session = AgentGameMrg.getInstance().getUdpSessionMap().get(req.getUpdSessionId());
        MsgUtil.responseHttp(responseMap, session);
    }

    private void handleWinUpdateCurrency(InnerMessage.NotifyData req) {
        final Map<String, Object> responseMap = new LinkedHashMap<>();
        int amountInCents = (int) Math.round(req.getWin() * 100);
        int balanceInCents = (int) Math.round(req.getBalance() * 100);

        String dataJson = req.getData();
        JSONObject json = JSON.parseObject(dataJson);
        String extPlayerId = json.getString("extPlayerId");
        String parentTransactionId = json.getString("parentTransactionId");
        String operation = json.getString("operation");
        String transactionId = json.getString("transactionId");

        responseMap.put("id", transactionId);
        responseMap.put("ext_transaction_id", transactionId);
        responseMap.put("parent_transaction_id", parentTransactionId);  // 如需支持可调整
        responseMap.put("user_id", extPlayerId);

        // 判断是否为win操作（优先使用传入的操作类型，其次根据金额）
        boolean isWin = (operation != null) ? "win".equals(operation) : req.getWin() > 0;
        if (isWin) {
            responseMap.put("operation", "win");
            responseMap.put("amount", amountInCents);
            responseMap.put("currency", req.getCurrency());
        } else {
            responseMap.put("operation", "lost");
        }

        responseMap.put("balance", balanceInCents);

        LOGGER.info("handleWinUpdateCurrency response: {}", responseMap);
        Channel session = AgentGameMrg.getInstance().getUdpSessionMap().get(req.getUpdSessionId());
        MsgUtil.responseHttp(responseMap, session);
    }

    private void handleRefundUpdateCurrency(InnerMessage.NotifyData req) {
        final Map<String, Object> responseMap = new LinkedHashMap<>();

        int balanceInCents = (int) Math.round(req.getBalance() * 100);

        String dataJson = req.getData();
        JSONObject json = JSON.parseObject(dataJson);
        String extPlayerId = json.getString("extPlayerId");
        String parentTransactionId = json.getString("parentTransactionId");
        int amountInCents = json.getInteger("transactionAmount");
        String transactionId = json.getString("transactionId");

        responseMap.put("id", transactionId);
        responseMap.put("ext_transaction_id", transactionId);
        responseMap.put("parent_transaction_id", parentTransactionId);  // 如需支持可调整
        responseMap.put("user_id", extPlayerId);
        responseMap.put("operation", "refund");
        responseMap.put("amount", amountInCents);
        responseMap.put("currency", req.getCurrency());

        responseMap.put("balance", balanceInCents);

        LOGGER.info("handleRefundUpdateCurrency response: {}", responseMap);
        Channel session = AgentGameMrg.getInstance().getUdpSessionMap().get(req.getUpdSessionId());
        MsgUtil.responseHttp(responseMap, session);
    }

    private void handleDiscardUpdateCurrency(InnerMessage.NotifyData req) {
        final Map<String, Object> responseMap = new LinkedHashMap<>();
        Channel session = AgentGameMrg.getInstance().getUdpSessionMap().get(req.getUpdSessionId());

        LOGGER.info("handleDiscardUpdateCurrency response: {}", responseMap);
        MsgUtil.responseHttp(responseMap, session);
    }

    private void handleRollbackUpdateCurrency(InnerMessage.NotifyData req) {
        final Map<String, Object> responseMap = new LinkedHashMap<>();

        int balanceInCents = (int) Math.round(req.getBalance() * 100);

        String dataJson = req.getData();
        JSONObject json = JSON.parseObject(dataJson);
        String extPlayerId = json.getString("extPlayerId");
        String parentTransactionId = json.getString("parentTransactionId");
        int amountInCents = json.getInteger("transactionAmount");
        String transactionId = json.getString("transactionId");

        responseMap.put("id", transactionId);
        responseMap.put("ext_transaction_id", transactionId);
        responseMap.put("parent_transaction_id", parentTransactionId);  // 如需支持可调整
        responseMap.put("user_id", extPlayerId);
        responseMap.put("operation", "rollback");
        responseMap.put("amount", amountInCents);
        responseMap.put("currency", req.getCurrency());

        responseMap.put("balance", balanceInCents);

        LOGGER.info("handleRollbackUpdateCurrency response: {}", responseMap);
        Channel session = AgentGameMrg.getInstance().getUdpSessionMap().get(req.getUpdSessionId());
        MsgUtil.responseHttp(responseMap, session);
    }

}
