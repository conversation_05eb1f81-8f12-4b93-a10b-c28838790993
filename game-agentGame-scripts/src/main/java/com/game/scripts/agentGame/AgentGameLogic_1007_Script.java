package com.game.scripts.agentGame;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.game.c_entity.middleplatform.C_BaseGamePlatform;
import com.game.engine.io.conf.ConstantConfig;
import com.game.engine.math.BigDecimalUtils;
import com.game.engine.utils.MsgUtil;
import com.game.engine.utils.TimeUtil;
import com.game.entity.player.Player;
import com.game.enums.AgentGame;
import com.game.enums.ErrorCode;
import com.game.gamesr.main.AgentGameServer;
import com.game.gamesr.manager.AgentGameMrg;
import com.game.gamesr.manager.DataAgentGameMrg;
import com.game.gamesr.scripts.IAgentGameLogicScript;
import com.game.gamesr.utils.BetbyJWTUtils;
import com.game.gamesr.utils.JWTUtils;
import com.game.handler.http.agentGame_1007_funky.FunkyConstants;
import com.proto.HallMessage;
import com.proto.InnerMessage;
import com.proto.MIDMessage;
import io.netty.channel.Channel;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.net.URI;
import java.net.http.HttpClient;
import java.net.http.HttpRequest;
import java.net.http.HttpResponse;
import java.time.Duration;
import java.util.LinkedHashMap;
import java.util.Map;


/**
 * Funky Games集成实现
 */
public class AgentGameLogic_1007_Script implements IAgentGameLogicScript {
    private static final Logger LOGGER = LoggerFactory.getLogger(AgentGameLogic_1007_Script.class);
    private static final String API_URL = "https://trial-gp-api.funkytest.com/Funky/Game/LaunchGame";

    @Override
    public boolean entryAgentGame(HallMessage.ReqEntryAgentGameMessage req, Player player, Channel session, long udpSessionId) {
        final HallMessage.ResEntryAgentGameMessage.Builder res = HallMessage.ResEntryAgentGameMessage.newBuilder();
        res.setMsgID(MIDMessage.MID.ResEntryAgentGame_VALUE);
        try {
            final String platformGameId = req.getPlatformGameId(); // 对应 Funky 的 gameCode
            final String currency = req.getCurrencyName();

            // 设置语言
            String language = "EN";
            if (player.getLanguage() == 2) {
                language = "PT";
            }

            // 获取API配置
            final C_BaseGamePlatform c_baseGamePlatform = DataAgentGameMrg.getInstance()
                    .findC_BaseGamePlatformSupplierId(this.getClass().getSimpleName(), AgentGame.FUNKY.getType());
            String agent = c_baseGamePlatform.getLobby();
            String auth = c_baseGamePlatform.getSecretKey();

            // 构造玩家唯一用户名
            String userName = AgentGameMrg.getPlatformUserName(currency, player.getPlayerId());

            // 创建 JWT 作为 Session ID
            final JWTUtils.JWData jwData = new JWTUtils.JWData();
            jwData.userId = player.getPlayerId();
            jwData.currency = currency;
            jwData.playerName = userName;
            jwData.expires = TimeUtil.currentTimeMillis() + 7 * TimeUtil.DAY;
            final String sessionId = JWTUtils.jwtToken(jwData);

            // 获取玩家 IP
            String playerIp = player.getIp() != null ? player.getIp() : "127.0.0.1";

            // 构建请求 JSON
            JSONObject params = new JSONObject();
            params.put("gameCode", platformGameId);
            params.put("userName", userName);
            params.put("playerId", String.valueOf(player.getPlayerId()));
            params.put("currency", currency);
            params.put("language", language);
            params.put("playerIp", playerIp);
            params.put("sessionId", sessionId);
            params.put("RedirectUrl", "");
            params.put("IsTestAccount", !ConstantConfig.getInstance().isAgentGame());

            // 打印请求头和请求体内容
            LOGGER.info("Sending request to URL: {}", API_URL);
            LOGGER.info("Request Headers: Content-Type: application/json;charset=UTF-8, User-Agent: {}, Authentication: {}", agent, auth);
            LOGGER.info("Request Body: {}", params.toJSONString());

            // 构造 HTTP 请求
            HttpRequest request = HttpRequest.newBuilder()
                    .timeout(Duration.ofSeconds(15))
                    .uri(URI.create(API_URL))
                    .version(HttpClient.Version.HTTP_1_1)
                    .header("Content-Type", "application/json;charset=UTF-8")
                    .header("User-Agent", agent)
                    .header("Authentication", auth)
                    .POST(HttpRequest.BodyPublishers.ofString(params.toJSONString()))
                    .build();

            // 发送请求
            HttpResponse<String> httpResponse = AgentGameServer.getInstance().getHttpClientMrg()
                    .send(request, HttpResponse.BodyHandlers.ofString());

            String response = httpResponse.body();
            LOGGER.info("Funky LaunchGame response: {}", response);

            // 解析响应
            JSONObject responseJson = JSONObject.parseObject(response);
            if (responseJson.getIntValue("errorCode") == 0) {
                JSONObject data = responseJson.getJSONObject("data");
                String gameUrl = data.getString("gameUrl");
                String token = data.getString("token");

                // 构建完整游戏URL
                String fullGameUrl = gameUrl + "?token=" + token;

                res.setGameUrl(fullGameUrl)
                        .setGameId(req.getGameId())
                        .setCurrencyId(req.getCurrencyId())
                        .setGameType(req.getGameType())
                        .setGameCurrencyId(req.getGameCurrencyId())
                        .setPlatformId(req.getPlatformId())
                        .setBonus(req.getBonus());

                MsgUtil.sendInnerMsg(session, res.build(), player.getPlayerId(), udpSessionId);
                return true;
            } else {
                LOGGER.warn("Funky LaunchGame failed, code: {}, message: {}", 
                        responseJson.getIntValue("errorCode"), 
                        responseJson.getString("errorMessage"));
                res.setError(ErrorCode.Data_Error.getCode());
                MsgUtil.sendInnerMsg(session, res.build(), player.getPlayerId(), udpSessionId);
                return false;
            }
        } catch (Exception e) {
            LOGGER.error("FunkyGames entryAgentGame error", e);
            res.setError(ErrorCode.Internal_Server_Error.getCode());
            MsgUtil.sendInnerMsg(session, res.build(), player.getPlayerId(), udpSessionId);
            return false;
        }
    }

    @Override
    public void notifyUpdateCurrency(InnerMessage.NotifyData req, long pid) {
        try {
            switch (req.getType()) {
                case 1 -> handleBetUpdateCurrency(req);
                case 2 -> handleWinUpdateCurrency(req);
                case 3 -> handleRefundUpdateCurrency(req);
                default -> LOGGER.warn("Unknown updateCurrency type: {}", req.getType());
            }
        } catch (Exception e) {
            LOGGER.error("updateCurrency error", e);
        } finally {
            AgentGameMrg.getInstance().removeUdpSessionMap(req.getUpdSessionId());
        }
    }


    private void handleBetUpdateCurrency(InnerMessage.NotifyData req) {
        final Map<String, Object> responseMap = new LinkedHashMap<>();

        responseMap.put("errorCode", FunkyConstants.ErrorCode.SUCCESS.getCode());
        responseMap.put("errorMessage", FunkyConstants.ErrorCode.SUCCESS.getMessage());

        final JSONObject data = new JSONObject();
        data.put("balance", BigDecimalUtils.round(req.getBalance(), 2));
        responseMap.put("data", data);

        LOGGER.info("handleBetUpdateCurrency response: {}", responseMap);
        Channel session = AgentGameMrg.getInstance().getUdpSessionMap().get(req.getUpdSessionId());
        MsgUtil.responseHttp(responseMap, session);
    }

    private void handleWinUpdateCurrency(InnerMessage.NotifyData req) {
        final Map<String, Object> responseMap = new LinkedHashMap<>();
        int amountInCents = (int) Math.round(req.getWin() * 100);
        int balanceInCents = (int) Math.round(req.getBalance() * 100);

        String dataJson = req.getData();
        JSONObject json = JSON.parseObject(dataJson);
        String extPlayerId = json.getString("extPlayerId");
        String parentTransactionId = json.getString("parentTransactionId");
        String operation = json.getString("operation");
        String transactionId = json.getString("transactionId");

        responseMap.put("id", transactionId);
        responseMap.put("ext_transaction_id", transactionId);
        responseMap.put("parent_transaction_id", parentTransactionId);  // 如需支持可调整
        responseMap.put("user_id", extPlayerId);

        // 判断是否为win操作（优先使用传入的操作类型，其次根据金额）
        boolean isWin = (operation != null) ? "win".equals(operation) : req.getWin() > 0;
        if (isWin) {
            responseMap.put("operation", "win");
            responseMap.put("amount", amountInCents);
            responseMap.put("currency", req.getCurrency());
        } else {
            responseMap.put("operation", "lost");
        }

        responseMap.put("balance", balanceInCents);

        LOGGER.info("handleWinUpdateCurrency response: {}", responseMap);
        Channel session = AgentGameMrg.getInstance().getUdpSessionMap().get(req.getUpdSessionId());
        MsgUtil.responseHttp(responseMap, session);
    }

    private void handleRefundUpdateCurrency(InnerMessage.NotifyData req) {
        final Map<String, Object> responseMap = new LinkedHashMap<>();

        int balanceInCents = (int) Math.round(req.getBalance() * 100);

        String dataJson = req.getData();
        JSONObject json = JSON.parseObject(dataJson);
        String extPlayerId = json.getString("extPlayerId");
        String parentTransactionId = json.getString("parentTransactionId");
        int amountInCents = json.getInteger("transactionAmount");
        String transactionId = json.getString("transactionId");

        responseMap.put("id", transactionId);
        responseMap.put("ext_transaction_id", transactionId);
        responseMap.put("parent_transaction_id", parentTransactionId);  // 如需支持可调整
        responseMap.put("user_id", extPlayerId);
        responseMap.put("operation", "refund");
        responseMap.put("amount", amountInCents);
        responseMap.put("currency", req.getCurrency());

        responseMap.put("balance", balanceInCents);

        LOGGER.info("handleRefundUpdateCurrency response: {}", responseMap);
        Channel session = AgentGameMrg.getInstance().getUdpSessionMap().get(req.getUpdSessionId());
        MsgUtil.responseHttp(responseMap, session);
    }

    private void handleDiscardUpdateCurrency(InnerMessage.NotifyData req) {
        final Map<String, Object> responseMap = new LinkedHashMap<>();
        Channel session = AgentGameMrg.getInstance().getUdpSessionMap().get(req.getUpdSessionId());

        LOGGER.info("handleDiscardUpdateCurrency response: {}", responseMap);
        MsgUtil.responseHttp(responseMap, session);
    }

    private void handleRollbackUpdateCurrency(InnerMessage.NotifyData req) {
        final Map<String, Object> responseMap = new LinkedHashMap<>();

        int balanceInCents = (int) Math.round(req.getBalance() * 100);

        String dataJson = req.getData();
        JSONObject json = JSON.parseObject(dataJson);
        String extPlayerId = json.getString("extPlayerId");
        String parentTransactionId = json.getString("parentTransactionId");
        int amountInCents = json.getInteger("transactionAmount");
        String transactionId = json.getString("transactionId");

        responseMap.put("id", transactionId);
        responseMap.put("ext_transaction_id", transactionId);
        responseMap.put("parent_transaction_id", parentTransactionId);  // 如需支持可调整
        responseMap.put("user_id", extPlayerId);
        responseMap.put("operation", "rollback");
        responseMap.put("amount", amountInCents);
        responseMap.put("currency", req.getCurrency());

        responseMap.put("balance", balanceInCents);

        LOGGER.info("handleRollbackUpdateCurrency response: {}", responseMap);
        Channel session = AgentGameMrg.getInstance().getUdpSessionMap().get(req.getUpdSessionId());
        MsgUtil.responseHttp(responseMap, session);
    }
}