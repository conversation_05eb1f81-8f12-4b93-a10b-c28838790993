package com.game.scripts.agentGame;

import com.alibaba.fastjson.JSONObject;
import com.game.c_entity.middleplatform.C_BaseGamePlatform;
import com.game.engine.math.BigDecimalUtils;
import com.game.engine.utils.*;
import com.game.entity.player.FreeGameInfo;
import com.game.entity.player.GameInfo;
import com.game.entity.player.Player;
import com.game.enums.Currency;
import com.game.enums.ErrorCode;
import com.game.enums.GameType;
import com.game.gamesr.main.AgentGameServer;
import com.game.gamesr.manager.AgentGameMrg;
import com.game.gamesr.manager.DataAgentGameMrg;
import com.game.gamesr.scripts.IAgentGameLogicScript;
import com.game.gamesr.utils.AESUtils;
import com.proto.HallMessage;
import com.proto.InnerMessage;
import com.proto.MIDMessage;
import io.netty.channel.Channel;
import io.netty.util.internal.StringUtil;
import org.apache.http.HttpStatus;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.net.URI;
import java.net.http.HttpClient;
import java.net.http.HttpRequest;
import java.net.http.HttpResponse;
import java.time.Duration;
import java.time.Instant;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;

/**
 * JDB
 */
public class AgentGameLogic_1003_Script implements IAgentGameLogicScript {
    private static final Logger LOGGER = LoggerFactory.getLogger(AgentGameLogic_1003_Script.class);

    @Override
    public boolean entryAgentGame(HallMessage.ReqEntryAgentGameMessage req, Player player, Channel session, long udpSessionId) {
        final HallMessage.ResEntryAgentGameMessage.Builder res = HallMessage.ResEntryAgentGameMessage.newBuilder();
        res.setMsgID(MIDMessage.MID.ResEntryAgentGame_VALUE);
        try {
            final int platformId = req.getPlatformId();
            final String platformGameId = req.getPlatformGameId();
            final int gameType = req.getGameType();
            final boolean freePlay = req.getFreePlay();
            final int currencyId = req.getGameCurrencyId();

            String parent = "";
            if (currencyId == Currency.USD.getCurrencyId()) {
                parent = "wingusdag";
            } else if (currencyId == Currency.BRL.getCurrencyId()) {
                parent = "wingbrlag";
            } else if (currencyId == Currency.USDT.getCurrencyId()) {
                parent = "wingusdtag";
            }

            final String referenceId = giveawayFreeGame(req, player, parent);

            final C_BaseGamePlatform c_baseGamePlatform = DataAgentGameMrg.getInstance()
                    .findC_BaseGamePlatformId(this.getClass().getSimpleName(), platformId);
            if (c_baseGamePlatform == null) {
                return false;
            }

            int gType = 0;
            if (gameType == GameType.Casino_Poker.getType()) {
                gType = 18;
            } else if (gameType == GameType.Casino_Fish.getType()) {
                gType = 7;
            }

            String language = "en";
            if (player.getLanguage() == 2) {
                language = "pt";
            }

            final Map<String, Object> paramsMap = new LinkedHashMap<>();
            if (!freePlay) {
                paramsMap.put("action", 21);
                paramsMap.put("ts", TimeUtil.currentTimeMillis());
                paramsMap.put("parent", parent);
                paramsMap.put("uid", AgentGameMrg.getPlatformUserName(parent, player.getPlayerId()));
            } else {
                paramsMap.put("action", 47);
                paramsMap.put("ts", TimeUtil.currentTimeMillis());
            }

            paramsMap.put("lang", language);
            paramsMap.put("gType", gType);
            paramsMap.put("mType", Integer.parseInt(platformGameId));

            final String params = JsonUtils.writeAsJson(paramsMap);
            LOGGER.warn("paramsMap：{}", params);
            final String x = AESUtils.encrypt(params, c_baseGamePlatform.getSecretKey(), c_baseGamePlatform.getToken());

            final Map<String, Object> bodyMap = new LinkedHashMap<>();
            bodyMap.put("dc", c_baseGamePlatform.getAgent());
            bodyMap.put("x", x);

            final String suffix = "dc=" + c_baseGamePlatform.getAgent() + "&x=" + x;
            final String uri = c_baseGamePlatform.getApiUrl() + "/apiRequest.do?" + suffix;
            LOGGER.warn("url：{}", uri);

            final HttpRequest request = HttpRequest.newBuilder()
                    .timeout(Duration.ofSeconds(15))
                    .uri(URI.create(uri))
                    .version(HttpClient.Version.HTTP_1_1)
                    .header("Content-Type", HttpUtils11.HTTP_CONTENT_TYPE_JSON)
                    .POST(HttpRequest.BodyPublishers.ofString(JsonUtils.writeAsJson(bodyMap)))
                    .build();
            final HttpResponse<String> httpResponse = AgentGameServer.getInstance().getHttpClientMrg().send(request, HttpResponse.BodyHandlers.ofString());
            notifyClient(req, player, referenceId, session, udpSessionId, httpResponse);
        } catch (Exception e) {
            LOGGER.error("entryAgentGame", e);
            res.setError(ErrorCode.Internal_Server_Error.getCode());
            MsgUtil.sendInnerMsg(session, res.build(), player.getPlayerId(), udpSessionId);
            return false;
        }
        return true;
    }

    private void notifyClient(HallMessage.ReqEntryAgentGameMessage req, Player player, String referenceId, Channel session, long udpSessionId, HttpResponse<String> httpResponse) {
        final HallMessage.ResEntryAgentGameMessage.Builder res = HallMessage.ResEntryAgentGameMessage.newBuilder();
        res.setMsgID(MIDMessage.MID.ResEntryAgentGame_VALUE);

        if (httpResponse.statusCode() != HttpStatus.SC_OK) {
            LOGGER.warn("exception msg：{}", httpResponse.body());
            res.setError(ErrorCode.Internal_Server_Error.getCode());
            MsgUtil.sendInnerMsg(session, res.build(), player.getPlayerId(), udpSessionId);
            throw new IllegalArgumentException("notifyClient");
        }

        final String body = httpResponse.body();
        LOGGER.info("response：{}", body);

        final JSONObject response = JsonUtils.readFromJson(body, JSONObject.class);
        final String status = response.getString("status");
        final String path = response.getString("path");

        if (!Objects.equals(status, "0000")) {//失败
            res.setError(ErrorCode.AgentGame_Error.getCode());
        } else {
            res.setGameUrl(path)
                    .setGameId(req.getGameId())
                    .setCurrencyId(req.getCurrencyId())
                    .setGameType(req.getGameType())
                    .setGameCurrencyId(req.getGameCurrencyId())
                    .setPlatformId(req.getPlatformId())
                    .setReferenceId(referenceId)
                    .setBonus(req.getBonus());
            ;
        }
        MsgUtil.sendInnerMsg(session, res.build(), player.getPlayerId(), udpSessionId);
    }

    private String giveawayFreeGame(HallMessage.ReqEntryAgentGameMessage req, Player player, String parent) {
        try {
            final int platformId = req.getPlatformId();
            final String platformGameId = req.getPlatformGameId();
            final int gameType = req.getGameType();

            if (gameType != GameType.Casino_Slots.getType()) {
                return "";
            }

            final FreeGameInfo freeGameInfo = player.getFreeGameInfo(req.getCurrencyId());
            final GameInfo gameInfo = freeGameInfo.getFreeGame(req.getGameId());
            if (gameInfo.getFreeTimes() == 0) {
                return "";
            }

            final C_BaseGamePlatform c_baseGamePlatform = DataAgentGameMrg.getInstance()
                    .findC_BaseGamePlatformId(this.getClass().getSimpleName(), platformId);
            if (c_baseGamePlatform == null) {
                return "";
            }

            if (!cancelFreeGame(gameInfo.getReferenceId(), parent, c_baseGamePlatform)) {
                return "";
            }

            final Map<String, Object> paramsMap = new LinkedHashMap<>();
            paramsMap.put("action", 72);
            paramsMap.put("ts", TimeUtil.currentTimeMillis());
            paramsMap.put("parent", parent);
            paramsMap.put("name", platformGameId + "_" + AgentGameMrg.getPlatformUserName(parent, player.getPlayerId()));
            paramsMap.put("startTime", dateTime(TimeUtil.currentTimeMillis()));
            paramsMap.put("endTime", dateTime(TimeUtil.currentTimeMillis() + 30 * TimeUtil.DAY));
            paramsMap.put("machineTypes", Collections.singletonList(platformGameId));
            paramsMap.put("bet", gameInfo.getBet());
            paramsMap.put("times", gameInfo.getFreeTimes());
            paramsMap.put("type", "NORMAL");
            paramsMap.put("minWithdrawal", gameInfo.getMinWithdraw());
            paramsMap.put("maxWithdrawal", gameInfo.getMaxWithdraw());
            paramsMap.put("transactionId", UUID.randomUUID().toString());

            final String params = JsonUtils.writeAsJson(paramsMap);
            LOGGER.warn("paramsMap：{}", params);
            final String x = AESUtils.encrypt(params, c_baseGamePlatform.getSecretKey(), c_baseGamePlatform.getToken());

            final Map<String, Object> bodyMap = new LinkedHashMap<>();
            bodyMap.put("dc", c_baseGamePlatform.getAgent());
            bodyMap.put("x", x);

            final String suffix = "dc=" + c_baseGamePlatform.getAgent() + "&x=" + x;
            final String uri = c_baseGamePlatform.getApiUrl() + "/apiRequest.do?" + suffix;
            LOGGER.warn("url：{}", uri);

            final HttpRequest request = HttpRequest.newBuilder()
                    .timeout(Duration.ofSeconds(15))
                    .uri(URI.create(uri))
                    .version(HttpClient.Version.HTTP_1_1)
                    .header("Content-Type", HttpUtils11.HTTP_CONTENT_TYPE_JSON)
                    .POST(HttpRequest.BodyPublishers.ofString(JsonUtils.writeAsJson(bodyMap)))
                    .build();
            final HttpResponse<String> response = AgentGameServer.getInstance().getHttpClientMrg().send(request, HttpResponse.BodyHandlers.ofString());
            LOGGER.warn("AgentGameLogic_1003_Script，giveawayFree：{}", response.body());

            final JSONObject responseData = JsonUtils.readFromJson(response.body(), JSONObject.class);
            final String status = responseData.getString("status");
            if (!Objects.equals(status, "0000")) {
                return "";
            }
            final JSONObject data = responseData.getJSONObject("data");
            final int eventId = data.getInteger("eventId");
            playerBindEventId(player, eventId, c_baseGamePlatform, parent);
            return eventId + "";
        } catch (Exception e) {
            LOGGER.error("AgentGameLogic_1003_Script，giveawayFree", e);
        }
        return "";
    }

    private static String dateTime(long time) {
        // Get the current time with the system's default timezone
        ZonedDateTime currentDateTime = ZonedDateTime.ofInstant(Instant.ofEpochMilli(time), ZoneId.systemDefault());

        // Define the formatter for the desired output
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss.SSSXXX");

        // Format the current time
        return currentDateTime.format(formatter);
    }

    private boolean cancelFreeGame(String referenceId, String parent, C_BaseGamePlatform c_baseGamePlatform) {
        try {
            if (StringUtil.isNullOrEmpty(referenceId)) {
                return true;
            }

            final Map<String, Object> paramsMap = new LinkedHashMap<>();
            paramsMap.put("action", 73);
            paramsMap.put("ts", TimeUtil.currentTimeMillis());
            paramsMap.put("parent", parent);
            paramsMap.put("eventId", Long.parseLong(referenceId));

            final String params = JsonUtils.writeAsJson(paramsMap);
            LOGGER.warn("paramsMap：{}", params);
            final String x = AESUtils.encrypt(params, c_baseGamePlatform.getSecretKey(), c_baseGamePlatform.getToken());

            final Map<String, Object> bodyMap = new LinkedHashMap<>();
            bodyMap.put("dc", c_baseGamePlatform.getAgent());
            bodyMap.put("x", x);

            final String suffix = "dc=" + c_baseGamePlatform.getAgent() + "&x=" + x;
            final String uri = c_baseGamePlatform.getApiUrl() + "/apiRequest.do?" + suffix;
            LOGGER.warn("url：{}", uri);

            final HttpRequest request = HttpRequest.newBuilder()
                    .timeout(Duration.ofSeconds(15))
                    .uri(URI.create(uri))
                    .version(HttpClient.Version.HTTP_1_1)
                    .header("Content-Type", HttpUtils11.HTTP_CONTENT_TYPE_JSON)
                    .POST(HttpRequest.BodyPublishers.ofString(JsonUtils.writeAsJson(bodyMap)))
                    .build();
            final HttpResponse<String> response = AgentGameServer.getInstance().getHttpClientMrg().send(request, HttpResponse.BodyHandlers.ofString());
            LOGGER.warn("AgentGameLogic_1003_Script，cancelFreeGame：{}", response.body());

            final JSONObject responseData = JsonUtils.readFromJson(response.body(), JSONObject.class);
            final String status = responseData.getString("status");
            if (!Objects.equals(status, "0000")) {
                return false;
            }
        } catch (Exception e) {
            LOGGER.error("AgentGameLogic_1003_Script，cancelFreeGame", e);
        }
        return true;
    }

    private void playerBindEventId(Player player, int eventId, C_BaseGamePlatform c_baseGamePlatform, String parent) {
        try {
            final Map<String, Object> paramsMap = new LinkedHashMap<>();
            paramsMap.put("action", 74);
            paramsMap.put("ts", TimeUtil.currentTimeMillis());
            paramsMap.put("parent", parent);
            paramsMap.put("eventId", eventId);
            paramsMap.put("players", Collections.singletonList(AgentGameMrg.getPlatformUserName(parent, player.getPlayerId())));

            final String params = JsonUtils.writeAsJson(paramsMap);
            LOGGER.warn("paramsMap：{}", params);
            final String x = AESUtils.encrypt(params, c_baseGamePlatform.getSecretKey(), c_baseGamePlatform.getToken());

            final Map<String, Object> bodyMap = new LinkedHashMap<>();
            bodyMap.put("dc", c_baseGamePlatform.getAgent());
            bodyMap.put("x", x);

            final String suffix = "dc=" + c_baseGamePlatform.getAgent() + "&x=" + x;
            final String uri = c_baseGamePlatform.getApiUrl() + "/apiRequest.do?" + suffix;
            LOGGER.warn("url：{}", uri);

            final HttpRequest request = HttpRequest.newBuilder()
                    .timeout(Duration.ofSeconds(15))
                    .uri(URI.create(uri))
                    .version(HttpClient.Version.HTTP_1_1)
                    .header("Content-Type", HttpUtils11.HTTP_CONTENT_TYPE_JSON)
                    .POST(HttpRequest.BodyPublishers.ofString(JsonUtils.writeAsJson(bodyMap)))
                    .build();
            final HttpResponse<String> response = AgentGameServer.getInstance().getHttpClientMrg().send(request, HttpResponse.BodyHandlers.ofString());
            LOGGER.warn("AgentGameLogic_1003_Script，playerBindEventId：{}", response.body());
        } catch (Exception e) {
            LOGGER.error("AgentGameLogic_1003_Script，playerBindEventId", e);
        }
    }

    @Override
    public void notifyUpdateCurrency(InnerMessage.NotifyData req, long pid) {
        final Map<String, Object> responseMap = new LinkedHashMap<>();
        try {
            final double balance = req.getBalance();
            responseMap.put("status", "0000");
            responseMap.put("balance", BigDecimalUtils.round(balance, 2));
            responseMap.put("err_text", "");

            final Channel session = AgentGameMrg.getInstance().getUdpSessionMap().get(req.getUpdSessionId());
            MsgUtil.responseHttp(responseMap, session);
        } catch (Exception e) {
            LOGGER.error("updateCurrency", e);
        } finally {
            AgentGameMrg.getInstance().removeUdpSessionMap(req.getUpdSessionId());
        }
    }
}
