package com.game.scripts.agentGame;

import com.alibaba.fastjson.JSONObject;
import com.game.c_entity.middleplatform.C_BaseGamePlatform;
import com.game.engine.io.conf.ConstantConfig;
import com.game.engine.math.BigDecimalUtils;
import com.game.engine.utils.MD5;
import com.game.engine.utils.MsgUtil;
import com.game.engine.utils.TimeUtil;
import com.game.entity.player.Player;
import com.game.enums.AgentGame;
import com.game.enums.Currency;
import com.game.enums.ErrorCode;
import com.game.gamesr.main.AgentGameServer;
import com.game.gamesr.manager.AgentGameMrg;
import com.game.gamesr.manager.DataAgentGameMrg;
import com.game.gamesr.scripts.IAgentGameLogicScript;
import com.game.gamesr.utils.JWTUtils;
import com.proto.HallMessage;
import com.proto.InnerMessage;
import com.proto.MIDMessage;
import io.netty.channel.Channel;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.net.URI;
import java.net.http.HttpClient;
import java.net.http.HttpRequest;
import java.net.http.HttpResponse;
import java.time.Duration;
import java.util.LinkedHashMap;
import java.util.Map;


/**
 *
 */
public class AgentGameLogic_1006_Script implements IAgentGameLogicScript {
    private static final Logger LOGGER = LoggerFactory.getLogger(AgentGameLogic_1006_Script.class);

    @Override
    public boolean entryAgentGame(HallMessage.ReqEntryAgentGameMessage req, Player player, Channel session, long udpSessionId) {
        final HallMessage.ResEntryAgentGameMessage.Builder res = HallMessage.ResEntryAgentGameMessage.newBuilder();
        res.setMsgID(MIDMessage.MID.ResEntryAgentGame_VALUE);
        try {
            final String platformGameId = req.getPlatformGameId();
            final int currencyId = req.getGameCurrencyId();
            final String currency = req.getCurrencyName();
            final int rtpPool = (int) Math.round(req.getRtpPool() * 100);

            // 设置语言，默认为英语
            String language = "en";
            if (player.getLanguage() == 2) {
                language = "pt";
            }

            // 获取API配置
            final C_BaseGamePlatform c_baseGamePlatform = DataAgentGameMrg.getInstance()
                    .findC_BaseGamePlatformSupplierId(this.getClass().getSimpleName(), AgentGame.ATGAME.getType());

            String apiKey = c_baseGamePlatform.getSecretKey();
            final String apiUrl = c_baseGamePlatform.getApiUrl();
            // 从配置中获取商户ID和密钥
            String mchId = "";
            //test
            if (!ConstantConfig.getInstance().isAgentGame()) {
                if (Currency.BRL.getCurrencyId() == currencyId) {
                    mchId = "10084";
                } else if (1006 == currencyId) {
                    mchId = "10098";
                    apiKey = "60A343A224FC3005F8BF4177BFFB628B";
                }
            } else {
                //正式
                if (Currency.BRL.getCurrencyId() == currencyId) {
                    mchId = "30047";
                } else if (Currency.USDT.getCurrencyId() == currencyId) {
                    mchId = "30048";
                } else if (1006 == currencyId) {
                    mchId = "30063";
                    apiKey = "30B8F220A79149A7D0B2FD85CE3280BB";
                }
            }

            final JWTUtils.JWData jwData = new JWTUtils.JWData();
            jwData.userId = player.getPlayerId();
            jwData.currency = currency;
            jwData.playerName = AgentGameMrg.getPlatformUserName(currency, player.getPlayerId());
            jwData.expires = TimeUtil.currentTimeMillis() + 7 * TimeUtil.DAY;
            final String token = JWTUtils.jwtToken(jwData);

            // 构建请求参数
            JSONObject params = new JSONObject();
            params.put("uname", AgentGameMrg.getPlatformUserName(currency, player.getPlayerId()));
            params.put("gameid", platformGameId);
            params.put("lang", language);
            params.put("token", token);
            params.put("rtp_pool", rtpPool);

            // 转换为JSON字符串，确保没有空格
            final String body = params.toJSONString();

            // 生成时间戳和签名
            final long timestamp = System.currentTimeMillis() / 1000; // 秒级时间戳
            final String signStr = body + timestamp + apiKey;

            // 计算MD5签名并转为大写
            final String sign = MD5.MD5Encode(signStr).toUpperCase();

            // 构建API接口URL
            final String uri = apiUrl + "/api/usr/ingame";

            // 添加请求头
            final HttpRequest request = HttpRequest.newBuilder()
                    .timeout(Duration.ofSeconds(15))
                    .uri(URI.create(uri))
                    .version(HttpClient.Version.HTTP_1_1)
                    .header("Cache-Control", "no-cache, no-store, must-revalidate")
                    .header("Content-Type", "application/json;charset=UTF-8")
                    .header("X-Atgame-Mchid", mchId)
                    .header("X-Atgame-Timestamp", String.valueOf(timestamp))
                    .header("X-Atgame-Sign", sign)
                    .POST(HttpRequest.BodyPublishers.ofString(body))
                    .build();

            // 发送HTTP请求
            final HttpResponse<String> httpResponse = AgentGameServer.getInstance().getHttpClientMrg().send(request, HttpResponse.BodyHandlers.ofString());
            final String response = httpResponse.body();
            LOGGER.info("body: {}", response);

            // 解析响应
            final JSONObject responseJson = JSONObject.parseObject(response);
            final int code = responseJson.getIntValue("code");

            if (code == 0) {
                // 成功，获取游戏URL
                String gameUrl = responseJson.getJSONObject("data").getString("gameurl");

                res.setGameUrl(gameUrl)
                        .setGameId(req.getGameId())
                        .setCurrencyId(req.getCurrencyId())
                        .setGameType(req.getGameType())
                        .setGameCurrencyId(req.getGameCurrencyId())
                        .setPlatformId(req.getPlatformId())
                        .setBonus(req.getBonus());

                LOGGER.info("gameUrl: {}", gameUrl);

                MsgUtil.sendInnerMsg(session, res.build(), player.getPlayerId(), udpSessionId);
                return true;
            } else {
                LOGGER.info("code: {}", code);
                res.setError(ErrorCode.Data_Error.getCode());
                MsgUtil.sendInnerMsg(session, res.build(), player.getPlayerId(), udpSessionId);
                return false;
            }
        } catch (Exception e) {
            LOGGER.error("AtGame entryAgentGame error", e);
            res.setError(ErrorCode.Internal_Server_Error.getCode());
            MsgUtil.sendInnerMsg(session, res.build(), player.getPlayerId(), udpSessionId);
            return false;
        }
    }

    @Override
    public void notifyUpdateCurrency(InnerMessage.NotifyData req, long pid) {
        final Map<String, Object> responseMap = new LinkedHashMap<>();
        try {
            final JSONObject data = new JSONObject();
            data.put("uname", req.getPlayerName());
            data.put("betid", req.getData());
            data.put("balance", BigDecimalUtils.round(req.getBalance(), 2));

            responseMap.put("code", 0);
            responseMap.put("msg", "");
            responseMap.put("data", data);

            final Channel session = AgentGameMrg.getInstance().getUdpSessionMap().get(req.getUpdSessionId());
            MsgUtil.responseHttp(responseMap, session);
        } catch (Exception e) {
            LOGGER.error("updateCurrency", e);
        } finally {
            AgentGameMrg.getInstance().removeUdpSessionMap(req.getUpdSessionId());
        }
    }
}
