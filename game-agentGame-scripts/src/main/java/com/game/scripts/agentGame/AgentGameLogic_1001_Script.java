package com.game.scripts.agentGame;

import com.alibaba.fastjson.JSONObject;
import com.game.c_entity.middleplatform.C_BaseGamePlatform;
import com.game.engine.math.BigDecimalUtils;
import com.game.engine.utils.*;
import com.game.entity.player.GameInfo;
import com.game.entity.player.FreeGameInfo;
import com.game.entity.player.Player;
import com.game.enums.ErrorCode;
import com.game.enums.GameType;
import com.game.gamesr.main.AgentGameServer;
import com.game.gamesr.manager.AgentGameMrg;
import com.game.gamesr.manager.DataAgentGameMrg;
import com.game.gamesr.scripts.IAgentGameLogicScript;
import com.game.gamesr.utils.JWTUtils;
import com.proto.HallMessage;
import com.proto.InnerMessage;
import com.proto.MIDMessage;
import io.netty.channel.Channel;
import io.netty.util.internal.StringUtil;
import org.apache.http.HttpStatus;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.net.URI;
import java.net.http.HttpClient;
import java.net.http.HttpRequest;
import java.net.http.HttpResponse;
import java.time.Duration;
import java.time.Instant;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;

/**
 * TaDa
 */
public class AgentGameLogic_1001_Script implements IAgentGameLogicScript {
    private static final Logger LOGGER = LoggerFactory.getLogger(AgentGameLogic_1001_Script.class);

    @Override
    public boolean entryAgentGame(HallMessage.ReqEntryAgentGameMessage req, Player player, Channel session, long udpSessionId) {
        final HallMessage.ResEntryAgentGameMessage.Builder res = HallMessage.ResEntryAgentGameMessage.newBuilder();
        res.setMsgID(MIDMessage.MID.ResEntryAgentGame_VALUE);
        try {
            final int platformId = req.getPlatformId();
            final String platformGameId = req.getPlatformGameId();
            final String currency = req.getCurrencyName();

            final C_BaseGamePlatform c_baseGamePlatform = DataAgentGameMrg.getInstance()
                    .findC_BaseGamePlatformId(this.getClass().getSimpleName(), platformId);
            if (c_baseGamePlatform == null) {
                return false;
            }

            final String referenceId = executeFreeGame(req, player, c_baseGamePlatform);

            final JWTUtils.JWData jwData = new JWTUtils.JWData();
            jwData.userId = player.getPlayerId();
            jwData.currency = currency;
            jwData.playerName = AgentGameMrg.getPlatformUserName(currency, player.getPlayerId());
            jwData.expires = TimeUtil.currentTimeMillis() + 7 * TimeUtil.DAY;

            final String token = JWTUtils.jwtToken(jwData);
            String language = "en-US";
            if (player.getLanguage() == 2) {
                language = "pt-BR";
            }

            final Map<String, Object> paramsMap = new LinkedHashMap<>();
            paramsMap.put("Token", token);//游戏代码
            paramsMap.put("GameId", platformGameId);
            paramsMap.put("Lang", language);
            paramsMap.put("AgentId", c_baseGamePlatform.getAgent());
            final String key = sign(c_baseGamePlatform, paramsMap);

            final String uri = c_baseGamePlatform.getApiUrl() + "/singleWallet/LoginWithoutRedirect";
            LOGGER.warn("url：{}", uri);
            final String body = String.format("Token=%s&GameId=%s&Lang=%s&AgentId=%s&Key=%s", token, platformGameId, language, c_baseGamePlatform.getAgent(), key);
            LOGGER.warn("body：{}", body);

            final HttpRequest request = HttpRequest.newBuilder()
                    .timeout(Duration.ofSeconds(15))
                    .uri(URI.create(uri))
                    .version(HttpClient.Version.HTTP_1_1)
                    .header("Content-Type", HttpUtils11.HTTP_CONTENT_TYPE_FORM)
                    .POST(HttpRequest.BodyPublishers.ofString(body))
                    .build();
            final HttpResponse<String> httpResponse = AgentGameServer.getInstance().getHttpClientMrg().send(request, HttpResponse.BodyHandlers.ofString());
            notifyClient(req, player, session, udpSessionId, referenceId, httpResponse);
        } catch (Exception e) {
            LOGGER.error("entryAgentGame", e);
            res.setError(ErrorCode.Internal_Server_Error.getCode());
            MsgUtil.sendInnerMsg(session, res.build(), player.getPlayerId(), udpSessionId);
            return false;
        }
        return true;
    }

    private void notifyClient(HallMessage.ReqEntryAgentGameMessage req, Player player,
                              Channel session, long udpSessionId, String referenceId,
                              HttpResponse<String> httpResponse) {
        final HallMessage.ResEntryAgentGameMessage.Builder res = HallMessage.ResEntryAgentGameMessage.newBuilder();
        res.setMsgID(MIDMessage.MID.ResEntryAgentGame_VALUE);

        if (httpResponse.statusCode() != HttpStatus.SC_OK) {
            LOGGER.warn("exception msg：{}", httpResponse.body());
            res.setError(ErrorCode.Internal_Server_Error.getCode());
            MsgUtil.sendInnerMsg(session, res.build(), player.getPlayerId(), udpSessionId);
            throw new IllegalArgumentException("notifyClient");
        }

        final String body = httpResponse.body();
        LOGGER.info("response：{}", body);

        final JSONObject response = JsonUtils.readFromJson(body, JSONObject.class);
        final int errorCode = response.getInteger("ErrorCode");
        final String data = response.getString("Data");

        if (errorCode != 0) {//失败
            res.setError(ErrorCode.AgentGame_Error.getCode());
        } else {
            res.setGameUrl(data)
                    .setGameId(req.getGameId())
                    .setCurrencyId(req.getCurrencyId())
                    .setGameType(req.getGameType())
                    .setGameCurrencyId(req.getGameCurrencyId())
                    .setPlatformId(req.getPlatformId())
                    .setReferenceId(referenceId)
                    .setBonus(req.getBonus());
        }
        MsgUtil.sendInnerMsg(session, res.build(), player.getPlayerId(), udpSessionId);
    }

    private static String sign(C_BaseGamePlatform c_baseGamePlatform, Map<String, Object> paramsMap) {
        final String querystring = MsgUtil.createObjectGetUrl(paramsMap);
//        LOGGER.warn("querystring：{}", querystring);

        final ZoneId zoneId = ZoneId.of("UTC-4");
        // 获取当前时间并转为 UTC-4
        final ZonedDateTime utcMinus4 = ZonedDateTime.now(zoneId);
        // 格式化为 yyMMdd
        final DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyMMd");
        final String now = utcMinus4.format(formatter);
        final String keyGStr = now + c_baseGamePlatform.getAgent() + c_baseGamePlatform.getSecretKey();
//        LOGGER.warn("keyGStr：{}", keyGStr);
        final String keyG = MD5.MD5Encode(keyGStr);
//        LOGGER.warn("keyG：{}", keyG);

        final var str1 = (int) (Math.random() * 900000) + 100000;
        final var str2 = (int) (Math.random() * 900000) + 100000;
        final var md5String = MD5.MD5Encode(querystring + keyG);
//        LOGGER.warn("md5String：{}", md5String);
        return str1 + md5String + str2;
    }

    private boolean cancelFreeGame(String referenceId, C_BaseGamePlatform c_baseGamePlatform) {
        try {
            if (StringUtil.isNullOrEmpty(referenceId)) {
                return true;
            }

            final Map<String, Object> paramsMap = new LinkedHashMap<>();
            paramsMap.put("ReferenceId", referenceId);
            paramsMap.put("AgentId", c_baseGamePlatform.getAgent());
            final String key = sign(c_baseGamePlatform, paramsMap);

            final String uri = c_baseGamePlatform.getApiUrl() + "/CancelFreeSpin";
            LOGGER.warn("url：{}", uri);
            final String body = MsgUtil.createObjectGetUrl(paramsMap) + String.format("&AgentId=%s&Key=%s", c_baseGamePlatform.getAgent(), key);
            LOGGER.warn("body：{}", body);

            final HttpRequest request = HttpRequest.newBuilder()
                    .timeout(Duration.ofSeconds(15))
                    .uri(URI.create(uri))
                    .version(HttpClient.Version.HTTP_1_1)
                    .header("Content-Type", HttpUtils11.HTTP_CONTENT_TYPE_FORM)
                    .POST(HttpRequest.BodyPublishers.ofString(body))
                    .build();
            final HttpResponse<String> httpResponse = AgentGameServer.getInstance().getHttpClientMrg().send(request, HttpResponse.BodyHandlers.ofString());
            LOGGER.warn("AgentGameLogic_1001_Script，cancelFreeGame：{}", httpResponse.body());

            final JSONObject res = JsonUtils.readFromJson(httpResponse.body(), JSONObject.class);
            final int errorCode = res.getInteger("ErrorCode");
            if (errorCode != 0) {
                return false;
            }
        } catch (Exception e) {
            LOGGER.error("AgentGameLogic_1001_Script，cancelFreeGame", e);
        }
        return true;
    }

    private String executeFreeGame(HallMessage.ReqEntryAgentGameMessage req, Player player, C_BaseGamePlatform c_baseGamePlatform) {
        try {
            final String platformGameId = req.getPlatformGameId();
            final int gameType = req.getGameType();
            final String currency = req.getCurrencyName();

            if (gameType != GameType.Casino_Slots.getType()) {
                return "";
            }

            final FreeGameInfo freeGameInfo = player.getFreeGameInfo(req.getCurrencyId());
            final GameInfo gameInfo = freeGameInfo.getFreeGame(req.getGameId());
            if (gameInfo.getFreeTimes() == 0) {
                return "";
            }

            //取消免费
            if (!cancelFreeGame(gameInfo.getReferenceId(), c_baseGamePlatform)) {
                return "";
            }

            final long time = TimeUtil.currentTimeMillis() / 1000 + 30 * TimeUtil.DAY / 1000;
            final ZoneId zoneId = ZoneId.of("UTC-4");
            // 获取当前时间并转为 UTC-4
            final ZonedDateTime utcMinus4 = ZonedDateTime.ofInstant(Instant.ofEpochSecond(time), zoneId);
            // 格式化为 yyMMdd
            final DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss");
            final String now = utcMinus4.format(formatter);

            final String referenceId = UUID.randomUUID().toString();
            final Map<String, Object> paramsMap = new LinkedHashMap<>();
            paramsMap.put("Account", AgentGameMrg.getPlatformUserName(currency, player.getPlayerId()));
            paramsMap.put("Currency", currency);
            paramsMap.put("ReferenceId", referenceId);
            paramsMap.put("FreeSpinValidity", now);
            paramsMap.put("NumberOfRounds", gameInfo.getFreeTimes());
            paramsMap.put("GameIds", platformGameId);
            paramsMap.put("AgentId", c_baseGamePlatform.getAgent());
            final String key = sign(c_baseGamePlatform, paramsMap);

            final String uri = c_baseGamePlatform.getApiUrl() + "/CreateFreeSpin";
            LOGGER.warn("url：{}", uri);
            final String body = MsgUtil.createObjectGetUrl(paramsMap) + String.format("&AgentId=%s&Key=%s", c_baseGamePlatform.getAgent(), key);
            LOGGER.warn("body：{}", body);

            final HttpRequest request = HttpRequest.newBuilder()
                    .timeout(Duration.ofSeconds(15))
                    .uri(URI.create(uri))
                    .version(HttpClient.Version.HTTP_1_1)
                    .header("Content-Type", HttpUtils11.HTTP_CONTENT_TYPE_FORM)
                    .POST(HttpRequest.BodyPublishers.ofString(body))
                    .build();
            final HttpResponse<String> httpResponse = AgentGameServer.getInstance().getHttpClientMrg().send(request, HttpResponse.BodyHandlers.ofString());
            LOGGER.warn("AgentGameLogic_1001_Script，giveawayFree：{}", httpResponse.body());
            return referenceId;
        } catch (Exception e) {
            LOGGER.error("AgentGameLogic_1001_Script，giveawayFree", e);
        }
        return "";
    }

    @Override
    public void notifyUpdateCurrency(InnerMessage.NotifyData req, long pid) {
        final Map<String, Object> responseMap = new LinkedHashMap<>();
        try {
            final double balance = req.getBalance();
            responseMap.put("errorCode", 0);
            responseMap.put("message", "success");
            responseMap.put("username", req.getPlayerName());
            responseMap.put("currency", req.getCurrency());
            responseMap.put("balance", BigDecimalUtils.round(balance, 2));

            final Channel session = AgentGameMrg.getInstance().getUdpSessionMap().get(req.getUpdSessionId());
            MsgUtil.responseHttp(responseMap, session);
        } catch (Exception e) {
            LOGGER.error("updateCurrency", e);
        } finally {
            AgentGameMrg.getInstance().removeUdpSessionMap(req.getUpdSessionId());
        }
    }

}
