//package com.game.scripts.agentGame;
//
//import com.game.c_entity.middleplatform.C_BaseGamePlatform;
//import com.game.engine.utils.*;
//import com.game.entity.player.Player;
//import com.game.enums.ErrorCode;
//import com.game.gamesr.main.AgentGameServer;
//import com.game.gamesr.manager.AgentGameMrg;
//import com.game.gamesr.manager.DataAgentGameMrg;
//import com.game.gamesr.scripts.IAgentGameLogicScript;
//import com.game.gamesr.utils.SHAUtils;
//import com.proto.*;
//import io.netty.channel.Channel;
//import org.apache.http.HttpStatus;
//import org.slf4j.Logger;
//import org.slf4j.LoggerFactory;
//
//import java.net.URI;
//import java.net.http.HttpClient;
//import java.net.http.HttpRequest;
//import java.net.http.HttpResponse;
//import java.time.Duration;
//import java.util.Base64;
//import java.util.LinkedHashMap;
//import java.util.List;
//import java.util.Map;
//
//public class AgentGameLogic_1000_Script implements IAgentGameLogicScript {
//    private static final Logger LOGGER = LoggerFactory.getLogger(AgentGameLogic_1000_Script.class);
//
//    @Override
//    public boolean entryAgentGame(HallMessage.ReqEntryAgentGameMessage req, Player player, Channel session, long udpSessionId) {
//        final HallMessage.ResEntryAgentGameMessage.Builder res = HallMessage.ResEntryAgentGameMessage.newBuilder();
//        res.setMsgID(MIDMessage.MID.ResEntryAgentGame_VALUE);
//        try {
//            final String currencyName = req.getCurrencyName();
//            final int gameId = req.getGameId();
//            final int platformId = req.getPlatformId();
//            final int gameType = req.getGameType();
//            final String platformGameId = req.getPlatformGameId();
//
//            final C_BaseGamePlatform c_baseGamePlatform = DataAgentGameMrg.getInstance().findC_BaseGamePlatform(this.getClass().getSimpleName(), platformId, gameType);
//            if (c_baseGamePlatform == null) {
//                return false;
//            }
//
//            final String agent = c_baseGamePlatform.getAgent();
//            final String secretKey = c_baseGamePlatform.getSecretKey();
//            final long timestamp = TimeUtil.currentTimeMillis() / 1000;
//
//            final Map<String, Object> paramsMap = new LinkedHashMap<>();
//            paramsMap.put("game", platformGameId);//游戏代码
//            paramsMap.put("player", c_baseGamePlatform.getPlatformName() + "_" + player.getPlayerId());
//            paramsMap.put("player_name", player.getPlayerName());
//            paramsMap.put("currency", currencyName.toLowerCase());
//            paramsMap.put("lang", "en");
//            paramsMap.put("device", "web");//游戏 UID
//            paramsMap.put("lobby", "https://www.wingaming.com/");
//            paramsMap.put("fun", false);
//            final List<Map.Entry<String, Object>> sortMap = AgentGameMrg.sortMap(paramsMap);
//            final String formData = AgentGameMrg.formData(sortMap) + ":" + secretKey + "" + timestamp;
////            LOGGER.info("formData：{}", formData);
//            final String sign = Base64.getEncoder().encodeToString(SHAUtils.hmacSha256(secretKey, formData).getBytes()).replaceAll("=", "");
////            LOGGER.info("sign：{}", sign);
//
//            final String uri = c_baseGamePlatform.getApiUrl() + "/seamless/game_url";
//            final String paramsJson = JsonUtils.writeAsJson(paramsMap);
//            final HttpRequest request = HttpRequest.newBuilder()
//                    .timeout(Duration.ofSeconds(15))
//                    .uri(URI.create(uri))
//                    .version(HttpClient.Version.HTTP_1_1)
//                    .header("Content-Type", HttpUtils11.HTTP_CONTENT_TYPE_JSON)
//                    .header("Signature", agent + ":" + sign)
//                    .header("Timestamp", timestamp + "")
//                    .POST(HttpRequest.BodyPublishers.ofString(paramsJson))
//                    .build();
//            AgentGameServer.getInstance().getHttpClientMrg().sendAsync(request, HttpResponse.BodyHandlers.ofString())
//                    .whenComplete(((httpResponse, throwable) -> notifyClient(player, gameId, session, udpSessionId, httpResponse, throwable)));
//        } catch (Exception e) {
//            LOGGER.error("entryAgentGame", e);
//            res.setError(ErrorCode.Internal_Server_Error.getCode());
//            MsgUtil.sendInnerMsg(session, res.build(), player.getPlayerId(), udpSessionId);
//            return false;
//        }
//        return true;
//    }
//
//    private void notifyClient(Player player, int gameId, Channel session, long udpSessionId, HttpResponse<String> httpResponse, Throwable throwable) {
//        final HallMessage.ResEntryAgentGameMessage.Builder res = HallMessage.ResEntryAgentGameMessage.newBuilder();
//        res.setMsgID(MIDMessage.MID.ResEntryAgentGame_VALUE);
//
//        if (throwable != null || httpResponse.statusCode() != HttpStatus.SC_OK) {
//            LOGGER.warn("exception msg：{}", httpResponse.body());
//            res.setError(ErrorCode.Internal_Server_Error.getCode());
//            MsgUtil.sendInnerMsg(session, res.build(), player.getPlayerId(), udpSessionId);
//            throw new IllegalArgumentException(throwable);
//        }
//
//        final String body = httpResponse.body();
//        LOGGER.info("response：{}", body);
//
//        if (body.contains("message")) {//失败
//            res.setError(ErrorCode.AgentGame_Error.getCode());
//        } else {
//            res.setGameUrl(body);
//        }
//        res.setGameId(gameId);
//        MsgUtil.sendInnerMsg(session, res.build(), player.getPlayerId(), udpSessionId);
//    }
//
//}
