package com.game.scripts.agentGame;

import com.alibaba.fastjson.JSONObject;
import com.game.c_entity.middleplatform.C_BaseGamePlatform;
import com.game.engine.math.BigDecimalUtils;
import com.game.engine.utils.HttpUtils11;
import com.game.engine.utils.JsonUtils;
import com.game.engine.utils.MsgUtil;
import com.game.engine.utils.TimeUtil;
import com.game.entity.player.FreeGameInfo;
import com.game.entity.player.GameInfo;
import com.game.entity.player.Player;
import com.game.enums.ErrorCode;
import com.game.enums.GameType;
import com.game.gamesr.main.AgentGameServer;
import com.game.gamesr.manager.AgentGameMrg;
import com.game.gamesr.manager.DataAgentGameMrg;
import com.game.gamesr.scripts.IAgentGameLogicScript;
import com.game.gamesr.utils.JWTUtils;
import com.proto.HallMessage;
import com.proto.InnerMessage;
import com.proto.MIDMessage;
import io.netty.channel.Channel;
import io.netty.util.internal.StringUtil;
import org.apache.http.HttpStatus;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.net.URI;
import java.net.URLEncoder;
import java.net.http.HttpClient;
import java.net.http.HttpRequest;
import java.net.http.HttpResponse;
import java.time.Duration;
import java.util.LinkedHashMap;
import java.util.Map;
import java.util.UUID;

/**
 * PG
 */
public class AgentGameLogic_1004_Script implements IAgentGameLogicScript {
    private static final Logger LOGGER = LoggerFactory.getLogger(AgentGameLogic_1004_Script.class);

    @Override
    public boolean entryAgentGame(HallMessage.ReqEntryAgentGameMessage req, Player player, Channel session, long udpSessionId) {
        final HallMessage.ResEntryAgentGameMessage.Builder res = HallMessage.ResEntryAgentGameMessage.newBuilder();
        res.setMsgID(MIDMessage.MID.ResEntryAgentGame_VALUE);
        try {

            final String referenceId = executeFreeGame(req, player);

            final int platformId = req.getPlatformId();
            final String platformGameId = req.getPlatformGameId();
            final String currency = req.getCurrencyName();

            final C_BaseGamePlatform c_baseGamePlatform = DataAgentGameMrg.getInstance()
                    .findC_BaseGamePlatformId(this.getClass().getSimpleName(), platformId);
            if (c_baseGamePlatform == null) {
                return false;
            }

            final JWTUtils.JWData jwData = new JWTUtils.JWData();
            jwData.userId = player.getPlayerId();
            jwData.currency = currency;
            jwData.playerName = AgentGameMrg.getPlatformUserName(currency, player.getPlayerId());
            jwData.expires = TimeUtil.currentTimeMillis() + 7 * TimeUtil.DAY;
            final String token = JWTUtils.jwtToken(jwData);

            String language = "en";
            if (player.getLanguage() == 2) {
                language = "pt";
            }

            final Map<String, Object> paramsMap = new LinkedHashMap<>();
            paramsMap.put("operator_token", c_baseGamePlatform.getToken());
            paramsMap.put("path", URLEncoder.encode(String.format("/%s/index.html", platformGameId), "UTF-8"));
            paramsMap.put("url_type", "game-entry");
            paramsMap.put("client_ip", player.getIp());
            paramsMap.put("extra_args", URLEncoder.encode(String.format("btt=1&ops=%s&l=%s", token, language), "UTF-8"));

            final String body = MsgUtil.createObjectGetUrl(paramsMap);
            LOGGER.warn("body：{}", body);

            final String uri = String.format("%s/external-game-launcher/api/v1/GetLaunchURLHTML?trace_id=%s", c_baseGamePlatform.getApiUrl(), UUID.randomUUID());
            LOGGER.warn("url：{}", uri);

            final HttpRequest request = HttpRequest.newBuilder()
                    .timeout(Duration.ofSeconds(15))
                    .uri(URI.create(uri))
                    .version(HttpClient.Version.HTTP_1_1)
                    .header("Cache-Control", "no-cache, no-store, must-revalidate")
                    .header("Content-Type", HttpUtils11.HTTP_CONTENT_TYPE_FORM)
                    .POST(HttpRequest.BodyPublishers.ofString(body))
                    .build();
            final HttpResponse<String> httpResponse = AgentGameServer.getInstance().getHttpClientMrg().send(request, HttpResponse.BodyHandlers.ofString());
            notifyClient(req, player, session, udpSessionId, referenceId, httpResponse);
        } catch (Exception e) {
            LOGGER.error("entryAgentGame", e);
            res.setError(ErrorCode.Internal_Server_Error.getCode());
            MsgUtil.sendInnerMsg(session, res.build(), player.getPlayerId(), udpSessionId);
            return false;
        }
        return true;
    }

    private void notifyClient(HallMessage.ReqEntryAgentGameMessage req, Player player,
                              Channel session, long udpSessionId, String referenceId,
                              HttpResponse<String> httpResponse) {
        final HallMessage.ResEntryAgentGameMessage.Builder res = HallMessage.ResEntryAgentGameMessage.newBuilder();
        res.setMsgID(MIDMessage.MID.ResEntryAgentGame_VALUE);

        if (httpResponse.statusCode() != HttpStatus.SC_OK) {
            LOGGER.warn("exception msg：{}", httpResponse.body());
            res.setError(ErrorCode.Internal_Server_Error.getCode());
            MsgUtil.sendInnerMsg(session, res.build(), player.getPlayerId(), udpSessionId);
            throw new IllegalArgumentException("notifyClient");
        }

        final String body = httpResponse.body();

        res.setGameUrl(body)
                .setUrlType(1)
                .setGameId(req.getGameId())
                .setCurrencyId(req.getCurrencyId())
                .setGameType(req.getGameType())
                .setGameCurrencyId(req.getGameCurrencyId())
                .setPlatformId(req.getPlatformId())
                .setReferenceId(referenceId)
                .setBonus(req.getBonus());
        MsgUtil.sendInnerMsg(session, res.build(), player.getPlayerId(), udpSessionId);
    }

    private String executeFreeGame(HallMessage.ReqEntryAgentGameMessage req, Player player) {
        try {
            final int platformId = req.getPlatformId();
            final String platformGameId = req.getPlatformGameId();
            final int gameType = req.getGameType();
            final String currency = req.getCurrencyName();

            if (gameType != GameType.Casino_Slots.getType()) {
                return "";
            }

            final FreeGameInfo freeGameInfo = player.getFreeGameInfo(req.getCurrencyId());
            final GameInfo gameInfo = freeGameInfo.getFreeGame(req.getGameId());
            if (gameInfo.getFreeTimes() == 0) {
                return "";
            }

            final C_BaseGamePlatform c_baseGamePlatform = DataAgentGameMrg.getInstance()
                    .findC_BaseGamePlatformId(this.getClass().getSimpleName(), platformId);
            if (c_baseGamePlatform == null) {
                return "";
            }

            //取消免费
            if (!cancelFreeGame(gameInfo.getReferenceId(), c_baseGamePlatform)) {
                return "";
            }

            final Map<String, Object> paramsMap = new LinkedHashMap<>();
            paramsMap.put("operator_token", c_baseGamePlatform.getToken());
            paramsMap.put("secret_key", c_baseGamePlatform.getSecretKey());
            paramsMap.put("currency", currency);
            paramsMap.put("free_game_name", platformGameId);
            paramsMap.put("expired_date", TimeUtil.currentTimeMillis() + 30 * TimeUtil.DAY);
            paramsMap.put("conversion_type", "C");
            paramsMap.put("bet_amount", gameInfo.getBet());
            paramsMap.put("game_count", gameInfo.getFreeTimes());
            paramsMap.put("game_ids", platformGameId);
            paramsMap.put("is_event", false);
            paramsMap.put("bonus_type", 1);
            paramsMap.put("bonus_ratio", 1);
            paramsMap.put("transaction_id", UUID.randomUUID().toString());

            final String body = MsgUtil.createObjectGetUrl(paramsMap);
            LOGGER.warn("body：{}", body);

            final String uri = String.format("%s/external/FreeGame/v1/CreateFreeGameByBetAmount?trace_id=%s", c_baseGamePlatform.getApiUrl(), UUID.randomUUID());
            LOGGER.warn("url：{}", uri);

            final HttpRequest request = HttpRequest.newBuilder()
                    .timeout(Duration.ofSeconds(15))
                    .uri(URI.create(uri))
                    .version(HttpClient.Version.HTTP_1_1)
                    .header("Cache-Control", "no-cache, no-store, must-revalidate")
                    .header("Content-Type", HttpUtils11.HTTP_CONTENT_TYPE_FORM)
                    .POST(HttpRequest.BodyPublishers.ofString(body))
                    .build();
            final HttpResponse<String> response = AgentGameServer.getInstance().getHttpClientMrg().send(request, HttpResponse.BodyHandlers.ofString());
            LOGGER.warn("giveawayFreeGame：{}", response.body());
            //{"data":{"freeGameId":262075},"error":null}
            final JSONObject res = JsonUtils.readFromJson(response.body(), JSONObject.class);
            final JSONObject data = res.getJSONObject("data");
            if (data == null) {
                return "";
            }
            final String freeGameId = data.getString("freeGameId");
            if (StringUtil.isNullOrEmpty(freeGameId)) {
                return "";
            }

            bindFreeGame(req, player, c_baseGamePlatform, freeGameId);

            return freeGameId;
        } catch (Exception e) {
            LOGGER.error("AgentGameLogic_1004_Script，giveawayFree", e);
        }
        return "";
    }

    private void bindFreeGame(HallMessage.ReqEntryAgentGameMessage req, Player player, C_BaseGamePlatform c_baseGamePlatform, String freeGameId) {
        try {
            final String currency = req.getCurrencyName();

            final Map<String, Object> paramsMap = new LinkedHashMap<>();
            paramsMap.put("operator_token", c_baseGamePlatform.getToken());
            paramsMap.put("secret_key", c_baseGamePlatform.getSecretKey());
            paramsMap.put("free_game_id", freeGameId);
            paramsMap.put("player_names", AgentGameMrg.getPlatformUserName(currency, player.getPlayerId()));
            paramsMap.put("transfer_reference", UUID.randomUUID().toString());

            final String body = MsgUtil.createObjectGetUrl(paramsMap);
            LOGGER.warn("body：{}", body);

            final String uri = String.format("%s/external/FreeGame/v1/TransferInFreeGame?trace_id=%s", c_baseGamePlatform.getApiUrl(), UUID.randomUUID());
            LOGGER.warn("url：{}", uri);

            final HttpRequest request = HttpRequest.newBuilder()
                    .timeout(Duration.ofSeconds(15))
                    .uri(URI.create(uri))
                    .version(HttpClient.Version.HTTP_1_1)
                    .header("Cache-Control", "no-cache, no-store, must-revalidate")
                    .header("Content-Type", HttpUtils11.HTTP_CONTENT_TYPE_FORM)
                    .POST(HttpRequest.BodyPublishers.ofString(body))
                    .build();
            final HttpResponse<String> response = AgentGameServer.getInstance().getHttpClientMrg().send(request, HttpResponse.BodyHandlers.ofString());
            LOGGER.warn("bindFreeGame：{}", response.body());
        } catch (Exception e) {
            LOGGER.error("AgentGameLogic_1004_Script，bindFreeGame", e);
        }
    }

    private boolean cancelFreeGame(String referenceId, C_BaseGamePlatform c_baseGamePlatform) {
        try {
            if (StringUtil.isNullOrEmpty(referenceId)) {
                return true;
            }

            final Map<String, Object> paramsMap = new LinkedHashMap<>();
            paramsMap.put("operator_token", c_baseGamePlatform.getToken());
            paramsMap.put("secret_key", c_baseGamePlatform.getSecretKey());
            paramsMap.put("free_game_id", referenceId);

            final String body = MsgUtil.createObjectGetUrl(paramsMap);
            LOGGER.warn("body：{}", body);

            final String uri = String.format("%s/external/FreeGame/v1/CancelFreeGame?trace_id=%s", c_baseGamePlatform.getApiUrl(), UUID.randomUUID());
            LOGGER.warn("url：{}", uri);

            final HttpRequest request = HttpRequest.newBuilder()
                    .timeout(Duration.ofSeconds(15))
                    .uri(URI.create(uri))
                    .version(HttpClient.Version.HTTP_1_1)
                    .header("Cache-Control", "no-cache, no-store, must-revalidate")
                    .header("Content-Type", HttpUtils11.HTTP_CONTENT_TYPE_FORM)
                    .POST(HttpRequest.BodyPublishers.ofString(body))
                    .build();
            final HttpResponse<String> response = AgentGameServer.getInstance().getHttpClientMrg().send(request, HttpResponse.BodyHandlers.ofString());
            LOGGER.warn("cancelFreeGame：{}", response.body());

            final JSONObject res = JsonUtils.readFromJson(response.body(), JSONObject.class);
            final JSONObject error = res.getJSONObject("error");
            if (error != null) {
                return false;
            }
        } catch (Exception e) {
            LOGGER.error("AgentGameLogic_1004_Script，cancelFreeGame", e);
        }
        return true;
    }

    @Override
    public void notifyUpdateCurrency(InnerMessage.NotifyData req, long pid) {
        final Map<String, Object> responseMap = new LinkedHashMap<>();
        try {
            final double balance = req.getBalance();
            final JSONObject data = new JSONObject();
            data.put("currency_code", req.getCurrency());
            data.put("balance_amount", BigDecimalUtils.round(balance, 2));
            data.put("updated_time", req.getUpdatedTime());
            data.put("real_transfer_amount", req.getRealTransferAmount());

            responseMap.put("data", data);
            responseMap.put("error", null);

            final Channel session = AgentGameMrg.getInstance().getUdpSessionMap().get(req.getUpdSessionId());
            MsgUtil.responseHttp(responseMap, session);
        } catch (Exception e) {
            LOGGER.error("updateCurrency", e);
        } finally {
            AgentGameMrg.getInstance().removeUdpSessionMap(req.getUpdSessionId());
        }
    }
}
