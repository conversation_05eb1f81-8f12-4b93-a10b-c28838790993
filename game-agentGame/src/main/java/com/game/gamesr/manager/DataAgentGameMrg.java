package com.game.gamesr.manager;

import com.game.c_entity.middleplatform.C_BaseExchangeRate;
import com.game.c_entity.middleplatform.C_BaseGamePlatform;
import com.game.engine.io.conf.NettyServerConfig;
import com.game.engine.mongo.DBConnectionMrg;
import com.game.gamesr.main.AgentGameServer;
import com.mongodb.client.MongoClient;
import it.unimi.dsi.fastutil.ints.Int2ObjectArrayMap;
import it.unimi.dsi.fastutil.ints.Int2ObjectMap;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.mongodb.core.MongoTemplate;

import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;


public class DataAgentGameMrg {
    private static final DataAgentGameMrg instance = new DataAgentGameMrg();
    private static final Logger LOGGER = LoggerFactory.getLogger(DataAgentGameMrg.class);

    private MongoTemplate mongoTemplate;

    private final Map<Integer, C_BaseGamePlatform> c_baseGamePlatformIdMap = new ConcurrentHashMap<>(8);
    private final Map<Integer, C_BaseGamePlatform> c_baseGamePlatformSupplierIdMap = new ConcurrentHashMap<>(8);
    private final Map<Integer, C_BaseExchangeRate> c_baseExchangeRateMap = new ConcurrentHashMap<>(8);

    public static DataAgentGameMrg getInstance() {
        return instance;
    }

    public void initMongoConfig() {
        final NettyServerConfig nettyServerConfig = AgentGameServer.getInstance().getNettyServerConfig();
        final String gameData = nettyServerConfig.getMongo_config_database();
        final MongoClient mongoClient = DBConnectionMrg.getInstance().getMongoClient();
        mongoTemplate = new MongoTemplate(mongoClient, gameData);
    }

    public void loadConfigData() throws Exception {
        loadBaseGamePlatform();
        loadBaseExchangeRate();
    }

    public void loadBaseGamePlatform() throws Exception {
        //游戏平台
        c_baseGamePlatformIdMap.clear();
        c_baseGamePlatformSupplierIdMap.clear();
        List<C_BaseGamePlatform> c_baseGamePlatforms = mongoTemplate.findAll(C_BaseGamePlatform.class);
        for (C_BaseGamePlatform c_baseGamePlatform : c_baseGamePlatforms) {
            c_baseGamePlatform.check();
            c_baseGamePlatformIdMap.put(c_baseGamePlatform.getPlatformId(), c_baseGamePlatform);
            c_baseGamePlatformSupplierIdMap.put(c_baseGamePlatform.getSupplierId(), c_baseGamePlatform);
        }
    }

    public void loadBaseExchangeRate() throws Exception {
        //汇率
        c_baseExchangeRateMap.clear();
        List<C_BaseExchangeRate> c_baseExchangeRates = mongoTemplate.findAll(C_BaseExchangeRate.class);
        for (C_BaseExchangeRate c_baseExchangeRate : c_baseExchangeRates) {
            c_baseExchangeRate.check();
            c_baseExchangeRateMap.put(c_baseExchangeRate.getCurrencyId(), c_baseExchangeRate);
        }
    }

    public C_BaseGamePlatform findC_BaseGamePlatformSupplierId(String className, int supplierId) {
        final C_BaseGamePlatform c_baseGamePlatform = c_baseGamePlatformSupplierIdMap.get(supplierId);
        if (c_baseGamePlatform == null) {
            LOGGER.warn("className：{}，config，C_BaseGamePlatform，supplierId：{}，not exist", className, supplierId);
            return null;
        }
        return c_baseGamePlatform;
    }

    public C_BaseGamePlatform findC_BaseGamePlatformId(String className, int platformId) {
        final C_BaseGamePlatform c_baseGamePlatform = c_baseGamePlatformIdMap.get(platformId);
        if (c_baseGamePlatform == null) {
            LOGGER.warn("className：{}，config，C_BaseGamePlatform，platformId：{}，not exist", className, platformId);
            return null;
        }
        return c_baseGamePlatform;
    }

    public C_BaseExchangeRate findC_BaseExchangeRate(int currencyId) {
        final C_BaseExchangeRate c_baseExchangeRate = c_baseExchangeRateMap.get(currencyId);
        if (c_baseExchangeRate == null) {
            LOGGER.warn("config，C_BaseExchangeRate，currencyId：{}，not exist", currencyId);
            return null;
        }
        return c_baseExchangeRate;
    }
}
