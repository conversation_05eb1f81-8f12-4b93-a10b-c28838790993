package com.game.gamesr.manager;

import com.game.dao.game.GameNoteDao;
import com.game.dao.player.PlayerDao;
import com.game.engine.io.conf.ConstantConfig;
import com.game.engine.utils.Symbol;
import com.game.engine.utils.TimeUtil;
import com.game.entity.game.GameNote;
import com.game.entity.order.Order;
import com.game.entity.player.Player;
import com.game.manager.EntityDaoMrg;
import com.proto.HallMessage;
import io.netty.channel.Channel;
import it.unimi.dsi.fastutil.longs.Long2ObjectArrayMap;
import it.unimi.dsi.fastutil.longs.Long2ObjectMap;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

public class AgentGameMrg {
    private static final Logger LOGGER = LoggerFactory.getLogger(AgentGameMrg.class);

    private static final AgentGameMrg instance = new AgentGameMrg();

    //只读 不能修改
    private final Map<Long, Player> agentGamePlayerMap = new ConcurrentHashMap<>();

    private final Map<Long, Channel> udpSessionMap = new ConcurrentHashMap<>(8);

    public static AgentGameMrg getInstance() {
        return instance;
    }

    public Map<Long, Player> getAgentGamePlayerMap() {
        return agentGamePlayerMap;
    }

    public Map<Long, Channel> getUdpSessionMap() {
        return udpSessionMap;
    }

    public void addUdpSessionMap(long udpSessionId, Channel channel) {
        udpSessionMap.put(udpSessionId, channel);
    }

    public void removeUdpSessionMap(long udpSessionId) {
        udpSessionMap.remove(udpSessionId);
    }

    public Player findDbPlayer(long playerId) {
        Player player = agentGamePlayerMap.get(playerId);
        if (player == null) {
            player = EntityDaoMrg.getInstance().getDao(PlayerDao.class).getById(playerId);
            if (player != null) {
                agentGamePlayerMap.put(playerId, player);
                player.setHeartbeat(TimeUtil.currentTimeMillis() + 15 * TimeUtil.MIN);
            }
        }
        return player;
    }

    public void addAgentGamePlayer(HallMessage.ReqEntryAgentGameMessage req, Player player) {
        this.agentGamePlayerMap.put(player.getPlayerId(), player);
        player.setGameId(req.getGameId());
        player.setBonus(req.getBonus());
        player.setGameCurrencyId(req.getGameCurrencyId());
        player.setPlayerCurrencyId(req.getCurrencyId());
        player.setPlatformId(req.getPlatformId());
        player.setGameType(req.getGameType());
        player.setHeartbeat(TimeUtil.currentTimeMillis() + 15 * TimeUtil.MIN);
    }

    public void addGameNote(Player player, GameNote gameNote) {
        player.getGameNoteMap().put(gameNote.getRoundId(), gameNote);
        if (player.getGameNoteMap().size() >= 50) {
            final String firstKey = player.getGameNoteMap().keySet().iterator().next();
            player.getGameNoteMap().remove(firstKey);
        }
    }

    public GameNote findRoundIdGameNote(Player player, String roundId) {
        GameNote gameNote = player.getGameNoteMap().get(roundId);
        if (gameNote == null) {
            gameNote = EntityDaoMrg.getInstance().getDao(GameNoteDao.class).findByRoundIdGameNote(player.getPlayerId(), roundId);
            if (gameNote == null) {
                return null;
            }
            player.getGameNoteMap().put(roundId, gameNote);
        }
        return gameNote;
    }


    public static long getAgentGamePlayerId(String player) {
        try {
            final String[] xh = player.split(Symbol.XIAHUAXIAN_REG);
            return Long.parseLong(xh[1]);
        } catch (Exception e) {
            LOGGER.error("getAgentGamePlayerId", e);
            return 0;
        }
    }

    public static String getPlatformUserName(String prefix, long playerId) {
        if (!ConstantConfig.getInstance().isAgentGame()) {
            return prefix.toUpperCase() + "TEST_" + playerId;
        }
        return prefix.toUpperCase() + "_" + playerId;
    }

    public static List<Map.Entry<String, Object>> sortMap(final Map<String, Object> map) {
        final List<Map.Entry<String, Object>> infos = new ArrayList<>(map.entrySet());
        // 重写集合的排序方法：按字母顺序
        infos.sort(Map.Entry.comparingByKey());
        return infos;
    }

    public static String formData(List<Map.Entry<String, Object>> params) {
        final var sb = new StringBuilder();
        for (Map.Entry<String, Object> entry : params) {
            final String key = entry.getKey();
            final Object value = entry.getValue();
            if (sb.length() > 0) {
                sb.append("&");
            }
            sb.append(key).append("=").append(value);
        }
        return sb.toString();
    }

    public void timerCheckHear() {
        try {
            if (agentGamePlayerMap.isEmpty()) {
                return;
            }

            final long currentTime = TimeUtil.currentTimeMillis();
            final Iterator<Map.Entry<Long, Player>> iterator = agentGamePlayerMap.entrySet().iterator();
            while (iterator.hasNext()) {
                Map.Entry<Long, Player> entry = iterator.next();
                final Player player = entry.getValue();
                if (player != null && currentTime >= player.getHeartbeat()) {
                    iterator.remove();  // Use iterator's remove method to avoid ConcurrentModificationException
                    LOGGER.info("playerId：{}，timerCheckHear exit", player.getPlayerId());
                }
            }
        } catch (Exception e) {
            LOGGER.error("timerCheckHear", e);
        }
    }

    public void timerCheckUdpActive() {
        try {
            if (udpSessionMap.isEmpty()) {
                return;
            }

            final Iterator<Map.Entry<Long, Channel>> iterator = udpSessionMap.entrySet().iterator();
            while (iterator.hasNext()) {
                final Map.Entry<Long, Channel> entry = iterator.next();
                final Channel session = entry.getValue();
                if (!session.isActive()) {
                    iterator.remove();
                }
            }
        } catch (Exception e) {
            LOGGER.error("timerCheckUdpActive", e);
        }
    }

    public GameNote findByTransactionId(String transactionId) {
        // 直接从数据库中查询 GameNote
        return EntityDaoMrg.getInstance().getDao(GameNoteDao.class).findByTransactionId(transactionId);
    }
}
