package com.game.gamesr.scripts;

import com.game.engine.script.IBaseScript;
import com.game.entity.player.Player;
import com.proto.HallMessage;
import com.proto.InnerMessage;
import io.netty.channel.Channel;

public interface IAgentGameLogicScript extends IBaseScript {

    default boolean entryAgentGame(HallMessage.ReqEntryAgentGameMessage req, Player player, Channel session, long udpSessionId) {
        return false;
    }

    default void notifyUpdateCurrency(InnerMessage.NotifyData req, long pid) {

    }
}

