package com.game.gamesr.scripts;

import com.game.engine.script.IBaseScript;
import com.game.entity.game.GameNote;
import com.game.entity.player.Player;
import com.game.gamesr.NotifyData;

public interface IAgentGameScript extends IBaseScript {


    default boolean isIpBlacklist(String ip, int supplierId) {
        return false;
    }

    default void updateCurrency(NotifyData notifyData) {

    }

    default GameNote addGameNote(Player player, String roundId, String transactionId, double betAmount, double validBets, double balance) {
        return null;
    }

    default GameNote addGameNote(Player player, String roundId, String transactionId, double betAmount, double validBets, double balance, String remark) {
        return null;
    }

    default GameNote addGameNote(Player player, String roundId, String transactionId, int status, double betAmount, double validBets, double win, double balance) {
        return null;
    }

    default GameNote updateGameNote(Player player, String roundId, String transactionId, int status, double win, double balance) {
        return null;
    }

    default void freeAddGameNote(Player player, String roundId, String transactionId, int status, double win, double balance) {
    }

    default void updateGameNoteStatus(Player player, String roundId, int status) {

    }

    default GameNote updateGameNoteSportClose(String transactionId) {
        return null;
    }

    default void sendAgentGameLog(Player player, GameNote gameNote) {

    }


    default double realBalance(Player player) {
        return 0;
    }

    default double usdTransformAmount(Player player, double amount) {
        return 0;
    }

    default double amountTransformUsd(Player player, double amount) {
        return 0;
    }
}
