package com.game.gamesr.main;

import com.game.engine.BlockingTaskSchedulerMrg;
import com.game.engine.HttpClientMrg;
import com.game.engine.enums.MsgType;
import com.game.engine.enums.ServerType;
import com.game.engine.enums.state.ServerState;
import com.game.engine.io.conf.*;
import com.game.engine.io.handler.IHandler;
import com.game.engine.io.handler.TcpHandler;
import com.game.engine.io.message.HttpMessageBean;
import com.game.engine.io.message.MessageBean;
import com.game.engine.io.redis.RedisPoolManager;
import com.game.engine.kafka.LogProducerMrg;
import com.game.engine.mongo.DBConnectionMrg;
import com.game.engine.net.*;
import com.game.engine.script.ScriptLoader;
import com.game.engine.util.async.DefaultSameThreadScheduledExecutor;
import com.game.engine.util.async.SameThreadScheduledExecutor;
import com.game.engine.util.concurrent.DefaultThreadFactory;
import com.game.engine.util.concurrent.RejectedExecutionHandlers;
import com.game.engine.utils.*;
import com.game.enums.AgentGame;
import com.game.enums.ErrorCode;
import com.game.gamesr.manager.AgentGameMrg;
import com.game.gamesr.manager.DataAgentGameMrg;
import com.game.gamesr.manager.GameEventLoop;
import com.game.gamesr.server.http.AgentGameHttpServer;
import com.game.gamesr.server.tcp.AgentGameTcpClient2Hall;
import com.game.gamesr.server.tcp.AgentGameTcpClient2Proxy;
import com.game.manager.DBHandlerRegisterMrg;
import com.game.utils.VirtualThreadUtils;
import com.google.protobuf.Message;
import com.proto.InnerMessage;
import com.proto.MIDMessage;
import io.netty.channel.Channel;
import io.netty.util.internal.StringUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.function.Consumer;


public class AgentGameServer extends World {
    private static final Logger LOGGER = LoggerFactory.getLogger(AgentGameServer.class);

    private static AgentGameServer agentGameServer;

    // 链接HallServer
    private AgentGameTcpClient2Hall agentGameTcpClient2Hall;
    // 链接ProxyServer
    private AgentGameTcpClient2Proxy agentGameTcpClient2Proxy;
    // httpServer
    private AgentGameHttpServer agentGameHttpServer;

    private NettyServerConfig nettyServerConfig;
    private NettyClientConfig nettyClientConfig_proxy;
    private NettyClientConfig nettyClientConfig_hall;
    private NettyServerConfig nettyServerConfig_http;

    private HttpClientMrg httpClientMrg;
    private LogProducerMrg logProducerMrg;
    private UniqueIDGenerator uniqueIDGenerator;
    private SameThreadScheduledExecutor timerSystem;
    private BlockingTaskSchedulerMrg blockingTaskSchedulerMrg;

    private List<GameEventLoop> gameEventLoops = new ArrayList<>();

    public AgentGameServer() {
        super(1000 / 30);
    }

    public static AgentGameServer getInstance() {
        return agentGameServer;
    }

    public UniqueIDGenerator getUniqueIDGenerator() {
        return uniqueIDGenerator;
    }

    public AgentGameTcpClient2Proxy getAgentGameTcpClient2Proxy() {
        return agentGameTcpClient2Proxy;
    }

    public AgentGameTcpClient2Hall getAgentGameTcpClient2Hall() {
        return agentGameTcpClient2Hall;
    }

    public HttpClientMrg getHttpClientMrg() {
        return httpClientMrg;
    }

    public LogProducerMrg getLogProducerMrg() {
        return logProducerMrg;
    }

    public BlockingTaskSchedulerMrg getBlockingTaskSchedulerMrg() {
        return blockingTaskSchedulerMrg;
    }

    public NettyServerConfig getNettyServerConfig() {
        return nettyServerConfig;
    }

    public NettyClientConfig getNettyClientConfig_proxy() {
        return nettyClientConfig_proxy;
    }

    public NettyClientConfig getNettyClientConfig_hall() {
        return nettyClientConfig_hall;
    }

    public static void main(String[] args) {
        LOGGER.info("服务器启动时间：{}", TimeUtil.getDateTimeFormat(TimeUtil.YYYYMMDDHHMMSS));

        Config.path = FileUtil.getMainPath();
        LOGGER.info("配置路径为：" + Config.path);

        ScriptLoader.getInstance().init((str) ->
                SysUtil.exit(AgentGameServer.class, null, "scripts load error"));

        agentGameServer = new AgentGameServer();
        final ExecutorService service = Executors.newFixedThreadPool(1, (r) -> new Thread(r, "LOGIC_THREAD"));
        final EventConsumer<LogicEvent> eventConsumer = new EventConsumer<>(agentGameServer);
        final GlobalQueue<LogicEvent> queue = new GlobalQueue<>(service, eventConsumer);
        GlobalQueueContainerMrg.getInstance().setGlobalQueue(queue);

        Runtime.getRuntime().addShutdownHook(new Thread(AgentGameServer::stops));
    }

    private void initServerConfig() {
        ConstantConfig.reloadConstantConfig(Config.path, "constantConfig.xml");
        NettyServerConfig nettyServerConfig_agentGame = FileUtil.getConfigXML(Config.path, "nettyServerConfig_agentGame.xml", NettyServerConfig.class);
        if (nettyServerConfig_agentGame == null) {
            SysUtil.exit(this.getClass(), null, "nettyServerConfig_agentGame");
            return;
        }
        NettyClientConfig nettyClientConfig_hall = FileUtil.getConfigXML(Config.path, "nettyClientConfig_hall.xml", NettyClientConfig.class);
        if (nettyClientConfig_hall == null) {
            SysUtil.exit(this.getClass(), null, "nettyClientConfig_hall");
            return;
        }
        NettyClientConfig nettyClientConfig_proxy = FileUtil.getConfigXML(Config.path, "nettyClientConfig_proxy.xml", NettyClientConfig.class);
        if (nettyClientConfig_proxy == null) {
            SysUtil.exit(this.getClass(), null, "nettyClientConfig_proxy");
            return;
        }
        NettyServerConfig nettyServerConfig_http = FileUtil.getConfigXML(Config.path, "nettyServerConfig_http.xml", NettyServerConfig.class);
        if (nettyServerConfig_http == null) {
            SysUtil.exit(this.getClass(), null, "nettyServerConfig_http");
            return;
        }

        this.nettyServerConfig = nettyServerConfig_agentGame;
        this.nettyClientConfig_hall = nettyClientConfig_hall;
        this.nettyClientConfig_proxy = nettyClientConfig_proxy;
        this.nettyServerConfig_http = nettyServerConfig_http;

        Config.SERVER_ID = nettyServerConfig.getId(); // 设置ID
        Config.SERVER_NAME = nettyServerConfig.getName(); // 设置SERVERNAME
        Config.SERVER_CHANNEL = nettyServerConfig.getChannel(); // 设置渠道
        Config.serverState = ServerState.NORMAL;
    }

    @Override
    protected void registerSeri() {

    }

    @Override
    protected void registerProtoHandler() {

    }

    @Override
    protected void listenOrConnect() throws Exception {
        startServer();
    }

    @Override
    protected void initWhenThreadStartImpl() throws Exception {
        initServerConfig();
        this.httpClientMrg = new HttpClientMrg();
        this.logProducerMrg = new LogProducerMrg();
        final String serverId = Config.SERVER_ID + "";
        this.uniqueIDGenerator = new UniqueIDGenerator(Integer.parseInt(serverId));
        this.timerSystem = new DefaultSameThreadScheduledExecutor(4);
        this.blockingTaskSchedulerMrg = new BlockingTaskSchedulerMrg();
        for (int i = 1; i <= 5; i++) {
            final GameEventLoop gameEventLoop = new GameEventLoop(null, new DefaultThreadFactory("AgentGame_Event_Loop_" + i), RejectedExecutionHandlers.abort());
            this.gameEventLoops.add(gameEventLoop);
        }

        final JedisClusterConfig jpc = FileUtil.getConfigXML(Config.path, "jedisClusterConfig.xml", JedisClusterConfig.class);
        final RedisPoolManager redisPoolManager = new RedisPoolManager(jpc);

        final String mongoUrl = nettyServerConfig.getMongo_connection_string();
        final String mongoData = nettyServerConfig.getMongo_read_write_database();
        DBConnectionMrg.getInstance().dBConnection(mongoUrl, mongoData);
        DBHandlerRegisterMrg.getInstance().dBHandlerRegister();

        DataAgentGameMrg.getInstance().initMongoConfig();

        DataAgentGameMrg.getInstance().loadConfigData();

        this.logProducerMrg.start(this.nettyServerConfig.getKafka_connection_string());
        this.timerSystem.scheduleAtFixedDelay(5 * TimeUtil.SEC, 5 * TimeUtil.SEC, () -> {
            AgentGameServer.getInstance().serverHeartCheck();
            AgentGameMrg.getInstance().timerCheckHear();
            AgentGameMrg.getInstance().timerCheckUdpActive();
        });
    }

    @Override
    protected void tickImpl(long curTime) throws Exception {
        httpClientMrg.tick();
        timerSystem.tick();
        blockingTaskSchedulerMrg.tick();
        DBHandlerRegisterMrg.getInstance().tick(curTime);
    }

    @Override
    protected void onLogicEvent(LogicEvent evt) {
        switch (evt.getLogicEventType()) {
            /**
             * 监听大厅
             */
            case A_HALLCLIENT_ON_TCP_CONNECT: {
                agentGameTcpClient2Hall.onIoSessionConnect(evt.getChannel());
                break;
            }
            case A_HALLCLIENT_ON_DISCONNECT: {
                agentGameTcpClient2Hall.onIoSessionClosed(evt.getChannel());
                break;
            }

            /**
             * 监听代理
             */
            case A_PROXYCLIENT_ON_TCP_CONNECT: {
                agentGameTcpClient2Proxy.onIoSessionConnect(evt.getChannel());
                break;
            }
            case A_PROXYCLIENT_ON_DISCONNECT: {
                agentGameTcpClient2Proxy.onIoSessionClosed(evt.getChannel());
                break;
            }

            case A_HALLCLIENT_MESSAGE_EVENT_C_RECV:
            case A_PROXYCLIENT_MESSAGE_EVENT_C_RECV:
                messageHandler(evt);
                break;

            case UDP_MESSAGE_EVENT_S_RECV:
                httpMessageHandler(evt.getParamA(), evt.getParamC(), evt.getParamB(), evt.getChannel());
                AgentGameMrg.getInstance().addUdpSessionMap(MsgUtil.getSessionID(evt.getChannel()), evt.getChannel());
                break;
            default: {
                throw new RuntimeException();
            }
        }
    }

    private void startServer() {
        agentGameTcpClient2Hall = new AgentGameTcpClient2Hall(nettyClientConfig_hall);
        agentGameTcpClient2Proxy = new AgentGameTcpClient2Proxy(nettyClientConfig_proxy);
        agentGameHttpServer = new AgentGameHttpServer(nettyServerConfig_http);
        {
            agentGameTcpClient2Hall.start();
            agentGameTcpClient2Proxy.start();
            agentGameHttpServer.start();
        }
    }

    private static void stops() {
        try {
            Config.serverState = ServerState.CLOSING;
            if (agentGameServer.timerSystem != null) {
                agentGameServer.timerSystem.shutdownNow();
            }
            if (!agentGameServer.gameEventLoops.isEmpty()) {
                for (GameEventLoop gameEventLoop : agentGameServer.gameEventLoops) {
                    gameEventLoop.shutdownNow();
                }
            }
            VirtualThreadUtils.shutdown();

            Thread.sleep(1000);
            RedisPoolManager.getInstance().destroy();
            agentGameServer.stop();
        } catch (Exception e) {
            LOGGER.error("释放资源", e);
        }
    }

    private void stop() {
        LOGGER.warn("关闭链接大厅服务器");
        try {
            if (this.agentGameTcpClient2Hall != null) {
                this.agentGameTcpClient2Hall.stop();
            }
        } catch (Exception e) {
            LOGGER.error("关闭链接大厅服务器", e);
        }

        LOGGER.warn("关闭连接代理服务器");
        try {
            if (this.agentGameTcpClient2Proxy != null) {
                this.agentGameTcpClient2Proxy.stop();
            }
        } catch (Exception e) {
            LOGGER.error("关闭连接代理服务器", e);
        }

        LOGGER.warn("关闭连接http服务器");
        try {
            if (this.agentGameHttpServer != null) {
                this.agentGameHttpServer.stop();
            }
        } catch (Exception e) {
            LOGGER.error("关闭连接http服务器", e);
        }
    }

    /**
     * 构建游戏服信息
     *
     * @param action
     */
    private void buildRegisterUpdateMessage(Consumer<InnerMessage.InnerReqRegisterUpdateMessage> action) {
        final InnerMessage.InnerReqRegisterUpdateMessage.Builder updateServerInfoBuilder = InnerMessage.InnerReqRegisterUpdateMessage.newBuilder();
        updateServerInfoBuilder.setMsgID(MIDMessage.MID.InnerReqRegisterUpdate_VALUE);
        updateServerInfoBuilder.setServerInfo(buildServerInfo(nettyServerConfig));
        if (action != null) {
            action.accept(updateServerInfoBuilder.build());
        }
    }

    /**
     * 构建服务器信息
     *
     * @param config
     * @return
     */
    private InnerMessage.InnerServerInfo buildServerInfo(NettyServerConfig config) {
        InnerMessage.InnerServerInfo.Builder builder = InnerMessage.InnerServerInfo.newBuilder();
        builder.setId(config.getId())
                .setName(config.getName())
                .setType(config.getType().getType())
                .setPower(config.getPower())
                .setIp(config.getIp() == null ? "" : config.getIp())
                .setPort(config.getPort())
                .setHttpPort(config.getHttpPort())
                .setGameState(Config.serverState.getState());
        return builder.build();
    }

    private void messageHandler(LogicEvent event) {
        try {
            final int msgType = event.getIntParamA();
            final long id = event.getLongParamA();
            final long udpSessionId = event.getLongParamB();
            final int msgId = event.getIntParamB();
            final byte[] bytes = (byte[]) event.getParamA();
            if (msgType == MsgType.IDMESSAGE.getType()) {// 数据结构:msgId:pfbytes
                // 在本地注册，必须预处理
                final MessageBean messageBean = ScriptLoader.getInstance().getMessageBean(msgId);
                if (messageBean != null) {
                    long time = TimeUtil.currentTimeMillis();
                    final Message message = messageBean.buildMessage(bytes);
                    final TcpHandler handler = (TcpHandler) messageBean.newHandler();
                    if (handler != null) {
                        handler.setPid(id);
                        handler.setUdpSessionId(udpSessionId);
                        handler.setMsgBytes(bytes);
                        handler.setMessage(message);
                        handler.setSession(event.getChannel());
                        handler.setMsgId(msgId);
                        handler.run();
                        time = TimeUtil.currentTimeMillis() - time;
                        if (time > 5) {
                            LOGGER.warn("{}，处理时间超过，{}", handler.getClass().getSimpleName(), time);
                        }
                    }
                } else {
                    LOGGER.warn("消息[{}]代码未实现逻辑", msgId);
                }
            } else {
                LOGGER.warn("消息类型{}未实现,消息id{}，玩家{}消息处理失败", msgType, msgId, id);
            }
        } catch (Exception e) {
            LOGGER.error("channelRead", e);
        }
    }

    private void httpMessageHandler(Object msg, Object head, Object uri, Channel session) {
        try {
            @SuppressWarnings("unchecked") final Map<String, Object> paramsMap = (Map<String, Object>) msg;
            @SuppressWarnings("unchecked") final Map<String, Object> headMap = (Map<String, Object>) head;

            final String xst8sign = (String) headMap.get("x-st8-sign");
            if (!StringUtil.isNullOrEmpty(xst8sign)) {
                paramsMap.put("x-st8-sign", xst8sign);
            }

            // 添加Funky协议需要的HTTP头信息
            final String userAgent = (String) headMap.get("user-agent");
            if (!StringUtil.isNullOrEmpty(userAgent)) {
                paramsMap.put("userAgent", userAgent);
            }

            final String authentication = (String) headMap.get("authentication");
            if (!StringUtil.isNullOrEmpty(authentication)) {
                paramsMap.put("authentication", authentication);
            }

            final String xRequestId = (String) headMap.get("x-request-id");
            if (!StringUtil.isNullOrEmpty(xRequestId)) {
                paramsMap.put("xRequestId", xRequestId);
            }

            final String requestPath = (String) uri;
            if (StringUtil.isNullOrEmpty(requestPath) || paramsMap.isEmpty()) {
                LOGGER.warn("ip ：{}，request error：{}，params：{}", MsgUtil.getClientIp(session), requestPath, JsonUtils.writeAsJson(paramsMap));
                MsgUtil.responseHttp(ErrorCode.Data_Error.getCode(), session);
                return;
            }

            final HttpMessageBean httpMessageBean = ScriptLoader.getInstance().getHttpMessageBean(requestPath);
            if (httpMessageBean == null) {
                LOGGER.error("HttpMessagePoll，not find，content = {} httpMessageBean", requestPath);
                MsgUtil.responseHttp(ErrorCode.Data_Error.getCode(), session);
                return;
            }

            IHandler handler = httpMessageBean.newHandler();
            handler.setSession(session);
            handler.setCreateTime(TimeUtil.currentTimeMillis());
            handler.setParamsMap(paramsMap);
            if (requestPath.contains("reload")) {
                VirtualThreadUtils.execute(handler);
            } else if (requestPath.contains("middlePlatform")) {
                handler.run();
            } else {
                try {
                    final String[] request = requestPath.split("/");
                    final String params = request[1];
                    final int agentGameId = AgentGame.valueOfs(params);
                    asyncExecute(agentGameId, handler);
                } catch (Exception e) {
                    handler.run();
                    LOGGER.error("httpMessageHandler", e);
                }
            }
        } catch (Exception e) {
            LOGGER.error("httpMessageHandler", e);
            MsgUtil.responseHttp(ErrorCode.Data_Error.getCode(), session);
        }
    }

    private void serverHeartCheck() {
        try {
            AgentGameServer.getInstance().getAgentGameTcpClient2Proxy().checkStatus();
            AgentGameServer.getInstance().getAgentGameTcpClient2Hall().checkStatus();

            AgentGameServer.getInstance().buildRegisterUpdateMessage(msg -> {
                AgentGameServer.getInstance().getAgentGameTcpClient2Proxy().broadcastMsgAllSessions(msg);
                AgentGameServer.getInstance().getAgentGameTcpClient2Hall().broadcastMsgAllSessions(msg);
            });

            InnerMessage.InnerReqServerListMessage.Builder req = InnerMessage.InnerReqServerListMessage.newBuilder();
            req.setMsgID(MIDMessage.MID.InnerReqServerList_VALUE)
                    .addType(ServerType.HALL.getType());
            AgentGameServer.getInstance().getAgentGameTcpClient2Proxy().sendMsg(req.build());
        } catch (Exception e) {
            LOGGER.error("serverHeartCheck", e);
        }
    }

    public void asyncExecute(long playerId, Runnable runnable) {
        final int index = (int) (playerId % this.gameEventLoops.size());
        this.gameEventLoops.get(index).execute(runnable);
    }
}
