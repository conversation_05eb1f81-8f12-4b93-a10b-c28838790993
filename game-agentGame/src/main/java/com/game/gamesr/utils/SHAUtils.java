package com.game.gamesr.utils;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.io.UnsupportedEncodingException;
import java.nio.charset.StandardCharsets;
import java.security.*;
import java.security.spec.KeySpec;
import java.security.spec.PKCS8EncodedKeySpec;
import java.security.spec.X509EncodedKeySpec;
import java.util.Base64;

public class SHAUtils {
    private static final Logger LOGGER = LoggerFactory.getLogger(SHAUtils.class);

    public static String hmacSha256(String key, String data) throws Exception {
        // Define the HMAC_SHA256 algorithm
        final String algorithm = "HmacSHA256";

        // Create a new SecretKeySpec with the given key and algorithm
        final SecretKeySpec secretKeySpec = new SecretKeySpec(key.getBytes(), algorithm);

        // Create a Mac instance with the specified algorithm
        final Mac mac = Mac.getInstance(algorithm);

        // Initialize the Mac instance with the SecretKeySpec
        mac.init(secretKeySpec);

        // Compute the HMAC SHA-256 hash
        final byte[] hmacSha256Bytes = mac.doFinal(data.getBytes());

        // Encode the byte array to a Base64 string (optional, for readability)
        return bytesToHex(hmacSha256Bytes);
    }

    // 将字节数组转换为十六进制字符串
    private static String bytesToHex(byte[] bytes) {
        StringBuilder hexString = new StringBuilder();
        for (byte b : bytes) {
            String hex = Integer.toHexString(0xff & b);
            if (hex.length() == 1) {
                hexString.append('0');
            }
            hexString.append(hex);
        }
        return hexString.toString();
    }


    private static PrivateKey privateKey(String key) {
        try {
            final byte[] encoded = Base64.getDecoder().decode(key);
            KeySpec keySpec = new PKCS8EncodedKeySpec(encoded);
            final KeyFactory keyFactory = KeyFactory.getInstance("EC");
            return keyFactory.generatePrivate(keySpec);
        } catch (Exception ex) {
            LOGGER.error("", ex);
            return null;
        }
    }

    public static String sign(String key, String body) throws NoSuchAlgorithmException, InvalidKeyException, UnsupportedEncodingException, SignatureException {
        final PrivateKey priv = privateKey(key);

        final Signature ecdsa_priv = Signature.getInstance("SHA256withECDSA");

        ecdsa_priv.initSign(priv);

        final byte[] strByte = body.getBytes(StandardCharsets.UTF_8);
        ecdsa_priv.update(strByte);

        final byte[] realSig = ecdsa_priv.sign();
        return new String(Base64.getEncoder().encode(realSig));
    }


    public static Boolean verify(String data, String sign, String pub) throws Exception {
        final Signature verifier = Signature.getInstance("SHA256withECDSA");
        verifier.initVerify(getPublicKeyFromString(pub));
        verifier.update(data.getBytes());
        return verifier.verify(Base64.getDecoder().decode(sign));
    }

    /**
     * 根据字符串生成 PublicKey 对象
     *
     * @param key 公钥字符串
     * @return PublicKey 对象
     * @throws Exception 如果解析失败
     */
    private static PublicKey getPublicKeyFromString(String key) throws Exception {
        byte[] keyBytes = Base64.getDecoder().decode(key);
        KeyFactory keyFactory = KeyFactory.getInstance("EC");
        KeySpec keySpec = new X509EncodedKeySpec(keyBytes);
        return keyFactory.generatePublic(keySpec);
    }


    public static void main(String[] args) throws Exception {
        //openssl pkcs8 -topk8 -nocrypt -in ec-secp256k1-dummy-priv-key.pem -out p8file.pem
        //sign("MIGHAgEAMBMGByqGSM49AgEGCCqGSM49AwEHBG0wawIBAQQgrYKyg+AvIEk0fyufrQzpFhPT5KtoMuw1XJnqPVECNgqhRANCAAQVMFtjNxMu6I6jBffgtuI/ZlBMRJtYKkMxTFhNFr0PA7Xx3RcMweLZ5mx1dhC2F6xAX5M9Og5Fodf0U4CY/Rnp", "test");
        //        try {
//            // Create a KeyPairGenerator for EC keys
//            KeyPairGenerator keyPairGenerator = KeyPairGenerator.getInstance("EC");
//
//            // Specify the curve (e.g., P-256)
//            ECGenParameterSpec ecSpec = new ECGenParameterSpec("secp256r1"); // You can change to another curve if needed
//            keyPairGenerator.initialize(ecSpec, new SecureRandom());
//
//            // Generate the key pair
//            KeyPair keyPair = keyPairGenerator.generateKeyPair();
//            PrivateKey privateKey = keyPair.getPrivate();
//            PublicKey publicKey = keyPair.getPublic();
//
//            // Print the keys
//            System.out.println("Private Key: " + encodeToPEM(privateKey, true));
//            System.out.println("Public Key: " + encodeToPEM(publicKey, false));
//        } catch (Exception e) {
//            e.printStackTrace();
//        }
        String data = "{\"currency\":\"USD\",\"player\":\"USDTEST_10415633\",\"site\":\"cashcat.staging\",\"token\":\"eyJ0eXAiOiJqd3QiLCJhbGciOiJIUzI1NiJ9.eyJwbGF5ZXJOYW1lIjpudWxsLCJjdXJyZW5jeSI6IlVTRCIsImV4cCI6MTczNjI0OTU3NiwidXNlcklkIjoxMDQxNTYzMywiaWF0IjoxNzM1NjQ0Nzc2fQ.6zedaKk2cD_U94fpoKbwXD2qazOzNonZuqKqCTrEwOc\"}";
        String pub = "MFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAEHx1VYnXwsvB+u9h1pqQ9FrspXreGKV6XQ3rvgZ08/ZyOimsqZjcfRuw4LbAZuRcVEiJmvMLaVvCxifX2vYAbYw==";

//        String sign = "MEQCIErma9fiS5GduGumYev2ucwMS9pmzpQ4hnlvoxTZW6gYAiAPC4kBC/GlueB7kdypewNqxEvRqef8lOtXbm/ehToNwA==";
        String sign = "MEYCIQCqG4UNJJaW/6yasAAydfJMefSQJAhR31P+aAxzVn83iQIhAJi4KkNvJjwt0yZW7ska+tGzESdcw+nRKoeXDgysy3ES";

        Signature verifier = Signature.getInstance("SHA256withECDSA");
        verifier.initVerify(getPublicKeyFromString(pub));
        verifier.update(data.getBytes());
        boolean isVerified = verifier.verify(Base64.getDecoder().decode(sign));

        System.out.println("签名验证结果: " + isVerified);
    }

}
