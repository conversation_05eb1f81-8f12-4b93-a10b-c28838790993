package com.game.gamesr.server.tcp;

import com.game.engine.enums.ServerType;
import com.game.engine.io.conf.NettyClientConfig;
import com.game.engine.io.netty.TcpClient;
import com.game.engine.io.netty.code.ProtocolCoder;
import com.game.engine.net.GlobalQueueContainerMrg;
import com.game.engine.service.TcpService;
import com.game.gamesr.server.handler.AgentGameProxyClientHandler;
import io.netty.channel.ChannelInitializer;
import io.netty.channel.socket.SocketChannel;
import org.slf4j.LoggerFactory;
import org.slf4j.Logger;

public class AgentGameTcpClient2Proxy extends TcpService {

    private static final Logger log = LoggerFactory.getLogger(AgentGameTcpClient2Proxy.class);

    private final NettyClientConfig nettyClientConfig;
    private final TcpClient nettyClient;

    public AgentGameTcpClient2Proxy(NettyClientConfig nettyClientConfig) {
        super(ServerType.PROXY, GlobalQueueContainerMrg.getInstance().getGlobalQueue().getLogicQueue());
        this.nettyClientConfig = nettyClientConfig;
        this.nettyClient = new TcpClient(nettyClientConfig, new ProxyClientInitializer(this));
    }

    @Override
    public void broadcastMsg(Object msg) {
    }

    @Override
    public void start() {
        nettyClient.start();
    }

    @Override
    public void stop() {
        nettyClient.stop();
    }

    @Override
    public void checkStatus() {
        nettyClient.checkStatus();
    }

    @Override
    public String toString() {
        return nettyClientConfig.getName();
    }

    public TcpClient getNettyClient() {
        return nettyClient;
    }

    static class ProxyClientInitializer extends ChannelInitializer<SocketChannel> {

        private TcpService tcpService;

        public ProxyClientInitializer(TcpService tcpService) {
            this.tcpService = tcpService;
        }

        @Override
        protected void initChannel(SocketChannel ch) throws Exception {
            ch.pipeline().addLast(new ProtocolCoder());
            ch.pipeline().addLast(new AgentGameProxyClientHandler(tcpService));
        }

    }
}
