package com.game.gamesr.server.tcp;

import com.game.engine.enums.ServerType;
import com.game.engine.io.conf.NettyClientConfig;
import com.game.engine.io.netty.TcpClient;
import com.game.engine.io.netty.code.ProtocolCoder;
import com.game.engine.net.GlobalQueueContainerMrg;
import com.game.engine.service.TcpService;
import com.game.gamesr.server.handler.AgentGameHallClientHandler;
import com.game.manager.ServersMrg;
import io.netty.channel.ChannelInitializer;
import io.netty.channel.socket.SocketChannel;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class AgentGameTcpClient2Hall extends TcpService {

    private static final Logger log = LoggerFactory.getLogger(AgentGameTcpClient2Hall.class);

    private final NettyClientConfig nettyClientConfig;
    private final TcpClient nettyClient;
    private final ServersMrg serversMrg;

    public AgentGameTcpClient2Hall(NettyClientConfig nettyClientConfig) {
        super(ServerType.HALL, GlobalQueueContainerMrg.getInstance().getGlobalQueue().getLogicQueue());
        this.nettyClientConfig = nettyClientConfig;
        this.nettyClient = new TcpClient(nettyClientConfig, new HallClientInitializer(this));
        this.serversMrg = new ServersMrg(nettyClient, nettyClientConfig);
    }

    public ServersMrg getServersMrg() {
        return serversMrg;
    }

    @Override
    public void broadcastMsg(Object msg) {
    }

    @Override
    public void start() {
        nettyClient.start();
    }

    @Override
    public void stop() {
        nettyClient.stop();
    }

    @Override
    public void checkStatus() {
        nettyClient.checkStatus();
    }

    @Override
    public String toString() {
        return nettyClientConfig.getName();
    }

    public TcpClient getNettyClient() {
        return nettyClient;
    }

    static class HallClientInitializer extends ChannelInitializer<SocketChannel> {

        private TcpService tcpService;

        public HallClientInitializer(TcpService tcpService) {
            this.tcpService = tcpService;
        }

        @Override
        protected void initChannel(SocketChannel ch) throws Exception {
            ch.pipeline().addLast(new ProtocolCoder());
            ch.pipeline().addLast(new AgentGameHallClientHandler(tcpService));
        }

    }
}
