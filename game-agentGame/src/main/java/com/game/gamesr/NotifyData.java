package com.game.gamesr;

public class NotifyData {
    private long pid;
    private String noteId;
    private int type; //1.bet 2.win 3.refund
    private int currencyId;
    private double betAmount;
    private double validBets;
    private double win;
    private double totalWin;
    private int freeTimes;
    private long updSessionId;
    private String playerName;
    private String currency;
    private long updated_time;
    private double real_transfer_amount;
    private String data;

    public long getPid() {
        return pid;
    }

    public NotifyData setPid(long pid) {
        this.pid = pid;
        return this;
    }

    public String getNoteId() {
        return noteId;
    }

    public NotifyData setNoteId(String noteId) {
        this.noteId = noteId;
        return this;
    }

    public int getType() {
        return type;
    }

    public NotifyData setType(int type) {
        this.type = type;
        return this;
    }

    public int getCurrencyId() {
        return currencyId;
    }

    public NotifyData setCurrencyId(int currencyId) {
        this.currencyId = currencyId;
        return this;
    }

    public double getBetAmount() {
        return betAmount;
    }

    public NotifyData setBetAmount(double betAmount) {
        this.betAmount = betAmount;
        return this;
    }

    public double getValidBets() {
        return validBets;
    }

    public NotifyData setValidBets(double validBets) {
        this.validBets = validBets;
        return this;
    }

    public double getWin() {
        return win;
    }

    public NotifyData setWin(double win) {
        this.win = win;
        return this;
    }

    public double getTotalWin() {
        return totalWin;
    }

    public NotifyData setTotalWin(double totalWin) {
        this.totalWin = totalWin;
        return this;
    }

    public int getFreeTimes() {
        return freeTimes;
    }

    public NotifyData setFreeTimes(int freeTimes) {
        this.freeTimes = freeTimes;
        return this;
    }

    public long getUpdSessionId() {
        return updSessionId;
    }

    public NotifyData setUpdSessionId(long updSessionId) {
        this.updSessionId = updSessionId;
        return this;
    }

    public String getPlayerName() {
        return playerName;
    }

    public NotifyData setPlayerName(String playerName) {
        this.playerName = playerName;
        return this;
    }

    public String getCurrency() {
        return currency;
    }

    public NotifyData setCurrency(String currency) {
        this.currency = currency;
        return this;
    }

    public long getUpdated_time() {
        return updated_time;
    }

    public NotifyData setUpdated_time(long updated_time) {
        this.updated_time = updated_time;
        return this;
    }

    public double getReal_transfer_amount() {
        return real_transfer_amount;
    }

    public NotifyData setReal_transfer_amount(double real_transfer_amount) {
        this.real_transfer_amount = real_transfer_amount;
        return this;
    }

    public String getData() {
        return this.data;
    }

    public NotifyData setData(String data) {
        this.data = data;
        return this;
    }
}
