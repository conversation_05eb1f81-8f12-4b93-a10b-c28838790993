<?xml version="1.0" encoding="UTF-8"?>
<NettyServerConfig>
    <id>${AGENTGAMEID}</id>
    <port>8800</port>
    <name>代理游戏服务器</name>
    <type>AGENT_GAME</type>
    <sendBufferSize>2048</sendBufferSize>
    <receiveBufferSize>8196</receiveBufferSize>
    <reuseAddress>true</reuseAddress>
    <tcpNoDelay>true</tcpNoDelay>
    <readerIdleTime>180</readerIdleTime> <!--3分钟没收到客户端请求消息，断开当前链接-->
    <writerIdleTime>180</writerIdleTime>
    <kafka_connection_string>${kafka.url}</kafka_connection_string>
    <mongo_connection_string>${mongo.url}</mongo_connection_string>
    <mongo_read_write_database>${mongo.db}</mongo_read_write_database>
    <mongo_config_database>${mongo.config}</mongo_config_database>
</NettyServerConfig>
