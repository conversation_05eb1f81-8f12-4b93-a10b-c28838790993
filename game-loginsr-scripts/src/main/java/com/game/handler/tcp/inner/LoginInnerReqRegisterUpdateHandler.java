package com.game.handler.tcp.inner;

import com.game.engine.enums.ServerType;
import com.game.engine.io.handler.TcpHandler;
import com.game.engine.script.IHandlerEntity;
import com.game.engine.struct.ServerInfo;
import com.game.engine.utils.MsgUtil;
import com.game.loginsr.mrg.ServerMrg;
import com.proto.InnerMessage;
import com.proto.MIDMessage;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 服务器注册返回，状态改变时进行自动操作
 *
 */
@IHandlerEntity(mid = MIDMessage.MID.InnerReqRegisterUpdate_VALUE, msg = InnerMessage.InnerReqRegisterUpdateMessage.class)
public class LoginInnerReqRegisterUpdateHandler extends TcpHandler {

    private static final Logger LOGGER = LoggerFactory.getLogger(LoginInnerReqRegisterUpdateHandler.class);

    @Override
    public void run() {
        final InnerMessage.InnerReqRegisterUpdateMessage reqMessage = (InnerMessage.InnerReqRegisterUpdateMessage) getMessage();

        final InnerMessage.InnerServerInfo info = reqMessage.getServerInfo();
        final ServerInfo serverInfo = ServerMrg.getInstance().getServerInfo(ServerType.valueOf(info.getType()), info.getId());
        if (serverInfo == null) {
            LOGGER.warn("registerUpdate，serverType：{}，serverId：{}", ServerType.valueOf(info.getType()), info.getId());
            return;
        }
        if (serverInfo.getIp().equals(MsgUtil.getIp(getSession()))) {
            serverInfo.onSessionOpen(getSession());
//                LOGGER.info("登录注册，服务器：{} 已正式连接注册到本登录服 ip:{}", serverInfo.getName(), MsgUtil.getIp(getSession()));
        } else {
            LOGGER.info("登录注册，服务器：{} 连接注册到登录服务器 ip:{} 非法，与配置ip：{}不一致", serverInfo.getName(), MsgUtil.getIp(getSession()), serverInfo.getIp());
        }
    }

}
