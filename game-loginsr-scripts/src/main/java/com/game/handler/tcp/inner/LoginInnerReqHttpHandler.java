package com.game.handler.tcp.inner;

import com.game.engine.io.handler.IHandler;
import com.game.engine.io.handler.TcpHandler;
import com.game.engine.io.message.HttpMessageBean;
import com.game.engine.script.IHandlerEntity;
import com.game.engine.script.ScriptLoader;
import com.game.engine.utils.JsonUtils;
import com.game.engine.utils.TimeUtil;
import com.game.loginsr.main.LoginServer;
import com.game.utils.VirtualThreadUtils;
import com.proto.InnerMessage;
import com.proto.MIDMessage;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.LinkedHashMap;


@IHandlerEntity(mid = MIDMessage.MID.InnerReqHttpHandler_VALUE, msg = InnerMessage.InnerReqHttpHandlerMessage.class)
public class LoginInnerReqHttpHandler extends TcpHandler {
    private static final Logger LOGGER = LoggerFactory.getLogger(LoginInnerReqHttpHandler.class);

    @Override
    public void run() {
        final InnerMessage.InnerReqHttpHandlerMessage innerHttpHandler = (InnerMessage.InnerReqHttpHandlerMessage) getMessage();
        final String params = innerHttpHandler.getParams();
        final String requestPath = innerHttpHandler.getRequestPath();

        final HttpMessageBean httpMessageBean = ScriptLoader.getInstance().getHttpMessageBean(requestPath);
        if (httpMessageBean == null) {
            LOGGER.warn("http requestPath not register：{}", requestPath);
            return;
        }

        try {
            @SuppressWarnings("unchecked") final LinkedHashMap<String, Object> paramsMap = JsonUtils.readFromJson(params, LinkedHashMap.class);
            final IHandler handler = httpMessageBean.newHandler();
            handler.setSession(session);
            handler.setCreateTime(TimeUtil.currentTimeMillis());
            handler.setParamsMap(paramsMap);
            handler.setPid(pid);
            if (requestPath.contains("reloadScripts")) {//脚本
                VirtualThreadUtils.execute(handler);
            } else if (requestPath.toLowerCase().contains("reload")) {
                LoginServer.getInstance().getConfigEventLoop().execute(handler);
            } else {
                long time = TimeUtil.currentTimeMillis();
                handler.run();
                time = TimeUtil.currentTimeMillis() - time;
                if (time > 10) {
                    LOGGER.warn("{}，处理时间超过，{}", handler.getClass().getSimpleName(), time);
                }
            }
        } catch (Exception e) {
            LOGGER.error("LoginInnerReqHttpHandler", e);
        }
    }

}
