package com.game.handler.http.login;

import com.game.c_entity.merchant.C_MailSmtp;
import com.game.engine.io.handler.HttpHandler;
import com.game.engine.io.redis.RedisPoolManager;
import com.game.engine.script.IHandlerEntity;
import com.game.engine.utils.MailSmtpUtil;
import com.game.engine.utils.MsgUtil;
import com.game.engine.utils.TimeUtil;
import com.game.enums.ErrorCode;
import com.game.enums.redis.RedisLogin;
import com.game.loginsr.mrg.DataLoginMrg;
import com.game.loginsr.mrg.MerchantData;
import com.game.utils.VirtualThreadUtils;
import com.proto.LoginMessage;
import com.proto.MIDMessage;
import io.lettuce.core.SetArgs;
import io.netty.util.internal.StringUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.time.format.DateTimeFormatter;
import java.util.concurrent.CompletableFuture;

@IHandlerEntity(path = "/api/login/verifyCode", desc = "验证码")
public class ReqVerifyCodeDataHandler extends HttpHandler {
    private static final Logger LOGGER = LoggerFactory.getLogger(ReqVerifyCodeDataHandler.class);

    @Override
    public void run() {
        final LoginMessage.ResMailVerifyCodeMessage.Builder res = LoginMessage.ResMailVerifyCodeMessage.newBuilder();
        res.setMsgID(MIDMessage.MID.ResMailVerifyCode_VALUE);
        try {
            final String data = (String) paramsMap.get("data");
            final byte[] bytes = MsgUtil.decode(data);

            final LoginMessage.ReqMailVerifyCodeMessage req = LoginMessage.ReqMailVerifyCodeMessage.parseFrom(bytes);
            final String email = req.getEmail().trim();
            final int codeType = req.getCodeType();
            final String host = req.getHost();

            if (codeType == 0) {
                LOGGER.warn("verifyCode type error：{}", codeType);
                res.setError(ErrorCode.Data_Error.getCode());
                MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                return;
            }

            final String business_no = DataLoginMrg.getInstance().findBusiness_no(this.getClass().getSimpleName(), host);
            if (StringUtil.isNullOrEmpty(business_no)) {
                LOGGER.warn("verifyCode host：{}，business_no is null or empty", host);
                res.setError(ErrorCode.Data_Error.getCode());
                MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                return;
            }

            final MerchantData merchantData = DataLoginMrg.getInstance().findMerchantData(this.getClass().getSimpleName(), business_no);
            if (merchantData == null) {
                res.setError(ErrorCode.Data_Error.getCode());
                MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                return;
            }

            final C_MailSmtp c_mailSmtp = merchantData.findC_MailSmtp();
            if (c_mailSmtp == null) {
                res.setError(ErrorCode.Data_Error.getCode());
                MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                return;
            }

            final String verifyCode = generateVerifyCode();
            final String key = RedisLogin.Platform_LG_Account_VerifyCode.getKey(business_no, codeType, email);//1.重置、2.绑定、3.解绑 4.更换 5.添加

            final long exits = RedisPoolManager.getInstance().function(
                    jedis -> jedis.sync().exists(key));
            if (exits > 0) {
                res.setEmail(email)
                        .setError(ErrorCode.Verification_Code_Not_Expired.getCode());
                MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                return;
            }

            RedisPoolManager.getInstance().executeAsync(commands ->
                    commands.set(key, verifyCode, SetArgs.Builder.ex(119))
            );

            final String smtpHost = c_mailSmtp.getSmtpHost();
            final String smtpSender = c_mailSmtp.getSmtpSender();
            final String smtpUserName = c_mailSmtp.getSmtpUserName();
            final String smtpPassWard = c_mailSmtp.getSmtpPassWard();

            final String context = String.format("The PIN for %s is: %s (expired on %s/%s %s)", email, verifyCode,
                    TimeUtil.getDayOfMonth(), TimeUtil.getMonth(),
                    TimeUtil.getDateTimeFormat(TimeUtil.currentTimeMillis() + 2 * TimeUtil.MIN,
                            DateTimeFormatter.ofPattern("HH:mm")));

            VirtualThreadUtils.execute(() ->
                    MailSmtpUtil.sendMailSmtp(smtpHost, smtpSender, smtpUserName, smtpPassWard,
                            email, "Mailbox Verification Code", context));

            res.setEmail(email);
            MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
            LOGGER.warn("email：{}，send verifyCode，{}", email, verifyCode);
        } catch (Exception e) {
            LOGGER.error("ReqVerifyCodeHandler", e);
            res.setError(ErrorCode.Internal_Server_Error.getCode());
            MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
        }
    }

    private static String generateVerifyCode() {
        final int a = (int) (899999 * Math.random() + 100000);
        return String.valueOf(a);
    }

}


