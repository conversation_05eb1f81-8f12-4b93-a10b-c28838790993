//package com.game.handler.http.gmtest;
//
//import com.game.engine.io.handler.HttpHandler;
//import com.game.engine.io.redis.RedisPoolManager;
//import com.game.engine.script.IHandlerEntity;
//import com.game.enums.ErrorCode;
//import com.game.enums.redis.RedisLogin;
//import com.game.loginsr.manager.ServerMrg;
//import io.netty.util.internal.StringUtil;
//import org.slf4j.Logger;
//import org.slf4j.LoggerFactory;
//import redis.clients.jedis.Pipeline;
//
//import java.util.LinkedHashMap;
//import java.util.Map;
//
////https://platform.winspin.live/gmLogin/unbindEmail?email=
//@IHandlerEntity(path = "/gmLogin/unbindEmail", desc = "解除绑定邮箱")
//public class GmLogin_UnbindEmailHandler extends HttpHandler {
//    private static final Logger LOGGER = LoggerFactory.getLogger(GmLogin_UnbindEmailHandler.class);
//
//    @Override
//    public void run() {
//        final LinkedHashMap<String, Object> res = ServerMrg.httpCachePool.get();
//        try {
//            final Map<String, Object> paramsMap = getParamsMap();
//            final String email = (String) paramsMap.get("email");
//
//            final String accountId = RedisPoolManager.getInstance().function(jedis -> jedis.hget(RedisLogin.Platform_LG_Map_EmailID.getKey(), email));
//            if (StringUtil.isNullOrEmpty(accountId)) {
//                res.put("error", ErrorCode.Request_Parameters.getCode());
//                res.put("description", "please check the request parameters");
//                response(res);
//                return;
//            }
//
//            RedisPoolManager.getInstance().consumer(jedis -> {
//                final Pipeline pl = jedis.pipelined();
//                pl.hdel(RedisLogin.Platform_LG_Map_EmailID.getKey(), email);
//                pl.hdel(RedisLogin.Platform_LG_Map_IDEmail.getKey(), accountId);
//                pl.sync();
//            });
//
//            res.put("error", ErrorCode.Success.getCode());
//            res.put("description", "ok");
//            response(res);
//        } catch (Exception e) {
//            res.put("error", ErrorCode.Internal_Error.getCode());
//            res.put("description", "internal error");
//            response(res);
//        } finally {
//            ServerMrg.httpCachePool.returnOne(res);
//        }
//    }
//}
