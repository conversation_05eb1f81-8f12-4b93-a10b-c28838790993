package com.game.handler.http.backstage;

import com.game.engine.io.handler.HttpHandler;
import com.game.engine.script.IHandlerEntity;
import com.game.enums.ErrorCode;
import com.game.enums.MerchantConfigType;
import com.game.loginsr.mrg.DataLoginMrg;
import com.game.loginsr.mrg.MerchantData;
import com.game.loginsr.mrg.ServerMrg;
import io.netty.util.internal.StringUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Map;
import java.util.Objects;

//http://127.0.0.1:8280/gmLogin/merchantReloadConfig?business_no=&reloadType=
@IHandlerEntity(path = "/gmLogin/merchantReloadConfig", desc = "重新加载商户配置")
public class GmLogin_ReloadMerchantConfigHandler extends HttpHandler {
    private static final Logger LOGGER = LoggerFactory.getLogger(GmLogin_ReloadMerchantConfigHandler.class);

    @Override
    public void run() {
        try {
            final String business_no = (String) paramsMap.get("business_no");
            final String reloadType = (String) paramsMap.get("reloadType");

            if (StringUtil.isNullOrEmpty(business_no) || StringUtil.isNullOrEmpty(reloadType)) {
                ServerMrg.responseHttp(ErrorCode.Parameter_Error.getCode(), session, pid);
                return;
            }

            if (Objects.equals(business_no, "ALL")) {
                for (Map.Entry<String, MerchantData> entry : DataLoginMrg.getInstance().getMerchantDataMap().entrySet()) {
                    loadConfig(entry.getKey(), entry.getValue(), Integer.parseInt(reloadType));
                }
            } else {
                final MerchantData merchantData = DataLoginMrg.getInstance().findMerchantData(this.getClass().getSimpleName(), business_no);
                if (merchantData == null) {
                    ServerMrg.responseHttp(ErrorCode.Parameter_Error.getCode(), session, pid);
                    return;
                }
                loadConfig(business_no, merchantData, Integer.parseInt(reloadType));
            }

            LOGGER.info("merchantReloadConfig，business_no：{}，reloadType：{}， success ...", business_no, reloadType);
            ServerMrg.responseHttp(ErrorCode.Success.getCode(), session, pid);
        } catch (Exception e) {
            LOGGER.error("", e);
            ServerMrg.responseHttp(ErrorCode.Internal_Server_Error.getCode(), session, pid);
        }
    }

    private void loadConfig(final String business_no, final MerchantData merchantData, int reloadType) throws Exception {
        switch (MerchantConfigType.valueOf(reloadType)) {
            case Global -> merchantData.loadGlobal(business_no);
            case WebSite -> merchantData.loadWebSite(business_no);
            case MaintainNotice -> merchantData.loadMaintainNotice(business_no);
            case PhoneMsg -> merchantData.loadPhoneMsg(business_no);
            case MailSmtp -> merchantData.loadMailSmtp(business_no);
            case Advertise -> merchantData.loadAdvertise(business_no);
        }
    }

}
