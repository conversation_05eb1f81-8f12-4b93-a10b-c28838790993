package com.game.handler.http.login;

import com.game.c_entity.merchant.C_MaintainNotice;
import com.game.c_entity.middleplatform.C_BaseMaintainNotice;
import com.game.c_entity.middleplatform.C_BaseMerchant;
import com.game.dao.RegisterLimitDao;
import com.game.engine.io.handler.HttpHandler;
import com.game.engine.script.IHandlerEntity;
import com.game.engine.script.ScriptLoader;
import com.game.engine.utils.MsgUtil;
import com.game.engine.utils.PatternUtil;
import com.game.entity.RegisterLimit;
import com.game.enums.ErrorCode;
import com.game.enums.ThreeParty;
import com.game.loginsr.mrg.DataLoginMrg;
import com.game.loginsr.mrg.MerchantData;
import com.game.loginsr.script.IRegisterScript;
import com.game.manager.EntityDaoMrg;
import com.proto.LoginMessage;
import com.proto.MIDMessage;
import io.netty.util.internal.StringUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@IHandlerEntity(path = "/api/login/register", desc = "注册")
public class ReqRegisterHandler extends HttpHandler {
    private final Logger LOGGER = LoggerFactory.getLogger(ReqRegisterHandler.class);

    @Override
    public void run() {
        final LoginMessage.ResLoginMessage.Builder res = LoginMessage.ResLoginMessage.newBuilder();
        res.setMsgID(MIDMessage.MID.ResLogin_VALUE);
        try {
            final String data = (String) paramsMap.get("data");
            final byte[] bytes = MsgUtil.decode(data);
            final String country = (String) paramsMap.get("country");

            final LoginMessage.ReqRegisterMessage req = LoginMessage.ReqRegisterMessage.parseFrom(bytes);
            final String account = req.getAccount().trim();
            final String password = req.getPassword().trim();
            final String host = req.getHost();//域名
            final int threeParty = req.getThreeParty();

            String lockKey = account;
            if (threeParty == ThreeParty.Phone.getThreeParty()) {
                lockKey = req.getAreaCode() + "-" + account;
            }

            synchronized (lockKey.intern()) {
                LOGGER.warn("lockKey：{}，http://{}/pwa?al={}&ea={}&de={}&fb_dynamic_pixel={}&fb_token={}",
                        lockKey, host, req.getReferralCode(), req.getEa(), req.getDe(), req.getFbInfo().getPixelId(), req.getFbInfo().getFbToken());

                LOGGER.warn("lockKey：{}，http://{}/kwai?al={}&ea={}&de={}&kwai_dynamic_pixel={}&kwai_token={}",
                        lockKey, host, req.getReferralCode(), req.getEa(), req.getDe(), req.getKWaiInfo().getPixelId(), req.getKWaiInfo().getKWaiToken());

                final C_BaseMerchant c_baseMerchant = DataLoginMrg.getInstance().findC_BaseMerchant(this.getClass().getSimpleName(), req.getHost());
                if (c_baseMerchant == null) {
                    res.setError(ErrorCode.DomainName_Not_Exist.getCode());
                    MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                    return;
                }

                final String business_no = c_baseMerchant.getBusiness_no();
                final MerchantData merchantData = DataLoginMrg.getInstance().findMerchantData(this.getClass().getSimpleName(), business_no);
                if (merchantData == null) {
                    res.setError(ErrorCode.Data_Error.getCode());
                    MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                    return;
                }

                final C_BaseMaintainNotice c_baseMaintainNotice = DataLoginMrg.getInstance().findC_BaseMaintainNotice();
                if (c_baseMaintainNotice != null && c_baseMaintainNotice.isStatus()) {
                    res.setError(ErrorCode.Server_Maintenance.getCode());
                    MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                    return;
                }

                final C_MaintainNotice c_maintainNotice = merchantData.findC_MaintainNotice();
                if (c_maintainNotice != null && c_maintainNotice.isStatus()) {
                    res.setError(ErrorCode.Server_Maintenance.getCode());
                    MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                    return;
                }

                final String ip = MsgUtil.getClientIp(session);
                if (DataLoginMrg.getInstance().isBlackList(business_no, ip)) {
                    LOGGER.warn("account：{}，ip：{}，blackList", account, ip);
                    res.setError(ErrorCode.Blacklist_Not_Login.getCode());
                    MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                    return;
                }

                if (password.length() < 6 || password.length() > 16) {
                    res.setError(ErrorCode.PassWard_Length.getCode());
                    MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                    return;
                }

//                if (!password.matches("^[a-zA-Z0-9]+$")) {
//                    res.setError(ErrorCode.PassWord_Format.getCode());
//                    MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
//                    return;
//                }

                final boolean isWhiteList = DataLoginMrg.getInstance().isWhiteList(business_no, ip);

                if (!isWhiteList) {
                    RegisterLimit registerLimit = EntityDaoMrg.getInstance().getDao(RegisterLimitDao.class).findByIp(business_no, ip);
                    if (registerLimit == null) {
                        registerLimit = new RegisterLimit();
                        registerLimit.setBusiness_no(business_no);
                        registerLimit.setIpAddress(ip);
                        EntityDaoMrg.getInstance().getDao(RegisterLimitDao.class).insert(registerLimit);
                    }

                    final String sameIpRegisterLimit = merchantData.findC_GlobalValue("sameIpRegisterLimit");
                    if (!StringUtil.isNullOrEmpty(sameIpRegisterLimit) && registerLimit.getCount() >= Integer.parseInt(sameIpRegisterLimit)) {
                        LOGGER.warn("account：{}，registered limit", account);
                        res.setError(ErrorCode.Registered_limit.getCode());
                        MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                        return;
                    }
                }

                switch (ThreeParty.valuesOf(threeParty)) {
                    case Email:
                        ScriptLoader.getInstance().consumerScript("MailRegisterScript", (IRegisterScript script) -> {
                            script.mailRegister(req, c_baseMerchant, ip, country, session);
                        });
                        break;
                    case Phone:
                        ScriptLoader.getInstance().consumerScript("PhoneRegisterScript", (IRegisterScript script) -> {
                            script.phoneRegister(req, c_baseMerchant, ip, country, session);
                        });
                        break;
                    case Account:
                        ScriptLoader.getInstance().consumerScript("AccountRegisterScript", (IRegisterScript script) -> {
                            script.accountRegister(req, c_baseMerchant, ip, country, session);
                        });
                        break;
                }
            }
        } catch (Exception e) {
            LOGGER.error("ReqRegisterHandler", e);
            res.setError(ErrorCode.Internal_Server_Error.getCode());
            MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
        }
    }

}
