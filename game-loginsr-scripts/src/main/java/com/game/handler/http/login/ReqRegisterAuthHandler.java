package com.game.handler.http.login;

import com.alibaba.fastjson.JSONObject;
import com.game.engine.io.handler.HttpHandler;
import com.game.engine.script.IHandlerEntity;
import com.game.engine.utils.HttpUtils11;
import com.game.engine.utils.JsonUtils;
import com.game.engine.utils.MsgUtil;
import com.game.enums.ErrorCode;
import com.game.utils.VirtualThreadUtils;
import com.proto.LoginMessage;
import com.proto.MIDMessage;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.net.http.HttpResponse;

@IHandlerEntity(path = "/api/login/registerAuth", desc = "注册认证")
public class ReqRegisterAuthHandler extends HttpHandler {
    private static final Logger LOGGER = LoggerFactory.getLogger(ReqRegisterAuthHandler.class);

    @Override
    public void run() {
        final LoginMessage.ResRegisterAuthMessage.Builder res = LoginMessage.ResRegisterAuthMessage.newBuilder();
        res.setMsgID(MIDMessage.MID.ResRegisterAuth_VALUE);
        try {
            final String data = (String) paramsMap.get("data");
            final byte[] bytes = MsgUtil.decode(data);


            final LoginMessage.ReqRegisterAuthMessage req = LoginMessage.ReqRegisterAuthMessage.parseFrom(bytes);
            final String googleToken = req.getGoogleToken();

//            final String boundary = "---WebKitFormBoundary7MA4YWxkTrZu0gW";
//            final String reqBody = buildMultipartBody(boundary, googleToken, MsgUtil.getClientIp(this.session));
//
//            final String uri = "https://www.google.com/recaptcha/api/siteverify";
////                LOGGER.warn("url：{}", uri);
//
//            final HttpResponse<String> response = HttpUtils11.sendPostSyncHttp(uri, reqBody, "multipart/form-data; boundary=---WebKitFormBoundary7MA4YWxkTrZu0gW");
//            final String respBody = response.body();
////            LOGGER.warn("respBody：{}", respBody);
//
//            final JSONObject jsonObject = JsonUtils.readFromJson(respBody, JSONObject.class);
//            final boolean success = jsonObject.getBoolean("success");
//            if (!success) {
//                res.setError(ErrorCode.GoogleAuth_Error.getCode());
//            }
            MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
        } catch (Exception e) {
            LOGGER.error("ReqRegisterAuthHandler", e);
            res.setError(ErrorCode.Internal_Server_Error.getCode());
            MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
        }
    }

    // 构建 multipart/form-data 的请求体
    private static String buildMultipartBody(String boundary, String googleToken, String remoteip) {
        StringBuilder formBody = new StringBuilder();

        // 添加 secret 参数
        formBody.append("--").append(boundary).append("\r\n")
                .append("Content-Disposition: form-data; name=\"secret\"\r\n\r\n")
                .append("6LcDwSIrAAAAALSEpHd2cI9o_pwjXdpYVCeq1OZu").append("\r\n");

        // 添加 response 参数
        formBody.append("--").append(boundary).append("\r\n")
                .append("Content-Disposition: form-data; name=\"response\"\r\n\r\n")
                .append(googleToken).append("\r\n");

        formBody.append("--").append(boundary).append("\r\n")
                .append("Content-Disposition: form-data; name=\"remoteip\"\r\n\r\n")
                .append(remoteip).append("\r\n");

        // 添加结束 boundary
        formBody.append("--").append(boundary).append("--\r\n");

        return formBody.toString();
    }
}
