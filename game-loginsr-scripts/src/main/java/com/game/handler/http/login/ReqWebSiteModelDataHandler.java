package com.game.handler.http.login;

import com.game.c_entity.merchant.C_BaseServerConfig;
import com.game.c_entity.merchant.C_WebSite;
import com.game.c_entity.middleplatform.C_BaseMerchant;
import com.game.engine.enums.ServerType;
import com.game.engine.io.handler.HttpHandler;
import com.game.engine.log.GameLog;
import com.game.engine.math.MathUtils;
import com.game.engine.script.IHandlerEntity;
import com.game.engine.struct.ServerInfo;
import com.game.engine.utils.GuidGeneratorUtils;
import com.game.engine.utils.JWTUtil;
import com.game.engine.utils.MsgUtil;
import com.game.engine.utils.TimeUtil;
import com.game.enums.ErrorCode;
import com.game.loginsr.main.LoginServer;
import com.game.loginsr.mrg.DataLoginMrg;
import com.game.loginsr.mrg.MerchantData;
import com.game.loginsr.mrg.ServerMrg;
import com.proto.LoginMessage;
import com.proto.MIDMessage;
import io.netty.util.internal.StringUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@IHandlerEntity(path = "/api/login/webSiteModel", desc = "获取网站模板")
public class ReqWebSiteModelDataHandler extends HttpHandler {
    private final Logger LOGGER = LoggerFactory.getLogger(ReqWebSiteModelDataHandler.class);

    @Override
    public void run() {
        final LoginMessage.ResWebSiteModelMessage.Builder res = LoginMessage.ResWebSiteModelMessage.newBuilder();
        res.setMsgID(MIDMessage.MID.ResWebSiteModel_VALUE);
        try {
            final String data = (String) paramsMap.get("data");
            final byte[] bytes = MsgUtil.decode(data);
            final String country = (String) paramsMap.get("country");

            final LoginMessage.ReqWebSiteModelMessage req = LoginMessage.ReqWebSiteModelMessage.parseFrom(bytes);
            final String host = req.getHost();

            final C_BaseMerchant c_baseMerchant = DataLoginMrg.getInstance().findC_BaseMerchant(this.getClass().getSimpleName(), host);
            if (c_baseMerchant == null) {
                res.setError(ErrorCode.DomainName_Not_Exist.getCode());
                MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                return;
            }

            final String business_no = c_baseMerchant.getBusiness_no();
            final MerchantData merchantData = DataLoginMrg.getInstance().findMerchantData(this.getClass().getSimpleName(), business_no);
            if (merchantData == null) {
                res.setError(ErrorCode.Data_Error.getCode());
                MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                return;
            }

            final C_BaseServerConfig c_serverConfig = DataLoginMrg.getInstance().findC_BaseServerConfig(this.getClass().getSimpleName(), business_no);
            if (c_serverConfig == null) {
                res.setError(ErrorCode.Data_Error.getCode());
                MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                return;
            }

            final C_WebSite c_webSite = merchantData.findC_WebSiteModel(host);
            if (c_webSite == null) {
                res.setError(ErrorCode.DomainName_Not_Exist.getCode());
                MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                return;
            }

            final long accountId = MathUtils.random(100000, 200000);

            final ServerInfo hallInfo = ServerMrg.getInstance()
                    .getServerType(ServerType.HALL, 0, c_serverConfig.getServerId(accountId));
            if (hallInfo == null) {
                LOGGER.warn("hallServer，not exits");
                res.setError(ErrorCode.Internal_Server_Error.getCode());
                MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                return;
            }

            final JWTUtil.JWData jwData = new JWTUtil.JWData();
            jwData.userId = -1;
            jwData.account = "visitors";
            jwData.version = c_serverConfig.getVersion();
            jwData.business_no = business_no;

            String region = country;
            if (StringUtil.isNullOrEmpty(region)) {
                region = merchantData.findC_GlobalValue("initRegion");
            }

            final String token = JWTUtil.jwtToken(jwData);
            final String initLanguage = merchantData.findC_GlobalValue("initLanguage");
            res.setToken(token)
                    .setRegion(region)
                    .setHost(hallInfo.getWwwip())
                    .setWebSiteInfo(buildWebSiteInfo(c_webSite, StringUtil.isNullOrEmpty(initLanguage) ? 1 : Integer.parseInt(initLanguage)));
            MsgUtil.responseHttp(res.build(), res.getMsgID(), session);

            //TODO 统计未登录访问
            final GameLog notLoginLog = new GameLog("platform_notLoginLog");
            notLoginLog.append("number", GuidGeneratorUtils.generateOrderId())
                    .append("site", host)
                    .append("business_no", business_no)
                    .append("ip", MsgUtil.getClientIp(session))
                    .append("logTime", TimeUtil.currentTimeMillis());
            LoginServer.getInstance().getLogProducerMrg().send(notLoginLog);
        } catch (Exception e) {
            LOGGER.error("ReqWebSiteModelHandler", e);
            res.setError(ErrorCode.Internal_Server_Error.getCode());
            MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
        }
    }

    private LoginMessage.WebSiteInfo buildWebSiteInfo(C_WebSite c_webSite, int language) {
        final LoginMessage.WebSiteInfo.Builder webSiteInfo = LoginMessage.WebSiteInfo.newBuilder()
                .setSiteId(c_webSite.getSiteId())
                .setSiteName(c_webSite.getSiteName())
                .setSiteLogo(c_webSite.getSiteLogo())
                .setSiteLogo1(c_webSite.getSiteLogo1())
                .setSiteLogo2(c_webSite.getSiteLogo2())
                .setSiteModel(c_webSite.getSiteModel())
                .setLanguage(language);
        return webSiteInfo.build();
    }
}
