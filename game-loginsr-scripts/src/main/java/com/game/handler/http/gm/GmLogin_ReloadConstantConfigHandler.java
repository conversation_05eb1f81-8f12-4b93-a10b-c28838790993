package com.game.handler.http.gm;

import com.game.engine.io.conf.ConstantConfig;
import com.game.engine.io.handler.HttpHandler;
import com.game.engine.script.IHandlerEntity;
import com.game.engine.utils.Config;
import com.game.engine.utils.MsgUtil;
import com.game.enums.ErrorCode;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

//http://127.0.0.1:8380/gmLogin/reloadConstantConfig?default=true
@IHandlerEntity(path = "/gmLogin/reloadConstantConfig", desc = "重新加载常量配置")
public class GmLogin_ReloadConstantConfigHandler extends HttpHandler {
    private static final Logger LOGGER = LoggerFactory.getLogger(GmLogin_ReloadConstantConfigHandler.class);

    @Override
    public void run() {
        try {
            ConstantConfig.reloadConstantConfig(Config.path, "constantConfig.xml");

            MsgUtil.responseHttp(ErrorCode.Success.getCode(), session);
        } catch (Exception e) {
            LOGGER.error("GmLogin_ReloadConstantConfigHandler", e);
            MsgUtil.responseHttp(ErrorCode.Internal_Server_Error.getCode(), session);
        }
    }
}
