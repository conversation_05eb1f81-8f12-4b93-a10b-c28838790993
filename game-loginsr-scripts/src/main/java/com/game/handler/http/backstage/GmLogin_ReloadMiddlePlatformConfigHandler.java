package com.game.handler.http.backstage;


import com.game.engine.io.handler.HttpHandler;
import com.game.engine.script.IHandlerEntity;
import com.game.enums.ErrorCode;
import com.game.enums.MiddleConfigType;
import com.game.loginsr.mrg.DataLoginMrg;
import com.game.loginsr.mrg.ServerMrg;
import io.netty.util.internal.StringUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;


//http://127.0.0.1:8280/gmLogin/middlePlatformReloadConfig?business_no=&reloadType=101
@IHandlerEntity(path = "/gmLogin/middlePlatformReloadConfig", desc = "重新加载中台配置")
public class GmLogin_ReloadMiddlePlatformConfigHandler extends HttpHandler {
    private static final Logger LOGGER = LoggerFactory.getLogger(GmLogin_ReloadMiddlePlatformConfigHandler.class);

    @Override
    public void run() {
        try {
            final String reloadType = (String) paramsMap.get("reloadType");
            final String business_no = (String) paramsMap.get("business_no");

            if (StringUtil.isNullOrEmpty(reloadType)) {
                ServerMrg.responseHttp(ErrorCode.Parameter_Error.getCode(), session, pid);
                return;
            }

            switch (MiddleConfigType.valueOf(Integer.parseInt(reloadType))) {
                case Merchant:
                    if (StringUtil.isNullOrEmpty(business_no)) {
                        ServerMrg.responseHttp(ErrorCode.Parameter_Error.getCode(), session, pid);
                        return;
                    }
                    DataLoginMrg.getInstance().loadMerchant(business_no);
                    break;
                case MaintainNotice:
                    DataLoginMrg.getInstance().loadBaseMaintainNotice();
                    break;
                case ServerConfig:
                    DataLoginMrg.getInstance().loadBaseServerConfig();
                    break;
                default:
                    throw new IllegalArgumentException(reloadType + "，not exist");
            }

            LOGGER.info("reloadMiddlePlatformConfig，reloadType：{}， success ...", reloadType);
            ServerMrg.responseHttp(ErrorCode.Success.getCode(), session, pid);
        } catch (Exception e) {
            LOGGER.error("GmLogin_ReloadMiddlePlatformConfigHandler", e);
            ServerMrg.responseHttp(ErrorCode.Internal_Server_Error.getCode(), session, pid);
        }
    }
}
