package com.game.handler.http.gmtest;


import com.game.dao.account.AccountDao;
import com.game.dao.player.PlayerDao;
import com.game.engine.io.handler.HttpHandler;
import com.game.engine.io.redis.RedisPoolManager;
import com.game.engine.script.IHandlerEntity;
import com.game.engine.utils.*;
import com.game.entity.account.Account;
import com.game.enums.ErrorCode;
import com.game.enums.ThreeParty;
import com.game.enums.redis.RedisLogin;
import com.game.loginsr.main.LoginServer;
import com.game.loginsr.mrg.AccountMrg;
import com.game.manager.EntityDaoMrg;
import com.game.utils.VirtualThreadUtils;
import io.netty.util.internal.StringUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.net.URI;
import java.net.URLEncoder;
import java.net.http.HttpClient;
import java.net.http.HttpRequest;
import java.net.http.HttpResponse;
import java.nio.charset.StandardCharsets;
import java.time.Duration;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.Function;
import java.util.function.Predicate;

//http://127.0.0.1:8280/login/test?accountId=********&phone=1000
@IHandlerEntity(path = "/login/test", desc = "")
public class GmLoginTestHandler extends HttpHandler {
    private static final Logger LOGGER = LoggerFactory.getLogger(GmLoginTestHandler.class);

    @Override
    public void run() {
        try {
            final String accountId = (String) paramsMap.get("accountId");

            AccountMrg.getInstance().createAccount("xxx",6,"aaaa");


            MsgUtil.responseHttp(ErrorCode.Success.getCode(), session);
        } catch (Exception e) {
            LOGGER.error("", e);
            MsgUtil.responseHttp(ErrorCode.Internal_Server_Error.getCode(), session);
        }
    }

    private void requestPhoneMsg(String areaCode, String phone) {
        try {
            final long timestamp = TimeUtil.currentTimeMillis() / 1000;

            final Map<String, String> paramsMap = new LinkedHashMap<>();
            paramsMap.put("appId", "9PofbQHa");//
            paramsMap.put("numbers", "*************");
            final String content = "Seu codigo de verificacao e: 121212, valido por 5 minutos";
            paramsMap.put("content", URLEncoder.encode(content, StandardCharsets.UTF_8));


            final String uri = "https://api.laaffic.com/v3/sendSms";
            LOGGER.warn("url：{}", uri);
            final String body = JsonUtils.writeAsJson(paramsMap);
            LOGGER.warn("body：{}", body);

            //Api Key + Api Secret + Timestamp
            final String sign = MD5.MD5Encode("AgPXbhj5" + "gEYahFdj" + timestamp);
            LOGGER.warn("sign：{}", sign);
            final HttpRequest request = HttpRequest.newBuilder()
                    .timeout(Duration.ofSeconds(15))
                    .uri(URI.create(uri))
                    .version(HttpClient.Version.HTTP_1_1)
                    .header("Content-Type", "application/json;charset=UTF-8")
                    .header("Sign", sign)
                    .header("Timestamp", timestamp + "")
                    .header("Api-Key", "AgPXbhj5")
                    .POST(HttpRequest.BodyPublishers.ofString(body))
                    .build();
            final HttpResponse<String> httpResponse = LoginServer.getInstance().getHttpClientMrg().send(request, HttpResponse.BodyHandlers.ofString());
            LOGGER.warn("httpResponse：{}", httpResponse.body());
        } catch (Exception e) {
            LOGGER.error("requestPhoneMsg", e);
        }
    }

    private static <T> Predicate<T> distinctByKey(Function<? super T, ?> keyExtractor) {
        final Set<Object> seen = ConcurrentHashMap.newKeySet();
        return t -> seen.add(keyExtractor.apply(t));
    }

    private String idKey = "player:id:seq";
    private int randomDigits = 5; // 随机部分的位数


    public static void main(String[] args) {
        System.out.println((110011 + "").substring(0, 5));
    }

    private static String generateVerifyCode() {
        final int a = (int) (899999 * Math.random() + 100000);
        return String.valueOf(a);
    }
}
