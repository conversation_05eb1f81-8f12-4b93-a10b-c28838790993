package com.game.handler.http.login;

import com.game.c_entity.merchant.C_PhoneMsg;
import com.game.engine.io.handler.HttpHandler;
import com.game.engine.io.redis.RedisPoolManager;
import com.game.engine.log.GameLog;
import com.game.engine.script.IHandlerEntity;
import com.game.engine.utils.*;
import com.game.enums.ErrorCode;
import com.game.enums.redis.RedisLogin;
import com.game.loginsr.main.LoginServer;
import com.game.loginsr.mrg.DataLoginMrg;
import com.game.loginsr.mrg.MerchantData;
import com.proto.LoginMessage;
import com.proto.MIDMessage;
import io.lettuce.core.SetArgs;
import io.netty.util.internal.StringUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.net.URI;
import java.net.URLEncoder;
import java.net.http.HttpClient;
import java.net.http.HttpRequest;
import java.net.http.HttpResponse;
import java.nio.charset.StandardCharsets;
import java.time.Duration;
import java.util.*;

@IHandlerEntity(path = "/api/login/phoneVerifyCode", desc = "手机验证码")
public class ReqPhoneVerifyCodeDataHandler extends HttpHandler {
    private static final Logger LOGGER = LoggerFactory.getLogger(ReqPhoneVerifyCodeDataHandler.class);

    @Override
    public void run() {
        final LoginMessage.ResPhoneVerifyCodeMessage.Builder res = LoginMessage.ResPhoneVerifyCodeMessage.newBuilder();
        res.setMsgID(MIDMessage.MID.ResPhoneVerifyCode_VALUE);
        try {
            final String data = (String) paramsMap.get("data");
            final byte[] bytes = MsgUtil.decode(data);

            final LoginMessage.ReqPhoneVerifyCodeMessage req = LoginMessage.ReqPhoneVerifyCodeMessage.parseFrom(bytes);
            final String host = req.getHost();
            final int codeType = req.getCodeType();
            final String areaCode = req.getAreaCode();
            final String phone = req.getPhone();

            final String business_no = DataLoginMrg.getInstance().findBusiness_no(this.getClass().getSimpleName(), host);
            if (StringUtil.isNullOrEmpty(business_no)) {
                LOGGER.warn("phoneVerifyCode host：{}，business_no is null or empty", host);
                res.setError(ErrorCode.VerifyCode_Send_Error.getCode());
                MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                return;
            }

            if (StringUtil.isNullOrEmpty(areaCode) || StringUtil.isNullOrEmpty(phone)) {
                LOGGER.warn("phoneVerifyCode，phone：{}", phone);
                res.setError(ErrorCode.VerifyCode_Send_Error.getCode());
                MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                return;
            }

            final MerchantData merchantData = DataLoginMrg.getInstance().findMerchantData(this.getClass().getSimpleName(), business_no);
            if (merchantData == null) {
                res.setError(ErrorCode.VerifyCode_Send_Error.getCode());
                MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                return;
            }

            final List<C_PhoneMsg> c_phoneMsgs = new ArrayList<>(merchantData.getC_phoneMsgMap().values());
            if (c_phoneMsgs.isEmpty()) {
                res.setError(ErrorCode.VerifyCode_Send_Error.getCode());
                MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                return;
            }

            c_phoneMsgs.sort(Comparator.comparingInt(C_PhoneMsg::getSort));

            final String verifyCode = generateVerifyCode();
            final String key = RedisLogin.Platform_LG_Account_VerifyCode
                    .getKey(business_no, codeType, areaCode + "-" + phone);//1.重置、2.绑定、3.解绑 4.更换 5.添加

            final long exits = RedisPoolManager.getInstance().function(jedis ->
                    jedis.sync().exists(key));
            if (exits > 0) {
                res.setPhone(phone)
                        .setError(ErrorCode.Verification_Code_Not_Expired.getCode());
                MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                return;
            }

            RedisPoolManager.getInstance().executeAsync(commands ->
                    commands.set(key, verifyCode, SetArgs.Builder.ex(119))
            );

            final long count = RedisPoolManager.getInstance().function(jedis ->
                    jedis.sync().incrby(RedisLogin.Platform_LG_Account_PhoneMsg.getKey(business_no, areaCode + "-" + phone), 1));

            final int index = (int) count % c_phoneMsgs.size();
            final C_PhoneMsg c_phoneMsg = c_phoneMsgs.get(index);

            final String context = c_phoneMsg.getContent().formatted(verifyCode);
            switch (c_phoneMsg.getThreePartyName()) {
                case "sms":
                    requestPhoneMsg(c_phoneMsg, areaCode, phone, context);
                    break;
                case "sms1":
                    requestPhoneMsg1(c_phoneMsg, areaCode, phone, context);
                    break;
                case "sms2":
                    requestPhoneMsg2(c_phoneMsg, areaCode, phone, context);
                    break;
            }

            res.setPhone(phone);
            MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
            LOGGER.warn("phone：{}，send verifyCode，{}", phone, verifyCode);
        } catch (Exception e) {
            LOGGER.error("ReqPhoneVerifyCodeDataHandler", e);
            res.setError(ErrorCode.Internal_Server_Error.getCode());
            MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
        }
    }

    private void requestPhoneMsg(C_PhoneMsg c_phoneMsg, String areaCode, String phone, String content) {
        try {
            final Map<String, Object> paramsMap = new LinkedHashMap<>();
            paramsMap.put("command", "MT_REQUEST");//
            paramsMap.put("cpid", c_phoneMsg.getApiKey());
            paramsMap.put("cppwd", c_phoneMsg.getApiSecret());
            paramsMap.put("da", areaCode + phone);
            paramsMap.put("sm", URLEncoder.encode(content, StandardCharsets.UTF_8));

            final String getUrl = MsgUtil.createObjectGetUrl(paramsMap);

            final String uri = c_phoneMsg.getApiUrl() + "?" + getUrl;
//            LOGGER.warn("url：{}", uri);

            final HttpRequest request = HttpRequest.newBuilder()
                    .timeout(Duration.ofSeconds(15))
                    .uri(URI.create(uri))
                    .version(HttpClient.Version.HTTP_1_1)
                    .header("Content-Type", HttpUtils11.HTTP_CONTENT_TYPE_FORM)
                    .GET()
                    .build();
            final HttpResponse<String> httpResponse = LoginServer.getInstance().getHttpClientMrg()
                    .send(request, HttpResponse.BodyHandlers.ofString());
            LOGGER.warn("httpResponse：{}", httpResponse.body());

            sendLog(c_phoneMsg.getBusiness_no(), areaCode + phone,
                    c_phoneMsg.getThreePartyName(), content, httpResponse.body());
        } catch (Exception e) {
            LOGGER.error("requestPhoneMsg", e);
        }
    }

    private void requestPhoneMsg1(C_PhoneMsg c_phoneMsg, String areaCode, String phone, String content) {
        try {
            final Map<String, Object> paramsMap = new LinkedHashMap<>();
            paramsMap.put("appkey", c_phoneMsg.getApiKey());//
            paramsMap.put("appsecret", c_phoneMsg.getApiSecret());
            paramsMap.put("phone", areaCode + phone);
            paramsMap.put("msg", URLEncoder.encode(content, StandardCharsets.UTF_8));

            final String getUrl = MsgUtil.createObjectGetUrl(paramsMap);

            final String uri = c_phoneMsg.getApiUrl() + "?" + getUrl;
//            LOGGER.warn("url：{}", uri);

            final HttpRequest request = HttpRequest.newBuilder()
                    .timeout(Duration.ofSeconds(15))
                    .uri(URI.create(uri))
                    .version(HttpClient.Version.HTTP_1_1)
                    .header("Content-Type", HttpUtils11.HTTP_CONTENT_TYPE_JSON)
                    .GET()
                    .build();
            final HttpResponse<String> httpResponse = LoginServer.getInstance().getHttpClientMrg()
                    .send(request, HttpResponse.BodyHandlers.ofString());
            LOGGER.warn("httpResponse：{}", httpResponse.body());

            sendLog(c_phoneMsg.getBusiness_no(), areaCode + phone,
                    c_phoneMsg.getThreePartyName(), content, httpResponse.body());
        } catch (Exception e) {
            LOGGER.error("requestPhoneMsg1", e);
        }
    }

    private void requestPhoneMsg2(C_PhoneMsg c_phoneMsg, String areaCode, String phone, String content) {
        try {
            final long timestamp = TimeUtil.currentTimeMillis() / 1000;

            final Map<String, Object> paramsMap = new LinkedHashMap<>();
            paramsMap.put("appId", "9PofbQHa");//
            paramsMap.put("numbers", areaCode + phone);
            paramsMap.put("content", URLEncoder.encode(content, StandardCharsets.UTF_8));

            final String uri = c_phoneMsg.getApiUrl();
//            LOGGER.warn("url：{}", uri);

            //Api Key + Api Secret + Timestamp
            final String sign = MD5.MD5Encode(c_phoneMsg.getApiKey() + c_phoneMsg.getApiSecret() + timestamp);
            final HttpRequest request = HttpRequest.newBuilder()
                    .timeout(Duration.ofSeconds(15))
                    .uri(URI.create(uri))
                    .version(HttpClient.Version.HTTP_1_1)
                    .header("Content-Type", HttpUtils11.HTTP_CONTENT_TYPE_JSON)
                    .header("Sign", sign)
                    .header("Timestamp", timestamp + "")
                    .header("Api-Key", c_phoneMsg.getApiKey())
                    .POST(HttpRequest.BodyPublishers.ofString(JsonUtils.writeAsJson(paramsMap)))
                    .build();
            final HttpResponse<String> httpResponse = LoginServer.getInstance().getHttpClientMrg()
                    .send(request, HttpResponse.BodyHandlers.ofString());
            LOGGER.warn("httpResponse：{}", httpResponse.body());

            sendLog(c_phoneMsg.getBusiness_no(), areaCode + phone,
                    c_phoneMsg.getThreePartyName(), content, httpResponse.body());
        } catch (Exception e) {
            LOGGER.error("requestPhoneMsg1", e);
        }
    }

    private static String generateVerifyCode() {
        final int a = (int) (899999 * Math.random() + 100000);
        return String.valueOf(a);
    }

    private void sendLog(String business_no, String phone, String channel, String msg, String resData) {
        final GameLog phoneVerifyCode = new GameLog("platform_phoneVerifyCodeLog");
        phoneVerifyCode.append("number", GuidGeneratorUtils.generateOrderId())
                .append("business_no", business_no)
                .append("phone", phone)
                .append("channel", channel)
                .append("msg", msg)
                .append("resData", resData)
                .append("logTime", TimeUtil.currentTimeMillis());
        LoginServer.getInstance().getLogProducerMrg().send(phoneVerifyCode);
    }

}


