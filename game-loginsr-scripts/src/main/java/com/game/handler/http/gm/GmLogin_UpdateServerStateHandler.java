package com.game.handler.http.gm;

import com.game.engine.enums.state.ServerState;
import com.game.engine.io.handler.HttpHandler;
import com.game.engine.script.IHandlerEntity;
import com.game.engine.utils.Config;
import com.game.engine.utils.MsgUtil;
import com.game.enums.ErrorCode;
import io.netty.util.internal.StringUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Map;

//http://127.0.0.1:8780/gmLogin/updateServerState?serverState=
@IHandlerEntity(path = "/gmLogin/updateServerState", desc = "设置登录服务器状态")
public class GmLogin_UpdateServerStateHandler extends HttpHandler {
    private static final Logger LOGGER = LoggerFactory.getLogger(GmLogin_UpdateServerStateHandler.class);

    @Override
    public void run() {
        try {
            final Map<String, Object> paramsMap = getParamsMap();
            final String serverState = (String) paramsMap.get("serverState");
            if (StringUtil.isNullOrEmpty(serverState)) {
                MsgUtil.responseHttp(ErrorCode.Parameter_Error.getCode(), session);
                return;
            }

            Config.serverState = ServerState.valueof(Integer.parseInt(serverState));

            MsgUtil.responseHttp(ErrorCode.Success.getCode(), session);
        } catch (Exception e) {
            LOGGER.error("GmLogin_UpdateServerStateHandler", e);
            MsgUtil.responseHttp(ErrorCode.Internal_Server_Error.getCode(), session);
        }
    }

}
