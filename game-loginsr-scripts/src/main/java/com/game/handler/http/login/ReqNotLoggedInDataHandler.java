//package com.game.handler.http.login;
//
//import com.game.engine.io.conf.ConstantConfig;
//import com.game.engine.io.handler.HttpHandler;
//import com.game.engine.log.GameLog;
//import com.game.engine.math.MathUtils;
//import com.game.engine.script.IHandlerEntity;
//import com.game.engine.struct.ServerInfo;
//import com.game.engine.utils.GuidGeneratorUtils;
//import com.game.engine.utils.JWTUtil;
//import com.game.engine.utils.MsgUtil;
//import com.game.engine.utils.TimeUtil;
//import com.game.enums.ErrorCode;
//import com.game.loginsr.main.LoginServer;
//import com.game.loginsr.mrg.DataLoginMrg;
//import com.game.loginsr.mrg.MerchantData;
//import com.game.loginsr.mrg.ServerMrg;
//import com.proto.LoginMessage;
//import com.proto.MIDMessage;
//import io.netty.util.internal.StringUtil;
//import org.slf4j.Logger;
//import org.slf4j.LoggerFactory;
//
//@IHandlerEntity(path = "/api/login/notLoggedIn", desc = "未登录")
//public class ReqNotLoggedInDataHandler extends HttpHandler {
//    private final Logger LOGGER = LoggerFactory.getLogger(ReqNotLoggedInDataHandler.class);
//
//    @Override
//    public void run() {
//        final LoginMessage.ResNotLoggedInMessage.Builder res = LoginMessage.ResNotLoggedInMessage.newBuilder();
//        res.setMsgID(MIDMessage.MID.ResNotLoggedIn_VALUE);
//        try {
//            final String data = (String) paramsMap.get("data");
//            final byte[] bytes = MsgUtil.decode(data);
//            final String country = (String) paramsMap.get("country");
//
//            final LoginMessage.ReqNotLoggedInMessage req = LoginMessage.ReqNotLoggedInMessage.parseFrom(bytes);
//            final String host = req.getHost();
//
//            final String business_no = DataLoginMrg.getInstance().findBusiness_no(this.getClass().getSimpleName(), host);
//            if (StringUtil.isNullOrEmpty(business_no)) {
//                res.setError(ErrorCode.DomainName_Not_Exist.getCode());
//                MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
//                return;
//            }
//
//            final MerchantData merchantData = DataLoginMrg.getInstance().findMerchantData(this.getClass().getSimpleName(), business_no);
//            if (merchantData == null) {
//                res.setError(ErrorCode.Data_Error.getCode());
//                MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
//                return;
//            }
//
//            final long accountId = MathUtils.random(100000, 200000);
//            final ServerInfo gateInfo = ServerMrg.getInstance().getGateServer(accountId);
//            if (gateInfo == null) {
//                LOGGER.warn("gateServer，not exits");
//                res.setError(ErrorCode.Internal_Server_Error.getCode());
//                MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
//                return;
//            }
//
//            final ServerInfo hallInfo = ServerMrg.getInstance().getHallServer(accountId, 0);
//            if (hallInfo == null) {
//                LOGGER.warn("hallServer，not exits");
//                res.setError(ErrorCode.Internal_Server_Error.getCode());
//                MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
//                return;
//            }
//
//            final JWTUtil.JWData jwData = new JWTUtil.JWData();
//            jwData.userId = -1;
//            jwData.account = "visitors";
//            jwData.version = ConstantConfig.getInstance().getVersion();
//            jwData.business_no = business_no;
//            jwData.hallServerId = hallInfo.getId();
//
//            String region = country;
//            if (StringUtil.isNullOrEmpty(region)) {
//                region = merchantData.findC_GlobalValue("initRegion");
//            }
//
//            final String token = JWTUtil.jwtToken(jwData);
//            res.setToken(token)
//                    .setRegion(region)
//                    .setHost(gateInfo.getWwwip());
//            MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
//
//            //TODO 统计未登录访问
//            final GameLog notLoginLog = new GameLog("platform_notLoginLog");
//            notLoginLog.append("number", GuidGeneratorUtils.generateOrderId())
//                    .append("site", host)
//                    .append("business_no", business_no)
//                    .append("ip", MsgUtil.getClientIp(session))
//                    .append("logTime", TimeUtil.currentTimeMillis());
//            LoginServer.getInstance().getLogProducerMrg().send(notLoginLog);
//        } catch (Exception e) {
//            LOGGER.error("ReqNotLoggedInHandler", e);
//            res.setError(ErrorCode.Internal_Server_Error.getCode());
//            MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
//        }
//    }
//
//}
