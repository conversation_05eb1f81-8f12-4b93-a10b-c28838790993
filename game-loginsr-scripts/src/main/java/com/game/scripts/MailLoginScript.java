package com.game.scripts;

import com.game.engine.struct.ServerInfo;
import com.game.engine.utils.MD5;
import com.game.entity.account.Account;
import com.game.entity.account.email.Email;
import com.game.enums.ErrorCode;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Map;
import java.util.Objects;

public class MailLoginScript extends LoginScript {
    private static final Logger LOGGER = LoggerFactory.getLogger(MailLoginScript.class);

    @Override
    protected boolean filterIp(Account account) {
        return false;
    }

    @Override
    protected boolean filterChannel(ServerInfo serverInfo, Account account) {
        return false;
    }

    @Override
    protected ErrorCode _login(Account account, Map<String, Object> paramsMap) {
        if (account == null) {
            LOGGER.warn("email：{}，not exits", paramsMap.get("account"));
            return ErrorCode.Account_NotExist;
        }

        final String passWord = (String) paramsMap.get("password");
        if (!Objects.equals(MD5.MD5Encode(passWord), account.getPassword())) {
            LOGGER.warn("accountId：{}，email password error", account.getAccountId());
            return ErrorCode.PassWord_Error;
        }

        return ErrorCode.Success;
    }

}
