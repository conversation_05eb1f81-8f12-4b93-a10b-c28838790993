package com.game.scripts;

import com.facebook.ads.sdk.APIContext;
import com.facebook.ads.sdk.serverside.*;
import com.game.engine.io.conf.ConstantConfig;
import com.game.engine.utils.HttpUtils11;
import com.game.engine.utils.JsonUtils;
import com.game.engine.utils.SHA256Utils;
import com.game.engine.utils.TimeUtil;
import com.game.entity.account.Account;
import com.game.loginsr.main.LoginServer;
import com.game.loginsr.script.IFeedbackScript;
import com.game.utils.VirtualThreadUtils;
import io.netty.util.internal.StringUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.net.URI;
import java.net.http.HttpClient;
import java.net.http.HttpRequest;
import java.net.http.HttpResponse;
import java.time.Duration;
import java.util.LinkedHashMap;
import java.util.Map;


public class FeedbackScript implements IFeedbackScript {
    private final Logger LOGGER = LoggerFactory.getLogger(FeedbackScript.class.getName());

    @Override
    public void sendFbFeedback(Account account, String email, String phone, String country, String ip) {
        VirtualThreadUtils.execute(() -> {
            try {
                final String eventName = "CompleteRegistration";
                final String fbToken = account.getFbToken();
                final String pixelIdd = account.getPixelId();

                if (StringUtil.isNullOrEmpty(pixelIdd)) {
                    return;
                }

                final APIContext context = new APIContext(fbToken).enableDebug(true);

                final UserData userData = new UserData();
                if (!StringUtil.isNullOrEmpty(email)) {
                    userData.setEmail(SHA256Utils.SHA256(email.toLowerCase()));
                }
                if (!StringUtil.isNullOrEmpty(phone)) {
                    userData.setPhone(SHA256Utils.SHA256(phone));
                }
                if (!StringUtil.isNullOrEmpty(country)) {
                    userData.countryCode(SHA256Utils.SHA256(country.toLowerCase()));
                }
                // It is recommended to send Client IP and User Agent for Conversions API Events.
                userData.clientIpAddress(ip);

                final Event purchaseEvent = new Event();
                purchaseEvent.eventName(eventName)
                        .eventTime(TimeUtil.currentTimeMillis() / 1000L)
                        .userData(userData)
                        .actionSource(ActionSource.website);

                final EventRequest eventRequest = new EventRequest(pixelIdd, context);
                eventRequest.addDataItem(purchaseEvent);

                final EventResponse response = eventRequest.execute();
                LOGGER.info("fb register standard API response：{}", response);
            } catch (Exception e) {
                LOGGER.error("sendFbFeedback", e);
            }
        });
    }

    @Override
    public void sendKWaiFeedback(Account account) {
        VirtualThreadUtils.execute(() -> {
            try {
                final String pixelIdd = account.getKWaiPixelId();

                if (StringUtil.isNullOrEmpty(pixelIdd)) {
                    return;
                }

                LOGGER.warn("iskWai：{}", ConstantConfig.getInstance().iskWai());
                final String uri = "http://www.adsnebula.com/log/common/api";
                final Map<String, Object> paramsMap = new LinkedHashMap<>();
                paramsMap.put("event_name", "EVENT_COMPLETE_REGISTRATION");
                paramsMap.put("pixelId", account.getKWaiPixelId());
                paramsMap.put("access_token", account.getKWaiToken());
                paramsMap.put("testFlag", false);
                paramsMap.put("clickid", account.getClickId());//游戏代码
                paramsMap.put("trackFlag", ConstantConfig.getInstance().iskWai());//测试 true、正式 false
                paramsMap.put("is_attributed", 1);
                paramsMap.put("mmpcode", "PL");
                paramsMap.put("pixelSdkVersion", "9.9.9");

                final HttpRequest request = HttpRequest.newBuilder()
                        .timeout(Duration.ofSeconds(TimeUtil.MIN))
                        .uri(URI.create(uri))
                        .version(HttpClient.Version.HTTP_1_1)
                        .header("Content-Type", HttpUtils11.HTTP_CONTENT_TYPE_JSON)
                        .POST(HttpRequest.BodyPublishers.ofString(JsonUtils.writeAsJson(paramsMap)))
                        .build();
                final HttpResponse<String> httpResponse = LoginServer.getInstance().getHttpClientMrg().send(request, HttpResponse.BodyHandlers.ofString());
                LOGGER.warn("KWai register standard API response：{}", httpResponse.body());
            } catch (Exception e) {
                LOGGER.error("sendFbFeedback KWai", e);
            }
        });
    }
}
