package com.game.scripts;

import com.game.engine.struct.ServerInfo;
import com.game.entity.account.Account;
import com.game.entity.account.ThreePartyInfo;
import com.game.enums.ErrorCode;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Map;
import java.util.Objects;

public class ThreePartyLoginScript extends LoginScript {
    private static final Logger LOGGER = LoggerFactory.getLogger(ThreePartyLoginScript.class);

    @Override
    protected boolean filterIp(Account account) {
        return false;
    }

    @Override
    protected boolean filterChannel(ServerInfo serverInfo, Account account) {
        return false;
    }

    @Override
    protected ErrorCode _login(Account account, Map<String, Object> paramsMap) {
        final int threeParty = (int) paramsMap.get("threeParty");
        final String threePartyId = (String) paramsMap.get("threePartyId");

        if (account == null) {
            LOGGER.warn("threePartyId：{}，not exits", threePartyId);
            return ErrorCode.Account_NotExist;
        }

        final ThreePartyInfo threePartyInfo = account.getThreePartyInfoMap().get(threeParty);
        if (threePartyInfo == null || !Objects.equals(threePartyInfo.getThreePartyId(), threePartyId)) {
            return ErrorCode.ThreeParty_Login_Error;
        }

        return ErrorCode.Success;
    }

}
