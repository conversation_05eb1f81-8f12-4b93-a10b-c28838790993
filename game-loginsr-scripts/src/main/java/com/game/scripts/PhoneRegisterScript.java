package com.game.scripts;

import com.game.c_entity.merchant.C_Advertise;
import com.game.c_entity.middleplatform.C_BaseMerchant;
import com.game.dao.RegisterLimitDao;
import com.game.dao.account.AccountDao;
import com.game.engine.io.redis.RedisPoolManager;
import com.game.engine.script.ScriptLoader;
import com.game.engine.utils.MD5;
import com.game.engine.utils.MsgUtil;
import com.game.entity.account.Account;
import com.game.entity.account.phone.Phone;
import com.game.enums.ErrorCode;
import com.game.enums.ThreeParty;
import com.game.enums.VerifyCode;
import com.game.enums.redis.RedisLogin;
import com.game.loginsr.mrg.AccountMrg;
import com.game.loginsr.mrg.DataLoginMrg;
import com.game.loginsr.mrg.MerchantData;
import com.game.loginsr.script.IFeedbackScript;
import com.game.loginsr.script.IRegisterScript;
import com.game.loginsr.server.script.IAccountScript;
import com.game.loginsr.server.script.ILoginScript;
import com.game.manager.EntityDaoMrg;
import com.proto.LoginMessage;
import com.proto.MIDMessage;
import io.netty.channel.Channel;
import io.netty.util.internal.StringUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.LinkedHashMap;
import java.util.Map;
import java.util.Objects;

public class PhoneRegisterScript implements IRegisterScript {
    private static final Logger LOGGER = LoggerFactory.getLogger(PhoneRegisterScript.class);

    @Override
    public void phoneRegister(LoginMessage.ReqRegisterMessage req, C_BaseMerchant c_baseMerchant, String ip,
                              String country, Channel session) {
        final LoginMessage.ResLoginMessage.Builder res = LoginMessage.ResLoginMessage.newBuilder();
        res.setMsgID(MIDMessage.MID.ResLogin_VALUE);
        try {
            final String areaCode = req.getAreaCode();
            final String phone = req.getAccount();
            final String password = req.getPassword();
            final String referralCode = req.getReferralCode();//3.代理id、4.渠道id
            final String host = req.getHost();//域名
            final String ea = req.getEa();//媒体id
            final String de = req.getDe();//广告Id
            final String activity = req.getActivity();
            final String phoneCode = req.getPhoneCode();

            if (StringUtil.isNullOrEmpty(phone) || StringUtil.isNullOrEmpty(areaCode)) {
                LOGGER.warn("areaCode：{}，phone：{}，is null", phone, areaCode);
                res.setError(ErrorCode.Data_Error.getCode());
                MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                return;
            }

            final String business_no = c_baseMerchant.getBusiness_no();

            final MerchantData merchantData = DataLoginMrg.getInstance().findMerchantData(this.getClass().getSimpleName(), business_no);
            if (merchantData == null) {
                res.setError(ErrorCode.Data_Error.getCode());
                MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                return;
            }

            final String phoneRegisterVerifyCodeOpen = merchantData.findC_GlobalValue("phoneRegisterVerifyCodeOpen");
            if (!StringUtil.isNullOrEmpty(phoneRegisterVerifyCodeOpen) && Boolean.parseBoolean(phoneRegisterVerifyCodeOpen)) {
                final String code = RedisPoolManager.getInstance().function(jedis ->
                        jedis.sync().get(RedisLogin.Platform_LG_Account_VerifyCode.getKey(merchantData.getBusiness_no(), VerifyCode.Register.getType(), areaCode + "-" + phone)));
                if (StringUtil.isNullOrEmpty(phoneCode) || !phoneCode.equals(code)) {
                    LOGGER.warn("account：{}，verifyCode，{}-{}，", areaCode + "-" + phone, code, phoneCode);
                    res.setError(ErrorCode.VerifyCode_Error.getCode());
                    MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                    return;
                }
            }

            //巴西
            if (Objects.equals(areaCode, "55") && phone.charAt(2) != '9') {
                LOGGER.warn("areaCode：{}，phone：{}，invalid phone", areaCode, phone);
                res.setError(ErrorCode.Phone_Format_Error.getCode());
                MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                return;
            }

            //巴西
            if (Objects.equals(areaCode, "55") && phone.length() > 11) {
                LOGGER.warn("areaCode：{}，phone：{}，invalid phone", areaCode, phone);
                res.setError(ErrorCode.Phone_Format_Error.getCode());
                MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                return;
            }

            //邮箱是否注册
            final String accountId = RedisPoolManager.getInstance().function(jedis ->
                    jedis.sync().hget(RedisLogin.Platform_LG_Map_AccountID.getKey(business_no, ThreeParty.Phone.getThreeParty()), areaCode + "-" + phone));
            if (!StringUtil.isNullOrEmpty(accountId)) {
                LOGGER.warn("areaCode：{}，phone：{}，already registered", areaCode, phone);
                res.setError(ErrorCode.Account_AlreadyExists.getCode());
                MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                return;
            }

            final boolean check = ScriptLoader.getInstance().functionScript("AccountScript",
                    (IAccountScript script) -> script.checkReferralCode(referralCode));
            if (!check) {
                res.setError(ErrorCode.ReferralCode_Error.getCode());
                MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                return;
            }

            final Account account = AccountMrg.getInstance().createAccount(business_no, ThreeParty.Phone.getThreeParty(), areaCode + "-" + phone);

            ScriptLoader.getInstance().consumerScript("AccountScript",
                    (IAccountScript script) -> script.bindReferralCode(account, referralCode, host, ea, de));

            //TODO 处理掉参数
            int type = 0;
            String pixelId = "";
            String token = "";
            if (!StringUtil.isNullOrEmpty(req.getEa()) && !StringUtil.isNullOrEmpty(req.getDe())) {
                if (StringUtil.isNullOrEmpty(req.getFbInfo().getPixelId()) && StringUtil.isNullOrEmpty(req.getKWaiInfo().getPixelId())) {
                    final String advertiseId = req.getReferralCode() + req.getEa() + req.getDe();
                    final C_Advertise c_advertise = merchantData.findC_Advertise(advertiseId);
                    if (c_advertise != null) {
                        type = c_advertise.getType();
                        pixelId = c_advertise.getPixelId();
                        token = c_advertise.getToken();
                    }
                }
            }
            if (type == 0) {
                account.setPixelId(req.getFbInfo().getPixelId());
                account.setFbToken(req.getFbInfo().getFbToken());
                account.setClickId(req.getKWaiInfo().getClickId());
                account.setKWaiPixelId(req.getKWaiInfo().getPixelId());
                account.setKWaiToken(req.getKWaiInfo().getKWaiToken());
            } else if (type == 1) {//fb
                account.setPixelId(pixelId);
                account.setFbToken(token);
            } else if (type == 2) {//kWai
                account.setClickId(req.getKWaiInfo().getClickId());
                account.setKWaiPixelId(pixelId);
                account.setKWaiToken(token);
            }

            account.setRegion(country);
            account.setBusiness_no(business_no);
            account.setMediaId(Integer.parseInt(StringUtil.isNullOrEmpty(ea) ? "0" : ea));
            account.setAdId(Integer.parseInt(StringUtil.isNullOrEmpty(de) ? "0" : de));
            account.setActivity(StringUtil.isNullOrEmpty(activity) ? "" : activity);
            account.setEmailSubscribe(req.getEmailSubscribe());

            account.setPassword(MD5.MD5Encode(password));
            final Phone phoneInfo = account.getPhoneInfo();
            phoneInfo.setAreaCode(areaCode);
            phoneInfo.setPhone(phone);
            EntityDaoMrg.getInstance().getDao(AccountDao.class).insert(account);
            EntityDaoMrg.getInstance().getDao(RegisterLimitDao.class).updateRegisterLimit(business_no, ip);

            phoneLogin(req, account, session);

            //回传
            ScriptLoader.getInstance().consumerScript("FeedbackScript",
                    (IFeedbackScript script) -> script.sendFbFeedback(account, "", areaCode + phone, country, ip));

            //KWai回传
            ScriptLoader.getInstance().consumerScript("FeedbackScript",
                    (IFeedbackScript script) -> script.sendKWaiFeedback(account));
        } catch (Exception e) {
            LOGGER.error("phoneRegister error", e);
        }
    }

    private void phoneLogin(LoginMessage.ReqRegisterMessage req, Account account, Channel session) {
        final String areaCode = account.getPhoneInfo().getAreaCode();
        final String phone = account.getPhoneInfo().getPhone();
        final Map<String, Object> paramsMap = new LinkedHashMap<>();
        paramsMap.put("account", areaCode + "-" + phone);
        paramsMap.put("password", req.getPassword());
        paramsMap.put("threeParty", ThreeParty.Phone.getThreeParty());
        paramsMap.put("device", req.getDevice());
        paramsMap.put("business_no", account.getBusiness_no());
        ScriptLoader.getInstance().consumerScript("PhoneLoginScript",
                (ILoginScript script) -> script.login(account, session, paramsMap));
    }
}
