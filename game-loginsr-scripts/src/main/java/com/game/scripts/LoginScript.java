package com.game.scripts;

import com.game.c_entity.merchant.C_BaseServerConfig;
import com.game.dao.account.AccountDao;
import com.game.engine.enums.ServerType;
import com.game.engine.io.conf.ConstantConfig;
import com.game.engine.struct.ServerInfo;
import com.game.engine.utils.*;
import com.game.entity.account.Account;
import com.game.entity.account.AccountFields;
import com.game.enums.ErrorCode;
import com.game.loginsr.mrg.DataLoginMrg;
import com.game.loginsr.mrg.ServerMrg;
import com.game.loginsr.server.script.ILoginScript;
import com.game.manager.EntityDaoMrg;
import com.proto.LoginMessage;
import com.proto.MIDMessage;
import io.netty.channel.Channel;
import io.netty.util.internal.StringUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.mongodb.core.query.Update;

import java.util.*;

public abstract class LoginScript implements ILoginScript {

    private static final Logger LOGGER = LoggerFactory.getLogger(LoginScript.class);

    @Override
    public boolean login(Account account, Channel session, Map<String, Object> paramsMap) {
        final LoginMessage.ResLoginMessage.Builder res = LoginMessage.ResLoginMessage.newBuilder();
        res.setMsgID(MIDMessage.MID.ResLogin_VALUE);
        try {
            final String business_no = (String) paramsMap.get("business_no");
            final C_BaseServerConfig c_baseServerConfig = DataLoginMrg.getInstance().findC_BaseServerConfig(this.getClass().getSimpleName(), business_no);
            if (c_baseServerConfig == null) {
                res.setError(ErrorCode.Data_Error.getCode());
                MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                return false;
            }

            final ErrorCode errorCode = _login(account, paramsMap);
            if (errorCode != ErrorCode.Success) {
                res.setError(errorCode.getCode());
                MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                return false;
            }

            if (!StringUtil.isNullOrEmpty(account.getDate()) && !Objects.equals(account.getDate(), TimeUtil.getDateTimeFormat(TimeUtil.YYYYMMDD))) {
                EntityDaoMrg.getInstance().getDao(AccountDao.class)
                        .updateDailyLoginTimes(account, 0);
            }

            if (account.getDailyLoginTimes() >= 20) {
                LOGGER.warn("accountId：{}，login limit", account.getAccountId());
                res.setError(ErrorCode.Login_Limit_Error.getCode());
                MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                return false;
            }

            final int serverId = c_baseServerConfig.getServerId(account.getAccountId());
            final ServerInfo hallInfo = ServerMrg.getInstance()
                    .getServerType(ServerType.HALL, 0, serverId);
            if (hallInfo == null) {
                LOGGER.warn("hallServer，hallServerId：{}，not exits", serverId);
                res.setError(ErrorCode.Internal_Server_Error.getCode());
                MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                return false;
            }

            final int threeParty = (int) paramsMap.get("threeParty");
            final String ac = (String) paramsMap.get("account");
            final String device = (String) paramsMap.get("device");

            //TODO jwt参数不能再定义
            final JWTUtil.JWData jwData = new JWTUtil.JWData();
            jwData.account = ac;
            jwData.userId = account.getAccountId();
            jwData.business_no = account.getBusiness_no();
            jwData.threeParty = threeParty;
            jwData.device = device;
            jwData.browserId = UUID.randomUUID().toString().replace("-", "");
            jwData.expires = TimeUtil.currentTimeMillis() + 7 * TimeUtil.DAY;
            jwData.version = c_baseServerConfig.getVersion();
            final String token = JWTUtil.jwtToken(jwData);
            account.setDate(TimeUtil.getDateTimeFormat(TimeUtil.YYYYMMDD));

            EntityDaoMrg.getInstance().getDao(AccountDao.class)
                    .incLoginTimes(account);
            EntityDaoMrg.getInstance().getDao(AccountDao.class)
                    .incDailyLoginTimes(account);
            account.setLoginTimes(account.getLoginTimes() + 1);

            final Update update = new Update();
            update.set(AccountFields.date, account.getDate());
            EntityDaoMrg.getInstance().getDao(AccountDao.class)
                    .updateAccount(account.getAccountId(), update);

            res.setToken(token)
                    .setThreeParty(threeParty)
                    .setGateAddress(hallInfo.getWwwip())
                    .setRegister(paramsMap.get("register") != null && (boolean) paramsMap.get("register"));
            MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
            LOGGER.warn("threeParty：{}，account：{}，accountId：{}，login success", threeParty, ac, account.getAccountId());
        } catch (Exception e) {
            LOGGER.error("", e);
            res.setError(ErrorCode.Internal_Server_Error.getCode());
            MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
            return false;
        }
        return true;
    }

    protected abstract boolean filterIp(Account account);

    protected abstract boolean filterChannel(ServerInfo serverInfo, Account account);

    //帐号密码验证在子类进行，因为有的渠道要到渠道平台验证
    protected abstract ErrorCode _login(Account account, Map<String, Object> paramsMap);

}
