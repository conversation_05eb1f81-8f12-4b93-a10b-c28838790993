<?xml version="1.0" encoding="UTF-8"?>
<NettyServerConfig>
    <id>${LOGINID}</id>
    <name>登录服务器</name>
    <port>8200</port>
    <type>LOGIN</type>
    <sendBufferSize>2048</sendBufferSize>
    <receiveBufferSize>8196</receiveBufferSize>
    <reuseAddress>true</reuseAddress>
    <tcpNoDelay>true</tcpNoDelay>
    <readerIdleTime>1200</readerIdleTime>
    <writerIdleTime>1200</writerIdleTime>
    <kafka_connection_string>${kafka.url}</kafka_connection_string>
    <mongo_connection_string>${mongo.url}</mongo_connection_string>
    <mongo_read_write_database>${mongo.db}</mongo_read_write_database>
    <mongo_config_database>${mongo.config}</mongo_config_database>
</NettyServerConfig>