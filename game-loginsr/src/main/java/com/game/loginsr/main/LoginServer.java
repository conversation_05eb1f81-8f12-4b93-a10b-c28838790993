package com.game.loginsr.main;

import com.game.engine.BlockingTaskSchedulerMrg;
import com.game.engine.HttpClientMrg;
import com.game.engine.enums.MsgType;
import com.game.engine.enums.ServerType;
import com.game.engine.enums.state.ServerState;
import com.game.engine.io.conf.*;
import com.game.engine.io.handler.IHandler;
import com.game.engine.io.handler.TcpHandler;
import com.game.engine.io.message.HttpMessageBean;
import com.game.engine.io.message.MessageBean;
import com.game.engine.kafka.LogProducerMrg;
import com.game.engine.mongo.DBConnectionMrg;
import com.game.engine.net.*;
import com.game.engine.util.async.DefaultSameThreadScheduledExecutor;
import com.game.engine.util.async.SameThreadScheduledExecutor;
import com.game.engine.util.concurrent.DefaultThreadFactory;
import com.game.engine.util.concurrent.RejectedExecutionHandlers;
import com.game.engine.utils.*;
import com.game.engine.io.redis.RedisPoolManager;
import com.game.engine.script.ScriptLoader;
import com.game.enums.ErrorCode;
import com.game.loginsr.mrg.DataLoginMrg;
import com.game.loginsr.mrg.LoginEventLoop;
import com.game.loginsr.server.http.LoginHttpServer;
import com.game.loginsr.server.tcp.LoginTcpClient2Proxy;
import com.game.loginsr.server.tcp.LoginTcpServer;
import com.game.manager.DBHandlerRegisterMrg;
import com.game.utils.VirtualThreadUtils;
import com.google.protobuf.Message;
import com.proto.InnerMessage;
import com.proto.MIDMessage;
import io.netty.channel.Channel;
import io.netty.util.internal.StringUtil;
import org.slf4j.LoggerFactory;
import org.slf4j.Logger;

import java.util.Map;
import java.util.concurrent.*;
import java.util.function.Consumer;

public class LoginServer extends World {
    private static final Logger LOGGER = LoggerFactory.getLogger(LoginServer.class);

    private static LoginServer loginServer;

    private LoginTcpServer loginTcpServer;
    private LoginHttpServer loginHttpServer;
    private LoginTcpClient2Proxy loginTcpClient2Proxy;

    private NettyServerConfig nettyServerConfig;
    private NettyClientConfig nettyClientConfig_proxy;
    private NettyServerConfig nettyServerConfig_http;

    private HttpClientMrg httpClientMrg;
    private LoginEventLoop configEventLoop;
    private LogProducerMrg logProducerMrg;
    private SameThreadScheduledExecutor timerSystem;
    private UniqueIDGenerator uniqueIDGenerator;
    private BlockingTaskSchedulerMrg blockingTaskSchedulerMrg;

    public LoginServer() {
        super(1000 / 30);
    }

    public static LoginServer getInstance() {
        return loginServer;
    }

    public NettyClientConfig getNettyClientConfig_proxy() {
        return nettyClientConfig_proxy;
    }

    public LoginTcpClient2Proxy getLoginTcpClient2Proxy() {
        return loginTcpClient2Proxy;
    }

    public static LoginServer getLoginServer() {
        return loginServer;
    }

    public NettyServerConfig getNettyServerConfig() {
        return nettyServerConfig;
    }

    public HttpClientMrg getHttpClientMrg() {
        return httpClientMrg;
    }

    public LogProducerMrg getLogProducerMrg() {
        return logProducerMrg;
    }

    public BlockingTaskSchedulerMrg getBlockingTaskSchedulerMrg() {
        return blockingTaskSchedulerMrg;
    }

    public LoginEventLoop getConfigEventLoop() {
        return configEventLoop;
    }

    public UniqueIDGenerator getUniqueIDGenerator() {
        return uniqueIDGenerator;
    }

    public static void main(String[] args) {
        LOGGER.info("服务器启动时间：{}", TimeUtil.getDateTimeFormat(TimeUtil.YYYYMMDDHHMMSS));

        Config.path = FileUtil.getMainPath();
        LOGGER.info("配置路径为：" + Config.path);
        try {
            WordFilter.getInstance().initWords();
        } catch (Exception e) {
            SysUtil.exit(LoginServer.class, null, "加载Words错误");
        }
        ScriptLoader.getInstance().init((str) -> SysUtil.exit(LoginServer.class, null, "加载脚本错误"));

        loginServer = new LoginServer();
        final ExecutorService service = Executors.newFixedThreadPool(1, (r) -> new Thread(r, "LOGIC_THREAD"));
        final EventConsumer<LogicEvent> eventConsumer = new EventConsumer<>(loginServer);
        final GlobalQueue<LogicEvent> queue = new GlobalQueue<>(service, eventConsumer);
        GlobalQueueContainerMrg.getInstance().setGlobalQueue(queue);

        Runtime.getRuntime().addShutdownHook(new Thread(LoginServer::stops));
    }

    private void initServeConfig() {
        ConstantConfig.reloadConstantConfig(Config.path, "constantConfig.xml");
        NettyServerConfig nettyServerConfig = FileUtil.getConfigXML(Config.path, "nettyServerConfig.xml", NettyServerConfig.class);
        if (nettyServerConfig == null) {
            SysUtil.exit(LoginServer.class, null, "nettyServerConfig");
            return;
        }
        NettyClientConfig nettyClientConfig_proxy = FileUtil.getConfigXML(Config.path, "nettyClientConfig_proxy.xml", NettyClientConfig.class);
        if (nettyClientConfig_proxy == null) {
            SysUtil.exit(LoginServer.class, null, "nettyClientConfig_proxy");
            return;
        }

        NettyServerConfig nettyServerConfig_http = FileUtil.getConfigXML(Config.path, "nettyServerConfig_http.xml", NettyServerConfig.class);
        if (nettyServerConfig_http == null) {
            SysUtil.exit(LoginServer.class, null, "nettyServerConfig_http");
            return;
        }

        this.nettyServerConfig = nettyServerConfig;
        this.nettyClientConfig_proxy = nettyClientConfig_proxy;
        this.nettyServerConfig_http = nettyServerConfig_http;

        Config.SERVER_ID = nettyServerConfig.getId(); // 设置SERVERID
        Config.SERVER_NAME = nettyServerConfig.getName(); // 设置SERVERNAME
        Config.SERVER_CHANNEL = nettyServerConfig.getChannel(); // 设置SERVERWEB
        Config.serverState = ServerState.NORMAL;
    }

    @Override
    protected void registerSeri() {

    }

    @Override
    protected void registerProtoHandler() {

    }

    @Override
    protected void listenOrConnect() throws Exception {
        startServer();
    }

    @Override
    protected void initWhenThreadStartImpl() throws Exception {
        initServeConfig();
        final String serverId = Config.SERVER_ID + "";
        this.uniqueIDGenerator = new UniqueIDGenerator(Integer.parseInt(serverId));
        this.httpClientMrg = new HttpClientMrg();
        this.logProducerMrg = new LogProducerMrg();
        this.blockingTaskSchedulerMrg = new BlockingTaskSchedulerMrg();
        this.timerSystem = new DefaultSameThreadScheduledExecutor(4);
        this.configEventLoop = new LoginEventLoop(null, new DefaultThreadFactory("Login_Config_Event_Loop"), RejectedExecutionHandlers.abort());

        final JedisClusterConfig jpc = FileUtil.getConfigXML(Config.path, "jedisClusterConfig.xml", JedisClusterConfig.class);
        final RedisPoolManager redisPoolManager = new RedisPoolManager(jpc);

        final String mongoUrl = nettyServerConfig.getMongo_connection_string();
        final String mongoData = nettyServerConfig.getMongo_read_write_database();
        DBConnectionMrg.getInstance().dBConnection(mongoUrl, mongoData);
        DBHandlerRegisterMrg.getInstance().dBHandlerRegister();

        DataLoginMrg.getInstance().initMongoConfig();
        DataLoginMrg.getInstance().loadConfigData();

        this.timerSystem.scheduleAtFixedDelay(5 * TimeUtil.SEC, 5 * TimeUtil.SEC, this::serverHeartCheck);

        //启动kafka
        logProducerMrg.start(nettyServerConfig.getKafka_connection_string());
    }

    @Override
    protected void tickImpl(long curTime) throws Exception {
        DBHandlerRegisterMrg.getInstance().tick(curTime);
        this.httpClientMrg.tick();
        this.timerSystem.tick();
        this.blockingTaskSchedulerMrg.tick();
    }

    private void startServer() {
        loginTcpClient2Proxy = new LoginTcpClient2Proxy(nettyClientConfig_proxy);
        loginHttpServer = new LoginHttpServer(nettyServerConfig_http);
        loginTcpServer = new LoginTcpServer(nettyServerConfig);
        {
            loginTcpClient2Proxy.start();
            loginHttpServer.start();
            loginTcpServer.start();
        }
    }

    private static void stops() {
        try {
            Config.serverState = ServerState.CLOSING;
            if (loginServer.timerSystem != null) {
                loginServer.timerSystem.shutdownNow();
            }
            if (loginServer.logProducerMrg != null) {
                loginServer.logProducerMrg.shutdown();
            }
            if (loginServer.configEventLoop != null) {
                loginServer.configEventLoop.shutdownNow();
            }
            VirtualThreadUtils.shutdown();

            Thread.sleep(1000);
            RedisPoolManager.getInstance().destroy();
            loginServer.stop();
        } catch (Exception e) {
            LOGGER.error("stops", e);
        }
    }

    private void stop() {
        if (loginHttpServer != null) {
            loginHttpServer.stop();
        }
        if (loginTcpServer != null) {
            loginTcpServer.stop();
        }
        if (loginTcpClient2Proxy != null) {
            loginTcpClient2Proxy.stop();
        }
    }

    @Override
    protected void onLogicEvent(LogicEvent evt) {
        switch (evt.getLogicEventType()) {
            /**
             * 登录
             */
            case LOGINSERVER_ON_TCP_CONNECT: {
                loginTcpServer.onIoSessionConnect(evt.getChannel());
                break;
            }
            case LOGINSERVER_ON_DISCONNECT: {
                loginTcpServer.onIoSessionClosed(evt.getChannel());
                break;
            }

            case L_PROXYCLINET_ON_TCP_CONNECT: {
                loginTcpClient2Proxy.onIoSessionConnect(evt.getChannel());
                break;
            }
            case L_PROXYCLINET_ON_DISCONNECT: {
                loginTcpClient2Proxy.onIoSessionClosed(evt.getChannel());
                break;
            }

            case UDP_MESSAGE_EVENT_S_RECV:
                httpMessageHandler(evt);
                break;

            case LOGINSERVER_MESSAGE_EVENT_S_RECV:
            case L_PROXYCLINET_MESSAGE_EVENT_S_RECV:
                innerMessageHandler(evt);
                break;

            default: {
                throw new RuntimeException();
            }
        }
    }

    private void httpMessageHandler(LogicEvent evt) {
        final Channel session = evt.getChannel();
        try {
            @SuppressWarnings("unchecked") final Map<String, Object> headersMap = (Map<String, Object>) evt.getParamC();

            @SuppressWarnings("unchecked") final Map<String, Object> paramsMap = (Map<String, Object>) evt.getParamA();
            final String requestPath = (String) evt.getParamB();

            if (StringUtil.isNullOrEmpty(requestPath) || paramsMap.isEmpty()) {
                LOGGER.warn("ip ：{}，request error：{}，params：{}", MsgUtil.getClientIp(session), requestPath, JsonUtils.writeAsJson(paramsMap));
                MsgUtil.responseHttp(ErrorCode.Data_Error.getCode(), session);
                return;
            }

            final HttpMessageBean httpMessageBean = ScriptLoader.getInstance().getHttpMessageBean(requestPath);
            if (httpMessageBean == null) {
                LOGGER.error("HttpMessagePoll，未能找到，content = {} 的 httpMessageBean", requestPath);
                MsgUtil.responseHttp(ErrorCode.Data_Error.getCode(), session);
                return;
            }

            final Object country = headersMap.get("cf-ipcountry");
            if (country != null) {
                paramsMap.put("country", country);
            }

            IHandler handler = httpMessageBean.newHandler();
            handler.setSession(session);
            handler.setCreateTime(TimeUtil.currentTimeMillis());
            handler.setParamsMap(paramsMap);

            if (requestPath.contains("reloadScripts") || requestPath.contains("/api/login/")) {//脚本
                VirtualThreadUtils.execute(handler);
            } else if (requestPath.contains("ReloadConfig")) {
                LoginServer.getInstance().getConfigEventLoop().execute(handler);
            } else {
                long time = TimeUtil.currentTimeMillis();
                handler.run();
                time = TimeUtil.currentTimeMillis() - time;
                if (time > 10) {
                    LOGGER.warn("{}，处理时间超过，{}", handler.getClass().getSimpleName(), time);
                }
            }
        } catch (Exception e) {
            LOGGER.error("", e);
            MsgUtil.responseHttp(ErrorCode.Data_Error.getCode(), session);
        }
    }

    private void innerMessageHandler(LogicEvent event) {
        try {
            final int msgType = event.getIntParamA();
            final long id = event.getLongParamA();
            final int msgId = event.getIntParamB();
            final byte[] bytes = (byte[]) event.getParamA();
            if (msgType == MsgType.IDMESSAGE.getType()) {// 数据结构:msgId:pfbytes

                // 在本地注册，必须预处理
                final MessageBean messageBean = ScriptLoader.getInstance().getMessageBean(msgId);
                if (messageBean != null) {
                    final Message message = messageBean.buildMessage(bytes);
                    final TcpHandler handler = (TcpHandler) messageBean.newHandler();
                    if (handler != null) {
                        handler.setPid(id);
                        handler.setMsgBytes(bytes);
                        handler.setMessage(message);
                        handler.setSession(event.getChannel());
                        handler.run();
                    }
                } else {
                    LOGGER.warn("消息[{}]代码未实现逻辑", msgId);
                }
            } else {
                LOGGER.warn("消息类型{}未实现,玩家{}消息发送失败", msgType, id);
            }
        } catch (Exception e) {
            LOGGER.error("channelRead", e);
        }
    }

    private void serverHeartCheck() {
        try {
            LoginServer.getInstance().getLoginTcpClient2Proxy().checkStatus();

            LoginServer.getInstance().buildRegisterUpdateMessage(msg -> {
                LoginServer.getInstance().getLoginTcpClient2Proxy().broadcastMsgAllSessions(msg);
            });

            InnerMessage.InnerReqServerListMessage.Builder req = InnerMessage.InnerReqServerListMessage.newBuilder();
            req.setMsgID(MIDMessage.MID.InnerReqServerList_VALUE)
                    .addType(ServerType.GATE.getType())
                    .addType(ServerType.HALL.getType());
            LoginServer.getInstance().getLoginTcpClient2Proxy().sendMsg(req.build());
        } catch (Exception e) {
            LOGGER.error("serverHeartCheck", e);
        }
    }

    /**
     * 构建登录信息
     *
     * @param action
     */
    private void buildRegisterUpdateMessage(Consumer<InnerMessage.InnerReqRegisterUpdateMessage> action) {
        InnerMessage.InnerReqRegisterUpdateMessage.Builder req = InnerMessage.InnerReqRegisterUpdateMessage.newBuilder();
        req.setMsgID(MIDMessage.MID.InnerReqRegisterUpdate_VALUE);
        req.setServerInfo(buildServerInfo(nettyServerConfig));
        if (action != null) {
            action.accept(req.build());
        }
    }

    /**
     * 构建服务器信息
     *
     * @param config
     * @return
     */
    private InnerMessage.InnerServerInfo buildServerInfo(NettyServerConfig config) {
        final InnerMessage.InnerServerInfo.Builder builder = InnerMessage.InnerServerInfo.newBuilder();
        builder.setId(config.getId())
                .setIp(config.getIp() == null ? "" : config.getIp())
                .setType(config.getType().getType())
                .setPort(config.getPort())
                .setGameState(Config.serverState.getState())
                .setPower(config.getPower())
                .setHttpPort(config.getHttpPort())
                .setName(config.getName());
        return builder.build();
    }

    private void asyncExecute(IHandler handler) {
        VirtualThreadUtils.execute(handler);
    }
}
