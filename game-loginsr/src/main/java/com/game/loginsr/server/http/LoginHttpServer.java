package com.game.loginsr.server.http;

import com.game.engine.enums.ServerType;
import com.game.engine.io.conf.NettyServerConfig;
import com.game.engine.net.GlobalQueueContainerMrg;
import com.game.engine.service.HttpService;
import com.game.engine.io.netty.HttpServer;
import com.game.loginsr.server.handler.LoginHttpServerHandler;
import io.netty.channel.ChannelInitializer;
import io.netty.channel.socket.SocketChannel;
import io.netty.handler.codec.http.HttpMethod;
import io.netty.handler.codec.http.HttpObjectAggregator;
import io.netty.handler.codec.http.HttpServerCodec;
import io.netty.handler.codec.http.cors.CorsConfig;
import io.netty.handler.codec.http.cors.CorsConfigBuilder;
import io.netty.handler.codec.http.cors.CorsHandler;
import io.netty.util.AsciiString;
import org.slf4j.LoggerFactory;
import org.slf4j.Logger;

import java.util.ArrayList;
import java.util.Arrays;

public class LoginHttpServer extends HttpService {

    private static final Logger log = LoggerFactory.getLogger(LoginHttpServer.class);

    private final HttpServer nettyHttpServer;
    private final NettyServerConfig nettyServerConfig;

    public LoginHttpServer(NettyServerConfig nettyServerConfig) {
        super(ServerType.HTTP, GlobalQueueContainerMrg.getInstance().getGlobalQueue().getLogicQueue());
        this.nettyServerConfig = nettyServerConfig;
        this.nettyHttpServer = new HttpServer(nettyServerConfig, new LoginHttpServerInitializer(this));
    }

    @Override
    public void start() {
        log.info(" run ... ");
        nettyHttpServer.start();
    }

    @Override
    public void stop() {
        log.warn(" stop ... ");
        nettyHttpServer.stop();
    }

    @Override
    public String toString() {
        return nettyServerConfig.getName();
    }

    static class LoginHttpServerInitializer extends ChannelInitializer<SocketChannel> {

        private final HttpService httpService;

        public LoginHttpServerInitializer(HttpService httpService) {
            this.httpService = httpService;
        }

        @Override
        protected void initChannel(SocketChannel ch) {
            // 添加CORS处理器
            final CorsConfig corsConfig = CorsConfigBuilder
                    .forAnyOrigin()
                    .allowNullOrigin()
                    .allowCredentials()
                    .allowedRequestMethods(HttpMethod.POST, HttpMethod.GET, HttpMethod.PUT, HttpMethod.OPTIONS, HttpMethod.DELETE)
                    .allowedRequestHeaders(AsciiString.cached("X-PINGOTHER"), AsciiString.cached("Authorization"), AsciiString.cached("Content-Type"))
                    .build();

            ch.pipeline().addLast(new HttpServerCodec());
            ch.pipeline().addLast(new HttpObjectAggregator(1024 * 10));
            ch.pipeline().addLast(new CorsHandler(corsConfig));
            ch.pipeline().addLast(new LoginHttpServerHandler(httpService));
        }

    }
}
