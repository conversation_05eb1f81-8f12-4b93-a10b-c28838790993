package com.game.loginsr.server.tcp;

import com.game.engine.enums.ServerType;
import com.game.engine.io.conf.NettyServerConfig;
import com.game.engine.io.netty.TcpServer;
import com.game.engine.io.netty.code.ProtocolCoder;
import com.game.engine.net.GlobalQueueContainerMrg;
import com.game.engine.service.TcpService;
import com.game.loginsr.server.handler.LoginTcpServerHandler;
import io.netty.channel.ChannelInitializer;
import io.netty.channel.socket.SocketChannel;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class LoginTcpServer extends TcpService {

    private static final Logger LOGGER = LoggerFactory.getLogger(LoginTcpServer.class);

    private final TcpServer nettyServer;
    private final NettyServerConfig nettyServerConfig;

    public LoginTcpServer(NettyServerConfig nettyServerConfig) {
        super(ServerType.LOGIN, GlobalQueueContainerMrg.getInstance().getGlobalQueue().getLogicQueue());
        this.nettyServerConfig = nettyServerConfig;
        nettyServer = new TcpServer(nettyServerConfig, new LoginServerInitializer(this));
    }

    @Override
    public void broadcastMsg(Object msg) {
    }

    @Override
    public void checkStatus() {
        nettyServer.start();
    }

    @Override
    public void start() {
        LOGGER.info(" run ... ");
        nettyServer.start();
    }

    @Override
    public void stop() {
        LOGGER.warn(" stop ... ");
        nettyServer.stop();
    }

    @Override
    public String toString() {
        return nettyServerConfig.getName();
    }

    public int getId() {
        return nettyServerConfig.getId();
    }

    public String getName() {
        return nettyServerConfig.getName();
    }

    public String getWeb() {
        return nettyServerConfig.getChannel();
    }


    static class LoginServerInitializer extends ChannelInitializer<SocketChannel> {

        private final TcpService tcpService;

        public LoginServerInitializer(TcpService tcpService) {
            this.tcpService = tcpService;
        }

        @Override
        protected void initChannel(SocketChannel ch) throws Exception {
            ch.pipeline().addLast(new ProtocolCoder());
            ch.pipeline().addLast(new LoginTcpServerHandler(tcpService));
        }

    }
}
