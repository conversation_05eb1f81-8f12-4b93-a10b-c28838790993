package com.game.loginsr.server.tcp;

import com.game.engine.enums.ServerType;
import com.game.engine.io.conf.NettyClientConfig;
import com.game.engine.io.netty.TcpClient;
import com.game.engine.io.netty.code.ProtocolCoder;
import com.game.engine.net.GlobalQueueContainerMrg;
import com.game.engine.service.TcpService;
import com.game.loginsr.server.handler.LoginProxyClientHandler;
import io.netty.channel.ChannelInitializer;
import io.netty.channel.socket.SocketChannel;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class LoginTcpClient2Proxy extends TcpService {
    private static final Logger LOGGER = LoggerFactory.getLogger(LoginTcpClient2Proxy.class);

    private final TcpClient tcpClient;
    private final NettyClientConfig nettyClientConfig;

    public LoginTcpClient2Proxy(NettyClientConfig nettyClientConfig) {
        super(ServerType.PROXY, GlobalQueueContainerMrg.getInstance().getGlobalQueue().getLogicQueue());
        this.nettyClientConfig = nettyClientConfig;
        tcpClient = new TcpClient(nettyClientConfig, new ProxyClientInitializer(this));
    }

    @Override
    public void broadcastMsg(Object msg) {
    }

    @Override
    public void checkStatus() {
        tcpClient.checkStatus();
    }

    @Override
    public void start() {
        LOGGER.info(" run ... ");
        tcpClient.start();
    }

    @Override
    public void stop() {
        LOGGER.warn(" stop ... ");
        tcpClient.stop();
    }

    @Override
    public String toString() {
        return nettyClientConfig.getName();
    }

    static class ProxyClientInitializer extends ChannelInitializer<SocketChannel> {

        private final TcpService tcpService;

        public ProxyClientInitializer(TcpService tcpService) {
            this.tcpService = tcpService;
        }

        @Override
        protected void initChannel(SocketChannel ch) throws Exception {
            ch.pipeline().addLast(new ProtocolCoder());
            ch.pipeline().addLast(new LoginProxyClientHandler(tcpService));
        }

    }
}
