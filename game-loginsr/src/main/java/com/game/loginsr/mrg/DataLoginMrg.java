package com.game.loginsr.mrg;

import com.game.c_entity.merchant.*;
import com.game.c_entity.middleplatform.C_BaseMaintainNotice;
import com.game.c_entity.middleplatform.C_BaseMerchant;
import com.game.engine.io.conf.NettyServerConfig;
import com.game.engine.mongo.DBConnectionMrg;
import com.game.loginsr.main.LoginServer;
import com.mongodb.client.MongoClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

public class DataLoginMrg {
    private static final Logger LOGGER = LoggerFactory.getLogger(DataLoginMrg.class);

    private static final DataLoginMrg instance = new DataLoginMrg();

    public static DataLoginMrg getInstance() {
        return instance;
    }

    public static MongoTemplate mongoTemplate;

    public void initMongoConfig() {
        final NettyServerConfig nettyServerConfig = LoginServer.getInstance().getNettyServerConfig();
        final String gameData = nettyServerConfig.getMongo_config_database();
        final MongoClient mongoClient = DBConnectionMrg.getInstance().getMongoClient();
        mongoTemplate = new MongoTemplate(mongoClient, gameData);
    }

    private final Map<String, C_BaseMerchant> c_baseHostMerchantMap = new ConcurrentHashMap<>(8);
    private final Map<Integer, C_BaseMaintainNotice> c_baseMaintainNoticeMap = new ConcurrentHashMap<>(8);
    private final Map<String, C_BaseServerConfig> c_baseServerConfigMap = new ConcurrentHashMap<>(8);

    //商户数据
    private final Map<String, MerchantData> merchantDataMap = new ConcurrentHashMap<>(8);

    public void loadConfigData() throws Exception {
        loadBaseMaintainNotice();
        loadBaseServerConfig();

        loadAllMerchant();
    }

    public void loadBaseMaintainNotice() throws Exception {
        //维护公告
        c_baseMaintainNoticeMap.clear();
        final Query query = new Query();
        query.addCriteria(Criteria.where("status").is(true));

        final C_BaseMaintainNotice c_baseMaintainNotice = mongoTemplate.findOne(query, C_BaseMaintainNotice.class);
        if (c_baseMaintainNotice == null) {
            return;
        }
        c_baseMaintainNotice.check();
        c_baseMaintainNoticeMap.put(c_baseMaintainNotice.getC_id(), c_baseMaintainNotice);
    }

    public void loadBaseServerConfig() throws Exception {
        //服务器配置
        c_baseServerConfigMap.clear();

        final List<C_BaseServerConfig> c_baseServerConfigs = DataLoginMrg.mongoTemplate.findAll(C_BaseServerConfig.class);
        for (C_BaseServerConfig c_baseServerConfig : c_baseServerConfigs) {
            c_baseServerConfig.check();
            c_baseServerConfigMap.put(c_baseServerConfig.getBusiness_no(), c_baseServerConfig);
        }
    }

    private void loadAllMerchant() throws Exception {
        final Query query = new Query();
        query.addCriteria(Criteria.where("status").is(1)
                .and("deleteTime").is(0));
        //商户
        c_baseHostMerchantMap.clear();
        merchantDataMap.clear();
        List<C_BaseMerchant> c_baseMerchants = mongoTemplate.find(query, C_BaseMerchant.class);
        for (C_BaseMerchant c_baseMerchant : c_baseMerchants) {
            c_baseMerchant.check();
            for (String domain : c_baseMerchant.getMerchantDomain()) {
                c_baseHostMerchantMap.put(domain, c_baseMerchant);
            }

            MerchantData merchantData = merchantDataMap.putIfAbsent(c_baseMerchant.getBusiness_no(), new MerchantData());
            if (merchantData == null) {
                merchantData = merchantDataMap.get(c_baseMerchant.getBusiness_no());
                merchantDataMap.put(c_baseMerchant.getBusiness_no(), merchantData);
            }
            merchantData.loadData(c_baseMerchant.getBusiness_no());
        }
    }

    public void loadMerchant(String business_no) throws Exception {
        //商户
        final Query query = new Query(Criteria.where("business_no").is(business_no));
        final C_BaseMerchant c_baseMerchant = mongoTemplate.findOne(query, C_BaseMerchant.class);
        if (c_baseMerchant == null) {
            LOGGER.warn("merchant ：{}，not exist", business_no);
            return;
        }

        c_baseMerchant.check();

        if (c_baseMerchant.getDeleteTime() > 0 || c_baseMerchant.getStatus() == 0) {
            merchantDataMap.remove(c_baseMerchant.getBusiness_no());
            for (String domain : c_baseMerchant.getMerchantDomain()) {
                c_baseHostMerchantMap.remove(domain);
            }
            return;
        }

        for (String domain : c_baseMerchant.getMerchantDomain()) {
            c_baseHostMerchantMap.put(domain, c_baseMerchant);
        }

        MerchantData merchantData = merchantDataMap.putIfAbsent(c_baseMerchant.getBusiness_no(), new MerchantData());
        if (merchantData == null) {
            merchantData = merchantDataMap.get(c_baseMerchant.getBusiness_no());
            merchantDataMap.put(c_baseMerchant.getBusiness_no(), merchantData);
            merchantData.loadData(c_baseMerchant.getBusiness_no());
            LOGGER.info("business_no：{}，add success", c_baseMerchant.getBusiness_no());
        }
    }

    public String findBusiness_no(String className, String host) {
        final C_BaseMerchant c_baseMerchant = this.c_baseHostMerchantMap.get(host);
        if (c_baseMerchant == null) {
            LOGGER.warn("className：{}，c_baseMerchant，merchantDomain：{}，not exist", className, host);
            return null;
        }
        return c_baseMerchant.getBusiness_no();
    }

    public C_BaseMerchant findC_BaseMerchant(String className, String host) {
        final C_BaseMerchant c_baseMerchant = this.c_baseHostMerchantMap.get(host);
        if (c_baseMerchant == null) {
            LOGGER.warn("className：{}，c_baseMerchant，merchantDomain：{}，not exist", className, host);
            return null;
        }
        return c_baseMerchant;
    }

    public MerchantData findMerchantData(String className, String business_no) {
        final MerchantData merchantData = merchantDataMap.get(business_no);
        if (merchantData == null) {
            LOGGER.warn("className：{}，MerchantData，business_no：{}，not exits", className, business_no);
            return null;
        }
        return merchantData;
    }

    public boolean isBlackList(String business_no, String ip) {
        final Query query = new Query();
        query.addCriteria(Criteria.where("business_no").is(business_no)
                .and("ip").is(ip));
        return mongoTemplate.exists(query, C_IpBlackList.class);
    }

    public boolean isWhiteList(String business_no, String ip) {
        final Query query = new Query();
        query.addCriteria(Criteria.where("business_no").is(business_no)
                .and("ip").is(ip));
        return mongoTemplate.exists(query, C_IpWhiteList.class);
    }

    public C_BaseMaintainNotice findC_BaseMaintainNotice() {
        //维护公告
        final Optional<C_BaseMaintainNotice> optional = c_baseMaintainNoticeMap.values().stream().findFirst();
        return optional.orElse(null);
    }

    public C_BaseServerConfig findC_BaseServerConfig(String className, String business_no) {
        final C_BaseServerConfig c_serverConfig = c_baseServerConfigMap.get(business_no);
        if (c_serverConfig == null) {
            LOGGER.warn("className：{}，C_BaseServerConfig，business_no：{}，not exist", className, business_no);
            return null;
        }
        return c_serverConfig;
    }

    public Map<String, MerchantData> getMerchantDataMap() {
        return merchantDataMap;
    }
}
