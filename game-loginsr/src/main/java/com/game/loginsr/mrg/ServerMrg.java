package com.game.loginsr.mrg;

import com.game.engine.enums.ServerType;
import com.game.engine.struct.ServerInfo;
import com.game.engine.utils.JsonUtils;
import com.game.engine.utils.MsgUtil;
import com.proto.InnerMessage;
import com.proto.MIDMessage;
import io.netty.channel.Channel;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;

public class ServerMrg {
    private static final Logger LOGGER = LoggerFactory.getLogger(ServerMrg.class);

    private static final ServerMrg instance = new ServerMrg();

    public static final AtomicInteger count = new AtomicInteger(0);

    //服务器 网关、大厅
    private final Map<ServerType, Map<Integer, ServerInfo>> serversMap = new HashMap<>(5);

    //网关
    private final List<ServerInfo> gateServerList = new ArrayList<>();

    //大厅
//    private final List<ServerInfo> hallServerList = new ArrayList<>();

    public static ServerMrg getInstance() {
        return instance;
    }

    public ServerInfo getServerInfo(ServerType serverType, int serverId) {
        final Map<Integer, ServerInfo> serverMap = serversMap.get(serverType);
        if (serverMap != null) {
            return serverMap.get(serverId);
        }
        return null;
    }

    public Map<Integer, ServerInfo> getServersMap(ServerType serverType) {
        return serversMap.get(serverType);
    }

    private void addServerInfo(ServerType serverType, ServerInfo serverInfo) {
        Map<Integer, ServerInfo> serverMap = serversMap.putIfAbsent(serverType, new TreeMap<>(Comparator.comparingInt(o -> o)));
        if (serverMap == null) {
            serverMap = serversMap.get(serverType);
        }
        serverMap.put(serverInfo.getId(), serverInfo);
    }

    public void removeGateServer(int gateId) {
        for (int i = 0; i < gateServerList.size(); i++) {
            if (gateServerList.get(i).getId() != gateId) {
                continue;
            }
            gateServerList.remove(i);
            return;
        }
    }

    public ServerInfo getGateServer(long accountId) {
        final Map<Integer, ServerInfo> serverInfoMap = getServersMap(ServerType.GATE);
        if (serverInfoMap.isEmpty()) {
            return null;
        }
        final int index = (int) (accountId % serverInfoMap.size());
        final List<ServerInfo> gateServers = new ArrayList<>(serverInfoMap.values());
        if (this.gateServerList.getFirst().getPower() > 0) {
            return this.gateServerList.getFirst();
        }
        return gateServers.get(index);
    }

    public void updateServer(InnerMessage.InnerServerInfo info) {
        boolean isRegister = false;
        ServerInfo serverInfo = getServerInfo(ServerType.valueOf(info.getType()), info.getId());
        if (serverInfo == null) {
            serverInfo = new ServerInfo();
            serverInfo.setId(info.getId());
            serverInfo.setServerType(info.getType());
            addServerInfo(ServerType.valueOf(info.getType()), serverInfo);
            if (ServerType.GATE.getType() == info.getType()) {
                this.gateServerList.add(serverInfo);
            }
//            if (ServerType.HALL.getType() == info.getType()) {
//                this.hallServerList.add(serverInfo);
//            }
            isRegister = true;
        }
        serverInfo.setIp(info.getIp());
        serverInfo.setPort(info.getPort());
        serverInfo.setGameState(info.getGameState());
        serverInfo.setVersion(info.getVersion());
        serverInfo.setContent(info.getContent());
        serverInfo.setPower(info.getPower());
        serverInfo.setOnline(info.getOnline());
        serverInfo.setHttpPort(info.getHttpPort());
        serverInfo.setName(info.getName());
        serverInfo.setWwwip(info.getWwwIp());
        if (ServerType.GATE.getType() == info.getType()) {
            updateSortGateServer();
        }
//        if (ServerType.HALL.getType() == info.getType()) {
//            updateSortHallServer();
//        }
        if (isRegister) {
            LOGGER.info("register server，{}", serverInfo);
        }
    }

    /**
     * 更新gate顺序
     */
    private void updateSortGateServer() {
        gateServerList.sort(Comparator.comparingInt((ServerInfo s0) -> (s0.getOnline() - s0.getPower())));
    }

//    public void removeHallServer(int hallServerId) {
//        for (int i = 0; i < hallServerList.size(); i++) {
//            if (hallServerList.get(i).getId() != hallServerId) {
//                continue;
//            }
//            hallServerList.remove(i);
//            return;
//        }
//    }

    public ServerInfo getServerType(ServerType serverType, long accountId, int hallServerId) {
        final Map<Integer, ServerInfo> serverInfoMap = getServersMap(serverType);
        if (serverInfoMap.isEmpty()) {
            return null;
        }

        final ServerInfo serverInfo = serverInfoMap.get(hallServerId);
        if (serverInfo != null) {
            return serverInfo;
        }

        if (accountId == 0) {
            return null;
        }

        final int index = (int) (accountId % serverInfoMap.size());
        final List<ServerInfo> serverInfos = new ArrayList<>(serverInfoMap.values());
        return serverInfos.get(index);
    }

    /**
     * 更新Hall顺序
     */
    private void updateSortHallServer() {
//        hallServerList.sort(Comparator.comparingInt((ServerInfo s0) -> (s0.getOnline() - s0.getPower())));
    }

    public static void responseHttp(int error, Channel session, long pid) {
        final Map<String, Object> httpRes = new LinkedHashMap<>();
        try {
            httpRes.put("error", error);
            final InnerMessage.InnerResHttpHandlerMessage.Builder res = InnerMessage.InnerResHttpHandlerMessage.newBuilder();
            res.setMsgID(MIDMessage.MID.InnerResHttpHandler_VALUE)
                    .setParams(JsonUtils.writeAsJson(httpRes));
            MsgUtil.sendInnerMsg(session, res.build(), pid);
        } catch (Exception e) {
            LOGGER.error("responseHttp", e);
        }
    }

}
