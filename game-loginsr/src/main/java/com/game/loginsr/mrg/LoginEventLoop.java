package com.game.loginsr.mrg;

import com.game.engine.util.concurrent.EventLoopGroup;
import com.game.engine.util.concurrent.RejectedExecutionHandler;
import com.game.engine.util.concurrent.disruptor.DisruptorEventLoop;
import com.game.engine.util.concurrent.disruptor.WaitStrategyFactory;

import javax.annotation.Nonnull;
import javax.annotation.Nullable;
import java.util.concurrent.ThreadFactory;

public class LoginEventLoop extends DisruptorEventLoop {
    public LoginEventLoop(@Nullable EventLoopGroup parent, @Nonnull ThreadFactory threadFactory, @Nonnull RejectedExecutionHandler rejectedExecutionHandler) {
        super(parent, threadFactory, rejectedExecutionHandler);
    }

    public LoginEventLoop(@Nullable EventLoopGroup parent, @Nonnull ThreadFactory threadFactory, @Nonnull RejectedExecutionHandler rejectedExecutionHandler, @Nonnull WaitStrategyFactory waitStrategyFactory) {
        super(parent, threadFactory, rejectedExecutionHandler, waitStrategyFactory);
    }

    public LoginEventLoop(@Nullable EventLoopGroup parent, @Nonnull ThreadFactory threadFactory, @Nonnull RejectedExecutionHandler rejectedExecutionHandler, int ringBufferSize, int taskBatchSize) {
        super(parent, threadFactory, rejectedExecutionHandler, ringBufferSize, taskBatchSize);
    }

    public LoginEventLoop(@Nullable EventLoopGroup parent, @Nonnull ThreadFactory threadFactory, @Nonnull RejectedExecutionHandler rejectedExecutionHandler, @Nonnull WaitStrategyFactory waitStrategyFactory, int ringBufferSize, int taskBatchSize) {
        super(parent, threadFactory, rejectedExecutionHandler, waitStrategyFactory, ringBufferSize, taskBatchSize);
    }

}
