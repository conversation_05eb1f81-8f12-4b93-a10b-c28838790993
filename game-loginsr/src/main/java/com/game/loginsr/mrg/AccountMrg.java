package com.game.loginsr.mrg;

import com.game.dao.account.AccountDao;
import com.game.engine.io.redis.RedisPoolManager;
import com.game.engine.utils.UniqueIDGenerator;
import com.game.entity.account.Account;
import com.game.enums.redis.RedisLogin;
import com.game.loginsr.main.LoginServer;
import com.game.manager.EntityDaoMrg;
import com.game.redis.RedisScriptUtils;
import io.netty.util.internal.StringUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class AccountMrg {

    private static final Logger LOGGER = LoggerFactory.getLogger(AccountMrg.class);
    private static final AccountMrg INSTANCE = new AccountMrg();

    public static AccountMrg getInstance() {
        return INSTANCE;
    }

    public Account findAccountId(long accountId) {
        return EntityDaoMrg.getInstance().getDao(AccountDao.class).getById(accountId);
    }

    public Account createAccount(String business_no, int threeParty, String account) {
        final UniqueIDGenerator uniqueIDGenerator = LoginServer.getInstance().getUniqueIDGenerator();
        final String accountId = RedisScriptUtils.getInstance().generateAccountId(uniqueIDGenerator);
        if (StringUtil.isNullOrEmpty(accountId)) {
            throw new IllegalArgumentException("accountId generate error");
        }
        RedisPoolManager.getInstance().executeAsync(commands ->
                commands.hset(RedisLogin.Platform_LG_Map_AccountID.getKey(business_no, threeParty), account, accountId));
        return new Account(Long.parseLong(accountId));
    }
}
