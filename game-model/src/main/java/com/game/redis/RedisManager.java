//package com.game.redis;
//
//import com.game.engine.io.conf.JedisClusterConfig;
//import com.game.enums.redis.RedisHall;
//import org.apache.commons.pool2.impl.GenericObjectPoolConfig;
//import org.slf4j.Logger;
//import org.slf4j.LoggerFactory;
//import redis.clients.jedis.HostAndPort;
//import redis.clients.jedis.JedisCluster;
//
//import java.util.HashSet;
//
///**
// * redis集群管理类
// *
// */
//public class RedisManager {
//
//    private static final Logger LOGGER = LoggerFactory.getLogger(RedisManager.class);
//    private static JedisCluster jedisCluster;
//
//    public RedisManager(JedisClusterConfig config) {
//        final HashSet<HostAndPort> jedisClusterNodes = new HashSet<>();
//        for (JedisClusterConfig.JedisClusterNodesConfig node : config.getNodes()) {
//            if (node == null) {
//                return;
//            }
//            try {
//                if (node.getIp() != null && node.getIp().length() > 5) {
//                    jedisClusterNodes.add(new HostAndPort(node.getIp(), node.getPort()));
//                }
//            } catch (Exception e) {
//                LOGGER.error(node.toString(), e);
//            }
//        }
//        GenericObjectPoolConfig poolConfig = new GenericObjectPoolConfig();
//        poolConfig.setMaxTotal(config.getPoolMaxTotal());
//        poolConfig.setMaxIdle(config.getPoolMaxIdle());
//        poolConfig.setMaxWaitMillis(config.getMaxWaitMillis());
//        poolConfig.setTimeBetweenEvictionRunsMillis(config.getTimeBetweenEvictionRunsMillis());
//        poolConfig.setMinEvictableIdleTimeMillis(config.getMinEvictableIdleTimeMillis());
//        poolConfig.setSoftMinEvictableIdleTimeMillis(config.getSoftMinEvictableIdleTimeMillis());
//        poolConfig.setTestOnBorrow(config.isTestOnBorrow());
//        poolConfig.setTestWhileIdle(config.isTestWhileIdle());
//        poolConfig.setTestOnReturn(config.isTestOnReturn());
//        jedisCluster = new JedisCluster(jedisClusterNodes, config.getConnectionTimeout(), config.getSoTimeout(), config.getMaxRedirections(), poolConfig);
//    }
//
//    private static JedisCluster getJedisCluster() {
//        return jedisCluster;
//    }
//}
