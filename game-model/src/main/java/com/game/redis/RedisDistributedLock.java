package com.game.redis;

import io.lettuce.core.api.StatefulRedisConnection;
import org.apache.commons.pool2.impl.GenericObjectPool;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class RedisDistributedLock {
    private static final Logger LOGGER = LoggerFactory.getLogger(RedisDistributedLock.class);

    public static final String LOCK_KEY = "my_lock";
    private static final int LOCK_EXPIRE = 10000; // 锁的过期时间，单位毫秒

    private GenericObjectPool<StatefulRedisConnection<String, String>> pool;

    public RedisDistributedLock(GenericObjectPool<StatefulRedisConnection<String, String>> pool) {
        this.pool = pool;
    }

    public boolean lock(String LOCK_KEY, long LOCK_EXPIRE) {
        StatefulRedisConnection<String, String> connection = null;
        try {
            // SETNX命令返回1表示成功获取锁，0表示锁已存在
            connection = pool.borrowObject();
            boolean result = connection.sync().setnx(LOCK_KEY, "locked");
            if (result) {
                // 设置锁的过期时间，防止死锁
                connection.sync().pexpire(LOCK_KEY, LOCK_EXPIRE);
                return true;
            }
            return false;
        } catch (Exception e) {
            LOGGER.error("lock", e);
        } finally {
            if (connection != null) {
                pool.returnObject(connection);
            }
        }
        return false;
    }

    public void unlock(String LOCK_KEY) {
        StatefulRedisConnection<String, String> connection = null;
        try {
            // SETNX命令返回1表示成功获取锁，0表示锁已存在
            connection = pool.borrowObject();
            connection.sync().del(LOCK_KEY); // 释放锁
        } catch (Exception e) {
            LOGGER.error("unlock", e);
        } finally {
            if (connection != null) {
                pool.returnObject(connection);
            }
        }
    }

}
