package com.game.redis;

import com.game.engine.io.redis.RedisPoolManager;
import com.game.engine.utils.UniqueIDGenerator;
import com.game.enums.redis.RedisLogin;
import io.lettuce.core.ScriptOutputType;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class RedisScriptUtils {
    private static final Logger LOGGER = LoggerFactory.getLogger(RedisScriptUtils.class);

    private static final RedisScriptUtils INSTANCE = new RedisScriptUtils();

    public static RedisScriptUtils getInstance() {
        return INSTANCE;
    }

    public String generateAccountId(UniqueIDGenerator uniqueIDGenerator) {
        try {
            final String luaScript =
                    "if redis.call('sadd', KEYS[1], ARGV[1]) == 1 then " +
                            "return ARGV[1] " +
                            "else " +
                            "return nil " +
                            "end";

            return RedisPoolManager.getInstance().function(jedis -> {
                int maxRetries = 100; // 限制最大重试次数，避免死循环
                int retryCount = 0;

                while (retryCount < maxRetries) {
                    final String accountId = uniqueIDGenerator.nextTenDigitId() + "";
                    final String result = jedis.sync().eval(luaScript,
                            ScriptOutputType.VALUE,
                            new String[]{RedisLogin.Platform_KEY_Account_UUID.getKey()},
                            new String[]{accountId});

                    if (result != null) {
                        return result; // 成功添加并返回 accountId
                    }

                    retryCount++; // 计数 +1
                    if (retryCount % 10 == 0) {
                        try {
                            Thread.sleep(1); // 每10次冲突稍微让下CPU
                        } catch (InterruptedException ie) {
                            LOGGER.error("", ie);
                        }
                    }

                    LOGGER.warn("accountId 冲突，第 {}/{} 次重试", retryCount, maxRetries);
                }

                LOGGER.error("generateAccountId 超过最大重试次数：{}", maxRetries);
                return ""; // 返回 null，外面自己判断
            });
        } catch (Exception e) {
            LOGGER.error("error generating account ID", e);
            return "";
        }
    }

    public static void main(String[] args) {
        String accountId = String.valueOf((long) (Math.random() * 9000000000L) + 1000000000L);
        System.out.println(accountId);
    }
}
