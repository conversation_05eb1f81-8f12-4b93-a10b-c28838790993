package com.game.redis;

import com.game.engine.io.redis.RedisPoolManager;
import com.game.enums.redis.RedisHall;
import com.game.utils.VirtualThreadUtils;
import io.netty.util.internal.StringUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 *
 */
public class RedisUtils {

    public static final String[] DEFAULTFIELD = {"name", "serverId", "vip", "job"};

    private static final Logger LOGGER = LoggerFactory.getLogger(RedisUtils.class);
    private static RedisUtils INSTANCE = new RedisUtils();

    public static RedisUtils getInstance() {
        return INSTANCE;
    }

    public static double convert(String value) {
        if (StringUtil.isNullOrEmpty(value)) {
            return 0;
        }
        return Double.parseDouble(value);
    }

    public static String getPlayerInfo(long playerId, String playerFields) {
        final String value = RedisPoolManager.getInstance().function(jedis ->
                jedis.sync().hget(RedisHall.Platform_Role_Map_PlayerInfo.getKey(playerId), playerFields));
        return StringUtil.isNullOrEmpty(value) ? "" : value;
    }

    public static void saveToRedis(long playerId, String playerFields, String value) {
        RedisPoolManager.getInstance().executeAsync(commands ->
                commands.hset(RedisHall.Platform_Role_Map_PlayerInfo.getKey(playerId), playerFields, value)
        );
    }
}
