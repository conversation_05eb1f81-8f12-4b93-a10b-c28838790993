package com.game.dao.quest;

import com.game.engine.mongo.*;
import com.game.engine.util.misc.Tuple2;
import com.game.entity.player.quest.QuestNote;
import com.game.entity.player.quest.QuestNoteFields;
import com.game.utils.VirtualThreadUtils;
import com.mongodb.client.MongoCollection;
import com.mongodb.client.model.Filters;
import com.mongodb.client.model.Updates;
import org.bson.Document;
import org.bson.conversions.Bson;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;

import java.util.List;

public class QuestNoteDao implements EntityDao<QuestNote> {

    private final MongoCollection<QuestNote> collection;
    private final MongoTemplate ops;

    public QuestNoteDao(DBConnectionMrg dbConnectionMrg) {
        this.ops = dbConnectionMrg.getMongoTemplate();
        this.collection = dbConnectionMrg.getCollection("quest_note", entityClass());
        final IndexDescAll indexDescAll = IndexDescAll.createWithAllIndexs(
                IndexDescSingle.createIndex(new IndexTypeAndName(Long.class, QuestNoteFields.playerId)),
                IndexDescSingle.createIndex(new IndexTypeAndName(Long.class, QuestNoteFields.uniqueId)),
                IndexDescSingle.createIndex(new IndexTypeAndName(Integer.class, QuestNoteFields.state)),
                IndexDescSingle.createIndex(new IndexTypeAndName(Long.class, QuestNoteFields.finishedTime))
        );
        final MongoCollection<Document> mongoCollection = dbConnectionMrg.getMongoTemplate().getCollection("quest_note");
        ToolMongoIndexHelper.indexBuildWork(indexDescAll.getApplicationIndex(), null, mongoCollection);
    }

    @Override
    public Class<QuestNote> entityClass() {
        return QuestNote.class;
    }

    public void insert(QuestNote questNote) {
        VirtualThreadUtils.execute(() -> collection.insertOne(questNote));
    }

    public Tuple2<Integer, List<QuestNote>> loadQuestNote(long playerId, long start, long end, int skip, int limit) {
        final Query query = new Query();
        query.addCriteria(Criteria.where(QuestNoteFields.playerId).is(playerId));
        if (start > 0 && end > 0) {
            query.addCriteria(Criteria.where(QuestNoteFields.finishedTime).gte(start).lt(end));
        }

        final int total = (int) ops.count(query, QuestNote.class);
        final List<QuestNote> questNotes = ops.find(query.with(Sort.by(Sort.Direction.ASC, QuestNoteFields.state))
                .with(Sort.by(Sort.Direction.DESC, QuestNoteFields.finishedTime))
                .skip(skip).limit(limit), QuestNote.class);
        return new Tuple2<>(total, questNotes);
    }


    public QuestNote loadQuestNote(long playerId, long uniqueId) {
        final Query query = new Query();
        query.addCriteria(Criteria.where(QuestNoteFields.playerId).is(playerId)
                .and(QuestNoteFields.uniqueId).is(uniqueId));

        return ops.findOne(query, QuestNote.class);
    }

    public void updateQuestState(long playerId, long uniqueId, int state) {
        VirtualThreadUtils.execute(() -> {
            final Bson bson = Filters.and(Filters.eq(QuestNoteFields.playerId, playerId),
                    Filters.eq(QuestNoteFields.uniqueId, uniqueId));

            collection.updateOne(bson, Updates.set(QuestNoteFields.state, state));
        });
    }

    public long delete(long end) {
        final Criteria criteria = new Criteria();
        criteria.and(QuestNoteFields.finishedTime).lt(end);

        int BATCH_SIZE = 1000;
        long totalDeleted = 0;
        while (true) {
            // 查询一批数据
            final Query query = new Query(criteria).limit(BATCH_SIZE);

            // 批量删除
            final long deletedCount = ops.remove(query, QuestNote.class).getDeletedCount();
            totalDeleted += deletedCount;

            if (deletedCount < BATCH_SIZE) {
                break;
            }
        }
        return totalDeleted;
    }
}
