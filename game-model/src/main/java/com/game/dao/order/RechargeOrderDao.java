package com.game.dao.order;

import com.game.engine.mongo.*;
import com.game.engine.util.misc.Tuple2;
import com.game.entity.order.RechargeOrder;
import com.game.entity.order.RechargeOrderFields;
import com.game.entity.player.Player;
import com.game.utils.VirtualThreadUtils;
import com.mongodb.client.MongoCollection;
import com.mongodb.client.model.Accumulators;
import com.mongodb.client.model.Aggregates;
import com.mongodb.client.model.Filters;
import org.bson.Document;
import org.bson.conversions.Bson;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;

import java.util.Arrays;
import java.util.List;

public class RechargeOrderDao implements EntityDao<RechargeOrder> {

    private final MongoCollection<RechargeOrder> collection;
    private final MongoTemplate ops;

    public RechargeOrderDao(DBConnectionMrg dbConnectionMrg) {
        this.ops = dbConnectionMrg.getMongoTemplate();
        this.collection = dbConnectionMrg.getCollection("recharge_order", entityClass());
        final IndexDescAll indexDescAll = IndexDescAll.createWithAllIndexs(
                IndexDescSingle.createIndex(new IndexTypeAndName(Long.class, RechargeOrderFields.orderId)),

                IndexDescSingle.createIndex(
                        new IndexTypeAndName(Long.class, RechargeOrderFields.playerId),
                        new IndexTypeAndName(Long.class, RechargeOrderFields.createTime)
                ),

                IndexDescSingle.createIndex(
                        new IndexTypeAndName(Long.class, RechargeOrderFields.playerId),
                        new IndexTypeAndName(Integer.class, RechargeOrderFields.currencyId),
                        new IndexTypeAndName(Integer.class, RechargeOrderFields.status),
                        new IndexTypeAndName(Long.class, RechargeOrderFields.createTime)
                )
        );
        final MongoCollection<Document> mongoCollection = dbConnectionMrg.getMongoTemplate().getCollection("recharge_order");
        ToolMongoIndexHelper.indexBuildWork(indexDescAll.getApplicationIndex(), null, mongoCollection);
    }

    @Override
    public Class<RechargeOrder> entityClass() {
        return RechargeOrder.class;
    }


    public void insert(RechargeOrder order) {
        if (order.getOrderId() <= 0) {
            throw new IllegalArgumentException("order.getOrderId() <= 0");
        }
        VirtualThreadUtils.execute(() -> collection.insertOne(order));
    }

    public void updateOrder(long orderId, Update update) {
        VirtualThreadUtils.execute(() ->
                collection.updateOne(Filters.eq(RechargeOrderFields.orderId, orderId), update.getUpdateObject()));
    }

    public RechargeOrder findRechargeOrder(long orderId) {
        return collection.find(Filters.eq(RechargeOrderFields.orderId, orderId)).first();
    }

    public Tuple2<Integer, List<RechargeOrder>> loadRechargeOrder(Player player, int transactionType, int assets, long start, long end, int status, int skip, int limit) {
        final Query query = new Query();
        query.addCriteria(Criteria.where(RechargeOrderFields.playerId).is(player.getPlayerId()));
        if (transactionType > 0) {
            query.addCriteria(Criteria.where(RechargeOrderFields.type).is(transactionType));
        }
        if (assets > 0) {
            query.addCriteria(Criteria.where(RechargeOrderFields.currencyId).is(assets));
        }
        if (status > 0) {
            query.addCriteria(Criteria.where(RechargeOrderFields.status).is(status));
        }
        query.addCriteria(Criteria.where(RechargeOrderFields.createTime).gte(start).lt(end));

        final int count = (int) ops.count(query.with(Sort.by(Sort.Direction.DESC, RechargeOrderFields.createTime)), RechargeOrder.class);

        final List<RechargeOrder> rechargeOrderList = ops.find(query.with(Sort.by(Sort.Direction.DESC, RechargeOrderFields.createTime)).skip(skip).limit(limit), RechargeOrder.class);

        return new Tuple2<>(count, rechargeOrderList);
    }

    public RechargeOrder aggregateRechargeAmount(long playerId, int currencyId, String amounts, long wagerStart, long wagerEnd) {
        final Bson query = Filters.and(
                Filters.eq(RechargeOrderFields.playerId, playerId),
                Filters.eq(RechargeOrderFields.currencyId, currencyId),
                Filters.eq(RechargeOrderFields.status, 1),
                Filters.gte(RechargeOrderFields.createTime, wagerStart),
                Filters.lt(RechargeOrderFields.createTime, wagerEnd)
        );

        final RechargeOrder aggregate = collection.aggregate(Arrays.asList(
                Aggregates.match(query),
                Aggregates.group(null, Accumulators.sum(amounts, "$" + amounts))
        )).first();

        final RechargeOrder rechargeOrder = new RechargeOrder();
        if (aggregate != null) {
            rechargeOrder.setAmounts(aggregate.getAmounts());
        }
        return rechargeOrder;
    }

    /**
     * 90 day
     *
     * @param end
     * @return
     */
    public long delete(long end) {
        final Criteria criteria = new Criteria();
        criteria.and(RechargeOrderFields.createTime).lt(end);

        int BATCH_SIZE = 1000;
        long totalDeleted = 0;
        while (true) {
            // 查询一批数据
            final Query query = new Query(criteria).limit(BATCH_SIZE);

            // 批量删除
            final long deletedCount = ops.remove(query, RechargeOrder.class).getDeletedCount();
            totalDeleted += deletedCount;

            if (deletedCount < BATCH_SIZE) {
                break;
            }
        }
        return totalDeleted;
    }
}
