package com.game.dao.order;

import com.game.engine.mongo.*;
import com.game.engine.util.misc.Tuple2;
import com.game.entity.order.WithdrawOrderFields;
import com.game.entity.order.WithdrawOrder;
import com.game.entity.player.Player;
import com.game.utils.VirtualThreadUtils;
import com.mongodb.client.MongoCollection;
import com.mongodb.client.model.Filters;
import org.bson.Document;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;

import java.util.List;

public class WithdrawOrderDao implements EntityDao<WithdrawOrder> {

    private final MongoCollection<WithdrawOrder> collection;
    private final MongoTemplate ops;

    public WithdrawOrderDao(DBConnectionMrg dbConnectionMrg) {
        this.ops = dbConnectionMrg.getMongoTemplate();
        this.collection = dbConnectionMrg.getCollection("withdraw_order", entityClass());
        final IndexDescAll indexDescAll = IndexDescAll.createWithAllIndexs(
                IndexDescSingle.createIndex(new IndexTypeAndName(Long.class, WithdrawOrderFields.orderId)),
                IndexDescSingle.createIndex(new IndexTypeAndName(Long.class, WithdrawOrderFields.createTime))
        );
        final MongoCollection<Document> mongoCollection = dbConnectionMrg.getMongoTemplate().getCollection("withdraw_order");
        ToolMongoIndexHelper.indexBuildWork(indexDescAll.getApplicationIndex(), null, mongoCollection);
    }

    @Override
    public Class<WithdrawOrder> entityClass() {
        return WithdrawOrder.class;
    }


    public void insert(WithdrawOrder order) {
        if (order.getOrderId() <= 0) {
            throw new IllegalArgumentException("order.getOrderId() <= 0");
        }
        VirtualThreadUtils.execute(() -> collection.insertOne(order));
    }

    public void updateOrder(long orderId, Update update) {
        VirtualThreadUtils.execute(() -> collection.updateOne(Filters.eq(WithdrawOrderFields.orderId, orderId), update.getUpdateObject()));
    }

    public WithdrawOrder findWithdrawOrder(long orderId) {
        return collection.find(Filters.eq(WithdrawOrderFields.orderId, orderId)).first();
    }

    public Tuple2<Integer, List<WithdrawOrder>> loadWithdrawOrder(Player player, int transactionType, int assets, long start, long end, int status, int skip, int limit) {
        final Query query = new Query();
        query.addCriteria(Criteria.where(WithdrawOrderFields.playerId).is(player.getPlayerId()));
        if (transactionType > 0) {
            query.addCriteria(Criteria.where(WithdrawOrderFields.type).is(transactionType));
        }
        if (assets > 0) {
            query.addCriteria(Criteria.where(WithdrawOrderFields.currencyId).is(assets));
        }
        if (status > 0) {
            query.addCriteria(Criteria.where(WithdrawOrderFields.status).is(status));
        }
        query.addCriteria(Criteria.where(WithdrawOrderFields.createTime).gte(start).lt(end));

        final int count = (int) ops.count(query.with(Sort.by(Sort.Direction.DESC, WithdrawOrderFields.createTime)), WithdrawOrder.class);

        final List<WithdrawOrder> withdrawOrderList = ops.find(query.with(Sort.by(Sort.Direction.DESC, WithdrawOrderFields.createTime))
                .skip(skip).limit(limit), WithdrawOrder.class);
        return new Tuple2<>(count, withdrawOrderList);
    }

    /**
     * 90 day
     *
     * @param end
     * @return
     */
    public long delete(long end) {
        final Criteria criteria = new Criteria();
        criteria.and(WithdrawOrderFields.createTime).lt(end);

        int BATCH_SIZE = 1000;
        long totalDeleted = 0;
        while (true) {
            // 查询一批数据
            final Query query = new Query(criteria).limit(BATCH_SIZE);

            // 批量删除
            final long deletedCount = ops.remove(query, WithdrawOrder.class).getDeletedCount();
            totalDeleted += deletedCount;

            if (deletedCount < BATCH_SIZE) {
                break;
            }
        }
        return totalDeleted;
    }
}
