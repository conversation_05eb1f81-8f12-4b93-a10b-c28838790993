package com.game.dao.account;

import com.game.engine.mongo.*;
import com.game.entity.account.*;
import com.game.entity.account.email.Email;
import com.game.entity.account.email.EmailFields;
import com.game.entity.account.phone.Phone;
import com.game.entity.account.phone.PhoneFields;
import com.mongodb.client.MongoCollection;
import com.mongodb.client.model.Filters;
import com.mongodb.client.model.Updates;
import it.unimi.dsi.fastutil.ints.IntList;
import it.unimi.dsi.fastutil.ints.IntLists;
import org.bson.BsonValue;
import org.bson.Document;
import org.bson.conversions.Bson;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;

public class AccountDao implements EntityDao<Account> {

    private final MongoCollection<Account> collection;
    private final MongoTemplate ops;

    public AccountDao(DBConnectionMrg dbConnectionMrg) {
        this.ops = dbConnectionMrg.getMongoTemplate();
        this.collection = dbConnectionMrg.getCollection("account", entityClass());
        final IndexDescAll indexDescAll = IndexDescAll.createWithAllIndexs(
                IndexDescSingle.createIndex(new IndexTypeAndName(Long.class, AccountFields.accountId))
        );
        final MongoCollection<Document> mongoCollection = dbConnectionMrg.getMongoTemplate().getCollection("account");
        ToolMongoIndexHelper.indexBuildWork(indexDescAll.getApplicationIndex(), null, mongoCollection);
    }

    @Override
    public Class<Account> entityClass() {
        return Account.class;
    }


    public void insert(Account account) {
        if (account.getAccountId() <= 0) {
            throw new IllegalArgumentException("account.getAccountId() <= 0");
        }
        collection.insertOne(account);
    }

    public Account getById(long accountId) {
        final Query query = new Query();
        query.addCriteria(Criteria.where(AccountFields.accountId).is(accountId));
        return ops.findOne(query, Account.class);
    }

    public void updateDailyLoginTimes(Account account, int dailyLoginTimes) {
        final Bson bson = Filters.and(Filters.eq(AccountFields.accountId, account.getAccountId()));
        collection.updateOne(bson, Updates.set(AccountFields.dailyLoginTimes, dailyLoginTimes));
    }

    public void incLoginTimes(Account account) {
        final Bson bson = Filters.and(Filters.eq(AccountFields.accountId, account.getAccountId()));
        collection.updateOne(bson, Updates.inc(AccountFields.loginTimes, 1));
    }

    public void incDailyLoginTimes(Account account) {
        final Bson bson = Filters.and(Filters.eq(AccountFields.accountId, account.getAccountId()));
        collection.updateOne(bson, Updates.inc(AccountFields.dailyLoginTimes, 1));
    }

    public void updateAccount(long accountId, Update update) {
        final Bson bson = Filters.and(Filters.eq(AccountFields.accountId, accountId));
        collection.updateOne(bson, update.getUpdateObject());
    }

    public void updateAccountField(long accountId, String name, Object value) {
        final Update update = new Update();
        update.set(name, value);
        final Bson bson = Filters.and(Filters.eq(AccountFields.accountId, accountId));
        collection.updateOne(bson, update.getUpdateObject());
    }

    public void updateMail2LField(long accountId, String fieldName, Object value) {
        final DBPrefixBuilder dbPrefixBuilder = new DBPrefixBuilder();
        dbPrefixBuilder.append(AccountFields.emailInfo);

        final Update update = new Update();
        update.set(dbPrefixBuilder.buildAndRewind(fieldName), value);

        final Bson bson = Filters.and(Filters.eq(AccountFields.accountId, accountId));
        collection.updateOne(bson, update.getUpdateObject());
    }

    public void updatePhone2LField(long accountId, String fieldName, Object value) {
        final DBPrefixBuilder dbPrefixBuilder = new DBPrefixBuilder();
        dbPrefixBuilder.append(AccountFields.phoneInfo);

        final Update update = new Update();
        update.set(dbPrefixBuilder.buildAndRewind(fieldName), value);

        final Bson bson = Filters.and(Filters.eq(AccountFields.accountId, accountId));
        collection.updateOne(bson, update.getUpdateObject());
    }

    public void updateInsertThreePartyInfo(Account account, IntList insertedMail) {
        updateThreePartyInfo(account, insertedMail, IntLists.EMPTY_LIST);
    }

    public void updateDeleteThreePartyInfo(Account account, IntList deleteMail) {
        updateThreePartyInfo(account, IntLists.EMPTY_LIST, deleteMail);
    }

    private void updateThreePartyInfo(Account account, IntList insertedThreeParty, IntList deleteThreeParty) {
        if (insertedThreeParty.isEmpty() && deleteThreeParty.isEmpty()) {
            return;
        }

        final DBPrefixBuilder dbPrefixBuilder = new DBPrefixBuilder();
        dbPrefixBuilder.append(AccountFields.threePartyInfoMap)
                .build();

        Update update = new Update();
        //添加
        for (int i = 0; i < insertedThreeParty.size(); i++) {
            final int updateThreePartyId = insertedThreeParty.getInt(i);
            ThreePartyInfo threePartyInfo = account.getThreePartyInfoMap().get(updateThreePartyId);
            if (threePartyInfo == null) {
                throw new IllegalArgumentException("threePartyInfo does not exist, Guid: " + updateThreePartyId);
            }
            BsonValue document = DBConnectionMrg.getInstance().getConvertMrg().writeNoTypeKey(threePartyInfo);
            update.set(dbPrefixBuilder.buildWithIntAndRewind(updateThreePartyId), document);
        }

        //删除
        for (int i = 0; i < deleteThreeParty.size(); i++) {
            final int deleteThreePartyId = deleteThreeParty.getInt(i);
            if (account.getThreePartyInfoMap().containsKey(deleteThreePartyId)) {
                throw new IllegalArgumentException("threePartyInfo is not deleted, Guid: " + deleteThreePartyId);
            }
            update.unset(dbPrefixBuilder.buildWithIntAndRewind(deleteThreePartyId));
        }

        final Bson bson = Filters.and(Filters.eq(AccountFields.accountId, account.getAccountId()));
        collection.updateOne(bson, update.getUpdateObject());
    }


    public void updateEmail(long accountId, Email mailInfo) {
        final DBPrefixBuilder dbPrefixBuilder = new DBPrefixBuilder();
        dbPrefixBuilder.append(AccountFields.emailInfo);

        final Update update = new Update();
        update.set(dbPrefixBuilder.buildAndRewind(EmailFields.email), mailInfo.getEmail());

        final Bson bson = Filters.and(Filters.eq(AccountFields.accountId, accountId));
        collection.updateOne(bson, update.getUpdateObject());
    }

    public void updatePhone(long accountId, Phone phoneInfo) {
        final DBPrefixBuilder dbPrefixBuilder = new DBPrefixBuilder();
        dbPrefixBuilder.append(AccountFields.phoneInfo);

        final Update update = new Update();
        update.set(dbPrefixBuilder.buildAndRewind(PhoneFields.areaCode), phoneInfo.getAreaCode())
                .set(dbPrefixBuilder.buildAndRewind(PhoneFields.phone), phoneInfo.getPhone());

        final Bson bson = Filters.and(Filters.eq(AccountFields.accountId, accountId));
        collection.updateOne(bson, update.getUpdateObject());
    }

}
