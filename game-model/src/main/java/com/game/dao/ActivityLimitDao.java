package com.game.dao;

import com.game.engine.mongo.*;
import com.game.engine.utils.TimeUtil;
import com.game.entity.ActivityLimit;
import com.game.entity.ActivityLimitFields;
import com.game.utils.VirtualThreadUtils;
import com.mongodb.client.MongoCollection;
import com.mongodb.client.model.Filters;
import com.mongodb.client.model.Updates;
import org.bson.Document;

public class ActivityLimitDao implements EntityDao<ActivityLimit> {
    private final MongoCollection<ActivityLimit> collection;

    public ActivityLimitDao(DBConnectionMrg dbConnectionMrg) {
        this.collection = dbConnectionMrg.getCollection("activityLimit", entityClass());
        final IndexDescAll indexDescAll = IndexDescAll.createWithAllIndexs(
                IndexDescSingle.createIndex(new IndexTypeAndName(String.class, ActivityLimitFields.business_no)),
                IndexDescSingle.createIndex(new IndexTypeAndName(String.class, ActivityLimitFields.activityId)),
                IndexDescSingle.createIndex(new IndexTypeAndName(String.class, ActivityLimitFields.uniqueId)),
                IndexDescSingle.createIndex(new IndexTypeAndName(String.class, ActivityLimitFields.ipAddress))
        );
        final MongoCollection<Document> mongoCollection = dbConnectionMrg.getMongoTemplate().getCollection("registerLimit");
        ToolMongoIndexHelper.indexBuildWork(indexDescAll.getApplicationIndex(), null, mongoCollection);
    }

    @Override
    public Class<ActivityLimit> entityClass() {
        return ActivityLimit.class;
    }

    public void insert(ActivityLimit activityLimit) {
        VirtualThreadUtils.execute(() -> collection.insertOne(activityLimit));
    }

    public ActivityLimit findByIp(String business_no, String ip, int activityId, int uniqueId) {
        return collection.find(Filters.and(
                Filters.eq(ActivityLimitFields.business_no, business_no),
                Filters.eq(ActivityLimitFields.ipAddress, ip),
                Filters.eq(ActivityLimitFields.activityId, activityId),
                Filters.eq(ActivityLimitFields.uniqueId, uniqueId)
        )).first();
    }

    public void updateActivityLimit(String business_no, String ip, int activityId, int uniqueId) {
        VirtualThreadUtils.execute(() -> collection.updateOne(
                Filters.and(
                        Filters.eq(ActivityLimitFields.business_no, business_no),
                        Filters.eq(ActivityLimitFields.activityId, activityId),
                        Filters.eq(ActivityLimitFields.uniqueId, uniqueId),
                        Filters.eq(ActivityLimitFields.ipAddress, ip)
                ),
                Updates.combine(
                        Updates.inc(ActivityLimitFields.count, 1),
                        Updates.set(ActivityLimitFields.lastTime, TimeUtil.currentTimeMillis())
                )));
    }
}
