package com.game.dao.game;

import com.game.engine.mongo.*;
import com.game.engine.util.misc.Tuple2;
import com.game.entity.game.GameNote;
import com.game.entity.game.GameNoteFields;
import com.game.entity.game.GameWagerStat;
import com.game.entity.player.Player;
import com.game.enums.GameType;
import com.game.utils.VirtualThreadUtils;
import com.mongodb.client.MongoCollection;
import com.mongodb.client.model.Accumulators;
import com.mongodb.client.model.Aggregates;
import com.mongodb.client.model.Filters;
import org.bson.Document;
import org.bson.conversions.Bson;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;

import java.util.*;

public class GameNoteDao implements EntityDao<GameNote> {

    private final MongoCollection<GameNote> collection;
    private final MongoTemplate ops;

    public GameNoteDao(DBConnectionMrg dbConnectionMrg) {
        this.ops = dbConnectionMrg.getMongoTemplate();
        this.collection = dbConnectionMrg.getCollection("game_note", entityClass());
        final IndexDescAll indexDescAll = IndexDescAll.createWithAllIndexs(
                IndexDescSingle.createIndex(
                        new IndexTypeAndName(Long.class, GameNoteFields.playerId),
                        new IndexTypeAndName(String.class, GameNoteFields.roundId)
                ),

                IndexDescSingle.createIndex(
                        new IndexTypeAndName(Long.class, GameNoteFields.playerId),
                        new IndexTypeAndName(Long.class, GameNoteFields.noteId)
                ),

                IndexDescSingle.createIndex(
                        new IndexTypeAndName(Long.class, GameNoteFields.playerId),
                        new IndexTypeAndName(Integer.class, GameNoteFields.currencyId),
                        new IndexTypeAndName(Long.class, GameNoteFields.createTime)
                ),

                IndexDescSingle.createIndex(
                        new IndexTypeAndName(Long.class, GameNoteFields.playerId),
                        new IndexTypeAndName(Long.class, GameNoteFields.createTime),
                        new IndexTypeAndName(Integer.class, GameNoteFields.gameId),
                        new IndexTypeAndName(Integer.class, GameNoteFields.gameType),
                        new IndexTypeAndName(Integer.class, GameNoteFields.platformId),
                        new IndexTypeAndName(Integer.class, GameNoteFields.currencyId)
                ),

                IndexDescSingle.createIndex(
                        new IndexTypeAndName(String.class, GameNoteFields.business_no),
                        new IndexTypeAndName(Long.class, GameNoteFields.createTime)
                ),

                IndexDescSingle.createIndex(
                        new IndexTypeAndName(String.class, GameNoteFields.business_no),
                        new IndexTypeAndName(Double.class, GameNoteFields.usdValidBets),
                        new IndexTypeAndName(Double.class, GameNoteFields.win)
                ),

                IndexDescSingle.createIndex(
                        new IndexTypeAndName(Set.class, GameNoteFields.transactionIds)
                )
        );
        final MongoCollection<Document> mongoCollection = dbConnectionMrg.getMongoTemplate().getCollection("game_note");
        ToolMongoIndexHelper.indexBuildWork(indexDescAll.getApplicationIndex(), null, mongoCollection);
    }

    @Override
    public Class<GameNote> entityClass() {
        return GameNote.class;
    }

    public void insert(GameNote gameNote) {
        if (gameNote.getNoteId() <= 0) {
            throw new IllegalArgumentException("gameNote.getNoteId() <= 0");
        }
        VirtualThreadUtils.execute(() -> collection.insertOne(gameNote));
    }

    public GameNote findByRoundIdGameNote(long playerId, String roundId) {
        final Bson query = Filters.and(Filters.eq(GameNoteFields.playerId, playerId),
                Filters.eq(GameNoteFields.roundId, roundId));
        return collection.find(query).first();
    }

    public void updateByRoundIdFiled(String roundId, Update update) {
        VirtualThreadUtils.execute(() ->
                collection.updateOne(Filters.eq(GameNoteFields.roundId, roundId), update.getUpdateObject()));
    }

    public List<GameNote> findByNoteIdList(long playerId, Set<Long> noteIds) {
        final Bson bson = Filters.and(Filters.eq(GameNoteFields.playerId, playerId),
                Filters.in(GameNoteFields.noteId, noteIds));
        return collection.find(bson).into(new ArrayList<>());
    }

    public Tuple2<Integer, List<GameNote>> loadHistoryGameNote(Player player, int gameId, int gameType, int platformId, int assets, long start, long end, int skip, int limit) {
        final Query query = new Query();
        query.addCriteria(Criteria.where(GameNoteFields.playerId).is(player.getPlayerId()))
                .addCriteria(Criteria.where(GameNoteFields.createTime).gte(start).lt(end));
        if (gameId > 0) {
            query.addCriteria(Criteria.where(GameNoteFields.gameId).is(gameId));
        }
        if (gameType > 0) {
            query.addCriteria(Criteria.where(GameNoteFields.gameType).is(gameType));
        }
        if (platformId > 0) {
            query.addCriteria(Criteria.where(GameNoteFields.platformId).is(platformId));
        }
        if (assets > 0) {
            query.addCriteria(Criteria.where(GameNoteFields.currencyId).is(assets));
        }

        final int count = (int) ops.count(query, GameNote.class);
        final List<GameNote> gameNoteList = ops.find(query.with(Sort.by(Sort.Direction.DESC, GameNoteFields.createTime))
                .skip(skip).limit(limit), GameNote.class);
        return new Tuple2<>(count, gameNoteList);
    }

    public Tuple2<Integer, List<GameNote>> loadTransactionGameNote(Player player, int status, int assets, long start, long end, int skip, int limit) {
        final Query query = new Query();
        query.addCriteria(Criteria.where(GameNoteFields.playerId).is(player.getPlayerId()))
                .addCriteria(Criteria.where(GameNoteFields.createTime).gte(start).lt(end));
        if (status > 0) {
            query.addCriteria(Criteria.where(GameNoteFields.status).is(status));
        }
        if (assets > 0) {
            query.addCriteria(Criteria.where(GameNoteFields.currencyId).is(assets));
        }

        final int count = (int) ops.count(query, GameNote.class);
        final List<GameNote> gameNoteList = ops.find(query.with(Sort.by(Sort.Direction.DESC, GameNoteFields.createTime))
                .skip(skip).limit(limit), GameNote.class);
        return new Tuple2<>(count, gameNoteList);
    }

    public List<GameNote> loadBigWinGameNote(String business_no, int gameType, long start, long end, int limit) {
        final Query query = new Query();
        query.addCriteria(Criteria.where(GameNoteFields.business_no).is(business_no)
                .and(GameNoteFields.win).gt(0));
        if (gameType > 0) {
            query.addCriteria(Criteria.where(GameNoteFields.gameType).is(gameType));
        }
        if (start > 0 && end > 0) {
            query.addCriteria(Criteria.where(GameNoteFields.createTime).gte(start).lt(end));
        }

        return ops.find(query.with(Sort.by(Sort.Direction.DESC, GameNoteFields.createTime))
                .limit(limit), GameNote.class);
    }

    public List<GameNote> loadGameNote(String business_no, double usdValidBets, long start, long end, int limit) {
        final Query query = new Query();
        query.addCriteria(Criteria.where(GameNoteFields.business_no).is(business_no)
                .and(GameNoteFields.betAmount).gt(0));
        if (usdValidBets > 0) {
            query.addCriteria(Criteria.where(GameNoteFields.usdValidBets).gte(usdValidBets)
                    .and(GameNoteFields.win).gt(0));
        }
        if (start > 0 && end > 0) {
            query.addCriteria(Criteria.where(GameNoteFields.createTime).gte(start).lt(end));
        }
        return ops.find(query.with(Sort.by(Sort.Direction.DESC, GameNoteFields.createTime))
                .limit(limit), GameNote.class);
    }

    public GameWagerStat aggregateGameWagerStat(List<Long> playerId, int currencyId, String validBets, long wagerStart, long wagerEnd) {
        final List<Bson> filterList = new ArrayList<>();
        filterList.add(Filters.in(GameNoteFields.playerId, playerId));
        if (currencyId > 0) {
            filterList.add(Filters.eq(GameNoteFields.currencyId, currencyId));
        }
        filterList.add(Filters.gte(GameNoteFields.createTime, wagerStart));
        filterList.add(Filters.lt(GameNoteFields.createTime, wagerEnd));

        final Bson query = Filters.and(filterList);

        final GameNote wagerAmount = collection.aggregate(Arrays.asList(
                Aggregates.match(query),
                Aggregates.group(null, Accumulators.sum(validBets, "$" + validBets))
        )).first();

        final Bson original = Filters.and(query,
                Filters.eq(GameNoteFields.gameType, GameType.Casino_Original.getType()));
        final GameNote wagerOriginal = collection.aggregate(Arrays.asList(
                Aggregates.match(original),
                Aggregates.group(null, Accumulators.sum(validBets, "$" + validBets))
        )).first();

        final Bson slots = Filters.and(query,
                Filters.in(GameNoteFields.gameType, GameType.Casino_Slots.getType()));
        final GameNote wagerSlots = collection.aggregate(Arrays.asList(
                Aggregates.match(slots),
                Aggregates.group(null, Accumulators.sum(validBets, "$" + validBets))
        )).first();

        final Bson live = Filters.and(query,
                Filters.in(GameNoteFields.gameType, GameType.Casino_Live.getType()));
        final GameNote wagerLive = collection.aggregate(Arrays.asList(
                Aggregates.match(live),
                Aggregates.group(null, Accumulators.sum(validBets, "$" + validBets))
        )).first();

        final Bson poker = Filters.and(query,
                Filters.in(GameNoteFields.gameType, GameType.Casino_Poker.getType()));
        final GameNote wagerPoker = collection.aggregate(Arrays.asList(
                Aggregates.match(poker),
                Aggregates.group(null, Accumulators.sum(validBets, "$" + validBets))
        )).first();

        final Bson fish = Filters.and(query,
                Filters.in(GameNoteFields.gameType, GameType.Casino_Fish.getType()));
        final GameNote wagerFish = collection.aggregate(Arrays.asList(
                Aggregates.match(fish),
                Aggregates.group(null, Accumulators.sum(validBets, "$" + validBets))
        )).first();

        final Bson arcade = Filters.and(query,
                Filters.in(GameNoteFields.gameType, GameType.Casino_Arcade.getType()));
        final GameNote wagerArcade = collection.aggregate(Arrays.asList(
                Aggregates.match(arcade),
                Aggregates.group(null, Accumulators.sum(validBets, "$" + validBets))
        )).first();

        final Bson bingo = Filters.and(query,
                Filters.in(GameNoteFields.gameType, GameType.Casino_Bingo.getType()));
        final GameNote wagerBingo = collection.aggregate(Arrays.asList(
                Aggregates.match(bingo),
                Aggregates.group(null, Accumulators.sum(validBets, "$" + validBets))
        )).first();


        final Bson sport = Filters.and(query,
                Filters.eq(GameNoteFields.gameType, GameType.Sport.getType()));
        final GameNote wagerSports = collection.aggregate(Arrays.asList(
                Aggregates.match(sport),
                Aggregates.group(null, Accumulators.sum(validBets, "$" + validBets))
        )).first();

        final GameWagerStat wagerStat = new GameWagerStat();
        if (wagerAmount != null) {
            if (Objects.equals(validBets, GameNoteFields.usdValidBets)) {
                wagerStat.setTotalWager(wagerAmount.getUsdValidBets());
            } else {
                wagerStat.setTotalWager(wagerAmount.getValidBets());
            }
        }

        if (wagerOriginal != null) {
            if (Objects.equals(validBets, GameNoteFields.usdValidBets)) {
                wagerStat.setTotalWagerOriginal(wagerOriginal.getUsdValidBets());
            } else {
                wagerStat.setTotalWagerOriginal(wagerOriginal.getValidBets());
            }
        }

        if (wagerSlots != null) {
            if (Objects.equals(validBets, GameNoteFields.usdValidBets)) {
                wagerStat.setTotalWagerSlots(wagerSlots.getUsdValidBets());
            } else {
                wagerStat.setTotalWagerSlots(wagerSlots.getValidBets());
            }
        }

        if (wagerLive != null) {
            if (Objects.equals(validBets, GameNoteFields.usdValidBets)) {
                wagerStat.setTotalWagerLive(wagerLive.getUsdValidBets());
            } else {
                wagerStat.setTotalWagerLive(wagerLive.getValidBets());
            }
        }

        if (wagerPoker != null) {
            if (Objects.equals(validBets, GameNoteFields.usdValidBets)) {
                wagerStat.setTotalWagerPoker(wagerPoker.getUsdValidBets());
            } else {
                wagerStat.setTotalWagerPoker(wagerPoker.getValidBets());
            }
        }

        if (wagerFish != null) {
            if (Objects.equals(validBets, GameNoteFields.usdValidBets)) {
                wagerStat.setTotalWagerFish(wagerFish.getUsdValidBets());
            } else {
                wagerStat.setTotalWagerFish(wagerFish.getValidBets());
            }
        }

        if (wagerArcade != null) {
            if (Objects.equals(validBets, GameNoteFields.usdValidBets)) {
                wagerStat.setTotalWagerArcade(wagerArcade.getUsdValidBets());
            } else {
                wagerStat.setTotalWagerArcade(wagerArcade.getValidBets());
            }
        }

        if (wagerBingo != null) {
            if (Objects.equals(validBets, GameNoteFields.usdValidBets)) {
                wagerStat.setTotalWagerBingo(wagerBingo.getUsdValidBets());
            } else {
                wagerStat.setTotalWagerBingo(wagerBingo.getValidBets());
            }
        }

        if (wagerSports != null) {
            if (Objects.equals(validBets, GameNoteFields.usdValidBets)) {
                wagerStat.setTotalWagerSports(wagerSports.getUsdValidBets());
            } else {
                wagerStat.setTotalWagerSports(wagerSports.getValidBets());
            }
        }
        return wagerStat;
    }

//    public GameWagerStat aggregateActivePlayerAndWagered(List<Long> playerId, int currencyId, long wagerStart, long wagerEnd) {
//        final List<Bson> filterList = new ArrayList<>();
//        filterList.add(Filters.in(GameNoteFields.playerId, playerId));
//        if (currencyId > 0) {
//            filterList.add(Filters.eq(GameNoteFields.currencyId, currencyId));
//        }
//        filterList.add(Filters.gte(GameNoteFields.createTime, wagerStart));
//        filterList.add(Filters.lt(GameNoteFields.createTime, wagerEnd));
//
//        final Bson query = Filters.and(filterList);
//
//        final GameNote activePlayer = collection.aggregate(Arrays.asList(
//                Aggregates.match(query),
//                Aggregates.group("$" + GameNoteFields.playerId),
//                Aggregates.count(GameNoteFields.activePlayerCount)
//        )).first();
//
//        final GameNote wagerAmount = collection.aggregate(Arrays.asList(
//                Aggregates.match(query),
//                Aggregates.group(null, Accumulators.sum(GameNoteFields.validBets, "$" + GameNoteFields.validBets))
//        )).first();
//
//        final GameWagerStat wagerStat = new GameWagerStat();
//        if (activePlayer != null) {
//            wagerStat.setTotalActivePlayerCount(activePlayer.getActivePlayerCount());
//        }
//
//        if (wagerAmount != null) {
//            wagerStat.setTotalWager(wagerAmount.getValidBets());
//        }
//        return wagerStat;
//    }

    public GameNote aggregateBet(long playerId, int currencyId, String validBets, long wagerStart, long wagerEnd) {
        final Bson query = Filters.and(
                Filters.eq(GameNoteFields.playerId, playerId),
                Filters.eq(GameNoteFields.currencyId, currencyId),
                Filters.gte(GameNoteFields.createTime, wagerStart),
                Filters.lt(GameNoteFields.createTime, wagerEnd)
        );

        final GameNote aggregate = collection.aggregate(Arrays.asList(
                Aggregates.match(query),
                Aggregates.group(null, Accumulators.sum(validBets, "$" + validBets))
        )).first();

        final GameNote gameNote = new GameNote();
        if (aggregate != null) {
            gameNote.setValidBets(aggregate.getValidBets());
        }
        return gameNote;
    }

    /**
     * 90 day
     *
     * @param end
     * @return
     */
    public long delete(long end) {
        final Criteria criteria = new Criteria();
        criteria.and(GameNoteFields.createTime).lt(end);

        int BATCH_SIZE = 1000;
        long totalDeleted = 0;
        while (true) {
            // 查询一批数据
            final Query query = new Query(criteria).limit(BATCH_SIZE);

            // 批量删除
            final long deletedCount = ops.remove(query, GameNote.class).getDeletedCount();
            totalDeleted += deletedCount;

            if (deletedCount < BATCH_SIZE) {
                break;
            }
        }
        return totalDeleted;
    }

    public GameNote findByTransactionId(String transactionId) {
        final Bson query = Filters.in("transactionIds", transactionId);  // 使用 MongoDB 查询 transactionIds 字段是否包含 transactionId
        return collection.find(query).first();
    }

}
