package com.game.dao.session;

import com.game.engine.mongo.*;
import com.game.engine.util.misc.Tuple2;
import com.game.entity.session.SessionsNote;
import com.game.entity.session.SessionsNoteFields;
import com.game.entity.player.Player;
import com.game.utils.VirtualThreadUtils;
import com.mongodb.client.MongoCollection;
import org.bson.Document;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;

import java.util.List;

public class SessionDao implements EntityDao<SessionsNote> {

    private final MongoCollection<SessionsNote> collection;
    private final MongoTemplate ops;

    public SessionDao(DBConnectionMrg dbConnectionMrg) {
        this.ops = dbConnectionMrg.getMongoTemplate();
        this.collection = dbConnectionMrg.getCollection("sessions_note", entityClass());
        final IndexDescAll indexDescAll = IndexDescAll.createWithAllIndexs(
                IndexDescSingle.createIndex(new IndexTypeAndName(Long.class, SessionsNoteFields.lastTime))
        );
        final MongoCollection<Document> mongoCollection = dbConnectionMrg.getMongoTemplate().getCollection("sessions_note");
        ToolMongoIndexHelper.indexBuildWork(indexDescAll.getApplicationIndex(), null, mongoCollection);
    }

    @Override
    public Class<SessionsNote> entityClass() {
        return SessionsNote.class;
    }

    public void insert(SessionsNote sessionsNote) {
        VirtualThreadUtils.execute(() -> collection.insertOne(sessionsNote));
    }

    public Tuple2<Integer, List<SessionsNote>> loadSessions(Player player, long start, long end, int skip, int limit) {
        final Query query = new Query();
        query.addCriteria(Criteria.where(SessionsNoteFields.playerId).is(player.getPlayerId()));
        if (start > 0 && end > 0) {
            query.addCriteria(Criteria.where(SessionsNoteFields.lastTime).gte(start).lt(end));
        }

        final int count = (int) ops.count(query, SessionsNote.class);
        final List<SessionsNote> sessionsNoteList = ops.find(query.with(Sort.by(Sort.Direction.DESC, SessionsNoteFields.lastTime))
                .skip(skip).limit(limit), SessionsNote.class);
        return new Tuple2<>(count, sessionsNoteList);
    }

    /**
     * 30 day
     *
     * @param end
     * @return
     */
    public long delete(long end) {
        final Criteria criteria = new Criteria();
        criteria.and(SessionsNoteFields.lastTime).lt(end);

        int BATCH_SIZE = 1000;
        long totalDeleted = 0;
        while (true) {
            // 查询一批数据
            final Query query = new Query(criteria).limit(BATCH_SIZE);

            // 批量删除
            final long deletedCount = ops.remove(query, SessionsNote.class).getDeletedCount();
            totalDeleted += deletedCount;

            if (deletedCount < BATCH_SIZE) {
                break;
            }
        }
        return totalDeleted;
    }
}
