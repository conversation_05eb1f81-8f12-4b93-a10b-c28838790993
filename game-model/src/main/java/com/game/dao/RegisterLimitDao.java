package com.game.dao;

import com.game.engine.mongo.*;
import com.game.engine.utils.TimeUtil;
import com.game.entity.RegisterLimit;
import com.game.entity.RegisterLimitFields;
import com.game.utils.VirtualThreadUtils;
import com.mongodb.client.MongoCollection;
import com.mongodb.client.model.Filters;
import com.mongodb.client.model.Updates;
import org.bson.Document;

public class RegisterLimitDao implements EntityDao<RegisterLimit> {
    private final MongoCollection<RegisterLimit> collection;

    public RegisterLimitDao(DBConnectionMrg dbConnectionMrg) {
        this.collection = dbConnectionMrg.getCollection("registerLimit", entityClass());
        final IndexDescAll indexDescAll = IndexDescAll.createWithAllIndexs(
                IndexDescSingle.createIndex(new IndexTypeAndName(String.class, RegisterLimitFields.business_no)),
                IndexDescSingle.createIndex(new IndexTypeAndName(String.class, RegisterLimitFields.device)),
                IndexDescSingle.createIndex(new IndexTypeAndName(String.class, RegisterLimitFields.ipAddress))
        );
        final MongoCollection<Document> mongoCollection = dbConnectionMrg.getMongoTemplate().getCollection("registerLimit");
        ToolMongoIndexHelper.indexBuildWork(indexDescAll.getApplicationIndex(), null, mongoCollection);
    }

    @Override
    public Class<RegisterLimit> entityClass() {
        return RegisterLimit.class;
    }

    public void insert(RegisterLimit registerLimit) {
        collection.insertOne(registerLimit);
    }

    public RegisterLimit findByIp(String business_no, String ip) {
        return collection.find(Filters.and(
                Filters.eq(RegisterLimitFields.business_no, business_no),
                Filters.eq(RegisterLimitFields.ipAddress, ip)
        )).first();
    }

    public void updateRegisterLimit(String business_no, String ip) {
        collection.updateOne(
                Filters.and(
                        Filters.eq(RegisterLimitFields.business_no, business_no),
                        Filters.eq(RegisterLimitFields.ipAddress, ip)
                ),
                Updates.combine(
                        Updates.inc(RegisterLimitFields.count, 1),
                        Updates.set(RegisterLimitFields.lastTime, TimeUtil.currentTimeMillis())
                ));
    }
}
