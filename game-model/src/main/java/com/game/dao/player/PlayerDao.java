package com.game.dao.player;

import com.game.engine.mongo.*;
import com.game.entity.account.ThreePartyInfo;
import com.game.entity.player.*;
import com.game.entity.player.activity.*;
import com.game.entity.player.activity.continuousDeposit.ContinuousDeposit;
import com.game.entity.player.activity.firstChargeSignIn.FirstChargeSignInInfo;
import com.game.entity.player.activity.firstDepositInviteBonus.FirstDepositInviteBonusInfo;
import com.game.entity.player.activity.mysteryBonus.MysteryBonusInfo;
import com.game.entity.player.activity.piggyBank.PiggyBankInfo;
import com.game.entity.player.activity.rechargeRecover.RechargeRecoverInfo;
import com.game.entity.player.activity.redEnvelopeRain.RedEnvelopeRainInfo;
import com.game.entity.player.activity.dailyContest.DailyContestInfo;
import com.game.entity.player.activity.luckSpin.LuckSpinInfo;
import com.game.entity.player.activity.rewardBox.RewardBoxInfo;
import com.game.entity.player.activity.wageredRebates.WageredRebatesInfo;
import com.game.entity.player.activity.weeklyRaffle.WeeklyRaffleInfo;
import com.game.entity.player.vip.VipClub;
import com.game.utils.VirtualThreadUtils;
import com.mongodb.client.MongoCollection;
import com.mongodb.client.model.Filters;
import com.mongodb.client.model.Updates;
import it.unimi.dsi.fastutil.ints.IntList;
import it.unimi.dsi.fastutil.ints.IntLists;
import it.unimi.dsi.fastutil.longs.LongList;
import it.unimi.dsi.fastutil.longs.LongLists;
import org.bson.BsonValue;
import org.bson.conversions.Bson;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

public class PlayerDao extends ShardEntityDao<Player> {

    public final InboxDao inboxDao;
    public final VipClubDao vipClubDao;
    public final StatsDao statsDao;
    public final BonusInfoDao bonusInfoDao;
    //    public final ActivityInfoDao activityInfoDao;
    public final QuestDao questDao;
    public final LuckSpinDao luckSpinDao;
    public final RedEnvelopeRainDao redEnvelopeRainDao;
    public final RewardBoxDao rewardBoxDao;
    public final MysteryBonusDao mysteryBonusDao;
    public final PiggyBankDao piggyBankDao;
    public final WageredRebatesDao wageredRebatesDao;
    public final FirstDepositInviteBonusDao firstDepositInviteBonusDao;

    private final MongoTemplate ops;
    private final MongoCollection<Player> collection;

    public PlayerDao(MongoConverterMrg converterMrg, HandlerCollection<Player> handler) {
        super(converterMrg, handler);
        this.collection = DBConnectionMrg.getInstance().getCollection("player", entityClass());

        this.ops = DBConnectionMrg.getInstance().getMongoTemplate();
        this.inboxDao = new InboxDao(converterMrg, handler, collection);
        this.vipClubDao = new VipClubDao(converterMrg, handler, collection);
        this.statsDao = new StatsDao(converterMrg, handler, collection);
        this.bonusInfoDao = new BonusInfoDao(converterMrg, handler, collection);
//        this.activityInfoDao = new ActivityInfoDao(converterMrg, handler, collection);
        this.questDao = new QuestDao(converterMrg, handler, collection);
        this.luckSpinDao = new LuckSpinDao(converterMrg, handler, collection);
        this.redEnvelopeRainDao = new RedEnvelopeRainDao(converterMrg, handler, collection);
        this.rewardBoxDao = new RewardBoxDao(converterMrg, handler, collection);
        this.mysteryBonusDao = new MysteryBonusDao(converterMrg, handler, collection);
        this.piggyBankDao = new PiggyBankDao(converterMrg, handler, collection);
        this.wageredRebatesDao = new WageredRebatesDao(converterMrg, handler, collection);
        this.firstDepositInviteBonusDao = new FirstDepositInviteBonusDao(converterMrg, handler, collection);
    }

    @Override
    public Class<Player> entityClass() {
        return Player.class;
    }

    public void insert(Player player) {
        if (player.getPlayerId() <= 0) {
            throw new IllegalArgumentException("player.getPlayerId() <= 0");
        }
        collection.insertOne(player);
    }

    public void updatePlayerField(long playerId, String name, Object value) {
        final Update update = new Update();
        update.set(name, value);

        VirtualThreadUtils.execute(() -> {
            collection.updateOne(Filters.eq(PlayerFields.playerId, playerId), update.getUpdateObject());
        });
    }

    public void updatePlayer(long playerId, Update update) {

        VirtualThreadUtils.execute(() -> {
            collection.updateOne(Filters.eq(PlayerFields.playerId, playerId), update.getUpdateObject());
        });
    }

    public void updateCurrency(Player player, IntList changedCurrencies) {
        if (changedCurrencies.isEmpty()) {
            return;
        }
        final DBPrefixBuilder dbPrefixBuilder = new DBPrefixBuilder();
        dbPrefixBuilder.append(PlayerFields.currencyMap);
        final Update update = new Update();
        for (int i = 0; i < changedCurrencies.size(); i++) {
            final int currencyType = changedCurrencies.getInt(i);
            final double currencyValue = player.getCurrencyMap().getOrDefault(currencyType, 0d);
            update.set(dbPrefixBuilder.buildWithIntAndRewind(currencyType), currencyValue);
        }

        VirtualThreadUtils.execute(() -> {
            collection.updateOne(Filters.eq(PlayerFields.playerId, player.getPlayerId()), update.getUpdateObject());
        });
    }

    public void updateCurrency(Player player) {
        if (player.getCurrencyMap().isEmpty()) {
            return;
        }

        final DBPrefixBuilder dbPrefixBuilder = new DBPrefixBuilder();
        dbPrefixBuilder.append(PlayerFields.currencyMap);
        final Update update = new Update();
        update.set(dbPrefixBuilder.build(), converterMrg.writeNoTypeKey(player.getCurrencyMap()));

        VirtualThreadUtils.execute(() -> {
            collection.updateOne(Filters.eq(PlayerFields.playerId, player.getPlayerId()), update.getUpdateObject());
        });
    }

    public void updateInsertWithdrawAccount(Player player, String currencyIdPayment, LongList insertPayInfo) {
        updateWithdrawAccount(player, currencyIdPayment, insertPayInfo, LongLists.EMPTY_LIST);
    }

    public void updateDeleteWithdrawAccount(Player player, String currencyIdPayment, LongList deletePayInfo) {
        updateWithdrawAccount(player, currencyIdPayment, LongLists.EMPTY_LIST, deletePayInfo);
    }

    private void updateWithdrawAccount(Player player, String currencyIdPay, LongList insertPayInfo, LongList deletePayInfo) {
        if (insertPayInfo.isEmpty() && deletePayInfo.isEmpty()) {
            return;
        }

        final DBPrefixBuilder dbPrefixBuilder = new DBPrefixBuilder();
        dbPrefixBuilder.append(PlayerFields.withdrawAccountMap)
                .append(currencyIdPay)
                .append(WithdrawAccountFields.payInfoMap)
                .build();

        Update update = new Update();
        //添加
        for (int i = 0; i < insertPayInfo.size(); i++) {
            final WithdrawAccount withdrawAccount = player.getWithdrawAccountMap().get(currencyIdPay);
            final long updatePayInfoGuid = insertPayInfo.getLong(i);
            PayInfo payInfo = withdrawAccount.getPayInfoMap().get(updatePayInfoGuid);
            if (payInfo == null) {
                throw new IllegalArgumentException("payInfo does not exist, Guid: " + updatePayInfoGuid);
            }
            BsonValue document = converterMrg.writeNoTypeKey(payInfo);
            update.set(dbPrefixBuilder.buildWithLongAndRewind(updatePayInfoGuid), document);
        }

        //删除
        for (int i = 0; i < deletePayInfo.size(); i++) {
            final WithdrawAccount withdrawAccount = player.getWithdrawAccountMap().get(currencyIdPay);
            final long deletePayInfoGuid = deletePayInfo.getLong(i);
            if (withdrawAccount.getPayInfoMap().containsKey(deletePayInfoGuid)) {
                throw new IllegalArgumentException("payInfo does not deleted, Guid: " + deletePayInfoGuid);
            }
            update.unset(dbPrefixBuilder.buildWithLongAndRewind(deletePayInfoGuid));
        }

        VirtualThreadUtils.execute(() -> {
            collection.updateOne(Filters.eq(PlayerFields.playerId, player.getPlayerId()), update.getUpdateObject());
        });
    }

    public void updateWithdrawStandard(Player player, IntList changedCurrencies) {
        if (changedCurrencies.isEmpty()) {
            return;
        }
        final DBPrefixBuilder dbPrefixBuilder = new DBPrefixBuilder();
        dbPrefixBuilder.append(PlayerFields.withdrawStandardMap);
        final Update update = new Update();
        for (int i = 0; i < changedCurrencies.size(); i++) {
            final int currencyType = changedCurrencies.getInt(i);
            final WithdrawStandard withdrawStandard = player.getWithdrawStandardMap().get(currencyType);
            if (withdrawStandard == null) {
                throw new IllegalArgumentException("withdrawStandard does not exist, currencyId: " + currencyType);
            }
            update.set(dbPrefixBuilder.buildWithIntAndRewind(currencyType), converterMrg.writeNoTypeKey(withdrawStandard));
        }

        VirtualThreadUtils.execute(() -> {
            collection.updateOne(Filters.eq(PlayerFields.playerId, player.getPlayerId()), update.getUpdateObject());
        });
    }

    public void updateTurnoverRecord(Player player,
                                     LongList changedOrderId, LongList deleteOrderId) {
        if (changedOrderId.isEmpty() && deleteOrderId.isEmpty()) {
            return;
        }
        final DBPrefixBuilder dbPrefixBuilder = new DBPrefixBuilder();
        dbPrefixBuilder.append(PlayerFields.turnoverRecordMap);
        final Update update = new Update();
        for (int i = 0; i < changedOrderId.size(); i++) {
            final long orderId = changedOrderId.getLong(i);
            final TurnoverRecord turnoverRecord = player.getTurnoverRecordMap().get(orderId);
            if (turnoverRecord == null) {
                throw new IllegalArgumentException("turnoverRecord does not exist, orderId: " + orderId);
            }
            update.set(dbPrefixBuilder.buildWithLongAndRewind(orderId), converterMrg.writeNoTypeKey(turnoverRecord));
        }

        //删除
        for (int i = 0; i < deleteOrderId.size(); i++) {
            final long deleteInboxGuid = deleteOrderId.getLong(i);
            if (player.getTurnoverRecordMap().containsKey(deleteInboxGuid)) {
                throw new IllegalArgumentException("turnoverRecord is not deleted, Guid: " + deleteInboxGuid);
            }
            update.unset(dbPrefixBuilder.buildWithLongAndRewind(deleteInboxGuid));
        }

        VirtualThreadUtils.execute(() -> {
            collection.updateOne(Filters.eq(PlayerFields.playerId, player.getPlayerId()), update.getUpdateObject());
        });
    }

    public void updateTurnoverRecord(Player player) {
        if (player.getTurnoverRecordMap().isEmpty()) {
            return;
        }
        final DBPrefixBuilder dbPrefixBuilder = new DBPrefixBuilder();
        dbPrefixBuilder.append(PlayerFields.turnoverRecordMap);
        final Update update = new Update();
        update.set(dbPrefixBuilder.build(), converterMrg.writeNoTypeKey(player.getTurnoverRecordMap()));

        VirtualThreadUtils.execute(() -> {
            collection.updateOne(Filters.eq(PlayerFields.playerId, player.getPlayerId()), update.getUpdateObject());
        });
    }

    public void updateInsertThreePartyInfo(Player player, IntList insertedMail) {
        updateThreePartyInfo(player, insertedMail, IntLists.EMPTY_LIST);
    }

    public void updateDeleteThreePartyInfo(Player player, IntList deleteMail) {
        updateThreePartyInfo(player, IntLists.EMPTY_LIST, deleteMail);
    }

    private void updateThreePartyInfo(Player player, IntList insertedThreeParty, IntList deleteThreeParty) {
        if (insertedThreeParty.isEmpty() && deleteThreeParty.isEmpty()) {
            return;
        }

        final DBPrefixBuilder dbPrefixBuilder = new DBPrefixBuilder();
        dbPrefixBuilder.append(PlayerFields.threePartyInfoMap)
                .build();

        Update update = new Update();
        //添加
        for (int i = 0; i < insertedThreeParty.size(); i++) {
            final int updateThreePartyId = insertedThreeParty.getInt(i);
            ThreePartyInfo threePartyInfo = player.getThreePartyInfoMap().get(updateThreePartyId);
            if (threePartyInfo == null) {
                throw new IllegalArgumentException("threePartyInfo does not exist, Guid: " + updateThreePartyId);
            }
            BsonValue document = converterMrg.writeNoTypeKey(threePartyInfo);
            update.set(dbPrefixBuilder.buildWithIntAndRewind(updateThreePartyId), document);
        }

        //删除
        for (int i = 0; i < deleteThreeParty.size(); i++) {
            final int deleteThreePartyId = deleteThreeParty.getInt(i);
            if (player.getThreePartyInfoMap().containsKey(deleteThreePartyId)) {
                throw new IllegalArgumentException("threePartyInfo is not deleted, Guid: " + deleteThreePartyId);
            }
            update.unset(dbPrefixBuilder.buildWithIntAndRewind(deleteThreePartyId));
        }

        VirtualThreadUtils.execute(() -> {
            collection.updateOne(Filters.eq(PlayerFields.playerId, player.getPlayerId()), update.getUpdateObject());
        });
    }

    public void updateLuckSpinInfo(long playerId, LuckSpinInfo luckSpinInfo) {
        if (luckSpinInfo.getLuckSpinDataMap().isEmpty()) {
            return;
        }
        final DBPrefixBuilder dbPrefixBuilder = new DBPrefixBuilder();
        dbPrefixBuilder.append(PlayerFields.luckSpinInfo).build();

        final Update update = new Update();
        update.set(dbPrefixBuilder.build(), converterMrg.writeNoTypeKey(luckSpinInfo));

        VirtualThreadUtils.execute(() -> {
            collection.updateOne(Filters.eq(PlayerFields.playerId, playerId), update.getUpdateObject());
        });
    }

    public void updateDailyContestInfo(long playerId, DailyContestInfo dailyContestInfo) {
        final DBPrefixBuilder dbPrefixBuilder = new DBPrefixBuilder();
        dbPrefixBuilder.append(PlayerFields.dailyContestInfo)
                .build();

        final Update update = new Update();
        update.set(dbPrefixBuilder.build(), converterMrg.writeNoTypeKey(dailyContestInfo));

        VirtualThreadUtils.execute(() -> {
            collection.updateOne(Filters.eq(PlayerFields.playerId, playerId), update.getUpdateObject());
        });
    }

    public void updateWeeklyRaffleInfo(long playerId, WeeklyRaffleInfo weeklyRaffleInfo) {
        final DBPrefixBuilder dbPrefixBuilder = new DBPrefixBuilder();
        dbPrefixBuilder.append(PlayerFields.weeklyRaffleInfo).build();

        final Update update = new Update();
        update.set(dbPrefixBuilder.build(), converterMrg.writeNoTypeKey(weeklyRaffleInfo));

        VirtualThreadUtils.execute(() -> {
            collection.updateOne(Filters.eq(PlayerFields.playerId, playerId), update.getUpdateObject());
        });
    }

    public void updateActivity(Player player, ActivityInfo activityInfo) {
        if (activityInfo.getActivityDataMap().isEmpty()) {
            return;
        }
        final DBPrefixBuilder dbPrefixBuilder = new DBPrefixBuilder();
        dbPrefixBuilder.append(PlayerFields.activityInfo);

        final Update update = new Update();
        update.set(dbPrefixBuilder.build(), converterMrg.writeNoTypeKey(activityInfo));

        VirtualThreadUtils.execute(() -> {
            collection.updateOne(Filters.eq(PlayerFields.playerId, player.getPlayerId()), update.getUpdateObject());
        });
    }

    public void updateFreeGameInfo(Player player, IntList changedCurrencies) {
        if (changedCurrencies.isEmpty()) {
            return;
        }
        final DBPrefixBuilder dbPrefixBuilder = new DBPrefixBuilder();
        dbPrefixBuilder.append(PlayerFields.freeGameInfoMap);
        final Update update = new Update();
        for (int i = 0; i < changedCurrencies.size(); i++) {
            final int currencyType = changedCurrencies.getInt(i);
            final FreeGameInfo freeGameInfo = player.getFreeGameInfoMap().get(currencyType);
            if (freeGameInfo == null) {
                throw new IllegalArgumentException("freeGameInfo does not exist, Guid: " + currencyType);
            }
            update.set(dbPrefixBuilder.buildWithIntAndRewind(currencyType), converterMrg.writeNoTypeKey(freeGameInfo));
        }

        VirtualThreadUtils.execute(() -> {
            collection.updateOne(Filters.eq(PlayerFields.playerId, player.getPlayerId()), update.getUpdateObject());
        });
    }

    public Player getById(long playerId) {
        return ops.findOne(new Query(Criteria.where(PlayerFields.playerId).is(playerId)), Player.class);
    }

    public void updateVipClubInfo(long playerId, VipClub vipClub) {
        final DBPrefixBuilder dbPrefixBuilder = new DBPrefixBuilder();
        dbPrefixBuilder.append(PlayerFields.vipClub)
                .build();

        final Update update = new Update();
        update.set(dbPrefixBuilder.build(), converterMrg.writeNoTypeKey(vipClub));

        VirtualThreadUtils.execute(() -> {
            collection.updateOne(Filters.eq(PlayerFields.playerId, playerId), update.getUpdateObject());
        });
    }

    public void updateRedEnvelopeRainInfo(long playerId, RedEnvelopeRainInfo redEnvelopeRainInfo) {
        final DBPrefixBuilder dbPrefixBuilder = new DBPrefixBuilder();
        dbPrefixBuilder.append(PlayerFields.redEnvelopeRainInfo)
                .build();

        final Update update = new Update();
        update.set(dbPrefixBuilder.build(), converterMrg.writeNoTypeKey(redEnvelopeRainInfo));

        VirtualThreadUtils.execute(() -> {
            collection.updateOne(Filters.eq(PlayerFields.playerId, playerId), update.getUpdateObject());
        });
    }

    public void updateRewardBoxInfo(long playerId, RewardBoxInfo rewardBoxInfo) {
        final DBPrefixBuilder dbPrefixBuilder = new DBPrefixBuilder();
        dbPrefixBuilder.append(PlayerFields.rewardBoxInfo)
                .build();

        final Update update = new Update();
        update.set(dbPrefixBuilder.build(), converterMrg.writeNoTypeKey(rewardBoxInfo));

        VirtualThreadUtils.execute(() -> {
            collection.updateOne(Filters.eq(PlayerFields.playerId, playerId), update.getUpdateObject());
        });
    }

    public void updateMysteryBonusInfo(long playerId, MysteryBonusInfo mysteryBonusInfo) {
        final DBPrefixBuilder dbPrefixBuilder = new DBPrefixBuilder();
        dbPrefixBuilder.append(PlayerFields.mysteryBonusInfo)
                .build();

        final Update update = new Update();
        update.set(dbPrefixBuilder.build(), converterMrg.writeNoTypeKey(mysteryBonusInfo));

        VirtualThreadUtils.execute(() -> {
            collection.updateOne(Filters.eq(PlayerFields.playerId, playerId), update.getUpdateObject());
        });
    }

    public void updatePiggyBankInfo(long playerId, PiggyBankInfo piggyBankInfo) {
        final DBPrefixBuilder dbPrefixBuilder = new DBPrefixBuilder();
        dbPrefixBuilder.append(PlayerFields.piggyBankInfo)
                .build();

        final Update update = new Update();
        update.set(dbPrefixBuilder.build(), converterMrg.writeNoTypeKey(piggyBankInfo));

        VirtualThreadUtils.execute(() -> {
            collection.updateOne(Filters.eq(PlayerFields.playerId, playerId), update.getUpdateObject());
        });
    }

    public void updateContinuousDepositInfo(long playerId, ContinuousDeposit continuousDeposit) {
        final DBPrefixBuilder dbPrefixBuilder = new DBPrefixBuilder();
        dbPrefixBuilder.append(PlayerFields.continuousDepositInfo)
                .build();

        final Update update = new Update();
        update.set(dbPrefixBuilder.build(), converterMrg.writeNoTypeKey(continuousDeposit));

        VirtualThreadUtils.execute(() -> {
            collection.updateOne(Filters.eq(PlayerFields.playerId, playerId), update.getUpdateObject());
        });
    }

    public void updateFirstChargeSignInInfo(long playerId, FirstChargeSignInInfo firstChargeSignInInfo) {
        final DBPrefixBuilder dbPrefixBuilder = new DBPrefixBuilder();
        dbPrefixBuilder.append(PlayerFields.firstChargeSignInInfo)
                .build();

        final Update update = new Update();
        update.set(dbPrefixBuilder.build(), converterMrg.writeNoTypeKey(firstChargeSignInInfo));

        VirtualThreadUtils.execute(() -> {
            collection.updateOne(Filters.eq(PlayerFields.playerId, playerId), update.getUpdateObject());
        });
    }

    public void updateRechargeRecoverInfo(long playerId, RechargeRecoverInfo rechargeRecoverInfo) {
        final DBPrefixBuilder dbPrefixBuilder = new DBPrefixBuilder();
        dbPrefixBuilder.append(PlayerFields.rechargeRecoverInfo)
                .build();

        final Update update = new Update();
        update.set(dbPrefixBuilder.build(), converterMrg.writeNoTypeKey(rechargeRecoverInfo));

        VirtualThreadUtils.execute(() -> {
            collection.updateOne(Filters.eq(PlayerFields.playerId, playerId), update.getUpdateObject());
        });
    }

    public void updateWageredRebatesInfo(long playerId, WageredRebatesInfo wageredRebatesInfo) {
        final DBPrefixBuilder dbPrefixBuilder = new DBPrefixBuilder();
        dbPrefixBuilder.append(PlayerFields.wageredRebatesInfo)
                .build();

        final Update update = new Update();
        update.set(dbPrefixBuilder.build(), converterMrg.writeNoTypeKey(wageredRebatesInfo));

        VirtualThreadUtils.execute(() -> {
            collection.updateOne(Filters.eq(PlayerFields.playerId, playerId), update.getUpdateObject());
        });
    }

    public void updateFirstDepositInviteBonusInfo(long playerId, FirstDepositInviteBonusInfo firstDepositInviteBonusInfo) {
        final DBPrefixBuilder dbPrefixBuilder = new DBPrefixBuilder();
        dbPrefixBuilder.append(PlayerFields.firstDepositInviteBonusInfo)
                .build();

        final Update update = new Update();
        update.set(dbPrefixBuilder.build(), converterMrg.writeNoTypeKey(firstDepositInviteBonusInfo));

        VirtualThreadUtils.execute(() -> {
            collection.updateOne(Filters.eq(PlayerFields.playerId, playerId), update.getUpdateObject());
        });
    }

    public List<Player> loadPlayer(String business_no, long start, long end, int skip, int limit) {
        final Query query = new Query();
        query.addCriteria(Criteria.where(PlayerFields.business_no).is(business_no));
        if (start > 0 && end > 0) {
            query.addCriteria(Criteria.where(PlayerFields.createTime).gte(start).lt(end));
        }
        return ops.find(query.with(Sort.by(Sort.Direction.DESC, PlayerFields.createTime)).skip(skip).limit(limit), Player.class);
    }

    public List<Player> loadPlayer(boolean online) {
        final Criteria criteria = new Criteria();
        criteria.and(PlayerFields.online).is(online);

        int BATCH_SIZE = 1000;
        final List<Player> playerIds = new ArrayList<>();
        int offset = 0;

        while (true) {
            // 查询一批数据，使用 skip 进行分页
            final Query query = new Query(criteria).limit(BATCH_SIZE).skip(offset);

            final List<Player> players = ops.find(query, Player.class);

            if (players.isEmpty()) {
                break; // 没有数据了，退出循环
            }

            playerIds.addAll(players);
            offset += players.size(); // 更新 offset，避免重复查询

            if (players.size() < BATCH_SIZE) {
                break; // 已经取完所有数据，退出循环
            }
        }

        return playerIds;
    }

    public List<Player> loadPlayersByIds(List<Long> playerIdList) {
        if (playerIdList == null || playerIdList.isEmpty()) {
            return Collections.emptyList();
        }

        final int BATCH_SIZE = 1000;
        final List<Player> result = new ArrayList<>(playerIdList.size());

        for (int i = 0; i < playerIdList.size(); i += BATCH_SIZE) {
            final List<Long> batch = playerIdList.subList(i, Math.min(i + BATCH_SIZE, playerIdList.size()));
            final Criteria criteria = Criteria.where(PlayerFields.playerId).in(batch);
            final Query query = new Query(criteria);

            // 可选：减少传输字段
            // query.fields().include("id").include("nickname").include("level");

            final List<Player> players = ops.find(query, Player.class);
            result.addAll(players);
        }

        return result;
    }
}
