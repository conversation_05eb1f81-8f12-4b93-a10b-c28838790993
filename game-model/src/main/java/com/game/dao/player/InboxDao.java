package com.game.dao.player;

import com.game.engine.mongo.DBPrefixBuilder;
import com.game.engine.mongo.EntityDao;
import com.game.engine.mongo.HandlerCollection;
import com.game.engine.mongo.MongoConverterMrg;
import com.game.entity.player.Player;
import com.game.entity.player.PlayerFields;
import com.game.entity.player.inbox.Inbox;
import com.game.entity.player.inbox.InboxFields;
import com.game.entity.player.inbox.InboxInfo;
import com.game.entity.player.inbox.InboxInfoFields;
import com.game.utils.VirtualThreadUtils;
import com.mongodb.client.MongoCollection;
import com.mongodb.client.model.Filters;
import it.unimi.dsi.fastutil.longs.LongList;
import it.unimi.dsi.fastutil.longs.LongLists;
import org.bson.BsonValue;
import org.springframework.data.mongodb.core.query.Update;

public class InboxDao {

    private final MongoConverterMrg converterMrg;
    private final HandlerCollection<Player> handler;
    private final MongoCollection<Player> collection;

    public InboxDao(MongoConverterMrg converterMrg, HandlerCollection<Player> handler, MongoCollection<Player> collection) {
        this.converterMrg = converterMrg;
        this.handler = handler;
        this.collection = collection;
    }

    public void updateInsertInbox(long playerId, InboxInfo inboxInfo, LongList insertedMail) {
        updateInbox(playerId, inboxInfo, insertedMail, LongLists.EMPTY_LIST);
    }

    public void updateDeleteInbox(long playerId, InboxInfo inboxInfo, LongList deleteMail) {
        updateInbox(playerId, inboxInfo, LongLists.EMPTY_LIST, deleteMail);
    }

    private void updateInbox(long playerId, InboxInfo inboxInfo, LongList insertedInbox, LongList deleteInbox) {
        if (insertedInbox.isEmpty() && deleteInbox.isEmpty()) {
            return;
        }

        final DBPrefixBuilder dbPrefixBuilder = new DBPrefixBuilder();
        dbPrefixBuilder.append(PlayerFields.inboxInfo)
                .append(InboxInfoFields.inboxMap)
                .build();

        Update update = new Update();
        //添加
        for (int i = 0; i < insertedInbox.size(); i++) {
            final long updateInboxGuid = insertedInbox.getLong(i);
            Inbox inbox = inboxInfo.getInboxMap().get(updateInboxGuid);
            if (inbox == null) {
                throw new IllegalArgumentException("inbox does not exist, Guid: " + updateInboxGuid);
            }
            BsonValue document = converterMrg.writeNoTypeKey(inbox);
            update.set(dbPrefixBuilder.buildWithLongAndRewind(updateInboxGuid), document);
        }

        //删除
        for (int i = 0; i < deleteInbox.size(); i++) {
            final long deleteInboxGuid = deleteInbox.getLong(i);
            if (inboxInfo.getInboxMap().containsKey(deleteInboxGuid)) {
                throw new IllegalArgumentException("inbox is not deleted, Guid: " + deleteInboxGuid);
            }
            update.unset(dbPrefixBuilder.buildWithLongAndRewind(deleteInboxGuid));
        }

        VirtualThreadUtils.execute(() -> {
            collection.updateOne(Filters.eq(PlayerFields.playerId, playerId), update.getUpdateObject());
        });
    }

    private void fillFieldUtilGuid(DBPrefixBuilder dbPrefixBuilder, long guild) {
        dbPrefixBuilder.append(PlayerFields.inboxInfo)
                .append(InboxInfoFields.inboxMap)
                .appendLong(guild);
    }

    public void updateInboxRead(long playerGuid, Inbox inbox) {
        final DBPrefixBuilder dbPrefixBuilder = new DBPrefixBuilder();
        fillFieldUtilGuid(dbPrefixBuilder, inbox.getInboxId());
        dbPrefixBuilder.append(InboxFields.read);

        Update update = new Update();
        update.set(dbPrefixBuilder.build(), inbox.isRead());

        VirtualThreadUtils.execute(() -> {
            collection.updateOne(Filters.eq(PlayerFields.playerId, playerGuid), update.getUpdateObject());
        });
    }

//    public void updateInboxAllRead(long playerGuid, InboxInfo inboxInfo, LongList updateInbox) {
//        final DBPrefixBuilder dbPrefixBuilder = EntityDao.builderPool.get();
//        dbPrefixBuilder.append(PlayerFields.inboxInfo)
//                .append(InboxInfoFields.inboxMap);
//
//        final Update update = new Update();
//        for (int i = 0; i < updateInbox.size(); i++) {
//            final long updateInboxGuid = updateInbox.getLong(i);
//            final int length = dbPrefixBuilder.length();
//            dbPrefixBuilder.appendLong(updateInboxGuid)
//                    .append(InboxFields.read);
//            final String result = dbPrefixBuilder.build();
//            dbPrefixBuilder.setLength(length);
//
//            final Inbox inbox = inboxInfo.getInboxMap().get(updateInboxGuid);
//            if (inbox == null) {
//                throw new IllegalArgumentException("inbox does not exist, Guid: " + updateInboxGuid);
//            }
//            update.set(result, inbox.isRead());
//        }
//        handler.yibuUpdateOne(PlayerFields.playerId, playerGuid, update.getUpdateObject(), null);
//        EntityDao.builderPool.returnOne(dbPrefixBuilder);
//    }

    public void updateReceivedPublicMails(long playerId, InboxInfo inboxInfo) {
        final DBPrefixBuilder dbPrefixBuilder = new DBPrefixBuilder();
        dbPrefixBuilder.append(PlayerFields.inboxInfo)
                .append(InboxInfoFields.receivedPublicMails)
                .build();

        Update update = new Update();
        update.set(dbPrefixBuilder.build(), inboxInfo.getReceivedPublicMails());

        VirtualThreadUtils.execute(() -> {
            collection.updateOne(Filters.eq(PlayerFields.playerId, playerId), update.getUpdateObject());
        });
    }

    public void updateReceivedDay(long playerId, InboxInfo inboxInfo) {
        final DBPrefixBuilder dbPrefixBuilder = new DBPrefixBuilder();
        dbPrefixBuilder.append(PlayerFields.inboxInfo)
                .append(InboxInfoFields.receivedDayMails)
                .build();

        Update update = new Update();
        update.set(dbPrefixBuilder.build(), inboxInfo.getReceivedDayMails());

        VirtualThreadUtils.execute(() -> {
            collection.updateOne(Filters.eq(PlayerFields.playerId, playerId), update.getUpdateObject());
        });
    }

    public void updateReceivedWeekly(long playerId, InboxInfo inboxInfo) {
        final DBPrefixBuilder dbPrefixBuilder = new DBPrefixBuilder();
        dbPrefixBuilder.append(PlayerFields.inboxInfo)
                .append(InboxInfoFields.receivedWeeklyMails)
                .build();

        Update update = new Update();
        update.set(dbPrefixBuilder.build(), inboxInfo.getReceivedWeeklyMails());

        VirtualThreadUtils.execute(() -> {
            collection.updateOne(Filters.eq(PlayerFields.playerId, playerId), update.getUpdateObject());
        });
    }

}
