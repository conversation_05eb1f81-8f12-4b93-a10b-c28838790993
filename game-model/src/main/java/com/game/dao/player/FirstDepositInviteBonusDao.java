package com.game.dao.player;

import com.game.engine.mongo.DBPrefixBuilder;
import com.game.engine.mongo.HandlerCollection;
import com.game.engine.mongo.MongoConverterMrg;
import com.game.entity.player.Player;
import com.game.entity.player.PlayerFields;
import com.game.entity.player.activity.firstDepositInviteBonus.FirstDepositInviteBonusInfo;
import com.game.entity.player.activity.firstDepositInviteBonus.FirstDepositInviteBonusInfoFields;
import com.game.utils.VirtualThreadUtils;
import com.mongodb.client.MongoCollection;
import com.mongodb.client.model.Filters;
import org.springframework.data.mongodb.core.query.Update;

public class FirstDepositInviteBonusDao {

    private final MongoConverterMrg converterMrg;
    private final HandlerCollection<Player> handler;
    private final MongoCollection<Player> collection;

    public FirstDepositInviteBonusDao(MongoConverterMrg converterMrg, HandlerCollection<Player> handler, MongoCollection<Player> collection) {
        this.converterMrg = converterMrg;
        this.handler = handler;
        this.collection = collection;
    }

    public void updateReceive(long playerId, FirstDepositInviteBonusInfo firstDepositInviteBonusInfo) {
        final DBPrefixBuilder builder = new DBPrefixBuilder();
        builder.append(PlayerFields.firstDepositInviteBonusInfo)
                .append(FirstDepositInviteBonusInfoFields.receiveBonus);

        final Update update = new Update();
        update.set(builder.build(), firstDepositInviteBonusInfo.getReceiveBonus());

        VirtualThreadUtils.execute(() -> {
            collection.updateOne(Filters.eq(PlayerFields.playerId, playerId), update.getUpdateObject());
        });
    }

}
