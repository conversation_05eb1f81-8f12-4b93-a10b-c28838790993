package com.game.dao.player;

import com.game.engine.mongo.DBPrefixBuilder;
import com.game.engine.mongo.HandlerCollection;
import com.game.engine.mongo.MongoConverterMrg;
import com.game.entity.player.Player;
import com.game.entity.player.PlayerFields;
import com.game.entity.player.activity.redEnvelopeRain.RedEnvelopeRainInfo;
import com.game.entity.player.activity.redEnvelopeRain.RedEnvelopeRainInfoFields;
import com.game.entity.player.promote.PlayerPromoteFields;
import com.game.utils.VirtualThreadUtils;
import com.mongodb.client.MongoCollection;
import com.mongodb.client.model.Filters;
import org.springframework.data.mongodb.core.query.Update;

public class RedEnvelopeRainDao {

    private final MongoConverterMrg converterMrg;
    private final HandlerCollection<Player> handler;
    private final MongoCollection<Player> collection;

    public RedEnvelopeRainDao(MongoConverterMrg converterMrg, HandlerCollection<Player> handler, MongoCollection<Player> collection) {
        this.converterMrg = converterMrg;
        this.handler = handler;
        this.collection = collection;
    }

    public void updateReceiveTimes(long playerId, RedEnvelopeRainInfo redEnvelopeRainInfo) {
        final DBPrefixBuilder builder = new DBPrefixBuilder();
        builder.append(PlayerFields.redEnvelopeRainInfo)
                .append(RedEnvelopeRainInfoFields.receiveTimes);

        final Update update = new Update();
        update.set(builder.build(), redEnvelopeRainInfo.getReceiveTimes());

        VirtualThreadUtils.execute(() -> {
            collection.updateOne(Filters.eq(PlayerFields.playerId, playerId), update.getUpdateObject());
        });
    }

    public void updateReceive(long playerId, RedEnvelopeRainInfo redEnvelopeRainInfo) {
        final DBPrefixBuilder builder = new DBPrefixBuilder();
        builder.append(PlayerFields.redEnvelopeRainInfo)
                .append(RedEnvelopeRainInfoFields.receive);

        final Update update = new Update();
        update.set(builder.build(), redEnvelopeRainInfo.getReceive());

        VirtualThreadUtils.execute(() -> {
            collection.updateOne(Filters.eq(PlayerFields.playerId, playerId), update.getUpdateObject());
        });
    }

}
