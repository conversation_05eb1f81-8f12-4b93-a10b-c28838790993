package com.game.dao.player;

import com.game.engine.mongo.DBPrefixBuilder;
import com.game.engine.mongo.HandlerCollection;
import com.game.engine.mongo.MongoConverterMrg;
import com.game.entity.player.Player;
import com.game.entity.player.PlayerFields;
import com.game.entity.player.activity.luckSpin.LuckSpinData;
import com.game.entity.player.activity.luckSpin.LuckSpinInfo;
import com.game.entity.player.activity.luckSpin.LuckSpinInfoFields;
import com.game.utils.VirtualThreadUtils;
import com.mongodb.client.MongoCollection;
import com.mongodb.client.model.Filters;
import it.unimi.dsi.fastutil.ints.IntList;
import org.bson.BsonValue;
import org.springframework.data.mongodb.core.query.Update;

public class LuckSpinDao {
    private final MongoConverterMrg converterMrg;
    private final HandlerCollection<Player> handler;
    private final MongoCollection<Player> collection;

    public LuckSpinDao(MongoConverterMrg converterMrg, HandlerCollection<Player> handler, MongoCollection<Player> collection) {
        this.converterMrg = converterMrg;
        this.handler = handler;
        this.collection = collection;
    }


    public void updateLuckSpin(long playerId, LuckSpinInfo luckSpinInfo, IntList updateLuckSpin) {
        if (updateLuckSpin.isEmpty()) {
            return;
        }

        final DBPrefixBuilder dbPrefixBuilder = new DBPrefixBuilder();
        dbPrefixBuilder.append(PlayerFields.luckSpinInfo)
                .append(LuckSpinInfoFields.luckSpinDataMap)
                .build();

        Update update = new Update();
        //添加
        for (int i = 0; i < updateLuckSpin.size(); i++) {
            final int luckSpinId = updateLuckSpin.getInt(i);
            LuckSpinData luckSpinData = luckSpinInfo.getLuckSpinDataMap().get(luckSpinId);
            if (luckSpinData == null) {
                throw new IllegalArgumentException("luckSpinData does not exist, Guid: " + luckSpinId);
            }
            BsonValue document = converterMrg.writeNoTypeKey(luckSpinData);
            update.set(dbPrefixBuilder.buildWithLongAndRewind(luckSpinId), document);
        }

        VirtualThreadUtils.execute(() -> {
            collection.updateOne(Filters.eq(PlayerFields.playerId, playerId), update.getUpdateObject());
        });
    }
}
