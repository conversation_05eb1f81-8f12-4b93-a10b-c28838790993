package com.game.dao.player;

import com.game.engine.mongo.*;
import com.game.engine.util.misc.Tuple2;
import com.game.entity.player.PlayerFields;
import com.game.entity.player.promote.*;
import com.game.utils.VirtualThreadUtils;
import com.mongodb.client.MongoCollection;
import com.mongodb.client.model.Filters;
import io.netty.util.internal.StringUtil;
import it.unimi.dsi.fastutil.ints.IntList;
import it.unimi.dsi.fastutil.objects.ObjectList;
import org.bson.BsonValue;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;

import java.util.List;
import java.util.stream.Collectors;

public class PlayerPromoteDao extends ShardEntityDao<PlayerPromote> {

    private final MongoTemplate ops;
    private final MongoCollection<PlayerPromote> collection;

    public PlayerPromoteDao(MongoConverterMrg converterMrg, HandlerCollection<PlayerPromote> handler) {
        super(converterMrg, handler);
        this.ops = DBConnectionMrg.getInstance().getMongoTemplate();
        this.collection = DBConnectionMrg.getInstance().getCollection("player_promote", entityClass());
    }

    @Override
    public Class<PlayerPromote> entityClass() {
        return PlayerPromote.class;
    }

    public void insert(PlayerPromote playerPromote) {
        if (playerPromote.getPlayerId() <= 0) {
            throw new IllegalArgumentException("playerPromote.getPlayerId() <= 0");
        }
        VirtualThreadUtils.execute(() -> {
            collection.insertOne(playerPromote);
        });
    }

    public PlayerPromote getById(long playerId) {
        return ops.findOne(new Query(Criteria.where(PlayerPromoteFields.playerId).is(playerId)), PlayerPromote.class);
    }

    public void updatePromotion(long playerId, Update update) {
        if (update.getUpdateObject().isEmpty()) {
            return;
        }

        VirtualThreadUtils.execute(() -> {
            collection.updateOne(Filters.eq(PlayerPromoteFields.playerId, playerId), update.getUpdateObject());
        });
    }

    public void updatePromotionField(long playerId, String name, Object value) {
        final Update update = new Update();
        update.set(name, value);

        VirtualThreadUtils.execute(() -> {
            collection.updateOne(Filters.eq(PlayerPromoteFields.playerId, playerId), update.getUpdateObject());
        });
    }

    public void updateCommissionRewards(PlayerPromote playerPromote, IntList changedCurrencies) {
        if (changedCurrencies.isEmpty()) {
            return;
        }
        final DBPrefixBuilder dbPrefixBuilder = new DBPrefixBuilder();
        dbPrefixBuilder.append(PlayerPromoteFields.commissionRewardsMap);
        final Update update = new Update();
        for (int i = 0; i < changedCurrencies.size(); i++) {
            final int currencyId = changedCurrencies.getInt(i);
            final CommissionRewards commissionRewards = playerPromote.getCommissionRewardsMap().get(currencyId);
            if (commissionRewards == null) {
                throw new IllegalArgumentException("commissionRewards does not exist, Guid: " + currencyId);
            }
            BsonValue document = converterMrg.writeNoTypeKey(commissionRewards);
            update.set(dbPrefixBuilder.buildWithIntAndRewind(currencyId), document);
        }

        VirtualThreadUtils.execute(() -> {
            collection.updateOne(Filters.eq(PlayerPromoteFields.playerId, playerPromote.getPlayerId()), update.getUpdateObject());
        });
    }

    public void updateTeamRewards(PlayerPromote playerPromote, IntList changedCurrencies) {
        if (changedCurrencies.isEmpty()) {
            return;
        }
        final DBPrefixBuilder dbPrefixBuilder = new DBPrefixBuilder();
        dbPrefixBuilder.append(PlayerPromoteFields.teamRewardsMap);
        final Update update = new Update();
        for (int i = 0; i < changedCurrencies.size(); i++) {
            final int currencyId = changedCurrencies.getInt(i);
            final TeamRewards teamRewards = playerPromote.getTeamRewardsMap().get(currencyId);
            if (teamRewards == null) {
                throw new IllegalArgumentException("teamRewards does not exist, Guid: " + currencyId);
            }
            BsonValue document = converterMrg.writeNoTypeKey(teamRewards);
            update.set(dbPrefixBuilder.buildWithIntAndRewind(currencyId), document);
        }

        VirtualThreadUtils.execute(() -> {
            collection.updateOne(Filters.eq(PlayerPromoteFields.playerId, playerPromote.getPlayerId()), update.getUpdateObject());
        });
    }

    public void updateThreeLevelRewards(PlayerPromote playerPromote, IntList changedCurrencies) {
        if (changedCurrencies.isEmpty()) {
            return;
        }
        final DBPrefixBuilder dbPrefixBuilder = new DBPrefixBuilder();
        dbPrefixBuilder.append(PlayerPromoteFields.threeLevelRewardsMap);
        final Update update = new Update();
        for (int i = 0; i < changedCurrencies.size(); i++) {
            final int currencyId = changedCurrencies.getInt(i);
            final ThreeLevelRewards threeLevelRewards = playerPromote.getThreeLevelRewardsMap().get(currencyId);
            if (threeLevelRewards == null) {
                throw new IllegalArgumentException("threeLevelRewards does not exist, Guid: " + currencyId);
            }
            BsonValue document = converterMrg.writeNoTypeKey(threeLevelRewards);
            update.set(dbPrefixBuilder.buildWithIntAndRewind(currencyId), document);
        }

        VirtualThreadUtils.execute(() -> {
            collection.updateOne(Filters.eq(PlayerPromoteFields.playerId, playerPromote.getPlayerId()), update.getUpdateObject());
        });
    }

    public void updateInsertReferralCode(PlayerPromote playerPromote, ObjectList<String> insertedCode) {
        updateReferralCode(playerPromote, insertedCode);
    }

    private void updateReferralCode(PlayerPromote playerPromote, ObjectList<String> insertedCode) {
        if (insertedCode.isEmpty()) {
            return;
        }

        final DBPrefixBuilder dbPrefixBuilder = new DBPrefixBuilder();
        dbPrefixBuilder.append(PlayerPromoteFields.referralCodeMap)
                .build();

        Update update = new Update();
        //添加
        for (int i = 0; i < insertedCode.size(); i++) {
            final String updateCode = insertedCode.get(i);
            ReferralCode referralCode = playerPromote.getReferralCodeMap().get(updateCode);
            if (referralCode == null) {
                throw new IllegalArgumentException("referralCode does not exist, code: " + updateCode);
            }
            BsonValue document = converterMrg.writeNoTypeKey(referralCode);
            update.set(dbPrefixBuilder.buildAndRewind(updateCode), document);
        }

        VirtualThreadUtils.execute(() -> {
            collection.updateOne(Filters.eq(PlayerPromoteFields.playerId, playerPromote.getPlayerId()), update.getUpdateObject());
        });
    }

    public List<PlayerPromote> loadAllBySuperiorId(long playerId, int skip, int limit) {
        return ops.find(new Query(Criteria.where(PlayerPromoteFields.superiorId).is(playerId)).with(Sort.by(Sort.Direction.DESC, PlayerPromoteFields.createTime))
                .skip(skip).limit(limit), PlayerPromote.class);
    }

    public int countAllTeam(List<Long> playerId) {
        return (int) ops.count(new Query(Criteria.where(PlayerPromoteFields.superiorId).in(playerId)), PlayerPromote.class);
    }

    public List<Long> findByCodeFriends(String code) {
        final Query query = new Query();
        query.addCriteria(Criteria.where(PlayerPromoteFields.superiorCode).is(code));
        final List<PlayerPromote> playerPromotes = ops.find(query, PlayerPromote.class);
        return playerPromotes.stream().map(PlayerPromote::getPlayerId).collect(Collectors.toList());
    }

    public List<PlayerPromote> findByCodeAllFriends(List<String> code) {
        final Query query = new Query();
        query.addCriteria(Criteria.where(PlayerPromoteFields.superiorCode).in(code));
        return ops.find(query, PlayerPromote.class);
    }

    public PlayerPromote findUserNameOrUserIdFriend(List<String> referralCodes, String userName, long userId) {
        final Query query = new Query();
        query.addCriteria(Criteria.where(PlayerPromoteFields.superiorCode).in(referralCodes));
        if (!StringUtil.isNullOrEmpty(userName)) {
            query.addCriteria(Criteria.where(PlayerPromoteFields.playerName).regex(".*" + userName + ".*", "i"));
        }
        if (userId != 0) {
            query.addCriteria(Criteria.where(PlayerPromoteFields.playerId).is(userId));
        }
        return ops.findOne(query, PlayerPromote.class);
    }

    public PlayerPromote findByUserName(String userName) {
        final Query query = new Query();
        if (!StringUtil.isNullOrEmpty(userName)) {
            query.addCriteria(Criteria.where(PlayerPromoteFields.playerName).regex(".*" + userName + ".*", "i"));
        }
        return ops.findOne(query, PlayerPromote.class);
    }

    public Tuple2<Integer, List<PlayerPromote>> findByCodeFriends(List<String> referralCodes, long registerStart, long registerEnd, int skip, int pageSize) {
        final Query query = new Query();
        query.addCriteria(Criteria.where(PlayerPromoteFields.superiorCode).in(referralCodes));
        if (registerStart != 0 && registerEnd != 0) {
            query.addCriteria(Criteria.where(PlayerPromoteFields.createTime).gte(registerStart).lt(registerEnd));
        }
        final int count = (int) ops.count(query, PlayerPromote.class);
        final List<PlayerPromote> playerPromoteList = ops.find(query.skip(skip).limit(pageSize), PlayerPromote.class);
        return new Tuple2<>(count, playerPromoteList);
    }

    public Tuple2<Integer, List<PlayerPromote>> findByCodeFriends(List<String> referralCodes) {
        final Query query = new Query();
        query.addCriteria(Criteria.where(PlayerPromoteFields.superiorCode).in(referralCodes));

        final int count = (int) ops.count(query, PlayerPromote.class);
        final List<PlayerPromote> playerPromoteList = ops.find(query, PlayerPromote.class);
        return new Tuple2<>(count, playerPromoteList);
    }

    public int countFriends(List<String> referralCodes) {
        final Query query = new Query();
        query.addCriteria(Criteria.where(PlayerPromoteFields.superiorCode).in(referralCodes));
        return (int) ops.count(query, PlayerPromote.class);
    }

    public PlayerPromote findUserNameOrUserIdTeamFriend(List<String> referralCodes, String userName, long userId) {
        final Query query = new Query();
        query.addCriteria(Criteria.where(PlayerPromoteFields.teamCode).in(referralCodes));
        if (!StringUtil.isNullOrEmpty(userName)) {
            query.addCriteria(Criteria.where(PlayerPromoteFields.playerName).regex(".*" + userName + ".*", "i"));
        }
        if (userId != 0) {
            query.addCriteria(Criteria.where(PlayerPromoteFields.playerId).is(userId));
        }
        return ops.findOne(query, PlayerPromote.class);
    }

    public int findByCodeAllTeamFriends(List<String> referralCodes) {
        final Query query = new Query();
        query.addCriteria(Criteria.where(PlayerPromoteFields.teamCode).in(referralCodes));
        return (int) ops.count(query, PlayerPromote.class);
    }

    public Tuple2<Integer, List<PlayerPromote>> findByCodeTeamFriends(List<String> referralCodes, long registerStart, long registerEnd, int skip, int pageSize) {
        final Query query = new Query();
        query.addCriteria(Criteria.where(PlayerPromoteFields.teamCode).in(referralCodes));
        if (registerStart != 0 && registerEnd != 0) {
            query.addCriteria(Criteria.where(PlayerPromoteFields.createTime).gte(registerStart).lt(registerEnd));
        }
        final int count = (int) ops.count(query, PlayerPromote.class);
        final List<PlayerPromote> playerPromoteList = ops.find(query.skip(skip).limit(pageSize), PlayerPromote.class);
        return new Tuple2<>(count, playerPromoteList);
    }

    public PlayerPromote findUserNameOrUserIdThreeLevelFriend(List<String> referralCodes, String userName, long userId) {
        final Query query = new Query();
        query.addCriteria(Criteria.where(PlayerPromoteFields.threeLevelCode).in(referralCodes));
        if (!StringUtil.isNullOrEmpty(userName)) {
            query.addCriteria(Criteria.where(PlayerPromoteFields.playerName).regex(".*" + userName + ".*", "i"));
        }
        if (userId != 0) {
            query.addCriteria(Criteria.where(PlayerPromoteFields.playerId).is(userId));
        }
        return ops.findOne(query, PlayerPromote.class);
    }

    public int findByCodeAllThreeLevelFriends(List<String> referralCodes) {
        final Query query = new Query();
        query.addCriteria(Criteria.where(PlayerPromoteFields.threeLevelCode).in(referralCodes));
        return (int) ops.count(query, PlayerPromote.class);
    }

    public Tuple2<Integer, List<PlayerPromote>> findByCodeThreeLevelFriends(List<String> referralCodes, long registerStart, long registerEnd, int skip, int pageSize) {
        final Query query = new Query();
        query.addCriteria(Criteria.where(PlayerPromoteFields.threeLevelCode).in(referralCodes));
        if (registerStart != 0 && registerEnd != 0) {
            query.addCriteria(Criteria.where(PlayerPromoteFields.createTime).gte(registerStart).lt(registerEnd));
        }
        final int count = (int) ops.count(query, PlayerPromote.class);
        final List<PlayerPromote> playerPromoteList = ops.find(query.skip(skip).limit(pageSize), PlayerPromote.class);
        return new Tuple2<>(count, playerPromoteList);
    }
}
