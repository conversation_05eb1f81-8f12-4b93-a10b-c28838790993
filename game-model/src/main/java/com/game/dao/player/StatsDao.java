package com.game.dao.player;

import com.game.engine.mongo.DBPrefixBuilder;
import com.game.engine.mongo.HandlerCollection;
import com.game.engine.mongo.MongoConverterMrg;
import com.game.entity.player.Player;
import com.game.entity.player.PlayerFields;
import com.game.entity.player.promote.PlayerPromoteFields;
import com.game.entity.player.stats.Stats;
import com.game.entity.player.stats.StatsInfo;
import com.game.entity.player.stats.StatsInfoFields;
import com.game.utils.VirtualThreadUtils;
import com.mongodb.client.MongoCollection;
import com.mongodb.client.model.Filters;
import it.unimi.dsi.fastutil.ints.IntList;
import org.bson.BsonValue;
import org.springframework.data.mongodb.core.query.Update;

public class StatsDao {
    private final MongoConverterMrg converterMrg;
    private final HandlerCollection<Player> handler;
    private final MongoCollection<Player> collection;

    public StatsDao(MongoConverterMrg converterMrg, HandlerCollection<Player> handler, MongoCollection<Player> collection) {
        this.converterMrg = converterMrg;
        this.handler = handler;
        this.collection = collection;
    }

    public void updateStats(long playerId, StatsInfo statsInfo, IntList updateStats) {
        if (updateStats.isEmpty()) {
            return;
        }

        final DBPrefixBuilder dbPrefixBuilder = new DBPrefixBuilder();
        dbPrefixBuilder.append(PlayerFields.statsInfo)
                .append(StatsInfoFields.statsMap)
                .build();

        Update update = new Update();
        //添加
        for (int i = 0; i < updateStats.size(); i++) {
            final int updateCurrencyId = updateStats.getInt(i);
            Stats stats = statsInfo.getStatsMap().get(updateCurrencyId);
            if (stats == null) {
                throw new IllegalArgumentException("stats does not exist, Guid: " + updateCurrencyId);
            }
            BsonValue document = converterMrg.writeNoTypeKey(stats);
            update.set(dbPrefixBuilder.buildWithLongAndRewind(updateCurrencyId), document);
        }

        VirtualThreadUtils.execute(() -> {
            collection.updateOne(Filters.eq(PlayerFields.playerId, playerId), update.getUpdateObject());
        });
    }
}
