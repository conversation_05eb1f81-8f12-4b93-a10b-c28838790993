package com.game.dao.player;

import com.game.engine.mongo.DBPrefixBuilder;
import com.game.engine.mongo.HandlerCollection;
import com.game.engine.mongo.MongoConverterMrg;
import com.game.entity.player.Player;
import com.game.entity.player.PlayerFields;
import com.game.entity.player.promote.PlayerPromoteFields;
import com.game.entity.player.quest.*;
import com.game.utils.VirtualThreadUtils;
import com.mongodb.client.MongoCollection;
import com.mongodb.client.model.Filters;
import com.mongodb.client.model.Updates;
import it.unimi.dsi.fastutil.ints.IntCollection;
import org.bson.conversions.Bson;
import org.springframework.data.mongodb.core.query.Update;

public class QuestDao {

    private final MongoConverterMrg converterMrg;
    private final HandlerCollection<Player> handler;
    private final MongoCollection<Player> collection;

    public QuestDao(MongoConverterMrg converterMrg, HandlerCollection<Player> handler, MongoCollection<Player> collection) {
        this.converterMrg = converterMrg;
        this.handler = handler;
        this.collection = collection;
    }

    /**
     * 更新任务列表信息
     *
     * @param questInfo      任务信息，其中questInfo需存在插入、更新列表，且不存在删除列表
     * @param insertedQuests 插入的列表
     * @param deletedQuests  删除列表
     * @param updatedQuests  更新列表
     */
    public void updateQuest(long playerId, QuestInfo questInfo, IntCollection insertedQuests,
                            IntCollection deletedQuests, IntCollection updatedQuests) {
        if (insertedQuests.isEmpty() && deletedQuests.isEmpty() && updatedQuests.isEmpty()) {
            return;
        }
        final DBPrefixBuilder builder = new DBPrefixBuilder();
        builder.append(PlayerFields.questInfo)
                .append(QuestInfoFields.questInfoMap);

        final Update update = new Update();

        appendUnsetQuest(update, questInfo, builder, deletedQuests);

        appendSetQuest(update, questInfo, builder, insertedQuests);
        appendSetQuest(update, questInfo, builder, updatedQuests);

        VirtualThreadUtils.execute(() -> {
            collection.updateOne(Filters.eq(PlayerFields.playerId, playerId), update.getUpdateObject());
        });
    }

    public void updateQuestState(long playerId, QuestInfo questInfo, int questId) {
        final SingleQuestInfo singleQuestInfo = questInfo.getQuestInfoMap().get(questId);
        if (singleQuestInfo == null) {
            throw new IllegalArgumentException("quest: " + questId + " is absent");
        }

        final DBPrefixBuilder builder = new DBPrefixBuilder();
        builder.append(PlayerFields.questInfo)
                .append(QuestInfoFields.questInfoMap)
                .appendInt(questId)
                .append(SingleQuestInfoFields.state);

        final Update update = new Update();
        update.set(builder.build(), singleQuestInfo.getState());

        VirtualThreadUtils.execute(() -> {
            collection.updateOne(Filters.eq(PlayerFields.playerId, playerId), update.getUpdateObject());
        });
    }

    private void appendUnsetQuest(Update update, QuestInfo questInfo, DBPrefixBuilder builder, IntCollection deletedQuests) {
        for (int questId : deletedQuests) {
            if (questInfo.getQuestInfoMap().get(questId) != null) {
                throw new IllegalArgumentException("quest: " + questId + " is not deleted");
            }
            update.unset(builder.buildWithIntAndRewind(questId));
        }
    }

    private void appendSetQuest(Update update, QuestInfo questInfo, DBPrefixBuilder builder, IntCollection insertedQuests) {
        for (int questId : insertedQuests) {
            final SingleQuestInfo singleQuestInfo = questInfo.getQuestInfoMap().get(questId);
            if (singleQuestInfo == null) {
                throw new IllegalArgumentException("quest: " + questId + " is absent");
            }
            update.set(builder.buildWithIntAndRewind(questId), converterMrg.writeNoTypeKey(singleQuestInfo));
        }
    }

    public void updateAccumulatedRewards(long playerId, QuestInfo questInfo) {
        final DBPrefixBuilder dbPrefixBuilder = new DBPrefixBuilder();
        DBPrefixBuilder builder = dbPrefixBuilder.append(PlayerFields.questInfo)
                .append(QuestInfoFields.accumulatedRewards);

        final Update update = new Update();
        update.set(builder.build(), converterMrg.writeNoTypeKey(questInfo.getAccumulatedRewards()));

        VirtualThreadUtils.execute(() -> {
            collection.updateOne(Filters.eq(PlayerFields.playerId, playerId), update.getUpdateObject());
        });
    }

    public void updateQuestInfo(long playerId, QuestInfo questInfo) {
        final DBPrefixBuilder dbPrefixBuilder = new DBPrefixBuilder();
        final String key = dbPrefixBuilder.append(PlayerFields.questInfo)
                .build();

        Update update = new Update();
        update.set(key, converterMrg.writeNoTypeKey(questInfo));

        VirtualThreadUtils.execute(() -> {
            collection.updateOne(Filters.eq(PlayerFields.playerId, playerId), update.getUpdateObject());
        });
    }

}
