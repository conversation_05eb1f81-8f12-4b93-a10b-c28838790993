package com.game.dao.player;

import com.game.engine.mongo.DBPrefixBuilder;
import com.game.engine.mongo.HandlerCollection;
import com.game.engine.mongo.MongoConverterMrg;
import com.game.entity.player.Player;
import com.game.entity.player.PlayerFields;
import com.game.entity.player.promote.PlayerPromoteFields;
import com.game.entity.player.vip.VipClub;
import com.game.entity.player.vip.VipClubFields;
import com.game.utils.VirtualThreadUtils;
import com.mongodb.client.MongoCollection;
import com.mongodb.client.model.Filters;
import org.springframework.data.mongodb.core.query.Update;

public class VipClubDao {

    private final MongoConverterMrg converterMrg;
    private final HandlerCollection<Player> handler;
    private final MongoCollection<Player> collection;

    public VipClubDao(MongoConverterMrg converterMrg, HandlerCollection<Player> handler, MongoCollection<Player> collection) {
        this.converterMrg = converterMrg;
        this.handler = handler;
        this.collection = collection;
    }

    public void updateReceiveDays(long playerId, VipClub vipClub) {
        final DBPrefixBuilder dbPrefixBuilder = new DBPrefixBuilder();
        dbPrefixBuilder.append(PlayerFields.vipClub)
                .build();

        final Update update = new Update();
        update.set(dbPrefixBuilder.buildAndRewind(VipClubFields.currDay), vipClub.getCurrDay())
                .set(dbPrefixBuilder.buildAndRewind(VipClubFields.activation), vipClub.isActivation())
                .set(dbPrefixBuilder.buildAndRewind(VipClubFields.receiveDays), vipClub.getReceiveDays());

        VirtualThreadUtils.execute(() -> {
            collection.updateOne(Filters.eq(PlayerFields.playerId, playerId), update.getUpdateObject());
        });
    }

    public void updateReceiveReward(long playerId, VipClub vipClub) {
        final DBPrefixBuilder dbPrefixBuilder = new DBPrefixBuilder();
        dbPrefixBuilder.append(PlayerFields.vipClub)
                .build();

        final Update update = new Update();
        update.set(dbPrefixBuilder.buildAndRewind(VipClubFields.receiveReward), vipClub.getReceiveReward());

        VirtualThreadUtils.execute(() -> {
            collection.updateOne(Filters.eq(PlayerFields.playerId, playerId), update.getUpdateObject());
        });
    }

    public void updateDailyWagered(long playerId, VipClub vipClub) {
        final DBPrefixBuilder dbPrefixBuilder = new DBPrefixBuilder();
        dbPrefixBuilder.append(PlayerFields.vipClub)
                .build();

        final Update update = new Update();
        update.set(dbPrefixBuilder.buildAndRewind(VipClubFields.receiveReward), vipClub.getReceiveReward())
                .set(dbPrefixBuilder.buildAndRewind(VipClubFields.dailyWageredMap), vipClub.getDailyWageredMap());

        VirtualThreadUtils.execute(() -> {
            collection.updateOne(Filters.eq(PlayerFields.playerId, playerId), update.getUpdateObject());
        });
    }

    public void updateWeeklyWagered(long playerId, VipClub vipClub) {
        final DBPrefixBuilder dbPrefixBuilder = new DBPrefixBuilder();
        dbPrefixBuilder.append(PlayerFields.vipClub)
                .build();

        final Update update = new Update();
        update.set(dbPrefixBuilder.buildAndRewind(VipClubFields.receiveReward), vipClub.getReceiveReward())
                .set(dbPrefixBuilder.buildAndRewind(VipClubFields.weeklyWageredMap), vipClub.getWeeklyWageredMap());

        VirtualThreadUtils.execute(() -> {
            collection.updateOne(Filters.eq(PlayerFields.playerId, playerId), update.getUpdateObject());
        });
    }

    public void updateMonthlyWagered(long playerId, VipClub vipClub) {
        final DBPrefixBuilder dbPrefixBuilder = new DBPrefixBuilder();
        dbPrefixBuilder.append(PlayerFields.vipClub)
                .build();

        final Update update = new Update();
        update.set(dbPrefixBuilder.buildAndRewind(VipClubFields.receiveReward), vipClub.getReceiveReward())
                .set(dbPrefixBuilder.buildAndRewind(VipClubFields.monthlyWageredMap), vipClub.getMonthlyWageredMap());

        VirtualThreadUtils.execute(() -> {
            collection.updateOne(Filters.eq(PlayerFields.playerId, playerId), update.getUpdateObject());
        });
    }
}
