package com.game.dao.player;

import com.game.engine.mongo.DBPrefixBuilder;
import com.game.engine.mongo.EntityDao;
import com.game.engine.mongo.HandlerCollection;
import com.game.engine.mongo.MongoConverterMrg;
import com.game.entity.player.Player;
import com.game.entity.player.PlayerFields;
import com.game.entity.player.activity.mysteryBonus.MysteryBonusInfo;
import com.game.entity.player.activity.mysteryBonus.MysteryBonusInfoFields;
import com.game.entity.player.activity.piggyBank.PiggyBankInfo;
import com.game.entity.player.activity.piggyBank.PiggyBankInfoFields;
import com.game.utils.VirtualThreadUtils;
import com.mongodb.client.MongoCollection;
import com.mongodb.client.model.Filters;
import com.mongodb.client.model.Updates;
import org.bson.conversions.Bson;
import org.springframework.data.mongodb.core.query.Update;

public class PiggyBankDao {

    private final MongoConverterMrg converterMrg;
    private final HandlerCollection<Player> handler;
    private final MongoCollection<Player> collection;

    public PiggyBankDao(MongoConverterMrg converterMrg, HandlerCollection<Player> handler, MongoCollection<Player> collection) {
        this.converterMrg = converterMrg;
        this.handler = handler;
        this.collection = collection;
    }

    public void updateReceive(long playerId, PiggyBankInfo piggyBankInfo) {
        final DBPrefixBuilder builder = EntityDao.builderPool.get()
                .append(PlayerFields.piggyBankInfo)
                .append(PiggyBankInfoFields.receive);

        final Update update = new Update();
        update.set(builder.build(), piggyBankInfo.getReceive());

        VirtualThreadUtils.execute(() -> {
            collection.updateOne(Filters.eq(PlayerFields.playerId, playerId), update.getUpdateObject());
        });
    }
}
