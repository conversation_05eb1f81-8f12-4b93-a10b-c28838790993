package com.game.dao.player;

import com.game.engine.mongo.DBPrefixBuilder;
import com.game.engine.mongo.HandlerCollection;
import com.game.engine.mongo.MongoConverterMrg;
import com.game.entity.player.Player;
import com.game.entity.player.PlayerFields;
import com.game.entity.player.activity.wageredRebates.WageredRebatesInfo;
import com.game.entity.player.activity.wageredRebates.WageredRebatesInfoFields;
import com.game.utils.VirtualThreadUtils;
import com.mongodb.client.MongoCollection;
import com.mongodb.client.model.Filters;
import it.unimi.dsi.fastutil.ints.IntList;
import org.springframework.data.mongodb.core.query.Update;

public class WageredRebatesDao {

    private final MongoConverterMrg converterMrg;
    private final HandlerCollection<Player> handler;
    private final MongoCollection<Player> collection;

    public WageredRebatesDao(MongoConverterMrg converterMrg, HandlerCollection<Player> handler, MongoCollection<Player> collection) {
        this.converterMrg = converterMrg;
        this.handler = handler;
        this.collection = collection;
    }

    public void updateReceive(long playerId, WageredRebatesInfo wageredRebatesInfo) {
        final DBPrefixBuilder builder = new DBPrefixBuilder();
        builder.append(PlayerFields.wageredRebatesInfo)
                .append(WageredRebatesInfoFields.status);

        final Update update = new Update();
        update.set(builder.build(), wageredRebatesInfo.isStatus());

        VirtualThreadUtils.execute(() -> {
            collection.updateOne(Filters.eq(PlayerFields.playerId, playerId), update.getUpdateObject());
        });
    }

}
