//package com.game.dao.player;
//
//import com.game.engine.mongo.DBPrefixBuilder;
//import com.game.engine.mongo.EntityDao;
//import com.game.engine.mongo.HandlerCollection;
//import com.game.engine.mongo.MongoConverterMrg;
//import com.game.entity.player.Player;
//import com.game.entity.player.PlayerFields;
//import com.game.entity.player.activity.ActivityData;
//import com.game.entity.player.activity.ActivityInfo;
//import com.game.entity.player.activity.ActivityInfoFields;
//import com.mongodb.client.MongoCollection;
//import it.unimi.dsi.fastutil.ints.IntList;
//import it.unimi.dsi.fastutil.ints.IntLists;
//import org.bson.BsonValue;
//import org.springframework.data.mongodb.core.query.Update;
//
//public class ActivityInfoDao {
//
//    private final MongoConverterMrg converterMrg;
//    private final HandlerCollection<Player> handler;
//    private final MongoCollection<Player> collection;
//
//    public ActivityInfoDao(MongoConverterMrg converterMrg, HandlerCollection<Player> handler, MongoCollection<Player> collection) {
//        this.converterMrg = converterMrg;
//        this.handler = handler;
//        this.collection = collection;
//    }
//
//    public void updateInsertActivity(long playerId, ActivityInfo activityInfo, IntList insertedActivity) {
//        updateActivityData(playerId, activityInfo, insertedActivity, IntLists.EMPTY_LIST);
//    }
//
//    public void updateDeleteActivity(long playerId, ActivityInfo activityInfo, IntList deleteActivity) {
//        updateActivityData(playerId, activityInfo, IntLists.EMPTY_LIST, deleteActivity);
//    }
//
//    public void updateActivityData(long playerId, ActivityInfo activityInfo, IntList insertActivity, IntList deleteActivity) {
//        if (insertActivity.isEmpty() && deleteActivity.isEmpty()) {
//            return;
//        }
//
//        final DBPrefixBuilder dbPrefixBuilder = EntityDao.builderPool.get();
//        dbPrefixBuilder.append(PlayerFields.activityInfo)
//                .append(ActivityInfoFields.activityDataMap)
//                .build();
//
//        Update update = new Update();
//        //添加
//        for (int i = 0; i < insertActivity.size(); i++) {
//            final int activityId = insertActivity.getInt(i);
//            ActivityData activityData = activityInfo.getActivityDataMap().get(activityId);
//            if (activityData == null) {
//                throw new IllegalArgumentException("activityData does not exist, Guid: " + activityId);
//            }
//            BsonValue document = converterMrg.writeNoTypeKey(activityData);
//            update.set(dbPrefixBuilder.buildWithIntAndRewind(activityId), document);
//        }
//
//        //删除
//        for (int i = 0; i < deleteActivity.size(); i++) {
//            final int activityId = deleteActivity.getInt(i);
//            if (activityInfo.getActivityDataMap().containsKey(activityId)) {
//                throw new IllegalArgumentException("activityData is not deleted, Guid: " + activityId);
//            }
//            update.unset(dbPrefixBuilder.buildWithIntAndRewind(activityId));
//        }
//
//        handler.yibuUpdateOne(PlayerFields.playerId, playerId, update.getUpdateObject(), null);
//        EntityDao.builderPool.returnOne(dbPrefixBuilder);
//    }
//
//
//}
