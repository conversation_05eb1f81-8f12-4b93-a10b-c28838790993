package com.game.dao.player;

import com.game.engine.mongo.DBPrefixBuilder;
import com.game.engine.mongo.HandlerCollection;
import com.game.engine.mongo.MongoConverterMrg;
import com.game.entity.player.Player;
import com.game.entity.player.PlayerFields;
import com.game.entity.player.activity.rewardBox.RewardBoxInfo;
import com.game.entity.player.activity.rewardBox.RewardBoxInfoFields;
import com.game.entity.player.promote.PlayerPromoteFields;
import com.game.utils.VirtualThreadUtils;
import com.mongodb.client.MongoCollection;
import com.mongodb.client.model.Filters;
import org.springframework.data.mongodb.core.query.Update;

public class RewardBoxDao {

    private final MongoConverterMrg converterMrg;
    private final HandlerCollection<Player> handler;
    private final MongoCollection<Player> collection;

    public RewardBoxDao(MongoConverterMrg converterMrg, HandlerCollection<Player> handler, MongoCollection<Player> collection) {
        this.converterMrg = converterMrg;
        this.handler = handler;
        this.collection = collection;
    }

    public void updateReceive(long playerId, RewardBoxInfo rewardBoxInfo) {
        final DBPrefixBuilder builder = new DBPrefixBuilder();
        builder.append(PlayerFields.rewardBoxInfo)
                .append(RewardBoxInfoFields.receive);

        final Update update = new Update();
        update.set(builder.build(), rewardBoxInfo.getReceive());

        VirtualThreadUtils.execute(() -> {
            collection.updateOne(Filters.eq(PlayerFields.playerId, playerId), update.getUpdateObject());
        });
    }
}
