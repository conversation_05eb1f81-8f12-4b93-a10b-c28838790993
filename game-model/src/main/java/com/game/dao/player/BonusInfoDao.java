package com.game.dao.player;

import com.game.engine.mongo.DBPrefixBuilder;
import com.game.engine.mongo.HandlerCollection;
import com.game.engine.mongo.MongoConverterMrg;
import com.game.entity.player.Player;
import com.game.entity.player.PlayerFields;
import com.game.entity.player.bonus.*;
import com.game.utils.VirtualThreadUtils;
import com.mongodb.client.MongoCollection;
import com.mongodb.client.model.Filters;
import it.unimi.dsi.fastutil.ints.IntList;
import org.springframework.data.mongodb.core.query.Update;

public class BonusInfoDao {

    private final MongoConverterMrg converterMrg;
    private final HandlerCollection<Player> handler;
    private final MongoCollection<Player> collection;

    public BonusInfoDao(MongoConverterMrg converterMrg, HandlerCollection<Player> handler, MongoCollection<Player> collection) {
        this.converterMrg = converterMrg;
        this.handler = handler;

        this.collection = collection;
    }


    public void updateBonusInfo(long playerId, String fields, long time) {
        final DBPrefixBuilder dbPrefixBuilder = new DBPrefixBuilder();
        dbPrefixBuilder.append(PlayerFields.bonusInfo)
                .build();

        final Update update = new Update();
        update.set(dbPrefixBuilder.buildAndRewind(fields), time);

        VirtualThreadUtils.execute(() -> {
            collection.updateOne(Filters.eq(PlayerFields.playerId, playerId), update.getUpdateObject());
        });
    }

    public void updateBonusProcessInfo(Player player, BonusInfo bonusInfo, IntList changedBonusType) {
        if (changedBonusType.isEmpty()) {
            return;
        }

        final DBPrefixBuilder dbPrefixBuilder = new DBPrefixBuilder();
        dbPrefixBuilder.append(PlayerFields.bonusInfo)
                .append(BonusInfoFields.bonusProcessInfoMap);

        final Update update = new Update();
        for (int i = 0; i < changedBonusType.size(); i++) {
            final int bonusType = changedBonusType.getInt(i);
            final BonusProcessInfo bonusProcessInfo = bonusInfo.getBonusProcessInfo(bonusType);
            if (bonusProcessInfo == null) {
                throw new IllegalArgumentException("bonusType does not exist, Guid: " + bonusType);
            }
            update.set(dbPrefixBuilder.buildWithIntAndRewind(bonusType), converterMrg.writeNoTypeKey(bonusProcessInfo));
        }

        VirtualThreadUtils.execute(() -> {
            collection.updateOne(Filters.eq(PlayerFields.playerId, player.getPlayerId()), update.getUpdateObject());
        });
    }

    public void updateBonusDetails(Player player, int bonusType, BonusDetailsInfo bonusDetailsInfo, IntList changedCurrencies) {
        if (changedCurrencies.isEmpty()) {
            return;
        }

        final DBPrefixBuilder dbPrefixBuilder = new DBPrefixBuilder();
        dbPrefixBuilder.append(PlayerFields.bonusInfo)
                .append(BonusInfoFields.bonusDetailsMap)
                .appendInt(bonusType)
                .append(BonusDetailsInfoFields.bonusMap);

        final Update update = new Update();
        for (int i = 0; i < changedCurrencies.size(); i++) {
            final int currencyType = changedCurrencies.getInt(i);
            final double currencyValue = bonusDetailsInfo.getBonusMap().getOrDefault(currencyType, 0d);
            update.set(dbPrefixBuilder.buildWithIntAndRewind(currencyType), currencyValue);
        }

        VirtualThreadUtils.execute(() -> {
            collection.updateOne(Filters.eq(PlayerFields.playerId, player.getPlayerId()), update.getUpdateObject());
        });
    }
}
