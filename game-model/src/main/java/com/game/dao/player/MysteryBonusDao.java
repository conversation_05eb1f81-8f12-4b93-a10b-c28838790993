package com.game.dao.player;

import com.game.engine.mongo.DBPrefixBuilder;
import com.game.engine.mongo.HandlerCollection;
import com.game.engine.mongo.MongoConverterMrg;
import com.game.entity.player.Player;
import com.game.entity.player.PlayerFields;
import com.game.entity.player.activity.mysteryBonus.MysteryBonusInfo;
import com.game.entity.player.activity.mysteryBonus.MysteryBonusInfoFields;
import com.game.utils.VirtualThreadUtils;
import com.mongodb.client.MongoCollection;
import com.mongodb.client.model.Filters;
import com.mongodb.client.model.Updates;
import org.bson.conversions.Bson;
import org.springframework.data.mongodb.core.query.Update;

public class MysteryBonusDao {

    private final MongoConverterMrg converterMrg;
    private final HandlerCollection<Player> handler;
    private final MongoCollection<Player> collection;

    public MysteryBonusDao(MongoConverterMrg converterMrg, HandlerCollection<Player> handler, MongoCollection<Player> collection) {
        this.converterMrg = converterMrg;
        this.handler = handler;
        this.collection = collection;
    }

    public void updateReceive(long playerId, MysteryBonusInfo mysteryBonusInfo) {
        final DBPrefixBuilder builder = new DBPrefixBuilder();
        builder.append(PlayerFields.mysteryBonusInfo)
                .append(MysteryBonusInfoFields.receive);

        final Update update = new Update();
        update.set(builder.build(), mysteryBonusInfo.getReceive());

        VirtualThreadUtils.execute(() -> {
            collection.updateOne(Filters.eq(PlayerFields.playerId, playerId), update.getUpdateObject());
        });
    }
}
