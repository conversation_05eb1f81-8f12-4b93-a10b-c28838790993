package com.game.dao;

import com.game.engine.mongo.ShardEntity;
import org.bson.Document;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

public interface Converter {

    /**
     * 请确保你需要写入类型的情况下再调用改方法
     */
    <T> Document write(T entity);

    /**
     * 通常用在读外层对象
     */
    <T extends ShardEntity> T read(Document document, Class<T> typeClass);

    /**
     * 写一个对象但不保存类型信息（通常用在读内嵌对象）
     * <p>
     * 对于一个嵌套对象，通常不应该写入类型信息，否则可能导致解析错误。
     * 因为Spring在解析的时候，如果存在类型信息，则解析时会使用类型信息，从而导致丢失了所在的外部环境。
     * 最常见的情况就是{@link java.util.Map}和{@link List}
     * 如果直接写一个Map对象，则会在{@link Document}中写入Map的类型信息，从而解码时出现异常，因为失去了泛型信息。
     * <p>
     * 如果字段的声明类型是一个抽象类型或是{@link Object}，可能需要写入类型信息。
     * 比如我们在写入物品的特殊属性的时候，是需要写入类型信息的，否则无法准确的编解码。
     * (只要对象是一个嵌套对象，它就可以从它所在的类获得它的类型信息)
     */
    default <T> Document writeNoTypeKey(T entity) {
        final Document document = write(entity);
        document.remove("_class");
        return document;
    }

    /**
     * {@link #writeNoTypeKey(Object)}暂不支持直接序列化集合对象，因此需要在外部构建为{@link Document}的数组。
     */
    default <T> List<Document> writeCollectionNoTypeKey(Collection<T> collection) {
        final ArrayList<Document> result = new ArrayList<>(collection.size());
        for (T t : collection) {
            result.add(writeNoTypeKey(t));
        }
        return result;
    }

}
