package com.game.dao.promote;

import com.game.engine.mongo.*;
import com.game.engine.util.misc.Tuple2;
import com.game.entity.player.promote.*;
import com.game.utils.VirtualThreadUtils;
import com.mongodb.client.MongoCollection;
import com.mongodb.client.model.Filters;
import com.mongodb.client.model.Sorts;
import org.bson.Document;
import org.bson.conversions.Bson;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;

import java.util.ArrayList;
import java.util.List;

public class ThreeLevelRewardsNoteDao implements EntityDao<ThreeLevelRewardsNote> {

    private final MongoCollection<ThreeLevelRewardsNote> collection;
    private final MongoTemplate ops;

    public ThreeLevelRewardsNoteDao(DBConnectionMrg dbConnectionMrg) {
        this.ops = dbConnectionMrg.getMongoTemplate();
        this.collection = dbConnectionMrg.getCollection("threeLevelRewards_note", entityClass());
        final IndexDescAll indexDescAll = IndexDescAll.createWithAllIndexs(
                IndexDescSingle.createIndex(new IndexTypeAndName(Long.class, ThreeLevelRewardsNoteFields.playerId)),
                IndexDescSingle.createIndex(new IndexTypeAndName(Long.class, ThreeLevelRewardsNoteFields.time))
        );
        final MongoCollection<Document> mongoCollection = dbConnectionMrg.getMongoTemplate().getCollection("threeLevelRewards_note");
        ToolMongoIndexHelper.indexBuildWork(indexDescAll.getApplicationIndex(), null, mongoCollection);
    }

    @Override
    public Class<ThreeLevelRewardsNote> entityClass() {
        return ThreeLevelRewardsNote.class;
    }


    public void insert(ThreeLevelRewardsNote threeLevelRewardsNote) {
        if (threeLevelRewardsNote.getPlayerId() <= 0) {
            throw new IllegalArgumentException("threeLevelRewardsNote.getPlayerId() <= 0");
        }
        VirtualThreadUtils.execute(() -> collection.insertOne(threeLevelRewardsNote));
    }

    public Tuple2<Integer, List<ThreeLevelRewardsNote>> findByTime(long playerId, long startTime, long endTime, int skip, int limit) {
        final Bson query = Filters.and(
                Filters.eq(ThreeLevelRewardsNoteFields.playerId, playerId),
                Filters.gte(ThreeLevelRewardsNoteFields.time, startTime),
                Filters.lt(ThreeLevelRewardsNoteFields.time, endTime)
        );
        final int count = (int) collection.countDocuments(query);
        final List<ThreeLevelRewardsNote> threeLevelRewardsNoteList = collection.find(query).sort(Sorts.descending(ThreeLevelRewardsNoteFields.time)).skip(skip).limit(limit).into(new ArrayList<>());
        return new Tuple2<>(count, threeLevelRewardsNoteList);
    }


    /**
     * 60 day
     *
     * @param start
     * @param end
     * @return
     */
    public long delete(long start, long end) {
        final Criteria criteria = new Criteria();
        criteria.and(ThreeLevelRewardsNoteFields.time).gte(start).lte(end);

        int BATCH_SIZE = 1000;
        long totalDeleted = 0;
        while (true) {
            // 查询一批数据
            final Query query = new Query(criteria).limit(BATCH_SIZE);

            // 批量删除
            final long deletedCount = ops.remove(query, ThreeLevelRewardsNote.class).getDeletedCount();
            totalDeleted += deletedCount;

            if (deletedCount < BATCH_SIZE) {
                break;
            }
        }
        return totalDeleted;
    }

}
