package com.game.dao.promote;

import com.game.engine.mongo.*;
import com.game.engine.util.misc.Tuple2;
import com.game.entity.player.promote.CommissionRewardsNote;
import com.game.entity.player.promote.CommissionRewardsNoteFields;
import com.game.entity.player.promote.ReferralRewardsNote;
import com.game.entity.player.promote.ReferralRewardsNoteFields;
import com.game.utils.VirtualThreadUtils;
import com.mongodb.client.MongoCollection;
import com.mongodb.client.model.Filters;
import com.mongodb.client.model.Sorts;
import org.bson.Document;
import org.bson.conversions.Bson;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;

import java.util.ArrayList;
import java.util.List;

public class ReferralRewardsNoteDao implements EntityDao<ReferralRewardsNote> {

    private final MongoCollection<ReferralRewardsNote> collection;
    private final MongoTemplate ops;

    public ReferralRewardsNoteDao(DBConnectionMrg dbConnectionMrg) {
        this.ops = dbConnectionMrg.getMongoTemplate();
        this.collection = dbConnectionMrg.getCollection("referralRewards_note", entityClass());
        final IndexDescAll indexDescAll = IndexDescAll.createWithAllIndexs(
                IndexDescSingle.createIndex(new IndexTypeAndName(Long.class, ReferralRewardsNoteFields.playerId)),
                IndexDescSingle.createIndex(new IndexTypeAndName(Long.class, ReferralRewardsNoteFields.superiorId)),
                IndexDescSingle.createIndex(new IndexTypeAndName(Long.class, ReferralRewardsNoteFields.time))
        );
        final MongoCollection<Document> mongoCollection = dbConnectionMrg.getMongoTemplate().getCollection("referralRewards_note");
        ToolMongoIndexHelper.indexBuildWork(indexDescAll.getApplicationIndex(), null, mongoCollection);
    }

    @Override
    public Class<ReferralRewardsNote> entityClass() {
        return ReferralRewardsNote.class;
    }


    public void insert(ReferralRewardsNote referralRewardsNote) {
        if (referralRewardsNote.getPlayerId() <= 0) {
            throw new IllegalArgumentException("referralRewardsNote.getPlayerId() <= 0");
        }
        VirtualThreadUtils.execute(() -> collection.insertOne(referralRewardsNote));
    }

    public Tuple2<Integer, List<ReferralRewardsNote>> findByTime(long playerId, long startTime, long endTime, int skip, int limit) {
        final Bson query = Filters.and(
                Filters.eq(ReferralRewardsNoteFields.superiorId, playerId),
                Filters.gte(ReferralRewardsNoteFields.time, startTime),
                Filters.lt(ReferralRewardsNoteFields.time, endTime)
        );
        final int count = (int) collection.countDocuments(query);
        final List<ReferralRewardsNote> referralRewardsNotes = collection.find(query).sort(Sorts.descending(ReferralRewardsNoteFields.time)).skip(skip).limit(limit).into(new ArrayList<>());
        return new Tuple2<>(count, referralRewardsNotes);
    }


    /**
     * 60 day
     *
     * @param start
     * @param end
     * @return
     */
    public long delete(long start, long end) {
        final Criteria criteria = new Criteria();
        criteria.and(ReferralRewardsNoteFields.time).gte(start).lte(end);

        int BATCH_SIZE = 1000;
        long totalDeleted = 0;
        while (true) {
            // 查询一批数据
            final Query query = new Query(criteria).limit(BATCH_SIZE);

            // 批量删除
            final long deletedCount = ops.remove(query, ReferralRewardsNote.class).getDeletedCount();
            totalDeleted += deletedCount;

            if (deletedCount < BATCH_SIZE) {
                break;
            }
        }
        return totalDeleted;
    }
}
