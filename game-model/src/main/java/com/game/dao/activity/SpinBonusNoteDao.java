package com.game.dao.activity;

import com.game.engine.mongo.*;
import com.game.entity.activity.SpinBonusNote;
import com.game.entity.activity.SpinBonusNoteFields;
import com.game.entity.game.GameNoteFields;
import com.game.utils.VirtualThreadUtils;
import com.mongodb.client.MongoCollection;
import org.bson.Document;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;

import java.util.List;

public class SpinBonusNoteDao implements EntityDao<SpinBonusNote> {

    private final MongoCollection<SpinBonusNote> collection;
    private final MongoTemplate ops;

    public SpinBonusNoteDao(DBConnectionMrg dbConnectionMrg) {
        this.ops = dbConnectionMrg.getMongoTemplate();
        this.collection = dbConnectionMrg.getCollection("spinBonus_note", entityClass());
        final IndexDescAll indexDescAll = IndexDescAll.createWithAllIndexs(
                IndexDescSingle.createIndex(new IndexTypeAndName(Long.class, SpinBonusNoteFields.playerId)),
                IndexDescSingle.createIndex(new IndexTypeAndName(Long.class, SpinBonusNoteFields.createTime))
        );
        final MongoCollection<Document> mongoCollection = dbConnectionMrg.getMongoTemplate().getCollection("spinBonus_note");
        ToolMongoIndexHelper.indexBuildWork(indexDescAll.getApplicationIndex(), null, mongoCollection);
    }

    @Override
    public Class<SpinBonusNote> entityClass() {
        return SpinBonusNote.class;
    }

    public void insert(SpinBonusNote spinBonusNote) {
        if (spinBonusNote.getPlayerId() <= 0) {
            throw new IllegalArgumentException("spinBonusNote.getPlayerId() <= 0");
        }
        VirtualThreadUtils.execute(() -> collection.insertOne(spinBonusNote));
    }

    public List<SpinBonusNote> loadSpinBonusNote(String business_no, int turntable, int subType, long start, long end, int limit) {
        final Query query = new Query();
        query.addCriteria(Criteria.where(SpinBonusNoteFields.business_no).is(business_no));
        if (turntable > 0) {
            query.addCriteria(Criteria.where(SpinBonusNoteFields.turntable).is(turntable));
        }
        if (subType > 0) {
            query.addCriteria(Criteria.where(SpinBonusNoteFields.subType).is(subType));
        }
        if (start > 0 && end > 0) {
            query.addCriteria(Criteria.where(SpinBonusNoteFields.createTime).gte(start).lt(end));
        }
        return ops.find(query.with(Sort.by(Sort.Direction.DESC, GameNoteFields.createTime))
                .limit(limit), SpinBonusNote.class);
    }

    public long delete(long end) {
        final Criteria criteria = new Criteria();
        criteria.and(SpinBonusNoteFields.createTime).lt(end);

        int BATCH_SIZE = 1000;
        long totalDeleted = 0;
        while (true) {
            // 查询一批数据
            final Query query = new Query(criteria).limit(BATCH_SIZE);

            // 批量删除
            final long deletedCount = ops.remove(query, SpinBonusNote.class).getDeletedCount();
            totalDeleted += deletedCount;

            if (deletedCount < BATCH_SIZE) {
                break;
            }
        }
        return totalDeleted;
    }

}
