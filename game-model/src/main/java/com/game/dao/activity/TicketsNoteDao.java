package com.game.dao.activity;

import com.game.engine.math.MathUtils;
import com.game.engine.mongo.*;
import com.game.engine.util.misc.Tuple2;
import com.game.entity.activity.SpinBonusNoteFields;
import com.game.entity.activity.TicketsNote;
import com.game.entity.activity.TicketsNoteFields;
import com.game.utils.VirtualThreadUtils;
import com.mongodb.client.MongoCollection;
import com.mongodb.client.model.Filters;
import io.netty.util.internal.StringUtil;
import org.bson.Document;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;

import java.util.List;

public class TicketsNoteDao implements EntityDao<TicketsNote> {
    private static final Logger LOGGER = LoggerFactory.getLogger(TicketsNoteDao.class);

    private final MongoCollection<TicketsNote> collection;
    final MongoTemplate ops;

    public TicketsNoteDao(DBConnectionMrg dbConnectionMrg) {
        this.ops = dbConnectionMrg.getMongoTemplate();
        this.collection = dbConnectionMrg.getCollection("tickets_note", entityClass());
        final IndexDescAll indexDescAll = IndexDescAll.createWithAllIndexs(
                IndexDescSingle.createIndex(new IndexTypeAndName(Long.class, TicketsNoteFields.business_no)),
                IndexDescSingle.createIndex(new IndexTypeAndName(Long.class, TicketsNoteFields.gameId)),
                IndexDescSingle.createIndex(new IndexTypeAndName(Long.class, TicketsNoteFields.createTime))
        );
        final MongoCollection<Document> mongoCollection = dbConnectionMrg.getMongoTemplate().getCollection("tickets_note");
        ToolMongoIndexHelper.indexBuildWork(indexDescAll.getApplicationIndex(), null, mongoCollection);
    }

    @Override
    public Class<TicketsNote> entityClass() {
        return TicketsNote.class;
    }

    public void insert(TicketsNote ticketsNote) {
        if (ticketsNote.getPlayerId() <= 0) {
            throw new IllegalArgumentException("ticketsNote.getPlayerId() <= 0");
        }
        VirtualThreadUtils.execute(() -> collection.insertOne(ticketsNote));
    }

    public int generateTicketNumbers(long playerId) {
        int ticketNumbers = MathUtils.random(10000000, 99999999);
        while (collection.find(Filters.eq(TicketsNoteFields.ticketNumbers, ticketNumbers)).first() != null) {
            ticketNumbers = MathUtils.random(10000000, 99999999);
            LOGGER.error("严重错误：{}，已存在相同，ticketNumbers：{}", playerId, ticketNumbers);
        }
        return ticketNumbers;
    }

    public Tuple2<Integer, List<TicketsNote>> loadByPlayerIdTicketsNote(long playerId, String gameId, int skip, int limit) {
        final Query query = new Query();
        query.addCriteria(Criteria.where(TicketsNoteFields.playerId).is(playerId));
        if (!StringUtil.isNullOrEmpty(gameId)) {
            query.addCriteria(Criteria.where(TicketsNoteFields.gameId).is(gameId));
        }

        final int total = (int) ops.count(query, TicketsNote.class);
        final List<TicketsNote> ticketsNotes = ops.find(query.with(Sort.by(Sort.Direction.DESC, TicketsNoteFields.createTime))
                .skip(skip).limit(limit), TicketsNote.class);
        return new Tuple2<>(total, ticketsNotes);
    }


    public List<TicketsNote> loadTicketsNote(String business_no, long start, long end, int limit) {
        final Query query = new Query();
        query.addCriteria(Criteria.where(TicketsNoteFields.business_no).is(business_no));
        if (start > 0 && end > 0) {
            query.addCriteria(Criteria.where(SpinBonusNoteFields.createTime).gte(start).lt(end));
        }
        return ops.find(query.with(Sort.by(Sort.Direction.DESC, TicketsNoteFields.createTime))
                .limit(limit), TicketsNote.class);
    }

    public List<TicketsNote> loadByGameIdTicketsNote(String business_no, String gameId, int skip, int limit) {
        final Query query = new Query();
        query.addCriteria(Criteria.where(TicketsNoteFields.business_no).is(business_no));
        if (!StringUtil.isNullOrEmpty(gameId)) {
            query.addCriteria(Criteria.where(TicketsNoteFields.gameId).is(gameId));
        }
        return ops.find(query.with(Sort.by(Sort.Direction.DESC, TicketsNoteFields.createTime))
                .skip(skip).limit(limit), TicketsNote.class);
    }


    /**
     * 90 day
     *
     * @param end
     * @return
     */
    public long delete(long end) {
        final Criteria criteria = new Criteria();
        criteria.and(TicketsNoteFields.createTime).lt(end);

        int BATCH_SIZE = 1000;
        long totalDeleted = 0;
        while (true) {
            // 查询一批数据
            final Query query = new Query(criteria).limit(BATCH_SIZE);

            // 批量删除
            final long deletedCount = ops.remove(query, TicketsNote.class).getDeletedCount();
            totalDeleted += deletedCount;

            if (deletedCount < BATCH_SIZE) {
                break;
            }
        }
        return totalDeleted;
    }
}
