package com.game.dao.activity;

import com.game.engine.mongo.*;
import com.game.engine.util.misc.Tuple2;
import com.game.entity.bonus.BonusNote;
import com.game.entity.bonus.BonusNoteFields;
import com.game.entity.game.GameNoteFields;
import com.game.utils.VirtualThreadUtils;
import com.mongodb.client.MongoCollection;
import org.bson.Document;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;

import java.util.List;

public class BonusNoteDao implements EntityDao<BonusNote> {

    private final MongoCollection<BonusNote> collection;
    private final MongoTemplate ops;

    public BonusNoteDao(DBConnectionMrg dbConnectionMrg) {
        this.ops = dbConnectionMrg.getMongoTemplate();
        this.collection = dbConnectionMrg.getCollection("bonus_note", entityClass());
        final IndexDescAll indexDescAll = IndexDescAll.createWithAllIndexs(
                IndexDescSingle.createIndex(new IndexTypeAndName(Long.class, BonusNoteFields.playerId)),
                IndexDescSingle.createIndex(new IndexTypeAndName(Long.class, BonusNoteFields.bonusType)),
                IndexDescSingle.createIndex(new IndexTypeAndName(Long.class, BonusNoteFields.bonusSubType)),
                IndexDescSingle.createIndex(new IndexTypeAndName(Long.class, BonusNoteFields.createTime))
        );
        final MongoCollection<Document> mongoCollection = dbConnectionMrg.getMongoTemplate().getCollection("bonus_note");
        ToolMongoIndexHelper.indexBuildWork(indexDescAll.getApplicationIndex(), null, mongoCollection);
    }

    @Override
    public Class<BonusNote> entityClass() {
        return BonusNote.class;
    }

    public void insert(BonusNote bonusNote) {
        if (bonusNote.getPlayerId() <= 0) {
            throw new IllegalArgumentException("bonusNote.getPlayerId() <= 0");
        }
        VirtualThreadUtils.execute(() -> collection.insertOne(bonusNote));
    }

    public Tuple2<Integer, List<BonusNote>> loadBonusNote(long playerId, List<Integer> bonusTypes, long start, long end, int skip, int limit) {
        final Query query = new Query();
        query.addCriteria(Criteria.where(BonusNoteFields.playerId).is(playerId)
                .and(BonusNoteFields.bonusType).in(bonusTypes));
        if (start > 0 && end > 0) {
            query.addCriteria(Criteria.where(BonusNoteFields.createTime).gte(start).lt(end));
        }

        final int count = (int) ops.count(query, BonusNote.class);
        final List<BonusNote> bonusNoteList = ops.find(query.with(Sort.by(Sort.Direction.DESC, GameNoteFields.createTime))
                .skip(skip).limit(limit), BonusNote.class);
        return new Tuple2<>(count, bonusNoteList);
    }


    public Tuple2<Integer, List<BonusNote>> loadTransactionsNote(long playerId, int currencyId, List<Integer> transactionFrom, long start, long end, int skip, int limit) {
        final Query query = new Query();
        query.addCriteria(Criteria.where(BonusNoteFields.playerId).is(playerId)
                .and(BonusNoteFields.bonusSubType).in(transactionFrom));
        if (currencyId > 0) {
            query.addCriteria(Criteria.where(BonusNoteFields.currencyId).is(currencyId));
        }
        if (start > 0 && end > 0) {
            query.addCriteria(Criteria.where(BonusNoteFields.createTime).gte(start).lt(end));
        }

        final int count = (int) ops.count(query, BonusNote.class);
        final List<BonusNote> bonusNoteList = ops.find(query.with(Sort.by(Sort.Direction.DESC, GameNoteFields.createTime))
                .skip(skip).limit(limit), BonusNote.class);
        return new Tuple2<>(count, bonusNoteList);
    }

    /**
     * 60 day
     *
     * @param end
     * @return
     */
    public long delete(long end) {
        final Criteria criteria = new Criteria();
        criteria.and(BonusNoteFields.createTime).lt(end);

        int BATCH_SIZE = 1000;
        long totalDeleted = 0;
        while (true) {
            // 查询一批数据
            final Query query = new Query(criteria).limit(BATCH_SIZE);

            // 批量删除
            final long deletedCount = ops.remove(query, BonusNote.class).getDeletedCount();
            totalDeleted += deletedCount;

            if (deletedCount < BATCH_SIZE) {
                break;
            }
        }
        return totalDeleted;
    }

}
