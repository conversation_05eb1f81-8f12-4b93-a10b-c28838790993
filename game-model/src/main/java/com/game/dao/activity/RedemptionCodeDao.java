package com.game.dao.activity;

import com.game.engine.mongo.*;
import com.game.engine.utils.TimeUtil;
import com.game.entity.player.activity.RedemptionCode;
import com.game.entity.player.activity.RedemptionCodeFields;
import com.game.utils.VirtualThreadUtils;
import com.mongodb.client.MongoCollection;
import com.mongodb.client.model.Filters;
import com.mongodb.client.model.Updates;
import io.netty.util.internal.StringUtil;
import org.bson.Document;

public class RedemptionCodeDao implements EntityDao<RedemptionCode> {
    private final MongoCollection<RedemptionCode> collection;

    public RedemptionCodeDao(DBConnectionMrg dbConnectionMrg) {
        this.collection = dbConnectionMrg.getCollection("redemptionCode", entityClass());
        final IndexDescAll indexDescAll = IndexDescAll.createWithAllIndexs(
                IndexDescSingle.createIndex(new IndexTypeAndName(String.class, RedemptionCodeFields.redemptionCode))
        );
        final MongoCollection<Document> mongoCollection = dbConnectionMrg.getMongoTemplate().getCollection("redemptionCode");
        ToolMongoIndexHelper.indexBuildWork(indexDescAll.getApplicationIndex(), null, mongoCollection);
    }

    @Override
    public Class<RedemptionCode> entityClass() {
        return RedemptionCode.class;
    }

    public void insert(RedemptionCode redemptionCode) {
        if (StringUtil.isNullOrEmpty(redemptionCode.getRedemptionCode())) {
            throw new IllegalArgumentException("redemptionCode.getRedemptionCode() is null or empty");
        }
        VirtualThreadUtils.execute(() -> collection.insertOne(redemptionCode));
    }

    public RedemptionCode findByRedemptionCode(String redemptionCode) {
        return collection.find(Filters.eq(RedemptionCodeFields.redemptionCode, redemptionCode)).first();
    }

    public void updateRedemptionCodeTimes(String redemptionCode) {
        VirtualThreadUtils.execute(() ->
                collection.updateOne(Filters.eq(RedemptionCodeFields.redemptionCode, redemptionCode),
                        Updates.combine(
                                Updates.inc(RedemptionCodeFields.times, 1),
                                Updates.set(RedemptionCodeFields.lastTime, TimeUtil.currentTimeMillis())
                        )));
    }
}
