package com.game.dao.activity;

import com.game.engine.mongo.*;
import com.game.engine.util.misc.Tuple2;
import com.game.entity.activity.*;
import com.game.utils.VirtualThreadUtils;
import com.mongodb.client.MongoCollection;
import io.netty.util.internal.StringUtil;
import org.bson.Document;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;

import java.util.List;

public class WinTicketsNoteDao implements EntityDao<WinTicketsNote> {
    private static final Logger LOGGER = LoggerFactory.getLogger(WinTicketsNoteDao.class);

    private final MongoCollection<WinTicketsNote> collection;
    private final MongoTemplate ops;

    public WinTicketsNoteDao(DBConnectionMrg dbConnectionMrg) {
        this.ops = dbConnectionMrg.getMongoTemplate();
        this.collection = dbConnectionMrg.getCollection("winTickets_note", entityClass());
        final IndexDescAll indexDescAll = IndexDescAll.createWithAllIndexs(
                IndexDescSingle.createIndex(new IndexTypeAndName(Long.class, WinTicketsNoteFields.business_no)),
                IndexDescSingle.createIndex(new IndexTypeAndName(Long.class, WinTicketsNoteFields.gameId)),
                IndexDescSingle.createIndex(new IndexTypeAndName(Long.class, WinTicketsNoteFields.prize))
        );
        final MongoCollection<Document> mongoCollection = dbConnectionMrg.getMongoTemplate().getCollection("winTickets_note");
        ToolMongoIndexHelper.indexBuildWork(indexDescAll.getApplicationIndex(), null, mongoCollection);
    }

    @Override
    public Class<WinTicketsNote> entityClass() {
        return WinTicketsNote.class;
    }

    public void insert(WinTicketsNote winTicketsNote) {
        if (winTicketsNote.getPlayerId() <= 0) {
            throw new IllegalArgumentException("winTicketsNote.getPlayerId() <= 0");
        }
        VirtualThreadUtils.execute(() -> collection.insertOne(winTicketsNote));
    }

    public void insertMany(List<WinTicketsNote> winTicketsNotes) {
        if (winTicketsNotes == null || winTicketsNotes.isEmpty()) {
            return;
        }
        VirtualThreadUtils.execute(() -> collection.insertMany(winTicketsNotes));
    }

    public Tuple2<Integer, List<WinTicketsNote>> loadWinTicketsNote(String business_no, String gameId, int skip, int limit) {
        final Criteria criteria = new Criteria();
        criteria.and(WinTicketsNoteFields.business_no).is(business_no);
        if (!StringUtil.isNullOrEmpty(gameId)) {
            criteria.and(WinTicketsNoteFields.gameId).is(gameId);
        }
        final List<WinTicketsNote> winTicketsNotes = ops.find(new Query(criteria).with(Sort.by(Sort.Direction.ASC, WinTicketsNoteFields.no))
                .skip(skip).limit(limit), WinTicketsNote.class);
        final int total = (int) ops.count(new Query(criteria), WinTicketsNote.class);
        return new Tuple2<>(total, winTicketsNotes);
    }

    public List<WinTicketsNote> loadByPlayerIdWinTicketsNote(String business_no, long playerId) {
        final Criteria criteria = new Criteria();
        criteria.and(WinTicketsNoteFields.business_no).is(business_no);
        criteria.and(WinTicketsNoteFields.playerId).is(playerId);
        return ops.find(new Query(criteria).with(Sort.by(Sort.Direction.ASC, WinTicketsNoteFields.no)), WinTicketsNote.class);
    }


    public List<WinTicketsNote> loadAllWinTicketsNote(String business_no, String gameId, int skip, int limit) {
        final Criteria criteria = new Criteria();
        criteria.and(WinTicketsNoteFields.business_no).is(business_no);
        if (!StringUtil.isNullOrEmpty(gameId)) {
            criteria.and(WinTicketsNoteFields.gameId).is(gameId);
        }
        return ops.find(new Query(criteria).with(Sort.by(Sort.Direction.DESC, WinTicketsNoteFields.no))
                .skip(skip).limit(limit), WinTicketsNote.class);
    }

    /**
     * 60 day
     *
     * @param end
     * @return
     */
    public long delete(long end) {
        final Criteria criteria = new Criteria();
        criteria.and(WinTicketsNoteFields.createTime).lt(end);

        int BATCH_SIZE = 1000;
        long totalDeleted = 0;
        while (true) {
            // 查询一批数据
            final Query query = new Query(criteria).limit(BATCH_SIZE);

            // 批量删除
            final long deletedCount = ops.remove(query, WinTicketsNote.class).getDeletedCount();
            totalDeleted += deletedCount;

            if (deletedCount < BATCH_SIZE) {
                break;
            }
        }
        return totalDeleted;
    }

}
