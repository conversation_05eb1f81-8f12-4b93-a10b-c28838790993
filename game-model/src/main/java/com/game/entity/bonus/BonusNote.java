package com.game.entity.bonus;

import com.game.engine.utils.TimeUtil;
import com.wjybxx.io.AutoFields;
import com.wjybxx.io.binary.SerializableClass;
import com.wjybxx.io.document.PersistentEntity;
import org.springframework.data.mongodb.core.mapping.Document;

@AutoFields
@PersistentEntity
@SerializableClass
@Document(collection = "bonus_note")
public class BonusNote  {

    private String business_no;

    private long playerId;

    private int bonusType;

    private int bonusSubType;

    private int currencyId;

    private double amount;

    private double balance;

    private long createTime;

    public BonusNote() {
        this.createTime = TimeUtil.currentTimeMillis();
    }

    public String getBusiness_no() {
        return business_no;
    }

    public void setBusiness_no(String business_no) {
        this.business_no = business_no;
    }

    public int getBonusType() {
        return bonusType;
    }

    public void setBonusType(int bonusType) {
        this.bonusType = bonusType;
    }

    public int getBonusSubType() {
        return bonusSubType;
    }

    public void setBonusSubType(int bonusSubType) {
        this.bonusSubType = bonusSubType;
    }

    public long getPlayerId() {
        return playerId;
    }

    public void setPlayerId(long playerId) {
        this.playerId = playerId;
    }

    public long getCreateTime() {
        return createTime;
    }

    public void setCreateTime(long createTime) {
        this.createTime = createTime;
    }

    public int getCurrencyId() {
        return currencyId;
    }

    public void setCurrencyId(int currencyId) {
        this.currencyId = currencyId;
    }

    public double getAmount() {
        return amount;
    }

    public void setAmount(double amount) {
        this.amount = amount;
    }

    public double getBalance() {
        return balance;
    }

    public void setBalance(double balance) {
        this.balance = balance;
    }
}
