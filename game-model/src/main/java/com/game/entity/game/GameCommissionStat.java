package com.game.entity.game;

public class GameCommissionStat {
    private double totalCommissionReward;
    private double totalCommissionOriginal;
    private double totalCommission3rdParty;
    private double totalCommissionSports;

    public double getTotalCommissionReward() {
        return totalCommissionReward;
    }

    public GameCommissionStat setTotalCommissionReward(double totalCommissionReward) {
        this.totalCommissionReward = totalCommissionReward;
        return this;
    }

    public double getTotalCommissionOriginal() {
        return totalCommissionOriginal;
    }

    public GameCommissionStat setTotalCommissionOriginal(double totalCommissionOriginal) {
        this.totalCommissionOriginal = totalCommissionOriginal;
        return this;
    }

    public double getTotalCommission3rdParty() {
        return totalCommission3rdParty;
    }

    public GameCommissionStat setTotalCommission3rdParty(double totalCommission3rdParty) {
        this.totalCommission3rdParty = totalCommission3rdParty;
        return this;
    }

    public double getTotalCommissionSports() {
        return totalCommissionSports;
    }

    public GameCommissionStat setTotalCommissionSports(double totalCommissionSports) {
        this.totalCommissionSports = totalCommissionSports;
        return this;
    }
}
