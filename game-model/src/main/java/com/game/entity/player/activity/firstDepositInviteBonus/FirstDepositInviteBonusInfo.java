package com.game.entity.player.activity.firstDepositInviteBonus;

import com.game.engine.math.BigDecimalUtils;
import com.wjybxx.io.AutoFields;
import com.wjybxx.io.binary.SerializableClass;
import com.wjybxx.io.document.PersistentEntity;
import org.springframework.data.annotation.Transient;

import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;

@AutoFields
@PersistentEntity
@SerializableClass
public class FirstDepositInviteBonusInfo {

    private int c_id;

    private boolean start;

    private int currencyId;

    private int vipLevel;

    private long firstDepositTime;

    private Map<Integer, Double> chargeMap = new HashMap<>();

    private Set<Integer> receiveBonus = new HashSet<>();

    private Set<Integer> receiveMails = new HashSet<>();

    @Transient
    private transient double totalValidWagered;

    public int getC_id() {
        return c_id;
    }

    public void setC_id(int c_id) {
        this.c_id = c_id;
    }

    public boolean isStart() {
        return start;
    }

    public void setStart(boolean start) {
        this.start = start;
    }

    public int getCurrencyId() {
        return currencyId;
    }

    public void setCurrencyId(int currencyId) {
        this.currencyId = currencyId;
    }

    public int getVipLevel() {
        return vipLevel;
    }

    public void setVipLevel(int vipLevel) {
        this.vipLevel = vipLevel;
    }

    public long getFirstDepositTime() {
        return firstDepositTime;
    }

    public void setFirstDepositTime(long firstDepositTime) {
        this.firstDepositTime = firstDepositTime;
    }

    public double getTotalValidWagered() {
        return totalValidWagered;
    }

    public void setTotalValidWagered(double totalValidWagered) {
        this.totalValidWagered = totalValidWagered;
    }

    public Map<Integer, Double> getChargeMap() {
        return chargeMap;
    }

    public void setChargeMap(Map<Integer, Double> chargeMap) {
        this.chargeMap = chargeMap;
    }

    public Set<Integer> getReceiveBonus() {
        return receiveBonus;
    }

    public void setReceiveBonus(Set<Integer> receiveBonus) {
        this.receiveBonus = receiveBonus;
    }

    public Set<Integer> getReceiveMails() {
        return receiveMails;
    }

    public void setReceiveMails(Set<Integer> receiveMails) {
        this.receiveMails = receiveMails;
    }

    public void incCharge(int currencyId, double charge) {
        double amount = this.chargeMap.getOrDefault(currencyId, 0.0);
        amount = BigDecimalUtils.add(amount, charge, 9);
        this.chargeMap.put(currencyId, amount);
    }

    public void reset() {
        this.c_id = 0;
        this.start = false;
        this.receiveBonus.clear();
        this.receiveMails.clear();
    }
}
