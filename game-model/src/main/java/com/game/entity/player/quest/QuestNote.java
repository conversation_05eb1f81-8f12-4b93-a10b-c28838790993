package com.game.entity.player.quest;

import com.game.engine.mongo.ShardEntity;
import com.wjybxx.io.AutoFields;
import com.wjybxx.io.binary.SerializableClass;
import com.wjybxx.io.document.PersistentEntity;
import org.springframework.data.mongodb.core.mapping.Document;

@AutoFields
@PersistentEntity
@SerializableClass
@Document(collection = "quest_note")
public class QuestNote extends ShardEntity {
    private String business_no;

    private long uniqueId;

    private long playerId;

    /**
     * 当前任务id
     */
    private int questId;
    /**
     * 任务状态
     */
    private int state;
    /**
     * 完成时间
     */
    private long finishedTime;

    public QuestNote() {
        super(-1);
    }

    public QuestNote(long id) {
        super(id);
        this.uniqueId = id;
    }

    public String getBusiness_no() {
        return business_no;
    }

    public void setBusiness_no(String business_no) {
        this.business_no = business_no;
    }

    public long getPlayerId() {
        return playerId;
    }

    public void setPlayerId(long playerId) {
        this.playerId = playerId;
    }

    public long getUniqueId() {
        return uniqueId;
    }

    public void setUniqueId(long uniqueId) {
        this.uniqueId = uniqueId;
    }

    public int getQuestId() {
        return questId;
    }

    public void setQuestId(int questId) {
        this.questId = questId;
    }

    public int getState() {
        return state;
    }

    public void setState(int state) {
        this.state = state;
    }

    public long getFinishedTime() {
        return finishedTime;
    }

    public void setFinishedTime(long finishedTime) {
        this.finishedTime = finishedTime;
    }

}
