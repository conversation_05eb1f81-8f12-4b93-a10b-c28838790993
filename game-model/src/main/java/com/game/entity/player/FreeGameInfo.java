package com.game.entity.player;

import com.wjybxx.io.AutoFields;
import com.wjybxx.io.binary.SerializableClass;
import com.wjybxx.io.document.PersistentEntity;

import java.util.LinkedHashMap;
import java.util.Map;

@AutoFields
@PersistentEntity
@SerializableClass
public class FreeGameInfo {
    //gameId
    private Map<Integer, GameInfo> gameInfoMap = new LinkedHashMap<>();

    public Map<Integer, GameInfo> getGameInfoMap() {
        return gameInfoMap;
    }

    public void setGameInfoMap(Map<Integer, GameInfo> gameInfoMap) {
        this.gameInfoMap = gameInfoMap;
    }

    public GameInfo getFreeGame(int gameId) {
        return this.gameInfoMap.computeIfAbsent(gameId, k -> new GameInfo());
    }
}
