package com.game.entity.player.activity.firstChargeSignIn;

import com.wjybxx.io.AutoFields;
import com.wjybxx.io.binary.SerializableClass;
import com.wjybxx.io.document.PersistentEntity;

import java.util.ArrayList;
import java.util.List;

@AutoFields
@PersistentEntity
@SerializableClass
public class FirstChargeSignInInfo {
    private int c_id;

    private int currDay;

    private long rechargeTime;

    private List<Integer> receive = new ArrayList<>();

    public int getC_id() {
        return c_id;
    }

    public void setC_id(int c_id) {
        this.c_id = c_id;
    }

    public int getCurrDay() {
        return currDay;
    }

    public void setCurrDay(int currDay) {
        this.currDay = currDay;
    }

    public long getRechargeTime() {
        return rechargeTime;
    }

    public void setRechargeTime(long rechargeTime) {
        this.rechargeTime = rechargeTime;
    }

    public List<Integer> getReceive() {
        return receive;
    }

    public void setReceive(List<Integer> receive) {
        this.receive = receive;
    }


    public void reset() {
        this.currDay = 0;
        this.rechargeTime = 0;
        this.receive.clear();
    }
}
