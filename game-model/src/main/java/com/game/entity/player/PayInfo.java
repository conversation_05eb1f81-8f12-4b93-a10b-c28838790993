package com.game.entity.player;

import com.game.engine.utils.TimeUtil;
import com.wjybxx.io.AutoFields;
import com.wjybxx.io.binary.SerializableClass;
import com.wjybxx.io.document.PersistentEntity;

@AutoFields
@PersistentEntity
@SerializableClass
public class PayInfo {
    private long payId;
    private int currencyId;
    //支付方式
    private int paymentMethod;
    //扩展
    private String extend;
    //扩展1
    private String extend_1;
    //扩展2
    private String extend_2;
    //扩展3
    private String extend_3;

    private long createTime;

    public PayInfo() {
        this.createTime = TimeUtil.currentTimeMillis();
    }

    public long getPayId() {
        return payId;
    }

    public void setPayId(long payId) {
        this.payId = payId;
    }

    public int getCurrencyId() {
        return currencyId;
    }

    public void setCurrencyId(int currencyId) {
        this.currencyId = currencyId;
    }

    public int getPaymentMethod() {
        return paymentMethod;
    }

    public void setPaymentMethod(int paymentMethod) {
        this.paymentMethod = paymentMethod;
    }

    public String getExtend() {
        return extend;
    }

    public void setExtend(String extend) {
        this.extend = extend;
    }

    public String getExtend_1() {
        return extend_1;
    }

    public void setExtend_1(String extend_1) {
        this.extend_1 = extend_1;
    }

    public String getExtend_2() {
        return extend_2;
    }

    public void setExtend_2(String extend_2) {
        this.extend_2 = extend_2;
    }

    public String getExtend_3() {
        return extend_3;
    }

    public void setExtend_3(String extend_3) {
        this.extend_3 = extend_3;
    }

    public long getCreateTime() {
        return createTime;
    }

    public void setCreateTime(long createTime) {
        this.createTime = createTime;
    }

}
