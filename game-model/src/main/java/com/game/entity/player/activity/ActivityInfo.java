package com.game.entity.player.activity;

import com.wjybxx.io.AutoFields;
import com.wjybxx.io.binary.SerializableClass;
import com.wjybxx.io.document.PersistentEntity;

import java.util.LinkedHashMap;
import java.util.Map;

@AutoFields
@PersistentEntity
@SerializableClass
public class ActivityInfo {

    private Map<Integer, ActivityData> activityDataMap = new LinkedHashMap<>();

    public ActivityData getActivityData(int activityId) {
        return activityDataMap.get(activityId);
    }

    public Map<Integer, ActivityData> getActivityDataMap() {
        return activityDataMap;
    }

    public void setActivityDataMap(Map<Integer, ActivityData> activityDataMap) {
        this.activityDataMap = activityDataMap;
    }
}
