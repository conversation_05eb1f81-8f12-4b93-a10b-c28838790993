package com.game.entity.player.promote;

import com.game.engine.mongo.ShardEntity;
import com.game.entity.player.Player;
import com.wjybxx.io.AutoFields;
import com.wjybxx.io.binary.SerializableClass;
import com.wjybxx.io.document.PersistentEntity;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.*;

@AutoFields
@PersistentEntity
@SerializableClass
@Document(collection = "player_promote")
public class PlayerPromote extends ShardEntity {

    @Id
    private long _id;

    private String business_no;

    private long playerId;

    private String playerName;

    //vip
    private int vipLevel;

    //1.正常 2.封号 3.冻结
    private int state;

    private long createTime;

    //邀请码
    private String invitationCode;

    //三级
    private long threeLevelId;
    private String threeLevelCode;

    //团队id
    private long teamId;
    private String teamCode;

    //上级id
    private long superiorId;
    //绑定推广码
    private String superiorCode;

    //上级链
    private List<Long> superiors = new ArrayList<>();

    //来源 1.推广
    private int source;

    //锁住奖励
    private int lockedCurrencyId;
    private double lockedReward;

    private ReferralRewards availableRewards = new ReferralRewards();
    //推荐奖励
    private ReferralRewards referralRewards = new ReferralRewards();
    //佣金
    private Map<Integer, CommissionRewards> commissionRewardsMap = new LinkedHashMap<>();
    //团长
    private Map<Integer, TeamRewards> teamRewardsMap = new LinkedHashMap<>();
    //三级
    private Map<Integer, ThreeLevelRewards> threeLevelRewardsMap = new LinkedHashMap<>();

    //推广码
    private Map<String, ReferralCode> referralCodeMap = new LinkedHashMap<>();

    public PlayerPromote() {
        super(0);//仅仅用于DB或反序列化
    }

    public PlayerPromote(long playerId) {
        super(playerId);
        this._id = playerId;
        this.playerId = playerId;
    }

    public long get_id() {
        return _id;
    }

    public void set_id(long _id) {
        this._id = _id;
    }

    public String getBusiness_no() {
        return business_no;
    }

    public void setBusiness_no(String business_no) {
        this.business_no = business_no;
    }

    public long getPlayerId() {
        return playerId;
    }

    public void setPlayerId(long playerId) {
        this.playerId = playerId;
    }

    public String getPlayerName() {
        return playerName;
    }

    public void setPlayerName(String playerName) {
        this.playerName = playerName;
    }

    public long getCreateTime() {
        return createTime;
    }

    public void setCreateTime(long createTime) {
        this.createTime = createTime;
    }

    public String getInvitationCode() {
        return invitationCode;
    }

    public void setInvitationCode(String invitationCode) {
        this.invitationCode = invitationCode;
    }

    public long getThreeLevelId() {
        return threeLevelId;
    }

    public void setThreeLevelId(long threeLevelId) {
        this.threeLevelId = threeLevelId;
    }

    public String getThreeLevelCode() {
        return threeLevelCode;
    }

    public void setThreeLevelCode(String threeLevelCode) {
        this.threeLevelCode = threeLevelCode;
    }

    public String getTeamCode() {
        return teamCode;
    }

    public void setTeamCode(String teamCode) {
        this.teamCode = teamCode;
    }

    public long getTeamId() {
        return teamId;
    }

    public void setTeamId(long teamId) {
        this.teamId = teamId;
    }

    public int getVipLevel() {
        return vipLevel;
    }

    public void setVipLevel(int vipLevel) {
        this.vipLevel = vipLevel;
    }

    public int getState() {
        return state;
    }

    public void setState(int state) {
        this.state = state;
    }

    public String getSuperiorCode() {
        return superiorCode;
    }

    public void setSuperiorCode(String superiorCode) {
        this.superiorCode = superiorCode;
    }

    public long getSuperiorId() {
        return superiorId;
    }

    public void setSuperiorId(long superiorId) {
        this.superiorId = superiorId;
    }

    public List<Long> getSuperiors() {
        return superiors;
    }

    public void setSuperiors(List<Long> superiors) {
        this.superiors = superiors;
    }

    public int getSource() {
        return source;
    }

    public void setSource(int source) {
        this.source = source;
    }

    public Map<Integer, CommissionRewards> getCommissionRewardsMap() {
        return commissionRewardsMap;
    }

    public void setCommissionRewardsMap(Map<Integer, CommissionRewards> commissionRewardsMap) {
        this.commissionRewardsMap = commissionRewardsMap;
    }

    public Map<Integer, TeamRewards> getTeamRewardsMap() {
        return teamRewardsMap;
    }

    public void setTeamRewardsMap(Map<Integer, TeamRewards> teamRewardsMap) {
        this.teamRewardsMap = teamRewardsMap;
    }

    public Map<Integer, ThreeLevelRewards> getThreeLevelRewardsMap() {
        return threeLevelRewardsMap;
    }

    public void setThreeLevelRewardsMap(Map<Integer, ThreeLevelRewards> threeLevelRewardsMap) {
        this.threeLevelRewardsMap = threeLevelRewardsMap;
    }

    public ReferralRewards getAvailableRewards() {
        return availableRewards;
    }

    public void setAvailableRewards(ReferralRewards availableRewards) {
        this.availableRewards = availableRewards;
    }

    public ReferralRewards getReferralRewards() {
        return referralRewards;
    }

    public void setReferralRewards(ReferralRewards referralRewards) {
        this.referralRewards = referralRewards;
    }

    public int getLockedCurrencyId() {
        return lockedCurrencyId;
    }

    public void setLockedCurrencyId(int lockedCurrencyId) {
        this.lockedCurrencyId = lockedCurrencyId;
    }

    public double getLockedReward() {
        return lockedReward;
    }

    public void setLockedReward(double lockedReward) {
        this.lockedReward = lockedReward;
    }

    public Map<String, ReferralCode> getReferralCodeMap() {
        return referralCodeMap;
    }

    public void setReferralCodeMap(Map<String, ReferralCode> referralCodeMap) {
        this.referralCodeMap = referralCodeMap;
    }

    public static PlayerPromote createPlayerPromote(Player player, ReferralCode referralCode) {
        final PlayerPromote playerPromote = new PlayerPromote(player.getPlayerId());
        playerPromote.setSuperiorCode("");
        playerPromote.setBusiness_no(player.getBusiness_no());
        playerPromote.setCreateTime(player.getCreateTime());
        playerPromote.setPlayerName(player.getPlayerName());
        playerPromote.setVipLevel(player.getVipClub().getVipLevel());
        playerPromote.setState(player.getState());
        playerPromote.getReferralCodeMap().put(referralCode.getCode(), referralCode);
        playerPromote.setInvitationCode(referralCode.getCode());
        return playerPromote;
    }
}
