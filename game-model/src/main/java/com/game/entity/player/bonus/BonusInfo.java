package com.game.entity.player.bonus;

import com.wjybxx.io.AutoFields;
import com.wjybxx.io.binary.SerializableClass;
import com.wjybxx.io.document.PersistentEntity;

import java.util.LinkedHashMap;
import java.util.Map;

@AutoFields
@PersistentEntity
@SerializableClass
public class BonusInfo {

    private long rechargeEndTime;

    private long monthlyEndTime;

    private long weeklyEndTime;

    //5.升级 6.充电 7.周 8.月
    private Map<Integer, BonusProcessInfo> bonusProcessInfoMap = new LinkedHashMap<>();

    //1.quests 2.luckySpin 3.depositBonus 4.freeSpin 5.levelUpBonus 6.recharge 7.weeklyCashBack 8.monthlyCashBack
    private Map<Integer, BonusDetailsInfo> bonusDetailsMap = new LinkedHashMap<>();

    public long getRechargeEndTime() {
        return rechargeEndTime;
    }

    public void setRechargeEndTime(long rechargeEndTime) {
        this.rechargeEndTime = rechargeEndTime;
    }

    public long getMonthlyEndTime() {
        return monthlyEndTime;
    }

    public void setMonthlyEndTime(long monthlyEndTime) {
        this.monthlyEndTime = monthlyEndTime;
    }

    public long getWeeklyEndTime() {
        return weeklyEndTime;
    }

    public void setWeeklyEndTime(long weeklyEndTime) {
        this.weeklyEndTime = weeklyEndTime;
    }

    public Map<Integer, BonusProcessInfo> getBonusProcessInfoMap() {
        return bonusProcessInfoMap;
    }

    public void setBonusProcessInfoMap(Map<Integer, BonusProcessInfo> bonusProcessInfoMap) {
        this.bonusProcessInfoMap = bonusProcessInfoMap;
    }

    public Map<Integer, BonusDetailsInfo> getBonusDetailsMap() {
        return bonusDetailsMap;
    }

    public void setBonusDetailsMap(Map<Integer, BonusDetailsInfo> bonusDetailsMap) {
        this.bonusDetailsMap = bonusDetailsMap;
    }

    public BonusProcessInfo getBonusProcessInfo(int bonusType) {
        BonusProcessInfo bonusProcessInfo = this.bonusProcessInfoMap.get(bonusType);
        if (bonusProcessInfo == null) {
            bonusProcessInfo = new BonusProcessInfo();
            this.bonusProcessInfoMap.put(bonusType, bonusProcessInfo);
        }
        return bonusProcessInfo;
    }

    public BonusDetailsInfo getBonusDetailsInfo(int bonusType) {
        BonusDetailsInfo bonusDetailsInfo = this.bonusDetailsMap.get(bonusType);
        if (bonusDetailsInfo == null) {
            bonusDetailsInfo = new BonusDetailsInfo();
            this.bonusDetailsMap.put(bonusType, bonusDetailsInfo);
        }
        return bonusDetailsInfo;
    }

}
