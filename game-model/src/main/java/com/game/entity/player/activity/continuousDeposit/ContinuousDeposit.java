package com.game.entity.player.activity.continuousDeposit;

import com.wjybxx.io.AutoFields;
import com.wjybxx.io.binary.SerializableClass;
import com.wjybxx.io.document.PersistentEntity;

@AutoFields
@PersistentEntity
@SerializableClass
public class ContinuousDeposit {
    private int c_id;

    private boolean start;

    private int rechargeTimes;

    public boolean isStart() {
        return start;
    }

    public void setStart(boolean start) {
        this.start = start;
    }

    public int getRechargeTimes() {
        return rechargeTimes;
    }

    public void setRechargeTimes(int rechargeTimes) {
        this.rechargeTimes = rechargeTimes;
    }

    public int getC_id() {
        return c_id;
    }

    public void setC_id(int c_id) {
        this.c_id = c_id;
    }

    public void reset() {
        this.rechargeTimes = 0;
    }
}
