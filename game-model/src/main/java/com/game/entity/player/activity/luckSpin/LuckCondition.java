package com.game.entity.player.activity.luckSpin;

import com.game.engine.math.BigDecimalUtils;
import com.wjybxx.io.AutoFields;
import com.wjybxx.io.binary.SerializableClass;
import com.wjybxx.io.document.PersistentEntity;

import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;

@AutoFields
@PersistentEntity
@SerializableClass
public class LuckCondition {
    private int totalTimes;
    private Set<Long> invitePlayerId = new HashSet<>();

    private int betTimes;
    private Map<Integer, Double> wageredMap = new HashMap<>();
    private Map<Integer, Double> rechargeAmountMap = new HashMap<>();

    public int getTotalTimes() {
        return totalTimes;
    }

    public void setTotalTimes(int totalTimes) {
        this.totalTimes = totalTimes;
    }

    public int getBetTimes() {
        return betTimes;
    }

    public void setBetTimes(int betTimes) {
        this.betTimes = betTimes;
    }

    public Set<Long> getInvitePlayerId() {
        return invitePlayerId;
    }

    public void setInvitePlayerId(Set<Long> invitePlayerId) {
        this.invitePlayerId = invitePlayerId;
    }

    public Map<Integer, Double> getWageredMap() {
        return wageredMap;
    }

    public void setWageredMap(Map<Integer, Double> wageredMap) {
        this.wageredMap = wageredMap;
    }

    public Map<Integer, Double> getRechargeAmountMap() {
        return rechargeAmountMap;
    }

    public void setRechargeAmountMap(Map<Integer, Double> rechargeAmountMap) {
        this.rechargeAmountMap = rechargeAmountMap;
    }

    public void incTotalTimes(int totalTimes) {
        this.totalTimes += totalTimes;
    }

    public void incBetTimes() {
        this.betTimes++;
    }

    public void incWagered(int currencyId, double betAmount) {
        double wagered = this.wageredMap.getOrDefault(currencyId, 0d);
        wagered = BigDecimalUtils.add(betAmount, wagered, 9);
        this.wageredMap.put(currencyId, wagered);
    }

    public void incRecharge(int currencyId, double recharge) {
        double rechargeAmount = this.rechargeAmountMap.getOrDefault(currencyId, 0d);
        rechargeAmount = BigDecimalUtils.add(recharge, rechargeAmount, 9);
        this.rechargeAmountMap.put(currencyId, rechargeAmount);
    }

    public void reset() {
        this.totalTimes = 0;
        this.betTimes = 0;
        this.invitePlayerId.clear();
        this.wageredMap.clear();
        this.rechargeAmountMap.clear();
    }

    public void resetCondition() {
        this.betTimes = 0;
        this.wageredMap.clear();
        this.rechargeAmountMap.clear();
    }
}
