package com.game.entity.player.activity.weeklyRaffle;

import com.game.engine.math.BigDecimalUtils;
import com.game.entity.activity.TicketsNote;
import com.wjybxx.io.AutoFields;
import com.wjybxx.io.binary.SerializableClass;
import com.wjybxx.io.document.PersistentEntity;

import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.Map;

@AutoFields
@PersistentEntity
@SerializableClass
public class WeeklyRaffleInfo {

    private String gameId;

    private int totalTickets;

    private int totalWinTickets;

    private Map<Integer, Double> totalPrizeMap = new LinkedHashMap<>();

    private int dailyTickets;

    private int weeklyTickets;

    private Map<Integer, Double> dailyWageredMap = new HashMap<>();

    private Map<Integer, Double> weeklyWageredMap = new HashMap<>();

    private Map<Integer, TicketsNote> activeTicketsMap = new HashMap<>();

    public String getGameId() {
        return gameId;
    }

    public void setGameId(String gameId) {
        this.gameId = gameId;
    }

    public int getTotalTickets() {
        return totalTickets;
    }

    public void setTotalTickets(int totalTickets) {
        this.totalTickets = totalTickets;
    }

    public int getTotalWinTickets() {
        return totalWinTickets;
    }

    public void setTotalWinTickets(int totalWinTickets) {
        this.totalWinTickets = totalWinTickets;
    }

    public Map<Integer, Double> getTotalPrizeMap() {
        return totalPrizeMap;
    }

    public void setTotalPrizeMap(Map<Integer, Double> totalPrizeMap) {
        this.totalPrizeMap = totalPrizeMap;
    }

    public int getDailyTickets() {
        return dailyTickets;
    }

    public void setDailyTickets(int dailyTickets) {
        this.dailyTickets = dailyTickets;
    }

    public int getWeeklyTickets() {
        return weeklyTickets;
    }

    public void setWeeklyTickets(int weeklyTickets) {
        this.weeklyTickets = weeklyTickets;
    }

    public Map<Integer, TicketsNote> getActiveTicketsMap() {
        return activeTicketsMap;
    }

    public void setActiveTicketsMap(Map<Integer, TicketsNote> activeTicketsMap) {
        this.activeTicketsMap = activeTicketsMap;
    }

    public Map<Integer, Double> getDailyWageredMap() {
        return dailyWageredMap;
    }

    public void setDailyWageredMap(Map<Integer, Double> dailyWageredMap) {
        this.dailyWageredMap = dailyWageredMap;
    }

    public Map<Integer, Double> getWeeklyWageredMap() {
        return weeklyWageredMap;
    }

    public void setWeeklyWageredMap(Map<Integer, Double> weeklyWageredMap) {
        this.weeklyWageredMap = weeklyWageredMap;
    }

    public void incTotalTickets() {
        this.totalTickets++;
    }

    public void incTotalWinTickets() {
        this.totalWinTickets++;
    }

    public void incDailyTickets() {
        this.dailyTickets++;
    }

    public void incWeeklyTickets() {
        this.weeklyTickets++;
    }

    public void incTotalPrize(int currencyId, double reward) {
        double prize = this.totalPrizeMap.getOrDefault(currencyId, 0d);
        prize = BigDecimalUtils.add(prize, reward, 9);
        this.totalPrizeMap.put(currencyId, prize);
    }

    public void incDailyWagered(int currencyId, double amount) {
        double wagered = this.dailyWageredMap.getOrDefault(currencyId, 0d);
        wagered = BigDecimalUtils.add(wagered, amount, 9);
        this.dailyWageredMap.put(currencyId, wagered);
    }

    public void incWeeklyWagered(int currencyId, double amount) {
        double wagered = this.weeklyWageredMap.getOrDefault(currencyId, 0d);
        wagered = BigDecimalUtils.add(wagered, amount, 9);
        this.weeklyWageredMap.put(currencyId, wagered);
    }

    public void reset() {
        this.gameId = "";
        this.dailyTickets = 0;
        this.weeklyTickets = 0;
        this.dailyWageredMap.clear();
        this.weeklyWageredMap.clear();
        this.activeTicketsMap.clear();
    }
}
