package com.game.entity.player.bonus;

import com.game.engine.math.BigDecimalUtils;
import com.wjybxx.io.AutoFields;
import com.wjybxx.io.binary.SerializableClass;
import com.wjybxx.io.document.PersistentEntity;

import java.util.LinkedHashMap;
import java.util.Map;

@AutoFields
@PersistentEntity
@SerializableClass
public class BonusDetailsInfo {

    private Map<Integer, Double> bonusMap = new LinkedHashMap<>();

    public Map<Integer, Double> getBonusMap() {
        return bonusMap;
    }

    public void setBonusMap(Map<Integer, Double> bonusMap) {
        this.bonusMap = bonusMap;
    }

    public void incBonus(int currencyId, double reward) {
        double value = this.bonusMap.getOrDefault(currencyId, 0d);
        value = BigDecimalUtils.add(value, reward, 9);
        this.bonusMap.put(currencyId, value);
    }
}
