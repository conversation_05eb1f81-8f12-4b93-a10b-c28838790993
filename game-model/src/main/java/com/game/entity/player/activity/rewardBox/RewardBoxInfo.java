package com.game.entity.player.activity.rewardBox;

import com.game.engine.math.BigDecimalUtils;
import com.wjybxx.io.AutoFields;
import com.wjybxx.io.binary.SerializableClass;
import com.wjybxx.io.document.PersistentEntity;

import java.util.*;

@AutoFields
@PersistentEntity
@SerializableClass
public class RewardBoxInfo {
    private int c_id;

    private boolean start;

    private int betTimes;

    private Set<Integer> receive = new HashSet<>();

    private Map<Integer, Double> wageredMap = new HashMap<>();

    private Map<Integer, Double> rechargeAmountMap = new HashMap<>();

    public int getBetTimes() {
        return betTimes;
    }

    public void setBetTimes(int betTimes) {
        this.betTimes = betTimes;
    }

    public Set<Integer> getReceive() {
        return receive;
    }

    public void setReceive(Set<Integer> receive) {
        this.receive = receive;
    }

    public Map<Integer, Double> getRechargeAmountMap() {
        return rechargeAmountMap;
    }

    public void setRechargeAmountMap(Map<Integer, Double> rechargeAmountMap) {
        this.rechargeAmountMap = rechargeAmountMap;
    }

    public Map<Integer, Double> getWageredMap() {
        return wageredMap;
    }

    public void setWageredMap(Map<Integer, Double> wageredMap) {
        this.wageredMap = wageredMap;
    }

    public int getC_id() {
        return c_id;
    }

    public void setC_id(int c_id) {
        this.c_id = c_id;
    }

    public boolean isStart() {
        return start;
    }

    public void setStart(boolean start) {
        this.start = start;
    }

    public void incBetTimes() {
        this.betTimes++;
    }

    public void incWagered(int currencyId, double betAmount) {
        double wagered = this.wageredMap.getOrDefault(currencyId, 0d);
        wagered = BigDecimalUtils.add(betAmount, wagered, 9);
        this.wageredMap.put(currencyId, wagered);
    }

    public void incRecharge(int currencyId, double recharge) {
        double rechargeAmount = this.rechargeAmountMap.getOrDefault(currencyId, 0d);
        rechargeAmount = BigDecimalUtils.add(recharge, rechargeAmount, 9);
        this.rechargeAmountMap.put(currencyId, rechargeAmount);
    }

    public void reset() {
        this.betTimes = 0;
        this.receive.clear();
        this.wageredMap.clear();
        this.rechargeAmountMap.clear();
    }
}
