package com.game.entity.player.activity;

import com.game.engine.math.BigDecimalUtils;
import com.wjybxx.io.AutoFields;
import com.wjybxx.io.binary.SerializableClass;
import com.wjybxx.io.document.PersistentEntity;

import java.util.*;

@AutoFields
@PersistentEntity
@SerializableClass
public class ActivityData {
    private int c_id;
    private boolean start;
    private Map<Integer, Long> lastEndTimeMap = new HashMap<>();

    private Map<Integer, Double> progressMap = new HashMap<>();
    private Map<Integer, RewardInfo> receiveRewardsMap = new HashMap<>();
    private Map<Integer, Double> bonusMap = new HashMap<>();
    private Map<Integer, Double> turnoverMap = new HashMap<>();

    //充值
    private long registerExpired;

    //救济
    private Map<Integer, Long> endTimeMap = new HashMap<>();
    private Map<Integer, Double> receiveAmountMap = new HashMap<>();

    private int betTimes;
    private String rankDate;
    private Set<Long> invitePlayerId = new HashSet<>();
    //排行报名
    private Map<Integer, Integer> signUpMap = new LinkedHashMap<>();
    private Map<Integer, Double> wageredMap = new HashMap<>();
    private Map<Integer, Double> rechargeAmountMap = new HashMap<>();

    //免费送
    private Set<Integer> received = new HashSet<>();
    private Set<Integer> notReceived = new HashSet<>();

    public Map<Integer, RewardInfo> getReceiveRewardsMap() {
        return receiveRewardsMap;
    }

    public void setReceiveRewardsMap(Map<Integer, RewardInfo> receiveRewardsMap) {
        this.receiveRewardsMap = receiveRewardsMap;
    }

    public Map<Integer, Double> getBonusMap() {
        return bonusMap;
    }

    public void setBonusMap(Map<Integer, Double> bonusMap) {
        this.bonusMap = bonusMap;
    }

    public Map<Integer, Double> getTurnoverMap() {
        return turnoverMap;
    }

    public void setTurnoverMap(Map<Integer, Double> turnoverMap) {
        this.turnoverMap = turnoverMap;
    }

    public long getRegisterExpired() {
        return registerExpired;
    }

    public void setRegisterExpired(long registerExpired) {
        this.registerExpired = registerExpired;
    }

    public int getC_id() {
        return c_id;
    }

    public void setC_id(int c_id) {
        this.c_id = c_id;
    }

    public boolean isStart() {
        return start;
    }

    public void setStart(boolean start) {
        this.start = start;
    }

    public Map<Integer, Long> getLastEndTimeMap() {
        return lastEndTimeMap;
    }

    public void setLastEndTimeMap(Map<Integer, Long> lastEndTimeMap) {
        this.lastEndTimeMap = lastEndTimeMap;
    }

    public Map<Integer, Double> getProgressMap() {
        return progressMap;
    }

    public void setProgressMap(Map<Integer, Double> progressMap) {
        this.progressMap = progressMap;
    }

    public Map<Integer, Long> getEndTimeMap() {
        return endTimeMap;
    }

    public void setEndTimeMap(Map<Integer, Long> endTimeMap) {
        this.endTimeMap = endTimeMap;
    }

    public Map<Integer, Double> getReceiveAmountMap() {
        return receiveAmountMap;
    }

    public void setReceiveAmountMap(Map<Integer, Double> receiveAmountMap) {
        this.receiveAmountMap = receiveAmountMap;
    }

    public Set<Integer> getReceived() {
        return received;
    }

    public void setReceived(Set<Integer> received) {
        this.received = received;
    }

    public Set<Integer> getNotReceived() {
        return notReceived;
    }

    public void setNotReceived(Set<Integer> notReceived) {
        this.notReceived = notReceived;
    }

    public Map<Integer, Integer> getSignUpMap() {
        return signUpMap;
    }

    public void setSignUpMap(Map<Integer, Integer> signUpMap) {
        this.signUpMap = signUpMap;
    }

    public int getBetTimes() {
        return betTimes;
    }

    public void setBetTimes(int betTimes) {
        this.betTimes = betTimes;
    }

    public String getRankDate() {
        return rankDate;
    }

    public void setRankDate(String rankDate) {
        this.rankDate = rankDate;
    }

    public Set<Long> getInvitePlayerId() {
        return invitePlayerId;
    }

    public void setInvitePlayerId(Set<Long> invitePlayerId) {
        this.invitePlayerId = invitePlayerId;
    }

    public Map<Integer, Double> getWageredMap() {
        return wageredMap;
    }

    public void setWageredMap(Map<Integer, Double> wageredMap) {
        this.wageredMap = wageredMap;
    }

    public Map<Integer, Double> getRechargeAmountMap() {
        return rechargeAmountMap;
    }

    public void setRechargeAmountMap(Map<Integer, Double> rechargeAmountMap) {
        this.rechargeAmountMap = rechargeAmountMap;
    }

    public Set<Integer> getReceiveRewards(int currencyId) {
        RewardInfo rewardInfo = this.receiveRewardsMap.putIfAbsent(currencyId, new RewardInfo());
        if (rewardInfo == null) {
            rewardInfo = this.receiveRewardsMap.get(currencyId);
        }
        return rewardInfo.getRewards();
    }

    public double getProgress(int currencyId) {
        return this.progressMap.getOrDefault(currencyId, 0d);
    }

    public void incProgress(int currencyId, double value) {
        double progress = BigDecimalUtils.add(this.progressMap.getOrDefault(currencyId, 0d), value, 9);
        if (progress <= 0) {
            progress = 0;
        }
        this.progressMap.put(currencyId, progress);
    }

    public void incTurnover(int currencyId, double value) {
        double turnover = this.turnoverMap.getOrDefault(currencyId, 0d);
        turnover = BigDecimalUtils.add(turnover, value, 9);
        this.turnoverMap.put(currencyId, turnover);
    }

    public long getEndTime(int activitySubType) {
        return this.endTimeMap.getOrDefault(activitySubType, 0L);
    }

    public void incBetTimes() {
        this.betTimes++;
    }

    public void incWagered(int currencyId, double betAmount) {
        double wagered = this.wageredMap.getOrDefault(currencyId, 0d);
        wagered = BigDecimalUtils.add(betAmount, wagered, 9);
        this.wageredMap.put(currencyId, wagered);
    }

    public void incRecharge(int currencyId, double recharge) {
        double rechargeAmount = this.rechargeAmountMap.getOrDefault(currencyId, 0d);
        rechargeAmount = BigDecimalUtils.add(recharge, rechargeAmount, 9);
        this.rechargeAmountMap.put(currencyId, rechargeAmount);
    }

    public void incReceiveAmount(int currencyId, double recharge) {
        double receiveAmount = this.receiveAmountMap.getOrDefault(currencyId, 0d);
        receiveAmount = BigDecimalUtils.add(recharge, receiveAmount, 9);
        this.receiveAmountMap.put(currencyId, receiveAmount);
    }

    public void reset() {
        this.progressMap.clear();
        this.receiveRewardsMap.clear();

        this.registerExpired = 0;

        this.endTimeMap.clear();
        this.receiveAmountMap.clear();

        this.betTimes = 0;
        this.rankDate = "";
        this.invitePlayerId.clear();

        this.signUpMap.clear();
        this.wageredMap.clear();
        this.rechargeAmountMap.clear();

        this.received.clear();
        this.notReceived.clear();
    }

    public void resetProcess() {
        this.start = false;
        this.progressMap.clear();
    }
}
