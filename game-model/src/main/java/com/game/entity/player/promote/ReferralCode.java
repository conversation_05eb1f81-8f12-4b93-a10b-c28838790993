package com.game.entity.player.promote;

import com.game.engine.utils.TimeUtil;
import com.wjybxx.io.AutoFields;
import com.wjybxx.io.binary.SerializableClass;
import com.wjybxx.io.document.PersistentEntity;

@AutoFields
@PersistentEntity
@SerializableClass
public class ReferralCode {
    private String name;//名字
    private String code;//推广码
    private String link;//推广连接
    private long createdDate;//创建时间

    public ReferralCode() {
        this.name = "";
        this.code = "";
        this.link = "";
        this.createdDate = TimeUtil.currentTimeMillis();
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getLink() {
        return link;
    }

    public void setLink(String link) {
        this.link = link;
    }

    public long getCreatedDate() {
        return createdDate;
    }

    public void setCreatedDate(long createdDate) {
        this.createdDate = createdDate;
    }

}
