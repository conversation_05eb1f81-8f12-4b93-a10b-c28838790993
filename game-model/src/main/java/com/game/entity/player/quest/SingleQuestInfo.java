package com.game.entity.player.quest;

import com.wjybxx.io.AutoFields;
import com.wjybxx.io.binary.SerializableClass;
import com.wjybxx.io.document.PersistentEntity;

@AutoFields
@PersistentEntity
@SerializableClass
public class SingleQuestInfo {

    /** 当前任务id */
    private int questId;
    /** 任务类型 */
    private int questType;
    /** 目标类型 */
    private int goalType;
    /** 任务状态 */
    private int state;
    /** 完成时间*/
    private long finishedTime;

    /** 默认0，表示无数据。暂认为不会越界 */
    private double progressive;

    public int getQuestId() {
        return questId;
    }

    public void setQuestId(int questId) {
        this.questId = questId;
    }

    public int getQuestType() {
        return questType;
    }

    public void setQuestType(int questType) {
        this.questType = questType;
    }

    public int getGoalType() {
        return goalType;
    }

    public void setGoalType(int goalType) {
        this.goalType = goalType;
    }

    public int getState() {
        return state;
    }

    public void setState(int state) {
        this.state = state;
    }

    public long getFinishedTime() {
        return finishedTime;
    }

    public void setFinishedTime(long finishedTime) {
        this.finishedTime = finishedTime;
    }

    public double getProgressive() {
        return progressive;
    }

    public void setProgressive(double progressive) {
        this.progressive = progressive;
    }

}
