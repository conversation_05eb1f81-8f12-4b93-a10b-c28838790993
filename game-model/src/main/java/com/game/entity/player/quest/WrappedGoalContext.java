package com.game.entity.player.quest;


/** 可能还需要存储一些信息用于update */
public class WrappedGoalContext {

    final public int goalType;
    final public SingleQuestInfo singleQuestInfo;
    final public GoalCallback callback;

    public WrappedGoalContext(SingleQuestInfo singleQuestInfo, int goalType, GoalCallback callback) {
        this.singleQuestInfo = singleQuestInfo;
        this.goalType = goalType;
        this.callback = callback;
    }
}