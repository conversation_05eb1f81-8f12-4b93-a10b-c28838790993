package com.game.entity.player.inbox;

import com.game.engine.utils.TimeUtil;
import com.wjybxx.io.AutoFields;
import com.wjybxx.io.binary.SerializableClass;
import com.wjybxx.io.document.PersistentEntity;

@AutoFields
@PersistentEntity
@SerializableClass
public class Inbox {
    private long inboxId;
    //模板id
    private int modelId;
    //创建的时间戳
    private long createTime;
    //邮件类型
    private int inboxType;
    //语言
    private int language;
    //标题
    private String title;
    //内容
    private String context;
    //图片文字
    private String imageText;

    //是否有弹窗
    private int isJump;
    //跳转类型
    private int jumpType;
    //弹框类型 1.任务 2.转盘 3.充值 4.客服
    private int popupLinks;
    //内部链接
    private String innerLinks;
    //外链接
    private String externalLinks;

    //文件地址
    private String fileUrl;
    //过期时间 （暂时都认为走表格的过期时间）
    private long expireTime;
    //是否已读
    private boolean read;

    public Inbox() {

    }

    public Inbox(long inboxId) {
        this.inboxId = inboxId;
        this.createTime = TimeUtil.currentTimeMillis();
    }

    public long getInboxId() {
        return inboxId;
    }

    public void setInboxId(long inboxId) {
        this.inboxId = inboxId;
    }

    public int getModelId() {
        return modelId;
    }

    public void setModelId(int modelId) {
        this.modelId = modelId;
    }

    public long getCreateTime() {
        return createTime;
    }

    public void setCreateTime(long createTime) {
        this.createTime = createTime;
    }

    public int getInboxType() {
        return inboxType;
    }

    public void setInboxType(int inboxType) {
        this.inboxType = inboxType;
    }

    public int getLanguage() {
        return language;
    }

    public void setLanguage(int language) {
        this.language = language;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getContext() {
        return context;
    }

    public void setContext(String context) {
        this.context = context;
    }

    public String getImageText() {
        return imageText;
    }

    public void setImageText(String imageText) {
        this.imageText = imageText;
    }

    public int getIsJump() {
        return isJump;
    }

    public void setIsJump(int isJump) {
        this.isJump = isJump;
    }

    public int getJumpType() {
        return jumpType;
    }

    public void setJumpType(int jumpType) {
        this.jumpType = jumpType;
    }

    public int getPopupLinks() {
        return popupLinks;
    }

    public void setPopupLinks(int popupLinks) {
        this.popupLinks = popupLinks;
    }

    public String getInnerLinks() {
        return innerLinks;
    }

    public void setInnerLinks(String innerLinks) {
        this.innerLinks = innerLinks;
    }

    public String getExternalLinks() {
        return externalLinks;
    }

    public void setExternalLinks(String externalLinks) {
        this.externalLinks = externalLinks;
    }

    public String getFileUrl() {
        return fileUrl;
    }

    public void setFileUrl(String fileUrl) {
        this.fileUrl = fileUrl;
    }

    public long getExpireTime() {
        return expireTime;
    }

    public void setExpireTime(long expireTime) {
        this.expireTime = expireTime;
    }

    public boolean isRead() {
        return read;
    }

    public void setRead(boolean read) {
        this.read = read;
    }

}
