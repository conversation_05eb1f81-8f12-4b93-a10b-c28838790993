package com.game.entity.player;

import com.wjybxx.io.AutoFields;
import com.wjybxx.io.binary.SerializableClass;
import com.wjybxx.io.document.PersistentEntity;

import java.util.LinkedHashMap;
import java.util.Map;

@AutoFields
@PersistentEntity
@SerializableClass
public class WithdrawAccount {
    private Map<Long, PayInfo> payInfoMap = new LinkedHashMap<>();

    public Map<Long, PayInfo> getPayInfoMap() {
        return payInfoMap;
    }

    public void setPayInfoMap(Map<Long, PayInfo> payInfoMap) {
        this.payInfoMap = payInfoMap;
    }
}
