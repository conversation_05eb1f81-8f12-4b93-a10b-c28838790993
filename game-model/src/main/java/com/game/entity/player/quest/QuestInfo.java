package com.game.entity.player.quest;


import com.game.engine.math.BigDecimalUtils;
import com.wjybxx.io.AutoFields;
import com.wjybxx.io.binary.SerializableClass;
import com.wjybxx.io.document.PersistentEntity;

import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.Map;

@AutoFields
@PersistentEntity
@SerializableClass
public class QuestInfo {
    /**
     * 累计奖励
     */
    private Map<Integer, Double> accumulatedRewards = new HashMap<>();

    /**
     * 所有的任务集合
     */
    private Map<Integer, SingleQuestInfo> questInfoMap = new LinkedHashMap<>();

    /**
     * 已完成的任务Id,次数，仅日常可完成多次
     */
//    private HashMap<Integer, Integer> finishQuestCount = new HashMap<>();

    public Map<Integer, SingleQuestInfo> getQuestInfoMap() {
        return questInfoMap;
    }

    public void setQuestInfoMap(Map<Integer, SingleQuestInfo> questInfoMap) {
        this.questInfoMap = questInfoMap;
    }

    public Map<Integer, Double> getAccumulatedRewards() {
        return accumulatedRewards;
    }

    public void setAccumulatedRewards(Map<Integer, Double> accumulatedRewards) {
        this.accumulatedRewards = accumulatedRewards;
    }

    public void incAccumulatedRewards(int currencyId, double reward) {
        double value = this.accumulatedRewards.getOrDefault(currencyId, 0d);
        value = BigDecimalUtils.add(reward, value, 9);
        this.accumulatedRewards.put(currencyId, value);
    }
}
