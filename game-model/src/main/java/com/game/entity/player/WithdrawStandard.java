package com.game.entity.player;

import com.game.engine.math.BigDecimalUtils;
import com.wjybxx.io.AutoFields;
import com.wjybxx.io.binary.SerializableClass;
import com.wjybxx.io.document.PersistentEntity;

@AutoFields
@PersistentEntity
@SerializableClass
public class WithdrawStandard {
    //提现后的累计充值
    private double totalRecharge;

    //提现要求
    private double drawStandard;
    //提现打码量
    private double bettingVolume;

    private double lastDrawStandard;
    private double lastBettingVolume;

    public double getTotalRecharge() {
        return totalRecharge;
    }

    public void setTotalRecharge(double totalRecharge) {
        this.totalRecharge = totalRecharge;
    }

    public double getDrawStandard() {
        return drawStandard;
    }

    public void setDrawStandard(double drawStandard) {
        this.drawStandard = drawStandard;
    }

    public double getBettingVolume() {
        return bettingVolume;
    }

    public void setBettingVolume(double bettingVolume) {
        this.bettingVolume = bettingVolume;
    }

    public double getLastDrawStandard() {
        return lastDrawStandard;
    }

    public void setLastDrawStandard(double lastDrawStandard) {
        this.lastDrawStandard = lastDrawStandard;
    }

    public double getLastBettingVolume() {
        return lastBettingVolume;
    }

    public void setLastBettingVolume(double lastBettingVolume) {
        this.lastBettingVolume = lastBettingVolume;
    }

    public void incTotalRecharge(double amount) {
        this.totalRecharge = BigDecimalUtils.add(this.totalRecharge, amount, 9);
    }

    public void incDrawStandard(double amount) {
        this.drawStandard = BigDecimalUtils.add(this.drawStandard, amount, 9);
        if (this.drawStandard <= 0) {
            this.drawStandard = 0;
        }
    }

    public void incBettingTurnover(double amount) {
        this.bettingVolume = BigDecimalUtils.add(this.bettingVolume, amount, 9);
        if (this.bettingVolume <= 0) {
            this.bettingVolume = 0;
        }
    }

    public void reset() {
        this.drawStandard = 0;
        this.bettingVolume = 0;
    }

    public void resetTemp() {
        this.lastDrawStandard = 0;
        this.lastBettingVolume = 0;
    }
}
