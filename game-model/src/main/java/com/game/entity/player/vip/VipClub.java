package com.game.entity.player.vip;

import com.game.engine.math.BigDecimalUtils;
import com.game.engine.utils.TimeUtil;
import com.wjybxx.io.AutoFields;
import com.wjybxx.io.binary.SerializableClass;
import com.wjybxx.io.document.PersistentEntity;

import java.util.*;


@AutoFields
@PersistentEntity
@SerializableClass
public class VipClub {
    //vip等级
    private int vipLevel;

    //当前经验
    private double curExp;

    //当前充值
    private double curRecharge;

    //更新时间
    private long vipUpdateTime;

    //保级下注
    private double relegationWagered;

    //保级充值
    private double relegationRecharge;

    //保级时间
    private long relegationTime;

    private int currDay;

    private boolean activation;

    private long activationTime;

    private Map<Integer, Double> dayBackCash = new HashMap<>();
    private Map<Integer, Double> lastDayBackCash = new HashMap<>();
    private Map<Integer, Double> playerWageredMap = new HashMap<>();

    private List<Integer> receiveDays = new ArrayList<>();

    private Set<Integer> receiveReward = new HashSet<>();
    private Map<Integer, Double> dailyWageredMap = new HashMap<>();
    private Map<Integer, Double> weeklyWageredMap = new HashMap<>();
    private Map<Integer, Double> monthlyWageredMap = new HashMap<>();

    public int getVipLevel() {
        return vipLevel;
    }

    public void setVipLevel(int vipLevel) {
        this.vipLevel = vipLevel;
    }

    public double getCurExp() {
        return curExp;
    }

    public void setCurExp(double curExp) {
        this.curExp = curExp;
    }

    public double getCurRecharge() {
        return curRecharge;
    }

    public void setCurRecharge(double curRecharge) {
        this.curRecharge = curRecharge;
    }

    public long getVipUpdateTime() {
        return vipUpdateTime;
    }

    public void setVipUpdateTime(long vipUpdateTime) {
        this.vipUpdateTime = vipUpdateTime;
    }

    public double getRelegationRecharge() {
        return relegationRecharge;
    }

    public void setRelegationRecharge(double relegationRecharge) {
        this.relegationRecharge = relegationRecharge;
    }

    public double getRelegationWagered() {
        return relegationWagered;
    }

    public void setRelegationWagered(double relegationWagered) {
        this.relegationWagered = relegationWagered;
    }

    public long getRelegationTime() {
        return relegationTime;
    }

    public void setRelegationTime(long relegationTime) {
        this.relegationTime = relegationTime;
    }

    public List<Integer> getReceiveDays() {
        return receiveDays;
    }

    public void setReceiveDays(List<Integer> receiveDays) {
        this.receiveDays = receiveDays;
    }

    public Set<Integer> getReceiveReward() {
        return receiveReward;
    }

    public void setReceiveReward(Set<Integer> receiveReward) {
        this.receiveReward = receiveReward;
    }

    public int getCurrDay() {
        return currDay;
    }

    public void setCurrDay(int currDay) {
        this.currDay = currDay;
    }

    public boolean isActivation() {
        return activation;
    }

    public void setActivation(boolean activation) {
        this.activation = activation;
    }

    public long getActivationTime() {
        return activationTime;
    }

    public void setActivationTime(long activationTime) {
        this.activationTime = activationTime;
    }

    public Map<Integer, Double> getDayBackCash() {
        return dayBackCash;
    }

    public void setDayBackCash(Map<Integer, Double> dayBackCash) {
        this.dayBackCash = dayBackCash;
    }

    public Map<Integer, Double> getLastDayBackCash() {
        return lastDayBackCash;
    }

    public void setLastDayBackCash(Map<Integer, Double> lastDayBackCash) {
        this.lastDayBackCash = lastDayBackCash;
    }

    public Map<Integer, Double> getPlayerWageredMap() {
        return playerWageredMap;
    }

    public void setPlayerWageredMap(Map<Integer, Double> playerWageredMap) {
        this.playerWageredMap = playerWageredMap;
    }

    public Map<Integer, Double> getDailyWageredMap() {
        return dailyWageredMap;
    }

    public void setDailyWageredMap(Map<Integer, Double> dailyWageredMap) {
        this.dailyWageredMap = dailyWageredMap;
    }

    public Map<Integer, Double> getWeeklyWageredMap() {
        return weeklyWageredMap;
    }

    public void setWeeklyWageredMap(Map<Integer, Double> weeklyWageredMap) {
        this.weeklyWageredMap = weeklyWageredMap;
    }

    public Map<Integer, Double> getMonthlyWageredMap() {
        return monthlyWageredMap;
    }

    public void setMonthlyWageredMap(Map<Integer, Double> monthlyWageredMap) {
        this.monthlyWageredMap = monthlyWageredMap;
    }

    public void incRelegationWagered(double wagered) {
        this.relegationWagered = BigDecimalUtils.add(this.relegationWagered, wagered, 2);
    }

    public void incRelegationRecharge(double recharge) {
        this.relegationRecharge = BigDecimalUtils.add(this.relegationRecharge, recharge, 2);
    }

    public void relegationReset(int day) {
        this.relegationWagered = 0;
        this.relegationRecharge = 0;
        this.relegationTime = TimeUtil.currentTimeMillis() + day * TimeUtil.DAY;
    }

    public void incDailyWagered(int currencyId, double betAmount) {
        double wagered = this.dailyWageredMap.getOrDefault(currencyId, 0d);
        wagered = BigDecimalUtils.add(betAmount, wagered, 9);
        this.dailyWageredMap.put(currencyId, wagered);
    }

    public void incWeeklyWagered(int currencyId, double betAmount) {
        double wagered = this.weeklyWageredMap.getOrDefault(currencyId, 0d);
        wagered = BigDecimalUtils.add(betAmount, wagered, 9);
        this.weeklyWageredMap.put(currencyId, wagered);
    }

    public void incMonthlyWagered(int currencyId, double betAmount) {
        double wagered = this.monthlyWageredMap.getOrDefault(currencyId, 0d);
        wagered = BigDecimalUtils.add(betAmount, wagered, 9);
        this.monthlyWageredMap.put(currencyId, wagered);
    }

    public void incDayBackCash(int currencyId, double baskCash) {
        double amount = this.dayBackCash.getOrDefault(currencyId, 0d);
        amount = BigDecimalUtils.add(baskCash, amount, 9);
        this.dayBackCash.put(currencyId, amount);
    }

    public void incPlayerWagered(int currencyId, double wagered) {
        double amount = this.playerWageredMap.getOrDefault(currencyId, 0d);
        amount = BigDecimalUtils.add(wagered, amount, 9);
        this.playerWageredMap.put(currencyId, amount);
    }

}
