package com.game.entity.player.activity.dailyContest;

import com.game.engine.math.BigDecimalUtils;
import com.wjybxx.io.AutoFields;
import com.wjybxx.io.binary.SerializableClass;
import com.wjybxx.io.document.PersistentEntity;

@AutoFields
@PersistentEntity
@SerializableClass
public class DailyContestInfo {

    private String gameId;

    private double usdWagered;

    public String getGameId() {
        return gameId;
    }

    public void setGameId(String gameId) {
        this.gameId = gameId;
    }

    public double getUsdWagered() {
        return usdWagered;
    }

    public void setUsdWagered(double usdWagered) {
        this.usdWagered = usdWagered;
    }

    public void incUsdWagered(double usdWager) {
        this.usdWagered = BigDecimalUtils.add(usdWagered, usdWager, 9);
    }

    public void reset() {
        this.gameId = "";
        this.usdWagered = 0;
    }
}
