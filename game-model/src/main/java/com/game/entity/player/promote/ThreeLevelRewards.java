package com.game.entity.player.promote;

import com.game.engine.math.BigDecimalUtils;
import com.wjybxx.io.AutoFields;
import com.wjybxx.io.binary.SerializableClass;
import com.wjybxx.io.document.PersistentEntity;

@AutoFields
@PersistentEntity
@SerializableClass
public class ThreeLevelRewards {
    //可用
    private double available;

    //总收入
    private double totalReceived;

    public double getAvailable() {
        return available;
    }

    public void setAvailable(double available) {
        this.available = available;
    }

    public double getTotalReceived() {
        return totalReceived;
    }

    public void setTotalReceived(double totalReceived) {
        this.totalReceived = totalReceived;
    }

    public void incAvailable(double commission) {
        this.available = BigDecimalUtils.add(available, commission, 9);
    }

    public void incTotalReceived(double commission) {
        this.totalReceived = BigDecimalUtils.add(totalReceived, commission, 9);
    }

    public void reset() {
        this.available = 0;
    }
}
