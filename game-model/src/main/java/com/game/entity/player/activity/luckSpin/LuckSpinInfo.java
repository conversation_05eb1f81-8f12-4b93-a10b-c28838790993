package com.game.entity.player.activity.luckSpin;

import com.wjybxx.io.AutoFields;
import com.wjybxx.io.binary.SerializableClass;
import com.wjybxx.io.document.PersistentEntity;

import java.util.LinkedHashMap;
import java.util.Map;

@AutoFields
@PersistentEntity
@SerializableClass
public class LuckSpinInfo {

    private Map<Integer, LuckSpinData> luckSpinDataMap = new LinkedHashMap<>();

    public Map<Integer, LuckSpinData> getLuckSpinDataMap() {
        return luckSpinDataMap;
    }

    public void setLuckSpinDataMap(Map<Integer, LuckSpinData> luckSpinDataMap) {
        this.luckSpinDataMap = luckSpinDataMap;
    }

    public LuckSpinData getLuckSpinData(int activityId) {
        return luckSpinDataMap.get(activityId);
    }

}
