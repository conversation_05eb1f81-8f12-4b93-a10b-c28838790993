package com.game.entity.player.promote;

import com.game.engine.math.BigDecimalUtils;
import com.wjybxx.io.AutoFields;
import com.wjybxx.io.binary.SerializableClass;
import com.wjybxx.io.document.PersistentEntity;

@AutoFields
@PersistentEntity
@SerializableClass
public class ReferralRewards {

    private int currencyId;

    //可用
    private double available;

    //总收入(earned)
    private double totalReceived;

    public int getCurrencyId() {
        return currencyId;
    }

    public void setCurrencyId(int currencyId) {
        this.currencyId = currencyId;
    }

    public double getAvailable() {
        return available;
    }

    public void setAvailable(double available) {
        this.available = available;
    }

    public double getTotalReceived() {
        return totalReceived;
    }

    public void setTotalReceived(double totalReceived) {
        this.totalReceived = totalReceived;
    }

    public void incAvailable(double rewards) {
        this.available = BigDecimalUtils.add(available, rewards, 9);
    }

    public void incTotalReceived(double rewards) {
        this.totalReceived = BigDecimalUtils.add(totalReceived, rewards, 9);
    }

    public void reset() {
        this.available = 0;
    }
}
