package com.game.entity.player.activity.mysteryBonus;

import com.game.engine.math.BigDecimalUtils;
import com.wjybxx.io.AutoFields;
import com.wjybxx.io.binary.SerializableClass;
import com.wjybxx.io.document.PersistentEntity;

import java.util.*;

@AutoFields
@PersistentEntity
@SerializableClass
public class MysteryBonusInfo {
    private int c_id;

    private boolean start;

    private Set<Integer> receive = new HashSet<>();

    private Map<Integer, Double> rechargeAmountMap = new HashMap<>();

    private Map<Integer, MysteryRewardInfo> mysteryRewardInfoMap = new LinkedHashMap<>();

    public int getC_id() {
        return c_id;
    }

    public void setC_id(int c_id) {
        this.c_id = c_id;
    }

    public boolean isStart() {
        return start;
    }

    public void setStart(boolean start) {
        this.start = start;
    }

    public Set<Integer> getReceive() {
        return receive;
    }

    public void setReceive(Set<Integer> receive) {
        this.receive = receive;
    }

    public Map<Integer, Double> getRechargeAmountMap() {
        return rechargeAmountMap;
    }

    public void setRechargeAmountMap(Map<Integer, Double> rechargeAmountMap) {
        this.rechargeAmountMap = rechargeAmountMap;
    }

    public Map<Integer, MysteryRewardInfo> getMysteryRewardInfoMap() {
        return mysteryRewardInfoMap;
    }

    public void setMysteryRewardInfoMap(Map<Integer, MysteryRewardInfo> mysteryRewardInfoMap) {
        this.mysteryRewardInfoMap = mysteryRewardInfoMap;
    }

    public void incRecharge(int currencyId, double recharge) {
        double rechargeAmount = this.rechargeAmountMap.getOrDefault(currencyId, 0d);
        rechargeAmount = BigDecimalUtils.add(recharge, rechargeAmount, 9);
        this.rechargeAmountMap.put(currencyId, rechargeAmount);
    }

    public void reset() {
        this.receive.clear();
        this.rechargeAmountMap.clear();
        this.mysteryRewardInfoMap.clear();
    }
}
