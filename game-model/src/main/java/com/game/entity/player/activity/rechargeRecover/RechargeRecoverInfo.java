package com.game.entity.player.activity.rechargeRecover;

import com.wjybxx.io.AutoFields;
import com.wjybxx.io.binary.SerializableClass;
import com.wjybxx.io.document.PersistentEntity;

import java.util.HashMap;
import java.util.Map;

@AutoFields
@PersistentEntity
@SerializableClass
public class RechargeRecoverInfo {
    private int c_id;

    private Map<Integer, Double> currRechargeMap = new HashMap<>();

    private boolean status;

    private Map<Integer, Double> wageredMap = new HashMap<>();
    private Map<Integer, Double> rechargeAmountMap = new HashMap<>();
    private Map<Integer, Double> redEnvelopeRainMap = new HashMap<>();

    public int getC_id() {
        return c_id;
    }

    public void setC_id(int c_id) {
        this.c_id = c_id;
    }

    public Map<Integer, Double> getCurrRechargeMap() {
        return currRechargeMap;
    }

    public void setCurrRechargeMap(Map<Integer, Double> currRechargeMap) {
        this.currRechargeMap = currRechargeMap;
    }

    public boolean isStatus() {
        return status;
    }

    public void setStatus(boolean status) {
        this.status = status;
    }

    public Map<Integer, Double> getWageredMap() {
        return wageredMap;
    }

    public void setWageredMap(Map<Integer, Double> wageredMap) {
        this.wageredMap = wageredMap;
    }

    public Map<Integer, Double> getRechargeAmountMap() {
        return rechargeAmountMap;
    }

    public void setRechargeAmountMap(Map<Integer, Double> rechargeAmountMap) {
        this.rechargeAmountMap = rechargeAmountMap;
    }

    public Map<Integer, Double> getRedEnvelopeRainMap() {
        return redEnvelopeRainMap;
    }

    public void setRedEnvelopeRainMap(Map<Integer, Double> redEnvelopeRainMap) {
        this.redEnvelopeRainMap = redEnvelopeRainMap;
    }

    public void reset() {
        this.status = false;
        this.currRechargeMap.clear();
    }
}
