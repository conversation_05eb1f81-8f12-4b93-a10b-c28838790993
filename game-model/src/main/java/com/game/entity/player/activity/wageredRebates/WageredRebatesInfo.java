package com.game.entity.player.activity.wageredRebates;

import com.game.engine.math.BigDecimalUtils;
import com.wjybxx.io.AutoFields;
import com.wjybxx.io.binary.SerializableClass;
import com.wjybxx.io.document.PersistentEntity;

import java.util.HashMap;
import java.util.Map;

@AutoFields
@PersistentEntity
@SerializableClass
public class WageredRebatesInfo {
    private int c_id;

    private boolean start;

    private long endTime;

    private boolean status;

    private long lastEndTime;

    private Map<Integer, Double> wageredMap = new HashMap<>();
    private Map<Integer, Double> lossMap = new HashMap<>();

    private Map<Integer, Double> lastWageredMap = new HashMap<>();
    private Map<Integer, Double> lastLossMap = new HashMap<>();

    private Map<Integer, Double> bonusMap = new HashMap<>();

    public int getC_id() {
        return c_id;
    }

    public void setC_id(int c_id) {
        this.c_id = c_id;
    }

    public boolean isStart() {
        return start;
    }

    public void setStart(boolean start) {
        this.start = start;
    }

    public long getEndTime() {
        return endTime;
    }

    public void setEndTime(long endTime) {
        this.endTime = endTime;
    }

    public boolean isStatus() {
        return status;
    }

    public void setStatus(boolean status) {
        this.status = status;
    }

    public long getLastEndTime() {
        return lastEndTime;
    }

    public void setLastEndTime(long lastEndTime) {
        this.lastEndTime = lastEndTime;
    }

    public Map<Integer, Double> getWageredMap() {
        return wageredMap;
    }

    public void setWageredMap(Map<Integer, Double> wageredMap) {
        this.wageredMap = wageredMap;
    }

    public Map<Integer, Double> getLastWageredMap() {
        return lastWageredMap;
    }

    public void setLastWageredMap(Map<Integer, Double> lastWageredMap) {
        this.lastWageredMap = lastWageredMap;
    }

    public Map<Integer, Double> getLastLossMap() {
        return lastLossMap;
    }

    public void setLastLossMap(Map<Integer, Double> lastLossMap) {
        this.lastLossMap = lastLossMap;
    }

    public Map<Integer, Double> getLossMap() {
        return lossMap;
    }

    public void setLossMap(Map<Integer, Double> lossMap) {
        this.lossMap = lossMap;
    }

    public Map<Integer, Double> getBonusMap() {
        return bonusMap;
    }

    public void setBonusMap(Map<Integer, Double> bonusMap) {
        this.bonusMap = bonusMap;
    }

    public void incWagered(int currencyId, double amount) {
        double wagered = this.wageredMap.getOrDefault(currencyId, 0.0);
        wagered = BigDecimalUtils.add(wagered, amount, 9);
        this.wageredMap.put(currencyId, wagered);
    }

    public void incLoss(int currencyId, double progress) {
        double amount = this.lossMap.getOrDefault(currencyId, 0.0);
        amount = BigDecimalUtils.add(amount, progress, 9);
        this.lossMap.put(currencyId, amount);
    }

    public void reset() {
        this.c_id = 0;
        this.start = false;
        this.wageredMap.clear();
        this.lossMap.clear();
    }
}
