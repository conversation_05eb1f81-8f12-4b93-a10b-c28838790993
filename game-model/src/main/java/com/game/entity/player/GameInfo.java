package com.game.entity.player;

import com.wjybxx.io.AutoFields;
import com.wjybxx.io.binary.SerializableClass;
import com.wjybxx.io.document.PersistentEntity;

@AutoFields
@PersistentEntity
@SerializableClass
public class GameInfo {
    private int freeTimes;

    private double bet;

    private double minWithdraw;

    private double maxWithdraw;

    private String referenceId;

    public int getFreeTimes() {
        return freeTimes;
    }

    public void setFreeTimes(int freeTimes) {
        this.freeTimes = freeTimes;
    }

    public double getBet() {
        return bet;
    }

    public void setBet(double bet) {
        this.bet = bet;
    }

    public double getMinWithdraw() {
        return minWithdraw;
    }

    public void setMinWithdraw(double minWithdraw) {
        this.minWithdraw = minWithdraw;
    }

    public double getMaxWithdraw() {
        return maxWithdraw;
    }

    public void setMaxWithdraw(double maxWithdraw) {
        this.maxWithdraw = maxWithdraw;
    }

    public String getReferenceId() {
        return referenceId;
    }

    public void setReferenceId(String referenceId) {
        this.referenceId = referenceId;
    }

    public void incFreeTimes(int freeTimes) {
        this.freeTimes += freeTimes;
        if (this.freeTimes <= 0) {
            this.freeTimes = 0;
        }
    }
}
