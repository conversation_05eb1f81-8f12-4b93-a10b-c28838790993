package com.game.entity.player.activity.piggyBank;

import com.game.engine.math.BigDecimalUtils;
import com.wjybxx.io.AutoFields;
import com.wjybxx.io.binary.SerializableClass;
import com.wjybxx.io.document.PersistentEntity;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@AutoFields
@PersistentEntity
@SerializableClass
public class PiggyBankInfo {
    private int c_id;

    private int gearId;

    private int currDay;

    private long receiveTime;

    private List<Integer> receive = new ArrayList<>();

    private Map<Integer, Double> rechargeAmountMap = new HashMap<>();

    public int getC_id() {
        return c_id;
    }

    public void setC_id(int c_id) {
        this.c_id = c_id;
    }

    public int getGearId() {
        return gearId;
    }

    public void setGearId(int gearId) {
        this.gearId = gearId;
    }

    public int getCurrDay() {
        return currDay;
    }

    public void setCurrDay(int currDay) {
        this.currDay = currDay;
    }

    public long getReceiveTime() {
        return receiveTime;
    }

    public void setReceiveTime(long receiveTime) {
        this.receiveTime = receiveTime;
    }

    public List<Integer> getReceive() {
        return receive;
    }

    public void setReceive(List<Integer> receive) {
        this.receive = receive;
    }

    public Map<Integer, Double> getRechargeAmountMap() {
        return rechargeAmountMap;
    }

    public void setRechargeAmountMap(Map<Integer, Double> rechargeAmountMap) {
        this.rechargeAmountMap = rechargeAmountMap;
    }

    public void incRecharge(int currencyId, double recharge) {
        double rechargeAmount = this.rechargeAmountMap.getOrDefault(currencyId, 0d);
        rechargeAmount = BigDecimalUtils.add(recharge, rechargeAmount, 9);
        this.rechargeAmountMap.put(currencyId, rechargeAmount);
    }

    public void reset() {
        this.gearId = 0;
        this.currDay = 0;
        this.receiveTime = 0;
        this.receive.clear();
        this.rechargeAmountMap.clear();
    }
}
