package com.game.entity.player;

import com.game.engine.utils.TimeUtil;
import com.wjybxx.io.AutoFields;
import com.wjybxx.io.binary.SerializableClass;
import com.wjybxx.io.document.PersistentEntity;

@AutoFields
@PersistentEntity
@SerializableClass
public class TurnoverRecord {

    private long orderId;

    private int currencyId;

    private double amount;

    private int turnoverType;

    //提现要求
    private double drawStandard;

    //提现打码量
    private double bettingVolume;

    private long createTime;

    public TurnoverRecord() {
        this.createTime = TimeUtil.currentTimeMillis();
    }

    public long getOrderId() {
        return orderId;
    }

    public void setOrderId(long orderId) {
        this.orderId = orderId;
    }

    public int getCurrencyId() {
        return currencyId;
    }

    public void setCurrencyId(int currencyId) {
        this.currencyId = currencyId;
    }

    public double getAmount() {
        return amount;
    }

    public void setAmount(double amount) {
        this.amount = amount;
    }

    public int getTurnoverType() {
        return turnoverType;
    }

    public void setTurnoverType(int turnoverType) {
        this.turnoverType = turnoverType;
    }

    public double getDrawStandard() {
        return drawStandard;
    }

    public void setDrawStandard(double drawStandard) {
        this.drawStandard = drawStandard;
    }

    public double getBettingVolume() {
        return bettingVolume;
    }

    public void setBettingVolume(double bettingVolume) {
        this.bettingVolume = bettingVolume;
    }

    public long getCreateTime() {
        return createTime;
    }

    public void setCreateTime(long createTime) {
        this.createTime = createTime;
    }
}
