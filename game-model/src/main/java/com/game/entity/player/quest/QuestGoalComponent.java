package com.game.entity.player.quest;

import it.unimi.dsi.fastutil.ints.Int2ObjectMap;
import it.unimi.dsi.fastutil.ints.Int2ObjectOpenHashMap;

public class QuestGoalComponent {

    /** 不迭代，仅仅是方便根据id查询，删除和插入时都直接进行 */
    private final Int2ObjectMap<WrappedGoalContext> handlerContextMap = new Int2ObjectOpenHashMap<>(20);

    public Int2ObjectMap<WrappedGoalContext> getHandlerContextMap() {
        return handlerContextMap;
    }

}