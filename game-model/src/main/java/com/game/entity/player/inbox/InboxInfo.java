package com.game.entity.player.inbox;

import com.wjybxx.io.AutoFields;
import com.wjybxx.io.binary.SerializableClass;
import com.wjybxx.io.document.PersistentEntity;

import java.util.HashSet;
import java.util.LinkedHashMap;
import java.util.Map;
import java.util.Set;

@AutoFields
@PersistentEntity
@SerializableClass
public class InboxInfo {
    private Map<Long, Inbox> inboxMap = new LinkedHashMap<>();

    private Set<Long> receivedPublicMails = new HashSet<>();

    public Map<Long, Inbox> getInboxMap() {
        return inboxMap;
    }

    public void setInboxMap(Map<Long, Inbox> inboxMap) {
        this.inboxMap = inboxMap;
    }

    public Set<Long> getReceivedPublicMails() {
        return receivedPublicMails;
    }

    public void setReceivedPublicMails(Set<Long> receivedPublicMails) {
        this.receivedPublicMails = receivedPublicMails;
    }
}
