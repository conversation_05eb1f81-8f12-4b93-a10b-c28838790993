package com.game.entity.player.inbox;

import com.wjybxx.io.AutoFields;
import com.wjybxx.io.binary.SerializableClass;
import com.wjybxx.io.document.PersistentEntity;

import java.util.HashSet;
import java.util.LinkedHashMap;
import java.util.Map;
import java.util.Set;

@AutoFields
@PersistentEntity
@SerializableClass
public class InboxInfo {
    private Map<Long, Inbox> inboxMap = new LinkedHashMap<>();

    private Set<Long> receivedPublicMails = new HashSet<>();

    private Set<Long> receivedDayMails = new HashSet<>();
    private Set<Long> receivedWeeklyMails = new HashSet<>();

    public Map<Long, Inbox> getInboxMap() {
        return inboxMap;
    }

    public void setInboxMap(Map<Long, Inbox> inboxMap) {
        this.inboxMap = inboxMap;
    }

    public Set<Long> getReceivedPublicMails() {
        return receivedPublicMails;
    }

    public void setReceivedPublicMails(Set<Long> receivedPublicMails) {
        this.receivedPublicMails = receivedPublicMails;
    }

    public Set<Long> getReceivedDayMails() {
        return receivedDayMails;
    }

    public void setReceivedDayMails(Set<Long> receivedDayMails) {
        this.receivedDayMails = receivedDayMails;
    }

    public Set<Long> getReceivedWeeklyMails() {
        return receivedWeeklyMails;
    }

    public void setReceivedWeeklyMails(Set<Long> receivedWeeklyMails) {
        this.receivedWeeklyMails = receivedWeeklyMails;
    }

    public void resetDay(){
        receivedDayMails.clear();
    }

    public void resetWeekly(){
        receivedWeeklyMails.clear();
    }
}
