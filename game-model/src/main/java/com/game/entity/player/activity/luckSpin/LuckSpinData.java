package com.game.entity.player.activity.luckSpin;

import com.game.engine.math.BigDecimalUtils;
import com.wjybxx.io.AutoFields;
import com.wjybxx.io.binary.SerializableClass;
import com.wjybxx.io.document.PersistentEntity;

import java.util.LinkedHashMap;
import java.util.Map;

@AutoFields
@PersistentEntity
@SerializableClass
public class LuckSpinData {

    private int uniqueId;

    private boolean start;

    private int totalTimes;

    private int remainTimes;

    private long expiredTime;

    private boolean finish;

    private boolean first;

    private Map<Integer, Double> currentRewardsMap = new LinkedHashMap<>();

    private LuckCondition luckCondition = new LuckCondition();

    public int getUniqueId() {
        return uniqueId;
    }

    public void setUniqueId(int uniqueId) {
        this.uniqueId = uniqueId;
    }

    public int getTotalTimes() {
        return totalTimes;
    }

    public void setTotalTimes(int totalTimes) {
        this.totalTimes = totalTimes;
    }

    public int getRemainTimes() {
        return remainTimes;
    }

    public void setRemainTimes(int remainTimes) {
        this.remainTimes = remainTimes;
    }

    public boolean isStart() {
        return start;
    }

    public void setStart(boolean start) {
        this.start = start;
    }

    public long getExpiredTime() {
        return expiredTime;
    }

    public void setExpiredTime(long expiredTime) {
        this.expiredTime = expiredTime;
    }

    public boolean isFinish() {
        return finish;
    }

    public void setFinish(boolean finish) {
        this.finish = finish;
    }

    public boolean isFirst() {
        return first;
    }

    public void setFirst(boolean first) {
        this.first = first;
    }

    public Map<Integer, Double> getCurrentRewardsMap() {
        return currentRewardsMap;
    }

    public void setCurrentRewardsMap(Map<Integer, Double> currentRewardsMap) {
        this.currentRewardsMap = currentRewardsMap;
    }

    public LuckCondition getLuckCondition() {
        return luckCondition;
    }

    public void setLuckCondition(LuckCondition luckCondition) {
        this.luckCondition = luckCondition;
    }

    public void incCurrentRewards(int currencyId, double rewards) {
        double value = this.currentRewardsMap.getOrDefault(currencyId, 0d);
        value = BigDecimalUtils.add(value, rewards, 9);
        this.currentRewardsMap.put(currencyId, value);
    }

    public void incTotalTimes(int addTimes) {
        this.totalTimes += addTimes;
    }

    public void incRemainTimes(int remainTimes) {
        this.remainTimes += remainTimes;
        if (this.remainTimes <= 0) {
            this.remainTimes = 0;
        }
    }

    public void reset() {
        this.start = false;
        this.totalTimes = 0;
        this.remainTimes = 0;
        this.expiredTime = 0;
        this.finish = false;
        this.first = false;
        this.currentRewardsMap.clear();
        this.luckCondition.reset();
    }
}
