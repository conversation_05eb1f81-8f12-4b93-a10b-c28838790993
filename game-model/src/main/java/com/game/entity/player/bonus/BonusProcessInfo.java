package com.game.entity.player.bonus;

import com.game.engine.math.BigDecimalUtils;
import com.wjybxx.io.AutoFields;
import com.wjybxx.io.binary.SerializableClass;
import com.wjybxx.io.document.PersistentEntity;

import java.util.LinkedHashMap;
import java.util.Map;

@AutoFields
@PersistentEntity
@SerializableClass
public class BonusProcessInfo {

    //下注
    private Map<Integer, Double> wagerMap = new LinkedHashMap<>();

    //奖励
    private Map<Integer, Double> rewardMap = new LinkedHashMap<>();

    public Map<Integer, Double> getWagerMap() {
        return wagerMap;
    }

    public void setWagerMap(Map<Integer, Double> wagerMap) {
        this.wagerMap = wagerMap;
    }

    public Map<Integer, Double> getRewardMap() {
        return rewardMap;
    }

    public void setRewardMap(Map<Integer, Double> rewardMap) {
        this.rewardMap = rewardMap;
    }

    public void incWager(int currencyId, double wager) {
        double value = this.wagerMap.getOrDefault(currencyId, 0d);
        value = BigDecimalUtils.add(value, wager, 9);
        this.wagerMap.put(currencyId, value);
    }

    public void incReward(int currencyId, double reward) {
        double value = this.rewardMap.getOrDefault(currencyId, 0d);
        value = BigDecimalUtils.add(value, reward, 9);
        this.rewardMap.put(currencyId, value);
    }
}
