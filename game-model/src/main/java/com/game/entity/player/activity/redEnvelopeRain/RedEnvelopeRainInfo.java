package com.game.entity.player.activity.redEnvelopeRain;

import com.game.engine.math.BigDecimalUtils;
import com.wjybxx.io.AutoFields;
import com.wjybxx.io.binary.SerializableClass;
import com.wjybxx.io.document.PersistentEntity;

import java.util.*;

@AutoFields
@PersistentEntity
@SerializableClass
public class RedEnvelopeRainInfo {
    private boolean start;

    private int receiveTimes;

    private Set<Integer> receive = new HashSet<>();

    private Map<Integer, Double> wageredMap = new HashMap<>();

    private Map<Integer, Double> rechargeAmountMap = new HashMap<>();

    public boolean isStart() {
        return start;
    }

    public void setStart(boolean start) {
        this.start = start;
    }

    public int getReceiveTimes() {
        return receiveTimes;
    }

    public void setReceiveTimes(int receiveTimes) {
        this.receiveTimes = receiveTimes;
    }

    public Set<Integer> getReceive() {
        return receive;
    }

    public void setReceive(Set<Integer> receive) {
        this.receive = receive;
    }

    public Map<Integer, Double> getWageredMap() {
        return wageredMap;
    }

    public void setWageredMap(Map<Integer, Double> wageredMap) {
        this.wageredMap = wageredMap;
    }

    public Map<Integer, Double> getRechargeAmountMap() {
        return rechargeAmountMap;
    }

    public void setRechargeAmountMap(Map<Integer, Double> rechargeAmountMap) {
        this.rechargeAmountMap = rechargeAmountMap;
    }

    public void incWagered(int currencyId, double betAmount) {
        double wagered = this.wageredMap.getOrDefault(currencyId, 0d);
        wagered = BigDecimalUtils.add(betAmount, wagered, 9);
        this.wageredMap.put(currencyId, wagered);
    }

    public void incRecharge(int currencyId, double recharge) {
        double rechargeAmount = this.rechargeAmountMap.getOrDefault(currencyId, 0d);
        rechargeAmount = BigDecimalUtils.add(recharge, rechargeAmount, 9);
        this.rechargeAmountMap.put(currencyId, rechargeAmount);
    }

    public void reset() {
        this.receive.clear();
    }
}
