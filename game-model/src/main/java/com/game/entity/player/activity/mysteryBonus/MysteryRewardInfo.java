package com.game.entity.player.activity.mysteryBonus;

import com.wjybxx.io.AutoFields;
import com.wjybxx.io.binary.SerializableClass;
import com.wjybxx.io.document.PersistentEntity;

@AutoFields
@PersistentEntity
@SerializableClass
public class MysteryRewardInfo {
    private int c_id;
    //结算领取时间
    private long settlementTime;
    //过期时间
    private long expiredTime;

    private int currencyId;
    //奖金
    private double bonus;

    public int getC_id() {
        return c_id;
    }

    public void setC_id(int c_id) {
        this.c_id = c_id;
    }

    public long getSettlementTime() {
        return settlementTime;
    }

    public void setSettlementTime(long settlementTime) {
        this.settlementTime = settlementTime;
    }

    public long getExpiredTime() {
        return expiredTime;
    }

    public void setExpiredTime(long expiredTime) {
        this.expiredTime = expiredTime;
    }

    public int getCurrencyId() {
        return currencyId;
    }

    public void setCurrencyId(int currencyId) {
        this.currencyId = currencyId;
    }

    public double getBonus() {
        return bonus;
    }

    public void setBonus(double bonus) {
        this.bonus = bonus;
    }
}
