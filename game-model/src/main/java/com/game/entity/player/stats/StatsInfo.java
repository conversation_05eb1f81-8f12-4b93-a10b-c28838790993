package com.game.entity.player.stats;

import com.wjybxx.io.AutoFields;
import com.wjybxx.io.binary.SerializableClass;
import com.wjybxx.io.document.PersistentEntity;

import java.util.HashMap;
import java.util.Map;

@AutoFields
@PersistentEntity
@SerializableClass
public class StatsInfo {

    private Map<Integer, Stats> statsMap = new HashMap<>();

    public Map<Integer, Stats> getStatsMap() {
        return statsMap;
    }

    public void setStatsMap(Map<Integer, Stats> statsMap) {
        this.statsMap = statsMap;
    }

}
