package com.game.entity;

public class WinGameNote {
    private int currencyId;
    private double betAmount;
    private double validBets;
    private int gameId;
    private String playerName;
    private long createTime;
    private double win;

    public double getValidBets() {
        return validBets;
    }

    public void setValidBets(double validBets) {
        this.validBets = validBets;
    }

    public int getCurrencyId() {
        return currencyId;
    }

    public void setCurrencyId(int currencyId) {
        this.currencyId = currencyId;
    }

    public double getBetAmount() {
        return betAmount;
    }

    public void setBetAmount(double betAmount) {
        this.betAmount = betAmount;
    }

    public int getGameId() {
        return gameId;
    }

    public void setGameId(int gameId) {
        this.gameId = gameId;
    }

    public String getPlayerName() {
        return playerName;
    }

    public void setPlayerName(String playerName) {
        this.playerName = playerName;
    }

    public long getCreateTime() {
        return createTime;
    }

    public void setCreateTime(long createTime) {
        this.createTime = createTime;
    }

    public double getWin() {
        return win;
    }

    public void setWin(double win) {
        this.win = win;
    }
}
