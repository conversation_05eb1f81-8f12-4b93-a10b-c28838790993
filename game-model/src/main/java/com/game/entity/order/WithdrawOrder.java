package com.game.entity.order;

import com.wjybxx.io.AutoFields;
import com.wjybxx.io.binary.SerializableClass;
import com.wjybxx.io.document.PersistentEntity;
import org.springframework.data.mongodb.core.mapping.Document;

@AutoFields
@PersistentEntity
@SerializableClass
@Document(collection = "withdraw_order")
public class WithdrawOrder extends Order {
    private String txId;

    //提现账号
    private String withdrawAccount;

    //提现信息
    private String withdrawInfo;

    //tips
    private String tips;

    public WithdrawOrder() {
        super();
    }

    public WithdrawOrder(long orderId) {
        super(orderId);
    }

    public String getTxId() {
        return txId;
    }

    public void setTxId(String txId) {
        this.txId = txId;
    }

    public String getWithdrawAccount() {
        return withdrawAccount;
    }

    public void setWithdrawAccount(String withdrawAccount) {
        this.withdrawAccount = withdrawAccount;
    }

    public String getWithdrawInfo() {
        return withdrawInfo;
    }

    public void setWithdrawInfo(String withdrawInfo) {
        this.withdrawInfo = withdrawInfo;
    }

    public String getTips() {
        return tips;
    }

    public void setTips(String tips) {
        this.tips = tips;
    }
}
