package com.game.entity.order;


import com.wjybxx.io.AutoFields;
import com.wjybxx.io.binary.SerializableClass;
import com.wjybxx.io.document.PersistentEntity;
import org.springframework.data.mongodb.core.mapping.Document;

@AutoFields
@PersistentEntity
@SerializableClass
@Document(collection = "recharge_order")
public class RechargeOrder extends Order {

    //充值账号
    private String rechargeAccount;

    public RechargeOrder() {
        super();
    }

    public RechargeOrder(long orderId) {
        super(orderId);
    }

    public String getRechargeAccount() {
        return rechargeAccount;
    }

    public void setRechargeAccount(String rechargeAccount) {
        this.rechargeAccount = rechargeAccount;
    }
}
