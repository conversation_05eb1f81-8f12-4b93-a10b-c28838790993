package com.game.entity.order;

public class WithdrawInfo {
    //手续费
    private double fee;

    //实际金额
    private double actualAmount;

    public double getFee() {
        return fee;
    }

    public void setFee(double fee) {
        this.fee = fee;
    }

    public double getActualAmount() {
        return actualAmount;
    }

    public void setActualAmount(double actualAmount) {
        this.actualAmount = actualAmount;
    }

    public static class RechargeCondition {
        //总充值次数
        private int totalRechargeTimes;

        //总充值金额
        private double totalRechargeAmount;

        //今日充值次数
        private int dailyRechargeTimes;

        public int getTotalRechargeTimes() {
            return totalRechargeTimes;
        }

        public void setTotalRechargeTimes(int totalRechargeTimes) {
            this.totalRechargeTimes = totalRechargeTimes;
        }

        public double getTotalRechargeAmount() {
            return totalRechargeAmount;
        }

        public void setTotalRechargeAmount(double totalRechargeAmount) {
            this.totalRechargeAmount = totalRechargeAmount;
        }

        public int getDailyRechargeTimes() {
            return dailyRechargeTimes;
        }

        public void setDailyRechargeTimes(int dailyRechargeTimes) {
            this.dailyRechargeTimes = dailyRechargeTimes;
        }
    }

    public static class WithdrawCondition {
        //总提现次数
        private int totalWithdrawTimes;

        //今日免费提现次数
        private int dailyFreeWithdrawTimes;

        //今日提现次数
        private int dailyWithdrawTimes;

        public int getTotalWithdrawTimes() {
            return totalWithdrawTimes;
        }

        public void setTotalWithdrawTimes(int totalWithdrawTimes) {
            this.totalWithdrawTimes = totalWithdrawTimes;
        }

        public int getDailyFreeWithdrawTimes() {
            return dailyFreeWithdrawTimes;
        }

        public void setDailyFreeWithdrawTimes(int dailyFreeWithdrawTimes) {
            this.dailyFreeWithdrawTimes = dailyFreeWithdrawTimes;
        }

        public int getDailyWithdrawTimes() {
            return dailyWithdrawTimes;
        }

        public void setDailyWithdrawTimes(int dailyWithdrawTimes) {
            this.dailyWithdrawTimes = dailyWithdrawTimes;
        }
    }

    public static class TurnoverCondition {
        //提现标准
        private double drawStandard;
        //提现打码量
        private double bettingTurnover;

        public double getDrawStandard() {
            return drawStandard;
        }

        public void setDrawStandard(double drawStandard) {
            this.drawStandard = drawStandard;
        }

        public double getBettingTurnover() {
            return bettingTurnover;
        }

        public void setBettingTurnover(double bettingTurnover) {
            this.bettingTurnover = bettingTurnover;
        }
    }

    public static class BetCondition {
        //实际下注次数
        private long realityBetTimes;

        public long getRealityBetTimes() {
            return realityBetTimes;
        }

        public void setRealityBetTimes(long realityBetTimes) {
            this.realityBetTimes = realityBetTimes;
        }
    }

    public static class WithdrawAccount {
        private String extend;
        private String extend_1;
        private String extend_2;
        private String extend_3;

        public String getExtend() {
            return extend;
        }

        public void setExtend(String extend) {
            this.extend = extend;
        }

        public String getExtend_1() {
            return extend_1;
        }

        public void setExtend_1(String extend_1) {
            this.extend_1 = extend_1;
        }

        public String getExtend_2() {
            return extend_2;
        }

        public void setExtend_2(String extend_2) {
            this.extend_2 = extend_2;
        }

        public String getExtend_3() {
            return extend_3;
        }

        public void setExtend_3(String extend_3) {
            this.extend_3 = extend_3;
        }
    }
}
