package com.game.entity.activity;

import com.game.engine.utils.TimeUtil;
import com.wjybxx.io.AutoFields;
import com.wjybxx.io.binary.SerializableClass;
import com.wjybxx.io.document.PersistentEntity;
import org.springframework.data.mongodb.core.mapping.Document;

@AutoFields
@PersistentEntity
@SerializableClass
@Document(collection = "spinBonus_note")
public class SpinBonusNote {

    private String business_no;

    private long createTime;

    //1.邀请 2.vip 3.每日 4.每周
    private int turntable;

    //vip子类型
    private int subType;

    private String headId;

    private long playerId;

    private String playerName;

    private String spin;

    private int currencyId;

    private double prize;

    public SpinBonusNote() {
        this.createTime = TimeUtil.currentTimeMillis();
    }


    public String getBusiness_no() {
        return business_no;
    }

    public void setBusiness_no(String business_no) {
        this.business_no = business_no;
    }

    public String getHeadId() {
        return headId;
    }

    public void setHeadId(String headId) {
        this.headId = headId;
    }

    public long getPlayerId() {
        return playerId;
    }

    public void setPlayerId(long playerId) {
        this.playerId = playerId;
    }

    public String getPlayerName() {
        return playerName;
    }

    public void setPlayerName(String playerName) {
        this.playerName = playerName;
    }

    public String getSpin() {
        return spin;
    }

    public void setSpin(String spin) {
        this.spin = spin;
    }

    public int getCurrencyId() {
        return currencyId;
    }

    public void setCurrencyId(int currencyId) {
        this.currencyId = currencyId;
    }

    public double getPrize() {
        return prize;
    }

    public void setPrize(double prize) {
        this.prize = prize;
    }

    public long getCreateTime() {
        return createTime;
    }

    public void setCreateTime(long createTime) {
        this.createTime = createTime;
    }

    public int getTurntable() {
        return turntable;
    }

    public void setTurntable(int turntable) {
        this.turntable = turntable;
    }

    public int getSubType() {
        return subType;
    }

    public void setSubType(int subType) {
        this.subType = subType;
    }
}
