package com.game.entity;

import com.game.engine.mongo.ShardEntity;
import com.game.engine.utils.TimeUtil;
import com.wjybxx.io.AutoFields;
import com.wjybxx.io.binary.SerializableClass;
import com.wjybxx.io.document.PersistentEntity;
import org.springframework.data.mongodb.core.mapping.Document;

@AutoFields
@PersistentEntity
@SerializableClass
@Document(collection = "activityLimit")
public class ActivityLimit extends ShardEntity {
    private String business_no;

    private int activityId;

    private int uniqueId;

    /** 设备 */
    private String device;

    /** ip地址*/
    private String ipAddress;

    /** 次数*/
    private int count;

    /** 上次时间 */
    private long lastTime;

    public ActivityLimit() {
        super(-1);
        this.lastTime = TimeUtil.currentTimeMillis();
    }

    public String getBusiness_no() {
        return business_no;
    }

    public void setBusiness_no(String business_no) {
        this.business_no = business_no;
    }

    public int getActivityId() {
        return activityId;
    }

    public void setActivityId(int activityId) {
        this.activityId = activityId;
    }

    public int getUniqueId() {
        return uniqueId;
    }

    public void setUniqueId(int uniqueId) {
        this.uniqueId = uniqueId;
    }

    public String getDevice() {
        return device;
    }

    public void setDevice(String device) {
        this.device = device;
    }

    public String getIpAddress() {
        return ipAddress;
    }

    public void setIpAddress(String ipAddress) {
        this.ipAddress = ipAddress;
    }

    public int getCount() {
        return count;
    }

    public void setCount(int count) {
        this.count = count;
    }

    public long getLastTime() {
        return lastTime;
    }

    public void setLastTime(long lastTime) {
        this.lastTime = lastTime;
    }
}
