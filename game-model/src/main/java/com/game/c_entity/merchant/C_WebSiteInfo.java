package com.game.c_entity.merchant;

import com.game.engine.utils.JsonUtils;
import com.game.po.data.IDataChecker;
import io.netty.util.internal.StringUtil;
import org.bson.types.ObjectId;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Document(collection = "c_webSiteInfo")
public class C_WebSiteInfo implements IDataChecker {

    @Id
    private ObjectId _id;

    //商户
    private String business_no;

    //站id
    private int siteId;

    @Indexed
    private List<String> domainName;

    private List<String> webSitesData = new ArrayList<>();

    private transient final Map<Integer, WebSiteInfo> webSiteInfoMap = new HashMap<>();

    @Override
    public boolean check() throws Exception {
        for (String data : webSitesData) {
            final WebSiteInfo webSiteInfo = JsonUtils.readFromJson(data, WebSiteInfo.class);
            webSiteInfoMap.put(webSiteInfo.getLanguage(), webSiteInfo);
        }
        return true;
    }

    public String getBusiness_no() {
        return business_no;
    }

    public void setBusiness_no(String business_no) {
        this.business_no = business_no;
    }

    public int getSiteId() {
        return siteId;
    }

    public void setSiteId(int siteId) {
        this.siteId = siteId;
    }

    public List<String> getDomainName() {
        return domainName;
    }

    public void setDomainName(List<String> domainName) {
        this.domainName = domainName;
    }

    public List<String> getWebSitesData() {
        return webSitesData;
    }

    public void setWebSitesData(List<String> webSitesData) {
        this.webSitesData = webSitesData;
    }

    public Map<Integer, WebSiteInfo> getWebSiteInfoMap() {
        return webSiteInfoMap;
    }

    public static class WebSiteInfo {
        private int language;

        private String info;

        private String info1;

        private String info2;

        private String title;

        private String keywords;

        private String introduce;

        private String ogTile;

        private String ogType;

        private String ogImage;

        private String ogDescription;

        private String site_name;

        private String icon;

        //隐私协议
        public String privacyAgreement;

        //用户条款
        public String userTerms;

        public int getLanguage() {
            return language;
        }

        public void setLanguage(int language) {
            this.language = language;
        }

        public String getInfo() {
            return StringUtil.isNullOrEmpty(info) ? "" : info;
        }

        public void setInfo(String info) {
            this.info = info;
        }

        public String getInfo1() {
            return StringUtil.isNullOrEmpty(info1) ? "" : info1;
        }

        public void setInfo1(String info1) {
            this.info1 = info1;
        }

        public String getInfo2() {
            return StringUtil.isNullOrEmpty(info2) ? "" : info2;
        }

        public void setInfo2(String info2) {
            this.info2 = info2;
        }

        public String getKeywords() {
            return StringUtil.isNullOrEmpty(keywords) ? "" : keywords;
        }

        public void setKeywords(String keywords) {
            this.keywords = keywords;
        }

        public String getIntroduce() {
            return StringUtil.isNullOrEmpty(introduce) ? "" : introduce;
        }

        public void setIntroduce(String introduce) {
            this.introduce = introduce;
        }

        public String getOgTile() {
            return StringUtil.isNullOrEmpty(ogTile) ? "" : ogTile;
        }

        public void setOgTile(String ogTile) {
            this.ogTile = ogTile;
        }

        public String getTitle() {
            return StringUtil.isNullOrEmpty(title) ? "" : title;
        }

        public void setTitle(String title) {
            this.title = title;
        }

        public String getOgImage() {
            return StringUtil.isNullOrEmpty(ogImage) ? "" : ogImage;
        }

        public void setOgImage(String ogImage) {
            this.ogImage = ogImage;
        }

        public String getOgDescription() {
            return StringUtil.isNullOrEmpty(ogDescription) ? "" : ogDescription;
        }

        public void setOgDescription(String ogDescription) {
            this.ogDescription = ogDescription;
        }

        public String getOgType() {
            return StringUtil.isNullOrEmpty(ogType) ? "" : ogType;
        }

        public void setOgType(String ogType) {
            this.ogType = ogType;
        }

        public String getSite_name() {
            return StringUtil.isNullOrEmpty(site_name) ? "" : site_name;
        }

        public void setSite_name(String site_name) {
            this.site_name = site_name;
        }

        public String getIcon() {
            return StringUtil.isNullOrEmpty(icon) ? "" : icon;
        }

        public void setIcon(String icon) {
            this.icon = icon;
        }

        public String getPrivacyAgreement() {
            return StringUtil.isNullOrEmpty(privacyAgreement) ? "" : privacyAgreement;
        }

        public void setPrivacyAgreement(String privacyAgreement) {
            this.privacyAgreement = privacyAgreement;
        }

        public String getUserTerms() {
            return StringUtil.isNullOrEmpty(userTerms) ? "" : userTerms;
        }

        public void setUserTerms(String userTerms) {
            this.userTerms = userTerms;
        }
    }
}
