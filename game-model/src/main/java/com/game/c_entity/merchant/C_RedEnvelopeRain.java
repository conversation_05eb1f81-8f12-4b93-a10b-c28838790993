package com.game.c_entity.merchant;

import com.alibaba.fastjson.JSON;
import com.game.po.data.IDataChecker;
import io.netty.util.internal.StringUtil;
import org.bson.types.ObjectId;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.*;

@Document(collection = "c_redEnvelopeRain")
public class C_RedEnvelopeRain implements IDataChecker {

    @Id
    private ObjectId _id;

    //商户
    private String business_no;

    private int c_id;

    private int activityId;

    private String activityName;

    //月
    private List<Integer> monthlys = new ArrayList<>();

    //周期
    private List<Integer> cycles = new ArrayList<>();

    private List<Integer> languages = new ArrayList<>();

    //时区
    private String timeZone;

    //排序
    private int sort;

    //红包币种
    private int currencyId;

    //打码倍数
    private int turnoverMul;

    //开关
    private boolean open;

    //每日领取次数
    private int dailyTimes;

    //总金额显示
    private double showTotalAmount;

    //单红包金额显示
    private double showSingleAmount;

    //总金额限制
    private double totalAmountLimit;

    //单红包限制
    private double singleAmountLimit;

    //领取类型 1.满足任意一个 2.全部满足 3.不限制
    private int receiveType;

    //最近时间
    private int recentTime;

    //时间段
    private List<String> timePeriodData = new ArrayList<>();

    //红包配置
    private List<String> redEnvelopeData = new ArrayList<>();

    //规则说明
    private List<String> ruleData = new ArrayList<>();

    private transient final Map<Integer, TimePeriod> timePeriodMap = new LinkedHashMap<>();

    private transient final Map<Integer, RedEnvelope> redEnvelopeMap = new LinkedHashMap<>();

    private transient final Map<Integer, Rule> ruleMap = new LinkedHashMap<>();

    @Override
    public boolean check() throws Exception {
        if (this.timePeriodData != null && !this.timePeriodData.isEmpty()) {
            for (String data : this.timePeriodData) {
                final TimePeriod timePeriod = JSON.parseObject(data, TimePeriod.class);
                timePeriodMap.put(timePeriod.id, timePeriod);
            }
        }

        if (this.redEnvelopeData != null && !this.redEnvelopeData.isEmpty()) {
            for (String data : this.redEnvelopeData) {
                final RedEnvelope redEnvelope = JSON.parseObject(data, RedEnvelope.class);
                redEnvelopeMap.put(redEnvelope.id, redEnvelope);
            }
        }

        if (this.ruleData != null && !this.ruleData.isEmpty()) {
            for (String data : this.ruleData) {
                final Rule rule = JSON.parseObject(data, Rule.class);
                ruleMap.put(rule.language, rule);
            }
        }
        return true;
    }

    public List<Integer> getMonthlys() {
        return monthlys;
    }

    public void setMonthlys(List<Integer> monthlys) {
        this.monthlys = monthlys;
    }

    public List<Integer> getCycles() {
        return cycles;
    }

    public void setCycles(List<Integer> cycles) {
        this.cycles = cycles;
    }

    public String getBusiness_no() {
        return business_no;
    }

    public void setBusiness_no(String business_no) {
        this.business_no = business_no;
    }

    public int getC_id() {
        return c_id;
    }

    public void setC_id(int c_id) {
        this.c_id = c_id;
    }

    public int getActivityId() {
        return activityId;
    }

    public void setActivityId(int activityId) {
        this.activityId = activityId;
    }

    public String getActivityName() {
        return activityName;
    }

    public void setActivityName(String activityName) {
        this.activityName = activityName;
    }

    public List<Integer> getLanguages() {
        return languages;
    }

    public void setLanguages(List<Integer> languages) {
        this.languages = languages;
    }

    public String getTimeZone() {
        return timeZone;
    }

    public void setTimeZone(String timeZone) {
        this.timeZone = timeZone;
    }

    public int getSort() {
        return sort;
    }

    public void setSort(int sort) {
        this.sort = sort;
    }

    public int getCurrencyId() {
        return currencyId;
    }

    public void setCurrencyId(int currencyId) {
        this.currencyId = currencyId;
    }

    public int getTurnoverMul() {
        return turnoverMul;
    }

    public void setTurnoverMul(int turnoverMul) {
        this.turnoverMul = turnoverMul;
    }

    public boolean isOpen() {
        return open;
    }

    public void setOpen(boolean open) {
        this.open = open;
    }

    public int getDailyTimes() {
        return dailyTimes;
    }

    public void setDailyTimes(int dailyTimes) {
        this.dailyTimes = dailyTimes;
    }

    public double getShowSingleAmount() {
        return showSingleAmount;
    }

    public void setShowSingleAmount(double showSingleAmount) {
        this.showSingleAmount = showSingleAmount;
    }

    public double getShowTotalAmount() {
        return showTotalAmount;
    }

    public void setShowTotalAmount(double showTotalAmount) {
        this.showTotalAmount = showTotalAmount;
    }

    public double getTotalAmountLimit() {
        return totalAmountLimit;
    }

    public void setTotalAmountLimit(double totalAmountLimit) {
        this.totalAmountLimit = totalAmountLimit;
    }

    public double getSingleAmountLimit() {
        return singleAmountLimit;
    }

    public void setSingleAmountLimit(double singleAmountLimit) {
        this.singleAmountLimit = singleAmountLimit;
    }

    public int getReceiveType() {
        return receiveType;
    }

    public void setReceiveType(int receiveType) {
        this.receiveType = receiveType;
    }

    public int getRecentTime() {
        return recentTime;
    }

    public void setRecentTime(int recentTime) {
        this.recentTime = recentTime;
    }

    public List<String> getTimePeriodData() {
        return timePeriodData;
    }

    public void setTimePeriodData(List<String> timePeriodData) {
        this.timePeriodData = timePeriodData;
    }

    public List<String> getRedEnvelopeData() {
        return redEnvelopeData;
    }

    public void setRedEnvelopeData(List<String> redEnvelopeData) {
        this.redEnvelopeData = redEnvelopeData;
    }

    public List<String> getRuleData() {
        return ruleData;
    }

    public void setRuleData(List<String> ruleData) {
        this.ruleData = ruleData;
    }

    public Map<Integer, TimePeriod> getTimePeriodMap() {
        return timePeriodMap;
    }

    public Map<Integer, Rule> getRuleMap() {
        return ruleMap;
    }

    public Map<Integer, RedEnvelope> getRedEnvelopeMap() {
        return redEnvelopeMap;
    }

    public static class TimePeriod {
        public int id;
        public long startTime;
        public long endTime;
        public String rangeTime;

        public int currencyId;
        public double amount;

        public int getId() {
            return id;
        }

        public void setId(int id) {
            this.id = id;
        }

        public int getCurrencyId() {
            return currencyId;
        }

        public void setCurrencyId(int currencyId) {
            this.currencyId = currencyId;
        }

        public long getStartTime() {
            return startTime;
        }

        public void setStartTime(long startTime) {
            this.startTime = startTime;
        }

        public long getEndTime() {
            return endTime;
        }

        public void setEndTime(long endTime) {
            this.endTime = endTime;
        }

        public String getRangeTime() {
            return rangeTime;
        }

        public void setRangeTime(String rangeTime) {
            this.rangeTime = rangeTime;
        }

        public double getAmount() {
            return amount;
        }

        public void setAmount(double amount) {
            this.amount = amount;
        }
    }

    public static class RedEnvelope {
        public int id;
        public double min;
        public double max;
        public int num;

        public int r_currencyId;
        public double minRecharge;
        public double maxRecharge;

        public int w_currencyId;
        public double minWagered;
        public double maxWagered;

        public int minVip;
        public int maxVip;

        public int getId() {
            return id;
        }

        public void setId(int id) {
            this.id = id;
        }

        public int getNum() {
            return num;
        }

        public void setNum(int num) {
            this.num = num;
        }

        public double getMin() {
            return min;
        }

        public void setMin(double min) {
            this.min = min;
        }

        public double getMax() {
            return max;
        }

        public void setMax(double max) {
            this.max = max;
        }

        public int getR_currencyId() {
            return r_currencyId;
        }

        public void setR_currencyId(int r_currencyId) {
            this.r_currencyId = r_currencyId;
        }

        public double getMinRecharge() {
            return minRecharge;
        }

        public void setMinRecharge(double minRecharge) {
            this.minRecharge = minRecharge;
        }

        public double getMaxRecharge() {
            return maxRecharge;
        }

        public void setMaxRecharge(double maxRecharge) {
            this.maxRecharge = maxRecharge;
        }

        public int getW_currencyId() {
            return w_currencyId;
        }

        public void setW_currencyId(int w_currencyId) {
            this.w_currencyId = w_currencyId;
        }

        public double getMinWagered() {
            return minWagered;
        }

        public void setMinWagered(double minWagered) {
            this.minWagered = minWagered;
        }

        public double getMaxWagered() {
            return maxWagered;
        }

        public void setMaxWagered(double maxWagered) {
            this.maxWagered = maxWagered;
        }

        public int getMinVip() {
            return minVip;
        }

        public void setMinVip(int minVip) {
            this.minVip = minVip;
        }

        public int getMaxVip() {
            return maxVip;
        }

        public void setMaxVip(int maxVip) {
            this.maxVip = maxVip;
        }
    }

    public static class Rule {
        public int language;
        public String icon;
        public String rule;

        public int getLanguage() {
            return language;
        }

        public void setLanguage(int language) {
            this.language = language;
        }

        public String getIcon() {
            return StringUtil.isNullOrEmpty(icon) ? null : icon;
        }

        public void setIcon(String icon) {
            this.icon = icon;
        }

        public String getRule() {
            return StringUtil.isNullOrEmpty(rule) ? "" : rule;
        }

        public void setRule(String rule) {
            this.rule = rule;
        }
    }

}
