package com.game.c_entity.merchant;

import com.game.engine.utils.JsonUtils;
import com.game.po.data.IDataChecker;
import org.bson.types.ObjectId;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Document(collection = "c_dailyContest")
public class C_DailyContest implements IDataChecker {
    @Id
    private ObjectId _id;

    //商户
    private String business_no;

    private int activityId;

    //奖励币种
    private int rewardCurrency;

    //奖励打码倍率
    private double rewardTurnoverMul;

    //参与货币
    private List<Integer> joinCurrency = new ArrayList<>();

    private double minBet;

    private double maxBet;

    //进入奖池金额=有效投注 X 每日竞赛投注进入奖池比例 X 游戏奖池比例
    private double entryRewardPoolRate;

    //游戏奖池比例
    private List<String> gameRewardPools;

    //初始奖池
    private int initRewardPool;

    //奖池最大
    private int maxRewardPool;

    //奖励分配
    private List<String> rewardAllocations;

    private transient Map<Integer, GameRewardPool> gameRewardPoolMap = new HashMap<>();

    private transient List<RewardAllocation> rewardAllocationList = new ArrayList<>();

    @Override
    public boolean check() throws Exception {
        if (gameRewardPools != null && !gameRewardPools.isEmpty()) {
            for (String gameRewardPool : gameRewardPools) {
                final GameRewardPool pool = JsonUtils.readFromJson(gameRewardPool, GameRewardPool.class);
                gameRewardPoolMap.put(pool.getGameType(), pool);
            }
        } else {
            throw new IllegalArgumentException("c_dailyContest，error，gameRewardPools：" + gameRewardPools);
        }

        if (rewardAllocations != null && !rewardAllocations.isEmpty()) {
            for (String data : rewardAllocations) {
                rewardAllocationList.add(JsonUtils.readFromJson(data, RewardAllocation.class));
            }
        }
        return true;
    }

    public String getBusiness_no() {
        return business_no;
    }

    public void setBusiness_no(String business_no) {
        this.business_no = business_no;
    }

    public int getActivityId() {
        return activityId;
    }

    public void setActivityId(int activityId) {
        this.activityId = activityId;
    }

    public int getRewardCurrency() {
        return rewardCurrency;
    }

    public void setRewardCurrency(int rewardCurrency) {
        this.rewardCurrency = rewardCurrency;
    }

    public double getRewardTurnoverMul() {
        return rewardTurnoverMul;
    }

    public void setRewardTurnoverMul(double rewardTurnoverMul) {
        this.rewardTurnoverMul = rewardTurnoverMul;
    }

    public List<Integer> getJoinCurrency() {
        return joinCurrency;
    }

    public void setJoinCurrency(List<Integer> joinCurrency) {
        this.joinCurrency = joinCurrency;
    }

    public double getMinBet() {
        return minBet;
    }

    public void setMinBet(double minBet) {
        this.minBet = minBet;
    }

    public double getMaxBet() {
        return maxBet;
    }

    public void setMaxBet(double maxBet) {
        this.maxBet = maxBet;
    }

    public double getEntryRewardPoolRate() {
        return entryRewardPoolRate;
    }

    public void setEntryRewardPoolRate(double entryRewardPoolRate) {
        this.entryRewardPoolRate = entryRewardPoolRate;
    }

    public List<String> getGameRewardPools() {
        return gameRewardPools;
    }

    public void setGameRewardPools(List<String> gameRewardPools) {
        this.gameRewardPools = gameRewardPools;
    }

    public Map<Integer, GameRewardPool> getGameRewardPoolMap() {
        return gameRewardPoolMap;
    }

    public void setGameRewardPoolMap(Map<Integer, GameRewardPool> gameRewardPoolMap) {
        this.gameRewardPoolMap = gameRewardPoolMap;
    }

    public int getInitRewardPool() {
        return initRewardPool;
    }

    public void setInitRewardPool(int initRewardPool) {
        this.initRewardPool = initRewardPool;
    }

    public int getMaxRewardPool() {
        return maxRewardPool;
    }

    public void setMaxRewardPool(int maxRewardPool) {
        this.maxRewardPool = maxRewardPool;
    }

    public List<String> getRewardAllocations() {
        return rewardAllocations;
    }

    public void setRewardAllocations(List<String> rewardAllocations) {
        this.rewardAllocations = rewardAllocations;
    }

    public List<RewardAllocation> getRewardAllocationList() {
        return rewardAllocationList;
    }

    public void setRewardAllocationList(List<RewardAllocation> rewardAllocationList) {
        this.rewardAllocationList = rewardAllocationList;
    }

    public static class GameRewardPool {
        public int gameType;
        private double rate;

        public int getGameType() {
            return gameType;
        }

        public void setGameType(int gameType) {
            this.gameType = gameType;
        }

        public double getRate() {
            return rate;
        }

        public void setRate(double rate) {
            this.rate = rate;
        }
    }

    public static class RewardAllocation {
        private int minRanking;

        private int maxRanking;

        private double rewardRate;

        public int getMinRanking() {
            return minRanking;
        }

        public void setMinRanking(int minRanking) {
            this.minRanking = minRanking;
        }

        public int getMaxRanking() {
            return maxRanking;
        }

        public void setMaxRanking(int maxRanking) {
            this.maxRanking = maxRanking;
        }

        public double getRewardRate() {
            return rewardRate;
        }

        public void setRewardRate(double rewardRate) {
            this.rewardRate = rewardRate;
        }
    }

    public double getPrizeRate(int ranking) {
        for (RewardAllocation rewardAllocation : this.rewardAllocationList) {
            if (rewardAllocation.getMinRanking() <= ranking && ranking <= rewardAllocation.getMaxRanking()) {
                return rewardAllocation.getRewardRate();
            }
        }
        return 0;
    }

}
