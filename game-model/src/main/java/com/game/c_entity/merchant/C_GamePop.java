package com.game.c_entity.merchant;

import com.game.engine.utils.JsonUtils;
import com.game.po.data.IDataChecker;
import io.netty.util.internal.StringUtil;
import org.bson.types.ObjectId;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

@Document(collection = "c_gamePop")
public class C_GamePop implements IDataChecker {

    @Id
    private ObjectId _id;

    //商户
    private String business_no;

    private int c_id;

    private List<String> descData = new ArrayList<>();

    private transient Map<Integer, DescInfo> descInfoMap = new LinkedHashMap<>();

    @Override
    public boolean check() throws Exception {
        if (this.descData != null && !this.descData.isEmpty()) {
            for (String data : this.descData) {
                final DescInfo descInfo = JsonUtils.readFromJson(data, DescInfo.class);
                this.descInfoMap.put(descInfo.language, descInfo);
            }
        }
        return true;
    }

    public String getBusiness_no() {
        return business_no;
    }

    public void setBusiness_no(String business_no) {
        this.business_no = business_no;
    }

    public int getC_id() {
        return c_id;
    }

    public void setC_id(int c_id) {
        this.c_id = c_id;
    }

    public List<String> getDescData() {
        return descData;
    }

    public void setDescData(List<String> descData) {
        this.descData = descData;
    }

    public Map<Integer, DescInfo> getDescInfoMap() {
        return descInfoMap;
    }

    public void setDescInfoMap(Map<Integer, DescInfo> descInfoMap) {
        this.descInfoMap = descInfoMap;
    }

    public static class DescInfo {
        public int language;

        public String title;

        public String desc;

        public String icon;

        public String text;

        public int getLanguage() {
            return language;
        }

        public void setLanguage(int language) {
            this.language = language;
        }

        public String getTitle() {
            return StringUtil.isNullOrEmpty(title) ? "" : title;
        }

        public void setTitle(String title) {
            this.title = title;
        }

        public String getDesc() {
            return StringUtil.isNullOrEmpty(desc) ? "" : desc;
        }

        public void setDesc(String desc) {
            this.desc = desc;
        }

        public String getIcon() {
            return StringUtil.isNullOrEmpty(icon) ? "" : icon;
        }

        public void setIcon(String icon) {
            this.icon = icon;
        }

        public String getText() {
            return StringUtil.isNullOrEmpty(text) ? "" : text;
        }

        public void setText(String text) {
            this.text = text;
        }
    }
}
