package com.game.c_entity.merchant;

import com.game.po.data.IDataChecker;
import org.bson.types.ObjectId;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

@Document(collection = "c_cashBack")
public class C_CashBack implements IDataChecker {
    @Id
    private ObjectId _id;

    //商户
    private String business_no;

    //游戏类型
    private int gameType;

    //返水方式 1.有效 4.佣金
    private int rebateMethod;

    //是否开启
    private boolean open;

    //打码倍数
    private double turnoverMul;

    //有效比例
    private double effectiveRate;

    //上级返水
    private double superiorCashBackRate;

    //团长比例
    private double teamCashBackRate;

    //三级比例
    private double threeLevelCashBackRate;

    //游戏返水比例
    private double gameCashBackRate;

    public String getBusiness_no() {
        return business_no;
    }

    public void setBusiness_no(String business_no) {
        this.business_no = business_no;
    }

    public int getGameType() {
        return gameType;
    }

    public void setGameType(int gameType) {
        this.gameType = gameType;
    }

    public int getRebateMethod() {
        return rebateMethod;
    }

    public void setRebateMethod(int rebateMethod) {
        this.rebateMethod = rebateMethod;
    }

    public boolean isOpen() {
        return open;
    }

    public void setOpen(boolean open) {
        this.open = open;
    }

    public double getTurnoverMul() {
        return turnoverMul;
    }

    public void setTurnoverMul(double turnoverMul) {
        this.turnoverMul = turnoverMul;
    }

    public double getEffectiveRate() {
        return effectiveRate;
    }

    public void setEffectiveRate(double effectiveRate) {
        this.effectiveRate = effectiveRate;
    }

    public double getSuperiorCashBackRate() {
        return superiorCashBackRate;
    }

    public void setSuperiorCashBackRate(double superiorCashBackRate) {
        this.superiorCashBackRate = superiorCashBackRate;
    }

    public double getTeamCashBackRate() {
        return teamCashBackRate;
    }

    public void setTeamCashBackRate(double teamCashBackRate) {
        this.teamCashBackRate = teamCashBackRate;
    }

    public double getThreeLevelCashBackRate() {
        return threeLevelCashBackRate;
    }

    public void setThreeLevelCashBackRate(double threeLevelCashBackRate) {
        this.threeLevelCashBackRate = threeLevelCashBackRate;
    }

    public double getGameCashBackRate() {
        return gameCashBackRate;
    }

    public void setGameCashBackRate(double gameCashBackRate) {
        this.gameCashBackRate = gameCashBackRate;
    }

}
