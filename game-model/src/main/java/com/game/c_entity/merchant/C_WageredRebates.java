package com.game.c_entity.merchant;

import com.alibaba.fastjson.JSON;
import com.game.engine.utils.JsonUtils;
import com.game.po.data.IDataChecker;
import io.netty.util.internal.StringUtil;
import org.bson.types.ObjectId;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

@Document(collection = "c_wageredRebates")
public class C_WageredRebates implements IDataChecker {

    @Id
    private ObjectId _id;

    private int c_id;

    private int activityId;

    //商户
    private String business_no;

    private boolean open;

    private String timeZone;

    //奖励清算时间
    private long awardClearTime;

    //排名类型 1.比例 2.数量
    private int rankType;

    //排名人数
    private double rankNum;

    //倍数
    private int turnoverMul;

    //赢分玩家
    private double winPlayer;

    //输分玩家
    private double losePlayer;

    //1.cash 2.bonus
    private int rewardType;

    //奖励限制
    private double rewardLimit;

    //投注条件数据
    private String wageredConditionData;

    //规则说明
    private List<String> ruleData = new ArrayList<>();

    private transient WageredCondition wageredCondition;
    private transient final Map<Integer, Rule> ruleMap = new LinkedHashMap<>();

    @Override
    public boolean check() throws Exception {
        if (this.ruleData != null && !this.ruleData.isEmpty()) {
            for (String data : this.ruleData) {
                final Rule rule = JSON.parseObject(data, Rule.class);
                ruleMap.put(rule.language, rule);
            }
        }

        if (!StringUtil.isNullOrEmpty(this.wageredConditionData)) {
            wageredCondition = JsonUtils.readFromJson(this.wageredConditionData, WageredCondition.class);
        }

        return true;
    }

    public List<String> getRuleData() {
        return ruleData;
    }

    public void setRuleData(List<String> ruleData) {
        this.ruleData = ruleData;
    }

    public static class Rule {
        public int language;
        public String icon;
        public String rule;

        public int getLanguage() {
            return language;
        }

        public void setLanguage(int language) {
            this.language = language;
        }

        public String getIcon() {
            return StringUtil.isNullOrEmpty(icon) ? null : icon;
        }

        public void setIcon(String icon) {
            this.icon = icon;
        }

        public String getRule() {
            return StringUtil.isNullOrEmpty(rule) ? "" : rule;
        }

        public void setRule(String rule) {
            this.rule = rule;
        }
    }

    public boolean isOpen() {
        return open;
    }

    public void setOpen(boolean open) {
        this.open = open;
    }

    public ObjectId get_id() {
        return _id;
    }

    public void set_id(ObjectId _id) {
        this._id = _id;
    }

    public int getC_id() {
        return c_id;
    }

    public void setC_id(int c_id) {
        this.c_id = c_id;
    }

    public int getActivityId() {
        return activityId;
    }

    public void setActivityId(int activityId) {
        this.activityId = activityId;
    }

    public String getBusiness_no() {
        return business_no;
    }

    public void setBusiness_no(String business_no) {
        this.business_no = business_no;
    }

    public String getTimeZone() {
        return timeZone;
    }

    public void setTimeZone(String timeZone) {
        this.timeZone = timeZone;
    }

    public long getAwardClearTime() {
        return awardClearTime;
    }

    public void setAwardClearTime(long awardClearTime) {
        this.awardClearTime = awardClearTime;
    }

    public int getRankType() {
        return rankType;
    }

    public void setRankType(int rankType) {
        this.rankType = rankType;
    }

    public double getRankNum() {
        return rankNum;
    }

    public void setRankNum(double rankNum) {
        this.rankNum = rankNum;
    }

    public int getTurnoverMul() {
        return turnoverMul;
    }

    public void setTurnoverMul(int turnoverMul) {
        this.turnoverMul = turnoverMul;
    }

    public double getWinPlayer() {
        return winPlayer;
    }

    public void setWinPlayer(double winPlayer) {
        this.winPlayer = winPlayer;
    }

    public double getLosePlayer() {
        return losePlayer;
    }

    public void setLosePlayer(double losePlayer) {
        this.losePlayer = losePlayer;
    }

    public int getRewardType() {
        return rewardType;
    }

    public void setRewardType(int rewardType) {
        this.rewardType = rewardType;
    }

    public double getRewardLimit() {
        return rewardLimit;
    }

    public void setRewardLimit(double rewardLimit) {
        this.rewardLimit = rewardLimit;
    }

    public Map<Integer, Rule> getRuleMap() {
        return ruleMap;
    }

    public String getWageredConditionData() {
        return wageredConditionData;
    }

    public WageredCondition getWageredCondition() {
        return wageredCondition;
    }

    public static class WageredCondition {
        public int gameType;
        public List<Integer> platformId = new ArrayList<>();
        public List<Integer> gameId = new ArrayList<>();

        public int getGameType() {
            return gameType;
        }

        public List<Integer> getPlatformId() {
            return platformId;
        }

        public void setPlatformId(List<Integer> platformId) {
            this.platformId = platformId;
        }

        public void setGameType(int gameType) {
            this.gameType = gameType;
        }

        public List<Integer> getGameId() {
            return gameId;
        }

        public void setGameId(List<Integer> gameId) {
            this.gameId = gameId;
        }
    }
}
