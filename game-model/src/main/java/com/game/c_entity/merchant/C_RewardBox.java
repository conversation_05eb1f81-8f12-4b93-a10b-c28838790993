package com.game.c_entity.merchant;

import com.alibaba.fastjson.JSON;
import com.game.po.data.IDataChecker;
import io.netty.util.internal.StringUtil;
import org.bson.types.ObjectId;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

@Document(collection = "c_rewardBox")
public class C_RewardBox implements IDataChecker {

    @Id
    private ObjectId _id;

    //商户
    private String business_no;

    private int c_id;

    private int activityId;

    private String activityName;

    private List<Integer> languages = new ArrayList<>();

    //时区
    private String timeZone;

    //开始时间
    private long startTime;

    //结束时间
    private long endTime;

    private boolean open;

    private int rewardCurrencyId;

    //打码倍数
    private int turnoverMul;

    //邀请数据
    private String inviteData;

    //规则说明
    private List<String> ruleData;

    private List<String> boxData;

    private transient InviteInfo inviteInfo;

    private transient final Map<Integer, Rule> ruleMap = new LinkedHashMap<>();

    private transient final Map<Integer, Box> boxMap = new LinkedHashMap<>();

    @Override
    public boolean check() throws Exception {
        if (!StringUtil.isNullOrEmpty(this.inviteData)) {
            inviteInfo = JSON.parseObject(this.inviteData, InviteInfo.class);
        }

        if (this.ruleData != null && !this.ruleData.isEmpty()) {
            for (String data : this.ruleData) {
                final Rule rule = JSON.parseObject(data, Rule.class);
                ruleMap.put(rule.language, rule);
            }
        }

        if (this.boxData != null && !this.boxData.isEmpty()) {
            for (String data : this.boxData) {
                final Box box = JSON.parseObject(data, Box.class);
                boxMap.put(box.num, box);
            }
        }
        return true;
    }

    public String getActivityName() {
        return activityName;
    }

    public void setActivityName(String activityName) {
        this.activityName = activityName;
    }

    public String getBusiness_no() {
        return business_no;
    }

    public void setBusiness_no(String business_no) {
        this.business_no = business_no;
    }

    public int getC_id() {
        return c_id;
    }

    public void setC_id(int c_id) {
        this.c_id = c_id;
    }

    public int getActivityId() {
        return activityId;
    }

    public void setActivityId(int activityId) {
        this.activityId = activityId;
    }

    public List<Integer> getLanguages() {
        return languages;
    }

    public void setLanguages(List<Integer> languages) {
        this.languages = languages;
    }

    public String getTimeZone() {
        return timeZone;
    }

    public void setTimeZone(String timeZone) {
        this.timeZone = timeZone;
    }

    public long getStartTime() {
        return startTime;
    }

    public void setStartTime(long startTime) {
        this.startTime = startTime;
    }

    public long getEndTime() {
        return endTime;
    }

    public void setEndTime(long endTime) {
        this.endTime = endTime;
    }

    public boolean isOpen() {
        return open;
    }

    public void setOpen(boolean open) {
        this.open = open;
    }

    public int getRewardCurrencyId() {
        return rewardCurrencyId;
    }

    public void setRewardCurrencyId(int rewardCurrencyId) {
        this.rewardCurrencyId = rewardCurrencyId;
    }

    public int getTurnoverMul() {
        return turnoverMul;
    }

    public void setTurnoverMul(int turnoverMul) {
        this.turnoverMul = turnoverMul;
    }

    public String getInviteData() {
        return inviteData;
    }

    public void setInviteData(String inviteData) {
        this.inviteData = inviteData;
    }

    public Map<Integer, Box> getBoxMap() {
        return boxMap;
    }

    public List<String> getRuleData() {
        return ruleData;
    }

    public void setRuleData(List<String> ruleData) {
        this.ruleData = ruleData;
    }

    public List<String> getBoxData() {
        return boxData;
    }

    public void setBoxData(List<String> boxData) {
        this.boxData = boxData;
    }

    public InviteInfo getInviteInfo() {
        return inviteInfo;
    }

    public void setInviteInfo(InviteInfo inviteInfo) {
        this.inviteInfo = inviteInfo;
    }

    public Map<Integer, C_RewardBox.Rule> getRuleMap() {
        return ruleMap;
    }

    public static class InviteInfo {
        public int currencyId;

        //投注次数
        public int betTimes;
        //充值金额
        public double rechargeAmount;
        //投注金额
        public double wageredAmount;

        public double getRechargeAmount() {
            return rechargeAmount;
        }

        public void setRechargeAmount(double rechargeAmount) {
            this.rechargeAmount = rechargeAmount;
        }

        public int getCurrencyId() {
            return currencyId;
        }

        public void setCurrencyId(int currencyId) {
            this.currencyId = currencyId;
        }

        public int getBetTimes() {
            return betTimes;
        }

        public void setBetTimes(int betTimes) {
            this.betTimes = betTimes;
        }

        public double getWageredAmount() {
            return wageredAmount;
        }

        public void setWageredAmount(double wageredAmount) {
            this.wageredAmount = wageredAmount;
        }
    }

    public static class Box {
        public int id;
        public int num;
        public int currencyId;
        public double amount;

        public int getCurrencyId() {
            return currencyId;
        }

        public void setCurrencyId(int currencyId) {
            this.currencyId = currencyId;
        }

        public int getId() {
            return id;
        }

        public void setId(int id) {
            this.id = id;
        }

        public int getNum() {
            return num;
        }

        public void setNum(int num) {
            this.num = num;
        }

        public double getAmount() {
            return amount;
        }

        public void setAmount(double amount) {
            this.amount = amount;
        }
    }

    public static class Rule {
        public int language;
        public String icon;
        public String rule;

        public int getLanguage() {
            return language;
        }

        public void setLanguage(int language) {
            this.language = language;
        }

        public String getIcon() {
            return StringUtil.isNullOrEmpty(icon) ? null : icon;
        }

        public void setIcon(String icon) {
            this.icon = icon;
        }

        public String getRule() {
            return StringUtil.isNullOrEmpty(rule) ? "" : rule;
        }

        public void setRule(String rule) {
            this.rule = rule;
        }
    }
}
