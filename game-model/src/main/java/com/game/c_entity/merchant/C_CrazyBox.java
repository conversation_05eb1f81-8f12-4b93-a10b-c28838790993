package com.game.c_entity.merchant;

import com.game.engine.utils.JsonUtils;
import com.game.po.data.IDataChecker;
import org.bson.types.ObjectId;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.*;

@Document(collection = "c_crazyBox")
public class C_CrazyBox implements IDataChecker {

    @Id
    private ObjectId _id;

    private int c_id;

    private int activityId;

    //商户
    private String business_no;

    //奖励币种
    private int rewardCurrencyId;

    //奖励金额
    private double rewardAmount;

    //打码
    private int turnoverMul;

    //每日抽卡上限
    private int dailyCardDrawLimit;

    //每多少次必中
    private int mustWinTimes;

    private boolean open;

    private List<String> probabilityData = new ArrayList<>();

    private List<String> extraData = new ArrayList<>();

    private transient final Map<Integer, ProbabilityInfo> probabilityInfoMap = new LinkedHashMap<>();

    private transient final Map<Integer, ExtraInfo> extraInfoMap = new LinkedHashMap<>();

    @Override
    public boolean check() throws Exception {
        if (!probabilityData.isEmpty()) {
            for (String data : probabilityData) {
                final ProbabilityInfo probabilityInfo = JsonUtils.readFromJson(data, ProbabilityInfo.class);
                probabilityInfoMap.put(probabilityInfo.id, probabilityInfo);
            }
        }

        if (!extraData.isEmpty()) {
            for (String data : extraData) {
                final ExtraInfo extraInfo = JsonUtils.readFromJson(data, ExtraInfo.class);
                extraInfoMap.put(extraInfo.id, extraInfo);
            }
        }
        return true;
    }

    public int getRewardCurrencyId() {
        return rewardCurrencyId;
    }

    public void setRewardCurrencyId(int rewardCurrencyId) {
        this.rewardCurrencyId = rewardCurrencyId;
    }

    public int getC_id() {
        return c_id;
    }

    public void setC_id(int c_id) {
        this.c_id = c_id;
    }

    public int getActivityId() {
        return activityId;
    }

    public void setActivityId(int activityId) {
        this.activityId = activityId;
    }

    public String getBusiness_no() {
        return business_no;
    }

    public void setBusiness_no(String business_no) {
        this.business_no = business_no;
    }

    public double getRewardAmount() {
        return rewardAmount;
    }

    public void setRewardAmount(double rewardAmount) {
        this.rewardAmount = rewardAmount;
    }

    public int getTurnoverMul() {
        return turnoverMul;
    }

    public void setTurnoverMul(int turnoverMul) {
        this.turnoverMul = turnoverMul;
    }

    public int getDailyCardDrawLimit() {
        return dailyCardDrawLimit;
    }

    public void setDailyCardDrawLimit(int dailyCardDrawLimit) {
        this.dailyCardDrawLimit = dailyCardDrawLimit;
    }

    public int getMustWinTimes() {
        return mustWinTimes;
    }

    public void setMustWinTimes(int mustWinTimes) {
        this.mustWinTimes = mustWinTimes;
    }

    public boolean isOpen() {
        return open;
    }

    public void setOpen(boolean open) {
        this.open = open;
    }

    public List<String> getProbabilityData() {
        return probabilityData;
    }

    public void setProbabilityData(List<String> probabilityData) {
        this.probabilityData = probabilityData;
    }

    public List<String> getExtraData() {
        return extraData;
    }

    public void setExtraData(List<String> extraData) {
        this.extraData = extraData;
    }

    public Map<Integer, ProbabilityInfo> getProbabilityInfoMap() {
        return probabilityInfoMap;
    }

    public Map<Integer, ExtraInfo> getExtraInfoMap() {
        return extraInfoMap;
    }

    public static class ProbabilityInfo {
        public int id;
        //1.货币 2.卡
        public int rewardType;
        public int currencyId;
        public double minAmount;
        public double maxAmount;
        public int cardId;
        public int turnoverMul;
        public int weight;

        public int getCurrencyId() {
            return currencyId;
        }

        public void setCurrencyId(int currencyId) {
            this.currencyId = currencyId;
        }

        public int getId() {
            return id;
        }

        public void setId(int id) {
            this.id = id;
        }

        public int getRewardType() {
            return rewardType;
        }

        public void setRewardType(int rewardType) {
            this.rewardType = rewardType;
        }

        public double getMinAmount() {
            return minAmount;
        }

        public void setMinAmount(double minAmount) {
            this.minAmount = minAmount;
        }

        public double getMaxAmount() {
            return maxAmount;
        }

        public void setMaxAmount(double maxAmount) {
            this.maxAmount = maxAmount;
        }

        public int getCardId() {
            return cardId;
        }

        public void setCardId(int cardId) {
            this.cardId = cardId;
        }

        public int getTurnoverMul() {
            return turnoverMul;
        }

        public void setTurnoverMul(int turnoverMul) {
            this.turnoverMul = turnoverMul;
        }

        public int getWeight() {
            return weight;
        }

        public void setWeight(int weight) {
            this.weight = weight;
        }
    }

    public static class ExtraInfo {
        public int id;
        public int cardNum;
        public int currencyId;
        public double rechargeAmount;
        public int recoveryRatio;

        public int getCardNum() {
            return cardNum;
        }

        public void setCardNum(int cardNum) {
            this.cardNum = cardNum;
        }

        public int getId() {
            return id;
        }

        public void setId(int id) {
            this.id = id;
        }

        public int getCurrencyId() {
            return currencyId;
        }

        public void setCurrencyId(int currencyId) {
            this.currencyId = currencyId;
        }

        public double getRechargeAmount() {
            return rechargeAmount;
        }

        public void setRechargeAmount(double rechargeAmount) {
            this.rechargeAmount = rechargeAmount;
        }

        public int getRecoveryRatio() {
            return recoveryRatio;
        }

        public void setRecoveryRatio(int recoveryRatio) {
            this.recoveryRatio = recoveryRatio;
        }
    }

}
