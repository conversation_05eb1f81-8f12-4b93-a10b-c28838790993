package com.game.c_entity.merchant;

import com.game.po.data.IDataChecker;
import org.bson.types.ObjectId;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

@Document(collection = "c_mailSmtp")
public class C_MailSmtp implements IDataChecker {

    @Id
    private ObjectId _id;

    private int c_Id;

    //商户号
    private String business_no;

    private String smtpHost;

    private String smtpSender;

    private String smtpUserName;

    private String smtpPassWard;

    private boolean open;

    private boolean baseOpen;

    @Override
    public boolean check() throws Exception {
        return true;
    }

    public int getC_Id() {
        return c_Id;
    }

    public void setC_Id(int c_Id) {
        this.c_Id = c_Id;
    }

    public String getBusiness_no() {
        return business_no;
    }

    public void setBusiness_no(String business_no) {
        this.business_no = business_no;
    }

    public String getSmtpUserName() {
        return smtpUserName;
    }

    public void setSmtpUserName(String smtpUserName) {
        this.smtpUserName = smtpUserName;
    }

    public String getSmtpHost() {
        return smtpHost;
    }

    public void setSmtpHost(String smtpHost) {
        this.smtpHost = smtpHost;
    }

    public String getSmtpSender() {
        return smtpSender;
    }

    public void setSmtpSender(String smtpSender) {
        this.smtpSender = smtpSender;
    }

    public String getSmtpPassWard() {
        return smtpPassWard;
    }

    public void setSmtpPassWard(String smtpPassWard) {
        this.smtpPassWard = smtpPassWard;
    }

    public boolean isOpen() {
        return open;
    }

    public void setOpen(boolean open) {
        this.open = open;
    }

    public boolean isBaseOpen() {
        return baseOpen;
    }

    public void setBaseOpen(boolean baseOpen) {
        this.baseOpen = baseOpen;
    }
}
