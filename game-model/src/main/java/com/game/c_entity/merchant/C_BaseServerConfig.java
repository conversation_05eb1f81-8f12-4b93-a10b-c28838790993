package com.game.c_entity.merchant;

import com.game.po.data.IDataChecker;
import org.bson.types.ObjectId;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

@Document(collection = "c_baseServerConfig")
public class C_BaseServerConfig implements IDataChecker {

    @Id
    private ObjectId _id;

    private int c_id;

    //商户
    private String business_no;

    //版本
    private String version;

    //服务器id
    private List<Integer> serverId = new ArrayList<>();

    @Override
    public boolean check() throws Exception {
        return true;
    }

    public int getC_id() {
        return c_id;
    }

    public void setC_id(int c_id) {
        this.c_id = c_id;
    }

    public String getBusiness_no() {
        return business_no;
    }

    public void setBusiness_no(String business_no) {
        this.business_no = business_no;
    }

    public String getVersion() {
        return version;
    }

    public void setVersion(String version) {
        this.version = version;
    }

    public List<Integer> getServerId() {
        return serverId;
    }

    public void setServerId(List<Integer> serverId) {
        this.serverId = serverId;
    }

    public int getServerId(long pid) {
        if (serverId.size() == 1) {
            return serverId.getFirst();
        }
        Collections.sort(serverId);
        final int index = (int) pid % serverId.size();
        return serverId.get(index);
    }
}
