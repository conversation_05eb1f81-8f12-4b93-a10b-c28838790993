package com.game.c_entity.merchant;

import com.game.po.data.IDataChecker;
import org.bson.types.ObjectId;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.ArrayList;
import java.util.List;

@Document(collection = "c_redemptionCode")
public class C_RedemptionCode implements IDataChecker {
    @Id
    private ObjectId _id;

    private int redemptionId;

    //商户
    private String business_no;

    private String redeemName;

    //1.全部玩家 2.指定玩家 3.渠道玩家
    private int redeemType;

    //1.复用 2.单次
    private int type;

    //可用次数
    private int availableTimes;

    public List<Long> playerIds = new ArrayList<>();

    public List<Integer> channelIds = new ArrayList<>();

    private List<String> redeemCodes = new ArrayList<>();

    //奖励币种
    private int rewardCurrencyId;

    //奖励金额
    private double rewardAmount;

    //打码量倍数
    private int turnoverMul;

    //过期时间
    private long expiredTime;

    //是否开启
    private boolean open;

    private long deleteTime;

    @Override
    public boolean check() throws Exception {
        return true;
    }

    public int getRedemptionId() {
        return redemptionId;
    }

    public void setRedemptionId(int redemptionId) {
        this.redemptionId = redemptionId;
    }

    public String getBusiness_no() {
        return business_no;
    }

    public void setBusiness_no(String business_no) {
        this.business_no = business_no;
    }

    public String getRedeemName() {
        return redeemName;
    }

    public void setRedeemName(String redeemName) {
        this.redeemName = redeemName;
    }

    public int getRedeemType() {
        return redeemType;
    }

    public void setRedeemType(int redeemType) {
        this.redeemType = redeemType;
    }

    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }

    public List<String> getRedeemCodes() {
        return redeemCodes;
    }

    public void setRedeemCodes(List<String> redeemCodes) {
        this.redeemCodes = redeemCodes;
    }

    public int getRewardCurrencyId() {
        return rewardCurrencyId;
    }

    public void setRewardCurrencyId(int rewardCurrencyId) {
        this.rewardCurrencyId = rewardCurrencyId;
    }

    public double getRewardAmount() {
        return rewardAmount;
    }

    public void setRewardAmount(double rewardAmount) {
        this.rewardAmount = rewardAmount;
    }

    public int getAvailableTimes() {
        return availableTimes;
    }

    public void setAvailableTimes(int availableTimes) {
        this.availableTimes = availableTimes;
    }

    public int getTurnoverMul() {
        return turnoverMul;
    }

    public void setTurnoverMul(int turnoverMul) {
        this.turnoverMul = turnoverMul;
    }

    public long getExpiredTime() {
        return expiredTime;
    }

    public void setExpiredTime(long expiredTime) {
        this.expiredTime = expiredTime;
    }

    public boolean isOpen() {
        return open;
    }

    public void setOpen(boolean open) {
        this.open = open;
    }

    public long getDeleteTime() {
        return deleteTime;
    }

    public void setDeleteTime(long deleteTime) {
        this.deleteTime = deleteTime;
    }

    public List<Long> getPlayerIds() {
        return playerIds;
    }

    public void setPlayerIds(List<Long> playerIds) {
        this.playerIds = playerIds;
    }

    public List<Integer> getChannelIds() {
        return channelIds;
    }

    public void setChannelIds(List<Integer> channelIds) {
        this.channelIds = channelIds;
    }
}
