package com.game.c_entity.merchant;

import com.alibaba.fastjson.JSON;
import com.game.po.data.IDataChecker;
import io.netty.util.internal.StringUtil;
import org.bson.types.ObjectId;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

@Document(collection = "c_mysteryBonus")
public class C_MysteryBonus implements IDataChecker {

    @Id
    private ObjectId _id;

    private int c_id;

    //商户
    private String business_no;

    private int activityId;

    private String activityName;

    private List<Integer> languages = new ArrayList<>();

    //时区
    private String timeZone;

    //排序
    private int sort;

    //参与 1.注册
    private int participate;

    //币种
    private int currencyId;

    //打码倍数
    private int turnoverMul;

    private boolean open;

    private long startTime;

    private long endTime;

    //规则说明
    private List<String> ruleData;

    private List<String> rewardData;

    private transient final Map<Integer, MysteryRewardInfo> mysteryRewardInfoMap = new LinkedHashMap<>();

    private transient final Map<Integer, Rule> ruleMap = new LinkedHashMap<>();

    @Override
    public boolean check() throws Exception {
        if (this.rewardData != null && !this.rewardData.isEmpty()) {
            for (String data : this.rewardData) {
                final MysteryRewardInfo rewardInfo = JSON.parseObject(data, MysteryRewardInfo.class);
                mysteryRewardInfoMap.put(rewardInfo.id, rewardInfo);
            }
        }

        if (this.ruleData != null && !this.ruleData.isEmpty()) {
            for (String data : this.ruleData) {
                final Rule rule = JSON.parseObject(data, Rule.class);
                ruleMap.put(rule.language, rule);
            }
        }

        return true;
    }

    public int getC_id() {
        return c_id;
    }

    public void setC_id(int c_id) {
        this.c_id = c_id;
    }

    public int getActivityId() {
        return activityId;
    }

    public void setActivityId(int activityId) {
        this.activityId = activityId;
    }

    public String getBusiness_no() {
        return business_no;
    }

    public void setBusiness_no(String business_no) {
        this.business_no = business_no;
    }

    public String getActivityName() {
        return activityName;
    }

    public void setActivityName(String activityName) {
        this.activityName = activityName;
    }

    public List<Integer> getLanguages() {
        return languages;
    }

    public void setLanguages(List<Integer> languages) {
        this.languages = languages;
    }

    public String getTimeZone() {
        return timeZone;
    }

    public void setTimeZone(String timeZone) {
        this.timeZone = timeZone;
    }

    public int getSort() {
        return sort;
    }

    public void setSort(int sort) {
        this.sort = sort;
    }

    public int getParticipate() {
        return participate;
    }

    public void setParticipate(int participate) {
        this.participate = participate;
    }

    public int getCurrencyId() {
        return currencyId;
    }

    public void setCurrencyId(int currencyId) {
        this.currencyId = currencyId;
    }

    public int getTurnoverMul() {
        return turnoverMul;
    }

    public void setTurnoverMul(int turnoverMul) {
        this.turnoverMul = turnoverMul;
    }

    public boolean isOpen() {
        return open;
    }

    public void setOpen(boolean open) {
        this.open = open;
    }

    public long getStartTime() {
        return startTime;
    }

    public void setStartTime(long startTime) {
        this.startTime = startTime;
    }

    public long getEndTime() {
        return endTime;
    }

    public void setEndTime(long endTime) {
        this.endTime = endTime;
    }

    public List<String> getRuleData() {
        return ruleData;
    }

    public void setRuleData(List<String> ruleData) {
        this.ruleData = ruleData;
    }

    public List<String> getRewardData() {
        return rewardData;
    }

    public void setRewardData(List<String> rewardData) {
        this.rewardData = rewardData;
    }

    public Map<Integer, MysteryRewardInfo> getMysteryRewardInfoMap() {
        return mysteryRewardInfoMap;
    }

    public Map<Integer, Rule> getRuleMap() {
        return ruleMap;
    }

    public static class MysteryRewardInfo {
        public int id;
        //结算领取时间
        public long settlementTime;
        //过期时间
        public long expiredTime;

        public List<RewardGear> rewardGears = new ArrayList<>();

        private List<RewardGear> showRewardGears = new ArrayList<>();

        public int getId() {
            return id;
        }

        public void setId(int id) {
            this.id = id;
        }

        public long getSettlementTime() {
            return settlementTime;
        }

        public void setSettlementTime(long settlementTime) {
            this.settlementTime = settlementTime;
        }

        public long getExpiredTime() {
            return expiredTime;
        }

        public void setExpiredTime(long expiredTime) {
            this.expiredTime = expiredTime;
        }

        public List<RewardGear> getRewardGears() {
            return rewardGears;
        }

        public void setRewardGears(List<RewardGear> rewardGears) {
            this.rewardGears = rewardGears;
        }

        public List<RewardGear> getShowRewardGears() {
            return showRewardGears;
        }

        public void setShowRewardGears(List<RewardGear> showRewardGears) {
            this.showRewardGears = showRewardGears;
        }
    }

    public static class RewardGear {
        public int id;
        public int currencyId;
        public double rechargeMin;
        public double rechargeMax;

        public double rewardMin;
        public double rewardMax;

        public int getId() {
            return id;
        }

        public void setId(int id) {
            this.id = id;
        }

        public int getCurrencyId() {
            return currencyId;
        }

        public void setCurrencyId(int currencyId) {
            this.currencyId = currencyId;
        }

        public double getRechargeMin() {
            return rechargeMin;
        }

        public void setRechargeMin(double rechargeMin) {
            this.rechargeMin = rechargeMin;
        }

        public double getRechargeMax() {
            return rechargeMax;
        }

        public void setRechargeMax(double rechargeMax) {
            this.rechargeMax = rechargeMax;
        }

        public double getRewardMin() {
            return rewardMin;
        }

        public void setRewardMin(double rewardMin) {
            this.rewardMin = rewardMin;
        }

        public double getRewardMax() {
            return rewardMax;
        }

        public void setRewardMax(double rewardMax) {
            this.rewardMax = rewardMax;
        }
    }

    public static class Rule {
        public int language;
        public String icon;
        public String rule;

        public int getLanguage() {
            return language;
        }

        public void setLanguage(int language) {
            this.language = language;
        }

        public String getIcon() {
            return StringUtil.isNullOrEmpty(icon) ? null : icon;
        }

        public void setIcon(String icon) {
            this.icon = icon;
        }

        public String getRule() {
            return StringUtil.isNullOrEmpty(rule) ? "" : rule;
        }

        public void setRule(String rule) {
            this.rule = rule;
        }
    }
}
