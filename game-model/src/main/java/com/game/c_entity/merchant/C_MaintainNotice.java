package com.game.c_entity.merchant;

import com.game.engine.utils.JsonUtils;
import com.game.po.data.IDataChecker;
import io.netty.util.internal.StringUtil;
import org.bson.types.ObjectId;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.*;

@Document(collection = "c_maintainNotice")
public class C_MaintainNotice implements IDataChecker {

    @Id
    private ObjectId _id;

    //商户
    private String business_no;

    private int c_id;

    //开始时间
    private long startTime;

    //结束时间
    private long endTime;

    //状态
    private boolean status;

    private List<String> maintainData = new ArrayList<>();

    private List<Long> whitelistIds = new ArrayList<>();

    private transient Map<Integer, MaintainInfo> maintainInfoMap = new LinkedHashMap<>();

    @Override
    public boolean check() throws Exception {
        if (this.maintainData != null && !this.maintainData.isEmpty()) {
            for (String data : this.maintainData) {
                final MaintainInfo maintainInfo = JsonUtils.readFromJson(data, MaintainInfo.class);
                this.maintainInfoMap.put(maintainInfo.language, maintainInfo);
            }
        }
        return true;
    }

    public String getBusiness_no() {
        return business_no;
    }

    public void setBusiness_no(String business_no) {
        this.business_no = business_no;
    }

    public int getC_id() {
        return c_id;
    }

    public void setC_id(int c_id) {
        this.c_id = c_id;
    }

    public long getStartTime() {
        return startTime;
    }

    public void setStartTime(long startTime) {
        this.startTime = startTime;
    }

    public long getEndTime() {
        return endTime;
    }

    public void setEndTime(long endTime) {
        this.endTime = endTime;
    }

    public boolean isStatus() {
        return status;
    }

    public void setStatus(boolean status) {
        this.status = status;
    }

    public List<String> getMaintainData() {
        return maintainData;
    }

    public void setMaintainData(List<String> maintainData) {
        this.maintainData = maintainData;
    }

    public List<Long> getWhitelistIds() {
        return whitelistIds;
    }

    public void setWhitelistIds(List<Long> whitelistIds) {
        this.whitelistIds = whitelistIds;
    }

    public Map<Integer, MaintainInfo> getMaintainInfoMap() {
        return maintainInfoMap;
    }

    public void setMaintainInfoMap(Map<Integer, MaintainInfo> maintainInfoMap) {
        this.maintainInfoMap = maintainInfoMap;
    }

    public static class MaintainInfo {
        public int language;

        public String info;

        public int getLanguage() {
            return language;
        }

        public void setLanguage(int language) {
            this.language = language;
        }

        public String getInfo() {
            return StringUtil.isNullOrEmpty(info) ? "" : info;
        }

        public void setInfo(String info) {
            this.info = info;
        }
    }
}
