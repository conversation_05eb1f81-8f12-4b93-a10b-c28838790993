package com.game.c_entity.merchant;

import com.game.po.data.IDataChecker;
import io.netty.util.internal.StringUtil;
import org.bson.types.ObjectId;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

@Document(collection = "c_winTicketNumbers")
public class C_WinTicketNumbers implements IDataChecker {
    @Id
    private ObjectId _id;

    private String business_no;

    //期数
    private String date;

    //票号
    private int ticketNumbers;

    private String headId;

    private long playerId;

    private String playerName;

    private int rewardLevel;

    private int currencyId;

    private double reward;

    @Override
    public boolean check() throws Exception {
        return true;
    }

    public String getBusiness_no() {
        return business_no;
    }

    public void setBusiness_no(String business_no) {
        this.business_no = business_no;
    }

    public String getDate() {
        return StringUtil.isNullOrEmpty(date) ? "" : date;
    }

    public void setDate(String date) {
        this.date = date;
    }

    public String getHeadId() {
        return StringUtil.isNullOrEmpty(headId) ? "" : headId;
    }

    public void setHeadId(String headId) {
        this.headId = headId;
    }

    public int getTicketNumbers() {
        return ticketNumbers;
    }

    public void setTicketNumbers(int ticketNumbers) {
        this.ticketNumbers = ticketNumbers;
    }

    public long getPlayerId() {
        return playerId;
    }

    public void setPlayerId(long playerId) {
        this.playerId = playerId;
    }

    public String getPlayerName() {
        return StringUtil.isNullOrEmpty(playerName) ? "" : playerName;
    }

    public void setPlayerName(String playerName) {
        this.playerName = playerName;
    }

    public int getRewardLevel() {
        return rewardLevel;
    }

    public void setRewardLevel(int rewardLevel) {
        this.rewardLevel = rewardLevel;
    }

    public int getCurrencyId() {
        return currencyId;
    }

    public void setCurrencyId(int currencyId) {
        this.currencyId = currencyId;
    }

    public double getReward() {
        return reward;
    }

    public void setReward(double reward) {
        this.reward = reward;
    }
}
