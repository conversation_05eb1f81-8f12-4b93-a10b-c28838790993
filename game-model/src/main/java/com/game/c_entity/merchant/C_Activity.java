package com.game.c_entity.merchant;

import com.game.engine.utils.JsonUtils;
import com.game.po.data.IDataChecker;
import io.netty.util.internal.StringUtil;
import org.bson.types.ObjectId;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.*;

@Document(collection = "c_activity")
public class C_Activity implements IDataChecker {
    @Id
    private ObjectId _id;

    private int c_id;

    //商户号
    @Indexed
    private String business_no;

    //活动id
    @Indexed
    private int activityId;

    //语言
    @Indexed
    private List<Integer> language = new ArrayList<>();

    private List<String> activityData = new ArrayList<>();

    //活动类型 4.首充 5.充值 6.下注 7.排行 8.补偿 9.免费送
    private int activityType;

    //活动子类型
    private int activitySubType;

    //活动标签 1.casino 2.sports
    private int activityTag;

    //0.不限制 1.渠道可见 2.渠道不可见
    private int channelLimit;

    //0.不限制 1.代理可见 2.代理不可见
    private int agentLimit;

    private List<Integer> channelType = new ArrayList<>();

    private List<Integer> channels = new ArrayList<>();

    private List<Integer> agentIds = new ArrayList<>();

    private String timeZone;

    //开始时间
    private long startTime;

    //结束时间
    private long endTime;

    //状态
    private boolean status;

    //救济时间
    private int compensateTime;

    //领取 1.手动 2.自动
    private int receive;

    //奖励类型 1.每档 2.最高
    private int rewardType;

    //evUsd
    private boolean evUsd;

    //结算方式  0.无周期 1.每日 2.每周
    private int settlementCycle;

    //是否立即领取
    private boolean nowReceive;

    //奖励清空时间 0.立即
    private long awardClearTime;

    //报名时间
    private long startSignUpTime;

    private long endSignUpTime;

    //领取天数
    private int receiveDay;

    //按钮
//    private boolean button;

    //0.无 1.封面 2.详情按钮
    private int jumpTarget;

    //跳转类型 1.内连 2.外链
    private int jumpType;

    //弹框类型 1.任务 2.转盘 3.充值 4.客服
    private int popupLinks;

    //功能类型 1.pwa
    private int functionType;

    //内部链接
    private String innerLinks;

    //外链接
    private String externalLinks;

    //条件
    private List<String> conditionData;
    //奖励
    private List<String> rewardData;

    //投注数据
    private String wageredData;
    //免费送
    private String freeGiveData;

    //排行
    private int rewardNum;//奖励人数
    //排行邀请条件
    private String inviteData;

    //排序
    private int sort;

    //注册过期时间
    private long registerExpired;

    private transient WageredCondition wageredCondition;
    private transient FreeGiveInfo freeGiveInfo;
    private transient InviteInfo inviteInfo;

    private transient Map<Integer, Activity> activityMap = new LinkedHashMap<>();
    private transient Map<Integer, ConditionInfo> conditionMap = new LinkedHashMap<>();
    private transient Map<Integer, List<RewardInfo>> rewardsMap = new LinkedHashMap<>();

    @Override
    public boolean check() throws Exception {
        if (this.activityData != null && !this.activityData.isEmpty()) {
            for (String str : this.activityData) {
                final Activity activity = JsonUtils.readFromJson(str, Activity.class);
                activityMap.putIfAbsent(activity.getLanguage(), activity);
            }
        }

        if (this.conditionData != null && !this.conditionData.isEmpty()) {
            for (String data : this.conditionData) {
                final ConditionInfo conditionInfo = JsonUtils.readFromJson(data, ConditionInfo.class);
                conditionMap.putIfAbsent(conditionInfo.currencyId, conditionInfo);
            }
        }

        if (this.rewardData != null && !this.rewardData.isEmpty()) {
            for (String data : this.rewardData) {
                final RewardInfo rewardInfo = JsonUtils.readFromJson(data, RewardInfo.class);
                List<RewardInfo> rewardInfos = rewardsMap.putIfAbsent(rewardInfo.currencyId, new ArrayList<>());
                if (rewardInfos == null) {
                    rewardInfos = rewardsMap.get(rewardInfo.currencyId);
                }
                rewardInfos.add(rewardInfo);
            }
        }

        if (!StringUtil.isNullOrEmpty(this.wageredData)) {
            wageredCondition = JsonUtils.readFromJson(this.wageredData, WageredCondition.class);
        }

        if (!StringUtil.isNullOrEmpty(this.freeGiveData)) {
            freeGiveInfo = JsonUtils.readFromJson(this.freeGiveData, FreeGiveInfo.class);
        }

        if (!StringUtil.isNullOrEmpty(this.inviteData)) {
            this.inviteInfo = JsonUtils.readFromJson(this.inviteData, InviteInfo.class);
        }
        return true;
    }

    public boolean channelLimit(int channelId) {
        if (this.channelLimit == 0) {
            return false;
        }

        if (this.channelLimit == 1) {//可见
            if (channelId == 0) {
                return true;
            }
            final int type = Character.getNumericValue(String.valueOf(channelId).charAt(1));
            if (this.channelType != null && this.channelType.contains(type)) {
                if (this.channels.contains(channelId)) {
                    return false;
                } else {
                    return true;
                }
            } else {
                return true;
            }
        }

        if (this.channelLimit == 2) {//不可见
            if (channelId == 0) {
                return false;
            }
            final int type = Character.getNumericValue(String.valueOf(channelId).charAt(1));
            if (this.channelType != null && this.channelType.contains(type)) {
                if (this.channels.contains(channelId)) {
                    return true;
                } else {
                    return false;
                }
            } else {
                return false;
            }
        }

        return true;
    }

    public boolean agentLimit(int agentId) {
        if (this.agentLimit == 0) {
            return false;
        }

        if (this.agentLimit == 1) {//可见
            if (agentId == 0) {
                return true;
            }
            if (this.agentIds.contains(agentId)) {
                return false;
            } else {
                return true;
            }
        }

        if (this.agentLimit == 2) {//不可见
            if (agentId == 0) {
                return false;
            }
            if (this.agentIds.contains(agentId)) {
                return true;
            } else {
                return false;
            }
        }
        return true;
    }

    public int getC_id() {
        return c_id;
    }

    public void setC_id(int c_id) {
        this.c_id = c_id;
    }

    public String getBusiness_no() {
        return business_no;
    }

    public void setBusiness_no(String business_no) {
        this.business_no = business_no;
    }

    public int getActivityId() {
        return activityId;
    }

    public void setActivityId(int activityId) {
        this.activityId = activityId;
    }

    public List<String> getActivityData() {
        return activityData;
    }

    public void setActivityData(List<String> activityData) {
        this.activityData = activityData;
    }

    public int getActivityType() {
        return activityType;
    }

    public void setActivityType(int activityType) {
        this.activityType = activityType;
    }

    public int getActivitySubType() {
        return activitySubType;
    }

    public void setActivitySubType(int activitySubType) {
        this.activitySubType = activitySubType;
    }

    public int getActivityTag() {
        return activityTag;
    }

    public void setActivityTag(int activityTag) {
        this.activityTag = activityTag;
    }

    public int getChannelLimit() {
        return channelLimit;
    }

    public void setChannelLimit(int channelLimit) {
        this.channelLimit = channelLimit;
    }

    public int getAgentLimit() {
        return agentLimit;
    }

    public void setAgentLimit(int agentLimit) {
        this.agentLimit = agentLimit;
    }

    public List<Integer> getChannelType() {
        return channelType;
    }

    public void setChannelType(List<Integer> channelType) {
        this.channelType = channelType;
    }

    public List<Integer> getChannels() {
        return channels;
    }

    public void setChannels(List<Integer> channels) {
        this.channels = channels;
    }

    public List<Integer> getAgentIds() {
        return agentIds;
    }

    public void setAgentIds(List<Integer> agentIds) {
        this.agentIds = agentIds;
    }

    public List<Integer> getLanguage() {
        return language;
    }

    public void setLanguage(List<Integer> language) {
        this.language = language;
    }

    public String getTimeZone() {
        return timeZone;
    }

    public void setTimeZone(String timeZone) {
        this.timeZone = timeZone;
    }

    public long getStartTime() {
        return startTime;
    }

    public void setStartTime(long startTime) {
        this.startTime = startTime;
    }

    public long getEndTime() {
        return endTime;
    }

    public void setEndTime(long endTime) {
        this.endTime = endTime;
    }

    public boolean isStatus() {
        return status;
    }

    public void setStatus(boolean status) {
        this.status = status;
    }

    public int getCompensateTime() {
        return compensateTime;
    }

    public void setCompensateTime(int compensateTime) {
        this.compensateTime = compensateTime;
    }

    public int getReceive() {
        return receive;
    }

    public void setReceive(int receive) {
        this.receive = receive;
    }

    public int getRewardType() {
        return rewardType;
    }

    public void setRewardType(int rewardType) {
        this.rewardType = rewardType;
    }

    public boolean isEvUsd() {
        return evUsd;
    }

    public void setEvUsd(boolean evUsd) {
        this.evUsd = evUsd;
    }

    public int getSettlementCycle() {
        return settlementCycle;
    }

    public void setSettlementCycle(int settlementCycle) {
        this.settlementCycle = settlementCycle;
    }

    public boolean isNowReceive() {
        return nowReceive;
    }

    public void setNowReceive(boolean nowReceive) {
        this.nowReceive = nowReceive;
    }

    public long getAwardClearTime() {
        return awardClearTime;
    }

    public void setAwardClearTime(long awardClearTime) {
        this.awardClearTime = awardClearTime;
    }

    public long getStartSignUpTime() {
        return startSignUpTime;
    }

    public void setStartSignUpTime(long startSignUpTime) {
        this.startSignUpTime = startSignUpTime;
    }

    public long getEndSignUpTime() {
        return endSignUpTime;
    }

    public void setEndSignUpTime(long endSignUpTime) {
        this.endSignUpTime = endSignUpTime;
    }

    public int getReceiveDay() {
        return receiveDay;
    }

    public void setReceiveDay(int receiveDay) {
        this.receiveDay = receiveDay;
    }

    public int getJumpTarget() {
        return jumpTarget;
    }

    public void setJumpTarget(int jumpTarget) {
        this.jumpTarget = jumpTarget;
    }

    public int getJumpType() {
        return jumpType;
    }

    public void setJumpType(int jumpType) {
        this.jumpType = jumpType;
    }

    public int getPopupLinks() {
        return popupLinks;
    }

    public void setPopupLinks(int popupLinks) {
        this.popupLinks = popupLinks;
    }

    public int getFunctionType() {
        return functionType;
    }

    public void setFunctionType(int functionType) {
        this.functionType = functionType;
    }

    public String getInnerLinks() {
        return StringUtil.isNullOrEmpty(innerLinks) ? "" : innerLinks;
    }

    public void setInnerLinks(String innerLinks) {
        this.innerLinks = innerLinks;
    }

    public String getExternalLinks() {
        return StringUtil.isNullOrEmpty(externalLinks) ? "" : externalLinks;
    }

    public void setExternalLinks(String externalLinks) {
        this.externalLinks = externalLinks;
    }

    public List<String> getConditionData() {
        return conditionData;
    }

    public void setConditionData(List<String> conditionData) {
        this.conditionData = conditionData;
    }

    public List<String> getRewardData() {
        return rewardData;
    }

    public void setRewardData(List<String> rewardData) {
        this.rewardData = rewardData;
    }

    public String getWageredData() {
        return wageredData;
    }

    public void setWageredData(String wageredData) {
        this.wageredData = wageredData;
    }

    public String getFreeGiveData() {
        return freeGiveData;
    }

    public void setFreeGiveData(String freeGiveData) {
        this.freeGiveData = freeGiveData;
    }

    public int getRewardNum() {
        return rewardNum;
    }

    public void setRewardNum(int rewardNum) {
        this.rewardNum = rewardNum;
    }

    public String getInviteData() {
        return inviteData;
    }

    public void setInviteData(String inviteData) {
        this.inviteData = inviteData;
    }

    public Map<Integer, Activity> getActivityMap() {
        return activityMap;
    }

    public void setActivityMap(Map<Integer, Activity> activityMap) {
        this.activityMap = activityMap;
    }

    public Map<Integer, ConditionInfo> getConditionMap() {
        return conditionMap;
    }

    public void setConditionMap(Map<Integer, ConditionInfo> conditionMap) {
        this.conditionMap = conditionMap;
    }

    public Map<Integer, List<RewardInfo>> getRewardsMap() {
        return rewardsMap;
    }

    public void setRewardsMap(Map<Integer, List<RewardInfo>> rewardsMap) {
        this.rewardsMap = rewardsMap;
    }

    public WageredCondition getWageredCondition() {
        return wageredCondition;
    }

    public void setWageredCondition(WageredCondition wageredCondition) {
        this.wageredCondition = wageredCondition;
    }

    public FreeGiveInfo getFreeGiveInfo() {
        return freeGiveInfo;
    }

    public void setFreeGiveInfo(FreeGiveInfo freeGiveInfo) {
        this.freeGiveInfo = freeGiveInfo;
    }

    public InviteInfo getInviteInfo() {
        return inviteInfo;
    }

    public void setInviteInfo(InviteInfo inviteInfo) {
        this.inviteInfo = inviteInfo;
    }

    public int getSort() {
        return sort;
    }

    public void setSort(int sort) {
        this.sort = sort;
    }

    public long getRegisterExpired() {
        return registerExpired;
    }

    public void setRegisterExpired(long registerExpired) {
        this.registerExpired = registerExpired;
    }

    public static class RewardInfo {
        public int id;
        public int currencyId;//参与币种
        public int rewardCurrency;//奖励币种
        public int turnoverMul;
        public double min;
        public double max;
        public int rewardType;//1.固定 2.比例
        public double reward;

        //免费游戏次数
        public int gameId;
        public int freeTimes;
        public double bet;
        public double minWithdraw;
        public double maxWithdraw;

        public int getId() {
            return id;
        }

        public void setId(int id) {
            this.id = id;
        }

        public int getRewardCurrency() {
            return rewardCurrency;
        }

        public void setRewardCurrency(int rewardCurrency) {
            this.rewardCurrency = rewardCurrency;
        }

        public int getCurrencyId() {
            return currencyId;
        }

        public void setCurrencyId(int currencyId) {
            this.currencyId = currencyId;
        }

        public int getTurnoverMul() {
            return turnoverMul;
        }

        public void setTurnoverMul(int turnoverMul) {
            this.turnoverMul = turnoverMul;
        }

        public double getMin() {
            return min;
        }

        public void setMin(double min) {
            this.min = min;
        }

        public double getMax() {
            return max;
        }

        public void setMax(double max) {
            this.max = max;
        }

        public int getRewardType() {
            return rewardType;
        }

        public void setRewardType(int rewardType) {
            this.rewardType = rewardType;
        }

        public double getReward() {
            return reward;
        }

        public void setReward(double reward) {
            this.reward = reward;
        }

        public int getGameId() {
            return gameId;
        }

        public void setGameId(int gameId) {
            this.gameId = gameId;
        }

        public int getFreeTimes() {
            return freeTimes;
        }

        public void setFreeTimes(int freeTimes) {
            this.freeTimes = freeTimes;
        }

        public double getBet() {
            return bet;
        }

        public void setBet(double bet) {
            this.bet = bet;
        }

        public double getMinWithdraw() {
            return minWithdraw;
        }

        public void setMinWithdraw(double minWithdraw) {
            this.minWithdraw = minWithdraw;
        }

        public double getMaxWithdraw() {
            return maxWithdraw;
        }

        public void setMaxWithdraw(double maxWithdraw) {
            this.maxWithdraw = maxWithdraw;
        }
    }

    public static class ConditionInfo {
        public int currencyId;
        public int vipLimit;//vip限制
        public double balance;//余额
        public int registerDay;//注册天数
        public int firstRechargeDay;//首充天数
        public double firstRechargeAmount;//首充金额
        public int rechargeTime;
        public int receiveTimes;//领取次数

        public boolean activities;//是否活动期间内
        public double wagered;//投注
        public double recharge;//充值

        public int getCurrencyId() {
            return currencyId;
        }

        public void setCurrencyId(int currencyId) {
            this.currencyId = currencyId;
        }

        public int getVipLimit() {
            return vipLimit;
        }

        public void setVipLimit(int vipLimit) {
            this.vipLimit = vipLimit;
        }

        public double getBalance() {
            return balance;
        }

        public void setBalance(double balance) {
            this.balance = balance;
        }

        public int getRegisterDay() {
            return registerDay;
        }

        public void setRegisterDay(int registerDay) {
            this.registerDay = registerDay;
        }

        public int getFirstRechargeDay() {
            return firstRechargeDay;
        }

        public void setFirstRechargeDay(int firstRechargeDay) {
            this.firstRechargeDay = firstRechargeDay;
        }

        public double getFirstRechargeAmount() {
            return firstRechargeAmount;
        }

        public void setFirstRechargeAmount(double firstRechargeAmount) {
            this.firstRechargeAmount = firstRechargeAmount;
        }

        public int getRechargeTime() {
            return rechargeTime;
        }

        public void setRechargeTime(int rechargeTime) {
            this.rechargeTime = rechargeTime;
        }

        public int getReceiveTimes() {
            return receiveTimes;
        }

        public void setReceiveTimes(int receiveTimes) {
            this.receiveTimes = receiveTimes;
        }

        public boolean isActivities() {
            return activities;
        }

        public void setActivities(boolean activities) {
            this.activities = activities;
        }

        public double getWagered() {
            return wagered;
        }

        public void setWagered(double wagered) {
            this.wagered = wagered;
        }

        public double getRecharge() {
            return recharge;
        }

        public void setRecharge(double recharge) {
            this.recharge = recharge;
        }
    }

    public static class WageredCondition {
        public int gameType;
        public List<Integer> platformId = new ArrayList<>();
        public List<Integer> gameId = new ArrayList<>();

        public int getGameType() {
            return gameType;
        }

        public List<Integer> getPlatformId() {
            return platformId;
        }

        public void setPlatformId(List<Integer> platformId) {
            this.platformId = platformId;
        }

        public void setGameType(int gameType) {
            this.gameType = gameType;
        }

        public List<Integer> getGameId() {
            return gameId;
        }

        public void setGameId(List<Integer> gameId) {
            this.gameId = gameId;
        }
    }

    public static class FreeGiveInfo {
        public int currencyId;
        public int turnoverMul;
        public int reward;

        //免费游戏次数
        public int gameId;
        public int freeTimes;
        public double bet;
        public double minWithdraw;
        public double maxWithdraw;

        public int getCurrencyId() {
            return currencyId;
        }

        public void setCurrencyId(int currencyId) {
            this.currencyId = currencyId;
        }

        public int getTurnoverMul() {
            return turnoverMul;
        }

        public void setTurnoverMul(int turnoverMul) {
            this.turnoverMul = turnoverMul;
        }

        public int getReward() {
            return reward;
        }

        public void setReward(int reward) {
            this.reward = reward;
        }

        public int getGameId() {
            return gameId;
        }

        public void setGameId(int gameId) {
            this.gameId = gameId;
        }

        public int getFreeTimes() {
            return freeTimes;
        }

        public void setFreeTimes(int freeTimes) {
            this.freeTimes = freeTimes;
        }

        public double getBet() {
            return bet;
        }

        public void setBet(double bet) {
            this.bet = bet;
        }

        public double getMinWithdraw() {
            return minWithdraw;
        }

        public void setMinWithdraw(double minWithdraw) {
            this.minWithdraw = minWithdraw;
        }

        public double getMaxWithdraw() {
            return maxWithdraw;
        }

        public void setMaxWithdraw(double maxWithdraw) {
            this.maxWithdraw = maxWithdraw;
        }
    }

    public static class InviteInfo {
        public int currencyId;
        //充值金额
        public double rechargeUsdAmount;

        //投注次数
        public int betTimes;

        //投注金额
        public double betUsdAmount;

        public int getCurrencyId() {
            return currencyId;
        }

        public void setCurrencyId(int currencyId) {
            this.currencyId = currencyId;
        }

        public double getRechargeUsdAmount() {
            return rechargeUsdAmount;
        }

        public void setRechargeUsdAmount(double rechargeUsdAmount) {
            this.rechargeUsdAmount = rechargeUsdAmount;
        }

        public int getBetTimes() {
            return betTimes;
        }

        public void setBetTimes(int betTimes) {
            this.betTimes = betTimes;
        }

        public double getBetUsdAmount() {
            return betUsdAmount;
        }

        public void setBetUsdAmount(double betUsdAmount) {
            this.betUsdAmount = betUsdAmount;
        }
    }

    public static class Activity {
        //语言
        private int language;

        //标题
        private String title;

        //活动名字
        private String activityName;

        //奖励显示类型 1.总奖金 2.最高奖金
        private int showRewardType;

        //奖励显示
        private int showRewardCurrencyId;

        //奖励金额
        private double showRewardAmount;

        //图标
        private String icon;

        //内部图标
        private String innerIcon;

        //描述
        private String content;

        //按钮文字
        private String buttonWord;

        public int getLanguage() {
            return language;
        }

        public void setLanguage(int language) {
            this.language = language;
        }

        public String getTitle() {
            return StringUtil.isNullOrEmpty(title) ? "" : title;
        }

        public void setTitle(String title) {
            this.title = title;
        }

        public String getActivityName() {
            return StringUtil.isNullOrEmpty(activityName) ? "" : activityName;
        }

        public void setActivityName(String activityName) {
            this.activityName = activityName;
        }

        public int getShowRewardType() {
            return showRewardType;
        }

        public void setShowRewardType(int showRewardType) {
            this.showRewardType = showRewardType;
        }

        public int getShowRewardCurrencyId() {
            return showRewardCurrencyId;
        }

        public void setShowRewardCurrencyId(int showRewardCurrencyId) {
            this.showRewardCurrencyId = showRewardCurrencyId;
        }

        public double getShowRewardAmount() {
            return showRewardAmount;
        }

        public void setShowRewardAmount(double showRewardAmount) {
            this.showRewardAmount = showRewardAmount;
        }

        public String getIcon() {
            return StringUtil.isNullOrEmpty(icon) ? "" : icon;
        }

        public void setIcon(String icon) {
            this.icon = icon;
        }

        public String getInnerIcon() {
            return StringUtil.isNullOrEmpty(innerIcon) ? "" : innerIcon;
        }

        public void setInnerIcon(String innerIcon) {
            this.innerIcon = innerIcon;
        }

        public String getContent() {
            return StringUtil.isNullOrEmpty(content) ? "" : content;
        }

        public void setContent(String content) {
            this.content = content;
        }

        public String getButtonWord() {
            return StringUtil.isNullOrEmpty(buttonWord) ? "" : buttonWord;
        }

        public void setButtonWord(String buttonWord) {
            this.buttonWord = buttonWord;
        }
    }

    public C_Activity.ConditionInfo findCondition(int currencyId) {
        return this.conditionMap.get(currencyId);
    }

    public C_Activity.RewardInfo findRankReward(int currencyId, double process) {
        final List<C_Activity.RewardInfo> rechargeList = this.rewardsMap.get(currencyId);
        if (rechargeList == null) {
            return null;
        }
        for (C_Activity.RewardInfo rewardInfo : rechargeList) {
            if (rewardInfo.getMin() <= process && process <= rewardInfo.getMax()) {
                return rewardInfo;
            }
        }
        return null;
    }

    public C_Activity.RewardInfo findInviteRewardInfo(double process) {
        for (Map.Entry<Integer, List<RewardInfo>> entry : this.rewardsMap.entrySet()) {
            for (C_Activity.RewardInfo rewardInfo : entry.getValue()) {
                if (rewardInfo.getMin() <= process && process <= rewardInfo.getMax()) {
                    return rewardInfo;
                }
            }
        }
        return null;
    }

    public C_Activity.RewardInfo findRechargeRewardInfo(int currencyId, double process) {
        final List<C_Activity.RewardInfo> rechargeList = this.rewardsMap.get(currencyId);
        if (rechargeList == null) {
            return null;
        }
        for (C_Activity.RewardInfo rewardInfo : rechargeList) {
            if (rewardInfo.getMin() <= process && process < rewardInfo.getMax()) {
                return rewardInfo;
            }
        }
        return null;
    }

    /**
     * 最高
     *
     * @param currencyId
     * @param process
     * @return
     */
    public C_Activity.RewardInfo findRewardInfo(int currencyId, double process) {
        final List<C_Activity.RewardInfo> rechargeList = this.rewardsMap.get(currencyId);
        if (rechargeList == null) {
            return null;
        }
        final List<C_Activity.RewardInfo> rewardInfos = new ArrayList<>(rechargeList);
        Collections.reverse(rewardInfos);
        for (C_Activity.RewardInfo rewardInfo : rewardInfos) {
            if (rewardInfo.getMin() <= process && process < rewardInfo.getMax()) {
                return rewardInfo;
            }
        }
        return null;
    }

    /**
     * 每档
     *
     * @param rewards
     * @param currencyId
     * @param process
     * @return
     */
    public List<C_Activity.RewardInfo> findRewardsList(final Set<Integer> rewards, int currencyId, double process) {
        final List<C_Activity.RewardInfo> recharges = new ArrayList<>();
        final List<C_Activity.RewardInfo> rechargeList = this.rewardsMap.get(currencyId);
        if (rechargeList == null) {
            return recharges;
        }

        if (rechargeList.size() == rewards.size()) {
            return recharges;
        }

        for (C_Activity.RewardInfo recharge : rechargeList) {
            if (rewards.contains(recharge.id)) {
                continue;
            }
            if (process >= recharge.getMin()) {
                recharges.add(recharge);
            }
        }
        return recharges;
    }
}
