package com.game.c_entity.merchant;

import com.alibaba.fastjson.JSON;
import com.game.engine.utils.JsonUtils;
import com.game.po.data.IDataChecker;
import io.netty.util.internal.StringUtil;
import org.bson.types.ObjectId;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.*;

@Document(collection = "c_continuousDeposit")
public class C_ContinuousDeposit implements IDataChecker {

    @Id
    private ObjectId _id;

    private int c_id;

    //商户
    private String business_no;

    private int activityId;

    private String activityName;

    private List<Integer> languages = new ArrayList<>();

    private String timeZone;

    //重置周期 1.日 2.周 3.月
    private int resetCycle;

    private long startTime;

    private long endTime;

    //参与币种
    private int joinCurrency;

    //打码
    private int turnoverMul;

    private boolean open;

    //规则说明
    private List<String> ruleData = new ArrayList<>();

    private List<String> continuousDepositData = new ArrayList<>();

    private transient final Map<Integer, ContinuousDeposit> continuousDepositMap = new LinkedHashMap<>();

    private transient final Map<Integer, Rule> ruleMap = new LinkedHashMap<>();

    @Override
    public boolean check() throws Exception {
        if (this.continuousDepositData != null && !this.continuousDepositData.isEmpty()) {
            for (String data : continuousDepositData) {
                final ContinuousDeposit continuousDeposit = JsonUtils.readFromJson(data, ContinuousDeposit.class);
                this.continuousDepositMap.put(continuousDeposit.getId(), continuousDeposit);
            }
        }

        if (this.ruleData != null && !this.ruleData.isEmpty()) {
            for (String data : this.ruleData) {
                final Rule rule = JSON.parseObject(data, Rule.class);
                rule.button();
                this.ruleMap.put(rule.language, rule);
            }
        }

        return true;
    }

    public int getActivityId() {
        return activityId;
    }

    public void setActivityId(int activityId) {
        this.activityId = activityId;
    }

    public int getC_id() {
        return c_id;
    }

    public void setC_id(int c_id) {
        this.c_id = c_id;
    }

    public String getBusiness_no() {
        return business_no;
    }

    public void setBusiness_no(String business_no) {
        this.business_no = business_no;
    }

    public String getActivityName() {
        return activityName;
    }

    public void setActivityName(String activityName) {
        this.activityName = activityName;
    }

    public List<Integer> getLanguages() {
        return languages;
    }

    public void setLanguages(List<Integer> languages) {
        this.languages = languages;
    }

    public String getTimeZone() {
        return timeZone;
    }

    public void setTimeZone(String timeZone) {
        this.timeZone = timeZone;
    }

    public int getResetCycle() {
        return resetCycle;
    }

    public void setResetCycle(int resetCycle) {
        this.resetCycle = resetCycle;
    }

    public long getStartTime() {
        return startTime;
    }

    public void setStartTime(long startTime) {
        this.startTime = startTime;
    }

    public long getEndTime() {
        return endTime;
    }

    public void setEndTime(long endTime) {
        this.endTime = endTime;
    }

    public int getJoinCurrency() {
        return joinCurrency;
    }

    public void setJoinCurrency(int joinCurrency) {
        this.joinCurrency = joinCurrency;
    }

    public int getTurnoverMul() {
        return turnoverMul;
    }

    public void setTurnoverMul(int turnoverMul) {
        this.turnoverMul = turnoverMul;
    }

    public List<String> getContinuousDepositData() {
        return continuousDepositData;
    }

    public void setContinuousDepositData(List<String> continuousDepositData) {
        this.continuousDepositData = continuousDepositData;
    }

    public Map<Integer, ContinuousDeposit> getContinuousDepositMap() {
        return continuousDepositMap;
    }

    public boolean isOpen() {
        return open;
    }

    public void setOpen(boolean open) {
        this.open = open;
    }

    public List<String> getRuleData() {
        return ruleData;
    }

    public void setRuleData(List<String> ruleData) {
        this.ruleData = ruleData;
    }

    public Map<Integer, Rule> getRuleMap() {
        return ruleMap;
    }

    public static class ContinuousDeposit {
        public int id;
        public String icon;
        public double recharge;
        public double giveawayRate;
        public double giveawayUpper;

        public int getId() {
            return id;
        }

        public void setId(int id) {
            this.id = id;
        }

        public String getIcon() {
            return icon;
        }

        public void setIcon(String icon) {
            this.icon = icon;
        }

        public double getRecharge() {
            return recharge;
        }

        public void setRecharge(double recharge) {
            this.recharge = recharge;
        }

        public double getGiveawayRate() {
            return giveawayRate;
        }

        public void setGiveawayRate(double giveawayRate) {
            this.giveawayRate = giveawayRate;
        }

        public double getGiveawayUpper() {
            return giveawayUpper;
        }

        public void setGiveawayUpper(double giveawayUpper) {
            this.giveawayUpper = giveawayUpper;
        }
    }

    public static class Rule {
        public int language;
        public String icon;
        public String rule;
        public List<String> button = new ArrayList<>();

        private transient final Map<Integer, Button> buttonMap = new LinkedHashMap<>();

        public void button() {
            if (!this.button.isEmpty()) {
                for (String data : this.button) {
                    final Button button = JSON.parseObject(data, Button.class);
                    this.buttonMap.put(button.id, button);
                }
            }
        }

        public int getLanguage() {
            return language;
        }

        public void setLanguage(int language) {
            this.language = language;
        }

        public String getIcon() {
            return StringUtil.isNullOrEmpty(icon) ? null : icon;
        }

        public void setIcon(String icon) {
            this.icon = icon;
        }

        public String getRule() {
            return StringUtil.isNullOrEmpty(rule) ? "" : rule;
        }

        public void setRule(String rule) {
            this.rule = rule;
        }

        public List<String> getButton() {
            return button;
        }

        public void setButton(List<String> button) {
            this.button = button;
        }

        public Map<Integer, Button> getButtonMap() {
            return buttonMap;
        }
    }

    public static class Button {
        public int id;
        public String name;

        //跳转类型 1.内连 2.外链
        public int jumpType;

        //弹框类型 1.任务 2.转盘 3.充值 4.客服 5.登录 6.注册
        public int popupLinks;

        //内部链接
        public String innerLinks;

        //外链接
        public String externalLinks;

        public int getId() {
            return id;
        }

        public void setId(int id) {
            this.id = id;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public int getJumpType() {
            return jumpType;
        }

        public void setJumpType(int jumpType) {
            this.jumpType = jumpType;
        }

        public int getPopupLinks() {
            return popupLinks;
        }

        public void setPopupLinks(int popupLinks) {
            this.popupLinks = popupLinks;
        }

        public String getInnerLinks() {
            return innerLinks;
        }

        public void setInnerLinks(String innerLinks) {
            this.innerLinks = innerLinks;
        }

        public String getExternalLinks() {
            return externalLinks;
        }

        public void setExternalLinks(String externalLinks) {
            this.externalLinks = externalLinks;
        }
    }
}
