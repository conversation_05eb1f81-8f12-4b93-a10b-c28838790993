package com.game.c_entity.merchant;

import com.game.engine.utils.JsonUtils;
import com.game.po.data.IDataChecker;
import io.netty.util.internal.StringUtil;
import org.bson.types.ObjectId;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

@Document(collection = "c_bottomMenu")
public class C_BottomMenu implements IDataChecker {

    @Id
    private ObjectId _id;

    //商户
    private String business_no;

    private int menuId;

    //菜单排序
    private int menuSort;

    //类型 1.菜单 2.图标
    private int type;

    //子菜单排序
    private int subMenuSort;

    //跳转类型 1.内连 2.外链
    private int jumpType;

    //弹框类型 1.任务 2.转盘 3.充值 4.客服
    private int popupLinks;

    //内部链接
    private String innerLinks;

    //外链接
    private String externalLinks;

    //图片信息
    private List<String> iconData;

    //菜单名字
    private List<String> menuNameData;

    //描述
    private String desc;

    private transient final List<IconInfo> iconList = new ArrayList<>();

    private transient final Map<Integer, MenuNameInfo> menuNameInfoMap = new LinkedHashMap<>();

    @Override
    public boolean check() throws Exception {
        if (this.iconData != null && !this.iconData.isEmpty()) {
            for (String data : this.iconData) {
                final IconInfo iconInfo = JsonUtils.readFromJson(data, IconInfo.class);
                iconList.add(iconInfo);
            }
        }

        if (this.menuNameData != null && !this.menuNameData.isEmpty()) {
            for (String data : this.menuNameData) {
                final MenuNameInfo menuNameInfo = JsonUtils.readFromJson(data, MenuNameInfo.class);
                menuNameInfoMap.put(menuNameInfo.getLanguage(), menuNameInfo);
            }
        }
        return true;
    }

    public String getBusiness_no() {
        return business_no;
    }

    public void setBusiness_no(String business_no) {
        this.business_no = business_no;
    }

    public int getMenuId() {
        return menuId;
    }

    public void setMenuId(int menuId) {
        this.menuId = menuId;
    }

    public int getMenuSort() {
        return menuSort;
    }

    public void setMenuSort(int menuSort) {
        this.menuSort = menuSort;
    }

    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }

    public int getSubMenuSort() {
        return subMenuSort;
    }

    public void setSubMenuSort(int subMenuSort) {
        this.subMenuSort = subMenuSort;
    }

    public int getJumpType() {
        return jumpType;
    }

    public void setJumpType(int jumpType) {
        this.jumpType = jumpType;
    }

    public int getPopupLinks() {
        return popupLinks;
    }

    public void setPopupLinks(int popupLinks) {
        this.popupLinks = popupLinks;
    }

    public String getInnerLinks() {
        return StringUtil.isNullOrEmpty(innerLinks) ? "" : innerLinks;
    }

    public void setInnerLinks(String innerLinks) {
        this.innerLinks = innerLinks;
    }

    public String getExternalLinks() {
        return StringUtil.isNullOrEmpty(externalLinks) ? "" : externalLinks;
    }

    public void setExternalLinks(String externalLinks) {
        this.externalLinks = externalLinks;
    }

    public List<String> getIconData() {
        return iconData;
    }

    public void setIconData(List<String> iconData) {
        this.iconData = iconData;
    }

    public String getDesc() {
        return StringUtil.isNullOrEmpty(desc) ? "" : desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public List<IconInfo> getIconList() {
        return iconList;
    }

    public List<String> getMenuNameData() {
        return menuNameData;
    }

    public void setMenuNameData(List<String> menuNameData) {
        this.menuNameData = menuNameData;
    }

    public Map<Integer, MenuNameInfo> getMenuNameInfoMap() {
        return menuNameInfoMap;
    }

    public static class MenuNameInfo {
        public int language;

        //菜单名字
        private String menuName;

        //子菜单名字
        private String subMenuName;

        public int getLanguage() {
            return language;
        }

        public void setLanguage(int language) {
            this.language = language;
        }

        public String getMenuName() {
            return StringUtil.isNullOrEmpty(menuName) ? "" : menuName;
        }

        public void setMenuName(String menuName) {
            this.menuName = menuName;
        }

        public String getSubMenuName() {
            return StringUtil.isNullOrEmpty(subMenuName) ? "" : subMenuName;
        }

        public void setSubMenuName(String subMenuName) {
            this.subMenuName = subMenuName;
        }
    }

    public static class IconInfo {
        private int id;

        public String icon;

        //跳转类型 1.内连 2.外链
        public int jumpType;

        //弹框类型 1.任务 2.转盘 3.充值 4.客服
        public int popupLinks;

        //内部链接
        public String innerLinks;

        //外链接
        public String externalLinks;

        public int getId() {
            return id;
        }

        public void setId(int id) {
            this.id = id;
        }

        public String getIcon() {
            return icon;
        }

        public void setIcon(String icon) {
            this.icon = icon;
        }

        public int getJumpType() {
            return jumpType;
        }

        public void setJumpType(int jumpType) {
            this.jumpType = jumpType;
        }

        public int getPopupLinks() {
            return popupLinks;
        }

        public void setPopupLinks(int popupLinks) {
            this.popupLinks = popupLinks;
        }

        public String getInnerLinks() {
            return StringUtil.isNullOrEmpty(innerLinks) ? "" : innerLinks;
        }

        public void setInnerLinks(String innerLinks) {
            this.innerLinks = innerLinks;
        }

        public String getExternalLinks() {
            return StringUtil.isNullOrEmpty(externalLinks) ? "" : externalLinks;
        }

        public void setExternalLinks(String externalLinks) {
            this.externalLinks = externalLinks;
        }
    }
}
