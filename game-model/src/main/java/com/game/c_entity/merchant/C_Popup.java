package com.game.c_entity.merchant;

import com.game.engine.utils.JsonUtils;
import com.game.po.data.IDataChecker;
import io.netty.util.internal.StringUtil;
import org.bson.types.ObjectId;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.*;

@Document(collection = "c_popup")
public class C_Popup implements IDataChecker {
    @Id
    private ObjectId _id;

    //商户号
    private String business_no;

    private int popupId;

    //0.不限制 1.渠道可见 2.渠道不可见
    private int channelLimit;

    //0.不限制 1.代理可见 2.代理不可见
    private int agentLimit;

    private List<Integer> channelType = new ArrayList<>();

    private List<Integer> channels = new ArrayList<>();

    private List<Integer> agentIds = new ArrayList<>();

    //标题
    private String title;

    //弹窗名字
    private String popupName;

    //弹窗类型 1.功能弹窗 2.配置弹窗
    private int popupType;

    //系统弹窗 1.任务 2.转盘 3.充值 4.客服
    private int systemPopup;

    //语言
    private int language;

    //排序
    private int sort;

    //弹窗触发类型 1.注册成功 2.游客访问 3.每日首次登录 4.余额不足 5.提现成功 6.充值成功
    private int triggerType;

    //余额不足
    private int currencyId;
    private double insufficientBalance;

    private int firstRechargeCurrencyId;
    private double firstRechargeAmount;

    //充值弹窗 1.首充未完成 2.二充未完成 3.三充未完成 4.其它
    private int rechargePopup;

    private int rechargeTimeType;
    //充值次数
    private int rechargeTimes;

    private int rechargeAmountType;
    //充值金额
    private double rechargeAmount;

    //按钮
    private boolean button;

    //按钮文字
    private String buttonWord;

    //出现方式 1.逐渐 2.向左滑动 3.从小变大
    private int appearance;

    //自动消失
    private boolean autoPassOff;

    //显示时间 （秒）
    private int showTime;

    //弹窗方式 1.居中公告弹窗 2.右下角小弹窗 3.右中引流弹窗
    private int popupWay;

    //弹窗平台 1.pc 2.app
    private List<Integer> popupPlatform = new ArrayList<>();

    //内容
    private String content;

    //开始时间
    private long startTime;

    //结束时间
    private long endTime;

    //更新时间
    private long updateTime;

    //状态 0.关 1.开
    private boolean status;

    //是否显示提示
    private boolean showHint;

    //弹框数据
    private List<String> popupDatas;

    private transient final Map<Integer, PopupData> popupDataMap = new LinkedHashMap<>();

    @Override
    public boolean check() throws Exception {
        if (popupDatas != null && !popupDatas.isEmpty()) {
            for (String data : popupDatas) {
                final PopupData popupData = JsonUtils.readFromJson(data, PopupData.class);
                popupDataMap.put(popupData.getId(), popupData);
            }
        }
        return true;
    }

    public String getBusiness_no() {
        return business_no;
    }

    public void setBusiness_no(String business_no) {
        this.business_no = business_no;
    }

    public int getPopupId() {
        return popupId;
    }

    public void setPopupId(int popupId) {
        this.popupId = popupId;
    }

    public int getChannelLimit() {
        return channelLimit;
    }

    public void setChannelLimit(int channelLimit) {
        this.channelLimit = channelLimit;
    }

    public int getAgentLimit() {
        return agentLimit;
    }

    public void setAgentLimit(int agentLimit) {
        this.agentLimit = agentLimit;
    }

    public List<Integer> getChannelType() {
        return channelType;
    }

    public void setChannelType(List<Integer> channelType) {
        this.channelType = channelType;
    }

    public List<Integer> getChannels() {
        return channels;
    }

    public void setChannels(List<Integer> channels) {
        this.channels = channels;
    }

    public List<Integer> getAgentIds() {
        return agentIds;
    }

    public void setAgentIds(List<Integer> agentIds) {
        this.agentIds = agentIds;
    }

    public String getTitle() {
        return StringUtil.isNullOrEmpty(title) ? "" : title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getPopupName() {
        return StringUtil.isNullOrEmpty(popupName) ? "" : popupName;
    }

    public void setPopupName(String popupName) {
        this.popupName = popupName;
    }

    public int getPopupType() {
        return popupType;
    }

    public void setPopupType(int popupType) {
        this.popupType = popupType;
    }

    public int getSystemPopup() {
        return systemPopup;
    }

    public void setSystemPopup(int systemPopup) {
        this.systemPopup = systemPopup;
    }

    public int getLanguage() {
        return language;
    }

    public void setLanguage(int language) {
        this.language = language;
    }

    public int getSort() {
        return sort;
    }

    public void setSort(int sort) {
        this.sort = sort;
    }

    public int getTriggerType() {
        return triggerType;
    }

    public void setTriggerType(int triggerType) {
        this.triggerType = triggerType;
    }

    public int getCurrencyId() {
        return currencyId;
    }

    public void setCurrencyId(int currencyId) {
        this.currencyId = currencyId;
    }

    public double getInsufficientBalance() {
        return insufficientBalance;
    }

    public void setInsufficientBalance(double insufficientBalance) {
        this.insufficientBalance = insufficientBalance;
    }

    public int getFirstRechargeCurrencyId() {
        return firstRechargeCurrencyId;
    }

    public void setFirstRechargeCurrencyId(int firstRechargeCurrencyId) {
        this.firstRechargeCurrencyId = firstRechargeCurrencyId;
    }

    public double getFirstRechargeAmount() {
        return firstRechargeAmount;
    }

    public void setFirstRechargeAmount(double firstRechargeAmount) {
        this.firstRechargeAmount = firstRechargeAmount;
    }

    public int getRechargePopup() {
        return rechargePopup;
    }

    public void setRechargePopup(int rechargePopup) {
        this.rechargePopup = rechargePopup;
    }

    public int getRechargeTimeType() {
        return rechargeTimeType;
    }

    public void setRechargeTimeType(int rechargeTimeType) {
        this.rechargeTimeType = rechargeTimeType;
    }

    public int getRechargeTimes() {
        return rechargeTimes;
    }

    public void setRechargeTimes(int rechargeTimes) {
        this.rechargeTimes = rechargeTimes;
    }

    public int getRechargeAmountType() {
        return rechargeAmountType;
    }

    public void setRechargeAmountType(int rechargeAmountType) {
        this.rechargeAmountType = rechargeAmountType;
    }

    public double getRechargeAmount() {
        return rechargeAmount;
    }

    public void setRechargeAmount(double rechargeAmount) {
        this.rechargeAmount = rechargeAmount;
    }

    public boolean isButton() {
        return button;
    }

    public void setButton(boolean button) {
        this.button = button;
    }

    public String getButtonWord() {
        return StringUtil.isNullOrEmpty(buttonWord) ? "" : buttonWord;
    }

    public void setButtonWord(String buttonWord) {
        this.buttonWord = buttonWord;
    }


    public int getAppearance() {
        return appearance;
    }

    public void setAppearance(int appearance) {
        this.appearance = appearance;
    }

    public boolean isAutoPassOff() {
        return autoPassOff;
    }

    public void setAutoPassOff(boolean autoPassOff) {
        this.autoPassOff = autoPassOff;
    }

    public int getShowTime() {
        return showTime;
    }

    public void setShowTime(int showTime) {
        this.showTime = showTime;
    }

    public int getPopupWay() {
        return popupWay;
    }

    public void setPopupWay(int popupWay) {
        this.popupWay = popupWay;
    }

    public List<Integer> getPopupPlatform() {
        return popupPlatform;
    }

    public void setPopupPlatform(List<Integer> popupPlatform) {
        this.popupPlatform = popupPlatform;
    }

    public String getContent() {
        return StringUtil.isNullOrEmpty(content) ? "" : content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public long getStartTime() {
        return startTime;
    }

    public void setStartTime(long startTime) {
        this.startTime = startTime;
    }

    public long getEndTime() {
        return endTime;
    }

    public void setEndTime(long endTime) {
        this.endTime = endTime;
    }

    public long getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(long updateTime) {
        this.updateTime = updateTime;
    }

    public boolean isStatus() {
        return status;
    }

    public void setStatus(boolean status) {
        this.status = status;
    }

    public boolean isShowHint() {
        return showHint;
    }

    public void setShowHint(boolean showHint) {
        this.showHint = showHint;
    }

    public List<String> getPopupDatas() {
        return popupDatas;
    }

    public void setPopupDatas(List<String> popupDatas) {
        this.popupDatas = popupDatas;
    }

    public Map<Integer, PopupData> getPopupDataMap() {
        return popupDataMap;
    }

    public static class PopupData {
        private int id;

        private String imageUrl;

        private int isJump;

        //跳转类型 1.内连 2.外链
        private int jumpType;

        //弹框类型 1.任务 2.转盘 3.充值 4.客服
        private int popupLinks;

        //内部链接
        private String innerLinks;

        //外链接
        private String externalLinks;

        //未登录是否可以跳转
        private boolean notLoginJump;

        private int firstRechargeCurrencyId;
        private double firstRechargeAmount;

        public int getId() {
            return id;
        }

        public void setId(int id) {
            this.id = id;
        }

        public String getImageUrl() {
            return imageUrl;
        }

        public void setImageUrl(String imageUrl) {
            this.imageUrl = imageUrl;
        }

        public int getIsJump() {
            return isJump;
        }

        public void setIsJump(int isJump) {
            this.isJump = isJump;
        }

        public int getJumpType() {
            return jumpType;
        }

        public void setJumpType(int jumpType) {
            this.jumpType = jumpType;
        }

        public int getPopupLinks() {
            return popupLinks;
        }

        public void setPopupLinks(int popupLinks) {
            this.popupLinks = popupLinks;
        }

        public String getInnerLinks() {
            return StringUtil.isNullOrEmpty(innerLinks) ? "" : innerLinks;
        }

        public void setInnerLinks(String innerLinks) {
            this.innerLinks = innerLinks;
        }

        public String getExternalLinks() {
            return StringUtil.isNullOrEmpty(externalLinks) ? "" : externalLinks;
        }

        public void setExternalLinks(String externalLinks) {
            this.externalLinks = externalLinks;
        }

        public boolean isNotLoginJump() {
            return notLoginJump;
        }

        public void setNotLoginJump(boolean notLoginJump) {
            this.notLoginJump = notLoginJump;
        }

        public int getFirstRechargeCurrencyId() {
            return firstRechargeCurrencyId;
        }

        public void setFirstRechargeCurrencyId(int firstRechargeCurrencyId) {
            this.firstRechargeCurrencyId = firstRechargeCurrencyId;
        }

        public double getFirstRechargeAmount() {
            return firstRechargeAmount;
        }

        public void setFirstRechargeAmount(double firstRechargeAmount) {
            this.firstRechargeAmount = firstRechargeAmount;
        }
    }

    public boolean channelLimit(int channelId) {
        if (this.channelLimit == 0) {
            return false;
        }

        if (this.channelLimit == 1) {//可见
            if (channelId == 0) {
                return true;
            }
            final int type = Character.getNumericValue(String.valueOf(channelId).charAt(1));
            if (this.channelType != null && this.channelType.contains(type)) {
                if (this.channels.contains(channelId)) {
                    return false;
                } else {
                    return true;
                }
            } else {
                return true;
            }
        }

        if (this.channelLimit == 2) {//不可见
            if (channelId == 0) {
                return false;
            }
            final int type = Character.getNumericValue(String.valueOf(channelId).charAt(1));
            if (this.channelType != null && this.channelType.contains(type)) {
                if (this.channels.contains(channelId)) {
                    return true;
                } else {
                    return false;
                }
            } else {
                return false;
            }
        }

        return true;
    }

    public boolean agentLimit(int agentId) {
        if (this.agentLimit == 0) {
            return false;
        }

        if (this.agentLimit == 1) {//可见
            if (agentId == 0) {
                return true;
            }
            if (this.agentIds.contains(agentId)) {
                return false;
            } else {
                return true;
            }
        }

        if (this.agentLimit == 2) {//不可见
            if (agentId == 0) {
                return false;
            }
            if (this.agentIds.contains(agentId)) {
                return true;
            } else {
                return false;
            }
        }
        return true;
    }
}
