package com.game.c_entity.merchant;

import com.alibaba.fastjson.JSON;
import com.game.po.data.IDataChecker;
import io.netty.util.internal.StringUtil;
import org.bson.types.ObjectId;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;


@Document(collection = "c_firstChargeSignIn")
public class C_FirstChargeSignIn implements IDataChecker {

    @Id
    private ObjectId _id;

    private int c_id;

    //商户
    private String business_no;

    private int activityId;

    private String activityName;

    private List<Integer> languages = new ArrayList<>();

    //时区
    private String timeZone;

    //奖励币种
    private int rewardCurrencyId;

    //充值
    private int rechargeCurrencyId;

    //充值金额
    private double rechargeAmount;

    //打码倍数
    private int turnoverMul;

    private boolean open;

    //重置天数
    private int resetDay;

    private List<String> firstChargeSignInData = new ArrayList<>();

    //规则说明
    private List<String> ruleData = new ArrayList<>();

    private transient final Map<Integer, Rule> ruleMap = new LinkedHashMap<>();

    private transient final Map<Integer, FirstChargeReward> firstChargeSignInMap = new LinkedHashMap<>();

    @Override
    public boolean check() throws Exception {
        if (this.ruleData != null && !this.ruleData.isEmpty()) {
            for (String data : this.ruleData) {
                final Rule rule = JSON.parseObject(data, Rule.class);
                ruleMap.put(rule.language, rule);
            }
        }

        if (this.firstChargeSignInData != null && !this.firstChargeSignInData.isEmpty()) {
            for (String data : this.firstChargeSignInData) {
                final FirstChargeReward firstChargeSignIn = JSON.parseObject(data, FirstChargeReward.class);
                firstChargeSignInMap.put(firstChargeSignIn.getId(), firstChargeSignIn);
            }
        }

        return true;
    }

    public String getBusiness_no() {
        return business_no;
    }

    public void setBusiness_no(String business_no) {
        this.business_no = business_no;
    }

    public int getC_id() {
        return c_id;
    }

    public void setC_id(int c_id) {
        this.c_id = c_id;
    }

    public int getActivityId() {
        return activityId;
    }

    public void setActivityId(int activityId) {
        this.activityId = activityId;
    }

    public String getActivityName() {
        return activityName;
    }

    public void setActivityName(String activityName) {
        this.activityName = activityName;
    }

    public List<Integer> getLanguages() {
        return languages;
    }

    public void setLanguages(List<Integer> languages) {
        this.languages = languages;
    }

    public String getTimeZone() {
        return timeZone;
    }

    public void setTimeZone(String timeZone) {
        this.timeZone = timeZone;
    }

    public int getRewardCurrencyId() {
        return rewardCurrencyId;
    }

    public void setRewardCurrencyId(int rewardCurrencyId) {
        this.rewardCurrencyId = rewardCurrencyId;
    }

    public int getRechargeCurrencyId() {
        return rechargeCurrencyId;
    }

    public void setRechargeCurrencyId(int rechargeCurrencyId) {
        this.rechargeCurrencyId = rechargeCurrencyId;
    }

    public double getRechargeAmount() {
        return rechargeAmount;
    }

    public void setRechargeAmount(double rechargeAmount) {
        this.rechargeAmount = rechargeAmount;
    }

    public int getTurnoverMul() {
        return turnoverMul;
    }

    public void setTurnoverMul(int turnoverMul) {
        this.turnoverMul = turnoverMul;
    }

    public boolean isOpen() {
        return open;
    }

    public void setOpen(boolean open) {
        this.open = open;
    }

    public int getResetDay() {
        return resetDay;
    }

    public void setResetDay(int resetDay) {
        this.resetDay = resetDay;
    }

    public List<String> getFirstChargeSignInData() {
        return firstChargeSignInData;
    }

    public void setFirstChargeSignInData(List<String> firstChargeSignInData) {
        this.firstChargeSignInData = firstChargeSignInData;
    }

    public List<String> getRuleData() {
        return ruleData;
    }

    public void setRuleData(List<String> ruleData) {
        this.ruleData = ruleData;
    }

    public Map<Integer, FirstChargeReward> getFirstChargeSignInMap() {
        return firstChargeSignInMap;
    }

    public Map<Integer, Rule> getRuleMap() {
        return ruleMap;
    }

    public static class FirstChargeReward {
        public int id;
        public int currencyId;
        public double bonus;

        public int getId() {
            return id;
        }

        public void setId(int id) {
            this.id = id;
        }

        public int getCurrencyId() {
            return currencyId;
        }

        public void setCurrencyId(int currencyId) {
            this.currencyId = currencyId;
        }

        public double getBonus() {
            return bonus;
        }

        public void setBonus(double bonus) {
            this.bonus = bonus;
        }
    }

    public static class Rule {
        public int language;
        public String icon;
        public String rule;

        public int getLanguage() {
            return language;
        }

        public void setLanguage(int language) {
            this.language = language;
        }

        public String getIcon() {
            return StringUtil.isNullOrEmpty(icon) ? "" : icon;
        }

        public void setIcon(String icon) {
            this.icon = icon;
        }

        public String getRule() {
            return StringUtil.isNullOrEmpty(rule) ? "" : rule;
        }

        public void setRule(String rule) {
            this.rule = rule;
        }
    }
}
