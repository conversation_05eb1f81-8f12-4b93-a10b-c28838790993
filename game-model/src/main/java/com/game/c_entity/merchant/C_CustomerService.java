package com.game.c_entity.merchant;

import com.game.po.data.IDataChecker;
import io.netty.util.internal.StringUtil;
import org.bson.types.ObjectId;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

@Document(collection = "c_customerService")
public class C_CustomerService implements IDataChecker {

    @Id
    private ObjectId _id;

    private int language;

    private int customerServiceId;

    private String name;

    private String icon;

    //媒体
    private String mediaName;

    //联系方式
    private String contactDetails;

    //链接
    private String links;

    //显示顺序
    private int showSort;

    private boolean open;

    @Override
    public boolean check() throws Exception {
        return true;
    }

    public int getLanguage() {
        return language;
    }

    public void setLanguage(int language) {
        this.language = language;
    }

    public int getCustomerServiceId() {
        return customerServiceId;
    }

    public void setCustomerServiceId(int customerServiceId) {
        this.customerServiceId = customerServiceId;
    }

    public String getName() {
        return StringUtil.isNullOrEmpty(name) ? "" : name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getIcon() {
        return StringUtil.isNullOrEmpty(icon) ? "" : icon;
    }

    public void setIcon(String icon) {
        this.icon = icon;
    }

    public String getLinks() {
        return StringUtil.isNullOrEmpty(links) ? "" : links;
    }

    public void setLinks(String links) {
        this.links = links;
    }

    public String getContactDetails() {
        return StringUtil.isNullOrEmpty(contactDetails) ? "" : contactDetails;
    }

    public void setContactDetails(String contactDetails) {
        this.contactDetails = contactDetails;
    }

    public String getMediaName() {
        return StringUtil.isNullOrEmpty(mediaName) ? "" : mediaName;
    }

    public void setMediaName(String mediaName) {
        this.mediaName = mediaName;
    }

    public int getShowSort() {
        return showSort;
    }

    public void setShowSort(int showSort) {
        this.showSort = showSort;
    }

    public boolean isOpen() {
        return open;
    }

    public void setOpen(boolean open) {
        this.open = open;
    }
}
