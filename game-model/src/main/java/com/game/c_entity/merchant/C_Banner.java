package com.game.c_entity.merchant;

import com.game.po.data.IDataChecker;
import io.netty.util.internal.StringUtil;
import org.bson.types.ObjectId;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.ArrayList;
import java.util.List;

@Document(collection = "c_banner")
public class C_Banner implements IDataChecker {
    @Id
    private ObjectId _id;

    //站点
    private List<String> webSites;

    //商户号
    private String business_no;

    private int bannerId;

    //0.不限制 1.渠道可见 2.渠道不可见
    private int channelLimit;

    //0.不限制 1.代理可见 2.代理不可见
    private int agentLimit;

    private List<Integer> channelType = new ArrayList<>();

    private List<Integer> channels = new ArrayList<>();

    private List<Integer> agentIds = new ArrayList<>();

    //名称
    private String bannerName;

    //位置 1.顶部 2.中部 3.底部
    private int index;

    //语言
    private int language;

    //排序
    private int showSeq;

    //1-有 0-无
    private int isJump;

    //未登录是否可以跳转
    private boolean notLoginJump;

    //跳转类型 1.内连 2.外链
    private int jumpType;

    //弹框类型 1.任务 2.转盘 3.充值 4.客服 5.登录 6.注册
    private int popupLinks;

    //内部链接
    private String innerLinks;

    //外链接
    private String externalLinks;

    //标题
    private String title;

    //副标题
    private String subtitle;

    //文件地址
    private String fileUrl;

    //按钮 1.有 0.无
    private int button;

    //按钮文字
    private String buttonWord;

    //开始时间
    private long startTime;

    //结束时间
    private long endTime;

    //状态 1.开 0.关
    private int status;

    private int firstRechargeCurrencyId;

    private double firstRechargeAmount;

    @Override
    public boolean check() throws Exception {
        return true;
    }

    public List<String> getWebSites() {
        return webSites;
    }

    public void setWebSites(List<String> webSites) {
        this.webSites = webSites;
    }

    public String getBusiness_no() {
        return business_no;
    }

    public void setBusiness_no(String business_no) {
        this.business_no = business_no;
    }

    public int getBannerId() {
        return bannerId;
    }

    public void setBannerId(int bannerId) {
        this.bannerId = bannerId;
    }

    public int getChannelLimit() {
        return channelLimit;
    }

    public void setChannelLimit(int channelLimit) {
        this.channelLimit = channelLimit;
    }

    public List<Integer> getChannelType() {
        return channelType;
    }

    public void setChannelType(List<Integer> channelType) {
        this.channelType = channelType;
    }

    public List<Integer> getChannels() {
        return channels;
    }

    public void setChannels(List<Integer> channels) {
        this.channels = channels;
    }

    public List<Integer> getAgentIds() {
        return agentIds;
    }

    public void setAgentIds(List<Integer> agentIds) {
        this.agentIds = agentIds;
    }

    public int getAgentLimit() {
        return agentLimit;
    }

    public void setAgentLimit(int agentLimit) {
        this.agentLimit = agentLimit;
    }

    public String getBannerName() {
        return StringUtil.isNullOrEmpty(bannerName) ? "" : bannerName;
    }

    public void setBannerName(String bannerName) {
        this.bannerName = bannerName;
    }

    public int getIndex() {
        return index;
    }

    public void setIndex(int index) {
        this.index = index;
    }

    public int getLanguage() {
        return language;
    }

    public void setLanguage(int language) {
        this.language = language;
    }

    public int getShowSeq() {
        return showSeq;
    }

    public void setShowSeq(int showSeq) {
        this.showSeq = showSeq;
    }

    public int getIsJump() {
        return isJump;
    }

    public void setIsJump(int isJump) {
        this.isJump = isJump;
    }

    public boolean isNotLoginJump() {
        return notLoginJump;
    }

    public void setNotLoginJump(boolean notLoginJump) {
        this.notLoginJump = notLoginJump;
    }

    public int getJumpType() {
        return jumpType;
    }

    public void setJumpType(int jumpType) {
        this.jumpType = jumpType;
    }

    public int getPopupLinks() {
        return popupLinks;
    }

    public void setPopupLinks(int popupLinks) {
        this.popupLinks = popupLinks;
    }

    public String getInnerLinks() {
        return StringUtil.isNullOrEmpty(innerLinks) ? "" : innerLinks;
    }

    public void setInnerLinks(String innerLinks) {
        this.innerLinks = innerLinks;
    }

    public String getExternalLinks() {
        return StringUtil.isNullOrEmpty(externalLinks) ? "" : externalLinks;
    }

    public void setExternalLinks(String externalLinks) {
        this.externalLinks = externalLinks;
    }

    public String getTitle() {
        return StringUtil.isNullOrEmpty(title) ? "" : title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getSubtitle() {
        return StringUtil.isNullOrEmpty(subtitle) ? "" : subtitle;
    }

    public void setSubtitle(String subtitle) {
        this.subtitle = subtitle;
    }

    public String getFileUrl() {
        return StringUtil.isNullOrEmpty(fileUrl) ? "" : fileUrl;
    }

    public void setFileUrl(String fileUrl) {
        this.fileUrl = fileUrl;
    }

    public int getButton() {
        return button;
    }

    public void setButton(int button) {
        this.button = button;
    }

    public String getButtonWord() {
        return StringUtil.isNullOrEmpty(buttonWord) ? "" : buttonWord;
    }

    public void setButtonWord(String buttonWord) {
        this.buttonWord = buttonWord;
    }

    public long getStartTime() {
        return startTime;
    }

    public void setStartTime(long startTime) {
        this.startTime = startTime;
    }

    public long getEndTime() {
        return endTime;
    }

    public void setEndTime(long endTime) {
        this.endTime = endTime;
    }

    public int getStatus() {
        return status;
    }

    public void setStatus(int status) {
        this.status = status;
    }

    public int getFirstRechargeCurrencyId() {
        return firstRechargeCurrencyId;
    }

    public void setFirstRechargeCurrencyId(int firstRechargeCurrencyId) {
        this.firstRechargeCurrencyId = firstRechargeCurrencyId;
    }

    public double getFirstRechargeAmount() {
        return firstRechargeAmount;
    }

    public void setFirstRechargeAmount(double firstRechargeAmount) {
        this.firstRechargeAmount = firstRechargeAmount;
    }

    public boolean channelLimit(int channelId) {
        if (this.channelLimit == 0) {
            return false;
        }

        if (this.channelLimit == 1) {//可见
            if (channelId == 0) {
                return true;
            }
            final int type = Character.getNumericValue(String.valueOf(channelId).charAt(1));
            if (this.channelType != null && this.channelType.contains(type)) {
                if (this.channels.contains(channelId)) {
                    return false;
                } else {
                    return true;
                }
            } else {
                return true;
            }
        }

        if (this.channelLimit == 2) {//不可见
            if (channelId == 0) {
                return false;
            }
            final int type = Character.getNumericValue(String.valueOf(channelId).charAt(1));
            if (this.channelType != null && this.channelType.contains(type)) {
                if (this.channels.contains(channelId)) {
                    return true;
                } else {
                    return false;
                }
            } else {
                return false;
            }
        }

        return true;
    }

    public boolean agentLimit(int agentId) {
        if (this.agentLimit == 0) {
            return false;
        }

        if (this.agentLimit == 1) {//可见
            if (agentId == 0) {
                return true;
            }
            if (this.agentIds.contains(agentId)) {
                return false;
            } else {
                return true;
            }
        }

        if (this.agentLimit == 2) {//不可见
            if (agentId == 0) {
                return false;
            }
            if (this.agentIds.contains(agentId)) {
                return true;
            } else {
                return false;
            }
        }
        return true;
    }
}
