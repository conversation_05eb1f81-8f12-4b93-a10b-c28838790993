package com.game.c_entity.merchant;

import com.game.po.data.IDataChecker;
import io.netty.util.internal.StringUtil;
import org.bson.types.ObjectId;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

@Document(collection = "c_gamePlatform")
public class C_GamePlatform implements IDataChecker {

    @Id
    private ObjectId _id;

    //商户
    private String business_no;

    //游戏供应商id
    private int supplierId;

    //游戏供应商
    private String supplierName;

    //平台id
    private int platformId;

    //游戏平台名字
    private String platformName;

    //游戏类型
    private int type;

    //地区
    private List<String> region = new ArrayList<>();

    //地区限制
    private List<String> regionLimit = new ArrayList<>();

    //支持货币
    private List<Integer> supportCurrency = new ArrayList<>();

    //语言
    private List<Integer> language = new ArrayList<>();

    //图片地址
    private String fileUrl;

    //状态 1.正常 2.关闭 3.维护
    private int status;

    //自动维护时间 1-2,08:00-12:00
    private String autoMaintenanceTime;

    //维护开始时间
    private long maintenanceStartTime;

    //维护结束时间
    private long maintenanceEndTime;

    //商户号
    private String agent;

    //api地址
    private String apiUrl;

    private String token;

    //密钥
    private String secretKey;

    private long deleteTime;

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        C_GamePlatform that = (C_GamePlatform) o;
        return platformId == that.platformId;
    }

    @Override
    public int hashCode() {
        return Objects.hash(platformId);
    }

    @Override
    public boolean check() throws Exception {
        return true;
    }

    public boolean regionLimit(String region) {
        if (this.region != null && !this.region.isEmpty()) {
            return !this.region.contains(region);
        }

        if (this.regionLimit != null && !this.regionLimit.isEmpty()) {
            return this.regionLimit.contains(region);
        }
        return false;
    }

    public String getBusiness_no() {
        return business_no;
    }

    public void setBusiness_no(String business_no) {
        this.business_no = business_no;
    }

    public int getPlatformId() {
        return platformId;
    }

    public void setPlatformId(int platformId) {
        this.platformId = platformId;
    }

    public String getPlatformName() {
        return StringUtil.isNullOrEmpty(platformName) ? "" : platformName;
    }

    public void setPlatformName(String platformName) {
        this.platformName = platformName;
    }

    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }

    public int getSupplierId() {
        return supplierId;
    }

    public void setSupplierId(int supplierId) {
        this.supplierId = supplierId;
    }

    public String getSupplierName() {
        return StringUtil.isNullOrEmpty(supplierName) ? "" : supplierName;
    }

    public void setSupplierName(String supplierName) {
        this.supplierName = supplierName;
    }

    public List<String> getRegion() {
        return region;
    }

    public void setRegion(List<String> region) {
        this.region = region;
    }

    public List<String> getRegionLimit() {
        return regionLimit;
    }

    public void setRegionLimit(List<String> regionLimit) {
        this.regionLimit = regionLimit;
    }

    public List<Integer> getSupportCurrency() {
        return supportCurrency;
    }

    public void setSupportCurrency(List<Integer> supportCurrency) {
        this.supportCurrency = supportCurrency;
    }

    public List<Integer> getLanguage() {
        return language;
    }

    public void setLanguage(List<Integer> language) {
        this.language = language;
    }

    public String getFileUrl() {
        return StringUtil.isNullOrEmpty(fileUrl) ? "" : fileUrl;
    }

    public void setFileUrl(String fileUrl) {
        this.fileUrl = fileUrl;
    }

    public int getStatus() {
        return status;
    }

    public void setStatus(int status) {
        this.status = status;
    }

    public String getAutoMaintenanceTime() {
        return StringUtil.isNullOrEmpty(autoMaintenanceTime) ? "" : autoMaintenanceTime;
    }

    public void setAutoMaintenanceTime(String autoMaintenanceTime) {
        this.autoMaintenanceTime = autoMaintenanceTime;
    }

    public long getMaintenanceStartTime() {
        return maintenanceStartTime;
    }

    public void setMaintenanceStartTime(long maintenanceStartTime) {
        this.maintenanceStartTime = maintenanceStartTime;
    }

    public long getMaintenanceEndTime() {
        return maintenanceEndTime;
    }

    public void setMaintenanceEndTime(long maintenanceEndTime) {
        this.maintenanceEndTime = maintenanceEndTime;
    }

    public String getAgent() {
        return agent;
    }

    public void setAgent(String agent) {
        this.agent = agent;
    }

    public String getApiUrl() {
        return apiUrl;
    }

    public void setApiUrl(String apiUrl) {
        this.apiUrl = apiUrl;
    }

    public String getToken() {
        return token;
    }

    public void setToken(String token) {
        this.token = token;
    }

    public String getSecretKey() {
        return secretKey;
    }

    public void setSecretKey(String secretKey) {
        this.secretKey = secretKey;
    }

    public long getDeleteTime() {
        return deleteTime;
    }

    public void setDeleteTime(long deleteTime) {
        this.deleteTime = deleteTime;
    }
}
