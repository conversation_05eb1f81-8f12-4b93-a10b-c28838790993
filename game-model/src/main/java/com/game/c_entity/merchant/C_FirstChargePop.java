package com.game.c_entity.merchant;

import com.game.engine.utils.JsonUtils;
import com.game.po.data.IDataChecker;
import io.netty.util.internal.StringUtil;
import org.bson.types.ObjectId;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

@Document(collection = "c_firstChargePop")
public class C_FirstChargePop implements IDataChecker {

    @Id
    private ObjectId _id;

    //商户
    private String business_no;

    private int c_id;

    private int currencyId;

    private double amount;

    private double giveawayAmount;

    private String button;

    private String button1;

    private List<String> descData = new ArrayList<>();

    private transient FirstChargeInfo firstChargeInfo;

    private transient FirstChargeInfo firstChargeInfo1;

    private transient Map<Integer, DescInfo> descInfoMap = new LinkedHashMap<>();

    @Override
    public boolean check() throws Exception {
        if (this.descData != null && !this.descData.isEmpty()) {
            for (String data : this.descData) {
                final DescInfo descInfo = JsonUtils.readFromJson(data, DescInfo.class);
                this.descInfoMap.put(descInfo.language, descInfo);
            }
        }

        if (!StringUtil.isNullOrEmpty(this.button)) {
            firstChargeInfo = JsonUtils.readFromJson(this.button, FirstChargeInfo.class);
        }

        if (!StringUtil.isNullOrEmpty(this.button1)) {
            firstChargeInfo1 = JsonUtils.readFromJson(this.button1, FirstChargeInfo.class);
        }
        return true;
    }

    public String getBusiness_no() {
        return business_no;
    }

    public void setBusiness_no(String business_no) {
        this.business_no = business_no;
    }

    public int getC_id() {
        return c_id;
    }

    public void setC_id(int c_id) {
        this.c_id = c_id;
    }

    public int getCurrencyId() {
        return currencyId;
    }

    public void setCurrencyId(int currencyId) {
        this.currencyId = currencyId;
    }

    public double getAmount() {
        return amount;
    }

    public void setAmount(double amount) {
        this.amount = amount;
    }

    public double getGiveawayAmount() {
        return giveawayAmount;
    }

    public void setGiveawayAmount(double giveawayAmount) {
        this.giveawayAmount = giveawayAmount;
    }

    public Map<Integer, DescInfo> getDescInfoMap() {
        return descInfoMap;
    }

    public void setDescInfoMap(Map<Integer, DescInfo> descInfoMap) {
        this.descInfoMap = descInfoMap;
    }

    public List<String> getDescData() {
        return descData;
    }

    public void setDescData(List<String> descData) {
        this.descData = descData;
    }

    public String getButton() {
        return button;
    }

    public void setButton(String button) {
        this.button = button;
    }

    public String getButton1() {
        return button1;
    }

    public void setButton1(String button1) {
        this.button1 = button1;
    }

    public FirstChargeInfo getFirstChargeInfo() {
        return firstChargeInfo;
    }

    public void setFirstChargeInfo(FirstChargeInfo firstChargeInfo) {
        this.firstChargeInfo = firstChargeInfo;
    }

    public FirstChargeInfo getFirstChargeInfo1() {
        return firstChargeInfo1;
    }

    public void setFirstChargeInfo1(FirstChargeInfo firstChargeInfo1) {
        this.firstChargeInfo1 = firstChargeInfo1;
    }

    public static class DescInfo {
        public int language;

        public String title;

        public String desc;

        public String icon;

        public String button;

        public String button1;

        public int getLanguage() {
            return language;
        }

        public void setLanguage(int language) {
            this.language = language;
        }

        public String getTitle() {
            return StringUtil.isNullOrEmpty(title) ? "" : title;
        }

        public void setTitle(String title) {
            this.title = title;
        }

        public String getDesc() {
            return StringUtil.isNullOrEmpty(desc) ? "" : desc;
        }

        public void setDesc(String desc) {
            this.desc = desc;
        }

        public String getIcon() {
            return StringUtil.isNullOrEmpty(icon) ? "" : icon;
        }

        public void setIcon(String icon) {
            this.icon = icon;
        }

        public String getButton() {
            return StringUtil.isNullOrEmpty(button) ? "" : button;
        }

        public void setButton(String button) {
            this.button = button;
        }

        public String getButton1() {
            return StringUtil.isNullOrEmpty(button1) ? "" : button1;
        }

        public void setButton1(String button1) {
            this.button1 = button1;
        }
    }

    public static class FirstChargeInfo {
        //未登录是否可以跳转
        private boolean notLoginJump;

        //1-有 0-无
        private int isJump;

        //跳转类型 1.内连 2.外链
        private int jumpType;

        //弹框类型 1.任务 2.转盘 3.充值 4.客服 5.登录 6.注册
        private int popupLinks;

        //内部链接
        private String innerLinks;

        //外链接
        private String externalLinks;

        public boolean isNotLoginJump() {
            return notLoginJump;
        }

        public void setNotLoginJump(boolean notLoginJump) {
            this.notLoginJump = notLoginJump;
        }

        public int getJumpType() {
            return jumpType;
        }

        public void setJumpType(int jumpType) {
            this.jumpType = jumpType;
        }

        public int getIsJump() {
            return isJump;
        }

        public void setIsJump(int isJump) {
            this.isJump = isJump;
        }

        public int getPopupLinks() {
            return popupLinks;
        }

        public void setPopupLinks(int popupLinks) {
            this.popupLinks = popupLinks;
        }

        public String getInnerLinks() {
            return innerLinks;
        }

        public void setInnerLinks(String innerLinks) {
            this.innerLinks = innerLinks;
        }

        public String getExternalLinks() {
            return externalLinks;
        }

        public void setExternalLinks(String externalLinks) {
            this.externalLinks = externalLinks;
        }
    }
}
