package com.game.c_entity.merchant;

import com.game.po.data.IDataChecker;
import org.bson.types.ObjectId;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

@Document(collection = "c_gameTurnover")
public class C_GameTurnover implements IDataChecker {

    @Id
    private ObjectId _id;

    //商户
    private String business_no;

    //游戏类型
    private int gameType;

    //返水方式
    private int rebateMethod;

    @Override
    public boolean check() throws Exception {
        return true;
    }

    public String getBusiness_no() {
        return business_no;
    }

    public void setBusiness_no(String business_no) {
        this.business_no = business_no;
    }

    public int getGameType() {
        return gameType;
    }

    public void setGameType(int gameType) {
        this.gameType = gameType;
    }

    public int getRebateMethod() {
        return rebateMethod;
    }

    public void setRebateMethod(int rebateMethod) {
        this.rebateMethod = rebateMethod;
    }
}
