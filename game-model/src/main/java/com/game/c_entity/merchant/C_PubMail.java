package com.game.c_entity.merchant;

import com.game.engine.utils.JsonUtils;
import com.game.po.data.IDataChecker;
import io.netty.util.internal.StringUtil;
import org.bson.types.ObjectId;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

@Document(collection = "c_pubMail")
public class C_PubMail implements IDataChecker {
    @Id
    private ObjectId _id;

    //商户
    @Indexed
    private String business_no;

    //邮件id
    @Indexed
    private int mailId;

    //区域id
    private String regionId;

    //时区
    private String timeZone;

    //发送类型 1.所有 2.指定
    private int sendType;

    //玩家id
    private List<Long> playerId = new ArrayList<>();

    //文件地址
    private String fileUrl;

    private int isJump;

    //跳转类型 1.内连 2.外链
    private int jumpType;

    //弹框类型 1.任务 2.转盘 3.充值 4.客服
    private int popupLinks;

    //内部链接
    private String innerLinks;

    //外链接
    private String externalLinks;

    //1.系统 2.活动 3.公告
    private int mailType;

    //发送条件 1.时间 2.事件 3.立即 4.定时
    private int sendCondition;

    //1.每日 每周
    private int timingType;

    //周列表
    private List<Integer> weeklyList = new ArrayList<>();

    //定时发送时间
    private int timingStart;

    //开始时间
    private long startTime;

    //结束时间
    private long endTime;

    //过期时间
    private long expirationTime;

    //发送事件 1.注册 2.登录
    private int sendEvent;

    //状态 0.关闭 1.开启
    private int status;

    private List<String> pubMailData = new ArrayList<>();

    private long deleteTime;

    private transient final Map<Integer, PubMailInfo> pubMailInfoMap = new LinkedHashMap<>();

    @Override
    public boolean check() throws Exception {
        if (this.pubMailData != null && !this.pubMailData.isEmpty()) {
            for (String data : this.pubMailData) {
                final PubMailInfo pubMailInfo = JsonUtils.readFromJson(data, PubMailInfo.class);
                pubMailInfoMap.put(pubMailInfo.getLanguage(), pubMailInfo);
            }
        }
        return true;
    }

    public String getBusiness_no() {
        return business_no;
    }

    public void setBusiness_no(String business_no) {
        this.business_no = business_no;
    }

    public int getMailId() {
        return mailId;
    }

    public void setMailId(int mailId) {
        this.mailId = mailId;
    }

    public String getRegionId() {
        return StringUtil.isNullOrEmpty(regionId) ? "" : regionId;
    }

    public void setRegionId(String regionId) {
        this.regionId = regionId;
    }

    public String getTimeZone() {
        return StringUtil.isNullOrEmpty(timeZone) ? "" : timeZone;
    }

    public void setTimeZone(String timeZone) {
        this.timeZone = timeZone;
    }

    public int getSendType() {
        return sendType;
    }

    public void setSendType(int sendType) {
        this.sendType = sendType;
    }

    public List<Long> getPlayerId() {
        return playerId;
    }

    public void setPlayerId(List<Long> playerId) {
        this.playerId = playerId;
    }

    public int getJumpType() {
        return jumpType;
    }

    public void setJumpType(int jumpType) {
        this.jumpType = jumpType;
    }

    public int getMailType() {
        return mailType;
    }

    public void setMailType(int mailType) {
        this.mailType = mailType;
    }

    public int getPopupLinks() {
        return popupLinks;
    }

    public void setPopupLinks(int popupLinks) {
        this.popupLinks = popupLinks;
    }

    public String getInnerLinks() {
        return StringUtil.isNullOrEmpty(innerLinks) ? "" : innerLinks;
    }

    public void setInnerLinks(String innerLinks) {
        this.innerLinks = innerLinks;
    }

    public String getExternalLinks() {
        return StringUtil.isNullOrEmpty(externalLinks) ? "" : externalLinks;
    }

    public void setExternalLinks(String externalLinks) {
        this.externalLinks = externalLinks;
    }

    public int getSendCondition() {
        return sendCondition;
    }

    public void setSendCondition(int sendCondition) {
        this.sendCondition = sendCondition;
    }

    public int getTimingType() {
        return timingType;
    }

    public void setTimingType(int timingType) {
        this.timingType = timingType;
    }

    public List<Integer> getWeeklyList() {
        return weeklyList;
    }

    public void setWeeklyList(List<Integer> weeklyList) {
        this.weeklyList = weeklyList;
    }

    public int getTimingStart() {
        return timingStart;
    }

    public void setTimingStart(int timingStart) {
        this.timingStart = timingStart;
    }

    public long getStartTime() {
        return startTime;
    }

    public void setStartTime(long startTime) {
        this.startTime = startTime;
    }

    public long getEndTime() {
        return endTime;
    }

    public void setEndTime(long endTime) {
        this.endTime = endTime;
    }

    public long getExpirationTime() {
        return expirationTime;
    }

    public void setExpirationTime(long expirationTime) {
        this.expirationTime = expirationTime;
    }

    public int getSendEvent() {
        return sendEvent;
    }

    public void setSendEvent(int sendEvent) {
        this.sendEvent = sendEvent;
    }

    public int getStatus() {
        return status;
    }

    public void setStatus(int status) {
        this.status = status;
    }

    public List<String> getPubMailData() {
        return pubMailData;
    }

    public void setPubMailData(List<String> pubMailData) {
        this.pubMailData = pubMailData;
    }

    public long getDeleteTime() {
        return deleteTime;
    }

    public void setDeleteTime(long deleteTime) {
        this.deleteTime = deleteTime;
    }

    public Map<Integer, PubMailInfo> getPubMailInfoMap() {
        return pubMailInfoMap;
    }

    public String getFileUrl() {
        return StringUtil.isNullOrEmpty(fileUrl) ? "" : fileUrl;
    }

    public void setFileUrl(String fileUrl) {
        this.fileUrl = fileUrl;
    }

    public int getIsJump() {
        return isJump;
    }

    public void setIsJump(int isJump) {
        this.isJump = isJump;
    }

    public static class PubMailInfo {
        //语言
        public int language;

        //标题
        private String title;

        //内容
        private String content;

        //图片文字
        private String imageText;

        public int getLanguage() {
            return language;
        }

        public void setLanguage(int language) {
            this.language = language;
        }

        public String getTitle() {
            return StringUtil.isNullOrEmpty(title) ? "" : title;
        }

        public void setTitle(String title) {
            this.title = title;
        }

        public String getContent() {
            return StringUtil.isNullOrEmpty(content) ? "" : content;
        }

        public void setContent(String content) {
            this.content = content;
        }

        public String getImageText() {
            return StringUtil.isNullOrEmpty(imageText) ? "" : imageText;
        }

        public void setImageText(String imageText) {
            this.imageText = imageText;
        }
    }
}
