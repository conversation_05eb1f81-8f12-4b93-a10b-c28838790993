package com.game.c_entity.merchant;

import com.alibaba.fastjson.JSON;
import com.game.po.data.IDataChecker;
import io.netty.util.internal.StringUtil;
import org.bson.types.ObjectId;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.*;

@Document(collection = "c_rechargeRecover")
public class C_RechargeRecover implements IDataChecker {
    @Id
    private ObjectId _id;

    private int c_id;

    //商户
    private String business_no;

    private int activityId;

    private int currencyId;

    //打码
    private int turnoverMul;

    private boolean open;

    private boolean redEnvelopeRainOpen;

    private boolean wageredOpen;

    private int recentTime;

    private List<String> wageredRewardData = new ArrayList<>();
    private List<String> redEnvelopeRainRewardData = new ArrayList<>();
    private List<String> rechargeRewardData = new ArrayList<>();

    private List<String> wageredRuleData = new ArrayList<>();
    private List<String> redEnvelopeRainRuleData = new ArrayList<>();


    private transient final Map<Integer, Rule> ruleWageredMap = new LinkedHashMap<>();
    private transient final Map<Integer, Rule> ruleRedEnvelopeRainMap = new LinkedHashMap<>();


    private transient final Map<Integer, RewardInfo> wageredRewardMap = new LinkedHashMap<>();
    private transient final Map<Integer, RewardInfo> redEnvelopeRainRewardMap = new LinkedHashMap<>();
    private transient final Map<Integer, RewardInfo> rechargeRewardMap = new LinkedHashMap<>();

    @Override
    public boolean check() throws Exception {
        if (this.wageredRuleData != null && !this.wageredRuleData.isEmpty()) {
            for (String data : this.wageredRuleData) {
                final Rule rule = JSON.parseObject(data, Rule.class);
                ruleWageredMap.put(rule.language, rule);
            }
        }
        if (this.redEnvelopeRainRuleData != null && !this.redEnvelopeRainRuleData.isEmpty()) {
            for (String data : this.redEnvelopeRainRuleData) {
                final Rule rule = JSON.parseObject(data, Rule.class);
                ruleRedEnvelopeRainMap.put(rule.language, rule);
            }
        }


        if (this.wageredRewardData != null && !this.wageredRewardData.isEmpty()) {
            for (String data : this.wageredRewardData) {
                final RewardInfo rewardInfo = JSON.parseObject(data, RewardInfo.class);
                wageredRewardMap.put(rewardInfo.id, rewardInfo);
            }
        }
        if (this.redEnvelopeRainRewardData != null && !this.redEnvelopeRainRewardData.isEmpty()) {
            for (String data : this.redEnvelopeRainRewardData) {
                final RewardInfo rewardInfo = JSON.parseObject(data, RewardInfo.class);
                redEnvelopeRainRewardMap.put(rewardInfo.id, rewardInfo);
            }
        }
        if (this.rechargeRewardData != null && !this.rechargeRewardData.isEmpty()) {
            for (String data : this.rechargeRewardData) {
                final RewardInfo rewardInfo = JSON.parseObject(data, RewardInfo.class);
                rechargeRewardMap.put(rewardInfo.id, rewardInfo);
            }
        }
        return true;
    }

    public int getActivityId() {
        return activityId;
    }

    public void setActivityId(int activityId) {
        this.activityId = activityId;
    }

    public int getC_id() {
        return c_id;
    }

    public void setC_id(int c_id) {
        this.c_id = c_id;
    }

    public String getBusiness_no() {
        return business_no;
    }

    public void setBusiness_no(String business_no) {
        this.business_no = business_no;
    }

    public int getCurrencyId() {
        return currencyId;
    }

    public void setCurrencyId(int currencyId) {
        this.currencyId = currencyId;
    }

    public int getTurnoverMul() {
        return turnoverMul;
    }

    public void setTurnoverMul(int turnoverMul) {
        this.turnoverMul = turnoverMul;
    }

    public boolean isOpen() {
        return open;
    }

    public void setOpen(boolean open) {
        this.open = open;
    }

    public boolean isRedEnvelopeRainOpen() {
        return redEnvelopeRainOpen;
    }

    public void setRedEnvelopeRainOpen(boolean redEnvelopeRainOpen) {
        this.redEnvelopeRainOpen = redEnvelopeRainOpen;
    }

    public boolean isWageredOpen() {
        return wageredOpen;
    }

    public void setWageredOpen(boolean wageredOpen) {
        this.wageredOpen = wageredOpen;
    }

    public int getRecentTime() {
        return recentTime;
    }

    public void setRecentTime(int recentTime) {
        this.recentTime = recentTime;
    }

    public List<String> getRedEnvelopeRainRuleData() {
        return redEnvelopeRainRuleData;
    }

    public void setRedEnvelopeRainRuleData(List<String> redEnvelopeRainRuleData) {
        this.redEnvelopeRainRuleData = redEnvelopeRainRuleData;
    }

    public List<String> getWageredRuleData() {
        return wageredRuleData;
    }

    public void setWageredRuleData(List<String> wageredRuleData) {
        this.wageredRuleData = wageredRuleData;
    }

    public Map<Integer, Rule> getRuleRedEnvelopeRainMap() {
        return ruleRedEnvelopeRainMap;
    }

    public Map<Integer, Rule> getRuleWageredMap() {
        return ruleWageredMap;
    }

    public List<String> getWageredRewardData() {
        return wageredRewardData;
    }

    public void setWageredRewardData(List<String> wageredRewardData) {
        this.wageredRewardData = wageredRewardData;
    }

    public List<String> getRedEnvelopeRainRewardData() {
        return redEnvelopeRainRewardData;
    }

    public void setRedEnvelopeRainRewardData(List<String> redEnvelopeRainRewardData) {
        this.redEnvelopeRainRewardData = redEnvelopeRainRewardData;
    }

    public List<String> getRechargeRewardData() {
        return rechargeRewardData;
    }

    public void setRechargeRewardData(List<String> rechargeRewardData) {
        this.rechargeRewardData = rechargeRewardData;
    }

    public Map<Integer, RewardInfo> getWageredRewardMap() {
        return wageredRewardMap;
    }

    public Map<Integer, RewardInfo> getRedEnvelopeRainRewardMap() {
        return redEnvelopeRainRewardMap;
    }

    public Map<Integer, RewardInfo> getRechargeRewardMap() {
        return rechargeRewardMap;
    }

    public static class Rule {
        public int language;
        public String name;

        public int getLanguage() {
            return language;
        }

        public void setLanguage(int language) {
            this.language = language;
        }

        public String getName() {
            return StringUtil.isNullOrEmpty(name) ? null : name;
        }

        public void setName(String name) {
            this.name = name;
        }
    }

    public static class RewardInfo {
        public int id;
        public int currencyId;
        public double wageredAmount;
        public double rechargeAmount;
        public double min;
        public double max;

        public int getCurrencyId() {
            return currencyId;
        }

        public void setCurrencyId(int currencyId) {
            this.currencyId = currencyId;
        }

        public double getWageredAmount() {
            return wageredAmount;
        }

        public void setWageredAmount(double wageredAmount) {
            this.wageredAmount = wageredAmount;
        }

        public int getId() {
            return id;
        }

        public void setId(int id) {
            this.id = id;
        }

        public double getRechargeAmount() {
            return rechargeAmount;
        }

        public void setRechargeAmount(double rechargeAmount) {
            this.rechargeAmount = rechargeAmount;
        }

        public double getMin() {
            return min;
        }

        public void setMin(double min) {
            this.min = min;
        }

        public double getMax() {
            return max;
        }

        public void setMax(double max) {
            this.max = max;
        }
    }

    /**
     * 最高
     *
     * @param rechargeAmount
     * @param wageredAmount
     * @return
     */
    public RewardInfo findMaxReward(Map<Integer, RewardInfo> rewardInfoMap, double rechargeAmount, double wageredAmount) {
        final List<RewardInfo> rewardInfos = new ArrayList<>(rewardInfoMap.values());
        Collections.reverse(rewardInfos);
        for (RewardInfo rewardInfo : rewardInfos) {
            if (rechargeAmount >= rewardInfo.getRechargeAmount() && wageredAmount >= rewardInfo.getWageredAmount()) {
                return rewardInfo;
            }
        }
        return null;
    }

}
