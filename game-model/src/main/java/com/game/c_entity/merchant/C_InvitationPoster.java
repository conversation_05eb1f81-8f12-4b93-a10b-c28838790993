package com.game.c_entity.merchant;

import com.game.engine.utils.JsonUtils;
import com.game.po.data.IDataChecker;
import io.netty.util.internal.StringUtil;
import org.bson.types.ObjectId;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

@Document(collection = "c_invitationPoster")
public class C_InvitationPoster implements IDataChecker {
    @Id
    private ObjectId _id;

    //商户
    private String business_no;

    private int invitationPosterId;

    private int language;

    private String posterName;

    //1.竖版 2.横板
    private int index;

    private String fileUrl;

    private String fileBase64;

    //是否生成二维码
    private boolean generateQRCode;

    //是否开启
    private boolean open;

    @Override
    public boolean check() throws Exception {
        return true;
    }

    public int getInvitationPosterId() {
        return invitationPosterId;
    }

    public void setInvitationPosterId(int invitationPosterId) {
        this.invitationPosterId = invitationPosterId;
    }

    public String getBusiness_no() {
        return business_no;
    }

    public void setBusiness_no(String business_no) {
        this.business_no = business_no;
    }

    public int getLanguage() {
        return language;
    }

    public void setLanguage(int language) {
        this.language = language;
    }

    public String getPosterName() {
        return StringUtil.isNullOrEmpty(posterName) ? "" : posterName;
    }

    public void setPosterName(String posterName) {
        this.posterName = posterName;
    }

    public int getIndex() {
        return index;
    }

    public void setIndex(int index) {
        this.index = index;
    }

    public String getFileUrl() {
        return StringUtil.isNullOrEmpty(fileUrl) ? "" : fileUrl;
    }

    public void setFileUrl(String fileUrl) {
        this.fileUrl = fileUrl;
    }

    public String getFileBase64() {
        return StringUtil.isNullOrEmpty(fileBase64) ? "" : fileBase64;
    }

    public void setFileBase64(String fileBase64) {
        this.fileBase64 = fileBase64;
    }

    public boolean isGenerateQRCode() {
        return generateQRCode;
    }

    public void setGenerateQRCode(boolean generateQRCode) {
        this.generateQRCode = generateQRCode;
    }

    public boolean isOpen() {
        return open;
    }

    public void setOpen(boolean open) {
        this.open = open;
    }

}
