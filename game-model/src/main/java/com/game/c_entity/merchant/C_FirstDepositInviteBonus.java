package com.game.c_entity.merchant;

import com.alibaba.fastjson.JSON;
import com.game.po.data.IDataChecker;
import io.netty.util.internal.StringUtil;
import org.bson.types.ObjectId;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.*;

@Document(collection = "c_firstDepositInviteBonus")
public class C_FirstDepositInviteBonus implements IDataChecker {

    @Id
    private ObjectId _id;

    private int c_id;

    private int activityId;

    //限制开始时间
    private long limitTimeStart;

    //限制结束时间
    private long limitTimeEnd;

    //需要首充金额
    private double needChargeAmount;

    //商户
    private String business_no;

    private boolean open;

    private String timeZone;

    private double chargeReward;

    //倍数
    private int turnoverMul;

    //1.cash 2.bonus
    private int rewardType;

    private List<String> rewardData = new ArrayList<>();

    //规则说明
    private List<String> ruleData = new ArrayList<>();

    private transient final Map<Integer, VipRewardInfo> rewardMap = new LinkedHashMap<>();

    private transient final Map<Integer, Rule> ruleMap = new LinkedHashMap<>();

    @Override
    public boolean check() throws Exception {
        if (this.rewardData != null && !this.rewardData.isEmpty()) {
            for (String data : this.rewardData) {
                final VipRewardInfo reward = JSON.parseObject(data, VipRewardInfo.class);
                rewardMap.put(reward.id, reward);
            }
        }

        if (this.ruleData != null && !this.ruleData.isEmpty()) {
            for (String data : this.ruleData) {
                final Rule rule = JSON.parseObject(data, Rule.class);
                ruleMap.put(rule.language, rule);
            }
        }
        return true;
    }

    public int getActivityId() {
        return activityId;
    }

    public void setActivityId(int activityId) {
        this.activityId = activityId;
    }

    public long getLimitTimeStart() {
        return limitTimeStart;
    }

    public void setLimitTimeStart(long limitTimeStart) {
        this.limitTimeStart = limitTimeStart;
    }

    public long getLimitTimeEnd() {
        return limitTimeEnd;
    }

    public void setLimitTimeEnd(long limitTimeEnd) {
        this.limitTimeEnd = limitTimeEnd;
    }

    public double getNeedChargeAmount() {
        return needChargeAmount;
    }

    public void setNeedChargeAmount(double needChargeAmount) {
        this.needChargeAmount = needChargeAmount;
    }

    public int getC_id() {
        return c_id;
    }

    public void setC_id(int c_id) {
        this.c_id = c_id;
    }

    public String getBusiness_no() {
        return business_no;
    }

    public void setBusiness_no(String business_no) {
        this.business_no = business_no;
    }

    public boolean isOpen() {
        return open;
    }

    public void setOpen(boolean open) {
        this.open = open;
    }

    public String getTimeZone() {
        return timeZone;
    }

    public void setTimeZone(String timeZone) {
        this.timeZone = timeZone;
    }

    public double getChargeReward() {
        return chargeReward;
    }

    public void setChargeReward(double chargeReward) {
        this.chargeReward = chargeReward;
    }

    public int getTurnoverMul() {
        return turnoverMul;
    }

    public void setTurnoverMul(int turnoverMul) {
        this.turnoverMul = turnoverMul;
    }

    public int getRewardType() {
        return rewardType;
    }

    public void setRewardType(int rewardType) {
        this.rewardType = rewardType;
    }

    public List<String> getRuleData() {
        return ruleData;
    }

    public void setRuleData(List<String> ruleData) {
        this.ruleData = ruleData;
    }

    public Map<Integer, Rule> getRuleMap() {
        return ruleMap;
    }

    public List<String> getRewardData() {
        return rewardData;
    }

    public void setRewardData(List<String> rewardData) {
        this.rewardData = rewardData;
    }

    public Map<Integer, VipRewardInfo> getRewardMap() {
        return rewardMap;
    }

    public static class Rule {
        public int language;
        public String icon;
        public String rule;

        public int getLanguage() {
            return language;
        }

        public void setLanguage(int language) {
            this.language = language;
        }

        public String getIcon() {
            return StringUtil.isNullOrEmpty(icon) ? null : icon;
        }

        public void setIcon(String icon) {
            this.icon = icon;
        }

        public String getRule() {
            return StringUtil.isNullOrEmpty(rule) ? "" : rule;
        }

        public void setRule(String rule) {
            this.rule = rule;
        }
    }

    public static class VipRewardInfo {
        private int id;

        private int vipMin;

        private int vipMax;

        private List<RewardInfo> rewardInfos = new ArrayList<>();

        public int getId() {
            return id;
        }

        public void setId(int id) {
            this.id = id;
        }

        public int getVipMin() {
            return vipMin;
        }

        public void setVipMin(int vipMin) {
            this.vipMin = vipMin;
        }

        public int getVipMax() {
            return vipMax;
        }

        public void setVipMax(int vipMax) {
            this.vipMax = vipMax;
        }

        public List<RewardInfo> getRewardInfos() {
            return rewardInfos;
        }

        public void setRewardInfos(List<RewardInfo> rewardInfos) {
            this.rewardInfos = rewardInfos;
        }
    }

    public static class RewardInfo {
        public int id;
        //奖励比例
        public double rewardRate;
        //解锁比例
        public double unlockRate;
        //总解锁比例
        public double totalUnlockRate;

        public double getUnlockRate() {
            return unlockRate;
        }

        public void setUnlockRate(double unlockRate) {
            this.unlockRate = unlockRate;
        }

        public int getId() {
            return id;
        }

        public void setId(int id) {
            this.id = id;
        }

        public double getRewardRate() {
            return rewardRate;
        }

        public void setRewardRate(double rewardRate) {
            this.rewardRate = rewardRate;
        }

        public double getTotalUnlockRate() {
            return totalUnlockRate;
        }

        public void setTotalUnlockRate(double totalUnlockRate) {
            this.totalUnlockRate = totalUnlockRate;
        }
    }

    public VipRewardInfo findVipRewardsList(int vipLevel) {
        for (VipRewardInfo vipRewardInfo : this.getRewardMap().values()) {
            if (vipRewardInfo.vipMin <= vipLevel && vipLevel <= vipRewardInfo.vipMax) {
                return vipRewardInfo;
            }
        }
        return null;
    }

    /**
     * 每档
     *
     * @param vipLevel
     * @param rewards
     * @param process
     * @return
     */
    public List<RewardInfo> findRewardsList(int vipLevel, final Set<Integer> rewards, double process) {
        final List<RewardInfo> rewardInfos = new ArrayList<>();
        final VipRewardInfo vipRewardInfo = findVipRewardsList(vipLevel);
        if (vipRewardInfo == null) {
            return rewardInfos;
        }
        for (RewardInfo rewardInfo : vipRewardInfo.getRewardInfos()) {
            if (rewards.contains(rewardInfo.id)) {
                continue;
            }
            if (process >= rewardInfo.getRewardRate()) {
                rewardInfos.add(rewardInfo);
            }
        }
        return rewardInfos;
    }

    /**
     * @param vipLevel
     * @param rewards
     * @param process
     * @return
     */
    public RewardInfo findRewards(int vipLevel, final Set<Integer> rewards, int gearId, double process) {
        final VipRewardInfo vipRewardInfo = findVipRewardsList(vipLevel);
        if (vipRewardInfo == null) {
            return null;
        }
        for (RewardInfo rewardInfo : vipRewardInfo.getRewardInfos()) {
            if (rewards.contains(rewardInfo.id)) {
                continue;
            }
            if (gearId == rewardInfo.id && process >= rewardInfo.getRewardRate()) {
                return rewardInfo;
            }
        }
        return null;
    }

}
