package com.game.c_entity.merchant;

import com.alibaba.fastjson.JSON;
import com.game.po.data.IDataChecker;
import io.netty.util.internal.StringUtil;
import org.bson.types.ObjectId;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.*;

@Document(collection = "c_firstDepositInviteBonus")
public class C_FirstDepositInviteBonus implements IDataChecker {

    @Id
    private ObjectId _id;

    private int c_id;

    private int activityId;

    //商户
    private String business_no;

    private boolean open;

    private String timeZone;

    private double chargeReward;

    //倍数
    private int turnoverMul;

    //1.cash 2.bonus
    private int rewardType;

    private List<String> rewardData = new ArrayList<>();

    //规则说明
    private List<String> ruleData = new ArrayList<>();

    private transient final Map<Integer, RewardInfo> rewardMap = new LinkedHashMap<>();

    private transient final Map<Integer, Rule> ruleMap = new LinkedHashMap<>();

    @Override
    public boolean check() throws Exception {
        if (this.rewardData != null && !this.rewardData.isEmpty()) {
            for (String data : this.rewardData) {
                final RewardInfo reward = JSON.parseObject(data, RewardInfo.class);
                rewardMap.put(reward.id, reward);
            }
        }

        if (this.ruleData != null && !this.ruleData.isEmpty()) {
            for (String data : this.ruleData) {
                final Rule rule = JSON.parseObject(data, Rule.class);
                ruleMap.put(rule.language, rule);
            }
        }
        return true;
    }

    public int getActivityId() {
        return activityId;
    }

    public void setActivityId(int activityId) {
        this.activityId = activityId;
    }

    public int getC_id() {
        return c_id;
    }

    public void setC_id(int c_id) {
        this.c_id = c_id;
    }

    public String getBusiness_no() {
        return business_no;
    }

    public void setBusiness_no(String business_no) {
        this.business_no = business_no;
    }

    public boolean isOpen() {
        return open;
    }

    public void setOpen(boolean open) {
        this.open = open;
    }

    public String getTimeZone() {
        return timeZone;
    }

    public void setTimeZone(String timeZone) {
        this.timeZone = timeZone;
    }

    public double getChargeReward() {
        return chargeReward;
    }

    public void setChargeReward(double chargeReward) {
        this.chargeReward = chargeReward;
    }

    public int getTurnoverMul() {
        return turnoverMul;
    }

    public void setTurnoverMul(int turnoverMul) {
        this.turnoverMul = turnoverMul;
    }

    public int getRewardType() {
        return rewardType;
    }

    public void setRewardType(int rewardType) {
        this.rewardType = rewardType;
    }

    public List<String> getRuleData() {
        return ruleData;
    }

    public void setRuleData(List<String> ruleData) {
        this.ruleData = ruleData;
    }

    public Map<Integer, Rule> getRuleMap() {
        return ruleMap;
    }

    public List<String> getRewardData() {
        return rewardData;
    }

    public void setRewardData(List<String> rewardData) {
        this.rewardData = rewardData;
    }

    public Map<Integer, RewardInfo> getRewardMap() {
        return rewardMap;
    }

    public static class Rule {
        public int language;
        public String icon;
        public String rule;

        public int getLanguage() {
            return language;
        }

        public void setLanguage(int language) {
            this.language = language;
        }

        public String getIcon() {
            return StringUtil.isNullOrEmpty(icon) ? null : icon;
        }

        public void setIcon(String icon) {
            this.icon = icon;
        }

        public String getRule() {
            return StringUtil.isNullOrEmpty(rule) ? "" : rule;
        }

        public void setRule(String rule) {
            this.rule = rule;
        }
    }

    public static class RewardInfo {
        public int id;
        //奖励比例
        public double rewardRate;
        //解锁比例
        public double unlockRate;
        //总解锁比例
        public double totalUnlockRate;

        public double getUnlockRate() {
            return unlockRate;
        }

        public void setUnlockRate(double unlockRate) {
            this.unlockRate = unlockRate;
        }

        public int getId() {
            return id;
        }

        public void setId(int id) {
            this.id = id;
        }

        public double getRewardRate() {
            return rewardRate;
        }

        public void setRewardRate(double rewardRate) {
            this.rewardRate = rewardRate;
        }

        public double getTotalUnlockRate() {
            return totalUnlockRate;
        }

        public void setTotalUnlockRate(double totalUnlockRate) {
            this.totalUnlockRate = totalUnlockRate;
        }
    }

    /**
     * 每档
     *
     * @param rewards
     * @param process
     * @return
     */
    public List<RewardInfo> findRewardsList(final Set<Integer> rewards, double process) {
        final List<RewardInfo> rewardInfos = new ArrayList<>();

        for (RewardInfo rewardInfo : this.getRewardMap().values()) {
            if (rewards.contains(rewardInfo.id)) {
                continue;
            }
            if (process >= rewardInfo.getRewardRate()) {
                rewardInfos.add(rewardInfo);
            }
        }
        return rewardInfos;
    }

}
