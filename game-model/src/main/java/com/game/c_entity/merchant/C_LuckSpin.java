package com.game.c_entity.merchant;

import com.game.engine.utils.JsonUtils;
import com.game.po.data.IDataChecker;
import io.netty.util.internal.StringUtil;
import org.bson.types.ObjectId;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

@Document(collection = "c_luckSpin")
public class C_LuckSpin implements IDataChecker {
    @Id
    private ObjectId _id;

    //商户号
    private String business_no;

    private int c_id;

    private int activityId;

    //标签排序
    private int tagSort;

    //规则说明
    private List<String> ruleInfo;

    //转盘设定
    private List<String> turntables;

    //奖金打码倍率
    private double rewardTurnoverMul;

    //转盘类型 1.邀请转盘 2.vip转盘 3.每日 4.每周
    private int turntableType;

    //子类型
    private int subType;

    //语言 1.英语 2.葡萄牙 3.西班牙
    private List<Integer> languages = new ArrayList<>();

    //初始转盘次数
    private int initTurntableTimes;

    //邀请数据
    private String inviteDataInfo;

    //充值数据
    private String rechargeDataInfo;

    //vip等级
    private int vipLevel;

    //转盘过期时间 -1
    private long expiredTime;

    //活动开始时间
    private long startTime;

    //活动结束时间
    private long endTime;

    //转盘是否开启
    private boolean open;

    //额外获得
    private String extras;

    private long deleteTime;

    private transient InviteData inviteInfo;
    private transient RechargeData rechargeInfo;
    private transient ExtraCondition extraCondition;

    private transient final Map<Integer, RuleData> ruleDataMap = new LinkedHashMap<>();
    private transient final Map<Integer, Integer> weightMap = new LinkedHashMap<>();
    private transient final Map<Integer, Turntable> turntableMap = new LinkedHashMap<>();

    @Override
    public boolean check() throws Exception {
        if (ruleInfo != null && !ruleInfo.isEmpty()) {
            for (String str : ruleInfo) {
                final RuleData ruleData = JsonUtils.readFromJson(str, RuleData.class);
                this.ruleDataMap.put(ruleData.getLanguage(), ruleData);
            }
        }

        if (turntables != null && !turntables.isEmpty()) {
            for (String data : turntables) {
                final Turntable turntable = JsonUtils.readFromJson(data, Turntable.class);
                this.weightMap.put(turntable.getId(), turntable.getWeight());
                this.turntableMap.put(turntable.getId(), turntable);
            }
        }

        if (!StringUtil.isNullOrEmpty(inviteDataInfo)) {
            inviteInfo = JsonUtils.readFromJson(inviteDataInfo, InviteData.class);
        }

        if (!StringUtil.isNullOrEmpty(rechargeDataInfo)) {
            rechargeInfo = JsonUtils.readFromJson(rechargeDataInfo, RechargeData.class);
        }

        if (!StringUtil.isNullOrEmpty(extras)) {
            extraCondition = JsonUtils.readFromJson(extras, ExtraCondition.class);
        }

        return true;
    }

    public int getC_id() {
        return c_id;
    }

    public void setC_id(int c_id) {
        this.c_id = c_id;
    }

    public int getActivityId() {
        return activityId;
    }

    public void setActivityId(int activityId) {
        this.activityId = activityId;
    }

    public String getBusiness_no() {
        return business_no;
    }

    public void setBusiness_no(String business_no) {
        this.business_no = business_no;
    }

    public int getTagSort() {
        return tagSort;
    }

    public void setTagSort(int tagSort) {
        this.tagSort = tagSort;
    }

    public List<String> getRuleInfo() {
        return ruleInfo;
    }

    public void setRuleInfo(List<String> ruleInfo) {
        this.ruleInfo = ruleInfo;
    }

    public List<String> getTurntables() {
        return turntables;
    }

    public void setTurntables(List<String> turntables) {
        this.turntables = turntables;
    }

    public double getRewardTurnoverMul() {
        return rewardTurnoverMul;
    }

    public void setRewardTurnoverMul(double rewardTurnoverMul) {
        this.rewardTurnoverMul = rewardTurnoverMul;
    }

    public int getTurntableType() {
        return turntableType;
    }

    public void setTurntableType(int turntableType) {
        this.turntableType = turntableType;
    }

    public int getSubType() {
        return subType;
    }

    public void setSubType(int subType) {
        this.subType = subType;
    }

    public List<Integer> getLanguages() {
        return languages;
    }

    public void setLanguages(List<Integer> languages) {
        this.languages = languages;
    }

    public int getInitTurntableTimes() {
        return initTurntableTimes;
    }

    public void setInitTurntableTimes(int initTurntableTimes) {
        this.initTurntableTimes = initTurntableTimes;
    }

    public int getVipLevel() {
        return vipLevel;
    }

    public void setVipLevel(int vipLevel) {
        this.vipLevel = vipLevel;
    }

    public long getExpiredTime() {
        return expiredTime;
    }

    public void setExpiredTime(long expiredTime) {
        this.expiredTime = expiredTime;
    }

    public long getStartTime() {
        return startTime;
    }

    public void setStartTime(long startTime) {
        this.startTime = startTime;
    }

    public long getEndTime() {
        return endTime;
    }

    public void setEndTime(long endTime) {
        this.endTime = endTime;
    }

    public boolean isOpen() {
        return open;
    }

    public void setOpen(boolean open) {
        this.open = open;
    }

    public Map<Integer, Integer> getWeightMap() {
        return weightMap;
    }

    public Map<Integer, Turntable> getTurntableMap() {
        return turntableMap;
    }

    public String getInviteDataInfo() {
        return inviteDataInfo;
    }

    public void setInviteDataInfo(String inviteDataInfo) {
        this.inviteDataInfo = inviteDataInfo;
    }

    public String getRechargeDataInfo() {
        return rechargeDataInfo;
    }

    public void setRechargeDataInfo(String rechargeDataInfo) {
        this.rechargeDataInfo = rechargeDataInfo;
    }

    public String getExtras() {
        return extras;
    }

    public void setExtras(String extras) {
        this.extras = extras;
    }

    public long getDeleteTime() {
        return deleteTime;
    }

    public void setDeleteTime(long deleteTime) {
        this.deleteTime = deleteTime;
    }

    public InviteData getInviteInfo() {
        return inviteInfo;
    }

    public void setInviteInfo(InviteData inviteInfo) {
        this.inviteInfo = inviteInfo;
    }

    public RechargeData getRechargeInfo() {
        return rechargeInfo;
    }

    public void setRechargeInfo(RechargeData rechargeInfo) {
        this.rechargeInfo = rechargeInfo;
    }

    public ExtraCondition getExtraCondition() {
        return extraCondition;
    }

    public void setExtraCondition(ExtraCondition extraCondition) {
        this.extraCondition = extraCondition;
    }

    public Map<Integer, RuleData> getRuleDataMap() {
        return ruleDataMap;
    }

    public static class Turntable {
        private int id;
        private int itemId;
        private double num;
        private int weight;

        public int getId() {
            return id;
        }

        public void setId(int id) {
            this.id = id;
        }

        public int getItemId() {
            return itemId;
        }

        public void setItemId(int itemId) {
            this.itemId = itemId;
        }

        public double getNum() {
            return num;
        }

        public void setNum(double num) {
            this.num = num;
        }

        public int getWeight() {
            return weight;
        }

        public void setWeight(int weight) {
            this.weight = weight;
        }
    }

    public static class InviteData {
        private int currencyId;

        //初始奖励
        private double initReward;

        //可领取金额
        private int availableReward;

        //间隔
        private int interval;

        //增加次数
        private int addTimes;

        //最大次数
        private int maxTimes;

        public int getCurrencyId() {
            return currencyId;
        }

        public void setCurrencyId(int currencyId) {
            this.currencyId = currencyId;
        }

        public double getInitReward() {
            return initReward;
        }

        public void setInitReward(double initReward) {
            this.initReward = initReward;
        }

        public int getAvailableReward() {
            return availableReward;
        }

        public void setAvailableReward(int availableReward) {
            this.availableReward = availableReward;
        }

        public int getInterval() {
            return interval;
        }

        public void setInterval(int interval) {
            this.interval = interval;
        }

        public int getAddTimes() {
            return addTimes;
        }

        public void setAddTimes(int addTimes) {
            this.addTimes = addTimes;
        }

        public int getMaxTimes() {
            return maxTimes;
        }

        public void setMaxTimes(int maxTimes) {
            this.maxTimes = maxTimes;
        }
    }

    public static class RechargeData {
        private double rechargeAmount;

        private int maxTimes;

        public double getRechargeAmount() {
            return rechargeAmount;
        }

        public void setRechargeAmount(double rechargeAmount) {
            this.rechargeAmount = rechargeAmount;
        }

        public int getMaxTimes() {
            return maxTimes;
        }

        public void setMaxTimes(int maxTimes) {
            this.maxTimes = maxTimes;
        }
    }

    public static class ExtraCondition {
        //邀请人数
        private int inviteNum;

        //增加次数
        private int addTimes;

        //最大次数
        private int maxTimes;

        //充值金额
        private int rechargeUsdAmount;

        //投注次数
        private int betTimes;

        //投注金额
        private int betUsdAmount;

        public int getInviteNum() {
            return inviteNum;
        }

        public void setInviteNum(int inviteNum) {
            this.inviteNum = inviteNum;
        }

        public int getAddTimes() {
            return addTimes;
        }

        public void setAddTimes(int addTimes) {
            this.addTimes = addTimes;
        }

        public int getMaxTimes() {
            return maxTimes;
        }

        public void setMaxTimes(int maxTimes) {
            this.maxTimes = maxTimes;
        }

        public int getRechargeUsdAmount() {
            return rechargeUsdAmount;
        }

        public void setRechargeUsdAmount(int rechargeUsdAmount) {
            this.rechargeUsdAmount = rechargeUsdAmount;
        }

        public int getBetTimes() {
            return betTimes;
        }

        public void setBetTimes(int betTimes) {
            this.betTimes = betTimes;
        }

        public int getBetUsdAmount() {
            return betUsdAmount;
        }

        public void setBetUsdAmount(int betUsdAmount) {
            this.betUsdAmount = betUsdAmount;
        }
    }

    public static class RuleData {
        public int language;
        public String desc;
        public String activityName;
        public String subName;

        public int getLanguage() {
            return language;
        }

        public void setLanguage(int language) {
            this.language = language;
        }

        public String getDesc() {
            return StringUtil.isNullOrEmpty(desc) ? "" : desc;
        }

        public void setDesc(String desc) {
            this.desc = desc;
        }

        public String getActivityName() {
            return StringUtil.isNullOrEmpty(activityName) ? "" : activityName;
        }

        public void setActivityName(String activityName) {
            this.activityName = activityName;
        }

        public String getSubName() {
            return StringUtil.isNullOrEmpty(subName) ? "" : subName;
        }

        public void setSubName(String subName) {
            this.subName = subName;
        }
    }
}
