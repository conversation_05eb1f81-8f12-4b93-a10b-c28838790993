package com.game.c_entity.merchant;

import com.game.po.data.IDataChecker;
import org.bson.types.ObjectId;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

@Document(collection = "c_freeGameTurnover")
public class C_FreeGameTurnover implements IDataChecker {

    @Id
    private ObjectId _id;

    //商户
    private String business_no;

    //平台id
    private int platformId;

    //打码量倍率
    private double turnoverMul;

    @Override
    public boolean check() throws Exception {
        return true;
    }

    public String getBusiness_no() {
        return business_no;
    }

    public void setBusiness_no(String business_no) {
        this.business_no = business_no;
    }

    public int getPlatformId() {
        return platformId;
    }

    public void setPlatformId(int platformId) {
        this.platformId = platformId;
    }

    public double getTurnoverMul() {
        return turnoverMul;
    }

    public void setTurnoverMul(double turnoverMul) {
        this.turnoverMul = turnoverMul;
    }
}
