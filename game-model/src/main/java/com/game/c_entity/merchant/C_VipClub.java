package com.game.c_entity.merchant;

import com.game.engine.utils.JsonUtils;
import com.game.po.data.IDataChecker;
import io.netty.util.internal.StringUtil;
import org.bson.types.ObjectId;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

@Document(collection = "c_vipClub")
public class C_VipClub implements IDataChecker {
    @Id
    private ObjectId _id;

    //商户
    private String business_no;

    private int vipClubId;

    //图标
    private String icon;

    //vip名字
    private String vipName;

    //vip等级
    private int vipLevel;

    private int currencyId;
    //需要投注
    private double needExp;
    //需要充值
    private double needRecharge;

    //保级周期
    private int relegationCycle;
    //保级充值
    private double relegationRecharge;
    //保级投注
    private double relegationWagered;

    //每日货币
    private int dailyCurrencyId;
    //每日提现金额
    private double dailyWithdrawAmount;
    //每日提现次数
    private int dailyWithdrawTimes;
    //单笔提现上限
    private double dailyWithdrawUpper;
    //单笔提现下限
    private double dailyWithdrawLower;
    //单日免手续费
    private int dailyWithdrawFreeTimes;

    //赌注打码量倍数
    private double wagerTurnoverMul;
    //下注(usd)100-200:0.01
    private List<String> wageredList = new ArrayList<>();

    //升级奖励(usd) 币种:数量
    private String upLevelReward;
    //升级打码量倍数
    private double upLevelTurnoverMul;

    //周奖励
    private String weeklyReward;
    private double weeklyTurnoverMul;

    //月奖励
    private String monthlyReward;
    private double monthlyTurnoverMul;

    //周返现比例 有效压住X1%X周返水比例
    private double weeklyCashBackRate;
    private double weeklyRateTurnoverMul;
    private String weeklyWagerRange;

    //月返现比例 有效压住X1%X月返水比例
    private double monthlyCashBackRate;
    private double monthlyRateTurnoverMul;
    private String monthlyWagerRange;

    //玩家返现开关
    private boolean playerCashBackOpen;

    //玩家返水配置
    private List<String> playerCashBacks = new ArrayList<>();

    private boolean vipSpin;

    private boolean luxuryGiveaway;

    private boolean vipHost;

    private String timeZone;
    private List<String> signInData =  new ArrayList<>();

    //领取
    private List<String> receiveRewardData = new ArrayList<>();

    private transient final List<Tire> tireList = new ArrayList<>();
    private transient final Map<Integer, PlayerCashBack> playerCashBackMap = new LinkedHashMap<>();
    private transient final Map<Integer, SignInReward> signInRewardMap = new LinkedHashMap<>();
    private transient final Map<Integer, ReceiveReward> receiveRewardMap = new LinkedHashMap<>();

    @Override
    public boolean check() throws Exception {
        if (playerCashBacks != null && !playerCashBacks.isEmpty()) {
            for (String str : playerCashBacks) {
                final PlayerCashBack playerCashBack = JsonUtils.readFromJson(str, PlayerCashBack.class);
                playerCashBackMap.put(playerCashBack.gameType, playerCashBack);
            }
        }
        if (this.wageredList != null && !this.wageredList.isEmpty()) {
            for (String wagered : wageredList) {
                tireList.add(JsonUtils.readFromJson(wagered, Tire.class));
            }
        }
        if (this.signInData != null && !this.signInData.isEmpty()) {
            for (String data : signInData) {
                final SignInReward signInReward = JsonUtils.readFromJson(data, SignInReward.class);
                signInRewardMap.put(signInReward.getDay(), signInReward);
            }
        }
        if (this.receiveRewardData != null && !this.receiveRewardData.isEmpty()) {
            for (String data : receiveRewardData) {
                final ReceiveReward receiveReward = JsonUtils.readFromJson(data, ReceiveReward.class);
                receiveRewardMap.put(receiveReward.getId(), receiveReward);
            }
        }
        return true;
    }

    public Tire getTire(double wagered) {
        for (Tire tire : this.tireList) {
            if (tire.getWagerMin() <= wagered && (wagered < tire.getWagerMax() || tire.getWagerMax() == 0)) {
                return tire;
            }
        }
        return null;
    }

    public String getBusiness_no() {
        return business_no;
    }

    public void setBusiness_no(String business_no) {
        this.business_no = business_no;
    }

    public int getVipClubId() {
        return vipClubId;
    }

    public void setVipClubId(int vipClubId) {
        this.vipClubId = vipClubId;
    }

    public String getIcon() {
        return StringUtil.isNullOrEmpty(icon) ? "" : icon;
    }

    public void setIcon(String icon) {
        this.icon = icon;
    }

    public String getVipName() {
        return StringUtil.isNullOrEmpty(vipName) ? "" : vipName;
    }

    public void setVipName(String vipName) {
        this.vipName = vipName;
    }

    public int getVipLevel() {
        return vipLevel;
    }

    public void setVipLevel(int vipLevel) {
        this.vipLevel = vipLevel;
    }

    public int getCurrencyId() {
        return currencyId;
    }

    public void setCurrencyId(int currencyId) {
        this.currencyId = currencyId;
    }

    public double getNeedExp() {
        return needExp;
    }

    public void setNeedExp(double needExp) {
        this.needExp = needExp;
    }

    public double getNeedRecharge() {
        return needRecharge;
    }

    public void setNeedRecharge(double needRecharge) {
        this.needRecharge = needRecharge;
    }

    public int getRelegationCycle() {
        return relegationCycle;
    }

    public void setRelegationCycle(int relegationCycle) {
        this.relegationCycle = relegationCycle;
    }

    public double getRelegationRecharge() {
        return relegationRecharge;
    }

    public void setRelegationRecharge(double relegationRecharge) {
        this.relegationRecharge = relegationRecharge;
    }

    public double getRelegationWagered() {
        return relegationWagered;
    }

    public void setRelegationWagered(double relegationWagered) {
        this.relegationWagered = relegationWagered;
    }

    public int getDailyCurrencyId() {
        return dailyCurrencyId;
    }

    public void setDailyCurrencyId(int dailyCurrencyId) {
        this.dailyCurrencyId = dailyCurrencyId;
    }

    public double getDailyWithdrawAmount() {
        return dailyWithdrawAmount;
    }

    public void setDailyWithdrawAmount(double dailyWithdrawAmount) {
        this.dailyWithdrawAmount = dailyWithdrawAmount;
    }

    public int getDailyWithdrawTimes() {
        return dailyWithdrawTimes;
    }

    public void setDailyWithdrawTimes(int dailyWithdrawTimes) {
        this.dailyWithdrawTimes = dailyWithdrawTimes;
    }

    public double getDailyWithdrawUpper() {
        return dailyWithdrawUpper;
    }

    public void setDailyWithdrawUpper(double dailyWithdrawUpper) {
        this.dailyWithdrawUpper = dailyWithdrawUpper;
    }

    public double getDailyWithdrawLower() {
        return dailyWithdrawLower;
    }

    public void setDailyWithdrawLower(double dailyWithdrawLower) {
        this.dailyWithdrawLower = dailyWithdrawLower;
    }

    public int getDailyWithdrawFreeTimes() {
        return dailyWithdrawFreeTimes;
    }

    public void setDailyWithdrawFreeTimes(int dailyWithdrawFreeTimes) {
        this.dailyWithdrawFreeTimes = dailyWithdrawFreeTimes;
    }

    public List<String> getWageredList() {
        return wageredList;
    }

    public void setWageredList(List<String> wageredList) {
        this.wageredList = wageredList;
    }

    public double getWagerTurnoverMul() {
        return wagerTurnoverMul;
    }

    public void setWagerTurnoverMul(double wagerTurnoverMul) {
        this.wagerTurnoverMul = wagerTurnoverMul;
    }

    public String getUpLevelReward() {
        return StringUtil.isNullOrEmpty(upLevelReward) ? "" : upLevelReward;
    }

    public void setUpLevelReward(String upLevelReward) {
        this.upLevelReward = upLevelReward;
    }

    public double getUpLevelTurnoverMul() {
        return upLevelTurnoverMul;
    }

    public void setUpLevelTurnoverMul(double upLevelTurnoverMul) {
        this.upLevelTurnoverMul = upLevelTurnoverMul;
    }

    public String getWeeklyReward() {
        return weeklyReward;
    }

    public void setWeeklyReward(String weeklyReward) {
        this.weeklyReward = weeklyReward;
    }

    public double getWeeklyTurnoverMul() {
        return weeklyTurnoverMul;
    }

    public void setWeeklyTurnoverMul(double weeklyTurnoverMul) {
        this.weeklyTurnoverMul = weeklyTurnoverMul;
    }

    public String getMonthlyReward() {
        return monthlyReward;
    }

    public void setMonthlyReward(String monthlyReward) {
        this.monthlyReward = monthlyReward;
    }

    public double getMonthlyTurnoverMul() {
        return monthlyTurnoverMul;
    }

    public void setMonthlyTurnoverMul(double monthlyTurnoverMul) {
        this.monthlyTurnoverMul = monthlyTurnoverMul;
    }

    public double getWeeklyCashBackRate() {
        return weeklyCashBackRate;
    }

    public void setWeeklyCashBackRate(double weeklyCashBackRate) {
        this.weeklyCashBackRate = weeklyCashBackRate;
    }

    public double getWeeklyRateTurnoverMul() {
        return weeklyRateTurnoverMul;
    }

    public void setWeeklyRateTurnoverMul(double weeklyRateTurnoverMul) {
        this.weeklyRateTurnoverMul = weeklyRateTurnoverMul;
    }

    public String getWeeklyWagerRange() {
        return weeklyWagerRange;
    }

    public void setWeeklyWagerRange(String weeklyWagerRange) {
        this.weeklyWagerRange = weeklyWagerRange;
    }

    public String getMonthlyWagerRange() {
        return monthlyWagerRange;
    }

    public void setMonthlyWagerRange(String monthlyWagerRange) {
        this.monthlyWagerRange = monthlyWagerRange;
    }

    public double getMonthlyCashBackRate() {
        return monthlyCashBackRate;
    }

    public void setMonthlyCashBackRate(double monthlyCashBackRate) {
        this.monthlyCashBackRate = monthlyCashBackRate;
    }

    public double getMonthlyRateTurnoverMul() {
        return monthlyRateTurnoverMul;
    }

    public void setMonthlyRateTurnoverMul(double monthlyRateTurnoverMul) {
        this.monthlyRateTurnoverMul = monthlyRateTurnoverMul;
    }

    public boolean isPlayerCashBackOpen() {
        return playerCashBackOpen;
    }

    public void setPlayerCashBackOpen(boolean playerCashBackOpen) {
        this.playerCashBackOpen = playerCashBackOpen;
    }

    public List<String> getPlayerCashBacks() {
        return playerCashBacks;
    }

    public void setPlayerCashBacks(List<String> playerCashBacks) {
        this.playerCashBacks = playerCashBacks;
    }

    public boolean isVipSpin() {
        return vipSpin;
    }

    public void setVipSpin(boolean vipSpin) {
        this.vipSpin = vipSpin;
    }

    public boolean isLuxuryGiveaway() {
        return luxuryGiveaway;
    }

    public void setLuxuryGiveaway(boolean luxuryGiveaway) {
        this.luxuryGiveaway = luxuryGiveaway;
    }

    public boolean isVipHost() {
        return vipHost;
    }

    public void setVipHost(boolean vipHost) {
        this.vipHost = vipHost;
    }

    public String getTimeZone() {
        return StringUtil.isNullOrEmpty(timeZone) ? "UTC-0" : timeZone;
    }

    public void setTimeZone(String timeZone) {
        this.timeZone = timeZone;
    }

    public Map<Integer, PlayerCashBack> getPlayerCashBackMap() {
        return playerCashBackMap;
    }

    public List<Tire> getTireList() {
        return tireList;
    }

    public Map<Integer, SignInReward> getSignInRewardMap() {
        return signInRewardMap;
    }

    public List<String> getSignInData() {
        return signInData;
    }

    public void setSignInData(List<String> signInData) {
        this.signInData = signInData;
    }

    public List<String> getReceiveRewardData() {
        return receiveRewardData;
    }

    public void setReceiveRewardData(List<String> receiveRewardData) {
        this.receiveRewardData = receiveRewardData;
    }

    public Map<Integer, ReceiveReward> getReceiveRewardMap() {
        return receiveRewardMap;
    }

    public static class Tire {
        public int currentTier;
        public double wagerMin;
        public double wagerMax;
        public double rate;

        public double getWagerMin() {
            return wagerMin;
        }

        public void setWagerMin(double wagerMin) {
            this.wagerMin = wagerMin;
        }

        public double getWagerMax() {
            return wagerMax;
        }

        public void setWagerMax(double wagerMax) {
            this.wagerMax = wagerMax;
        }

        public int getCurrentTier() {
            return currentTier;
        }

        public void setCurrentTier(int currentTier) {
            this.currentTier = currentTier;
        }

        public double getRate() {
            return rate;
        }

        public void setRate(double rate) {
            this.rate = rate;
        }
    }

    public static class PlayerCashBack {
        public int rebateMethod;
        public int gameType;
        public double turnoverMul;
        public double backCashRate;
        public double gameCashBackRate;

        public int getRebateMethod() {
            return rebateMethod;
        }

        public void setRebateMethod(int rebateMethod) {
            this.rebateMethod = rebateMethod;
        }

        public int getGameType() {
            return gameType;
        }

        public void setGameType(int gameType) {
            this.gameType = gameType;
        }

        public double getTurnoverMul() {
            return turnoverMul;
        }

        public void setTurnoverMul(double turnoverMul) {
            this.turnoverMul = turnoverMul;
        }

        public double getBackCashRate() {
            return backCashRate;
        }

        public void setBackCashRate(double backCashRate) {
            this.backCashRate = backCashRate;
        }

        public double getGameCashBackRate() {
            return gameCashBackRate;
        }

        public void setGameCashBackRate(double gameCashBackRate) {
            this.gameCashBackRate = gameCashBackRate;
        }
    }

    public static class SignInReward {
        public int currencyId;

        public double turnoverMul;

        public int day;

        public double reward;

        public int getCurrencyId() {
            return currencyId;
        }

        public void setCurrencyId(int currencyId) {
            this.currencyId = currencyId;
        }

        public double getTurnoverMul() {
            return turnoverMul;
        }

        public void setTurnoverMul(double turnoverMul) {
            this.turnoverMul = turnoverMul;
        }

        public int getDay() {
            return day;
        }

        public void setDay(int day) {
            this.day = day;
        }

        public double getReward() {
            return reward;
        }

        public void setReward(double reward) {
            this.reward = reward;
        }
    }

    public static class ReceiveReward {
        public int id;//1.每日 2.每周 3.每月
        public int currencyId;
        public double reward;
        public double turnoverMul;
        public double wagered;
        public long receiveTime;

        public int getCurrencyId() {
            return currencyId;
        }

        public void setCurrencyId(int currencyId) {
            this.currencyId = currencyId;
        }

        public int getId() {
            return id;
        }

        public void setId(int id) {
            this.id = id;
        }

        public double getReward() {
            return reward;
        }

        public void setReward(double reward) {
            this.reward = reward;
        }

        public double getTurnoverMul() {
            return turnoverMul;
        }

        public void setTurnoverMul(double turnoverMul) {
            this.turnoverMul = turnoverMul;
        }

        public double getWagered() {
            return wagered;
        }

        public void setWagered(double wagered) {
            this.wagered = wagered;
        }

        public long getReceiveTime() {
            return receiveTime;
        }

        public void setReceiveTime(long receiveTime) {
            this.receiveTime = receiveTime;
        }
    }
}
