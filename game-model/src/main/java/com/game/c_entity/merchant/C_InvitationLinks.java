package com.game.c_entity.merchant;

import com.game.engine.utils.JsonUtils;
import com.game.po.data.IDataChecker;
import io.netty.util.internal.StringUtil;
import org.bson.types.ObjectId;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Document(collection = "c_invitationLinks")
public class C_InvitationLinks implements IDataChecker {
    @Id
    private ObjectId _id;

    //商户
    private String business_no;

    private int invitationLinksId;

    private String socialMediaName;

    private String icon;

    //是否开启
    private boolean open;

    private List<String> descInfo = new ArrayList<>();

    private transient Map<Integer, DescData> descDataMap = new HashMap<>();

    @Override
    public boolean check() throws Exception {
        if (!descInfo.isEmpty()) {
            for (String desc : descInfo) {
                final DescData data = JsonUtils.readFromJson(desc, DescData.class);
                descDataMap.put(data.getLanguage(), data);
            }
        }
        return true;
    }

    public String getBusiness_no() {
        return business_no;
    }

    public void setBusiness_no(String business_no) {
        this.business_no = business_no;
    }

    public int getInvitationLinksId() {
        return invitationLinksId;
    }

    public void setInvitationLinksId(int invitationLinksId) {
        this.invitationLinksId = invitationLinksId;
    }

    public String getSocialMediaName() {
        return StringUtil.isNullOrEmpty(socialMediaName) ? "" : socialMediaName;
    }

    public void setSocialMediaName(String socialMediaName) {
        this.socialMediaName = socialMediaName;
    }

    public String getIcon() {
        return StringUtil.isNullOrEmpty(icon) ? "" : icon;
    }

    public void setIcon(String icon) {
        this.icon = icon;
    }

    public boolean isOpen() {
        return open;
    }

    public void setOpen(boolean open) {
        this.open = open;
    }

    public List<String> getDescInfo() {
        return descInfo;
    }

    public void setDescInfo(List<String> descInfo) {
        this.descInfo = descInfo;
    }

    public Map<Integer, DescData> getDescDataMap() {
        return descDataMap;
    }

    public void setDescDataMap(Map<Integer, DescData> descDataMap) {
        this.descDataMap = descDataMap;
    }

    public static class DescData {
        public int language;

        public String desc;

        public int getLanguage() {
            return language;
        }

        public void setLanguage(int language) {
            this.language = language;
        }

        public String getDesc() {
            return StringUtil.isNullOrEmpty(desc) ? "" : desc;
        }

        public void setDesc(String desc) {
            this.desc = desc;
        }
    }
}
