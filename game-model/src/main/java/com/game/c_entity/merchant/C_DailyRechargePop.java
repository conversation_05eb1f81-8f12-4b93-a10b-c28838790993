package com.game.c_entity.merchant;

import com.game.engine.utils.JsonUtils;
import com.game.po.data.IDataChecker;
import io.netty.util.internal.StringUtil;
import org.bson.types.ObjectId;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

@Document(collection = "c_dailyRechargePop")
public class C_DailyRechargePop implements IDataChecker {

    @Id
    private ObjectId _id;

    //商户
    private String business_no;

    private int c_id;

    //币种
    private int currencyId;

    private List<String> descData = new ArrayList<>();

    private List<String> gearData = new ArrayList<>();

    private transient Map<Integer, DescInfo> descInfoMap = new LinkedHashMap<>();

    private transient Map<Integer, GearInfo> gearInfoMap = new LinkedHashMap<>();

    @Override
    public boolean check() throws Exception {
        if (this.descData != null && !this.descData.isEmpty()) {
            for (String data : this.descData) {
                final DescInfo descInfo = JsonUtils.readFromJson(data, DescInfo.class);
                this.descInfoMap.put(descInfo.language, descInfo);
            }
        }

        if (this.gearData != null && !this.gearData.isEmpty()) {
            for (String data : this.gearData) {
                final GearInfo gearInfo = JsonUtils.readFromJson(data, GearInfo.class);
                this.gearInfoMap.put(gearInfo.gearId, gearInfo);
            }
        }
        return true;
    }

    public String getBusiness_no() {
        return business_no;
    }

    public void setBusiness_no(String business_no) {
        this.business_no = business_no;
    }

    public int getC_id() {
        return c_id;
    }

    public void setC_id(int c_id) {
        this.c_id = c_id;
    }

    public int getCurrencyId() {
        return currencyId;
    }

    public void setCurrencyId(int currencyId) {
        this.currencyId = currencyId;
    }

    public List<String> getDescData() {
        return descData;
    }

    public void setDescData(List<String> descData) {
        this.descData = descData;
    }

    public Map<Integer, DescInfo> getDescInfoMap() {
        return descInfoMap;
    }

    public void setDescInfoMap(Map<Integer, DescInfo> descInfoMap) {
        this.descInfoMap = descInfoMap;
    }

    public List<String> getGearData() {
        return gearData;
    }

    public void setGearData(List<String> gearData) {
        this.gearData = gearData;
    }

    public Map<Integer, GearInfo> getGearInfoMap() {
        return gearInfoMap;
    }

    public void setGearInfoMap(Map<Integer, GearInfo> gearInfoMap) {
        this.gearInfoMap = gearInfoMap;
    }

    public static class DescInfo {
        public int language;

        public String title;

        public String desc;

        public String getTitle() {
            return StringUtil.isNullOrEmpty(title) ? "" : title;
        }

        public void setTitle(String title) {
            this.title = title;
        }

        public int getLanguage() {
            return language;
        }

        public void setLanguage(int language) {
            this.language = language;
        }

        public String getDesc() {
            return StringUtil.isNullOrEmpty(desc) ? "" : desc;
        }

        public void setDesc(String desc) {
            this.desc = desc;
        }
    }

    public static class GearInfo {
        public int gearId;

        public double amount;

        public double giftRatio;

        public String icon;

        public double minRecharge;

        public double maxRecharge;

        public int getGearId() {
            return gearId;
        }

        public void setGearId(int gearId) {
            this.gearId = gearId;
        }

        public double getAmount() {
            return amount;
        }

        public void setAmount(double amount) {
            this.amount = amount;
        }

        public double getGiftRatio() {
            return giftRatio;
        }

        public void setGiftRatio(double giftRatio) {
            this.giftRatio = giftRatio;
        }

        public String getIcon() {
            return StringUtil.isNullOrEmpty(icon) ? "" : icon;
        }

        public void setIcon(String icon) {
            this.icon = icon;
        }

        public double getMinRecharge() {
            return minRecharge;
        }

        public void setMinRecharge(double minRecharge) {
            this.minRecharge = minRecharge;
        }

        public double getMaxRecharge() {
            return maxRecharge;
        }

        public void setMaxRecharge(double maxRecharge) {
            this.maxRecharge = maxRecharge;
        }
    }
}
