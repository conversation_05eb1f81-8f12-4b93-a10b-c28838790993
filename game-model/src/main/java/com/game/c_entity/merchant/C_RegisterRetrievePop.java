package com.game.c_entity.merchant;

import com.game.engine.utils.JsonUtils;
import com.game.po.data.IDataChecker;
import io.netty.util.internal.StringUtil;
import org.bson.types.ObjectId;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

@Document(collection = "c_registerRetrievePop")
public class C_RegisterRetrievePop implements IDataChecker {

    @Id
    private ObjectId _id;

    //商户
    private String business_no;

    private int c_id;

    //未登录是否可以跳转
    private boolean notLoginJump;

    //1-有 0-无
    private int isJump;

    //跳转类型 1.内连 2.外链
    private int jumpType;

    //弹框类型 1.任务 2.转盘 3.充值 4.客服 5.登录 6.注册
    private int popupLinks;

    //内部链接
    private String innerLinks;

    //外链接
    private String externalLinks;

    private boolean status;

    private List<String> registerData = new ArrayList<>();

    private transient Map<Integer, RegisterInfo> registerInfoMap = new LinkedHashMap<>();

    @Override
    public boolean check() throws Exception {
        if (this.registerData != null && !this.registerData.isEmpty()) {
            for (String data : this.registerData) {
                final RegisterInfo registerInfo = JsonUtils.readFromJson(data, RegisterInfo.class);
                this.registerInfoMap.put(registerInfo.language, registerInfo);
            }
        }
        return true;
    }

    public String getBusiness_no() {
        return business_no;
    }

    public void setBusiness_no(String business_no) {
        this.business_no = business_no;
    }

    public int getC_id() {
        return c_id;
    }

    public void setC_id(int c_id) {
        this.c_id = c_id;
    }

    public List<String> getRegisterData() {
        return registerData;
    }

    public void setRegisterData(List<String> registerData) {
        this.registerData = registerData;
    }

    public Map<Integer, RegisterInfo> getRegisterInfoMap() {
        return registerInfoMap;
    }

    public void setRegisterInfoMap(Map<Integer, RegisterInfo> registerInfoMap) {
        this.registerInfoMap = registerInfoMap;
    }

    public int getJumpType() {
        return jumpType;
    }

    public void setJumpType(int jumpType) {
        this.jumpType = jumpType;
    }

    public boolean isNotLoginJump() {
        return notLoginJump;
    }

    public void setNotLoginJump(boolean notLoginJump) {
        this.notLoginJump = notLoginJump;
    }

    public int getIsJump() {
        return isJump;
    }

    public void setIsJump(int isJump) {
        this.isJump = isJump;
    }

    public int getPopupLinks() {
        return popupLinks;
    }

    public void setPopupLinks(int popupLinks) {
        this.popupLinks = popupLinks;
    }

    public String getInnerLinks() {
        return innerLinks;
    }

    public void setInnerLinks(String innerLinks) {
        this.innerLinks = innerLinks;
    }

    public String getExternalLinks() {
        return externalLinks;
    }

    public void setExternalLinks(String externalLinks) {
        this.externalLinks = externalLinks;
    }

    public boolean isStatus() {
        return status;
    }

    public void setStatus(boolean status) {
        this.status = status;
    }

    public static class RegisterInfo {
        public int language;

        public String desc;

        public String text;

        public String icon;

        public String getDesc() {
            return StringUtil.isNullOrEmpty(desc) ? "" : desc;
        }

        public void setDesc(String desc) {
            this.desc = desc;
        }

        public String getText() {
            return StringUtil.isNullOrEmpty(text) ? "" : text;
        }

        public void setText(String text) {
            this.text = text;
        }

        public String getIcon() {
            return StringUtil.isNullOrEmpty(icon) ? "" : icon;
        }

        public void setIcon(String icon) {
            this.icon = icon;
        }

        public int getLanguage() {
            return language;
        }

        public void setLanguage(int language) {
            this.language = language;
        }

    }
}
