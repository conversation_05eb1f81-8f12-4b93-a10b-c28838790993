package com.game.c_entity.merchant;

import com.game.po.data.IDataChecker;
import org.bson.types.ObjectId;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

/**
 * 0 不限次数
 */
@Document(collection = "c_rechargeWithdrawLimit")
public class C_RechargeWithdrawLimit implements IDataChecker {

    @Id
    private ObjectId _id;

    //商户
    private String business_no;

    //货币id
    private int currencyId;

    //货币类型
    private int currencyType;

    //1.达到要求洗码量提现 2.按洗码量完成比例提现 3.打码量按表格
    private int withdrawType;

    //单日免手续费
    private int dailyFreeTimes;

    //单日提现上限
    private int dailyWithdrawUpperLimit;

    //单日提现次数
    private int dailyWithdrawTimes;

    //需要投注次数
    private int needBetTimes;

    /******************************* 充值 *******************************/

    //单日充值次数
    private int dailyRechargeTimes;

    //单日充值上限
    private int dailyRechargeUpperLimit;

    private long deleteTime;

    public String getBusiness_no() {
        return business_no;
    }

    public void setBusiness_no(String business_no) {
        this.business_no = business_no;
    }

    public int getCurrencyId() {
        return currencyId;
    }

    public void setCurrencyId(int currencyId) {
        this.currencyId = currencyId;
    }

    public int getCurrencyType() {
        return currencyType;
    }

    public void setCurrencyType(int currencyType) {
        this.currencyType = currencyType;
    }

    public int getWithdrawType() {
        return withdrawType;
    }

    public void setWithdrawType(int withdrawType) {
        this.withdrawType = withdrawType;
    }

    public int getDailyFreeTimes() {
        return dailyFreeTimes;
    }

    public void setDailyFreeTimes(int dailyFreeTimes) {
        this.dailyFreeTimes = dailyFreeTimes;
    }

    public int getDailyWithdrawUpperLimit() {
        return dailyWithdrawUpperLimit;
    }

    public void setDailyWithdrawUpperLimit(int dailyWithdrawUpperLimit) {
        this.dailyWithdrawUpperLimit = dailyWithdrawUpperLimit;
    }

    public int getDailyWithdrawTimes() {
        return dailyWithdrawTimes;
    }

    public void setDailyWithdrawTimes(int dailyWithdrawTimes) {
        this.dailyWithdrawTimes = dailyWithdrawTimes;
    }

    public int getNeedBetTimes() {
        return needBetTimes;
    }

    public void setNeedBetTimes(int needBetTimes) {
        this.needBetTimes = needBetTimes;
    }

    public int getDailyRechargeTimes() {
        return dailyRechargeTimes;
    }

    public void setDailyRechargeTimes(int dailyRechargeTimes) {
        this.dailyRechargeTimes = dailyRechargeTimes;
    }

    public int getDailyRechargeUpperLimit() {
        return dailyRechargeUpperLimit;
    }

    public void setDailyRechargeUpperLimit(int dailyRechargeUpperLimit) {
        this.dailyRechargeUpperLimit = dailyRechargeUpperLimit;
    }

    public long getDeleteTime() {
        return deleteTime;
    }

    public void setDeleteTime(long deleteTime) {
        this.deleteTime = deleteTime;
    }

    @Override
    public boolean check() throws Exception {
        return true;
    }
}
