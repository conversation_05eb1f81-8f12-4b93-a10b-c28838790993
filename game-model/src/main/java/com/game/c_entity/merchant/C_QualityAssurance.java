package com.game.c_entity.merchant;

import com.game.engine.utils.JsonUtils;
import com.game.po.data.IDataChecker;
import io.netty.util.internal.StringUtil;
import org.bson.types.ObjectId;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

@Document(collection = "c_qualityAssurance")
public class C_QualityAssurance implements IDataChecker {

    @Id
    private ObjectId _id;

    //商户
    private String business_no;

    private int qualityAssuranceId;

    //1.VIP CLUB 2.Affiliate 3.Weekly Raffle
    private int qualityAssuranceType;

    private List<String> qualityAssuranceData;

    private transient final Map<Integer, QualityAssuranceInfo> qualityAssuranceMap = new LinkedHashMap<>();

    @Override
    public boolean check() throws Exception {
        if (this.qualityAssuranceData != null && !this.qualityAssuranceData.isEmpty()) {
            for (String data : this.qualityAssuranceData) {
                final QualityAssuranceInfo qualityAssuranceInfo = JsonUtils.readFromJson(data, QualityAssuranceInfo.class);
                qualityAssuranceMap.put(qualityAssuranceInfo.getLanguage(), qualityAssuranceInfo);
            }
        }
        return true;
    }

    public String getBusiness_no() {
        return business_no;
    }

    public void setBusiness_no(String business_no) {
        this.business_no = business_no;
    }

    public int getQualityAssuranceId() {
        return qualityAssuranceId;
    }

    public void setQualityAssuranceId(int qualityAssuranceId) {
        this.qualityAssuranceId = qualityAssuranceId;
    }

    public int getQualityAssuranceType() {
        return qualityAssuranceType;
    }

    public void setQualityAssuranceType(int qualityAssuranceType) {
        this.qualityAssuranceType = qualityAssuranceType;
    }

    public List<String> getQualityAssuranceData() {
        return qualityAssuranceData;
    }

    public void setQualityAssuranceData(List<String> qualityAssuranceData) {
        this.qualityAssuranceData = qualityAssuranceData;
    }

    public Map<Integer, QualityAssuranceInfo> getQualityAssuranceMap() {
        return qualityAssuranceMap;
    }

    public static class QualityAssuranceInfo {
        public int language;

        private List<String> questionData = new ArrayList<>();

        public int getLanguage() {
            return language;
        }

        public void setLanguage(int language) {
            this.language = language;
        }

        public List<String> getQuestionData() {
            return questionData;
        }

        public void setQuestionData(List<String> questionData) {
            this.questionData = questionData;
        }
    }

    public static class QuestionInfo {
        public String question;

        public String answer;

        public String getQuestion() {
            return StringUtil.isNullOrEmpty(question) ? "" : question;
        }

        public void setQuestion(String question) {
            this.question = question;
        }

        public String getAnswer() {
            return StringUtil.isNullOrEmpty(answer) ? "" : answer;
        }

        public void setAnswer(String answer) {
            this.answer = answer;
        }
    }
}
