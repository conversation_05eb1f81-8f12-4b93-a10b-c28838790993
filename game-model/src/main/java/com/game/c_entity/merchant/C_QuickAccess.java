package com.game.c_entity.merchant;

import com.game.engine.utils.JsonUtils;
import com.game.po.data.IDataChecker;
import io.netty.util.internal.StringUtil;
import org.bson.types.ObjectId;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

@Document(collection = "c_quickAccess")
public class C_QuickAccess implements IDataChecker {

    @Id
    private ObjectId _id;

    //商户号
    private String business_no;

    private int quickAccessId;

    private List<String> webSites;

    //入口名称
    private String entranceName;

    //入口类型 1.大入口 2.小入口
    private int entranceType;

    //图片
    private String imageUrl;

    private int isJump;

    //跳转类型 1.内连 2.外链
    private int jumpType;

    //弹框类型 1.任务 2.转盘 3.充值 4.客服 5.登录 6.注册
    private int popupLinks;

    //内部链接
    private String innerLinks;

    //外链接
    private String externalLinks;

    private List<String> entranceData;

    private transient final Map<Integer, EntranceData> entranceDataMap = new LinkedHashMap<>();

    public Map<Integer, EntranceData> getEntranceDataMap() {
        return entranceDataMap;
    }

    @Override
    public boolean check() throws Exception {
        if (entranceData != null && !entranceData.isEmpty()) {
            for (String data : entranceData) {
                final EntranceData entrance = JsonUtils.readFromJson(data, EntranceData.class);
                entranceDataMap.put(entrance.getLanguage(), entrance);
            }
        }
        return true;
    }

    public String getBusiness_no() {
        return business_no;
    }

    public void setBusiness_no(String business_no) {
        this.business_no = business_no;
    }

    public int getQuickAccessId() {
        return quickAccessId;
    }

    public void setQuickAccessId(int quickAccessId) {
        this.quickAccessId = quickAccessId;
    }

    public List<String> getWebSites() {
        return webSites;
    }

    public void setWebSites(List<String> webSites) {
        this.webSites = webSites;
    }

    public String getEntranceName() {
        return StringUtil.isNullOrEmpty(entranceName) ? "" : entranceName;
    }

    public void setEntranceName(String entranceName) {
        this.entranceName = entranceName;
    }

    public int getIsJump() {
        return isJump;
    }

    public void setIsJump(int isJump) {
        this.isJump = isJump;
    }

    public int getEntranceType() {
        return entranceType;
    }

    public void setEntranceType(int entranceType) {
        this.entranceType = entranceType;
    }

    public String getImageUrl() {
        return imageUrl;
    }

    public void setImageUrl(String imageUrl) {
        this.imageUrl = imageUrl;
    }

    public int getPopupLinks() {
        return popupLinks;
    }

    public void setPopupLinks(int popupLinks) {
        this.popupLinks = popupLinks;
    }

    public int getJumpType() {
        return jumpType;
    }

    public void setJumpType(int jumpType) {
        this.jumpType = jumpType;
    }

    public String getInnerLinks() {
        return StringUtil.isNullOrEmpty(innerLinks) ? "" : innerLinks;
    }

    public void setInnerLinks(String innerLinks) {
        this.innerLinks = innerLinks;
    }

    public String getExternalLinks() {
        return StringUtil.isNullOrEmpty(externalLinks) ? "" : externalLinks;
    }

    public void setExternalLinks(String externalLinks) {
        this.externalLinks = externalLinks;
    }

    public List<String> getEntranceData() {
        return entranceData;
    }

    public void setEntranceData(List<String> entranceData) {
        this.entranceData = entranceData;
    }

    public static class EntranceData {
        private int language;

        private String entranceName;

        private int sort;

        public int getLanguage() {
            return language;
        }

        public void setLanguage(int language) {
            this.language = language;
        }

        public String getEntranceName() {
            return StringUtil.isNullOrEmpty(entranceName) ? "" : entranceName;
        }

        public void setEntranceName(String entranceName) {
            this.entranceName = entranceName;
        }

        public int getSort() {
            return sort;
        }

        public void setSort(int sort) {
            this.sort = sort;
        }
    }
}
