package com.game.c_entity.merchant;

import com.game.po.data.IDataChecker;
import org.bson.types.ObjectId;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

@Document(collection = "c_subChannelGameApi")
public class C_SubChannelGameApi implements IDataChecker {
    @Id
    private ObjectId _id;

    //1.casino 2.sport 3.Lottery
    private int channelType;

    //子频道 channel*100 自增
    private int subChannel;

    private int gameId;

    private int seq;

    private int baseStatus;

    private boolean status;

    public int getChannelType() {
        return channelType;
    }

    public void setChannelType(int channelType) {
        this.channelType = channelType;
    }

    public int getSubChannel() {
        return subChannel;
    }

    public void setSubChannel(int subChannel) {
        this.subChannel = subChannel;
    }

    public int getGameId() {
        return gameId;
    }

    public void setGameId(int gameId) {
        this.gameId = gameId;
    }

    public int getSeq() {
        return seq;
    }

    public void setSeq(int seq) {
        this.seq = seq;
    }

    public int getBaseStatus() {
        return baseStatus;
    }

    public void setBaseStatus(int baseStatus) {
        this.baseStatus = baseStatus;
    }

    public boolean isStatus() {
        return status;
    }

    public void setStatus(boolean status) {
        this.status = status;
    }
}
