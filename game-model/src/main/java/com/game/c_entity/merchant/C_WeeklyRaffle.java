package com.game.c_entity.merchant;

import com.game.engine.utils.JsonUtils;
import com.game.po.data.IDataChecker;
import io.netty.util.internal.StringUtil;
import org.bson.types.ObjectId;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.ArrayList;
import java.util.List;

@Document(collection = "c_weeklyRaffle")
public class C_WeeklyRaffle implements IDataChecker {
    @Id
    private ObjectId _id;

    //商户
    private String business_no;

    private int activityId;

    //奖励币种
    private int rewardCurrency;

    //奖励值
    private int reward;

    //奖励打码倍率
    private double rewardTurnoverMul;

    //参与货币
    private List<Integer> joinCurrency = new ArrayList<>();

    //每日下注值
    private double dailyUsdWagered;

    //每日单次获取票数
//    private int dailySingleTickets;

    //每日最多获取票数
    private int maxDailyTickets;

    //每下注值
    private double everyUsdWagered;

    //每周单次获取票数
//    private int weeklySingleTickets;

    //最多总共票数
    private int maxTotalTickets;

    //奖励分配
    private List<String> rewardAllocations;

    private transient List<RewardAllocation> rewardAllocationList = new ArrayList<>();

    @Override
    public boolean check() throws Exception {
        if (rewardAllocations != null && !rewardAllocations.isEmpty()) {
            for (String data : rewardAllocations) {
                rewardAllocationList.add(JsonUtils.readFromJson(data, RewardAllocation.class));
            }
        }
        return true;
    }

    public String getBusiness_no() {
        return business_no;
    }

    public void setBusiness_no(String business_no) {
        this.business_no = business_no;
    }

    public int getActivityId() {
        return activityId;
    }

    public void setActivityId(int activityId) {
        this.activityId = activityId;
    }

    public int getRewardCurrency() {
        return rewardCurrency;
    }

    public void setRewardCurrency(int rewardCurrency) {
        this.rewardCurrency = rewardCurrency;
    }

    public int getReward() {
        return reward;
    }

    public void setReward(int reward) {
        this.reward = reward;
    }

    public double getRewardTurnoverMul() {
        return rewardTurnoverMul;
    }

    public void setRewardTurnoverMul(double rewardTurnoverMul) {
        this.rewardTurnoverMul = rewardTurnoverMul;
    }

    public List<Integer> getJoinCurrency() {
        return joinCurrency;
    }

    public void setJoinCurrency(List<Integer> joinCurrency) {
        this.joinCurrency = joinCurrency;
    }

    public double getDailyUsdWagered() {
        return dailyUsdWagered;
    }

    public void setDailyUsdWagered(double dailyUsdWagered) {
        this.dailyUsdWagered = dailyUsdWagered;
    }

    public int getMaxDailyTickets() {
        return maxDailyTickets;
    }

    public void setMaxDailyTickets(int maxDailyTickets) {
        this.maxDailyTickets = maxDailyTickets;
    }

    public double getEveryUsdWagered() {
        return everyUsdWagered;
    }

    public void setEveryUsdWagered(double everyUsdWagered) {
        this.everyUsdWagered = everyUsdWagered;
    }

    public int getMaxTotalTickets() {
        return maxTotalTickets;
    }

    public void setMaxTotalTickets(int maxTotalTickets) {
        this.maxTotalTickets = maxTotalTickets;
    }

    public List<String> getRewardAllocations() {
        return rewardAllocations;
    }

    public void setRewardAllocations(List<String> rewardAllocations) {
        this.rewardAllocations = rewardAllocations;
    }

    public List<RewardAllocation> getRewardAllocationList() {
        return rewardAllocationList;
    }

    public void setRewardAllocationList(List<RewardAllocation> rewardAllocationList) {
        this.rewardAllocationList = rewardAllocationList;
    }

    public static class RewardAllocation {
        private int rewardLevels;

        private int num;

        private int currencyId;

        private double reward;

        public int getRewardLevels() {
            return rewardLevels;
        }

        public void setRewardLevels(int rewardLevels) {
            this.rewardLevels = rewardLevels;
        }

        public int getNum() {
            return num;
        }

        public void setNum(int num) {
            this.num = num;
        }

        public int getCurrencyId() {
            return currencyId;
        }

        public void setCurrencyId(int currencyId) {
            this.currencyId = currencyId;
        }

        public double getReward() {
            return reward;
        }

        public void setReward(double reward) {
            this.reward = reward;
        }
    }

}
