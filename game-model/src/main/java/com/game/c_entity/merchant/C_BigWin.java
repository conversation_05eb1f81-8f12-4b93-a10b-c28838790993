package com.game.c_entity.merchant;

import com.game.po.data.IDataChecker;
import org.bson.types.ObjectId;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.List;

@Document(collection = "c_bigWin")
public class C_BigWin implements IDataChecker {

    @Id
    private ObjectId _id;

    //商户号
    private String business_no;

    private int gameId;

    private String gameIcon;

    private String gameName;

    private int platformId;

    private String platformName;

    private int gameType;

    private List<Integer> currencys;

    private double lowAmount;

    @Override
    public boolean check() throws Exception {
        return true;
    }

    public String getBusiness_no() {
        return business_no;
    }

    public void setBusiness_no(String business_no) {
        this.business_no = business_no;
    }

    public int getGameId() {
        return gameId;
    }

    public void setGameId(int gameId) {
        this.gameId = gameId;
    }

    public String getGameIcon() {
        return gameIcon;
    }

    public void setGameIcon(String gameIcon) {
        this.gameIcon = gameIcon;
    }

    public String getGameName() {
        return gameName;
    }

    public void setGameName(String gameName) {
        this.gameName = gameName;
    }

    public int getPlatformId() {
        return platformId;
    }

    public void setPlatformId(int platformId) {
        this.platformId = platformId;
    }

    public String getPlatformName() {
        return platformName;
    }

    public void setPlatformName(String platformName) {
        this.platformName = platformName;
    }

    public int getGameType() {
        return gameType;
    }

    public void setGameType(int gameType) {
        this.gameType = gameType;
    }

    public List<Integer> getCurrencys() {
        return currencys;
    }

    public void setCurrencys(List<Integer> currencys) {
        this.currencys = currencys;
    }

    public double getLowAmount() {
        return lowAmount;
    }

    public void setLowAmount(double lowAmount) {
        this.lowAmount = lowAmount;
    }
}
