package com.game.c_entity.merchant;

import com.game.engine.utils.JsonUtils;
import com.game.po.data.IDataChecker;
import io.netty.util.internal.StringUtil;
import org.bson.types.ObjectId;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Document(collection = "c_quest")
public class C_Quest implements IDataChecker {
    @Id
    private ObjectId _id;

    //商户
    @Indexed
    private String business_no;

    @Indexed
    private int questId;

    //图标
    private String icon;

    //vip等级限制
    private int vipLevelLimit;

    //任务类型 1.每日 2.每周
    private int questType;

    //周几
    private List<Integer> dayOfWeek = new ArrayList<>();

    //任务目标 1.每日登录 2.下注
    private int goalType;

    //1.手动 2.自动
    private int receive;

    //游戏目标
    private String gameTargets;

    //任务条件
    private String questConditions;

    //奖励货币
    private String rewardCurrencys;

    //奖励物品
    private String rewardItems;

    //奖励免费转
    private String rewardFreeGameData;

    //打码倍率
    private double turnoverMul;

    //是否开启
    private boolean open;

    //描述
    private List<String> descData = new ArrayList<>();

    private long deleteTime;

    private transient GameTarget gameTarget;

    private transient QuestCondition questCondition;

    private transient RewardCurrency rewardCurrency;

    private transient RewardFreeGame rewardFreeGame;

    private transient Map<Integer, DescInfo> descInfoMap = new HashMap<>();

    @Override
    public boolean check() throws Exception {
        if (!StringUtil.isNullOrEmpty(this.gameTargets)) {
            gameTarget = JsonUtils.readFromJson(this.gameTargets, GameTarget.class);
        }

        if (!StringUtil.isNullOrEmpty(this.questConditions)) {
            questCondition = JsonUtils.readFromJson(this.questConditions, QuestCondition.class);
        } else {
            throw new IllegalArgumentException("c_quest，questId：" + this.questId + "，questConditions：" + this.questConditions + "，error");
        }

        if (!StringUtil.isNullOrEmpty(this.rewardCurrencys)) {
            rewardCurrency = JsonUtils.readFromJson(this.rewardCurrencys, RewardCurrency.class);
        }

        if (!StringUtil.isNullOrEmpty(this.rewardFreeGameData)) {
            rewardFreeGame = JsonUtils.readFromJson(this.rewardFreeGameData, RewardFreeGame.class);
        }

        if (!this.descData.isEmpty()) {
            for (String data : this.descData) {
                final DescInfo descInfo = JsonUtils.readFromJson(data, DescInfo.class);
                this.descInfoMap.put(descInfo.language, descInfo);
            }
        }
        return true;
    }

    public String getBusiness_no() {
        return business_no;
    }

    public void setBusiness_no(String business_no) {
        this.business_no = business_no;
    }

    public int getQuestId() {
        return questId;
    }

    public void setQuestId(int questId) {
        this.questId = questId;
    }

    public String getIcon() {
        return StringUtil.isNullOrEmpty(icon) ? "" : icon;
    }

    public void setIcon(String icon) {
        this.icon = icon;
    }

    public int getVipLevelLimit() {
        return vipLevelLimit;
    }

    public void setVipLevelLimit(int vipLevelLimit) {
        this.vipLevelLimit = vipLevelLimit;
    }

    public int getQuestType() {
        return questType;
    }

    public void setQuestType(int questType) {
        this.questType = questType;
    }

    public List<Integer> getDayOfWeek() {
        return dayOfWeek;
    }

    public void setDayOfWeek(List<Integer> dayOfWeek) {
        this.dayOfWeek = dayOfWeek;
    }

    public int getGoalType() {
        return goalType;
    }

    public void setGoalType(int goalType) {
        this.goalType = goalType;
    }

    public int getReceive() {
        return receive;
    }

    public void setReceive(int receive) {
        this.receive = receive;
    }

    public String getGameTargets() {
        return gameTargets;
    }

    public void setGameTargets(String gameTargets) {
        this.gameTargets = gameTargets;
    }

    public String getQuestConditions() {
        return questConditions;
    }

    public void setQuestConditions(String questConditions) {
        this.questConditions = questConditions;
    }

    public String getRewardCurrencys() {
        return rewardCurrencys;
    }

    public void setRewardCurrencys(String rewardCurrencys) {
        this.rewardCurrencys = rewardCurrencys;
    }

    public RewardFreeGame getRewardFreeGame() {
        return rewardFreeGame;
    }

    public void setRewardFreeGame(RewardFreeGame rewardFreeGame) {
        this.rewardFreeGame = rewardFreeGame;
    }

    public String getRewardItems() {
        return rewardItems;
    }

    public void setRewardItems(String rewardItems) {
        this.rewardItems = rewardItems;
    }

    public String getRewardFreeGameData() {
        return rewardFreeGameData;
    }

    public void setRewardFreeGameData(String rewardFreeGameData) {
        this.rewardFreeGameData = rewardFreeGameData;
    }

    public double getTurnoverMul() {
        return turnoverMul;
    }

    public void setTurnoverMul(double turnoverMul) {
        this.turnoverMul = turnoverMul;
    }

    public List<String> getDescData() {
        return descData;
    }

    public void setDescData(List<String> descData) {
        this.descData = descData;
    }

    public Map<Integer, DescInfo> getDescInfoMap() {
        return descInfoMap;
    }

    public void setDescInfoMap(Map<Integer, DescInfo> descInfoMap) {
        this.descInfoMap = descInfoMap;
    }

    public boolean isOpen() {
        return open;
    }

    public void setOpen(boolean open) {
        this.open = open;
    }

    public long getDeleteTime() {
        return deleteTime;
    }

    public void setDeleteTime(long deleteTime) {
        this.deleteTime = deleteTime;
    }

    public GameTarget getGameTarget() {
        return gameTarget;
    }

    public void setGameTarget(GameTarget gameTarget) {
        this.gameTarget = gameTarget;
    }

    public QuestCondition getQuestCondition() {
        return questCondition;
    }

    public void setQuestCondition(QuestCondition questCondition) {
        this.questCondition = questCondition;
    }

    public RewardCurrency getRewardCurrency() {
        return rewardCurrency;
    }

    public void setRewardCurrency(RewardCurrency rewardCurrency) {
        this.rewardCurrency = rewardCurrency;
    }

    public static class GameTarget {
        public int channelId;

        public int getChannelId() {
            return channelId;
        }

        public void setChannelId(int channelId) {
            this.channelId = channelId;
        }
    }

    public static class QuestCondition {
        public int totalProgressive;

        public int param1;

        public int param2;

        public List<Integer> param3 = new ArrayList<>();

        public int getTotalProgressive() {
            return totalProgressive;
        }

        public void setTotalProgressive(int totalProgressive) {
            this.totalProgressive = totalProgressive;
        }

        public int getParam1() {
            return param1;
        }

        public void setParam1(int param1) {
            this.param1 = param1;
        }

        public int getParam2() {
            return param2;
        }

        public void setParam2(int param2) {
            this.param2 = param2;
        }

        public List<Integer> getParam3() {
            return param3;
        }

        public void setParam3(List<Integer> param3) {
            this.param3 = param3;
        }
    }

    public static class RewardCurrency {
        public int currencyId;

        public double amount;

        public int getCurrencyId() {
            return currencyId;
        }

        public void setCurrencyId(int currencyId) {
            this.currencyId = currencyId;
        }

        public double getAmount() {
            return amount;
        }

        public void setAmount(int amount) {
            this.amount = amount;
        }
    }

    public static class RewardItem {
        public int itemId;

        public int num;

        public int getItemId() {
            return itemId;
        }

        public void setItemId(int itemId) {
            this.itemId = itemId;
        }

        public int getNum() {
            return num;
        }

        public void setNum(int num) {
            this.num = num;
        }
    }

    public static class RewardFreeGame {
        public int currencyId;
        public int gameId;
        public int freeTimes;
        public double bet;
        public double minWithdraw;
        public double maxWithdraw;

        public int getCurrencyId() {
            return currencyId;
        }

        public void setCurrencyId(int currencyId) {
            this.currencyId = currencyId;
        }

        public int getGameId() {
            return gameId;
        }

        public void setGameId(int gameId) {
            this.gameId = gameId;
        }

        public int getFreeTimes() {
            return freeTimes;
        }

        public void setFreeTimes(int freeTimes) {
            this.freeTimes = freeTimes;
        }

        public double getBet() {
            return bet;
        }

        public void setBet(double bet) {
            this.bet = bet;
        }

        public double getMinWithdraw() {
            return minWithdraw;
        }

        public void setMinWithdraw(double minWithdraw) {
            this.minWithdraw = minWithdraw;
        }

        public double getMaxWithdraw() {
            return maxWithdraw;
        }

        public void setMaxWithdraw(double maxWithdraw) {
            this.maxWithdraw = maxWithdraw;
        }
    }

    public static class DescInfo {
        public int language;

        //任务名字
        public String questName;

        //描述
        public String desc;

        public int getLanguage() {
            return language;
        }

        public void setLanguage(int language) {
            this.language = language;
        }

        public String getQuestName() {
            return StringUtil.isNullOrEmpty(questName) ? "" : questName;
        }

        public void setQuestName(String questName) {
            this.questName = questName;
        }

        public String getDesc() {
            return StringUtil.isNullOrEmpty(desc) ? "" : desc;
        }

        public void setDesc(String desc) {
            this.desc = desc;
        }
    }
}
