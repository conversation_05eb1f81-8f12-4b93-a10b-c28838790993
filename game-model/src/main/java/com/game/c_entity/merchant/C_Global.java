package com.game.c_entity.merchant;

import com.game.po.data.IDataChecker;
import org.bson.types.ObjectId;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

@Document(collection = "c_global")
public class C_Global implements IDataChecker {

    @Id
    private ObjectId _id;

    //商户
    private String business_no;

    private int globalId;

    private String name;

    private String value;

    private long deleteTime;

    public String getBusiness_no() {
        return business_no;
    }

    public void setBusiness_no(String business_no) {
        this.business_no = business_no;
    }

    public int getGlobalId() {
        return globalId;
    }

    public void setGlobalId(int globalId) {
        this.globalId = globalId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    @Override
    public boolean check() throws Exception {
//        if (StringUtil.isNullOrEmpty(this.name)) {
//            throw new IllegalArgumentException("name 为NULL，需要填写");
//        }
//        if (StringUtil.isNullOrEmpty(this.value)) {
//            throw new IllegalArgumentException("value 为NULL，需要填写");
//        }
        return true;
    }

    public long getDeleteTime() {
        return deleteTime;
    }

    public void setDeleteTime(long deleteTime) {
        this.deleteTime = deleteTime;
    }
}
