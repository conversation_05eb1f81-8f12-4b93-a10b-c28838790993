package com.game.c_entity.merchant;

import com.game.po.data.IDataChecker;
import io.netty.util.internal.StringUtil;
import org.bson.types.ObjectId;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.List;

@Document(collection = "c_bulletin")
public class C_Bulletin implements IDataChecker {

    @Id
    private ObjectId _id;

    //商户号
    private String business_no;

    private int bulletinId;

    //1.维护
    private int bulletinType;

    //标题
    private String title;

    //排序
    private int showSeq;

    //按钮 1.有 0.无
    private int button;

    //按钮文字
    private String buttonWord;

    //语言
    private List<String> language;

    //跳转类型 1.内连 2.外链
    private int jumpType;

    //跳转链接
    private String url;

    //图片地址
    private String fileUrl;

    //内容
    private String content;

    //开始时间
    private long startTime;

    //结束时间
    private long endTime;

    //状态 0.关 1.开
    private int status;

    private long deleteTime;

    @Override
    public boolean check() throws Exception {
        return true;
    }

    public String getBusiness_no() {
        return business_no;
    }

    public void setBusiness_no(String business_no) {
        this.business_no = business_no;
    }

    public int getBulletinId() {
        return bulletinId;
    }

    public void setBulletinId(int bulletinId) {
        this.bulletinId = bulletinId;
    }

    public int getBulletinType() {
        return bulletinType;
    }

    public void setBulletinType(int bulletinType) {
        this.bulletinType = bulletinType;
    }

    public String getTitle() {
        return StringUtil.isNullOrEmpty(title) ? "" : title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public int getShowSeq() {
        return showSeq;
    }

    public void setShowSeq(int showSeq) {
        this.showSeq = showSeq;
    }

    public int getButton() {
        return button;
    }

    public void setButton(int button) {
        this.button = button;
    }

    public String getButtonWord() {
        return StringUtil.isNullOrEmpty(buttonWord) ? "" : buttonWord;
    }

    public void setButtonWord(String buttonWord) {
        this.buttonWord = buttonWord;
    }

    public List<String> getLanguage() {
        return language;
    }

    public void setLanguage(List<String> language) {
        this.language = language;
    }

    public int getJumpType() {
        return jumpType;
    }

    public void setJumpType(int jumpType) {
        this.jumpType = jumpType;
    }

    public String getUrl() {
        return StringUtil.isNullOrEmpty(url) ? "" : url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public String getFileUrl() {
        return StringUtil.isNullOrEmpty(fileUrl) ? "" : fileUrl;
    }

    public void setFileUrl(String fileUrl) {
        this.fileUrl = fileUrl;
    }

    public String getContent() {
        return StringUtil.isNullOrEmpty(content) ? "" : content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public long getStartTime() {
        return startTime;
    }

    public void setStartTime(long startTime) {
        this.startTime = startTime;
    }

    public long getEndTime() {
        return endTime;
    }

    public void setEndTime(long endTime) {
        this.endTime = endTime;
    }

    public int getStatus() {
        return status;
    }

    public void setStatus(int status) {
        this.status = status;
    }

    public long getDeleteTime() {
        return deleteTime;
    }

    public void setDeleteTime(long deleteTime) {
        this.deleteTime = deleteTime;
    }
}
