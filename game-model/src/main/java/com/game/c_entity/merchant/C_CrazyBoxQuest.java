package com.game.c_entity.merchant;

import com.game.engine.utils.JsonUtils;
import com.game.po.data.IDataChecker;
import io.netty.util.internal.StringUtil;
import org.bson.types.ObjectId;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.*;

@Document(collection = "c_crazyBoxQuest")
public class C_CrazyBoxQuest implements IDataChecker {

    @Id
    private ObjectId _id;

    //商户
    private String business_no;

    private int questId;

    private String timeZone;

    //任务类型 1.每日 2.每周
    private int questType;

    private int rewardTimes;

    private String questConditionData;

    private List<String> questDescData = new ArrayList<>();

    //内链
    private String innerLink;

    //领取方式 1.手动
    private int receiveWay;

    //图标
    private String icon;

    private boolean open;

    private transient QuestCondition questCondition;
    private transient final Map<Integer, QuestDesc> questDescMap = new LinkedHashMap<>();

    @Override
    public boolean check() throws Exception {
        if (!StringUtil.isNullOrEmpty(questConditionData)) {
            questCondition = JsonUtils.readFromJson(questConditionData, QuestCondition.class);
        }

        if (!questDescData.isEmpty()) {
            for (String data : questDescData) {
                final QuestDesc questDesc = JsonUtils.readFromJson(data, QuestDesc.class);
                questDescMap.put(questDesc.language, questDesc);
            }
        }
        return true;
    }

    public static class QuestDesc {
        public int language;
        public String questName;
        public String questDesc;

        public int getLanguage() {
            return language;
        }

        public void setLanguage(int language) {
            this.language = language;
        }

        public String getQuestName() {
            return questName;
        }

        public void setQuestName(String questName) {
            this.questName = questName;
        }

        public String getQuestDesc() {
            return questDesc;
        }

        public void setQuestDesc(String questDesc) {
            this.questDesc = questDesc;
        }
    }

    public static class QuestCondition {

        public int totalProgressive;

        public List<Integer> param1 = new ArrayList<>();

        public List<Integer> param2 = new ArrayList<>();

        public int param3;

        public double param4;

        public int getTotalProgressive() {
            return totalProgressive;
        }

        public void setTotalProgressive(int totalProgressive) {
            this.totalProgressive = totalProgressive;
        }

        public List<Integer> getParam1() {
            return param1;
        }

        public void setParam1(List<Integer> param1) {
            this.param1 = param1;
        }

        public List<Integer> getParam2() {
            return param2;
        }

        public void setParam2(List<Integer> param2) {
            this.param2 = param2;
        }

        public int getParam3() {
            return param3;
        }

        public void setParam3(int param3) {
            this.param3 = param3;
        }

        public double getParam4() {
            return param4;
        }

        public void setParam4(double param4) {
            this.param4 = param4;
        }
    }

    public int getQuestType() {
        return questType;
    }

    public void setQuestType(int questType) {
        this.questType = questType;
    }

    public String getBusiness_no() {
        return business_no;
    }

    public void setBusiness_no(String business_no) {
        this.business_no = business_no;
    }

    public int getQuestId() {
        return questId;
    }

    public void setQuestId(int questId) {
        this.questId = questId;
    }

    public String getTimeZone() {
        return timeZone;
    }

    public void setTimeZone(String timeZone) {
        this.timeZone = timeZone;
    }

    public int getRewardTimes() {
        return rewardTimes;
    }

    public void setRewardTimes(int rewardTimes) {
        this.rewardTimes = rewardTimes;
    }

    public String getQuestConditionData() {
        return questConditionData;
    }

    public void setQuestConditionData(String questConditionData) {
        this.questConditionData = questConditionData;
    }

    public List<String> getQuestDescData() {
        return questDescData;
    }

    public void setQuestDescData(List<String> questDescData) {
        this.questDescData = questDescData;
    }

    public String getInnerLink() {
        return innerLink;
    }

    public void setInnerLink(String innerLink) {
        this.innerLink = innerLink;
    }

    public int getReceiveWay() {
        return receiveWay;
    }

    public void setReceiveWay(int receiveWay) {
        this.receiveWay = receiveWay;
    }

    public String getIcon() {
        return icon;
    }

    public void setIcon(String icon) {
        this.icon = icon;
    }

    public boolean isOpen() {
        return open;
    }

    public void setOpen(boolean open) {
        this.open = open;
    }

    public QuestCondition getQuestCondition() {
        return questCondition;
    }

    public void setQuestCondition(QuestCondition questCondition) {
        this.questCondition = questCondition;
    }

    public Map<Integer, QuestDesc> getQuestDescMap() {
        return questDescMap;
    }
}
