package com.game.c_entity.merchant;


import com.game.po.data.IDataChecker;
import org.bson.types.ObjectId;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

@Document(collection = "c_rechargeTurnover")
public class C_RechargeTurnover implements IDataChecker {
    @Id
    private ObjectId _id;

    //商户
    private String business_no;

    private int turnoverId;

    //货币id
    private int currencyId;

    //打码量倍率
    private double turnoverRate;

    //小于多少比例余额充值清打码量
    private double clearRate;

    //小于多少余额充值清打码量
    private double clearValue;

    private long deleteTime;

    @Override
    public boolean check() throws Exception {
        return true;
    }

    public String getBusiness_no() {
        return business_no;
    }

    public void setBusiness_no(String business_no) {
        this.business_no = business_no;
    }

    public int getTurnoverId() {
        return turnoverId;
    }

    public void setTurnoverId(int turnoverId) {
        this.turnoverId = turnoverId;
    }

    public int getCurrencyId() {
        return currencyId;
    }

    public void setCurrencyId(int currencyId) {
        this.currencyId = currencyId;
    }

    public double getTurnoverRate() {
        return turnoverRate;
    }

    public void setTurnoverRate(double turnoverRate) {
        this.turnoverRate = turnoverRate;
    }

    public double getClearRate() {
        return clearRate;
    }

    public void setClearRate(double clearRate) {
        this.clearRate = clearRate;
    }

    public double getClearValue() {
        return clearValue;
    }

    public void setClearValue(double clearValue) {
        this.clearValue = clearValue;
    }

    public long getDeleteTime() {
        return deleteTime;
    }

    public void setDeleteTime(long deleteTime) {
        this.deleteTime = deleteTime;
    }
}
