package com.game.c_entity.merchant;

import com.game.po.data.IDataChecker;
import io.netty.util.internal.StringUtil;
import org.bson.types.ObjectId;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.List;

@Document(collection = "c_webSite")
public class C_WebSite implements IDataChecker {

    @Id
    private ObjectId _id;

    //商户
    private String business_no;

    //站id
    private int siteId;

    //站名字
    private String siteName;

    //站点指向地址
    private String pointAddress;

    private List<String> domainName;

    //站图标
    private String siteLogo;

    //站图标
    private String siteLogo1;

    //站图标
    private String siteLogo2;

    //站模板
    private String siteModel;

    //开关
    private boolean open;

    //描述
    private String desc;

    @Override
    public boolean check() throws Exception {
        return true;
    }

    public String getBusiness_no() {
        return business_no;
    }

    public void setBusiness_no(String business_no) {
        this.business_no = business_no;
    }

    public int getSiteId() {
        return siteId;
    }

    public void setSiteId(int siteId) {
        this.siteId = siteId;
    }

    public String getSiteName() {
        return StringUtil.isNullOrEmpty(siteName) ? "" : siteName;
    }

    public void setSiteName(String siteName) {
        this.siteName = siteName;
    }

    public String getPointAddress() {
        return StringUtil.isNullOrEmpty(pointAddress) ? "" : pointAddress;
    }

    public void setPointAddress(String pointAddress) {
        this.pointAddress = pointAddress;
    }

    public List<String> getDomainName() {
        return domainName;
    }

    public void setDomainName(List<String> domainName) {
        this.domainName = domainName;
    }

    public String getSiteLogo() {
        return StringUtil.isNullOrEmpty(siteLogo) ? "" : siteLogo;
    }

    public void setSiteLogo(String siteLogo) {
        this.siteLogo = siteLogo;
    }

    public String getSiteLogo1() {
        return StringUtil.isNullOrEmpty(siteLogo1) ? "" : siteLogo1;
    }

    public void setSiteLogo1(String siteLogo1) {
        this.siteLogo1 = siteLogo1;
    }

    public String getSiteLogo2() {
        return StringUtil.isNullOrEmpty(siteLogo2) ? "" : siteLogo2;
    }

    public void setSiteLogo2(String siteLogo2) {
        this.siteLogo2 = siteLogo2;
    }

    public String getSiteModel() {
        return StringUtil.isNullOrEmpty(siteModel) ? "" : siteModel;
    }

    public void setSiteModel(String siteModel) {
        this.siteModel = siteModel;
    }

    public boolean isOpen() {
        return open;
    }

    public void setOpen(boolean open) {
        this.open = open;
    }

    public String getDesc() {
        return StringUtil.isNullOrEmpty(desc) ? "" : desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

}
