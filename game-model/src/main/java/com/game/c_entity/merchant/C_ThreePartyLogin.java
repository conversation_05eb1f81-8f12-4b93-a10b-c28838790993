package com.game.c_entity.merchant;

import com.game.po.data.IDataChecker;
import io.netty.util.internal.StringUtil;
import org.bson.types.ObjectId;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

@Document(collection = "c_threePartyLogin")
public class C_ThreePartyLogin implements IDataChecker {
    @Id
    private ObjectId _id;

    //商户
    private String business_no;

    private int threePartyId;

    //三方类型
    private int threePartyType;

    private boolean open;

    //域名
    private String domainName;

    private String extend_1;

    private String extend_2;

    private String extend_3;

    private String extend_4;

    private String extend_5;

    @Override
    public boolean check() throws Exception {
        return true;
    }

    public String getBusiness_no() {
        return business_no;
    }

    public void setBusiness_no(String business_no) {
        this.business_no = business_no;
    }

    public int getThreePartyId() {
        return threePartyId;
    }

    public void setThreePartyId(int threePartyId) {
        this.threePartyId = threePartyId;
    }

    public int getThreePartyType() {
        return threePartyType;
    }

    public void setThreePartyType(int threePartyType) {
        this.threePartyType = threePartyType;
    }

    public boolean isOpen() {
        return open;
    }

    public void setOpen(boolean open) {
        this.open = open;
    }

    public String getDomainName() {
        return domainName;
    }

    public void setDomainName(String domainName) {
        this.domainName = domainName;
    }

    public String getExtend_1() {
        return StringUtil.isNullOrEmpty(extend_1) ? "" : extend_1;
    }

    public void setExtend_1(String extend_1) {
        this.extend_1 = extend_1;
    }

    public String getExtend_2() {
        return StringUtil.isNullOrEmpty(extend_2) ? "" : extend_2;
    }

    public void setExtend_2(String extend_2) {
        this.extend_2 = extend_2;
    }

    public String getExtend_3() {
        return StringUtil.isNullOrEmpty(extend_3) ? "" : extend_3;
    }

    public void setExtend_3(String extend_3) {
        this.extend_3 = extend_3;
    }

    public String getExtend_4() {
        return StringUtil.isNullOrEmpty(extend_4) ? "" : extend_4;
    }

    public void setExtend_4(String extend_4) {
        this.extend_4 = extend_4;
    }

    public String getExtend_5() {
        return StringUtil.isNullOrEmpty(extend_5) ? "" : extend_5;
    }

    public void setExtend_5(String extend_5) {
        this.extend_5 = extend_5;
    }
}
