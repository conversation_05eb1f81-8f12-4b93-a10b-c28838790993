package com.game.c_entity.merchant;

import com.game.engine.utils.JsonUtils;
import com.game.po.data.IDataChecker;
import org.bson.types.ObjectId;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Document(collection = "c_activityTag")
public class C_ActivityTag implements IDataChecker {

    @Id
    private ObjectId _id;

    //商户号
    private String business_no;

    //活动标签 1.casino 2.sports
    private int activityTag;

    private int sort;

    private List<String> activityTagData = new ArrayList<>();

    private final transient Map<Integer, ActivityTagInfo> activityTagInfoMap = new HashMap<>();

    @Override
    public boolean check() throws Exception {
        if (!activityTagData.isEmpty()) {
            for (String data : activityTagData) {
                final ActivityTagInfo activityTagInfo = JsonUtils.readFromJson(data, ActivityTagInfo.class);
                activityTagInfoMap.put(activityTagInfo.language, activityTagInfo);
            }
        }
        return true;
    }

    public String getBusiness_no() {
        return business_no;
    }

    public void setBusiness_no(String business_no) {
        this.business_no = business_no;
    }

    public int getActivityTag() {
        return activityTag;
    }

    public void setActivityTag(int activityTag) {
        this.activityTag = activityTag;
    }

    public int getSort() {
        return sort;
    }

    public void setSort(int sort) {
        this.sort = sort;
    }

    public List<String> getActivityTagData() {
        return activityTagData;
    }

    public void setActivityTagData(List<String> activityTagData) {
        this.activityTagData = activityTagData;
    }

    public Map<Integer, ActivityTagInfo> getActivityTagInfoMap() {
        return activityTagInfoMap;
    }

    public static class ActivityTagInfo {
        public int language;

        public String tagName;

        public int getLanguage() {
            return language;
        }

        public void setLanguage(int language) {
            this.language = language;
        }

        public String getTagName() {
            return tagName;
        }

        public void setTagName(String tagName) {
            this.tagName = tagName;
        }
    }
}
