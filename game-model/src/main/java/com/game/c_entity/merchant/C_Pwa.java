package com.game.c_entity.merchant;

import com.game.engine.utils.JsonUtils;
import com.game.po.data.IDataChecker;
import io.netty.util.internal.StringUtil;
import org.bson.types.ObjectId;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

@Document(collection = "c_pwa")
public class C_Pwa implements IDataChecker {

    @Id
    private ObjectId _id;

    //商户
    private String business_no;

    private int c_id;

    private int currencyId;

    private double reward;

    private int turnoverMul;

    private boolean open;

    private List<String> pwaInfoData = new ArrayList<>();

    private transient Map<Integer, PwaInfo> pwaInfoMap = new LinkedHashMap<>();

    @Override
    public boolean check() throws Exception {
        if (this.pwaInfoData != null && !this.pwaInfoData.isEmpty()) {
            for (String data : this.pwaInfoData) {
                final PwaInfo pwaInfo = JsonUtils.readFromJson(data, PwaInfo.class);
                this.pwaInfoMap.put(pwaInfo.language, pwaInfo);
            }
        }
        return true;
    }

    public String getBusiness_no() {
        return business_no;
    }

    public void setBusiness_no(String business_no) {
        this.business_no = business_no;
    }

    public int getCurrencyId() {
        return currencyId;
    }

    public void setCurrencyId(int currencyId) {
        this.currencyId = currencyId;
    }

    public int getC_id() {
        return c_id;
    }

    public void setC_id(int c_id) {
        this.c_id = c_id;
    }

    public double getReward() {
        return reward;
    }

    public void setReward(double reward) {
        this.reward = reward;
    }

    public int getTurnoverMul() {
        return turnoverMul;
    }

    public void setTurnoverMul(int turnoverMul) {
        this.turnoverMul = turnoverMul;
    }

    public boolean isOpen() {
        return open;
    }

    public void setOpen(boolean open) {
        this.open = open;
    }

    public List<String> getPwaInfoData() {
        return pwaInfoData;
    }

    public void setPwaInfoData(List<String> pwaInfoData) {
        this.pwaInfoData = pwaInfoData;
    }

    public Map<Integer, PwaInfo> getPwaInfoMap() {
        return pwaInfoMap;
    }

    public void setPwaInfoMap(Map<Integer, PwaInfo> pwaInfoMap) {
        this.pwaInfoMap = pwaInfoMap;
    }

    public static class PwaInfo {
        public int language;

        public String icon;

        public String info;

        public int getLanguage() {
            return language;
        }

        public void setLanguage(int language) {
            this.language = language;
        }

        public String getIcon() {
            return StringUtil.isNullOrEmpty(icon) ? "" : icon;
        }

        public void setIcon(String icon) {
            this.icon = icon;
        }

        public String getInfo() {
            return StringUtil.isNullOrEmpty(info) ? "" : info;
        }

        public void setInfo(String info) {
            this.info = info;
        }
    }
}
