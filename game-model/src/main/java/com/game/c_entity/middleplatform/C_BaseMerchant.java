package com.game.c_entity.middleplatform;

import com.game.po.data.IDataChecker;
import org.bson.types.ObjectId;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.ArrayList;
import java.util.List;

@Document(collection = "c_baseMerchant")
public class C_BaseMerchant implements IDataChecker {

    @Id
    private ObjectId _id;

    private int merchantId;

    //商户号
    private String business_no;

    //时区
    private String timeZone;

    private List<String> region = new ArrayList<>();

    //域名
    @Indexed
    private List<String> merchantDomain = new ArrayList<>();

    private List<Integer> openLanguage = new ArrayList<>();

    //1.开启 0.关闭
    private int status;

    private long deleteTime;

    @Override
    public boolean check() throws Exception {
        return true;
    }

    public int getMerchantId() {
        return merchantId;
    }

    public void setMerchantId(int merchantId) {
        this.merchantId = merchantId;
    }

    public String getBusiness_no() {
        return business_no;
    }

    public void setBusiness_no(String business_no) {
        this.business_no = business_no;
    }

    public String getTimeZone() {
        return timeZone;
    }

    public void setTimeZone(String timeZone) {
        this.timeZone = timeZone;
    }

    public List<String> getRegion() {
        return region;
    }

    public void setRegion(List<String> region) {
        this.region = region;
    }

    public List<String> getMerchantDomain() {
        return merchantDomain;
    }

    public void setMerchantDomain(List<String> merchantDomain) {
        this.merchantDomain = merchantDomain;
    }

    public List<Integer> getOpenLanguage() {
        return openLanguage;
    }

    public void setOpenLanguage(List<Integer> openLanguage) {
        this.openLanguage = openLanguage;
    }

    public int getStatus() {
        return status;
    }

    public void setStatus(int status) {
        this.status = status;
    }

    public long getDeleteTime() {
        return deleteTime;
    }

    public void setDeleteTime(long deleteTime) {
        this.deleteTime = deleteTime;
    }
}
