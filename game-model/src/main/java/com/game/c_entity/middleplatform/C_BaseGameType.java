package com.game.c_entity.middleplatform;

import com.game.engine.utils.JsonUtils;
import com.game.po.data.IDataChecker;
import io.netty.util.internal.StringUtil;
import org.bson.types.ObjectId;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

@Document(collection = "c_baseGameType")
public class C_BaseGameType implements IDataChecker {

    @Id
    private ObjectId _id;

    //游戏类别Id
    private int gameCategoryId;

    //游戏类型
    private int gameType;

    //icon
    private String icon;

    private List<String> gameTypeData;

    private transient final Map<Integer, GameTypeInfo> gameTypeInfoMap = new LinkedHashMap<>();

    @Override
    public boolean check() throws Exception {
        if (this.gameTypeData != null && !this.gameTypeData.isEmpty()) {
            for (String data : this.gameTypeData) {
                final GameTypeInfo gameTypeInfo = JsonUtils.readFromJson(data, GameTypeInfo.class);
                gameTypeInfoMap.put(gameTypeInfo.getLanguage(), gameTypeInfo);
            }
        }
        return true;
    }

    public int getGameCategoryId() {
        return gameCategoryId;
    }

    public void setGameCategoryId(int gameCategoryId) {
        this.gameCategoryId = gameCategoryId;
    }

    public int getGameType() {
        return gameType;
    }

    public void setGameType(int gameType) {
        this.gameType = gameType;
    }

    public String getIcon() {
        return StringUtil.isNullOrEmpty(icon) ? "" : icon;
    }

    public void setIcon(String icon) {
        this.icon = icon;
    }

    public List<String> getGameTypeData() {
        return gameTypeData;
    }

    public void setGameTypeData(List<String> gameTypeData) {
        this.gameTypeData = gameTypeData;
    }

    public Map<Integer, GameTypeInfo> getGameTypeInfoMap() {
        return gameTypeInfoMap;
    }

    public static class GameTypeInfo {
        private int language;

        //游戏类别名字
        private String gameCategoryName;

        //游戏类型
        private String gameTypeName;

        public int getLanguage() {
            return language;
        }

        public void setLanguage(int language) {
            this.language = language;
        }

        public String getGameCategoryName() {
            return gameCategoryName;
        }

        public void setGameCategoryName(String gameCategoryName) {
            this.gameCategoryName = gameCategoryName;
        }

        public String getGameTypeName() {
            return gameTypeName;
        }

        public void setGameTypeName(String gameTypeName) {
            this.gameTypeName = gameTypeName;
        }
    }
}
