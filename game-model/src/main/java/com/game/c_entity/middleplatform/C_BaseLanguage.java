package com.game.c_entity.middleplatform;

import com.game.po.data.IDataChecker;
import org.bson.types.ObjectId;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

@Document(collection = "c_baseLanguage")
public class C_BaseLanguage implements IDataChecker {

    @Id
    private ObjectId _id;

    private int languageId;

    private String name;

    private String icon;

    @Override
    public boolean check() throws Exception {
        return true;
    }

    public int getLanguageId() {
        return languageId;
    }

    public void setLanguageId(int languageId) {
        this.languageId = languageId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getIcon() {
        return icon;
    }

    public void setIcon(String icon) {
        this.icon = icon;
    }
}
