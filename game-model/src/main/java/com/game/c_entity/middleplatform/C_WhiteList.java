package com.game.c_entity.middleplatform;

import com.game.po.data.IDataChecker;
import org.bson.types.ObjectId;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

@Document(collection = "c_whiteList")
public class C_WhiteList implements IDataChecker {

    @Id
    private ObjectId _id;

    //商户
    private String business_no;


    @Override
    public boolean check() throws Exception {
        return true;
    }

    public String getBusiness_no() {
        return business_no;
    }

    public void setBusiness_no(String business_no) {
        this.business_no = business_no;
    }

}
