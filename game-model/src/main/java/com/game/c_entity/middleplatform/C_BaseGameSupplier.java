package com.game.c_entity.middleplatform;

import com.game.po.data.IDataChecker;
import org.bson.types.ObjectId;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.List;

@Document(collection = "c_baseGameSupplier")
public class C_BaseGameSupplier implements IDataChecker {

    @Id
    private ObjectId _id;

    private int supplierId;

    //供应商名字
    private String name;

    //游戏平台
    private List<String> platform;

    //游戏类型
    private List<Integer> type;

    //地区
    private List<String> region;

    //支持货币
    private List<Integer> supportCurrency;

    //语言
    private List<String> language;

    //状态 1.正常 2.关闭 3.维护
    private int status;

    //自动维护时间 1-2,08:00-12:00
    private String autoMaintenanceTime;

    //维护时间
    private long maintenanceStartTime;

    //维护时间
    private long maintenanceEndTime;

    //商户号
    private String agent;

    //api地址
    private String apiUrl;

    private String token;

    //密钥
    private String secretKey;

    private long deleteTime;

    @Override
    public boolean check() throws Exception {
        return true;
    }

    public int getSupplierId() {
        return supplierId;
    }

    public void setSupplierId(int supplierId) {
        this.supplierId = supplierId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public List<String> getPlatform() {
        return platform;
    }

    public void setPlatform(List<String> platform) {
        this.platform = platform;
    }

    public List<Integer> getType() {
        return type;
    }

    public void setType(List<Integer> type) {
        this.type = type;
    }

    public List<String> getRegion() {
        return region;
    }

    public void setRegion(List<String> region) {
        this.region = region;
    }

    public List<Integer> getSupportCurrency() {
        return supportCurrency;
    }

    public void setSupportCurrency(List<Integer> supportCurrency) {
        this.supportCurrency = supportCurrency;
    }

    public List<String> getLanguage() {
        return language;
    }

    public void setLanguage(List<String> language) {
        this.language = language;
    }

    public int getStatus() {
        return status;
    }

    public void setStatus(int status) {
        this.status = status;
    }

    public String getAutoMaintenanceTime() {
        return autoMaintenanceTime;
    }

    public void setAutoMaintenanceTime(String autoMaintenanceTime) {
        this.autoMaintenanceTime = autoMaintenanceTime;
    }

    public long getMaintenanceStartTime() {
        return maintenanceStartTime;
    }

    public void setMaintenanceStartTime(long maintenanceStartTime) {
        this.maintenanceStartTime = maintenanceStartTime;
    }

    public long getMaintenanceEndTime() {
        return maintenanceEndTime;
    }

    public void setMaintenanceEndTime(long maintenanceEndTime) {
        this.maintenanceEndTime = maintenanceEndTime;
    }

    public String getAgent() {
        return agent;
    }

    public void setAgent(String agent) {
        this.agent = agent;
    }

    public String getApiUrl() {
        return apiUrl;
    }

    public void setApiUrl(String apiUrl) {
        this.apiUrl = apiUrl;
    }

    public String getToken() {
        return token;
    }

    public void setToken(String token) {
        this.token = token;
    }

    public String getSecretKey() {
        return secretKey;
    }

    public void setSecretKey(String secretKey) {
        this.secretKey = secretKey;
    }

    public long getDeleteTime() {
        return deleteTime;
    }

    public void setDeleteTime(long deleteTime) {
        this.deleteTime = deleteTime;
    }
}
