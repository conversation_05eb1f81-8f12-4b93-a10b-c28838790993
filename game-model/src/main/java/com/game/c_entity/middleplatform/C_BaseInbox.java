package com.game.c_entity.middleplatform;

import com.game.engine.utils.JsonUtils;
import com.game.po.data.IDataChecker;
import io.netty.util.internal.StringUtil;
import org.bson.types.ObjectId;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

@Document(collection = "c_baseInbox")
public class C_BaseInbox implements IDataChecker {
    @Id
    private ObjectId _id;

    //邮件id
    private int inboxId;

    //类型 1.系统 2.活动
    private int inboxType;

    private int isJump;

    //跳转类型 1.内连 2.外链
    private int jumpType;

    //弹框类型 1.任务 2.转盘 3.充值 4.客服
    private int popupLinks;

    //内部链接
    private String innerLinks;

    //外链接
    private String externalLinks;

    private boolean status;

    private List<String> inboxData;

    private transient final Map<Integer, InboxInfo> inboxInfoMap = new LinkedHashMap<>();

    @Override
    public boolean check() throws Exception {
        if (inboxData != null && !inboxData.isEmpty()) {
            for (String data : inboxData) {
                final InboxInfo inboxInfo = JsonUtils.readFromJson(data, InboxInfo.class);
                inboxInfoMap.put(inboxInfo.getLanguage(), inboxInfo);
            }
        }
        return true;
    }

    public int getInboxId() {
        return inboxId;
    }

    public void setInboxId(int inboxId) {
        this.inboxId = inboxId;
    }

    public int getInboxType() {
        return inboxType;
    }

    public void setInboxType(int inboxType) {
        this.inboxType = inboxType;
    }

    public int getPopupLinks() {
        return popupLinks;
    }

    public void setPopupLinks(int popupLinks) {
        this.popupLinks = popupLinks;
    }

    public int getIsJump() {
        return isJump;
    }

    public void setIsJump(int isJump) {
        this.isJump = isJump;
    }

    public int getJumpType() {
        return jumpType;
    }

    public void setJumpType(int jumpType) {
        this.jumpType = jumpType;
    }

    public String getExternalLinks() {
        return StringUtil.isNullOrEmpty(externalLinks) ? "" : externalLinks;
    }

    public void setExternalLinks(String externalLinks) {
        this.externalLinks = externalLinks;
    }

    public String getInnerLinks() {
        return StringUtil.isNullOrEmpty(innerLinks) ? "" : innerLinks;
    }

    public void setInnerLinks(String innerLinks) {
        this.innerLinks = innerLinks;
    }

    public boolean isStatus() {
        return status;
    }

    public void setStatus(boolean status) {
        this.status = status;
    }

    public List<String> getInboxData() {
        return inboxData;
    }

    public void setInboxData(List<String> inboxData) {
        this.inboxData = inboxData;
    }

    public Map<Integer, InboxInfo> getInboxInfoMap() {
        return inboxInfoMap;
    }

    public static class InboxInfo {
        public int language;

        //标题
        public String title;

        //内容
        public String context;

        public int getLanguage() {
            return language;
        }

        public void setLanguage(int language) {
            this.language = language;
        }

        public String getTitle() {
            return title;
        }

        public void setTitle(String title) {
            this.title = title;
        }

        public String getContext() {
            return StringUtil.isNullOrEmpty(context) ? "" : context;
        }

        public void setContext(String context) {
            this.context = context;
        }
    }
}
