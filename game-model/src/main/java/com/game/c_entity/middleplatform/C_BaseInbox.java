package com.game.c_entity.middleplatform;

import com.game.engine.utils.JsonUtils;
import com.game.po.data.IDataChecker;
import org.bson.types.ObjectId;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

@Document(collection = "c_baseInbox")
public class C_BaseInbox implements IDataChecker {
    @Id
    private ObjectId _id;

    //邮件id
    private int inboxId;

    //类型 1.系统 2.活动
    private int inboxType;

    private boolean status;

    private List<String> inboxData;

    private transient final Map<Integer, InboxInfo> inboxInfoMap = new LinkedHashMap<>();

    @Override
    public boolean check() throws Exception {
        if (inboxData != null && !inboxData.isEmpty()) {
            for (String data : inboxData) {
                final InboxInfo inboxInfo = JsonUtils.readFromJson(data, InboxInfo.class);
                inboxInfoMap.put(inboxInfo.getLanguage(), inboxInfo);
            }
        }
        return true;
    }

    public int getInboxId() {
        return inboxId;
    }

    public void setInboxId(int inboxId) {
        this.inboxId = inboxId;
    }

    public int getInboxType() {
        return inboxType;
    }

    public void setInboxType(int inboxType) {
        this.inboxType = inboxType;
    }

    public boolean isStatus() {
        return status;
    }

    public void setStatus(boolean status) {
        this.status = status;
    }

    public List<String> getInboxData() {
        return inboxData;
    }

    public void setInboxData(List<String> inboxData) {
        this.inboxData = inboxData;
    }

    public Map<Integer, InboxInfo> getInboxInfoMap() {
        return inboxInfoMap;
    }

    public static class InboxInfo {
        public int language;

        //标题
        public String title;

        //内容
        public String context;

        public int getLanguage() {
            return language;
        }

        public void setLanguage(int language) {
            this.language = language;
        }

        public String getTitle() {
            return title;
        }

        public void setTitle(String title) {
            this.title = title;
        }

        public String getContext() {
            return context;
        }

        public void setContext(String context) {
            this.context = context;
        }
    }
}
