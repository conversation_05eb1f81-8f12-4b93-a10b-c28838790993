package com.game.c_entity.middleplatform;


import com.game.engine.math.BigDecimalUtils;
import com.game.po.data.IDataChecker;
import org.bson.types.ObjectId;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

@Document(collection = "c_baseExchangeRate")
public class C_BaseExchangeRate implements IDataChecker {

    @Id
    private ObjectId _id;

    private int currencyId;

    //实时美元汇率
    private double usdExchangeRate;

    //实时汇率差值
    private double exchangeRateValue;

    //汇率差值开关
    private boolean exchangeRateValueOpen;

    //固定汇率
    private double fixedExchangeRate;

    //固定汇率开关
    private boolean fixedExchangeRateOpen;

    private long deleteTime;

    @Override
    public boolean check() throws Exception {
        return true;
    }

    public int getCurrencyId() {
        return currencyId;
    }

    public void setCurrencyId(int currencyId) {
        this.currencyId = currencyId;
    }

    public double getUsdExchangeRate() {
        return usdExchangeRate;
    }

    public void setUsdExchangeRate(double usdExchangeRate) {
        this.usdExchangeRate = usdExchangeRate;
    }

    public double getExchangeRateValue() {
        return exchangeRateValue;
    }

    public void setExchangeRateValue(double exchangeRateValue) {
        this.exchangeRateValue = exchangeRateValue;
    }

    public boolean isExchangeRateValueOpen() {
        return exchangeRateValueOpen;
    }

    public void setExchangeRateValueOpen(boolean exchangeRateValueOpen) {
        this.exchangeRateValueOpen = exchangeRateValueOpen;
    }

    public double getFixedExchangeRate() {
        return fixedExchangeRate;
    }

    public void setFixedExchangeRate(double fixedExchangeRate) {
        this.fixedExchangeRate = fixedExchangeRate;
    }

    public boolean isFixedExchangeRateOpen() {
        return fixedExchangeRateOpen;
    }

    public void setFixedExchangeRateOpen(boolean fixedExchangeRateOpen) {
        this.fixedExchangeRateOpen = fixedExchangeRateOpen;
    }

    public long getDeleteTime() {
        return deleteTime;
    }

    public void setDeleteTime(long deleteTime) {
        this.deleteTime = deleteTime;
    }

    public double getExchangeRate() {
        if (this.exchangeRateValueOpen) {
            return BigDecimalUtils.add(this.usdExchangeRate, this.exchangeRateValue, 9);
        }
        if (this.fixedExchangeRateOpen) {
            return this.fixedExchangeRate;
        }
        return this.usdExchangeRate;
    }
}
