package com.game.c_entity.middleplatform;

import com.game.po.data.IDataChecker;
import org.bson.types.ObjectId;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.List;
import java.util.Objects;

@Document(collection = "c_baseGamePlatform")
public class C_BaseGamePlatform implements IDataChecker {

    @Id
    private ObjectId _id;

    //游戏供应商id
    private int supplierId;

    //游戏供应商
    private String supplierName;

    //平台id
    private int platformId;

    //游戏平台名字
    private String platformName;

    //游戏类型
    private int type;

    //地区
    private List<String> region;

    //地区限制
    private List<String> regionLimit;

    //支持货币
    private List<Integer> supportCurrency;

    //语言
    private List<String> language;

    //图片地址
    private String fileUrl;

    //状态 1.正常 2.关闭 3.维护
    private int status;

    //自动维护时间 1-2,08:00-12:00
    private String autoMaintenanceTime;

    //维护开始时间
    private long maintenanceStartTime;

    //维护结束时间
    private long maintenanceEndTime;

    //商户号
    private String agent;

    //api地址
    private String apiUrl;

    private String token;

    //密钥
    private String secretKey;

    //大厅地址
    private String lobby;

    //站点id
    private String siteId;

    //白名单
    private List<String> ipBlacklist;

    private long deleteTime;

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        C_BaseGamePlatform that = (C_BaseGamePlatform) o;
        return platformId == that.platformId;
    }

    @Override
    public int hashCode() {
        return Objects.hash(platformId);
    }

    @Override
    public boolean check() throws Exception {
        return true;
    }

    public int getPlatformId() {
        return platformId;
    }

    public void setPlatformId(int platformId) {
        this.platformId = platformId;
    }

    public String getPlatformName() {
        return platformName;
    }

    public void setPlatformName(String platformName) {
        this.platformName = platformName;
    }

    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }

    public int getSupplierId() {
        return supplierId;
    }

    public void setSupplierId(int supplierId) {
        this.supplierId = supplierId;
    }

    public String getSupplierName() {
        return supplierName;
    }

    public void setSupplierName(String supplierName) {
        this.supplierName = supplierName;
    }

    public List<String> getRegion() {
        return region;
    }

    public void setRegion(List<String> region) {
        this.region = region;
    }

    public List<String> getRegionLimit() {
        return regionLimit;
    }

    public void setRegionLimit(List<String> regionLimit) {
        this.regionLimit = regionLimit;
    }

    public List<Integer> getSupportCurrency() {
        return supportCurrency;
    }

    public void setSupportCurrency(List<Integer> supportCurrency) {
        this.supportCurrency = supportCurrency;
    }

    public List<String> getLanguage() {
        return language;
    }

    public void setLanguage(List<String> language) {
        this.language = language;
    }

    public String getFileUrl() {
        return fileUrl;
    }

    public void setFileUrl(String fileUrl) {
        this.fileUrl = fileUrl;
    }

    public int getStatus() {
        return status;
    }

    public void setStatus(int status) {
        this.status = status;
    }

    public String getAutoMaintenanceTime() {
        return autoMaintenanceTime;
    }

    public void setAutoMaintenanceTime(String autoMaintenanceTime) {
        this.autoMaintenanceTime = autoMaintenanceTime;
    }

    public long getMaintenanceStartTime() {
        return maintenanceStartTime;
    }

    public void setMaintenanceStartTime(long maintenanceStartTime) {
        this.maintenanceStartTime = maintenanceStartTime;
    }

    public long getMaintenanceEndTime() {
        return maintenanceEndTime;
    }

    public void setMaintenanceEndTime(long maintenanceEndTime) {
        this.maintenanceEndTime = maintenanceEndTime;
    }

    public String getAgent() {
        return agent;
    }

    public void setAgent(String agent) {
        this.agent = agent;
    }

    public String getApiUrl() {
        return apiUrl;
    }

    public void setApiUrl(String apiUrl) {
        this.apiUrl = apiUrl;
    }

    public String getToken() {
        return token;
    }

    public void setToken(String token) {
        this.token = token;
    }

    public String getSecretKey() {
        return secretKey;
    }

    public void setSecretKey(String secretKey) {
        this.secretKey = secretKey;
    }

    public String getLobby() {
        return lobby;
    }

    public void setLobby(String lobby) {
        this.lobby = lobby;
    }

    public String getSiteId() {
        return siteId;
    }

    public void setSiteId(String siteId) {
        this.siteId = siteId;
    }

    public List<String> getIpBlacklist() {
        return ipBlacklist;
    }

    public void setIpBlacklist(List<String> ipBlacklist) {
        this.ipBlacklist = ipBlacklist;
    }

    public long getDeleteTime() {
        return deleteTime;
    }

    public void setDeleteTime(long deleteTime) {
        this.deleteTime = deleteTime;
    }
}
