package com.game.po.data;

import java.util.Map;

public interface IDataChecker {

    default <V> boolean check(Map<Integer, V> source) throws Exception {
        return true;
    }

    default <V, W> boolean check(Map<Integer, V> source, Map<Integer, W> source2) throws Exception {
        return true;
    }

    default <V, W, X> boolean check(Map<String, V> source, Map<Integer, W> source2, Map<Integer, X> source3) throws Exception {
        return true;
    }

    /**
     * 数据检测
     */
    default boolean check() throws Exception {
        return true;
    }
}
