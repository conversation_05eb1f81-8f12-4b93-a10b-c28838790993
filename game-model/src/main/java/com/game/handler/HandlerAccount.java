//package com.game.handler;
//
//import com.game.engine.mongo.HandlerCollection;
//import com.game.engine.mongo.IndexDescAll;
//import com.game.engine.mongo.IndexDescSingle;
//import com.game.engine.mongo.IndexTypeAndName;
//import com.game.entity.account.Account;
//import com.game.entity.account.AccountFields;
//
//public class HandlerAccount extends HandlerCollection<Account> {
//    public HandlerAccount() {
//        super(null);
//    }
//
//    @Override
//    protected IndexDescAll getAllIndexDesc() {
//        return IndexDescAll.createWithAllIndexs(
//                IndexDescSingle.createIndex(new IndexTypeAndName(Long.class, AccountFields.accountId))
//        );
//    }
//
//    @Override
//    protected Class<?> entityClass() {
//        return Account.class;
//    }
//}
