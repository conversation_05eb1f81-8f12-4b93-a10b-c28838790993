package com.game.handler;

import com.game.engine.mongo.HandlerCollection;
import com.game.engine.mongo.IndexDescAll;
import com.game.engine.mongo.IndexDescSingle;
import com.game.engine.mongo.IndexTypeAndName;
import com.game.entity.player.PlayerFields;
import com.game.entity.player.promote.PlayerPromote;
import com.game.entity.player.promote.PlayerPromoteFields;

public class HandlerPlayerPromote extends HandlerCollection<PlayerPromote> {

    public HandlerPlayerPromote() {
        super(null);
    }

    @Override
    protected IndexDescAll getAllIndexDesc() {
        return IndexDescAll.createWithAllIndexs(
                IndexDescSingle.createIndex(new IndexTypeAndName(Long.class, PlayerPromoteFields.playerId)),
                IndexDescSingle.createIndex(new IndexTypeAndName(String.class, PlayerPromoteFields.business_no)),
                IndexDescSingle.createIndex(new IndexTypeAndName(Long.class, PlayerPromoteFields.superiorId)),
                IndexDescSingle.createIndex(new IndexTypeAndName(String.class, PlayerPromoteFields.superiorCode))
        );
    }

    @Override
    protected Class<?> entityClass() {
        return PlayerPromote.class;
    }

}