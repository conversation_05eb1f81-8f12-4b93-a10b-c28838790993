package com.game.handler;

import com.game.engine.mongo.HandlerCollection;
import com.game.engine.mongo.IndexDescAll;
import com.game.engine.mongo.IndexDescSingle;
import com.game.engine.mongo.IndexTypeAndName;
import com.game.entity.player.Player;
import com.game.entity.player.PlayerFields;

public class HandlerPlayer extends HandlerCollection<Player> {

    public HandlerPlayer() {
        super(null);
    }

    @Override
    protected IndexDescAll getAllIndexDesc() {
        return IndexDescAll.createWithAllIndexs(
                IndexDescSingle.createIndex(new IndexTypeAndName(Long.class, PlayerFields.playerId)),
                IndexDescSingle.createIndex(new IndexTypeAndName(String.class, PlayerFields.business_no)),
                IndexDescSingle.createIndex(new IndexTypeAndName(String.class, PlayerFields.playerName)),
                IndexDescSingle.createIndex(new IndexTypeAndName(Boolean.class, PlayerFields.online)),
                IndexDescSingle.createIndex(new IndexTypeAndName(Long.class, PlayerFields.createTime)),
                IndexDescSingle.createIndex(new IndexTypeAndName(Long.class, PlayerFields.loginTime)),
                IndexDescSingle.createIndex(new IndexTypeAndName(Integer.class, PlayerFields.channelId)),
                IndexDescSingle.createIndex(new IndexTypeAndName(Integer.class, PlayerFields.mediaId)),
                IndexDescSingle.createIndex(new IndexTypeAndName(Integer.class, PlayerFields.adId)),
                IndexDescSingle.createIndex(new IndexTypeAndName(Integer.class, PlayerFields.agentId)),
                IndexDescSingle.createIndex(new IndexTypeAndName(Long.class, PlayerFields.firstRechargeTime)),

                IndexDescSingle.createIndex(new IndexTypeAndName(String.class, PlayerFields.areaCode), new IndexTypeAndName(String.class, PlayerFields.phone))
        );
    }

    @Override
    protected Class<?> entityClass() {
        return Player.class;
    }

}
