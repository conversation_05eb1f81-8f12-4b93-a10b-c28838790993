package com.game.enums;

public enum MiddleConfigType {
    Merchant(101),//商户
    Currency(102),//货币
    PaymentMethod(103),//支付方式
    ExchangeRate(104),//汇率
    Inbox(105),//邮件
    GameType(106),//游戏类型
    Language(107),//语言
    Head(108),//头像
    GamePlatform(109),//游戏平台
    MaintainNotice(110),//维护公告
    ServerConfig(111),//服务器配置
    ;

    private final int type;

    MiddleConfigType(int type) {
        this.type = type;
    }

    public int getType() {
        return type;
    }

    public static MiddleConfigType valueOf(int type) {
        for (MiddleConfigType reloadConfigType : MiddleConfigType.values()) {
            if (reloadConfigType.getType() == type) {
                return reloadConfigType;
            }
        }
        throw new IllegalArgumentException("MiddleConfigType：" + type + "不存在");
    }
}
