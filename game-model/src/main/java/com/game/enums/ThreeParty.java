package com.game.enums;

public enum ThreeParty {
    <PERSON><PERSON>(1, "邮件"),
    Google(2, "谷歌"),
    Telegram(3, "Telegram"),
    Facebook(4, "Facebook"),
    Twitter(5, "Twitter"),
    Phone(6, "phone"),
    Account(7, "account"),
    Wallet(8,"wallet")
    ;

    private final int threeParty;

    private final String name;

    ThreeParty(int threeParty, String name) {
        this.threeParty = threeParty;
        this.name = name;
    }

    public int getThreeParty() {
        return threeParty;
    }

    public String getName() {
        return name;
    }

    public static ThreeParty valuesOf(int threeParty) {
        for (ThreeParty type : ThreeParty.values()) {
            if (type.getThreeParty() == threeParty) {
                return type;
            }
        }
        throw new IllegalArgumentException("ThreeParty，error：" + threeParty);
    }
}
