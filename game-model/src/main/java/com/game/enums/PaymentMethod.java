package com.game.enums;


public enum PaymentMethod {
    BankTransfer_Pix(101, "银行转账"),

    EWallet(201, "电子钱包"),

    MobilePayment(301, "移动支付"),

    Encrypt(401, "加密"),
    ;

    private final int type;

    private final String name;

    PaymentMethod(int type, String name) {
        this.type = type;
        this.name = name;
    }

    public int getType() {
        return type;
    }

    public String getName() {
        return name;
    }
}
