package com.game.enums;

import java.util.Objects;

public enum AgentGame {
    TADA(1001, "tada"),
    ST8(1002, "st8"),
    <PERSON>D<PERSON>(1003, "jdb"),
    PG(1004, "pg"),
    BETBY(1005, "betby"),
    ATGAME(1006, "atgame"),
    FUNKY(1007, "funky"),
    ;

    AgentGame(int type, String name) {
        this.type = type;
        this.name = name;
    }

    private int type;

    private String name;

    public int getType() {
        return type;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public void setType(int type) {
        this.type = type;
    }

    public static int valueOfs(String name) {
        for (AgentGame agentGame : AgentGame.values()) {
            if (Objects.equals(agentGame.getName(), name)) {
                return agentGame.type;
            }
        }
        return 0;
    }
}
