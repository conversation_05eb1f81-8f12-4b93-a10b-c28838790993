package com.game.enums;

public enum ErrorCode {
    None(-1),
    //登录 1-100
    Success(0),
    PassWard_Length(1),//密码长度
    PassWord_Format(2),//密码格式
    Account_AlreadyExists(3),//用户已存在
    Account_NotExist(4),//用户不存在
    //    Mail_PassWord_Error(5),//邮箱密码错误
//    Mail_VerifyCode_Error(6),//邮箱验证码错误
    Mail_Format_Error(7),//邮箱格式错误
    UserName_Length(8),//用户名字长度
    UserName_IllegalCharacter(9),//名字包含非法字符
    UserName_Repeat(10),//用户名字重复
    Token_Expires(11),//token过期
    Token_Invalid(12),//无效token
    Two_Factor_Authentication_Fail(13),//2FA验证失败
    Verification_Code_Not_Expired(14),//验证码未过期
    Blacklist_Not_Login(15),//黑名单禁止登录
    ThreeParty_Login_Error(16),//三方登录异常
    Account_Replaced(17),//用户顶替
    ThreeParty_AlreadyBind(18),//三方已绑定
    Mail_NotBind(19),//邮箱未绑定
    Registered_limit(20),//注册限制
    Phone_Format_Error(21),//手机格式错误
    //    Phone_PassWord_Error(22),//手机密码错误
    PassWord_Error(23),//密码错误
    VerifyCode_Error(24),//验证码错误
    Format_Error(25),//格式错误
    GoogleAuth_Error(26),//谷歌认证失败
    Login_Limit_Error(27),//登录限制
    GoogleCertified_Error(28),//谷歌已认证
    VerifyCode_Send_Error(29),//验证码发送错误
    Phone_Not_Bound(30),//手机未绑定

    //玩家错误提示 100-200
    Player_Title(100),//玩家已封号
    Already_Bound_To_The_Superior(101),//已绑定上级
    Kyc_In_Progress(102),//kyc认证中
    Kyc_Params_Error(103),//kyc参数错误
    Player_Freeze(104),//玩家冻结

    //物品提示 201-300
    Currency_Num_Negative_Error(201),//货币为负
    Currency_Not_Enough_Error(202),//货币不足

    //充值
    Recharge_Not_Within_The_Range(300),//充值不在区间
    WithDraw_Not_Within_The_Range(301),//提现不在区间
    WalletAccount_NotExist(302),//钱包账户不存在
    OrderId_Not_Exist(303),//订单不存在
    Repeated_Recharge(304),//重复充值
    Repeated_WithDraw(305),//重复提现
    Channel_Maintenance(306),//充值维护
    No_Recharge_Times(307),//没有充值次数
    Recharge_UpperLimit(308),//充值上限
    No_Withdraw_Times(309),//没有提现次数
    Withdraw_UpperLimit(310),//提现上限
    Withdraw_BetTimes_Unfinished(311),//提现下注次数未完成
    Add_WithdrawAccount_UpperLimit(312),//添加提现账户上限
    Not_WithdrawAccount(313),//未填写提现账号
    Insufficient_Available_Funds(314),//可用金额不足
    DepositLimit(315),//充值限制
    WithdrawalLimit(316),//提现限制
    Payment_Callback_Address_Error(317),//支付回调地址异常
    AccountBound_NotDelete(318),//账号已绑定无法删除
    Add_DepositAccount(319),//请求添加充值账号
    Withdraw_Order_UnderReview(320),//提现订单审核中
    Withdraw_Account_Exist(321),//提现账户已存在

    //三方
    AgentGame_Error(400),
    AgentGame_InGame(401),//游戏中
    AgentGame_Recharge_Error(402),//提示充值

    //活动
    Activity_Ended(500),//活动已结束
    LuckSpin_Insufficient_Times(501),//幸运转盘次数不足
    ReferralCode_Error(502),//推广码异常
    Activity_NotStart(503),//活动未开始
    Activity_SignUpNotStart(504),//活动报名未开始
    LuckSpin_ConditionsMet(506),//已满足条件
    LuckSpin_Conditions_Not_Met(507),//不满足条件
    LuckSpin_Not_Available(508),//不可领取
    LuckSpin_VipLevel_Insufficient(509),//vip等级不足
    Invalid_Redemption_Code(510),//无效兑换码
    RedEnvelope_Finished(511),//红包已抢完
    Conditions_Not_Met(512),//不满足条件
    Reward_Received(513),//奖励已领取
    Not_Yet_Received(514),//未到领取时间
    Reward_Has_Expired(515),//奖励已过期
    Receive_Limit(516),//领取限制

    //任务
    Task_Not_Completed(600),//任务未完成
    Task_Already_Received(601),//任务已领取

    DomainName_Not_Exist(9996),//域名不存在
    Internal_Server_Error(9997),//内部错误
    Parameter_Error(9998),//参数错误
    Data_Error(9999),//数据异常

    //1000001 服务器
    Server_Maintenance(1000001),//服务器维护

    Player_Offline(2000000),//玩家下线
    ;

    private final int code;

    ErrorCode(int code) {
        this.code = code;
    }

    public int getCode() {
        return code;
    }
}