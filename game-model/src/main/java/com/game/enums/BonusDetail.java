package com.game.enums;

/**
 * 1.quests 2.luckySpin 3.depositBonus 4.freeSpin 5.levelUpBonus 6.recharge 7.weeklyCashBack 8.monthlyCashBack
 */
public enum BonusDetail {
    Quests(1),//任务
    LuckySpin(2),//幸运转盘
    DepositBonus(3),//充值送
    FreeSpin(4),//免费
    LevelUpBonus(5),//升级奖励
    Recharge(6),//充电
    WeeklyCashBack(7),//周奖励
    MonthlyCashBack(8),//月奖励
    ;

    private final int type;

    BonusDetail(int type) {
        this.type = type;
    }

    public int getType() {
        return type;
    }

    public static BonusDetail valueOf(int type) {
        for (BonusDetail bonusDetail : BonusDetail.values()) {
            if (bonusDetail.getType() == type) {
                return bonusDetail;
            }
        }
        return null;
    }

}
