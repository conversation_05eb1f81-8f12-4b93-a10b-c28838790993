package com.game.enums;


public enum GameType {
    Casino_Original(101, "原创"),
    Casino_Slots(102, "电子"),
    Casino_Live(103, "赌场视讯"),
    Casino_Poker(104, "棋牌牌桌"),
    Casino_Fish(105, "捕鱼"),
    Casino_Arcade(106, "街机"),
    Casino_Bingo(107, "宾果"),

    Sport(201, "体育"),

    Lottery(301, "彩票"),
    ;

    private final int type;

    private final String name;

    GameType(int type, String name) {
        this.type = type;
        this.name = name;
    }

    public int getType() {
        return type;
    }

    public String getName() {
        return name;
    }

}
