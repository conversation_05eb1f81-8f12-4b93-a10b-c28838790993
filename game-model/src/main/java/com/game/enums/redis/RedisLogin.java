package com.game.enums.redis;

/**
 * 登陆数据库
 */
public enum RedisLogin {

    //当前帐号序号，千万不能修改
    Platform_KEY_Account_UUID("platform:{KEY}:auuId"),
    Platform_LG_Map_InvitationID("platform:{LG}:Map:invitation:invitationId"),

    Platform_LG_Set_UserName("platform:{LG}:%s:Set:userName"),
    //商户-账号类型
    Platform_LG_Map_AccountID("platform:{LG}:%s:Map:account:%d:AccountId"),
    //商户 //验证码                               //类型//email
    Platform_LG_Account_VerifyCode("platform:{LG}:%s:account:%d:%s:verifyCode"),
    Platform_LG_Account_PhoneMsg("platform:{LG}:%s:account:%s:phoneMsg"),
    ;

    private final String key;

    private RedisLogin(String key) {
        this.key = key;
    }

    public String getKey(Object... objects) {
        return String.format(key, objects);
    }

}
