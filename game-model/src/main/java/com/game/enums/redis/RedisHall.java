package com.game.enums.redis;

public enum RedisHall {
    /** 玩家信息 */
    Platform_Role_Map_PlayerInfo("platform:Role:%d:Map:playerInfo"),

    /** 玩家货币 */
    Platform_Role_Map_Currency("platform:Role:%d:Map:currency"),

    /** 玩家邀请活动 */
    Platform_Role_Map_ReferralActivity("platform:Role:%d:Map:referralActivity"),

    /** 转盘 */
    Platform_Role_Map_ReferralLuckSpin_Invite("platform:Role:%d:Map:referralLuckSpin:invite"),
    Platform_Role_Map_ReferralLuckSpin_TotalTimes("platform:Role:%d:Map:referralLuckSpin:totalTimes"),
    Platform_Role_Map_ReferralLuckSpin_RemainTimes("platform:Role:%d:Map:referralLuckSpin:remainTimes"),
    Platform_Role_Map_ReferralLuckSpin_Ip("platform:Role:%d:Map:referralLuckSpin:ip"),
    Platform_Role_Map_ReferralLuckSpin_device("platform:Role:%d:Map:referralLuckSpin:device"),

    /** 推荐宝箱 */
    Platform_Role_Map_RewardBoxReferral("platform:Role:%d:Map:rewardBox:referral"),
    Platform_Role_Map_RewardBoxInvite("platform:Role:%d:Map:rewardBox:invite"),//有效
    Platform_Role_Map_RewardBox_Ip("platform:Role:%d:Map:rewardBox:ip"),
    Platform_Role_Map_RewardBox_Device("platform:Role:%d:Map:rewardBox:device"),
    ;
    private final String key;

    private RedisHall(String key) {
        this.key = key;
    }

    public String getKey(Object... objects) {
        return String.format(key, objects);
    }
}
