package com.game.enums.redis;

/**
 * 所有服务器共同存储的数据库
 */
public enum RedisAllGame {
    /** 服务器时间 */
    Platform_All_ServerTime("platform:All:serverTime:%d"),

    /** 游戏收藏 */
    Platform_All_GameCollect("platform:All:%s:gameCollect:%d"),

    /** 游戏点赞 */
    Platform_All_GameLike("platform:All:%s:gameLike:%d"),

    /** 幸运转盘奖池 (分币种)*/
    Platform_All_LuckSpin("platform:All:%s:luckSpin:%d"),

    /** 每日竞赛奖池 （不区分币种）*/
    Platform_All_DailyContest_PrizePool("platform:All:%s:dailyContestPrizePool:%s"),

    /** 每周抽奖 */
    Platform_All_WeeklyRaffle_Tickets("platform:All:%s:weeklyRaffle:%s"),

    /** 红包雨 */
    Platform_All_BonusRain("platform:All:%s:bonusRain:timePeriod:%d:redId:%d"),
    Platform_All_BonusRainAmount("platform:All:%s:bonusRainAmount:timePeriod:%d:redId:%d"),

    /** 提现账号 */
    Platform_All_WithdrawAccount("platform:All:%s:Set:withdrawAccount:%s"),

    /** 兑换码 */
    Platform_All_RedemptionCode("platform:All:%s:redemptionCode:%s"),
    ;

    private final String key;

    private RedisAllGame(String key) {
        this.key = key;
    }

    public String getKey(Object... objects) {
        return String.format(key, objects);
    }
}
