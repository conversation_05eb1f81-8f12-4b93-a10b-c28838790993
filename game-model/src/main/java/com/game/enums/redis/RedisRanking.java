package com.game.enums.redis;

/**
 * 所有排行榜的数据库
 */
public enum RedisRanking {
    RANKING_BIG_WIN(1, "platform:Ranking:%s:bigWin:%d"),//游戏id
    RANKING_LUCKY_WIN(2, "platform:Ranking:%s:luckyWin:%d"),//游戏id

    RANKING_DAILY_CONTEST(3, "platform:Ranking:%s:dailyContest:%s"),//每日竞赛

    RANKING_ACTIVITY(4, "platform:Ranking:%s:activity:%d:date:%s"),//排行活动
    RANKING_WAGEREDREBATES(5, "platform:Ranking:%s:wageredRebates:date:%s"),//投注返利
    ;

    private final int type;
    private final String key;

    public static RedisRanking valueOf(int type) {
        for (RedisRanking value : RedisRanking.values()) {
            if (value.getType() == type) {
                return value;
            }
        }
        return null;
    }

    RedisRanking(int type, String key) {
        this.type = type;
        this.key = key;
    }

    public String getKey(Object... objects) {
        return String.format(key, objects);
    }


    /**
     * @return the type
     */
    public int getType() {
        return type;
    }
}
