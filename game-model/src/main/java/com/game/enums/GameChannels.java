package com.game.enums;


public enum GameChannels {
    Casino_PicksForYou(101, "为您推荐"),
    Casino_Favorites(102, "收藏夹"),
    Casino_Recent(103, "最近的"),
    Casino_Slots(104, "电子"),
    Casino_LiveCasino(105, "真人娱乐场"),
    Casino_WGOriginals(106, "原创"),
    Casino_TableGames(107, "桌上游戏"),
    Casino_FeatureBuyIn(108, "特色购买"),
    Casino_HotGames(109, "热门游戏"),
    Casino_NewReleases(110, "新发行"),
    Casino_TopPicks(111, "精选"),
    Casino_GameProviders(112, "游戏提供商"),

    Sport(201, "运动"),

    Lottery(301, "彩票"),
    ;

    private final int type;

    private final String name;

    GameChannels(int type, String name) {
        this.type = type;
        this.name = name;
    }

    public int getType() {
        return type;
    }

    public String getName() {
        return name;
    }
}
