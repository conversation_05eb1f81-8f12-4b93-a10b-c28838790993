package com.game.enums;

public enum LuckSpinType {
    LUCK_SPIN_REFERRAL(1, "ReferralSpinScript"),
    LUCK_SPIN_VIP(2, "VipSpinScript"),
    LUCK_SPIN_DAILY(3, "DailySpinScript"),
    LUCK_SPIN_WEEKLY(4, "WeeklySpinScript"),
    ;

    private final int type;

    private final String name;

    LuckSpinType(int type, String name) {
        this.type = type;
        this.name = name;
    }

    public int getType() {
        return type;
    }

    public String getName() {
        return name;
    }

    public static LuckSpinType valueOf(int type) {
        for (LuckSpinType activityType : LuckSpinType.values()) {
            if (activityType.getType() == type) {
                return activityType;
            }
        }
        return null;
    }
}
