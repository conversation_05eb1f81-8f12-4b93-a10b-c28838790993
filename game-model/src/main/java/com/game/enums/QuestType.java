package com.game.enums;

public enum QuestType {

    /**
     * 日常任务
     */
    DAILY(1),

    /**
     * 周任务
     */
    WEEKLY(2),
    ;

    private final int number;

    QuestType(int number) {
        this.number = number;
    }

    public int getNumber() {
        return number;
    }

    public static QuestType valueOf(int type) {
        for (QuestType questType : QuestType.values()) {
            if (questType.getNumber() == type) {
                return questType;
            }
        }
        return null;
    }
}
