package com.game.enums;

public enum Language {
    EN(1, "en"),//英文
    PT(2, "pt"),//葡萄牙
    ;

    Language(int type, String name) {
        this.type = type;
        this.name = name;
    }

    private int type;

    private String name;

    public int getType() {
        return type;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public void setType(int type) {
        this.type = type;
    }

    public static String valueOfs(int type) {
        for (Language agentGame : Language.values()) {
            if (agentGame.getType() == type) {
                return agentGame.name;
            }
        }
        return "en";
    }
}
