package com.game.enums;

public enum FunctionEnabled {
    PhoneLogin(1),// 手机登录
    EmailLogin(2),// 邮箱登录
    ThreePartyLogin(3),// 三方登录
    Quest(4),// 任务
    LuckSpin(5),// 幸运转盘
    TG(6), //tg机器人
    DailyContest(7), //每日竞猜
    WeeklyRaffle(8), //每周抽奖
    Affiliate(9), //推广
    Chat(10), //聊天
    WebSite(11), //站点
    BLog(12),
    HelpCenter(13),
    Account<PERSON><PERSON><PERSON>(14),
    BigWinner(15),
    QuickAccess(16),
    Active(17), //活动
    Mystery_Bonus(18), //神秘奖金
    Piggy_Bank(19), // 存钱罐
    Bonus_Rain(20), // 红包雨
    Reward_Box(21), //推荐宝箱
    ContinueDeposit(22), // 连续充值
    Check_in(23),//签到'
    GameNote(24),//游戏通知
    GameKillRate(25),//游戏杀率
    ;

    FunctionEnabled(int type) {
        this.type = type;
    }

    private int type;

    public int getType() {
        return type;
    }
}
