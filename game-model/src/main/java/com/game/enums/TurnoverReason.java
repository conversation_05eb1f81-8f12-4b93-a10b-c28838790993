package com.game.enums;

public enum TurnoverReason {
    Default(0),
    Recharge(1),//充值
    Draw_fail(2),//提现失败
    AgentGame(3),//三方游戏
    Up_Points(4),//上分
    Withdraw_ToWallet(5),//提现到钱包
    LuckSpin(6),//幸运转盘
    VipBonus(7),
    Quest(8),//任务
    DailyContest(9),//每日竞赛
    WeeklyRaffle(10),//每周抽奖
    AgentGame_Refund(11),//退款
    Redemption_Code(12),//兑换码奖励
    AgentGame_FreeGames(13),//三方免费游戏
    Recharge_Giveaway(14),//充值赠送
    Draw(15),//提现
    Down_Points(16),//下分

    Activity_Recharge(20),//充值
    Activity_RechargeAccumulation(21),//充值累计活动
    Activity_Wagered(22),//洗码量活动
    Activity_Rank(23),//排行活动
    Activity_Compensate(24),//补偿活动
    Activity_FreeGive(25),//免费送活动

    RedEnvelope(26),//红包雨
    RewardBox(27),//推荐宝箱
    MysteryBonus(28),//神秘奖金
    PiggyBank(29), //存钱罐
    Vip_SignIn(30), //vip签到
    Vip_Daily(31), //vip每日
    Vip_Weekly(32), //vip每周
    Vip_Monthly(33), //vip每月
    ContinuousDeposit(34), //连续充值
    Pwa(35), //pwa奖励
    BackgroundSettings(36),//后台设置
    TurnoverSwitch(37),//打码量切换
    FirstChargeSignIn(38),//首充签到
    RechargeRecover(39),//充值返奖
    InvitedRewards(40),//被邀请奖励
    WageredRebates(41),//投注返利
    FirstDepositInviteBonus(42),//首充邀请
    ;

    private final int reason;

    private String source = "";

    TurnoverReason(int reason) {
        this.reason = reason;
    }

    public int getReason() {
        return reason;
    }

    public String getSource() {
        return source;
    }

    public void setSource(String source) {
        this.source = source;
    }

}
