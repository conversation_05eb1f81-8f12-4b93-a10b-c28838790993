package com.game.enums;

public enum OrderType {
    /**
     * 充值
     */
    RECHARGE(1),
    /**
     * 提现
     */
    WITHDRAW(2),
    ;

    private final int num;

    OrderType(int num) {
        this.num = num;
    }

    public int getNum() {
        return num;
    }

    public static OrderType valueOf(int type) {
        for (OrderType orderType : OrderType.values()) {
            if (orderType.getNum() == type) {
                return orderType;
            }
        }
        return null;
    }
}
