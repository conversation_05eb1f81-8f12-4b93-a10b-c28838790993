package com.game.manager;

import com.game.engine.mongo.EntityDao;
import com.google.common.base.Preconditions;

import javax.annotation.Nonnull;
import java.util.IdentityHashMap;
import java.util.Map;
import java.util.Objects;

/**
 * 理论上这里管理的是Dao的工厂，且注册时应该是接口和实例的绑定，获取时是通过接口获取其实例。
 * 不过我们只是为了封装数据库操作，并不是为了完全隔离数据库，因此不定义接口，直接使用具体类进行注册。
 */
public class EntityDaoMrg {

    private static final EntityDaoMrg instance = new EntityDaoMrg();

    public static EntityDaoMrg getInstance() {
        return instance;
    }

    private final Map<Class<?>, EntityDao<?>> entityDaoMap = new IdentityHashMap<>(10);

    public EntityDaoMrg() {

    }

    public void registerDao(@Nonnull EntityDao<?> entityDao) {
        Objects.requireNonNull(entityDao);
        Preconditions.checkArgument(!entityDaoMap.containsKey(entityDao.getClass()));
        entityDaoMap.put(entityDao.getClass(), entityDao);
    }

    public <T extends EntityDao<?>> T getDao(Class<T> daoClass) {
        @SuppressWarnings("unchecked") final T dao = (T) entityDaoMap.get(daoClass);
        if (null == dao) {
            throw new IllegalStateException(daoClass.getSimpleName() + " not registered ");
        }
        return dao;
    }

}