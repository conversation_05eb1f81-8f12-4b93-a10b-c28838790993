package com.game.manager;

import com.game.dao.ActivityLimitDao;
import com.game.dao.RegisterLimitDao;
import com.game.dao.activity.*;
import com.game.dao.player.PlayerPromoteDao;
import com.game.dao.quest.QuestNoteDao;
import com.game.dao.session.SessionDao;
import com.game.dao.account.AccountDao;
import com.game.dao.game.GameNoteDao;
import com.game.dao.order.RechargeOrderDao;
import com.game.dao.order.WithdrawOrderDao;
import com.game.dao.promote.*;
import com.game.dao.player.PlayerDao;
import com.game.engine.io.redis.RedisPoolManager;
import com.game.engine.mongo.DBConnectionMrg;
import com.game.engine.mongo.DBHandlerMrg;
import com.game.engine.mongo.MongoConverterMrg;
import com.game.engine.utils.TimeUtil;
import com.game.entity.player.Player;
import com.game.entity.player.promote.PlayerPromote;
import com.game.handler.*;
import com.game.redis.RedisDistributedLock;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class DBHandlerRegisterMrg {
    private static final Logger LOGGER = LoggerFactory.getLogger(DBHandlerRegisterMrg.class);

    private static final DBHandlerRegisterMrg instance = new DBHandlerRegisterMrg();

    public static DBHandlerRegisterMrg getInstance() {
        return instance;
    }

    public void dBHandlerRegister() {
        //分布式锁
        final RedisDistributedLock lock = new RedisDistributedLock(RedisPoolManager.getInstance().getPool());
        try {
            while (true) {
                if (lock.lock(RedisDistributedLock.LOCK_KEY, 10 * TimeUtil.SEC)) {
                    LOGGER.info("get redis lock...");
                    // 注册handler
                    DBHandlerMrg.getInstance().registerDBHandler(Player.class, new HandlerPlayer());
                    DBHandlerMrg.getInstance().registerDBHandler(PlayerPromote.class, new HandlerPlayerPromote());

                    EntityDaoMrg.getInstance().registerDao(new AccountDao(DBConnectionMrg.getInstance()));
                    EntityDaoMrg.getInstance().registerDao(new QuestNoteDao(DBConnectionMrg.getInstance()));
                    EntityDaoMrg.getInstance().registerDao(new TicketsNoteDao(DBConnectionMrg.getInstance()));
                    EntityDaoMrg.getInstance().registerDao(new WinTicketsNoteDao(DBConnectionMrg.getInstance()));
                    EntityDaoMrg.getInstance().registerDao(new BonusNoteDao(DBConnectionMrg.getInstance()));
                    EntityDaoMrg.getInstance().registerDao(new SessionDao(DBConnectionMrg.getInstance()));
                    EntityDaoMrg.getInstance().registerDao(new SpinBonusNoteDao(DBConnectionMrg.getInstance()));
                    EntityDaoMrg.getInstance().registerDao(new GameNoteDao(DBConnectionMrg.getInstance()));
                    EntityDaoMrg.getInstance().registerDao(new RechargeOrderDao(DBConnectionMrg.getInstance()));
                    EntityDaoMrg.getInstance().registerDao(new WithdrawOrderDao(DBConnectionMrg.getInstance()));
                    EntityDaoMrg.getInstance().registerDao(new CommissionRewardsNoteDao(DBConnectionMrg.getInstance()));
                    EntityDaoMrg.getInstance().registerDao(new TeamRewardsNoteDao(DBConnectionMrg.getInstance()));
                    EntityDaoMrg.getInstance().registerDao(new ThreeLevelRewardsNoteDao(DBConnectionMrg.getInstance()));
                    EntityDaoMrg.getInstance().registerDao(new ReferralRewardsNoteDao(DBConnectionMrg.getInstance()));
                    EntityDaoMrg.getInstance().registerDao(new RegisterLimitDao(DBConnectionMrg.getInstance()));
                    EntityDaoMrg.getInstance().registerDao(new ActivityLimitDao(DBConnectionMrg.getInstance()));
                    break;
                }
            }
        } catch (Exception e) {
            LOGGER.error("", e);
            System.exit(-1);
        } finally {
            lock.unlock(RedisDistributedLock.LOCK_KEY);
            LOGGER.info("release redis lock...");
        }

        // 注册对应的dao
        final MongoConverterMrg convertMrg = DBConnectionMrg.getInstance().getConvertMrg();
        EntityDaoMrg.getInstance().registerDao(new PlayerDao(convertMrg, DBHandlerMrg.getInstance().getDBHandler(Player.class)));
        EntityDaoMrg.getInstance().registerDao(new PlayerPromoteDao(convertMrg, DBHandlerMrg.getInstance().getDBHandler(PlayerPromote.class)));
    }

    public final void tick(long curMillTime) {
        DBHandlerMrg.getInstance().tick(curMillTime);
    }

}
