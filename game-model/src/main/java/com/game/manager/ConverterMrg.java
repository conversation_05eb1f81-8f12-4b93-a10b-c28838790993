package com.game.manager;

import com.game.dao.Converter;
import com.game.engine.mongo.DBHandlerMrg;
import com.game.engine.mongo.ShardEntity;
import org.bson.Document;

public class ConverterMrg implements Converter {

    private static final ConverterMrg instance = new ConverterMrg();

    public static ConverterMrg getInstance() {
        return instance;
    }

    @Override
    public <T> Document write(T entity) {
        return DBHandlerMrg.getInstance().convert(entity);
    }

    @Override
    public <T extends ShardEntity> T read(Document document, Class<T> typeClass) {
        return DBHandlerMrg.getInstance().convert(document, typeClass);
    }
}
