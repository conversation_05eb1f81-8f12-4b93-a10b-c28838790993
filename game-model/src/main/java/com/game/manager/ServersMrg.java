package com.game.manager;

import com.game.engine.enums.ServerType;
import com.game.engine.io.conf.NettyClientConfig;
import com.game.engine.io.netty.TcpClient;
import com.game.engine.struct.ServerInfo;
import com.proto.InnerMessage;
import io.netty.channel.Channel;
import it.unimi.dsi.fastutil.ints.Int2ObjectArrayMap;
import it.unimi.dsi.fastutil.ints.Int2ObjectMap;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

public class ServersMrg {
    private static final Logger LOGGER = LoggerFactory.getLogger(ServersMrg.class);

    //服务器
    private final Int2ObjectMap<ServerInfo> serverMap = new Int2ObjectArrayMap<>(2);

    private final TcpClient nettyClient;

    private final NettyClientConfig nettyClientConfig;

    public ServersMrg(TcpClient nettyClient, NettyClientConfig nettyClientConfig) {
        this.nettyClient = nettyClient;
        this.nettyClientConfig = nettyClientConfig;
    }

    public void updateServerInfo(InnerMessage.InnerServerInfo info) {
        ServerInfo serverInfo = serverMap.get(info.getId());
        boolean isRegister = false;
        if (serverInfo == null) {
            serverInfo = createServerInfo(info);
            addNettyClientConfig(serverInfo);
            isRegister = true;
        } else {
            serverInfo.setIp(info.getIp());
            serverInfo.setId(info.getId());
            serverInfo.setPort(info.getPort());
            serverInfo.setWwwip(info.getWwwIp());
            serverInfo.setGameState(info.getGameState());
            serverInfo.setVersion(info.getVersion());
            serverInfo.setContent(info.getContent());
            serverInfo.setPower(info.getPower());
            serverInfo.setOnline(info.getOnline());
            serverInfo.setName(info.getName());
        }
        serverMap.put(info.getId(), serverInfo);
        if (isRegister) {
            LOGGER.warn("register server：{}", serverInfo);
        }
    }

    private ServerInfo createServerInfo(InnerMessage.InnerServerInfo info) {
        final ServerInfo serverInfo = new ServerInfo();
        serverInfo.setIp(info.getIp());
        serverInfo.setId(info.getId());
        serverInfo.setPort(info.getPort());
        serverInfo.setWwwip(info.getWwwIp());
        serverInfo.setGameState(info.getGameState());
        serverInfo.setVersion(info.getVersion());
        serverInfo.setContent(info.getContent());
        serverInfo.setPower(info.getPower());
        serverInfo.setOnline(info.getOnline());
        serverInfo.setName(info.getName());
        serverInfo.setServerType(info.getType());
        return serverInfo;
    }

    private void addNettyClientConfig(ServerInfo serverInfo) {
        if (nettyClient.containsKey(serverInfo.getId())) {
            return;
        }
        final NettyClientConfig conf = new NettyClientConfig();
        conf.setType(ServerType.valueOf(serverInfo.getServerType()));
        conf.setId(serverInfo.getId());
        conf.setMaxConnectCount(this.nettyClientConfig.getMaxConnectCount());
        conf.setTargetHost(serverInfo.getIp());
        conf.setTargetPort(this.nettyClientConfig.getTargetPort());
        nettyClient.addNettyClientConfig(conf);
    }

    public void removeTcpClient(int id) {
        nettyClient.removeNettyClientConfig(id);
        serverMap.remove(id);
    }

    public Map<Integer, ServerInfo> getServerMap() {
        return serverMap;
    }

    public Channel getSession(int serverId) {
        final NettyClientConfig nettyClientConfig = nettyClient.getConfigMap().get(serverId);
        if (nettyClientConfig == null) {
            return null;
        }
        return nettyClientConfig.getSession();
    }

    public Channel getSessionByAccountId(long accountId) {
        final Int2ObjectMap<NettyClientConfig> serverMap = nettyClient.getConfigMap();
        if (serverMap.isEmpty()) {
            return null;
        }
        final int index = (int) (accountId % serverMap.size());
        final List<NettyClientConfig> serverList = new ArrayList<>(serverMap.values());
        return serverList.get(index).getSession();
    }
}
