package com.game.utils;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.concurrent.*;

public class VirtualThreadUtils {
    private static final Logger LOGGER = LoggerFactory.getLogger(VirtualThreadUtils.class);

    // 使用 ExecutorService 来管理虚拟线程
    private static final ExecutorService executorService = Executors.newVirtualThreadPerTaskExecutor();

    // 执行一个任务
    public static void execute(Runnable task) {
        executorService.submit(task);
    }

    // 执行一个任务并返回结果
    public static <T> CompletableFuture<T> executeCall(Callable<T> task) {
        return CompletableFuture.supplyAsync(() -> {
            try {
                return task.call();
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
        }, executorService);
    }

    // 关闭虚拟线程池
    public static void shutdown() {
        executorService.shutdown();
        try {
            if (!executorService.awaitTermination(30, TimeUnit.SECONDS)) {
                executorService.shutdownNow();
            }
        } catch (InterruptedException e) {
            executorService.shutdownNow();
            LOGGER.error("VirtualThreadUtils", e);
        }
    }

    // 获取当前活跃虚拟线程数量（根据需要做定制化统计）
    public static int getActiveThreadCount() {
        // 可通过使用自定义的监控方法来获取当前活跃线程数量
        return ((ThreadPoolExecutor) executorService).getActiveCount();
    }
}
