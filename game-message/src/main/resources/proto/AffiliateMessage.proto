syntax = "proto3";

package ProtoMessage;
option java_package = "com.proto";

import "CommonMessage.proto";

//请求绑定上级
message ReqBindSuperiorMessage {
    int32 msgID 		             = 1;
    string referrerCode          = 2; //推荐码
}

//返回绑定上级
message ResBindSuperiorMessage {
    int32 msgID 		             = 1;
    int32 error                  = 2; //错误码
    int64 referrerId             = 3; //推荐人id
}

//请求仪表板数据
message ReqDashboardDataMessage {
    int32 msgID 		             = 1;
}

//返回仪表板数据
message ResDashboardDataMessage {
    int32 msgID 		                    = 1;
    int32 error                         = 2; //错误码
    string referralLink                 = 3; //推广链接
    string referralCode                 = 4; //推广码
    double totalRewards                 = 5; //总奖励 (evUsd)
    int32 totalFriends                  = 6; //下级总人数
    int32 totalTeammates                = 7; //团长总人数
    double referralRewards              = 8; //推广奖励 (evUsd)
    double commissionRewards            = 9; //佣金奖励 (evUsd)
    double teamRewards                  = 10; //团长奖励 (evUsd)
    RewardsActivities rewardsActivities = 11; //活跃奖励
    DCurrencyItem referralBonus         = 12; //推广赠送
    double commissionRate               = 13; //佣金比例
    double teamRate                     = 14; //团长比例
    int32 rebateMethod                  = 15; //返水方式 1.有效 4.佣金
    bool referralRewardOpen             = 16; //推荐奖励是否开启
    repeated RebateRate rebateRateList  = 17; //返水比例
    int32 totalThreeLevels              = 18; //三级总人数
    double threeLevelRewards            = 19; //三级奖励 (evUsd)
    double threeLevelRate               = 20; //三级比例
    string text                         = 21; //文本
}

//返水比例
message RebateRate {
    string gameCategory                 = 1; //游戏类别
    int32 gameType                      = 2; //游戏类型
    string typeName                     = 3; //类型名字
    double rate                         = 4; //游戏比例
    double effectiveRate                = 5; //有效比例
}

message RewardsActivities {
    double referralReward                       = 1; //推荐奖励 (evUsd)
    repeated CommissionReward commissionReward  = 2; //佣金奖励
    repeated TeamReward teamReward              = 3; //团长奖励
    repeated ThreeLevelReward threeLevelReward  = 4; //三级奖励
}

//请求我的奖励
message ReqMyRewardDataMessage {
    int32 msgID 		              = 1;
}

//返回我的奖励
message ResMyRewardDataMessage {
    int32 msgID 		                  = 1;
    int32 error                       = 2; //错误码
    double availableCommissionReward  = 3; //可用佣金
    double totalCommissionReward      = 4; //总佣金
    double availableReferralRewards   = 5; //可用推荐奖励
    double totalReferralRewards       = 6; //总推荐奖励
    double availableTeamReward        = 7; //可用团长奖励
    double totalTeamReward            = 8; //总团长奖励
    DCurrencyItem LockedRewards       = 9; //锁住奖励
    double availableThreeLevelReward  =10; //可用三级奖励
    double totalThreeLevelReward      =11; //总三级奖励
}

//请求佣金奖励
message ReqCommissionRewardDataMessage {
    int32 msgID 		              = 1;
}

//返回佣金奖励
message ResCommissionRewardDataMessage {
    int32 msgID 		              = 1;
    int32 error                       = 2; //错误码
    repeated CommissionReward reward  = 3; //佣金奖励
}

message CommissionReward {
    int32 currencyId                  = 1;
    double available                  = 2; //可用
    double totalReceived              = 3; //总收入
}

//请求团长奖励
message ReqTeamRewardDataMessage {
    int32 msgID 		              = 1;
}

//返回团长奖励
message ResTeamRewardDataMessage {
    int32 msgID 		              = 1;
    int32 error                       = 2; //错误码
    repeated TeamReward reward        = 3; //团队奖励
}

message TeamReward {
    int32 currencyId                  = 1;
    double available                  = 2; //可用
    double totalReceived              = 3; //总收入
}

//请求推广奖励
message ReqReferralRewardDataMessage {
    int32 msgID 		              = 1;
    int64 registerStart               = 2; //注册开始时间
    int64 registerEnd                 = 3; //注册结束时间
    int32 page                        = 4; //页数
    int32 pageSize                    = 5; //页数量 10
    string username                   = 6; //用户名字
}

//返回推广奖励
message ResReferralRewardDataMessage {
    int32 msgID 		                   = 1;
    int32 error                            = 2; //错误码
    int32 page                             = 3;
    int32 pageSize                         = 4;
    int32 total                            = 5; //总条数
    int32 totalPage                        = 6; //总页数
    repeated ReferralReward referralReward = 7; //推荐奖励
}

message ReferralReward {
   string username                    = 1; //用户名字
   int64 registrationDate             = 2; //注册时间
   int32 vipLevel                     = 3; //vip等级
   string code                        = 4; //推荐码
   double earned                      = 5; //赢得
}

//请求历史数据
message ReqHistoryDataMessage {
   int32 msgID 		                   = 1;
   int32 page                        = 2; //页数
   int32 pageSize                    = 3; //页数量 10
   int32 type                        = 4; //1.referralRewards 2.commissionRewards 3.teamRewards
}

//返回历史数据
message ResHistoryDataMessage {
   int32 msgID 		                                    = 1;
   int32 error                                        = 2; //错误码
   int32 page                                         = 3; //页数
   int32 pageSize                                     = 4; //页数量 10
   int32 total                                        = 5; //总条数
   int32 totalPage                                    = 6; //总页数
   repeated ReferralRewardHistory referralHistory     = 7; //推广历史
   repeated CommissionRewardHistory commissionHistory = 8; //佣金历史
   repeated TeamRewardHistory teamRewardHistory       = 9; //团队历史
   repeated ThreeLevelRewardHistory threeLevelRewardHistory = 10; //三级历史
}

message ReferralRewardHistory {
   string playerName                 = 1;
   int32 currencyId                  = 2;
   double amount                     = 3;
   int64 time                        = 4;
   int32 status                      = 5;
}

message CommissionRewardHistory {
   int32 currencyId                  = 1;
   double amount                     = 2;
   int64 time                        = 3;
   int32 status                      = 4;
}

message TeamRewardHistory {
   int32 currencyId                  = 1;
   double amount                     = 2;
   int64 time                        = 3;
   int32 status                      = 4;
}

message ThreeLevelRewardHistory {
    int32 currencyId                  = 1;
    double amount                     = 2;
    int64 time                        = 3;
    int32 status                      = 4;
}

//请求推广码和下级数据
message ReqReferralCodeAndFriendsDataMessage {
    int32 msgID 		               = 1;
}

//返回推广码和下级数据
message ResReferralCodeAndFriendsDataMessage {
    int32 msgID 		                   = 1;
    int32 error                        = 2; //错误码
    int32 referralCodeNum              = 3; //推荐码数量
    int32 referralCodeLimit            = 4; //推荐码限制
    int32 friends                      = 5; //下级数据
}

//请求创建推广码
message ReqCreateReferralCodeMessage {
    int32 msgID 		                  = 1;
    string campaignName               = 2;
}

//返回创建推广码
message ResCreateReferralCodeMessage {
    int32 msgID 		                  = 1;
    int32 error                       = 2; //错误码
    string name                       = 3; //名字
    string referralCode               = 4; //推广码
    string link                       = 5; //连接
    int64 createTime                  = 6; //创建时间
    double commissionRate             = 7; //佣金比例
}

//请求推广码数据
message ReqReferralCodeDataMessage {
    int32 msgID 		              = 1;
    int64 wagerStart                  = 2; //下注开始时间
    int64 wagerEnd                    = 3; //下注结束时间
    int32 page                        = 4; //页数
    int32 pageSize                    = 5; //页数量 10
}

//返回推广码数据
message ResReferralCodeDataMessage {
    int32 msgID 		               = 1;
    int32 error                        = 2; //错误码
    int32 page                         = 3;
    int32 pageSize                     = 4;
    int32 total                        = 5; //总条数
    int32 totalPage                    = 6; //总页数
    repeated ReferralCode referralCode = 7; //推广列表
}

message ReferralCode {
    string name                       = 1; //名字
    string code                       = 2; //推广码
    string link                       = 3; //连接
    int64 createTime                  = 4; //创建时间
    int32 referrals                   = 5; //下级数量
    double commissionRate             = 6; //佣金比例
    double totalWagerOriginal         = 7; //自研投注
    double totalWager3rdParty         = 8; //三方投注
    double totalWagerSports           = 9; //运动投注
    double totalWager                 =10; //总投注金额
    double totalCommissionOriginal    =11; //自研佣金
    double totalCommission3rdParty    =12; //三方佣金
    double totalCommissionSports      =13; //运动佣金
    double totalCommissionReward      =14; //总佣金
}

//请求下级数据
message ReqFriendsDataMessage {
    int32 msgID 		                  = 1;
    string referralCode               = 2; //推广码 (不填代表全部)
    int64 registerStart               = 3; //注册开始时间
    int64 registerEnd                 = 4; //注册结束时间
    int64 wagerStart                  = 5; //下注开始时间
    int64 wagerEnd                    = 6; //下注结束时间
    int32 page                        = 7;
    int32 pageSize                    = 8; //页数10
    string username                   = 9; //用户名字
    int32 userId                      =10; //用户id
}

//返回下级数据
message ResFriendsDataMessage {
    int32 msgID 		                  = 1;
    int32 error                       = 2; //错误码
    int32 page                        = 3;
    int32 pageSize                    = 4; //页数10
    int32 total                       = 5; //总条数
    int32 totalPage                   = 6; //总页数
    repeated Friends friends          = 7; //下级数据
}

message Friends {
    int64 userId                       = 1;
    string userName                    = 2; //用户名字
    string code                        = 3; //推广码
    double commissionRate              = 4; //佣金比例
    int64 registrationDate             = 5; //注册时间
    int32 status                       = 6; //状态 1.正常
    double wagerUsdForBCOriginal       = 7;
    double wagerUsdFor3rdParty         = 8;
    double wagerUsdForSports           = 9;
    double wagerUsd                    =10;
    double commissionUsdForBCOriginal  =11;
    double commissionUsdFor3rdParty    =12;
    double commissionUsdForSports      =13;
    double commissionUsd               =14;
}

//请求团长数据
message ReqTeamDataMessage {
    int32 msgID 		              = 1;
    string referralCode               = 2; //推广码 (不填代表全部)
    int64 registerStart               = 3; //注册开始时间
    int64 registerEnd                 = 4; //注册结束时间
    int64 wagerStart                  = 5; //下注开始时间
    int64 wagerEnd                    = 6; //下注结束时间
    int32 page                        = 7;
    int32 pageSize                    = 8; //页数10
    string username                   = 9; //用户名字
    int32 userId                      =10; //用户id
}

//返回团长数据
message ResTeamDataMessage {
    int32 msgID 		              = 1;
    int32 error                       = 2; //错误码
    int32 page                        = 3;
    int32 pageSize                    = 4; //页数10
    int32 total                       = 5; //总条数
    int32 totalPage                   = 6; //总页数
    repeated Friends friends          = 7; //下级数据
}

//请求提现到钱包
message ReqWithdrawToWalletMessage {
    int32 msgID 		              = 1;
}

//返回提现到钱包
message ResWithdrawToWalletMessage {
    int32 msgID 		              = 1;
    int32 error                       = 2; //错误码
}

//请求提现数据
message ReqAffiliateWithdrawDataMessage {
    int32 msgID 		              = 1;
}

//返回提现数据
message ResAffiliateWithdrawDataMessage {
    int32 msgID 		              = 1;
    int32 error                       = 2; //错误码
    repeated DCurrencyItem cItem      = 3; //货币
}

//请求三级数据
message ReqThreeLevelDataMessage {
    int32 msgID 		              = 1;
    string referralCode               = 2; //推广码 (不填代表全部)
    int64 registerStart               = 3; //注册开始时间
    int64 registerEnd                 = 4; //注册结束时间
    int64 wagerStart                  = 5; //下注开始时间
    int64 wagerEnd                    = 6; //下注结束时间
    int32 page                        = 7;
    int32 pageSize                    = 8; //页数10
    string username                   = 9; //用户名字
    int32 userId                      =10; //用户id
}

//返回三级数据
message ResThreeLevelDataMessage {
    int32 msgID 		                  = 1;
    int32 error                       = 2; //错误码
    int32 page                        = 3;
    int32 pageSize                    = 4; //页数10
    int32 total                       = 5; //总条数
    int32 totalPage                   = 6; //总页数
    repeated Friends friends          = 7; //下级数据
}

//请求三级奖励
message ReqThreeLevelRewardDataMessage {
    int32 msgID 		                  = 1;
}

//返回三级奖励
message ResThreeLevelRewardDataMessage {
    int32 msgID 		                  = 1;
    int32 error                       = 2; //错误码
    repeated ThreeLevelReward reward  = 3; //团队奖励
}

message ThreeLevelReward {
    int32 currencyId                  = 1;
    double available                  = 2; //可用
    double totalReceived              = 3; //总收入
}