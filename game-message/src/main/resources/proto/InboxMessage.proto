syntax = "proto3";

package ProtoMessage;
option java_package = "com.proto";
import "CommonMessage.proto";

//请求邮件数据
message ReqInboxDataMessage {
    int32 msgID                       = 1;
}

//返回邮件数据
message ResInboxDataMessage {
    int32 msgID                      = 1;
    int32 error                      = 2; //错误码
	repeated InboxInfo inboxList     = 3; //邮件列表
}

//请求查看邮件
message ReqReadInBoxMessage {
     int32 msgID                     = 1;
     string inboxId                  = 2; //邮件id
}                         

//返回查看邮件
message ResReadInBoxMessage {
     int32 msgID                     = 1;
     int32 error                     = 2; //错误码
     string inboxId                  = 3; //邮件id
}

//请求删除邮件
message ReqDeleteInboxMessage {
    int32 msgID                      = 1;
    string inboxId                   = 2; //邮件id
}

//返回删除邮件
message ResDeleteInboxMessage {
    int32 msgID                      = 1;
    int32 error                      = 2; //错误码
	string inboxId                   = 3; //邮件唯一id
}









 


