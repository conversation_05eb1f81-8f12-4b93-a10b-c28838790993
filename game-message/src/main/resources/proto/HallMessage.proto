syntax = "proto3";

package ProtoMessage;
option java_package = "com.proto";
import "CommonMessage.proto";

//请求玩家进入大厅
message ReqPlayerEntryHallMessage {
     int32 msgID            = 1;
     string account         = 2; //账号（服务器使用）
	   int64 accountId        = 3; //用户id（服务器使用）
	   string ip              = 4; //登录ip（服务器使用）
	   int32 gateId           = 5; //网关id（服务器使用）
	   int32 threeParty       = 6; //三方（服务器使用）
	   string threePartyId    = 7; //三方Id（服务器使用）
	   string device1         = 8; //设备信息
	   string host            = 9;
     string region          =10; //地区 （服务器使用）
     int32 channel          =11; //渠道 1.pwa 2.app
     string model1          =12; //机型
     string mac             =13; //mac地址
     string browser         =14; //浏览器
}

//返回玩家进入大厅
message ResPlayerEntryHallMessage {
     int32 msgID            = 1;
     int32 error            = 2; //错误码
}

//请求刷新玩家数据
message ReqRefreshPlayerDataMessage {
     int32 msgID            = 1;
}

//返回刷新玩家数据
message ResRefreshPlayerDataMessage {
     int32 msgID            = 1;
     int32 error            = 2; //错误码
     PlayerInfo playerInfo  = 3; //玩家个人信息
}

//请求玩家登出
message ReqPlayerSignOutMessage {
     int32 msgID            = 1;
}

//返回玩家登出
message ResPlayerSignOutMessage {
     int32 msgID            = 1;
     int32 error            = 2; //错误码
}

//请求头像更换
message ReqHeadChangeMessage {
    int32 msgID             = 1;
    int32 headId            = 2;
}

//返回头像更换
message ResHeadChangeMessage {
    int32 msgID             = 1;
    int32 error             = 2; //错误码
    int32 headId            = 3;
}

//请求Enable2FA
message ReqEnable2FADataMessage {
   int32 msgID             = 1;
}

//返回Enable2FA
message ResEnable2FADataMessage {
   int32 msgID             = 1;
   int32 error             = 2; //错误码
   string secretKey        = 3; //密钥
   string qrCode           = 4; //二维码
}

//请求2FA Verification Code
message Req2FAVerificationCodeMessage {
   int32 msgID             = 1;
   string verificationCode = 2; //验证码
}

//返回2FA Verification Code
message Res2FAVerificationCodeMessage {
   int32 msgID             = 1;
   int32 error             = 2; //错误码
}

//请求名字修改
message ReqNameModifyMessage {
    int32 msgID             = 1;
    string userName         = 2;
}

//返回名字修改
message ResNameModifyMessage {
    int32 msgID             = 1;
    int32 error             = 2; //错误码
    string userName         = 3;
}

//请求用户数据
message ReqAccountDataMessage {
     int32 msgID 		    = 1;
}

//返回用户数据
message ResAccountDataMessage {
     int32 msgID 		      = 1;
     int32 error              = 2; //错误码
     AccountInfo accountInfo  = 3;
}

//请求切换货币
message ReqChangeCurrencyMessage {
   int32 msgID                   = 1;
   int32 currency                = 2; //货币id
}

//返回切换货币
message ResChangeCurrencyMessage {
   int32 msgID                   = 1;
   int32 error                   = 2; //错误码
   int32 currency                = 3; //货币id
}

//请求显示法币
message ReqViewInFiatMessage {
   int32 msgID                   = 1;
   int32 currency                = 2; //货币id
}

//返回显示法币
message ResViewInFiatMessage {
   int32 msgID                   = 1;
   int32 error                   = 2; //错误码
   int32 currency                = 3; //货币id
   int32 fiatCurrency            = 4; //法币货币id
}

//请求casino游戏数据
message ReqCasinoDataMessage {
    int32 msgID 		                = 1;
    string host                         = 2; //域名地址
    int32 language                      = 3; //语言id
    repeated PagerList pagerList        = 4;
}

message PagerList {
   int32 page                           = 1; //页数
   int32 pageSize                       = 2; //页数量 20
   string sectionId                     = 3; //subChannel
   repeated int32 platformId            = 4; //供应商id
   string gameName                      = 5; //游戏名字
}

//返回Casino游戏数据
message ResCasinoDataMessage {
    int32 msgID 		                = 1;
    int32 error                         = 2;
    repeated PageList pageList          = 3;
}

message PageList {
   int32 page                           = 1;
   int32 pageSize                       = 2;
   int32 total                          = 3; //总条数
   int32 totalPage                      = 4; //总页数
   string sectionId                     = 5; //subChannel
   repeated GameApiInfo gameApiInfo     = 6;
   repeated GameProvider GameProviders  = 7; //游戏提供商
}

message GameProvider {
    int32 platformId                    = 1; //平台id
    string platformName                 = 2; //平台名字
    string fileUrl                      = 3; //图片地址
    int32 gameNum                       = 4; //游戏数量
}

//请求游戏频道数据
message ReqGameChannelDataMessage {
    int32 msgID 		                = 1;
    string host                         = 2; //域名地址
    int32 language                      = 3; //语言id
}

//返回游戏频道数据
message ResGameChannelDataMessage {
    int32 msgID 		                        = 1;
    int32 error                                 = 2;
    repeated GameChannelInfo gameChannelInfo    = 3;
}

message GameChannelInfo {
    int32 channel                                  = 1; //频道
    int32 channelType                              = 2; //1.casino、2.sport
    string channelName                             = 3; //频道名字
    string channelIcon                             = 4; //频道图标
    string channelIcon1                            = 5; //频道图标
    int32 channelSort                              = 6; //频道排序
    repeated GameSubChannelInfo gameSubChannelList = 7; //游戏子频道
}

//游戏子频道频道
message GameSubChannelInfo {
    int32 subChannel           = 1; //子频道
    string subChannelName      = 2; //子频道名字
    string subChannelIcon      = 3; //子频道图标
    string subChannelIcon1     = 4; //子频道图标1
    int32 subChannelSort       = 5; //子频道排序
    int32 subChannelType       = 7; //1.厂商 2.游戏 3.最近 4.收藏
    bool menuShow              = 8; //是否显示菜单界面
    bool homeShow              = 9; //是否显示首页
    bool channelHomeShow       =10; //是否显示频道首页
    int32 homeSort             =11; //首页排序
    int32 channelHomeSort      =12; //频道首页排序
    string channelHomeIcon     =13; //频道首页图标
    string channelHomeIcon1    =14; //频道首页图标1
    string subChannelInner     =15; //子频道内链
}

//请求搜索游戏
message ReqSearchGameDataMessage {
   int32 msgID                   = 1;
   string gameName               = 2; //游戏名字
   int32  platformId             = 3; //平台id
   int32 gameId                  = 4; //游戏id
   string host                   = 5; //域名地址
   int32 language                = 6; //语言id
}

//返回搜索游戏
message ResSearchGameDataMessage {
   int32 msgID                          = 1;
   int32 error                          = 2;
   repeated GameApiInfo gameApiInfo     = 3; //游戏列表
}

//请求进入三方游戏
message ReqEntryAgentGameMessage {
   int32 msgID                   = 1;
   int32 gameId                  = 2; //游戏id
   int32 currencyId              = 3; //货币id
   bool freePlay                 = 4; //是否是试玩
   string currencyName           = 5; //货币名字（服务器使用）
   int32 platformId              = 6; //平台id（服务器使用）
   int32 gameType                = 7; //游戏类型（服务器使用）
   string platformGameId         = 8; //平台游戏id（服务器使用）
   int32 gameCurrencyId          = 9; //游戏货币id（服务器使用）
   bool bonus                    =10; //赠金（服务器使用）
   double rtpPool                =11; //rtp（服务器使用）
}

//返回进入三方游戏
message ResEntryAgentGameMessage {
   int32 msgID                   = 1;
   int32 error                   = 2; //错误码
   int32 urlType                 = 3; //0.url 1.html
   string gameUrl                = 4; //游戏链接
   int32 gameId                  = 5; //游戏id
   int32 currencyId              = 6; //货币id //服务器使用
   string referenceId            = 7; //免费游戏id //服务器使用
   int32 platformId              = 8; //平台id（服务器使用）
   int32 gameType                = 9; //游戏类型（服务器使用）
   int32 gameCurrencyId          =10; //游戏货币id（服务器使用）
   bool bonus                    =11; //赠金（服务器使用）
   double rtpPool                =12; //rtp（服务器使用）
}

//请求收藏、点赞
message ReqUserInteractionMessage {
   int32 msgID                   = 1;
   int32 type                    = 2; //1.收藏 2.点赞
   int32 gameId                  = 3; //游戏id
}

//返回收藏、点赞
message ResUserInteractionMessage {
   int32 msgID                   = 1;
   int32 error                   = 2; //错误码
   int32 type                    = 3; //1.收藏 2.点赞
   int32 gameId                  = 4; //游戏id
   int32 num                     = 5; //数量
}

//请求游戏opt数据
message ReqGameOptDataMessage {
   int32 msgID                   = 1;
   int32 gameId                  = 2; //游戏id
   string host                   = 3; //域名
}

//返回游戏opt数据
message ResGameOptDataMessage {
   int32 msgID                   = 1;
   int32 error                   = 2; //错误码
   int32 favoritesNum            = 3; //收藏数量
   int32 likeNum                 = 4; //点赞数量
   bool favorites                = 5; //是否收藏
   bool like                     = 6; //是否点赞
}

//请求获取排行数据
message ReqGetRankDataMessage {
   int32 msgID                   = 1;
   string host                   = 2; //域名
   int32 gameId                  = 3; //游戏id
}

//返回获取排行数据
message ResGetRankDataMessage {
   int32 msgID                   = 1;
   int32 error                   = 2; //错误码
   repeated RankInfo bigWin      = 3;
   repeated RankInfo luckyWin    = 4;
}

message RankInfo {
   int32 gameId                  = 1; //游戏id
   string playerName             = 2; //玩家名字
   int64 time                    = 3; //时间
   int32 currencyId              = 4; //货币id
   double betAmount              = 5; //下注
   double winAmount              = 6; //赢分
   double payout                 = 7; //倍率
}

//请求最近最大数据
message ReqRecentBigWinsDataMessage {
   int32 msgID                          = 1;
   string host                          = 2; //域名
   int32 date                           = 3; //1.1d 2.3d 3.7d
   int32 recentType                     = 4; //0.all 102.slots 103.liveCasino 101.原创
   int32 language                       = 5; //语言id
}

//返回最近最大赢分
message ResRecentBigWinsDataMessage {
   int32 msgID                         = 1;
   int32 error                         = 2; //错误码
   int32 date                          = 3; //1.1d 2.3d 3.7d
   int32 recentType                    = 4; //0.all 102.slots 103.liveCasino 101.原创
   repeated BigWinsInfo bigWinsList    = 5;
}

message BigWinsInfo {
    int32 gameId                       = 1; //游戏id
    string gameIcon                    = 2; //游戏icon
    int32 currencyId                   = 3; //货币id
    double amount                      = 4; //金额
    string playerName                  = 5; //玩家名字
    int32 platformId                   = 6; //平台id
    string platformName                = 7; //平台名字
    int32 gameType                     = 8; //游戏类型
    string gameName                    = 9; //游戏名字
}

//请求解绑三方
message ReqUnbindThreePartyMessage {
    int32 msgID                        = 1;
    int32 threeParty                   = 2; // 2.谷歌 3.Telegram 4.Facebook 5.Twitter
    int32 choose                       = 3; // 1.邮件 2.电话
    string account                     = 4;
    string verificationCode            = 5; //验证码
}

//返回解绑三方
message ResUnbindThreePartyMessage {
    int32 msgID                        = 1;
    int32 error                        = 2; //错误码
    int32 threeParty                   = 3; // 2.谷歌 3.Telegram 4.Facebook 5.Twitter
}

//请求绑定三方
message ReqBindThreePartyMessage {
    int32 msgID                        = 1;
    ThreePartyInfo threePartyInfo      = 2;
}

//返回绑定三方
message ResBindThreePartyMessage {
    int32 msgID                        = 1;
    int32 error                        = 2; //错误码
    ThreePartyInfo threePartyInfo      = 3;
}

//请求设置隐私偏好
message ReqSetPrivacyPreferencesMessage {
    int32 msgID                        = 1;
    int32 privacy                      = 2; //1.hideMyUsername
}

//返回设置隐私偏好
message ResSetPrivacyPreferencesMessage {
    int32 msgID                        = 1;
    int32 error                        = 2; //错误码
    int32 privacy                      = 3; //1.hideMyUsername
    bool value                         = 4;
}

//请求查看sessions
message ReqCheckSessionDataMessage {
    int32 msgID                        = 1;
    int32 page                         = 2; //页
    int32 pageSize                     = 3; //页数量
}

//返回查看sessions
message ResCheckSessionDataMessage {
    int32 msgID                        = 1;
    int32 error                        = 2; //错误码
    int32 page                         = 3; //页
    int32 pageSize                     = 4; //页数量
    int32 total                        = 5; //总条数
    int32 totalPage                    = 6; //总页数
    repeated Sessions sessionList      = 7;
}

message Sessions {
    string device                      = 1; //设备
    string location                    = 2; //本地
    string ipAddress                   = 3; //ip
    int64 lastTime                     = 4; //上次时间
}

//请求切换语言
message ReqChangeLanguageMessage {
    int32 msgID                        = 1;
    int32 language                     = 2; //语言 1.英语 2.葡萄牙 3.西班牙
}

//返回切换语言
message ResChangeLanguageMessage {
    int32 msgID                        = 1;
    int32 error                        = 2; //错误码
    int32 language                     = 3; //语言 1.英语 2.葡萄牙 3.西班牙
}

//请求kcy认证
message ReqKycAuthMessage {
    int32 msgID                        = 1;
    int32 authType                     = 2; //1.基础 2.高级
    string country                     = 3; //国家
    int32 documentType                 = 4; //证件类型 1.身份证 2.护照 3.visa
    string front                       = 5; //正面
    string back                        = 6; //背面
    string photo                       = 7; //照片
    string addressDocument             = 8; //地址文件
    string video                       = 9; //视频
}

//返回kcy认证
message ResKycAuthMessage {
    int32 msgID                        = 1;
    int32 error                        = 2; //错误码
}

//请求添加账号
message ReqAddAccountMessage {
  int32 msgID             = 1;
  string areaCode         = 2; //区号
  string account          = 3; //
  string password         = 4;
  string confirmPassWord  = 5; //确认密码
  string verification     = 6; //验证码
  int32 threeParty        = 7; //1.邮件 6.phone
}

//返回添加账号
message ResAddAccountMessage {
  int32 msgID             = 1;
  int32 error             = 2; //错误码
  string areaCode         = 3; //区号
  string account          = 4; //
  int32 threeParty        = 5; //1.邮件 6.phone
}

//请求重置密码
message ReqResetPasswordMessage {
  int32 msgID             = 1;
  string areaCode         = 2; //区号
  string account          = 3; //
  string password         = 4;
  string confirmPassWord  = 5; //确认密码
  string verification     = 6; //验证码
  string host             = 7;
  int32 threeParty        = 8; //1.邮件 6.phone
}

//返回重置密码
message ResResetPasswordMessage {
  int32 msgID             = 1;
  int32 error             = 2; //错误码
  string areaCode         = 3; //区号
  string account          = 4; //
  int32 threeParty        = 5; //1.邮件 6.phone
}

//请求绑定账号
message ReqBindAccountMessage {
  int32 msgID             = 1;
  string areaCode         = 2; //区号
  string account          = 3; //
  string verifyCode       = 4; //验证码
  int32 threeParty        = 5; //1.邮件 6.phone
}

//返回绑定账号
message ResBindAccountMessage {
  int32 msgID             = 1;
  int32 error             = 2; //错误码
  string areaCode         = 3; //区号
  string account          = 4; //
  int32 threeParty        = 5; //1.邮件 6.phone
}

//请求验证账号
message ReqVerifyAccountMessage {
  int32 msgID             = 1;
  string areaCode         = 2; //区号
  string account          = 3; //
  int32 codeType          = 4; //验证类型 1.重置 2.绑定 3.解绑 4.更换 5.提现
  string verifyCode       = 5; //验证码
  int32 threeParty        = 6; //1.邮件 6.phone
}

//返回验证账号
message ResVerifyAccountMessage {
  int32 msgID             = 1;
  int32 error             = 2; //错误码
  int32 threeParty        = 3; //1.邮件 6.phone
}

//请求更换账号
message ReqChangeAccountMessage {
  int32 msgID             = 1;
  string areaCode         = 2; //区号
  string account          = 3; //
  string verifyCode       = 4; //验证码
  int32 threeParty        = 5; //1.邮件 6.phone
}

//返回更换账号
message ResChangeAccountMessage {
  int32 msgID             = 1;
  int32 error             = 2; //错误码
  string areaCode         = 3; //区号
  string account          = 4; //
  int32 threeParty        = 5; //1.邮件 6.phone
}

//请求充值奖励开关
message ReqRechargeBonusOpenMessage {
  int32 msgID             = 1;
  bool open               = 2;
}

//返回充值奖励开关
message ResRechargeBonusOpenMessage {
  int32 msgID             = 1;
  int32 error             = 2; //错误码
  bool open               = 3;
}