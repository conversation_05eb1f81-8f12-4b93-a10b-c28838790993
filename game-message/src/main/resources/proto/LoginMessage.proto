syntax = "proto3";

package ProtoMessage;
option java_package = "com.proto";

import "CommonMessage.proto";

//请求注册
message ReqRegisterMessage {
    int32 msgID             = 1;
    string account          = 2;
    string password         = 3;
    string referralCode     = 4; //推荐码
    string areaCode         = 5; //区号
    string ea               = 6; //媒体id
    string de               = 7; //广告
    string host             = 8; //域名
    string device           = 9; //设备信息
    string confirmPassword  =10; //确认密码
    int32 threeParty        =11; //1.邮件 2.谷歌 3.Telegram 4.Facebook 5.Twitter 6.phone 7.account
    FbInfo fbInfo           =12;
    string activity         =13;
    int32 channel           =14; //1.pwa 2.app
    string phoneCode        =15; //手机验证码
    bool emailSubscribe     =16; //邮件订阅
    KWaiInfo kWaiInfo       =17; //
    int32 feedback          =18; //1.fb 2.kWai
}

//请求登录
message ReqLoginMessage {
    int32 msgID             = 1;
    string account          = 2;
    string password         = 3;
    int32 threeParty        = 4; //1.邮件 2.谷歌 3.Telegram 4.Facebook 5.Twitter 6.phone 7.account
    string threePartyId     = 5; //三方id
    string referralCode     = 6; //推荐码
    string areaCode         = 7; //区号
    string ea               = 8; //媒体id
    string de               = 9; //广告
    string host             =10; //域名
    string device           =11; //设备信息
    FbInfo fbInfo           =12;
    string activity         =13;
    int32 channel           =14; //1.pwa 2.app
    KWaiInfo kWaiInfo       =15; //
    int32 feedback          =16; //1.fb 2.kWai
}


//请求注册认证
message ReqRegisterAuthMessage {
    int32 msgID             = 1;
    string googleToken      = 2;
}

//返回注册认证
message ResRegisterAuthMessage {
    int32 msgID             = 1;
    int32 error             = 2; //错误码
}

//返回登录
message ResLoginMessage {
    int32 msgID             = 1;
    int32 error             = 2; //错误码
    string account          = 3;
    string gateAddress      = 4; //网关地址
    string token            = 5;
    int32 threeParty        = 6; //1.邮件 2.谷歌 3.Telegram 4.Facebook 5.Twitter 6.phone 7.account
    bool register           = 7; //是否注册
}

//请求邮箱验证码
message ReqMailVerifyCodeMessage {
    int32 msgID             = 1;
    string email            = 2; //邮箱地址
    int32 codeType          = 3; //验证码类型 1.reset、2.bind、3.unbind 4.change 5.add
    string host             = 4;
}

//返回邮箱验证码
message ResMailVerifyCodeMessage {
    int32 msgID             = 1;
    int32 error             = 2; //错误码
    string email            = 3; //邮箱地址
}

//请求未登录
message ReqNotLoggedInMessage {
    int32 msgID             = 1;
    string host             = 2; //域名
}

//返回未登录
message ResNotLoggedInMessage {
    int32 msgID             = 1;
    int32 error             = 2; //错误码
    string host             = 3; //网关地址
    string token            = 4;
    string region           = 5; //地区
}

//请求website
message ReqWebSiteModelMessage {
    int32 msgID             = 1;
    string host             = 2; //域名
}

//返回website
message ResWebSiteModelMessage {
    int32 msgID             = 1;
    int32 error             = 2;
    WebSiteInfo webSiteInfo = 3;
    string host             = 4; //网关地址
    string token            = 5;
    string region           = 6; //地区
}

//站点
message WebSiteInfo {
    int32 siteId            = 1; //站点id
    string siteName         = 2; //站点名字
    string siteLogo         = 3; //网页Log
    string siteLogo1        = 4; //网页icon
    string siteLogo2        = 5; //pc图标
    string siteModel        = 6; //站点模板
    int32 language          = 7; //语言id
}

//请求手机验证码
message ReqPhoneVerifyCodeMessage {
    int32 msgID             = 1;
    string areaCode         = 2; //区号
    string phone            = 3; //手机
    string host             = 4;
    int32 codeType          = 5; //验证码类型 1.reset、2.bind、3.unbind 4.change 5.add
}

//返回邮箱验证码
message ResPhoneVerifyCodeMessage {
    int32 msgID             = 1;
    int32 error             = 2; //错误码
    string phone            = 3; //手机
}

//请求channel安装
message ReqChannelInstallMessage {
    int32 msgID             = 1;
    string host             = 2; //域名
    string referralCode     = 3; //推荐码
    string ea               = 4; //媒体id
    string de               = 5; //广告
    string uniqueID         = 6; //唯一标识
    int32 channel           = 7; //1.pwa 2.app
}

//返回channel安装
message ResChannelInstallMessage {
    int32 msgID             = 1;
    int32 error             = 2; //错误码
}