syntax = "proto3";

package ProtoMessage;
option java_package = "com.proto";
import "CommonMessage.proto";

//帮助中心
message HelpCenterInfo {
    int32 sort                 = 1; //排序
    string channel             = 2; //频道
    string language            = 3; //语言
    string fileUrl             = 4; //图片地址
    string title               = 5; //标题
    string subtitle            = 6; //副标题
    string abstracts           = 7; //摘要
    string content             = 8; //内容
    repeated string tag        = 9; //标签
    int32 status               =10; //状态 0.下架 1.上架
    int32 helpId               =11; //帮助id
}


//请求新闻数据
message ReqNewsDataMessage {
    int32 msgID 		            = 1;
}

//返回新闻数据
message ResNewsDataMessage {
    int32 msgID 		            = 1;
    int32 error                     = 2;
    repeated NewsInfo newsInfo      = 3;
}

//新闻
message NewsInfo {
    string code                = 1; //新闻编码
    string channel             = 2; //频道
    string language            = 3; //语言
    string fileUrl             = 4; //图片地址
    string title               = 5; //标题
    string subtitle            = 6; //副标题
    string abstracts           = 7; //摘要
    string content             = 8; //内容
    repeated string tag        = 9; //标签
    int32 status               =10; //状态 1.上架 0.下架
    int32 newsId               =11; //新闻id
}

//请求配置数据
message ReqConfigDataMessage {
   int32 msgID 		                      = 1;
   string host                          = 2; //域名地址
   int32 language                       = 3; //语言id
   int32 configType                     = 4; //0.所有 6.vip 9.弹窗 18.邀请海报
}

//返回配置数据
message ResConfigDataMessage {
   int32 msgID 		                                    = 1;
   int32 error                                        = 2;
   repeated ExchangeRate exchangeRateList             = 3; //汇率
   repeated DCurrencyItem cItemList                   = 4; //货币
   repeated HeadInfo headList                         = 5; //头像
   repeated VipClubInfo vipClubList                   = 6; //vip
   MaintainNoticeInfo maintainNoticeInfo              = 7; //维护公告
   repeated BannerInfo bannerList                     = 8; //banner
   repeated PopupInfo PopupList                       = 9; //弹窗
   repeated ReferralRewardInfo referralRewardList     = 10; //推荐奖励
   repeated LanguageInfo languageList                 = 11; //语言id
   repeated CustomerServiceInfo customerServiceList   = 12; //客户列表
   repeated BottomMenuInfo bottomMenuList             = 13; //底部菜单列表
   repeated HelpCenterInfo helpCenterInfo             = 15; //帮助中心
   repeated QualityAssuranceInfo qualityAssuranceList = 16; //QA
   repeated GameTypeInfo gameTypeList                 = 17; //游戏类型
   repeated InvitationPosterInfo invitationPosterList = 18; //邀请海报
   repeated InvitationLinksInfo invitationLinksList   = 19; //邀请连接
   repeated ThreePartyLoginInfo threePartyLoginList   = 20; //三方登录
   repeated int32 functionId                          = 21; //功能id
   WebSiteData webSiteData                            = 22; //站点信息
   int32 showCurrencyId                               = 23; //显示货币id
   repeated QuickAccessInfo quickAccessList           = 24; //快捷访问
   string thirdPartyCustomer                          = 25; //三方客户
   bool chargingBenefitsOpen                          = 26; //充电福利
   bool upgradeRewardsOpen                            = 27; //升级奖励
   bool weeklyCashBackOpen                            = 28; //周返水
   bool monthlyCashBackOpen                           = 29; //月返水
   PwaInfo pwaInfo                                    = 30; //pwa信息
   bool phoneRegisterVerifyCodeOpen                   = 31; //手机注册验证码
   repeated RegisterRetrieveInfo registerRetrieveList = 32; //注册挽回列表
   repeated DailyRechargePopInfo dailyRechargePopList = 33; //每日充值弹窗
   repeated FirstChargePopInfo firstChargePopList     = 34; //首充弹窗
   repeated GamePopInfo gamePopList                   = 35; //游戏弹窗
   repeated InboxInfo bulletinList                    = 36; //公告
}

message PwaInfo {
  string pwaInfo                                     = 1; //pwa信息
  string icon                                        = 2; //图标
}

message LanguageInfo {
   int32 languageId                     = 1;
   string name                          = 2;
   string icon                          = 3;
}

message ExchangeRate {
   int32 currencyId                     = 1; //货币
   double usdExchangeRate               = 2; //实时美元汇率
}

message HeadInfo {
   int32 headId                         = 1; //头像id
   string fileUrl                       = 2; //图片地址
}

//vip信息
message VipClubInfo {
   int32 vipClubId                      = 1;
   string icon                          = 2; //图标
   int32 vipLevel                       = 3; //vip等级
   double needExp                       = 4; //需要经验
   string vipName                       = 5; //vip名字
   string upLevelReward                 = 6; //升级奖励 币种:数量
   repeated Wagered wageredList         = 7; //投注
   double weeklyCashBackRate            = 8; //周返现比例 有效压住X1%X周返水比例
   double monthlyCashBackRate           = 9; //月返现比例 有效压住X1%X月返水比例
   int32 dailyFreeWithdrawTimes         =10; //每日免费次数
   bool luxuryGiveaway                  =11; //奢侈品赠送
   bool vipHost                         =12;
   bool vipSpin                         =13;
   double needRecharge                  =14; //需要充值
   repeated ReceiveReward receiveRewards =15; //奖励配置
}

message ReceiveReward {
    int32 type                         = 1; //1.每日 2.每周 3.每月
    int32 currencyId                   = 2; //货币id
    double reward                      = 3; //奖励
    double wagered                     = 4; //需要下注
}

message Wagered {
     int32 currentTier                  = 1; //当前挡位
     double wageredMin                  = 2; //投注最小值
     double wageredMax                  = 3; //投注最大值
     double rate                        = 4; //比例
}

//维护公告
message MaintainNoticeInfo {
    string info                    = 1; //维护信息
    int64 startTime                = 2; //开始时间
    int64 endTime                  = 3; //结束时间
}

//广告
message BannerInfo {
    int32 bannerId                  = 1; //名称
    string bannerName               = 2; //名字
    int32 index                     = 3; //位置
    int32 language                  = 4; //语言
    repeated int32 currencyId       = 5; //币种
    int32 showSeq                   = 6; //排序
    int32 jumpType                  = 7; //跳转类型 1.内连 2.外链
    int32 popupLinks                = 8; //弹框类型 1.任务 2.转盘 3.充值 4.客服
    string innerLinks               = 9; //内部链接
    string externalLinks            =10; //外链接
    string title                    =11; //标题
    string subtitle                 =12; //副标题
    string fileUrl                  =13; //图片地址
    int32 button                    =14; //按钮 0.无 1.有
    string buttonWord               =15; //按钮文字
    int64 startTime                 =16; //开始时间
    int64 endTime                   =17; //结束时间
    int32 status                    =18; //状态 0.关 1.开
    int32 isJump                    =19; //是否有弹窗
    bool notLoginJump               =20; //未登录跳转
    int32 firstRechargeCurrencyId   =21; //首充货币
    double firstRechargeAmount      =22; //首充金额
}

//弹窗
message PopupInfo {
    int32 popupId                   = 1;
    string popupName                = 2; //弹窗名字
    int32 language                  = 3; //语言
    int32 triggerType               = 4; //弹窗触发类型 1.注册成功 2.游客访问 3.每日首次登录 4.余额不足 5.提现成功 6.充值成功
    double insufficientBalance      = 5; //余额不足
    int32 rechargePopup             = 6; //充值弹窗 1.首充未完成 2.二充未完成 3.三充未完成 4.其它
    bool button                     = 7; //按钮
    string buttonWord               = 8; //按钮文字
    int32 appearance                = 9; //出现方式 1.逐渐 2.向左滑动 3.从小变大
    bool autoPassOff                =10; //自动消失
    int32 showTime                  =11; //显示时间 （秒）
    int32 popupWay                  =12; //弹窗方式 1.居中公告弹窗 2.右下角小弹窗 3.右中引流弹窗
    repeated int32 popupPlatform    =13; //弹窗平台 1.pc 2.app
    string content                  =14; //内容
    int64 startTime                 =15; //开始时间
    int64 endTime                   =16; //结束时间
    int32 sort                      =17; //排序
    int32 currencyId                =18; //货币id
    string title                    =19; //标题
    int64 updateTime                =20; //更新时间
    repeated PopupData popupDataList=21; //弹框数据
    int32 rechargeTimeType          =22; //充值类型 0.不限制 1.< 2.≤ 3.> 4.≥ 5.=
    int32  rechargeTimes            =23; //次数
    int32 rechargeAmountType        =24; //充值金额类型 0.不限制 1.< 2.≤ 3.> 4.≥ 5.=
    double rechargeAmount           =25; //充值金额
    bool showHint                   =26; //显示提示
    int32 firstRechargeCurrencyId   =27; //首充货币
    double firstRechargeAmount      =28; //首充金额
}

message PopupData {
  int32 jumpType                    =1; //跳转类型 1.内连 2.外链
  int32 popupLinks                  =2; //弹框链接 1.任务 2.转盘 3.充值 4.客服（内连）
  string innerLinks                 =3; //内部链接（内连）
  string externalLinks              =4; //外链接（外连）
  string imageUrls                  =5; //图片地址
  int32 isJump                      =6; //是否有弹窗
  int32 popupType                   =7; //弹窗类型 1.功能弹窗 2.配置弹窗
  int32 systemPopup                 =8; //系统弹窗 1.任务 2.转盘 3.充值 4.客服
  bool notLoginJump                 =9; //未登录跳转
  int32 firstRechargeCurrencyId     =10; //首充货币
  double firstRechargeAmount        =11; //首充金额
}

//推荐奖励
message ReferralRewardInfo {
    int32 vipLevels                 = 1; //vip等级
    int32 currencyId                = 2; //货币id
    double unLockReward             = 3; //解锁奖励
    double totalWager               = 4; //总下注
}

//客服
message CustomerServiceInfo {
    int32 customerServiceId         = 1; //
    string name                     = 2; //
    string icon                     = 3;
    string mediaName                = 4; //媒体
    string contactDetails           = 5; //联系方式
    string links                    = 6; //链接
    int32 showSort                  = 7; //显示顺序
    bool open                       = 8; //开启
    int32 language                  = 9; //语言
}

//底部菜单
message BottomMenuInfo {
    int32 menuId                    = 1;
    string menuName                 = 2; //菜单名字
    int32 menuSort                  = 3; //菜单排序
    string subMenuName              = 4; //子菜单名字
    int32 type                      = 5; //类型 1.菜单 2.图标
    int32 subMenuSort               = 6; //子菜单排序
    int32 jumpType                  = 7; //跳转类型 1.内连 2.外链
    int32 popupLinks                = 8; //弹框类型 1.任务 2.转盘 3.充值 4.客服
    string innerLinks               = 9; //内部链接
    string externalLinks            =10; //外链接
    repeated IconInfo iconList      =11; //图标列表
}

message IconInfo {
    int32 id                        = 1;
    string icon                     = 2; //图标
    int32 jumpType                  = 3; //跳转类型 1.内连 2.外链
    int32 popupLinks                = 4; //弹框类型 1.任务 2.转盘 3.充值 4.客服
    string innerLinks               = 5; //内部链接
    string externalLinks            = 6; //外链接
}

//QA
message QualityAssuranceInfo {
    int32 qualityAssuranceType         = 1; //1.Vip Club 2.Affiliate 3.Weekly Raffle
    repeated QuestionInfo questionList = 2;
}

message QuestionInfo {
    string question                 = 1; //问题
    string answer                   = 2; //回答
}

message GameTypeInfo {
    int32 gameCategoryId            = 1; //游戏类别Id
    int32 gameType                  = 2; //游戏类型
    string gameCategoryName         = 3; //游戏类别名字
    string gameTypeName             = 4; //游戏类型
    string icon                     = 5; //图片
}

message InvitationPosterInfo {
    int32 invitationPosterId        = 1;
    string posterName               = 2; //海报名字
    int32 index                     = 3; //1.竖版 2.横板
    string fileUrl                  = 4; //文件地址
    string fileBase64               = 5; //文件地址
    bool generateQRCode             = 6; //是否生成二维码
}

message InvitationLinksInfo {
    int32 invitationLinksId         = 1;
    string socialMediaName          = 2; //社媒名字
    string icon                     = 3; //图片
    string desc                     = 4; //描述
}

message ThreePartyLoginInfo {
    int32 threePartyType            = 1; // 1.google 2.telegram
    string domainName               = 2; // 域名
    string extend_1                 = 3; // google：client_id，Telegram：Bot_id
    string extend_2                 = 4; // google：client_secret
    string extend_3                 = 5;
    string extend_4                 = 6;
    string extend_5                 = 7;
}

message WebSiteData {
    string info                     = 1; //底部信息
    string title                    = 2; //标题
    string keywords                 = 3; //关键字
    string introduce                = 4; //介绍
    string info1                    = 5; //底部信息1
    string ogTile                   = 6;
    string ogType                   = 7;
    string ogImage                  = 8;
    string ogDescription            = 9;
    string site_name                =10;
    string icon                     =11;
    string info2                    =12; //底部信息2
    string privacyAgreement         =13; //隐私协议
    string userTerms                =14; //用户条款
}

message QuickAccessInfo {
    int32 quickAccessId             =1;
    int32 entranceType              =2; //入口类型 1.大入口 2.小入口
    int32 jumpType                  =3;
    int32 popupLinks                =4; //弹框类型 1.任务 2.转盘 3.充值 4.客服 5.登录 6.注册
    string innerLinks               =5; //内部链接
    string externalLinks            =6; //外链接
    string entranceName             =7; //入口名字
    int32 sort                      =8; //排序
    int32 isJump                    =9; //是否有弹
    string imageUrl                 =10;//图片地址
}

message RegisterRetrieveInfo {
    int32 c_Id                      =1;
    string desc                     =2; //描述
    string text                     =3; //文本
    int32 jumpType                  =4;
    int32 popupLinks                =5; //弹框类型 1.任务 2.转盘 3.充值 4.客服 5.登录 6.注册
    string innerLinks               =6; //内部链接
    string externalLinks            =7; //外链接
    int32 isJump                    =8; //是否有弹
    string icon                     =9; //图片
    bool notLoginJump               =10; //未登录跳转
}

message DailyRechargePopInfo {
    int32 c_Id                      =1;
    int32 currencyId                =2; //币种
    string desc                     =3; //描述
    string title                    =4; //标题
    repeated GearInfo gearInfo      =5; //挡位
}

message GearInfo {
    int32 gearId                    =1;
    double amount                   =2;
    double giftRatio                =3;
    string icon                     =4;
    double minRecharge              =5;
    double maxRecharge              =6;
}

message FirstChargePopInfo {
    int32 c_id                      =1;
    int32 currencyId                =2;
    double amount                   =3;
    double giveawayAmount           =4;
    string title                    =5;
    string desc                     =6;
    string icon                     =7;
    string buttonText               =8;
    string buttonText1              =9;
    FirstChargeInfo button          =10;
    FirstChargeInfo button1         =11;
}

message FirstChargeInfo {
    bool notLoginJump               =1; //未登录是否可以跳转
    int32 isJump                    =2; //1-有 0-无
    int32 jumpType                  =3; //跳转类型 1.内连 2.外链
    int32 popupLinks                =4; //弹框类型 1.任务 2.转盘 3.充值 4.客服 5.登录 6.注册
    string innerLinks               =5; //内部链接
    string externalLinks            =6; //外链接
}

message GamePopInfo {
    int32 c_Id                      =1;
    string title                    =2;
    string desc                     =3;
    string icon                     =4;
    string text                     =5;
}
