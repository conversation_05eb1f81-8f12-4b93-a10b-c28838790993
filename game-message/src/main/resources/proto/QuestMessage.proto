syntax = "proto3";

package ProtoMessage;
option java_package = "com.proto";
import "CommonMessage.proto";

//请求任务数据
message ReqQuestDataMessage {
   int32 msgID                       = 1;
}

//返回任务数据
message ResQuestDataMessage {
   int32 msgID                       = 1;
   int32 error                       = 2; //错误码
   DItemShow accumulatedRewards      = 3; //累计奖励
   int64 currentTime                 = 4; //当前时间
   int64 dailyEndTime                = 5; //每日结束时间
   int64 weeklyEndTime               = 6; //每周结束时间
   repeated QuestInfo questList      = 7; //任务列表
}

message QuestInfo {
   int32 questId                     = 1; //当前任务id
   string questName                  = 2; //任务名字
   int32 questType                   = 3; //任务类型 1.每日 2.每周
   int32 goalType                    = 4; //目标类型
   int32 state                       = 5; //任务状态 1.接收 2.完成 3.领取
   double progressive                = 6; //当前进度
   int64 finishedTime                = 7; //完成时间
   ConditionInfo conditionInfo       = 8; //条件
   DItemShow rewards                 = 9; //奖励
   string icon                       =10; //任务图标
   string desc                       =11; //任务描述
   string uniqueId                   =12; //任务唯一id
   QuestTarget questTarget           =13; //任务目标
}

message QuestTarget {
   int32 channelId                   = 1; //频道id
}

message ConditionInfo {
   int32 totalProgressive            = 1; //总进度
   int32 param1                      = 2; //条件1
   int32 param2                      = 3; //条件2
   repeated int32 param3             = 4; //条件3
}

//请求领取任务奖励
message ReqReceiveQuestRewardMessage {
   int32 msgID                       = 1;
   int32 questId                     = 2; //任务id
   string uniqueId                   = 3; //任务唯一id (此id在历史记录使用)
}

//返回领取任务奖励
message ResReceiveQuestRewardMessage {
   int32 msgID                       = 1;
   int32 error                       = 2; //错误码
   int32 state                       = 3; //任务状态 1.接收 2.完成 3.领取
   DItemShow rewards                 = 4; //奖励
}

//请求之前任务数据
message ReqPreviousQuestsDataMessage {
   int32 msgID                       = 1;
   int32 page                        = 2; //页
   int32 pageSize                    = 3; //页数量
}

//返回之前任务数据
message ResPreviousQuestsDataMessage {
   int32 msgID                       = 1;
   int32 error                       = 2; //错误码
   int32 page                        = 3; //页
   int32 pageSize                    = 4; //页数量
   int32 total                       = 5; //总条数
   int32 totalPage                   = 6; //总页数
   repeated QuestInfo questList      = 7; //任务
   int64 clearRewardTime             = 8; //清理奖励时间
}









 


