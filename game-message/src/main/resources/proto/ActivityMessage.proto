syntax = "proto3";

package ProtoMessage;
option java_package = "com.proto";

import "CommonMessage.proto";

//请求活动数据
message ReqPromotionsDataMessage {
   int32 msgID                                  = 1;
   string host                                  = 2;
   int32 language                               = 3;
}

//返回活动数据
message ResPromotionsDataMessage {
   int32 msgID                                  = 1;
   int32 error                                  = 2;
   repeated PromotionsInfo promotionsList       = 3;
}

message PromotionsInfo {
    int32 cid                                    = 1; //配置id
    int32 activityId                             = 2; //活动id
    int32 type                                   = 3; //活动类型 4.首充 5.充值 6.下注 7.排行 8.补偿 9.免费送
    int32 subType                                = 4; //子类型
    int32 language                               = 5; //语言
    string name                                  = 6; //名称
    string title                                 = 7; //标题
    string pictureUrl                            = 8; //图片地址
    int32 status                                 = 9; //状态 0.不可领取 1.可领取
    int64 startTime                              =10; //开始时间
    int64 endTime                                =11; //结束时间
    DItemShow totalPrize                         =12; //总奖励
    int32 prizeShow                              =13; //1.总奖金 2.最高
    int32 activityTag                            =14; //活动标签 1.Casino 2.Sport
    string content                               =15; //内容
    repeated GameTarget gameTarget               =16; //游戏目标
    string innerPageIcon                         =17; //内页图片
    int32 signUpStatus                           =18; //报名状态 0.未报名 1.报名 2.过期
    int64 startSignUpTime                        =19; //报名开始时间
    int64 endSignUpTime                          =20; //报名结束时间
    int64 expirationTime                         =21; //过期时间
    string buttonWord                            =23; //按钮文字
    int32 jumpType                               =24; //跳转类型 1.内连 2.外链
    int32 popupLinks                             =25; //弹框类型 1.任务 2.转盘 3.充值 4.客服
    string innerLinks                            =26; //内部链接
    string externalLinks                         =27; //外链接
    repeated RewardPosition rewardPositionList   =28; //奖励挡位
    int32 gameTypeTarget                         =29; //游戏类型目标
    int32 currencyId                             =30; //货币id
    double curWagered                            =31; //投注
    int64 registerExpired                        =32; //注册有效时间
    int32 tagSort                                =33; //活动标签排序
    string tagName                               =34; //活动标签名字
    int32 functionType                           =35; //功能类型 1.pwa
    int32 sort                                   =36; //排序
    int32 jumpTarget                             =37; //跳转目标 0.无 1.封面 2.详情按钮
    int64 awardClearTime                         =38; //过期领取时间
    repeated DItemShow expiredReward             =39; //过期奖励
    repeated int32 receiveRewards                =40; //领取的挡位
 }

 message RewardPosition {
    int32 rewardCurrency                         = 1; //奖励币种
    double min                                   = 2; //最小值
    double max                                   = 3; //最大值
    int32 rewardType                             = 4;//1.固定 2.比例
    double reward                                = 5;
    int32 gameId                                 = 6;//
    string gameName                              = 7;//游戏名字
    int32 freeTimes                              = 8;//免费次数
    int32 id                                     = 9;//
 }

//请求活动报名
message ReqActivitySignUpMessage {
    int32 msgID                                  = 1;
    int32 cId                                    = 2; //活动配置id
    int32 activityId                             = 3; //活动id
}

//返回活动报名
message ResActivitySignUpMessage {
    int32 msgID                                  = 1;
    int32 error                                  = 2;
}

message GameTarget {
   int32 platformId                             = 1; //平台id
   string platformName                          = 2; //平台名字
   string icon                                  = 3; //图片
   int32 gameId                                 = 4; //游戏id
   string gameName                              = 5; //游戏名字
   int32 gameType                               = 6; //游戏类型
}

//请求活动排行数据
message ReqActivityRankDataMessage {
    int32 msgID                                  = 1;
    int32 cId                                    = 2; //活动配置id
    string host                                  = 3;
}

//返回活动排行数据
message ResActivityRankDataMessage {
    int32 msgID                                  = 1;
    int32 error                                  = 2;
    ActivityRankInfo myRank                      = 3; //自己数据
    repeated ActivityRankInfo rankList           = 4; //排行数据
}

message ActivityRankInfo {
   int32 ranking                           = 1; //名次
   string headId                           = 2; //头像id
   string name                             = 3; //名字
   double wagered                          = 4; //下注
   int32 currencyId                        = 5; //货币id
   double prize                            = 6; //奖励
}

//请求领取活动奖励
message ReqReceivePromotionsRewardMessage {
   int32 msgID                                  = 1;
   int32 cId                                    = 2; //配置唯一id
   int32 activityId                             = 3; //活动id
}

//返回领取活动奖励
message ResReceivePromotionsRewardMessage {
   int32 msgID                                  = 1;
   int32 error                                  = 2;
   repeated DItemShow reward                    = 3; //奖励
}

//请求bonus数据
message ReqBonusDataMessage {
   int32 msgID                                  = 1;
   int32 language                               = 2;
}

//返回bonus数据
message ResBonusDataMessage {
   int32 msgID                                  = 1;
   int32 error                                  = 2;
   repeated DepositBonus depositBonus           = 3; //充值奖励
   Quests quests                                = 4; //任务
   LuckySpin luckySpin                          = 5; //幸运转盘
   LevelUpRewards levelUpRewards                = 6; //vip升级奖励
   Recharge recharge                            = 7; //充电
   WeeklyCashBack weeklyCashBack                = 8; //周返水
   MonthlyCashBack monthlyCashBack              = 9; //月返水
   int32 curDeposit                             =10; //当前充值次数
   repeated FreeGameInfo freeGameList           =11; //免费游戏列表
}

//请求bonus明细
message ReqBonusDetailsDataMessage {
   int32 msgID                                  = 1;
}

//返回bonus明细
message ResBonusDetailsDataMessage {
   int32 msgID                                  = 1;
   int32 error                                  = 2;
   repeated BonusDetails bonusDetails           = 3; //bonus明细
}

message BonusDetails {
   int32 bonusType                              = 1; //1.quests 2.luckySpin 3.depositBonus 4.freeSpin 5.levelUpBonus 6.recharge 7.weeklyCashBack 8.monthlyCashBack
   repeated DItemShow bonusDetails              = 2;
}

message DepositBonus {
   int32 activityId                       = 1;
   int32 c_Id                             = 2;
   int32 depositType                      = 3; //1.首充 2.二充 3.三充
   int32 currencyId                       = 4; //币种
   int32 bonus                            = 5; //奖金 1.固定值 2.百分比
   double extra                           = 6; //额外
   double minimum                         = 7; //最小充值
   double maximum                         = 8; //最大充值
}

message Quests {
   int32 dailyNum                         = 1; //每日任务个数
   int32 dailyProgress                    = 2; //每日进度
   int32 weeklyNum                        = 3; //每周任务个数
   int32 weeklyProgress                   = 4; //每周进度
}

message LuckySpin {
   repeated DItemShow currentWager        = 1; //当前赌注
   double dailyWager                      = 2; //每日赌注（usd）
   int32 vipSpinLimit                     = 3; //vip限制
}

message LevelUpRewards {
   repeated DItemShow rewards             = 1; //奖励
}

message Recharge {
   int32 currentTire                      = 1;
   repeated DItemShow reward              = 2; //奖励
   repeated Tire tireList                 = 3;
   int32 vipLevelLimit                    = 4; //vip限制等级
}

message Tire {
   double startWager                      = 1; //开始赌注（usd）
   double endWager                        = 2; //结束赌注（usd）
   double rechargeRate                    = 3; //比例
   int32 currencyTire                     = 4;
}

message WeeklyCashBack {
   int64 endTime                          = 1; //结束时间
   repeated DItemShow currentWager        = 2; //当前赌注
   double minWager                        = 3; //最小赌注（usd）
   double maxWager                        = 4; //最大目赌注（usd）
   int32 vipLevelLimit                    = 5; //vip限制等级
   repeated DItemShow reward              = 6; //奖励
}

message MonthlyCashBack {
   int64 endTime                          = 1; //结束时间
   repeated DItemShow currentWager        = 2; //当前赌注
   double minWager                        = 3; //最小赌注（usd）
   double maxWager                        = 4; //最大目赌注（usd）
   int32 vipLevelLimit                    = 5; //vip限制等级
   repeated DItemShow reward              = 6; //奖励
}

message FreeGameInfo {
  int32 currencyId                        = 1;
  int32 gameId                            = 2; //游戏id
  string gameName                         = 3; //游戏名字
  int32 freeTimes                         = 4; //免费次数
}


//请求vipBonus奖励领取
message ReqVipBonusRewardsReceiveMessage {
   int32 msgID                             = 1;
   int32 bonusType                         = 2; //5.levelUpRewards 6.recharge 7.weekly 8.monthly
}

//返回vipBonus奖励领取
message ResVipBonusRewardsReceiveMessage {
   int32 msgID                             = 1;
   int32 error                             = 2;
   int32 bonusType                         = 3; //5.levelUpRewards 6.recharge 7.weekly 8.monthly
   repeated DItemShow show                 = 4; //奖励显示
}

//请求bonus交易数据
message ReqBonusTransactionsDataMessage {
   int32 msgID                             = 1;
   int32 page                              = 2; //页
   int32 pageSize                          = 3; //页数量
   int32 bonusType                         = 4; //1.quest 2.luckSpin 3.depositBonus 4.FreeSpin 5.LevelUpBonus 6.Recharge 7.WeeklyCashBack 8.MonthlyCashBonus
}

//返回bonus交易数据
message ResBonusTransactionsDataMessage {
   int32 msgID                             = 1;
   int32 error                             = 2;
   int32 page                              = 3; //页
   int32 pageSize                          = 4; //页数量
   int32 total                             = 5; //总条数
   int32 totalPage                         = 6; //总页数
   repeated BonusNote bonusNoteList        = 7;
}

message BonusNote {
   int32 bonusType                         = 1; //1.quests 2.luckySpin 3.depositBonus 4.freeSpin 5.levelUpBonus 6.recharge 7.weeklyCashBack 8.monthlyCashBack
   int32 currencyId                        = 2; //货币id
   double amount                           = 3; //金额
   int64 time                              = 4; //创建时间
}

//请求幸运转盘数据
message ReqLuckSpinDataMessage {
    int32 msgID                             = 1;
    string host                             = 2; //域名
    int32 turntable                         = 3; //1.邀请 2.vip 3.每日
    int32 language                          = 4; //1.英语 2.葡萄牙 3.西班牙
}

//返回幸运转盘数据
message ResLuckSpinDataMessage {
    int32 msgID                             = 1;
    int32 error                             = 2;
    repeated LuckSpinList luckSpinList      = 3;
}

message LuckSpinList {
    int32 activityId                        = 1;
    int32 tagSort                           = 2;
    string activityName                     = 3; //活动名字
    repeated LuckSpinInfo luckSpinList      = 4;
}

message LuckSpinInfo {
   int32 activityId                         = 1; //活动id
   int64 expiresTime                        = 2; //过期时间
   DItemShow drawTotalReward                = 3; //已领取的总奖励
   repeated SpinBonus  spinBonusList        = 4; //奖励记录
   repeated Turntable turntables            = 5; //转盘数据
   int64 startTime                          = 6; //活动开始时间
   int64 endTime                            = 7; //活动结束时间
   string desc                              = 8; //描述
   int32 tagSort                            = 9; //标签排序
   double needWager                         =10; //需要赌注（usd）
   repeated DItemShow currentWager          =11; //当前赌注
   int32 remainTimes                        =12; //剩余次数
   int32 unLockVip                          =13; //解锁vip
   int32 subType                            =14; //子类型
   string subName                           =15; //子名字
   DItemShow initReward                     =16; //初始奖励
   DItemShow availableReward                =17; //可领取奖励
   int32 turntableType                      =18; //1.邀请转盘 2.vip转盘 3.每日 4.每周
   bool finish                              =19; //完成
   double needRecharge                      =20; //需要充值（usd）
   repeated DItemShow currentRecharge       =21; //当前充值
   int32 needBetTimes                       =22; //需要投注次数
   int32 currentBetTimes                    =23; //当前投注次数
   string referralLink                      =24; //推荐链接
   int32 c_Id                               =25; //唯一id
}

message SpinBonus {
   string headId                            = 1; //头像id
   string userName                          = 2;
   string spin                              = 3;
   int32 currencyId                         = 4;
   double prize                             = 5;
}

message Turntable {
   int32 turntableId                        = 1;
   int32 itemId                             = 2;
   double num                               = 3;
}

//请求点击LuckSpin
message ReqClickLuckSpinMessage {
   int32 msgID                             = 1;
   int32 activityId                        = 2;
   int32 subType                           = 3; //vip需要
}

//返回点击LuckSpin
message ResClickLuckSpinMessage {
   int32 msgID                             = 1;
   int32 error                             = 2;
   int32 activityId                        = 3;
   int32 subType                           = 4; //vip需要
   DItemShow curReward                     = 5; //当前奖励
   int32 remainFreeTimes                   = 6; //剩余免费次数
   repeated DItemShow show                 = 7; //获得物品展示
   int32 turntableId                       = 8; //转盘id
}

//请求幸运转盘推广的提现
message ReqLuckSpinReferralWithdrawMessage {
   int32 msgID                             = 1;
}

//返回幸运转盘推广的提现
message ResLuckSpinReferralWithdrawMessage {
   int32 msgID                             = 1;
   int32 error                             = 2;
   repeated DItemShow show                 = 3; //获得物品展示
   DItemShow currentProcess                = 4; //当前进度
}

//请求每日竞赛数据
message ReqDailyContestDataMessage {
   int32 msgID                             = 1;
   string host                             = 2;
}

//返回每日竞赛数据
message ResDailyContestDataMessage {
   int32 msgID                             = 1;
   int32 error                             = 2;
   int64 startDate                         = 3; //开始日期
   int64 endDate                           = 4; //结束日期
   int64 remainTime                        = 5; //剩余时间
   double prizePool                        = 6; //奖池
   DailyRankInfo myRank                    = 7; //个人排名
   DailyRankInfo lastChampion              = 8; //上次winner
   repeated DailyRankInfo rankInfo         = 9; //排名
   int32 topNum                            =10; //排行人数
}

message DailyRankInfo {
   int32 ranking                           = 1; //名次
   string headId                           = 2; //头像
   string name                             = 3; //名字
   double wagered                          = 4; //下注
   double prize                            = 5; //奖励
   double prizeRate                        = 6; //奖比例
}

//请求每日竞赛历史数据
message ReqDailyContestHistoryDataMessage {
   int32 msgID                             = 1;
   string host                             = 2;
}

//返回每日竞赛历史数据
message ResDailyContestHistoryDataMessage {
   int32 msgID                             = 1;
   int32 error                             = 2;
   int64 startDate                         = 3; //开始日期
   int64 endDate                           = 4; //结束日期
   repeated DailyRankInfo rankInfo         = 5; //排名
}

//请求每周抽奖数据
message ReqWeeklyRaffleDataMessage {
   int32 msgID                             = 1;
   string host                             = 2;
}

//返回每周抽奖数据
message ResWeeklyRaffleDataMessage {
   int32 msgID                             = 1;
   int32 error                             = 2;
   string gameId                           = 3; //编号
   DItemShow superLuckDraw                 = 4; //超级幸运奖励
   int64 endTime                           = 5; //结束时间
   double dailyWagered                     = 6; //每日下注值（usd）
   double everyWagered                     = 7; //每下注值（usd）
   repeated RecentGetData recentGetList    = 8; //最近50条数据
   int32 thisRoundTickets                  = 9; //本轮票数
   repeated RewardAllocation rewardList    =10; //奖励分配
}

message RecentGetData {
   string headId                           = 1; //头像id
   string playerName                       = 2; //玩家名字
   int32 num                               = 3; //数量
}

message RewardAllocation {
   int32 rewardLevels                      = 1; //奖励等级
   int32 min                               = 2; //最小
   int32 max                               = 3; //最大
   int32 currencyId                        = 4; //货币id
   double reward                           = 5; //奖励
}

//请求每周抽奖我的数据
message ReqWeeklyRaffleMyTicketsDataMessage {
   int32 msgID                             = 1;
   int32 type                              = 2; //1.active 2.past 3.myWinnings
   string gameId                           = 3; //编号
}

//返回每周抽奖我的数据
message ResWeeklyRaffleMyTicketsDataMessage {
   int32 msgID                             = 1;
   int32 error                             = 2;
   int32 totalTickets                      = 3; //总票数
   int32 totalWinTickets                   = 4; //赢的总票数
   DItemShow totalPrize                    = 5; //总奖
   int32 type                              = 6; //1.active 2.past 3.myWinnings
   repeated TicketsInfo activeList         = 7; //活跃
   repeated TicketsInfo pastLists          = 8; //往期
   repeated TicketsInfo myWinningList      = 9; //我的赢数据
}

message TicketsInfo {
   int32 no                                = 1; //编号
   string gameId                           = 2; //期数
   int32 ticketNumbers                     = 3; //票号
   int32 currencyId                        = 4; //货币id
   double prize                            = 5; //奖
   string playerName                       = 6; //玩家名字
   string headId                           = 7; //头像
}

//请求每周抽奖结果数据
message ReqWeeklyRaffleResultDataMessage {
   int32 msgID                             = 1;
   string host                             = 2;
   string gameId                           = 3; //编号
   int32 page                              = 4; //页
   int32 pageSize                          = 5; //页数量
}

//返回每周抽奖我的数据
message ResWeeklyRaffleResultDataMessage {
   int32 msgID                             = 1;
   int32 error                             = 2;
   int32 page                              = 3; //页
   int32 pageSize                          = 4; //页数量
   int32 total                             = 5; //总条数
   int32 totalPage                         = 6; //总页数
   int32 totalTickets                      = 7; //总票数
   repeated TicketsInfo winnerList         = 8; //赢数据
}

//请求兑换码奖励
message ReqRedemptionCodeRewardMessage {
   int32 msgID                             = 1;
   string redemptionCode                   = 2; //兑换码
}

//请求兑换码奖励
message ResRedemptionCodeRewardMessage {
   int32 msgID                             = 1;
   int32 error                             = 2;
   repeated DItemShow rewardShow           = 3; //奖励显示
}

//请求活动数据
message ReqActivityDataMessage {
  int32 msgID                             = 1;
  string host                             = 2;
  int32 language                          = 3;
}

//返回活动数据
message ResActivityDataMessage {
  int32 msgID                                   = 1;
  int32 error                                   = 2;
  BonusRainInfo bonusRainInfo                   = 3; //红包雨
  RewardBoxInfo rewardBoxInfo                   = 4; //推荐宝箱
  MysteryBonusInfo mysteryBonusInfo             = 5; //神秘奖金
  PiggyBankInfo piggyBankInfo                   = 6; //存钱罐
  ContinuousDepositInfo continuousDepositInfo   = 7; //连续充值
  FirstChargeSignInInfo firstChargeSignInInfo   = 8; //首充签到
  RechargeRecoverInfo rechargeRecoverInfo       = 9; //充值返奖
  WageredRebatesInfo wageredRebatesInfo         =10; //投注返利
  DepositInviteBonusInfo depositInviteBonusInfo =11; //充值邀请奖励
}

message RechargeRecoverInfo {
  int32 c_Id                                    = 1; //唯一id
  int32 activityId                              = 2;
  repeated RechargeRecoverReward rewards        = 3; //奖励列表
  bool receive                                  = 4; //已领取
  DItemShow rechargeAmount                      = 5; //充值金额显示
  int32 status                                  = 6; //0.不可领取 1.可领取
}

//请求领取首充返奖
message ReqReceiveRechargeRecoverMessage {
  int32 msgID                                   = 1;
}

//返回领取首充返奖
message ResReceiveRechargeRecoverMessage {
  int32 msgID                                   = 1;
  int32 error                                   = 2;
  DItemShow rewardShow                          = 3; //奖励显示
}

message RechargeRecoverReward {
  int32 receiveType                             = 1; //1.投注 2.红包
  int32 currencyId                              = 2; //货币
  double minAmount                              = 3;
  double maxAmount                              = 4;
  string name                                   = 5; //名字
}

message FirstChargeSignInInfo {
  int32 c_Id                                    = 1; //唯一id
  int32 activityId                              = 2;
  string rule                                   = 3; //规则说明
  string icon                                   = 4; //图标
  int32 currDay                                 = 5; //当前天数
  int32 resetDay                                = 6; //重置天数
  int64 rechargeTime                            = 7; //充值时间
  repeated int32 receiveDay                     = 8; //领取天数
  repeated FirstChargeReward firstChargeRewards = 9; //首充奖励
}

message FirstChargeReward {
  int32 id                                    = 1; //天数
  int32 currencyId                            = 2; //货币
  double rewardAmount                         = 3; //奖励金额
}

//请求首充签到
message ReqFirstChargeSignInMessage {
  int32 msgID                                 = 1;
  int32 day                                   = 2;
}

//返回首充签到
message ResFirstChargeSignInMessage {
  int32 msgID                                 = 1;
  int32 error                                 = 2;
  DItemShow rewardShow                        = 3; //奖励显示
}

message ContinuousDepositInfo {
  repeated RechargeInfo depositList       = 1;
  repeated ButtonInfo buttonList          = 2;
  int32 activityId                        = 3;
  int32 c_Id                              = 4; //唯一id
}

message RechargeInfo {
  int32 id                                = 1; //充值次数
  string icon                             = 2; //图片
  double giveawayRate                     = 3; //赠送比例
}

message ButtonInfo {
  string name                             = 1; //名字
  int32 jumpType                          = 2; //跳转类型 1.内连 2.外链
  int32 popupLinks                        = 3; //弹框类型 1.任务 2.转盘 3.充值 4.客服 5.登录 6.注册
  string innerLinks                       = 4; //内部链接
  string externalLinks                    = 5; //外链接
}

message BonusRainInfo {
  string rule                             = 1; //规则说明
  string icon                             = 2; //图标
  DItemShow maxAmount                     = 3; //最大金额
  repeated RedEnvelopeInfo redEnvelopeList= 4; //红包配置
  repeated TimePeriodInfo timePeriodList  = 5; //时间段
  DItemShow currWagered                   = 6; //当前投注
  DItemShow currRecharge                  = 7; //当前充值
  repeated int32 receive                  = 8; //已领取
  int32 activityId                        = 9;
  int32 c_Id                              =10; //唯一id
  DItemShow showSingleAmount              =11; //单红包金额显示
  int32 receiveType                       =12; //1.满足任意一个 2.全部满足 3.不限制
}

message RedEnvelopeInfo {
  int32 id                                = 1;
  int32 r_currencyId                      = 2; //充值货币
  double minRecharge                      = 3; //最小充值
  double maxRecharge                      = 4; //最大充值
  int32 w_currencyId                      = 5; //投注货币
  double minWagered                       = 6; //最小投注
  double maxWagered                       = 7; //最大投注
  int32 minVip                            = 8; //vip最小等级
  int32 maxVip                            = 9; //vip最大等级
}

message TimePeriodInfo {
  int32 id                                = 1;
  int64 startTime                         = 2; //开始时间
  int64 endTime                           = 3; //结束时间
  DItemShow showAmount                    = 4; //显示金额
}

//请求领取红包
message ReqReceiveRedEnvelopeMessage {
  int32 msgID                             = 1;
  int32 timePeriodId                      = 2;
}

//返回领取红包
message ResReceiveRedEnvelopeMessage {
  int32 msgID                             = 1;
  int32 error                             = 2;
  repeated DItemShow rewardShow           = 3; //奖励显示
}

//推荐宝箱
message RewardBoxInfo {
  int32 directNum                         = 1; //直属下级
  string rule                             = 2; //规则说明
  string icon                             = 3; //图标
  int32 currencyId                        = 4; //货币
  double rechargeAmount                   = 5; //要求充值金额
  double wageredAmount                    = 6; //要求投注金额
  repeated int32 receive                  = 7; //已领取
  repeated BoxInfo boxList                = 8;
  repeated SubordinateInfo subordinateList= 9; //下级数据
  string referralLink                     =10; //推荐链接
  int32 activityId                        =11;
  int32 c_Id                              =12; //唯一id
}

//请求推荐宝箱下级
message ReqRewardBoxSubordinateDataMessage {
  int32 msgID 		                        = 1;
  int32 page                              = 2; //页
  int32 pageSize                          = 3; //页数量
  bool efficient                          = 4; //有效
}

//返回推荐宝箱下级
message ResRewardBoxSubordinateDataMessage {
  int32 msgID 		                        = 1;
  int32 error                             = 2; //错误码
  int32 page                              = 3;
  int32 pageSize                          = 4;
  int32 total                             = 5; //总条数
  int32 totalPage                         = 6; //总页数
  repeated SubordinateInfo subordinateList= 7; //下级数据
}

message BoxInfo {
  int32 num                               = 1; //推荐人数
  int32 currencyId                        = 2; //币种
  double amount                           = 3; //金额
}

//下级数据
message SubordinateInfo {
  string name                             = 1; //名字
  int32 currencyId                        = 2; //货币
  double recharge                         = 3; //充值
  double wagered                          = 4; //投注
}

//请求领取推荐宝箱
message ReqReceiveRewardBoxMessage {
  int32 msgID                             = 1;
  int32 inviteNum                         = 2;
}

//返回领取推荐宝箱
message ResReceiveRewardBoxMessage {
  int32 msgID                             = 1;
  int32 error                             = 2;
  DItemShow rewardShow                    = 3; //奖励显示
}

//神秘奖金
message MysteryBonusInfo {
  int64 signUpTime                        = 1; //报名时间
  string rule                             = 2; //规则说明
  string icon                             = 3; //图标
  repeated int32 receive                  = 4; //已领取
  repeated MysteryReward mysteryReward    = 5; //
  int32 activityId                        = 6;
  int32 c_Id                              = 7; //唯一id
}

message MysteryReward {
  int32 id                                = 1; //天数
  int64 settlementTime                    = 2; //结算领取时间
  int64 expiredTime                       = 3; //过期时间
  DItemShow currRecharge                  = 4; //当前充值
  int32 currencyId                        = 5; //货币
  double bonus                            = 6; //奖金
  repeated MysteryGear mysteryGearList    = 7; //挡位
}

message MysteryGear {
  int32 id                                = 1;
  int32 currencyId                        = 2;
  double rechargeMin                      = 3;
  double rechargeMax                      = 4;
  double rewardMin                        = 5;
  double rewardMax                        = 6;
}

//请求领取神秘奖金
message ReqReceiveMysteryBonusMessage {
  int32 msgID                             = 1;
  int32 day                               = 2;
}

//返回领取神秘奖金
message ResReceiveMysteryBonusMessage {
  int32 msgID                             = 1;
  int32 error                             = 2;
  DItemShow rewardShow                    = 3; //奖励显示
}

//存钱罐
message PiggyBankInfo {
  string rule                                   = 1; //规则说明
  string icon                                   = 2; //图标
  DItemShow currRecharge                        = 3; //当前充值
  int32 currDay                                 = 4; //当前天数
  repeated int32 receiveDay                     = 5; //领取天数
  repeated PiggyBankRewardInfo piggyBankRewardList = 6; //
  int32 activityId                              = 7;
  int32 c_Id                                    = 8; //唯一id
}

message PiggyBankRewardInfo {
  double achieveRecharge                        = 1; //要求充值
  int32 level                                   = 2; //等级
  repeated PiggyBankReward piggyBankRewardList  = 3; //
}

message PiggyBankReward {
  int32 id                                = 1; //天数
  int32 currencyId                        = 2; //货币
  double rewardAmount                     = 3; //奖励金额
}

//请求领取存钱罐
message ReqReceivePiggyBankMessage {
  int32 msgID                             = 1;
  int32 day                               = 2;
}

//返回领取存钱罐
message ResReceivePiggyBankMessage {
  int32 msgID                             = 1;
  int32 error                             = 2;
  DItemShow rewardShow                    = 3; //奖励显示
}

//请求vip数据
message ReqVipDataMessage {
  int32 msgID                             = 1;
}

//返回vip数据
message ResVipDataMessage {
  int32 msgID                             = 1;
  int32 error                             = 2;
  VipSignInInfo vipSignInInfo             = 3; //签到数据
  ReceiveRewardInfo receiveRewardInfo     = 4; //奖励领取
}

message VipSignInInfo {
  int32 currDay                           = 1; //当前天数
  bool activation                         = 2; //是否充值
  repeated int32 receiveDays              = 3; //领取天数
  repeated SignInfo signList              = 4; //签到数据
}

message SignInfo {
  int32 day                               = 1; //天数
  DCurrencyItem reward                    = 2; //奖励
}

message ReceiveRewardInfo {
  int32 currencyId                        = 1; //货币
  double dailyWagered                     = 2; //每日下注
  double weeklyWagered                    = 3; //每周下注
  double monthlyWagered                   = 4; //每月下注
  int64 dailyTime                         = 5; //每日结束时间
  int64 weeklyTime                        = 6; //每周结束时间
  int64 monthlyTime                       = 7; //每月结束时间
  repeated int32 receiveReward            = 8; //领取奖励
}

//请求vip签到
message ReqVipSignInMessage {
  int32 msgID                             = 1;
  int32 day                               = 2;
}

//返回vip签到
message ResVipSignInMessage {
  int32 msgID                             = 1;
  int32 error                             = 2;
  DItemShow rewardShow                    = 3; //奖励显示
}

//请求领取vip奖励
message ReqReceiveVipRewardMessage {
  int32 msgID                             = 1;
  int32 type                              = 2; //1.每日 2.每周 3.每月
}

//返回领取vip奖励
message ResReceiveVipRewardMessage {
  int32 msgID                             = 1;
  int32 error                             = 2;
  DItemShow rewardShow                    = 3; //奖励显示
}

//投注返利数据
message WageredRebatesInfo {
  int32 msgID                             = 1;
  int32 error                             = 2;
  int32 topNum                            = 3; //排行人数
  int64 endTime                           = 4; //结束时间
  int64 awardClearTime                    = 5; //奖励清空时间
  int64 activeTime                        = 6; //激活时间
  bool status                             = 7; //领取状态
  string icon                             = 8; //icon
  string rule                             = 9; //规则
  DItemShow claim                         =10; //可领取
  DItemShow win                           =11;
  DItemShow bonus                         =12;
  DItemShow refund                        =13;
  ActivityRankInfo myRank                 =14; //个人排名
  repeated ActivityRankInfo history       =15; //历史
  repeated ActivityRankInfo rankInfo      =16; //排名
  string timeZone                         =17; //时区
}

//请求领取投注返利奖励
message ReqReceiveWageredRebatesMessage {
  int32 msgID                             = 1;
}

//返回领取投注返利奖励
message ResReceiveWageredRebatesMessage {
  int32 msgID                             = 1;
  int32 error                             = 2;
  DItemShow rewardShow                    = 3;
}

//首充邀请奖励信息
message DepositInviteBonusInfo {
  int32 currencyId                                         = 1;
  double lockedBonus                                       = 2; //解锁奖励
  double claimedBonus                                      = 3; //领取奖励
  double unClaimedBonus                                    = 4; //未领取奖励
  double validWagered                                      = 5; //有效投注
  double chargeAmount                                      = 6; //充值金额
  string icon                                              = 7; //icon
  string rule                                              = 8; //规则
  int32 turnoverMul                                        = 9; //打码量
  string referralLink                                      =10; //推荐连接
  string referralCode                                      =11; //邀请码
  repeated RewardData rewardData                           =12; //奖励数据
  repeated int32 receiveBonus                              =13; //已领取奖励
  string timeZone                                          =14; //时区
  int64 limitTimeStart                                     =15; //限制开始时间
  int64 limitTimeEnd                                       =16; //限制结束时间
  double needChargeAmount                                  =17; //需要首充金额
}

message RewardData {
  int32 id                                = 1; //挡位
  double rewardRate                       = 2; //奖励比例
  double unlockRate                       = 3; //解锁比例
  double totalUnlockRate                  = 4; //总解锁比例
}

message FriendsValidWageredData {
  string playerName                       = 1; //名字
  double validWagered                     = 2; //有效下注
  string headId                           = 3; //头像
}

//请求领取充值邀请奖励
message ReqReceiveDepositInviteBonusMessage {
  int32 msgID                             = 1;
  int32 gearId                            = 2; //挡位
}

//返回领取充值邀请奖励
message ResReceiveDepositInviteBonusMessage {
  int32 msgID                             = 1;
  int32 error                             = 2;
  DItemShow rewardShow                    = 3;
}

//请求查看明细
message ReqCheckDetailsDataMessage {
  int32 msgID                             = 1;
  int32 page                              = 2; //页
  int32 pageSize                          = 3; //页数量
}

//返回查看明细
message ResCheckDetailsDataMessage {
  int32 msgID                                              = 1;
  int32 error                                              = 2;
  int32 page                                               = 3;
  int32 pageSize                                           = 4;
  int32 total                                              = 5; //总条数
  int32 totalPage                                          = 6; //总页数
  repeated FriendsValidWageredData friendsValidWageredList = 7; //有效朋友下注
}