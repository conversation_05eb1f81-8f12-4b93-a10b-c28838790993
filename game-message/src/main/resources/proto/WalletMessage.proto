syntax = "proto3";

package ProtoMessage;
option java_package = "com.proto";
import "CommonMessage.proto";

//请求下注历史
message ReqBetHistoryDataMessage {
   int32 msgID 		           = 1;
   int32 gameId              = 2; //游戏id
   int32 gameType            = 3; //0.all 101.原创 102.电子 103.视讯 201.体育
   int32 platformId          = 4; //平台
   int32 assets              = 5; //资产 0.all
   int32 past                = 6; //0.all 1.24小时 2.7day 3.30day 4.60day
   int32 page                = 7; //页
   int32 pageSize            = 8; //页数量
   int64 statTime            = 9; //开始时间
   int64 endTime             =10; //结束时间
   int32 language            =11; //语言id
}

//返回下注历史
message ResBetHistoryDataMessage {
   int32 msgID 		           = 1;
   int32 error               = 2; //错误码
   int32 gameId              = 3; //游戏id
   int32 gameType            = 4; //0.all 101.原创 102.电子 103.视讯 201.体育
   int32 platformId          = 5; //平台
   int32 assets              = 6; //资产 0.all
   int32 past                = 7; //0.all 1.24小时 2.7day 3.30day 4.60day
   int32 page                = 8;
   int32 pageSize            = 9;
   int32 total               =10; //总条数
   int32 totalPage           =11; //总页数
   repeated BetInfo betList  =12; //下注列表
}

//请求交易
message ReqTransactionDataMessage {
   int32 msgID               = 1;
   int32 transactionType     = 2; //0.all 1.deposit 2.withdraw 3.bill
   int32 assets              = 3; //资产 0.all
   int32 past                = 4; //1.24小时 2.7day 3.30day 4.60day 5.90day
   int32 status              = 5; //0.all 1.complete 2.processing 3.failed 4.canceled
   int32 page                = 6; //页
   int32 pageSize            = 7; //页数量
   int64 statTime            = 8; //开始时间
   int64 endTime             = 9; //结束时间
   int32 language            =10; //语言id
}

//返回交易
message ResTransactionDataMessage {
   int32 msgID                              = 1;
   int32 error                              = 2; //错误码
   int32 transactionType                    = 3; //0.all 1.deposit 2.withdraw 3.bill
   int32 assets                             = 4; //资产 0.all
   int32 past                               = 5; //1.24小时 2.7day 3.30day 4.60day 5.90day
   int32 status                             = 6; //0.all 1.complete 2.processing 3.failed 4.canceled
   int32 page                               = 7;
   int32 pageSize                           = 8;
   int32 total                              = 9; //总条数
   int32 totalPage                          =10; //总页数
   repeated TransactionInfo transactionList =11; //交易列表
}

message TransactionInfo {
   string betId              = 1; //下注id
   string gameName           = 2; //游戏名字
   int32 currencyId          = 3; //货币id
   double amount             = 4; //金额
   int64 time                = 5; //时间
   double payout             = 6; //赔率
   double profit             = 7; //利润
   double balance            = 8; //余额
   int32 status              = 9; //状态 1.complete 2.processing 3.failed 4.canceled
   int32 type                =10; //类型 1.deposit 2.withdraw 3.bill 4.bonus
   string walletAddress      =11; //钱包地址
   string orderId            =12; //订单id
   double fee                =13; //手续费
   string channel            =14; //渠道
   string tips               =15; //备注
}
