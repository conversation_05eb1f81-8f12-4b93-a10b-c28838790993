// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: InboxMessage.proto

package com.proto;

public final class InboxMessage {
  private InboxMessage() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  public interface ReqInboxDataMessageOrBuilder extends
      // @@protoc_insertion_point(interface_extends:ProtoMessage.ReqInboxDataMessage)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>int32 msgID = 1;</code>
     * @return The msgID.
     */
    int getMsgID();
  }
  /**
   * <pre>
   *请求邮件数据
   * </pre>
   *
   * Protobuf type {@code ProtoMessage.ReqInboxDataMessage}
   */
  public static final class ReqInboxDataMessage extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:ProtoMessage.ReqInboxDataMessage)
      ReqInboxDataMessageOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use ReqInboxDataMessage.newBuilder() to construct.
    private ReqInboxDataMessage(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private ReqInboxDataMessage() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new ReqInboxDataMessage();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private ReqInboxDataMessage(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {

              msgID_ = input.readInt32();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.proto.InboxMessage.internal_static_ProtoMessage_ReqInboxDataMessage_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.proto.InboxMessage.internal_static_ProtoMessage_ReqInboxDataMessage_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.proto.InboxMessage.ReqInboxDataMessage.class, com.proto.InboxMessage.ReqInboxDataMessage.Builder.class);
    }

    public static final int MSGID_FIELD_NUMBER = 1;
    private int msgID_;
    /**
     * <code>int32 msgID = 1;</code>
     * @return The msgID.
     */
    @java.lang.Override
    public int getMsgID() {
      return msgID_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (msgID_ != 0) {
        output.writeInt32(1, msgID_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (msgID_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, msgID_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.proto.InboxMessage.ReqInboxDataMessage)) {
        return super.equals(obj);
      }
      com.proto.InboxMessage.ReqInboxDataMessage other = (com.proto.InboxMessage.ReqInboxDataMessage) obj;

      if (getMsgID()
          != other.getMsgID()) return false;
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + MSGID_FIELD_NUMBER;
      hash = (53 * hash) + getMsgID();
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.proto.InboxMessage.ReqInboxDataMessage parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.proto.InboxMessage.ReqInboxDataMessage parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.proto.InboxMessage.ReqInboxDataMessage parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.proto.InboxMessage.ReqInboxDataMessage parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.proto.InboxMessage.ReqInboxDataMessage parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.proto.InboxMessage.ReqInboxDataMessage parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.proto.InboxMessage.ReqInboxDataMessage parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.proto.InboxMessage.ReqInboxDataMessage parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.proto.InboxMessage.ReqInboxDataMessage parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.proto.InboxMessage.ReqInboxDataMessage parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.proto.InboxMessage.ReqInboxDataMessage parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.proto.InboxMessage.ReqInboxDataMessage parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.proto.InboxMessage.ReqInboxDataMessage prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     *请求邮件数据
     * </pre>
     *
     * Protobuf type {@code ProtoMessage.ReqInboxDataMessage}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:ProtoMessage.ReqInboxDataMessage)
        com.proto.InboxMessage.ReqInboxDataMessageOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.proto.InboxMessage.internal_static_ProtoMessage_ReqInboxDataMessage_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.proto.InboxMessage.internal_static_ProtoMessage_ReqInboxDataMessage_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.proto.InboxMessage.ReqInboxDataMessage.class, com.proto.InboxMessage.ReqInboxDataMessage.Builder.class);
      }

      // Construct using com.proto.InboxMessage.ReqInboxDataMessage.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        msgID_ = 0;

        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.proto.InboxMessage.internal_static_ProtoMessage_ReqInboxDataMessage_descriptor;
      }

      @java.lang.Override
      public com.proto.InboxMessage.ReqInboxDataMessage getDefaultInstanceForType() {
        return com.proto.InboxMessage.ReqInboxDataMessage.getDefaultInstance();
      }

      @java.lang.Override
      public com.proto.InboxMessage.ReqInboxDataMessage build() {
        com.proto.InboxMessage.ReqInboxDataMessage result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.proto.InboxMessage.ReqInboxDataMessage buildPartial() {
        com.proto.InboxMessage.ReqInboxDataMessage result = new com.proto.InboxMessage.ReqInboxDataMessage(this);
        result.msgID_ = msgID_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.proto.InboxMessage.ReqInboxDataMessage) {
          return mergeFrom((com.proto.InboxMessage.ReqInboxDataMessage)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.proto.InboxMessage.ReqInboxDataMessage other) {
        if (other == com.proto.InboxMessage.ReqInboxDataMessage.getDefaultInstance()) return this;
        if (other.getMsgID() != 0) {
          setMsgID(other.getMsgID());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.proto.InboxMessage.ReqInboxDataMessage parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.proto.InboxMessage.ReqInboxDataMessage) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }

      private int msgID_ ;
      /**
       * <code>int32 msgID = 1;</code>
       * @return The msgID.
       */
      @java.lang.Override
      public int getMsgID() {
        return msgID_;
      }
      /**
       * <code>int32 msgID = 1;</code>
       * @param value The msgID to set.
       * @return This builder for chaining.
       */
      public Builder setMsgID(int value) {
        
        msgID_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>int32 msgID = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearMsgID() {
        
        msgID_ = 0;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:ProtoMessage.ReqInboxDataMessage)
    }

    // @@protoc_insertion_point(class_scope:ProtoMessage.ReqInboxDataMessage)
    private static final com.proto.InboxMessage.ReqInboxDataMessage DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.proto.InboxMessage.ReqInboxDataMessage();
    }

    public static com.proto.InboxMessage.ReqInboxDataMessage getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<ReqInboxDataMessage>
        PARSER = new com.google.protobuf.AbstractParser<ReqInboxDataMessage>() {
      @java.lang.Override
      public ReqInboxDataMessage parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new ReqInboxDataMessage(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<ReqInboxDataMessage> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<ReqInboxDataMessage> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.proto.InboxMessage.ReqInboxDataMessage getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface ResInboxDataMessageOrBuilder extends
      // @@protoc_insertion_point(interface_extends:ProtoMessage.ResInboxDataMessage)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>int32 msgID = 1;</code>
     * @return The msgID.
     */
    int getMsgID();

    /**
     * <pre>
     *错误码
     * </pre>
     *
     * <code>int32 error = 2;</code>
     * @return The error.
     */
    int getError();

    /**
     * <pre>
     *邮件列表
     * </pre>
     *
     * <code>repeated .ProtoMessage.InboxInfo inboxList = 3;</code>
     */
    java.util.List<com.proto.CommonMessage.InboxInfo> 
        getInboxListList();
    /**
     * <pre>
     *邮件列表
     * </pre>
     *
     * <code>repeated .ProtoMessage.InboxInfo inboxList = 3;</code>
     */
    com.proto.CommonMessage.InboxInfo getInboxList(int index);
    /**
     * <pre>
     *邮件列表
     * </pre>
     *
     * <code>repeated .ProtoMessage.InboxInfo inboxList = 3;</code>
     */
    int getInboxListCount();
    /**
     * <pre>
     *邮件列表
     * </pre>
     *
     * <code>repeated .ProtoMessage.InboxInfo inboxList = 3;</code>
     */
    java.util.List<? extends com.proto.CommonMessage.InboxInfoOrBuilder> 
        getInboxListOrBuilderList();
    /**
     * <pre>
     *邮件列表
     * </pre>
     *
     * <code>repeated .ProtoMessage.InboxInfo inboxList = 3;</code>
     */
    com.proto.CommonMessage.InboxInfoOrBuilder getInboxListOrBuilder(
        int index);
  }
  /**
   * <pre>
   *返回邮件数据
   * </pre>
   *
   * Protobuf type {@code ProtoMessage.ResInboxDataMessage}
   */
  public static final class ResInboxDataMessage extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:ProtoMessage.ResInboxDataMessage)
      ResInboxDataMessageOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use ResInboxDataMessage.newBuilder() to construct.
    private ResInboxDataMessage(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private ResInboxDataMessage() {
      inboxList_ = java.util.Collections.emptyList();
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new ResInboxDataMessage();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private ResInboxDataMessage(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {

              msgID_ = input.readInt32();
              break;
            }
            case 16: {

              error_ = input.readInt32();
              break;
            }
            case 26: {
              if (!((mutable_bitField0_ & 0x00000001) != 0)) {
                inboxList_ = new java.util.ArrayList<com.proto.CommonMessage.InboxInfo>();
                mutable_bitField0_ |= 0x00000001;
              }
              inboxList_.add(
                  input.readMessage(com.proto.CommonMessage.InboxInfo.parser(), extensionRegistry));
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        if (((mutable_bitField0_ & 0x00000001) != 0)) {
          inboxList_ = java.util.Collections.unmodifiableList(inboxList_);
        }
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.proto.InboxMessage.internal_static_ProtoMessage_ResInboxDataMessage_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.proto.InboxMessage.internal_static_ProtoMessage_ResInboxDataMessage_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.proto.InboxMessage.ResInboxDataMessage.class, com.proto.InboxMessage.ResInboxDataMessage.Builder.class);
    }

    public static final int MSGID_FIELD_NUMBER = 1;
    private int msgID_;
    /**
     * <code>int32 msgID = 1;</code>
     * @return The msgID.
     */
    @java.lang.Override
    public int getMsgID() {
      return msgID_;
    }

    public static final int ERROR_FIELD_NUMBER = 2;
    private int error_;
    /**
     * <pre>
     *错误码
     * </pre>
     *
     * <code>int32 error = 2;</code>
     * @return The error.
     */
    @java.lang.Override
    public int getError() {
      return error_;
    }

    public static final int INBOXLIST_FIELD_NUMBER = 3;
    private java.util.List<com.proto.CommonMessage.InboxInfo> inboxList_;
    /**
     * <pre>
     *邮件列表
     * </pre>
     *
     * <code>repeated .ProtoMessage.InboxInfo inboxList = 3;</code>
     */
    @java.lang.Override
    public java.util.List<com.proto.CommonMessage.InboxInfo> getInboxListList() {
      return inboxList_;
    }
    /**
     * <pre>
     *邮件列表
     * </pre>
     *
     * <code>repeated .ProtoMessage.InboxInfo inboxList = 3;</code>
     */
    @java.lang.Override
    public java.util.List<? extends com.proto.CommonMessage.InboxInfoOrBuilder> 
        getInboxListOrBuilderList() {
      return inboxList_;
    }
    /**
     * <pre>
     *邮件列表
     * </pre>
     *
     * <code>repeated .ProtoMessage.InboxInfo inboxList = 3;</code>
     */
    @java.lang.Override
    public int getInboxListCount() {
      return inboxList_.size();
    }
    /**
     * <pre>
     *邮件列表
     * </pre>
     *
     * <code>repeated .ProtoMessage.InboxInfo inboxList = 3;</code>
     */
    @java.lang.Override
    public com.proto.CommonMessage.InboxInfo getInboxList(int index) {
      return inboxList_.get(index);
    }
    /**
     * <pre>
     *邮件列表
     * </pre>
     *
     * <code>repeated .ProtoMessage.InboxInfo inboxList = 3;</code>
     */
    @java.lang.Override
    public com.proto.CommonMessage.InboxInfoOrBuilder getInboxListOrBuilder(
        int index) {
      return inboxList_.get(index);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (msgID_ != 0) {
        output.writeInt32(1, msgID_);
      }
      if (error_ != 0) {
        output.writeInt32(2, error_);
      }
      for (int i = 0; i < inboxList_.size(); i++) {
        output.writeMessage(3, inboxList_.get(i));
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (msgID_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, msgID_);
      }
      if (error_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(2, error_);
      }
      for (int i = 0; i < inboxList_.size(); i++) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(3, inboxList_.get(i));
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.proto.InboxMessage.ResInboxDataMessage)) {
        return super.equals(obj);
      }
      com.proto.InboxMessage.ResInboxDataMessage other = (com.proto.InboxMessage.ResInboxDataMessage) obj;

      if (getMsgID()
          != other.getMsgID()) return false;
      if (getError()
          != other.getError()) return false;
      if (!getInboxListList()
          .equals(other.getInboxListList())) return false;
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + MSGID_FIELD_NUMBER;
      hash = (53 * hash) + getMsgID();
      hash = (37 * hash) + ERROR_FIELD_NUMBER;
      hash = (53 * hash) + getError();
      if (getInboxListCount() > 0) {
        hash = (37 * hash) + INBOXLIST_FIELD_NUMBER;
        hash = (53 * hash) + getInboxListList().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.proto.InboxMessage.ResInboxDataMessage parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.proto.InboxMessage.ResInboxDataMessage parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.proto.InboxMessage.ResInboxDataMessage parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.proto.InboxMessage.ResInboxDataMessage parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.proto.InboxMessage.ResInboxDataMessage parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.proto.InboxMessage.ResInboxDataMessage parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.proto.InboxMessage.ResInboxDataMessage parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.proto.InboxMessage.ResInboxDataMessage parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.proto.InboxMessage.ResInboxDataMessage parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.proto.InboxMessage.ResInboxDataMessage parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.proto.InboxMessage.ResInboxDataMessage parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.proto.InboxMessage.ResInboxDataMessage parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.proto.InboxMessage.ResInboxDataMessage prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     *返回邮件数据
     * </pre>
     *
     * Protobuf type {@code ProtoMessage.ResInboxDataMessage}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:ProtoMessage.ResInboxDataMessage)
        com.proto.InboxMessage.ResInboxDataMessageOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.proto.InboxMessage.internal_static_ProtoMessage_ResInboxDataMessage_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.proto.InboxMessage.internal_static_ProtoMessage_ResInboxDataMessage_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.proto.InboxMessage.ResInboxDataMessage.class, com.proto.InboxMessage.ResInboxDataMessage.Builder.class);
      }

      // Construct using com.proto.InboxMessage.ResInboxDataMessage.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
          getInboxListFieldBuilder();
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        msgID_ = 0;

        error_ = 0;

        if (inboxListBuilder_ == null) {
          inboxList_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000001);
        } else {
          inboxListBuilder_.clear();
        }
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.proto.InboxMessage.internal_static_ProtoMessage_ResInboxDataMessage_descriptor;
      }

      @java.lang.Override
      public com.proto.InboxMessage.ResInboxDataMessage getDefaultInstanceForType() {
        return com.proto.InboxMessage.ResInboxDataMessage.getDefaultInstance();
      }

      @java.lang.Override
      public com.proto.InboxMessage.ResInboxDataMessage build() {
        com.proto.InboxMessage.ResInboxDataMessage result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.proto.InboxMessage.ResInboxDataMessage buildPartial() {
        com.proto.InboxMessage.ResInboxDataMessage result = new com.proto.InboxMessage.ResInboxDataMessage(this);
        int from_bitField0_ = bitField0_;
        result.msgID_ = msgID_;
        result.error_ = error_;
        if (inboxListBuilder_ == null) {
          if (((bitField0_ & 0x00000001) != 0)) {
            inboxList_ = java.util.Collections.unmodifiableList(inboxList_);
            bitField0_ = (bitField0_ & ~0x00000001);
          }
          result.inboxList_ = inboxList_;
        } else {
          result.inboxList_ = inboxListBuilder_.build();
        }
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.proto.InboxMessage.ResInboxDataMessage) {
          return mergeFrom((com.proto.InboxMessage.ResInboxDataMessage)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.proto.InboxMessage.ResInboxDataMessage other) {
        if (other == com.proto.InboxMessage.ResInboxDataMessage.getDefaultInstance()) return this;
        if (other.getMsgID() != 0) {
          setMsgID(other.getMsgID());
        }
        if (other.getError() != 0) {
          setError(other.getError());
        }
        if (inboxListBuilder_ == null) {
          if (!other.inboxList_.isEmpty()) {
            if (inboxList_.isEmpty()) {
              inboxList_ = other.inboxList_;
              bitField0_ = (bitField0_ & ~0x00000001);
            } else {
              ensureInboxListIsMutable();
              inboxList_.addAll(other.inboxList_);
            }
            onChanged();
          }
        } else {
          if (!other.inboxList_.isEmpty()) {
            if (inboxListBuilder_.isEmpty()) {
              inboxListBuilder_.dispose();
              inboxListBuilder_ = null;
              inboxList_ = other.inboxList_;
              bitField0_ = (bitField0_ & ~0x00000001);
              inboxListBuilder_ = 
                com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                   getInboxListFieldBuilder() : null;
            } else {
              inboxListBuilder_.addAllMessages(other.inboxList_);
            }
          }
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.proto.InboxMessage.ResInboxDataMessage parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.proto.InboxMessage.ResInboxDataMessage) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private int msgID_ ;
      /**
       * <code>int32 msgID = 1;</code>
       * @return The msgID.
       */
      @java.lang.Override
      public int getMsgID() {
        return msgID_;
      }
      /**
       * <code>int32 msgID = 1;</code>
       * @param value The msgID to set.
       * @return This builder for chaining.
       */
      public Builder setMsgID(int value) {
        
        msgID_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>int32 msgID = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearMsgID() {
        
        msgID_ = 0;
        onChanged();
        return this;
      }

      private int error_ ;
      /**
       * <pre>
       *错误码
       * </pre>
       *
       * <code>int32 error = 2;</code>
       * @return The error.
       */
      @java.lang.Override
      public int getError() {
        return error_;
      }
      /**
       * <pre>
       *错误码
       * </pre>
       *
       * <code>int32 error = 2;</code>
       * @param value The error to set.
       * @return This builder for chaining.
       */
      public Builder setError(int value) {
        
        error_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *错误码
       * </pre>
       *
       * <code>int32 error = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearError() {
        
        error_ = 0;
        onChanged();
        return this;
      }

      private java.util.List<com.proto.CommonMessage.InboxInfo> inboxList_ =
        java.util.Collections.emptyList();
      private void ensureInboxListIsMutable() {
        if (!((bitField0_ & 0x00000001) != 0)) {
          inboxList_ = new java.util.ArrayList<com.proto.CommonMessage.InboxInfo>(inboxList_);
          bitField0_ |= 0x00000001;
         }
      }

      private com.google.protobuf.RepeatedFieldBuilderV3<
          com.proto.CommonMessage.InboxInfo, com.proto.CommonMessage.InboxInfo.Builder, com.proto.CommonMessage.InboxInfoOrBuilder> inboxListBuilder_;

      /**
       * <pre>
       *邮件列表
       * </pre>
       *
       * <code>repeated .ProtoMessage.InboxInfo inboxList = 3;</code>
       */
      public java.util.List<com.proto.CommonMessage.InboxInfo> getInboxListList() {
        if (inboxListBuilder_ == null) {
          return java.util.Collections.unmodifiableList(inboxList_);
        } else {
          return inboxListBuilder_.getMessageList();
        }
      }
      /**
       * <pre>
       *邮件列表
       * </pre>
       *
       * <code>repeated .ProtoMessage.InboxInfo inboxList = 3;</code>
       */
      public int getInboxListCount() {
        if (inboxListBuilder_ == null) {
          return inboxList_.size();
        } else {
          return inboxListBuilder_.getCount();
        }
      }
      /**
       * <pre>
       *邮件列表
       * </pre>
       *
       * <code>repeated .ProtoMessage.InboxInfo inboxList = 3;</code>
       */
      public com.proto.CommonMessage.InboxInfo getInboxList(int index) {
        if (inboxListBuilder_ == null) {
          return inboxList_.get(index);
        } else {
          return inboxListBuilder_.getMessage(index);
        }
      }
      /**
       * <pre>
       *邮件列表
       * </pre>
       *
       * <code>repeated .ProtoMessage.InboxInfo inboxList = 3;</code>
       */
      public Builder setInboxList(
          int index, com.proto.CommonMessage.InboxInfo value) {
        if (inboxListBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureInboxListIsMutable();
          inboxList_.set(index, value);
          onChanged();
        } else {
          inboxListBuilder_.setMessage(index, value);
        }
        return this;
      }
      /**
       * <pre>
       *邮件列表
       * </pre>
       *
       * <code>repeated .ProtoMessage.InboxInfo inboxList = 3;</code>
       */
      public Builder setInboxList(
          int index, com.proto.CommonMessage.InboxInfo.Builder builderForValue) {
        if (inboxListBuilder_ == null) {
          ensureInboxListIsMutable();
          inboxList_.set(index, builderForValue.build());
          onChanged();
        } else {
          inboxListBuilder_.setMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <pre>
       *邮件列表
       * </pre>
       *
       * <code>repeated .ProtoMessage.InboxInfo inboxList = 3;</code>
       */
      public Builder addInboxList(com.proto.CommonMessage.InboxInfo value) {
        if (inboxListBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureInboxListIsMutable();
          inboxList_.add(value);
          onChanged();
        } else {
          inboxListBuilder_.addMessage(value);
        }
        return this;
      }
      /**
       * <pre>
       *邮件列表
       * </pre>
       *
       * <code>repeated .ProtoMessage.InboxInfo inboxList = 3;</code>
       */
      public Builder addInboxList(
          int index, com.proto.CommonMessage.InboxInfo value) {
        if (inboxListBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureInboxListIsMutable();
          inboxList_.add(index, value);
          onChanged();
        } else {
          inboxListBuilder_.addMessage(index, value);
        }
        return this;
      }
      /**
       * <pre>
       *邮件列表
       * </pre>
       *
       * <code>repeated .ProtoMessage.InboxInfo inboxList = 3;</code>
       */
      public Builder addInboxList(
          com.proto.CommonMessage.InboxInfo.Builder builderForValue) {
        if (inboxListBuilder_ == null) {
          ensureInboxListIsMutable();
          inboxList_.add(builderForValue.build());
          onChanged();
        } else {
          inboxListBuilder_.addMessage(builderForValue.build());
        }
        return this;
      }
      /**
       * <pre>
       *邮件列表
       * </pre>
       *
       * <code>repeated .ProtoMessage.InboxInfo inboxList = 3;</code>
       */
      public Builder addInboxList(
          int index, com.proto.CommonMessage.InboxInfo.Builder builderForValue) {
        if (inboxListBuilder_ == null) {
          ensureInboxListIsMutable();
          inboxList_.add(index, builderForValue.build());
          onChanged();
        } else {
          inboxListBuilder_.addMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <pre>
       *邮件列表
       * </pre>
       *
       * <code>repeated .ProtoMessage.InboxInfo inboxList = 3;</code>
       */
      public Builder addAllInboxList(
          java.lang.Iterable<? extends com.proto.CommonMessage.InboxInfo> values) {
        if (inboxListBuilder_ == null) {
          ensureInboxListIsMutable();
          com.google.protobuf.AbstractMessageLite.Builder.addAll(
              values, inboxList_);
          onChanged();
        } else {
          inboxListBuilder_.addAllMessages(values);
        }
        return this;
      }
      /**
       * <pre>
       *邮件列表
       * </pre>
       *
       * <code>repeated .ProtoMessage.InboxInfo inboxList = 3;</code>
       */
      public Builder clearInboxList() {
        if (inboxListBuilder_ == null) {
          inboxList_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000001);
          onChanged();
        } else {
          inboxListBuilder_.clear();
        }
        return this;
      }
      /**
       * <pre>
       *邮件列表
       * </pre>
       *
       * <code>repeated .ProtoMessage.InboxInfo inboxList = 3;</code>
       */
      public Builder removeInboxList(int index) {
        if (inboxListBuilder_ == null) {
          ensureInboxListIsMutable();
          inboxList_.remove(index);
          onChanged();
        } else {
          inboxListBuilder_.remove(index);
        }
        return this;
      }
      /**
       * <pre>
       *邮件列表
       * </pre>
       *
       * <code>repeated .ProtoMessage.InboxInfo inboxList = 3;</code>
       */
      public com.proto.CommonMessage.InboxInfo.Builder getInboxListBuilder(
          int index) {
        return getInboxListFieldBuilder().getBuilder(index);
      }
      /**
       * <pre>
       *邮件列表
       * </pre>
       *
       * <code>repeated .ProtoMessage.InboxInfo inboxList = 3;</code>
       */
      public com.proto.CommonMessage.InboxInfoOrBuilder getInboxListOrBuilder(
          int index) {
        if (inboxListBuilder_ == null) {
          return inboxList_.get(index);  } else {
          return inboxListBuilder_.getMessageOrBuilder(index);
        }
      }
      /**
       * <pre>
       *邮件列表
       * </pre>
       *
       * <code>repeated .ProtoMessage.InboxInfo inboxList = 3;</code>
       */
      public java.util.List<? extends com.proto.CommonMessage.InboxInfoOrBuilder> 
           getInboxListOrBuilderList() {
        if (inboxListBuilder_ != null) {
          return inboxListBuilder_.getMessageOrBuilderList();
        } else {
          return java.util.Collections.unmodifiableList(inboxList_);
        }
      }
      /**
       * <pre>
       *邮件列表
       * </pre>
       *
       * <code>repeated .ProtoMessage.InboxInfo inboxList = 3;</code>
       */
      public com.proto.CommonMessage.InboxInfo.Builder addInboxListBuilder() {
        return getInboxListFieldBuilder().addBuilder(
            com.proto.CommonMessage.InboxInfo.getDefaultInstance());
      }
      /**
       * <pre>
       *邮件列表
       * </pre>
       *
       * <code>repeated .ProtoMessage.InboxInfo inboxList = 3;</code>
       */
      public com.proto.CommonMessage.InboxInfo.Builder addInboxListBuilder(
          int index) {
        return getInboxListFieldBuilder().addBuilder(
            index, com.proto.CommonMessage.InboxInfo.getDefaultInstance());
      }
      /**
       * <pre>
       *邮件列表
       * </pre>
       *
       * <code>repeated .ProtoMessage.InboxInfo inboxList = 3;</code>
       */
      public java.util.List<com.proto.CommonMessage.InboxInfo.Builder> 
           getInboxListBuilderList() {
        return getInboxListFieldBuilder().getBuilderList();
      }
      private com.google.protobuf.RepeatedFieldBuilderV3<
          com.proto.CommonMessage.InboxInfo, com.proto.CommonMessage.InboxInfo.Builder, com.proto.CommonMessage.InboxInfoOrBuilder> 
          getInboxListFieldBuilder() {
        if (inboxListBuilder_ == null) {
          inboxListBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
              com.proto.CommonMessage.InboxInfo, com.proto.CommonMessage.InboxInfo.Builder, com.proto.CommonMessage.InboxInfoOrBuilder>(
                  inboxList_,
                  ((bitField0_ & 0x00000001) != 0),
                  getParentForChildren(),
                  isClean());
          inboxList_ = null;
        }
        return inboxListBuilder_;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:ProtoMessage.ResInboxDataMessage)
    }

    // @@protoc_insertion_point(class_scope:ProtoMessage.ResInboxDataMessage)
    private static final com.proto.InboxMessage.ResInboxDataMessage DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.proto.InboxMessage.ResInboxDataMessage();
    }

    public static com.proto.InboxMessage.ResInboxDataMessage getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<ResInboxDataMessage>
        PARSER = new com.google.protobuf.AbstractParser<ResInboxDataMessage>() {
      @java.lang.Override
      public ResInboxDataMessage parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new ResInboxDataMessage(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<ResInboxDataMessage> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<ResInboxDataMessage> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.proto.InboxMessage.ResInboxDataMessage getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface ReqReadInBoxMessageOrBuilder extends
      // @@protoc_insertion_point(interface_extends:ProtoMessage.ReqReadInBoxMessage)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>int32 msgID = 1;</code>
     * @return The msgID.
     */
    int getMsgID();

    /**
     * <pre>
     *邮件id
     * </pre>
     *
     * <code>string inboxId = 2;</code>
     * @return The inboxId.
     */
    java.lang.String getInboxId();
    /**
     * <pre>
     *邮件id
     * </pre>
     *
     * <code>string inboxId = 2;</code>
     * @return The bytes for inboxId.
     */
    com.google.protobuf.ByteString
        getInboxIdBytes();
  }
  /**
   * <pre>
   *请求查看邮件
   * </pre>
   *
   * Protobuf type {@code ProtoMessage.ReqReadInBoxMessage}
   */
  public static final class ReqReadInBoxMessage extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:ProtoMessage.ReqReadInBoxMessage)
      ReqReadInBoxMessageOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use ReqReadInBoxMessage.newBuilder() to construct.
    private ReqReadInBoxMessage(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private ReqReadInBoxMessage() {
      inboxId_ = "";
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new ReqReadInBoxMessage();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private ReqReadInBoxMessage(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {

              msgID_ = input.readInt32();
              break;
            }
            case 18: {
              java.lang.String s = input.readStringRequireUtf8();

              inboxId_ = s;
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.proto.InboxMessage.internal_static_ProtoMessage_ReqReadInBoxMessage_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.proto.InboxMessage.internal_static_ProtoMessage_ReqReadInBoxMessage_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.proto.InboxMessage.ReqReadInBoxMessage.class, com.proto.InboxMessage.ReqReadInBoxMessage.Builder.class);
    }

    public static final int MSGID_FIELD_NUMBER = 1;
    private int msgID_;
    /**
     * <code>int32 msgID = 1;</code>
     * @return The msgID.
     */
    @java.lang.Override
    public int getMsgID() {
      return msgID_;
    }

    public static final int INBOXID_FIELD_NUMBER = 2;
    private volatile java.lang.Object inboxId_;
    /**
     * <pre>
     *邮件id
     * </pre>
     *
     * <code>string inboxId = 2;</code>
     * @return The inboxId.
     */
    @java.lang.Override
    public java.lang.String getInboxId() {
      java.lang.Object ref = inboxId_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        inboxId_ = s;
        return s;
      }
    }
    /**
     * <pre>
     *邮件id
     * </pre>
     *
     * <code>string inboxId = 2;</code>
     * @return The bytes for inboxId.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getInboxIdBytes() {
      java.lang.Object ref = inboxId_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        inboxId_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (msgID_ != 0) {
        output.writeInt32(1, msgID_);
      }
      if (!getInboxIdBytes().isEmpty()) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 2, inboxId_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (msgID_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, msgID_);
      }
      if (!getInboxIdBytes().isEmpty()) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(2, inboxId_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.proto.InboxMessage.ReqReadInBoxMessage)) {
        return super.equals(obj);
      }
      com.proto.InboxMessage.ReqReadInBoxMessage other = (com.proto.InboxMessage.ReqReadInBoxMessage) obj;

      if (getMsgID()
          != other.getMsgID()) return false;
      if (!getInboxId()
          .equals(other.getInboxId())) return false;
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + MSGID_FIELD_NUMBER;
      hash = (53 * hash) + getMsgID();
      hash = (37 * hash) + INBOXID_FIELD_NUMBER;
      hash = (53 * hash) + getInboxId().hashCode();
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.proto.InboxMessage.ReqReadInBoxMessage parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.proto.InboxMessage.ReqReadInBoxMessage parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.proto.InboxMessage.ReqReadInBoxMessage parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.proto.InboxMessage.ReqReadInBoxMessage parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.proto.InboxMessage.ReqReadInBoxMessage parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.proto.InboxMessage.ReqReadInBoxMessage parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.proto.InboxMessage.ReqReadInBoxMessage parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.proto.InboxMessage.ReqReadInBoxMessage parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.proto.InboxMessage.ReqReadInBoxMessage parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.proto.InboxMessage.ReqReadInBoxMessage parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.proto.InboxMessage.ReqReadInBoxMessage parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.proto.InboxMessage.ReqReadInBoxMessage parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.proto.InboxMessage.ReqReadInBoxMessage prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     *请求查看邮件
     * </pre>
     *
     * Protobuf type {@code ProtoMessage.ReqReadInBoxMessage}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:ProtoMessage.ReqReadInBoxMessage)
        com.proto.InboxMessage.ReqReadInBoxMessageOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.proto.InboxMessage.internal_static_ProtoMessage_ReqReadInBoxMessage_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.proto.InboxMessage.internal_static_ProtoMessage_ReqReadInBoxMessage_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.proto.InboxMessage.ReqReadInBoxMessage.class, com.proto.InboxMessage.ReqReadInBoxMessage.Builder.class);
      }

      // Construct using com.proto.InboxMessage.ReqReadInBoxMessage.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        msgID_ = 0;

        inboxId_ = "";

        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.proto.InboxMessage.internal_static_ProtoMessage_ReqReadInBoxMessage_descriptor;
      }

      @java.lang.Override
      public com.proto.InboxMessage.ReqReadInBoxMessage getDefaultInstanceForType() {
        return com.proto.InboxMessage.ReqReadInBoxMessage.getDefaultInstance();
      }

      @java.lang.Override
      public com.proto.InboxMessage.ReqReadInBoxMessage build() {
        com.proto.InboxMessage.ReqReadInBoxMessage result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.proto.InboxMessage.ReqReadInBoxMessage buildPartial() {
        com.proto.InboxMessage.ReqReadInBoxMessage result = new com.proto.InboxMessage.ReqReadInBoxMessage(this);
        result.msgID_ = msgID_;
        result.inboxId_ = inboxId_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.proto.InboxMessage.ReqReadInBoxMessage) {
          return mergeFrom((com.proto.InboxMessage.ReqReadInBoxMessage)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.proto.InboxMessage.ReqReadInBoxMessage other) {
        if (other == com.proto.InboxMessage.ReqReadInBoxMessage.getDefaultInstance()) return this;
        if (other.getMsgID() != 0) {
          setMsgID(other.getMsgID());
        }
        if (!other.getInboxId().isEmpty()) {
          inboxId_ = other.inboxId_;
          onChanged();
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.proto.InboxMessage.ReqReadInBoxMessage parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.proto.InboxMessage.ReqReadInBoxMessage) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }

      private int msgID_ ;
      /**
       * <code>int32 msgID = 1;</code>
       * @return The msgID.
       */
      @java.lang.Override
      public int getMsgID() {
        return msgID_;
      }
      /**
       * <code>int32 msgID = 1;</code>
       * @param value The msgID to set.
       * @return This builder for chaining.
       */
      public Builder setMsgID(int value) {
        
        msgID_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>int32 msgID = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearMsgID() {
        
        msgID_ = 0;
        onChanged();
        return this;
      }

      private java.lang.Object inboxId_ = "";
      /**
       * <pre>
       *邮件id
       * </pre>
       *
       * <code>string inboxId = 2;</code>
       * @return The inboxId.
       */
      public java.lang.String getInboxId() {
        java.lang.Object ref = inboxId_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          inboxId_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       *邮件id
       * </pre>
       *
       * <code>string inboxId = 2;</code>
       * @return The bytes for inboxId.
       */
      public com.google.protobuf.ByteString
          getInboxIdBytes() {
        java.lang.Object ref = inboxId_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          inboxId_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       *邮件id
       * </pre>
       *
       * <code>string inboxId = 2;</code>
       * @param value The inboxId to set.
       * @return This builder for chaining.
       */
      public Builder setInboxId(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  
        inboxId_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *邮件id
       * </pre>
       *
       * <code>string inboxId = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearInboxId() {
        
        inboxId_ = getDefaultInstance().getInboxId();
        onChanged();
        return this;
      }
      /**
       * <pre>
       *邮件id
       * </pre>
       *
       * <code>string inboxId = 2;</code>
       * @param value The bytes for inboxId to set.
       * @return This builder for chaining.
       */
      public Builder setInboxIdBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
        
        inboxId_ = value;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:ProtoMessage.ReqReadInBoxMessage)
    }

    // @@protoc_insertion_point(class_scope:ProtoMessage.ReqReadInBoxMessage)
    private static final com.proto.InboxMessage.ReqReadInBoxMessage DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.proto.InboxMessage.ReqReadInBoxMessage();
    }

    public static com.proto.InboxMessage.ReqReadInBoxMessage getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<ReqReadInBoxMessage>
        PARSER = new com.google.protobuf.AbstractParser<ReqReadInBoxMessage>() {
      @java.lang.Override
      public ReqReadInBoxMessage parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new ReqReadInBoxMessage(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<ReqReadInBoxMessage> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<ReqReadInBoxMessage> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.proto.InboxMessage.ReqReadInBoxMessage getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface ResReadInBoxMessageOrBuilder extends
      // @@protoc_insertion_point(interface_extends:ProtoMessage.ResReadInBoxMessage)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>int32 msgID = 1;</code>
     * @return The msgID.
     */
    int getMsgID();

    /**
     * <pre>
     *错误码
     * </pre>
     *
     * <code>int32 error = 2;</code>
     * @return The error.
     */
    int getError();

    /**
     * <pre>
     *邮件id
     * </pre>
     *
     * <code>string inboxId = 3;</code>
     * @return The inboxId.
     */
    java.lang.String getInboxId();
    /**
     * <pre>
     *邮件id
     * </pre>
     *
     * <code>string inboxId = 3;</code>
     * @return The bytes for inboxId.
     */
    com.google.protobuf.ByteString
        getInboxIdBytes();
  }
  /**
   * <pre>
   *返回查看邮件
   * </pre>
   *
   * Protobuf type {@code ProtoMessage.ResReadInBoxMessage}
   */
  public static final class ResReadInBoxMessage extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:ProtoMessage.ResReadInBoxMessage)
      ResReadInBoxMessageOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use ResReadInBoxMessage.newBuilder() to construct.
    private ResReadInBoxMessage(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private ResReadInBoxMessage() {
      inboxId_ = "";
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new ResReadInBoxMessage();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private ResReadInBoxMessage(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {

              msgID_ = input.readInt32();
              break;
            }
            case 16: {

              error_ = input.readInt32();
              break;
            }
            case 26: {
              java.lang.String s = input.readStringRequireUtf8();

              inboxId_ = s;
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.proto.InboxMessage.internal_static_ProtoMessage_ResReadInBoxMessage_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.proto.InboxMessage.internal_static_ProtoMessage_ResReadInBoxMessage_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.proto.InboxMessage.ResReadInBoxMessage.class, com.proto.InboxMessage.ResReadInBoxMessage.Builder.class);
    }

    public static final int MSGID_FIELD_NUMBER = 1;
    private int msgID_;
    /**
     * <code>int32 msgID = 1;</code>
     * @return The msgID.
     */
    @java.lang.Override
    public int getMsgID() {
      return msgID_;
    }

    public static final int ERROR_FIELD_NUMBER = 2;
    private int error_;
    /**
     * <pre>
     *错误码
     * </pre>
     *
     * <code>int32 error = 2;</code>
     * @return The error.
     */
    @java.lang.Override
    public int getError() {
      return error_;
    }

    public static final int INBOXID_FIELD_NUMBER = 3;
    private volatile java.lang.Object inboxId_;
    /**
     * <pre>
     *邮件id
     * </pre>
     *
     * <code>string inboxId = 3;</code>
     * @return The inboxId.
     */
    @java.lang.Override
    public java.lang.String getInboxId() {
      java.lang.Object ref = inboxId_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        inboxId_ = s;
        return s;
      }
    }
    /**
     * <pre>
     *邮件id
     * </pre>
     *
     * <code>string inboxId = 3;</code>
     * @return The bytes for inboxId.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getInboxIdBytes() {
      java.lang.Object ref = inboxId_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        inboxId_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (msgID_ != 0) {
        output.writeInt32(1, msgID_);
      }
      if (error_ != 0) {
        output.writeInt32(2, error_);
      }
      if (!getInboxIdBytes().isEmpty()) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 3, inboxId_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (msgID_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, msgID_);
      }
      if (error_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(2, error_);
      }
      if (!getInboxIdBytes().isEmpty()) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(3, inboxId_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.proto.InboxMessage.ResReadInBoxMessage)) {
        return super.equals(obj);
      }
      com.proto.InboxMessage.ResReadInBoxMessage other = (com.proto.InboxMessage.ResReadInBoxMessage) obj;

      if (getMsgID()
          != other.getMsgID()) return false;
      if (getError()
          != other.getError()) return false;
      if (!getInboxId()
          .equals(other.getInboxId())) return false;
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + MSGID_FIELD_NUMBER;
      hash = (53 * hash) + getMsgID();
      hash = (37 * hash) + ERROR_FIELD_NUMBER;
      hash = (53 * hash) + getError();
      hash = (37 * hash) + INBOXID_FIELD_NUMBER;
      hash = (53 * hash) + getInboxId().hashCode();
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.proto.InboxMessage.ResReadInBoxMessage parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.proto.InboxMessage.ResReadInBoxMessage parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.proto.InboxMessage.ResReadInBoxMessage parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.proto.InboxMessage.ResReadInBoxMessage parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.proto.InboxMessage.ResReadInBoxMessage parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.proto.InboxMessage.ResReadInBoxMessage parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.proto.InboxMessage.ResReadInBoxMessage parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.proto.InboxMessage.ResReadInBoxMessage parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.proto.InboxMessage.ResReadInBoxMessage parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.proto.InboxMessage.ResReadInBoxMessage parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.proto.InboxMessage.ResReadInBoxMessage parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.proto.InboxMessage.ResReadInBoxMessage parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.proto.InboxMessage.ResReadInBoxMessage prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     *返回查看邮件
     * </pre>
     *
     * Protobuf type {@code ProtoMessage.ResReadInBoxMessage}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:ProtoMessage.ResReadInBoxMessage)
        com.proto.InboxMessage.ResReadInBoxMessageOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.proto.InboxMessage.internal_static_ProtoMessage_ResReadInBoxMessage_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.proto.InboxMessage.internal_static_ProtoMessage_ResReadInBoxMessage_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.proto.InboxMessage.ResReadInBoxMessage.class, com.proto.InboxMessage.ResReadInBoxMessage.Builder.class);
      }

      // Construct using com.proto.InboxMessage.ResReadInBoxMessage.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        msgID_ = 0;

        error_ = 0;

        inboxId_ = "";

        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.proto.InboxMessage.internal_static_ProtoMessage_ResReadInBoxMessage_descriptor;
      }

      @java.lang.Override
      public com.proto.InboxMessage.ResReadInBoxMessage getDefaultInstanceForType() {
        return com.proto.InboxMessage.ResReadInBoxMessage.getDefaultInstance();
      }

      @java.lang.Override
      public com.proto.InboxMessage.ResReadInBoxMessage build() {
        com.proto.InboxMessage.ResReadInBoxMessage result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.proto.InboxMessage.ResReadInBoxMessage buildPartial() {
        com.proto.InboxMessage.ResReadInBoxMessage result = new com.proto.InboxMessage.ResReadInBoxMessage(this);
        result.msgID_ = msgID_;
        result.error_ = error_;
        result.inboxId_ = inboxId_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.proto.InboxMessage.ResReadInBoxMessage) {
          return mergeFrom((com.proto.InboxMessage.ResReadInBoxMessage)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.proto.InboxMessage.ResReadInBoxMessage other) {
        if (other == com.proto.InboxMessage.ResReadInBoxMessage.getDefaultInstance()) return this;
        if (other.getMsgID() != 0) {
          setMsgID(other.getMsgID());
        }
        if (other.getError() != 0) {
          setError(other.getError());
        }
        if (!other.getInboxId().isEmpty()) {
          inboxId_ = other.inboxId_;
          onChanged();
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.proto.InboxMessage.ResReadInBoxMessage parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.proto.InboxMessage.ResReadInBoxMessage) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }

      private int msgID_ ;
      /**
       * <code>int32 msgID = 1;</code>
       * @return The msgID.
       */
      @java.lang.Override
      public int getMsgID() {
        return msgID_;
      }
      /**
       * <code>int32 msgID = 1;</code>
       * @param value The msgID to set.
       * @return This builder for chaining.
       */
      public Builder setMsgID(int value) {
        
        msgID_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>int32 msgID = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearMsgID() {
        
        msgID_ = 0;
        onChanged();
        return this;
      }

      private int error_ ;
      /**
       * <pre>
       *错误码
       * </pre>
       *
       * <code>int32 error = 2;</code>
       * @return The error.
       */
      @java.lang.Override
      public int getError() {
        return error_;
      }
      /**
       * <pre>
       *错误码
       * </pre>
       *
       * <code>int32 error = 2;</code>
       * @param value The error to set.
       * @return This builder for chaining.
       */
      public Builder setError(int value) {
        
        error_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *错误码
       * </pre>
       *
       * <code>int32 error = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearError() {
        
        error_ = 0;
        onChanged();
        return this;
      }

      private java.lang.Object inboxId_ = "";
      /**
       * <pre>
       *邮件id
       * </pre>
       *
       * <code>string inboxId = 3;</code>
       * @return The inboxId.
       */
      public java.lang.String getInboxId() {
        java.lang.Object ref = inboxId_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          inboxId_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       *邮件id
       * </pre>
       *
       * <code>string inboxId = 3;</code>
       * @return The bytes for inboxId.
       */
      public com.google.protobuf.ByteString
          getInboxIdBytes() {
        java.lang.Object ref = inboxId_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          inboxId_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       *邮件id
       * </pre>
       *
       * <code>string inboxId = 3;</code>
       * @param value The inboxId to set.
       * @return This builder for chaining.
       */
      public Builder setInboxId(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  
        inboxId_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *邮件id
       * </pre>
       *
       * <code>string inboxId = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearInboxId() {
        
        inboxId_ = getDefaultInstance().getInboxId();
        onChanged();
        return this;
      }
      /**
       * <pre>
       *邮件id
       * </pre>
       *
       * <code>string inboxId = 3;</code>
       * @param value The bytes for inboxId to set.
       * @return This builder for chaining.
       */
      public Builder setInboxIdBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
        
        inboxId_ = value;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:ProtoMessage.ResReadInBoxMessage)
    }

    // @@protoc_insertion_point(class_scope:ProtoMessage.ResReadInBoxMessage)
    private static final com.proto.InboxMessage.ResReadInBoxMessage DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.proto.InboxMessage.ResReadInBoxMessage();
    }

    public static com.proto.InboxMessage.ResReadInBoxMessage getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<ResReadInBoxMessage>
        PARSER = new com.google.protobuf.AbstractParser<ResReadInBoxMessage>() {
      @java.lang.Override
      public ResReadInBoxMessage parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new ResReadInBoxMessage(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<ResReadInBoxMessage> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<ResReadInBoxMessage> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.proto.InboxMessage.ResReadInBoxMessage getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface ReqDeleteInboxMessageOrBuilder extends
      // @@protoc_insertion_point(interface_extends:ProtoMessage.ReqDeleteInboxMessage)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>int32 msgID = 1;</code>
     * @return The msgID.
     */
    int getMsgID();

    /**
     * <pre>
     *邮件id
     * </pre>
     *
     * <code>string inboxId = 2;</code>
     * @return The inboxId.
     */
    java.lang.String getInboxId();
    /**
     * <pre>
     *邮件id
     * </pre>
     *
     * <code>string inboxId = 2;</code>
     * @return The bytes for inboxId.
     */
    com.google.protobuf.ByteString
        getInboxIdBytes();
  }
  /**
   * <pre>
   *请求删除邮件
   * </pre>
   *
   * Protobuf type {@code ProtoMessage.ReqDeleteInboxMessage}
   */
  public static final class ReqDeleteInboxMessage extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:ProtoMessage.ReqDeleteInboxMessage)
      ReqDeleteInboxMessageOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use ReqDeleteInboxMessage.newBuilder() to construct.
    private ReqDeleteInboxMessage(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private ReqDeleteInboxMessage() {
      inboxId_ = "";
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new ReqDeleteInboxMessage();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private ReqDeleteInboxMessage(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {

              msgID_ = input.readInt32();
              break;
            }
            case 18: {
              java.lang.String s = input.readStringRequireUtf8();

              inboxId_ = s;
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.proto.InboxMessage.internal_static_ProtoMessage_ReqDeleteInboxMessage_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.proto.InboxMessage.internal_static_ProtoMessage_ReqDeleteInboxMessage_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.proto.InboxMessage.ReqDeleteInboxMessage.class, com.proto.InboxMessage.ReqDeleteInboxMessage.Builder.class);
    }

    public static final int MSGID_FIELD_NUMBER = 1;
    private int msgID_;
    /**
     * <code>int32 msgID = 1;</code>
     * @return The msgID.
     */
    @java.lang.Override
    public int getMsgID() {
      return msgID_;
    }

    public static final int INBOXID_FIELD_NUMBER = 2;
    private volatile java.lang.Object inboxId_;
    /**
     * <pre>
     *邮件id
     * </pre>
     *
     * <code>string inboxId = 2;</code>
     * @return The inboxId.
     */
    @java.lang.Override
    public java.lang.String getInboxId() {
      java.lang.Object ref = inboxId_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        inboxId_ = s;
        return s;
      }
    }
    /**
     * <pre>
     *邮件id
     * </pre>
     *
     * <code>string inboxId = 2;</code>
     * @return The bytes for inboxId.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getInboxIdBytes() {
      java.lang.Object ref = inboxId_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        inboxId_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (msgID_ != 0) {
        output.writeInt32(1, msgID_);
      }
      if (!getInboxIdBytes().isEmpty()) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 2, inboxId_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (msgID_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, msgID_);
      }
      if (!getInboxIdBytes().isEmpty()) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(2, inboxId_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.proto.InboxMessage.ReqDeleteInboxMessage)) {
        return super.equals(obj);
      }
      com.proto.InboxMessage.ReqDeleteInboxMessage other = (com.proto.InboxMessage.ReqDeleteInboxMessage) obj;

      if (getMsgID()
          != other.getMsgID()) return false;
      if (!getInboxId()
          .equals(other.getInboxId())) return false;
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + MSGID_FIELD_NUMBER;
      hash = (53 * hash) + getMsgID();
      hash = (37 * hash) + INBOXID_FIELD_NUMBER;
      hash = (53 * hash) + getInboxId().hashCode();
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.proto.InboxMessage.ReqDeleteInboxMessage parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.proto.InboxMessage.ReqDeleteInboxMessage parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.proto.InboxMessage.ReqDeleteInboxMessage parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.proto.InboxMessage.ReqDeleteInboxMessage parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.proto.InboxMessage.ReqDeleteInboxMessage parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.proto.InboxMessage.ReqDeleteInboxMessage parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.proto.InboxMessage.ReqDeleteInboxMessage parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.proto.InboxMessage.ReqDeleteInboxMessage parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.proto.InboxMessage.ReqDeleteInboxMessage parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.proto.InboxMessage.ReqDeleteInboxMessage parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.proto.InboxMessage.ReqDeleteInboxMessage parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.proto.InboxMessage.ReqDeleteInboxMessage parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.proto.InboxMessage.ReqDeleteInboxMessage prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     *请求删除邮件
     * </pre>
     *
     * Protobuf type {@code ProtoMessage.ReqDeleteInboxMessage}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:ProtoMessage.ReqDeleteInboxMessage)
        com.proto.InboxMessage.ReqDeleteInboxMessageOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.proto.InboxMessage.internal_static_ProtoMessage_ReqDeleteInboxMessage_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.proto.InboxMessage.internal_static_ProtoMessage_ReqDeleteInboxMessage_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.proto.InboxMessage.ReqDeleteInboxMessage.class, com.proto.InboxMessage.ReqDeleteInboxMessage.Builder.class);
      }

      // Construct using com.proto.InboxMessage.ReqDeleteInboxMessage.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        msgID_ = 0;

        inboxId_ = "";

        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.proto.InboxMessage.internal_static_ProtoMessage_ReqDeleteInboxMessage_descriptor;
      }

      @java.lang.Override
      public com.proto.InboxMessage.ReqDeleteInboxMessage getDefaultInstanceForType() {
        return com.proto.InboxMessage.ReqDeleteInboxMessage.getDefaultInstance();
      }

      @java.lang.Override
      public com.proto.InboxMessage.ReqDeleteInboxMessage build() {
        com.proto.InboxMessage.ReqDeleteInboxMessage result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.proto.InboxMessage.ReqDeleteInboxMessage buildPartial() {
        com.proto.InboxMessage.ReqDeleteInboxMessage result = new com.proto.InboxMessage.ReqDeleteInboxMessage(this);
        result.msgID_ = msgID_;
        result.inboxId_ = inboxId_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.proto.InboxMessage.ReqDeleteInboxMessage) {
          return mergeFrom((com.proto.InboxMessage.ReqDeleteInboxMessage)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.proto.InboxMessage.ReqDeleteInboxMessage other) {
        if (other == com.proto.InboxMessage.ReqDeleteInboxMessage.getDefaultInstance()) return this;
        if (other.getMsgID() != 0) {
          setMsgID(other.getMsgID());
        }
        if (!other.getInboxId().isEmpty()) {
          inboxId_ = other.inboxId_;
          onChanged();
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.proto.InboxMessage.ReqDeleteInboxMessage parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.proto.InboxMessage.ReqDeleteInboxMessage) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }

      private int msgID_ ;
      /**
       * <code>int32 msgID = 1;</code>
       * @return The msgID.
       */
      @java.lang.Override
      public int getMsgID() {
        return msgID_;
      }
      /**
       * <code>int32 msgID = 1;</code>
       * @param value The msgID to set.
       * @return This builder for chaining.
       */
      public Builder setMsgID(int value) {
        
        msgID_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>int32 msgID = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearMsgID() {
        
        msgID_ = 0;
        onChanged();
        return this;
      }

      private java.lang.Object inboxId_ = "";
      /**
       * <pre>
       *邮件id
       * </pre>
       *
       * <code>string inboxId = 2;</code>
       * @return The inboxId.
       */
      public java.lang.String getInboxId() {
        java.lang.Object ref = inboxId_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          inboxId_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       *邮件id
       * </pre>
       *
       * <code>string inboxId = 2;</code>
       * @return The bytes for inboxId.
       */
      public com.google.protobuf.ByteString
          getInboxIdBytes() {
        java.lang.Object ref = inboxId_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          inboxId_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       *邮件id
       * </pre>
       *
       * <code>string inboxId = 2;</code>
       * @param value The inboxId to set.
       * @return This builder for chaining.
       */
      public Builder setInboxId(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  
        inboxId_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *邮件id
       * </pre>
       *
       * <code>string inboxId = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearInboxId() {
        
        inboxId_ = getDefaultInstance().getInboxId();
        onChanged();
        return this;
      }
      /**
       * <pre>
       *邮件id
       * </pre>
       *
       * <code>string inboxId = 2;</code>
       * @param value The bytes for inboxId to set.
       * @return This builder for chaining.
       */
      public Builder setInboxIdBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
        
        inboxId_ = value;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:ProtoMessage.ReqDeleteInboxMessage)
    }

    // @@protoc_insertion_point(class_scope:ProtoMessage.ReqDeleteInboxMessage)
    private static final com.proto.InboxMessage.ReqDeleteInboxMessage DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.proto.InboxMessage.ReqDeleteInboxMessage();
    }

    public static com.proto.InboxMessage.ReqDeleteInboxMessage getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<ReqDeleteInboxMessage>
        PARSER = new com.google.protobuf.AbstractParser<ReqDeleteInboxMessage>() {
      @java.lang.Override
      public ReqDeleteInboxMessage parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new ReqDeleteInboxMessage(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<ReqDeleteInboxMessage> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<ReqDeleteInboxMessage> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.proto.InboxMessage.ReqDeleteInboxMessage getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface ResDeleteInboxMessageOrBuilder extends
      // @@protoc_insertion_point(interface_extends:ProtoMessage.ResDeleteInboxMessage)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>int32 msgID = 1;</code>
     * @return The msgID.
     */
    int getMsgID();

    /**
     * <pre>
     *错误码
     * </pre>
     *
     * <code>int32 error = 2;</code>
     * @return The error.
     */
    int getError();

    /**
     * <pre>
     *邮件唯一id
     * </pre>
     *
     * <code>string inboxId = 3;</code>
     * @return The inboxId.
     */
    java.lang.String getInboxId();
    /**
     * <pre>
     *邮件唯一id
     * </pre>
     *
     * <code>string inboxId = 3;</code>
     * @return The bytes for inboxId.
     */
    com.google.protobuf.ByteString
        getInboxIdBytes();
  }
  /**
   * <pre>
   *返回删除邮件
   * </pre>
   *
   * Protobuf type {@code ProtoMessage.ResDeleteInboxMessage}
   */
  public static final class ResDeleteInboxMessage extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:ProtoMessage.ResDeleteInboxMessage)
      ResDeleteInboxMessageOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use ResDeleteInboxMessage.newBuilder() to construct.
    private ResDeleteInboxMessage(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private ResDeleteInboxMessage() {
      inboxId_ = "";
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new ResDeleteInboxMessage();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private ResDeleteInboxMessage(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {

              msgID_ = input.readInt32();
              break;
            }
            case 16: {

              error_ = input.readInt32();
              break;
            }
            case 26: {
              java.lang.String s = input.readStringRequireUtf8();

              inboxId_ = s;
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.proto.InboxMessage.internal_static_ProtoMessage_ResDeleteInboxMessage_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.proto.InboxMessage.internal_static_ProtoMessage_ResDeleteInboxMessage_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.proto.InboxMessage.ResDeleteInboxMessage.class, com.proto.InboxMessage.ResDeleteInboxMessage.Builder.class);
    }

    public static final int MSGID_FIELD_NUMBER = 1;
    private int msgID_;
    /**
     * <code>int32 msgID = 1;</code>
     * @return The msgID.
     */
    @java.lang.Override
    public int getMsgID() {
      return msgID_;
    }

    public static final int ERROR_FIELD_NUMBER = 2;
    private int error_;
    /**
     * <pre>
     *错误码
     * </pre>
     *
     * <code>int32 error = 2;</code>
     * @return The error.
     */
    @java.lang.Override
    public int getError() {
      return error_;
    }

    public static final int INBOXID_FIELD_NUMBER = 3;
    private volatile java.lang.Object inboxId_;
    /**
     * <pre>
     *邮件唯一id
     * </pre>
     *
     * <code>string inboxId = 3;</code>
     * @return The inboxId.
     */
    @java.lang.Override
    public java.lang.String getInboxId() {
      java.lang.Object ref = inboxId_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        inboxId_ = s;
        return s;
      }
    }
    /**
     * <pre>
     *邮件唯一id
     * </pre>
     *
     * <code>string inboxId = 3;</code>
     * @return The bytes for inboxId.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getInboxIdBytes() {
      java.lang.Object ref = inboxId_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        inboxId_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (msgID_ != 0) {
        output.writeInt32(1, msgID_);
      }
      if (error_ != 0) {
        output.writeInt32(2, error_);
      }
      if (!getInboxIdBytes().isEmpty()) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 3, inboxId_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (msgID_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, msgID_);
      }
      if (error_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(2, error_);
      }
      if (!getInboxIdBytes().isEmpty()) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(3, inboxId_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.proto.InboxMessage.ResDeleteInboxMessage)) {
        return super.equals(obj);
      }
      com.proto.InboxMessage.ResDeleteInboxMessage other = (com.proto.InboxMessage.ResDeleteInboxMessage) obj;

      if (getMsgID()
          != other.getMsgID()) return false;
      if (getError()
          != other.getError()) return false;
      if (!getInboxId()
          .equals(other.getInboxId())) return false;
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + MSGID_FIELD_NUMBER;
      hash = (53 * hash) + getMsgID();
      hash = (37 * hash) + ERROR_FIELD_NUMBER;
      hash = (53 * hash) + getError();
      hash = (37 * hash) + INBOXID_FIELD_NUMBER;
      hash = (53 * hash) + getInboxId().hashCode();
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.proto.InboxMessage.ResDeleteInboxMessage parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.proto.InboxMessage.ResDeleteInboxMessage parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.proto.InboxMessage.ResDeleteInboxMessage parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.proto.InboxMessage.ResDeleteInboxMessage parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.proto.InboxMessage.ResDeleteInboxMessage parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.proto.InboxMessage.ResDeleteInboxMessage parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.proto.InboxMessage.ResDeleteInboxMessage parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.proto.InboxMessage.ResDeleteInboxMessage parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.proto.InboxMessage.ResDeleteInboxMessage parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.proto.InboxMessage.ResDeleteInboxMessage parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.proto.InboxMessage.ResDeleteInboxMessage parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.proto.InboxMessage.ResDeleteInboxMessage parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.proto.InboxMessage.ResDeleteInboxMessage prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     *返回删除邮件
     * </pre>
     *
     * Protobuf type {@code ProtoMessage.ResDeleteInboxMessage}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:ProtoMessage.ResDeleteInboxMessage)
        com.proto.InboxMessage.ResDeleteInboxMessageOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.proto.InboxMessage.internal_static_ProtoMessage_ResDeleteInboxMessage_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.proto.InboxMessage.internal_static_ProtoMessage_ResDeleteInboxMessage_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.proto.InboxMessage.ResDeleteInboxMessage.class, com.proto.InboxMessage.ResDeleteInboxMessage.Builder.class);
      }

      // Construct using com.proto.InboxMessage.ResDeleteInboxMessage.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        msgID_ = 0;

        error_ = 0;

        inboxId_ = "";

        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.proto.InboxMessage.internal_static_ProtoMessage_ResDeleteInboxMessage_descriptor;
      }

      @java.lang.Override
      public com.proto.InboxMessage.ResDeleteInboxMessage getDefaultInstanceForType() {
        return com.proto.InboxMessage.ResDeleteInboxMessage.getDefaultInstance();
      }

      @java.lang.Override
      public com.proto.InboxMessage.ResDeleteInboxMessage build() {
        com.proto.InboxMessage.ResDeleteInboxMessage result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.proto.InboxMessage.ResDeleteInboxMessage buildPartial() {
        com.proto.InboxMessage.ResDeleteInboxMessage result = new com.proto.InboxMessage.ResDeleteInboxMessage(this);
        result.msgID_ = msgID_;
        result.error_ = error_;
        result.inboxId_ = inboxId_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.proto.InboxMessage.ResDeleteInboxMessage) {
          return mergeFrom((com.proto.InboxMessage.ResDeleteInboxMessage)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.proto.InboxMessage.ResDeleteInboxMessage other) {
        if (other == com.proto.InboxMessage.ResDeleteInboxMessage.getDefaultInstance()) return this;
        if (other.getMsgID() != 0) {
          setMsgID(other.getMsgID());
        }
        if (other.getError() != 0) {
          setError(other.getError());
        }
        if (!other.getInboxId().isEmpty()) {
          inboxId_ = other.inboxId_;
          onChanged();
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.proto.InboxMessage.ResDeleteInboxMessage parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.proto.InboxMessage.ResDeleteInboxMessage) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }

      private int msgID_ ;
      /**
       * <code>int32 msgID = 1;</code>
       * @return The msgID.
       */
      @java.lang.Override
      public int getMsgID() {
        return msgID_;
      }
      /**
       * <code>int32 msgID = 1;</code>
       * @param value The msgID to set.
       * @return This builder for chaining.
       */
      public Builder setMsgID(int value) {
        
        msgID_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>int32 msgID = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearMsgID() {
        
        msgID_ = 0;
        onChanged();
        return this;
      }

      private int error_ ;
      /**
       * <pre>
       *错误码
       * </pre>
       *
       * <code>int32 error = 2;</code>
       * @return The error.
       */
      @java.lang.Override
      public int getError() {
        return error_;
      }
      /**
       * <pre>
       *错误码
       * </pre>
       *
       * <code>int32 error = 2;</code>
       * @param value The error to set.
       * @return This builder for chaining.
       */
      public Builder setError(int value) {
        
        error_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *错误码
       * </pre>
       *
       * <code>int32 error = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearError() {
        
        error_ = 0;
        onChanged();
        return this;
      }

      private java.lang.Object inboxId_ = "";
      /**
       * <pre>
       *邮件唯一id
       * </pre>
       *
       * <code>string inboxId = 3;</code>
       * @return The inboxId.
       */
      public java.lang.String getInboxId() {
        java.lang.Object ref = inboxId_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          inboxId_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       *邮件唯一id
       * </pre>
       *
       * <code>string inboxId = 3;</code>
       * @return The bytes for inboxId.
       */
      public com.google.protobuf.ByteString
          getInboxIdBytes() {
        java.lang.Object ref = inboxId_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          inboxId_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       *邮件唯一id
       * </pre>
       *
       * <code>string inboxId = 3;</code>
       * @param value The inboxId to set.
       * @return This builder for chaining.
       */
      public Builder setInboxId(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  
        inboxId_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *邮件唯一id
       * </pre>
       *
       * <code>string inboxId = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearInboxId() {
        
        inboxId_ = getDefaultInstance().getInboxId();
        onChanged();
        return this;
      }
      /**
       * <pre>
       *邮件唯一id
       * </pre>
       *
       * <code>string inboxId = 3;</code>
       * @param value The bytes for inboxId to set.
       * @return This builder for chaining.
       */
      public Builder setInboxIdBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
        
        inboxId_ = value;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:ProtoMessage.ResDeleteInboxMessage)
    }

    // @@protoc_insertion_point(class_scope:ProtoMessage.ResDeleteInboxMessage)
    private static final com.proto.InboxMessage.ResDeleteInboxMessage DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.proto.InboxMessage.ResDeleteInboxMessage();
    }

    public static com.proto.InboxMessage.ResDeleteInboxMessage getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<ResDeleteInboxMessage>
        PARSER = new com.google.protobuf.AbstractParser<ResDeleteInboxMessage>() {
      @java.lang.Override
      public ResDeleteInboxMessage parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new ResDeleteInboxMessage(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<ResDeleteInboxMessage> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<ResDeleteInboxMessage> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.proto.InboxMessage.ResDeleteInboxMessage getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_ProtoMessage_ReqInboxDataMessage_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_ProtoMessage_ReqInboxDataMessage_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_ProtoMessage_ResInboxDataMessage_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_ProtoMessage_ResInboxDataMessage_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_ProtoMessage_ReqReadInBoxMessage_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_ProtoMessage_ReqReadInBoxMessage_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_ProtoMessage_ResReadInBoxMessage_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_ProtoMessage_ResReadInBoxMessage_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_ProtoMessage_ReqDeleteInboxMessage_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_ProtoMessage_ReqDeleteInboxMessage_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_ProtoMessage_ResDeleteInboxMessage_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_ProtoMessage_ResDeleteInboxMessage_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n\022InboxMessage.proto\022\014ProtoMessage\032\023Comm" +
      "onMessage.proto\"$\n\023ReqInboxDataMessage\022\r" +
      "\n\005msgID\030\001 \001(\005\"_\n\023ResInboxDataMessage\022\r\n\005" +
      "msgID\030\001 \001(\005\022\r\n\005error\030\002 \001(\005\022*\n\tinboxList\030" +
      "\003 \003(\0132\027.ProtoMessage.InboxInfo\"5\n\023ReqRea" +
      "dInBoxMessage\022\r\n\005msgID\030\001 \001(\005\022\017\n\007inboxId\030" +
      "\002 \001(\t\"D\n\023ResReadInBoxMessage\022\r\n\005msgID\030\001 " +
      "\001(\005\022\r\n\005error\030\002 \001(\005\022\017\n\007inboxId\030\003 \001(\t\"7\n\025R" +
      "eqDeleteInboxMessage\022\r\n\005msgID\030\001 \001(\005\022\017\n\007i" +
      "nboxId\030\002 \001(\t\"F\n\025ResDeleteInboxMessage\022\r\n" +
      "\005msgID\030\001 \001(\005\022\r\n\005error\030\002 \001(\005\022\017\n\007inboxId\030\003" +
      " \001(\tB\013\n\tcom.protob\006proto3"
    };
    descriptor = com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
          com.proto.CommonMessage.getDescriptor(),
        });
    internal_static_ProtoMessage_ReqInboxDataMessage_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_ProtoMessage_ReqInboxDataMessage_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_ProtoMessage_ReqInboxDataMessage_descriptor,
        new java.lang.String[] { "MsgID", });
    internal_static_ProtoMessage_ResInboxDataMessage_descriptor =
      getDescriptor().getMessageTypes().get(1);
    internal_static_ProtoMessage_ResInboxDataMessage_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_ProtoMessage_ResInboxDataMessage_descriptor,
        new java.lang.String[] { "MsgID", "Error", "InboxList", });
    internal_static_ProtoMessage_ReqReadInBoxMessage_descriptor =
      getDescriptor().getMessageTypes().get(2);
    internal_static_ProtoMessage_ReqReadInBoxMessage_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_ProtoMessage_ReqReadInBoxMessage_descriptor,
        new java.lang.String[] { "MsgID", "InboxId", });
    internal_static_ProtoMessage_ResReadInBoxMessage_descriptor =
      getDescriptor().getMessageTypes().get(3);
    internal_static_ProtoMessage_ResReadInBoxMessage_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_ProtoMessage_ResReadInBoxMessage_descriptor,
        new java.lang.String[] { "MsgID", "Error", "InboxId", });
    internal_static_ProtoMessage_ReqDeleteInboxMessage_descriptor =
      getDescriptor().getMessageTypes().get(4);
    internal_static_ProtoMessage_ReqDeleteInboxMessage_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_ProtoMessage_ReqDeleteInboxMessage_descriptor,
        new java.lang.String[] { "MsgID", "InboxId", });
    internal_static_ProtoMessage_ResDeleteInboxMessage_descriptor =
      getDescriptor().getMessageTypes().get(5);
    internal_static_ProtoMessage_ResDeleteInboxMessage_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_ProtoMessage_ResDeleteInboxMessage_descriptor,
        new java.lang.String[] { "MsgID", "Error", "InboxId", });
    com.proto.CommonMessage.getDescriptor();
  }

  // @@protoc_insertion_point(outer_class_scope)
}
