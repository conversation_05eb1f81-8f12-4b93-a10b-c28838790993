// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: QuestMessage.proto

package com.proto;

public final class QuestMessage {
  private QuestMessage() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  public interface ReqQuestDataMessageOrBuilder extends
      // @@protoc_insertion_point(interface_extends:ProtoMessage.ReqQuestDataMessage)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>int32 msgID = 1;</code>
     * @return The msgID.
     */
    int getMsgID();
  }
  /**
   * <pre>
   *请求任务数据
   * </pre>
   *
   * Protobuf type {@code ProtoMessage.ReqQuestDataMessage}
   */
  public static final class ReqQuestDataMessage extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:ProtoMessage.ReqQuestDataMessage)
      ReqQuestDataMessageOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use ReqQuestDataMessage.newBuilder() to construct.
    private ReqQuestDataMessage(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private ReqQuestDataMessage() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new ReqQuestDataMessage();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private ReqQuestDataMessage(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {

              msgID_ = input.readInt32();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.proto.QuestMessage.internal_static_ProtoMessage_ReqQuestDataMessage_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.proto.QuestMessage.internal_static_ProtoMessage_ReqQuestDataMessage_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.proto.QuestMessage.ReqQuestDataMessage.class, com.proto.QuestMessage.ReqQuestDataMessage.Builder.class);
    }

    public static final int MSGID_FIELD_NUMBER = 1;
    private int msgID_;
    /**
     * <code>int32 msgID = 1;</code>
     * @return The msgID.
     */
    @java.lang.Override
    public int getMsgID() {
      return msgID_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (msgID_ != 0) {
        output.writeInt32(1, msgID_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (msgID_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, msgID_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.proto.QuestMessage.ReqQuestDataMessage)) {
        return super.equals(obj);
      }
      com.proto.QuestMessage.ReqQuestDataMessage other = (com.proto.QuestMessage.ReqQuestDataMessage) obj;

      if (getMsgID()
          != other.getMsgID()) return false;
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + MSGID_FIELD_NUMBER;
      hash = (53 * hash) + getMsgID();
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.proto.QuestMessage.ReqQuestDataMessage parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.proto.QuestMessage.ReqQuestDataMessage parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.proto.QuestMessage.ReqQuestDataMessage parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.proto.QuestMessage.ReqQuestDataMessage parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.proto.QuestMessage.ReqQuestDataMessage parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.proto.QuestMessage.ReqQuestDataMessage parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.proto.QuestMessage.ReqQuestDataMessage parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.proto.QuestMessage.ReqQuestDataMessage parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.proto.QuestMessage.ReqQuestDataMessage parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.proto.QuestMessage.ReqQuestDataMessage parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.proto.QuestMessage.ReqQuestDataMessage parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.proto.QuestMessage.ReqQuestDataMessage parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.proto.QuestMessage.ReqQuestDataMessage prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     *请求任务数据
     * </pre>
     *
     * Protobuf type {@code ProtoMessage.ReqQuestDataMessage}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:ProtoMessage.ReqQuestDataMessage)
        com.proto.QuestMessage.ReqQuestDataMessageOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.proto.QuestMessage.internal_static_ProtoMessage_ReqQuestDataMessage_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.proto.QuestMessage.internal_static_ProtoMessage_ReqQuestDataMessage_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.proto.QuestMessage.ReqQuestDataMessage.class, com.proto.QuestMessage.ReqQuestDataMessage.Builder.class);
      }

      // Construct using com.proto.QuestMessage.ReqQuestDataMessage.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        msgID_ = 0;

        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.proto.QuestMessage.internal_static_ProtoMessage_ReqQuestDataMessage_descriptor;
      }

      @java.lang.Override
      public com.proto.QuestMessage.ReqQuestDataMessage getDefaultInstanceForType() {
        return com.proto.QuestMessage.ReqQuestDataMessage.getDefaultInstance();
      }

      @java.lang.Override
      public com.proto.QuestMessage.ReqQuestDataMessage build() {
        com.proto.QuestMessage.ReqQuestDataMessage result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.proto.QuestMessage.ReqQuestDataMessage buildPartial() {
        com.proto.QuestMessage.ReqQuestDataMessage result = new com.proto.QuestMessage.ReqQuestDataMessage(this);
        result.msgID_ = msgID_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.proto.QuestMessage.ReqQuestDataMessage) {
          return mergeFrom((com.proto.QuestMessage.ReqQuestDataMessage)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.proto.QuestMessage.ReqQuestDataMessage other) {
        if (other == com.proto.QuestMessage.ReqQuestDataMessage.getDefaultInstance()) return this;
        if (other.getMsgID() != 0) {
          setMsgID(other.getMsgID());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.proto.QuestMessage.ReqQuestDataMessage parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.proto.QuestMessage.ReqQuestDataMessage) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }

      private int msgID_ ;
      /**
       * <code>int32 msgID = 1;</code>
       * @return The msgID.
       */
      @java.lang.Override
      public int getMsgID() {
        return msgID_;
      }
      /**
       * <code>int32 msgID = 1;</code>
       * @param value The msgID to set.
       * @return This builder for chaining.
       */
      public Builder setMsgID(int value) {
        
        msgID_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>int32 msgID = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearMsgID() {
        
        msgID_ = 0;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:ProtoMessage.ReqQuestDataMessage)
    }

    // @@protoc_insertion_point(class_scope:ProtoMessage.ReqQuestDataMessage)
    private static final com.proto.QuestMessage.ReqQuestDataMessage DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.proto.QuestMessage.ReqQuestDataMessage();
    }

    public static com.proto.QuestMessage.ReqQuestDataMessage getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<ReqQuestDataMessage>
        PARSER = new com.google.protobuf.AbstractParser<ReqQuestDataMessage>() {
      @java.lang.Override
      public ReqQuestDataMessage parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new ReqQuestDataMessage(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<ReqQuestDataMessage> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<ReqQuestDataMessage> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.proto.QuestMessage.ReqQuestDataMessage getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface ResQuestDataMessageOrBuilder extends
      // @@protoc_insertion_point(interface_extends:ProtoMessage.ResQuestDataMessage)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>int32 msgID = 1;</code>
     * @return The msgID.
     */
    int getMsgID();

    /**
     * <pre>
     *错误码
     * </pre>
     *
     * <code>int32 error = 2;</code>
     * @return The error.
     */
    int getError();

    /**
     * <pre>
     *累计奖励
     * </pre>
     *
     * <code>.ProtoMessage.DItemShow accumulatedRewards = 3;</code>
     * @return Whether the accumulatedRewards field is set.
     */
    boolean hasAccumulatedRewards();
    /**
     * <pre>
     *累计奖励
     * </pre>
     *
     * <code>.ProtoMessage.DItemShow accumulatedRewards = 3;</code>
     * @return The accumulatedRewards.
     */
    com.proto.CommonMessage.DItemShow getAccumulatedRewards();
    /**
     * <pre>
     *累计奖励
     * </pre>
     *
     * <code>.ProtoMessage.DItemShow accumulatedRewards = 3;</code>
     */
    com.proto.CommonMessage.DItemShowOrBuilder getAccumulatedRewardsOrBuilder();

    /**
     * <pre>
     *当前时间
     * </pre>
     *
     * <code>int64 currentTime = 4;</code>
     * @return The currentTime.
     */
    long getCurrentTime();

    /**
     * <pre>
     *每日结束时间
     * </pre>
     *
     * <code>int64 dailyEndTime = 5;</code>
     * @return The dailyEndTime.
     */
    long getDailyEndTime();

    /**
     * <pre>
     *每周结束时间
     * </pre>
     *
     * <code>int64 weeklyEndTime = 6;</code>
     * @return The weeklyEndTime.
     */
    long getWeeklyEndTime();

    /**
     * <pre>
     *任务列表
     * </pre>
     *
     * <code>repeated .ProtoMessage.QuestInfo questList = 7;</code>
     */
    java.util.List<com.proto.QuestMessage.QuestInfo> 
        getQuestListList();
    /**
     * <pre>
     *任务列表
     * </pre>
     *
     * <code>repeated .ProtoMessage.QuestInfo questList = 7;</code>
     */
    com.proto.QuestMessage.QuestInfo getQuestList(int index);
    /**
     * <pre>
     *任务列表
     * </pre>
     *
     * <code>repeated .ProtoMessage.QuestInfo questList = 7;</code>
     */
    int getQuestListCount();
    /**
     * <pre>
     *任务列表
     * </pre>
     *
     * <code>repeated .ProtoMessage.QuestInfo questList = 7;</code>
     */
    java.util.List<? extends com.proto.QuestMessage.QuestInfoOrBuilder> 
        getQuestListOrBuilderList();
    /**
     * <pre>
     *任务列表
     * </pre>
     *
     * <code>repeated .ProtoMessage.QuestInfo questList = 7;</code>
     */
    com.proto.QuestMessage.QuestInfoOrBuilder getQuestListOrBuilder(
        int index);
  }
  /**
   * <pre>
   *返回任务数据
   * </pre>
   *
   * Protobuf type {@code ProtoMessage.ResQuestDataMessage}
   */
  public static final class ResQuestDataMessage extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:ProtoMessage.ResQuestDataMessage)
      ResQuestDataMessageOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use ResQuestDataMessage.newBuilder() to construct.
    private ResQuestDataMessage(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private ResQuestDataMessage() {
      questList_ = java.util.Collections.emptyList();
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new ResQuestDataMessage();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private ResQuestDataMessage(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {

              msgID_ = input.readInt32();
              break;
            }
            case 16: {

              error_ = input.readInt32();
              break;
            }
            case 26: {
              com.proto.CommonMessage.DItemShow.Builder subBuilder = null;
              if (accumulatedRewards_ != null) {
                subBuilder = accumulatedRewards_.toBuilder();
              }
              accumulatedRewards_ = input.readMessage(com.proto.CommonMessage.DItemShow.parser(), extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(accumulatedRewards_);
                accumulatedRewards_ = subBuilder.buildPartial();
              }

              break;
            }
            case 32: {

              currentTime_ = input.readInt64();
              break;
            }
            case 40: {

              dailyEndTime_ = input.readInt64();
              break;
            }
            case 48: {

              weeklyEndTime_ = input.readInt64();
              break;
            }
            case 58: {
              if (!((mutable_bitField0_ & 0x00000001) != 0)) {
                questList_ = new java.util.ArrayList<com.proto.QuestMessage.QuestInfo>();
                mutable_bitField0_ |= 0x00000001;
              }
              questList_.add(
                  input.readMessage(com.proto.QuestMessage.QuestInfo.parser(), extensionRegistry));
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        if (((mutable_bitField0_ & 0x00000001) != 0)) {
          questList_ = java.util.Collections.unmodifiableList(questList_);
        }
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.proto.QuestMessage.internal_static_ProtoMessage_ResQuestDataMessage_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.proto.QuestMessage.internal_static_ProtoMessage_ResQuestDataMessage_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.proto.QuestMessage.ResQuestDataMessage.class, com.proto.QuestMessage.ResQuestDataMessage.Builder.class);
    }

    public static final int MSGID_FIELD_NUMBER = 1;
    private int msgID_;
    /**
     * <code>int32 msgID = 1;</code>
     * @return The msgID.
     */
    @java.lang.Override
    public int getMsgID() {
      return msgID_;
    }

    public static final int ERROR_FIELD_NUMBER = 2;
    private int error_;
    /**
     * <pre>
     *错误码
     * </pre>
     *
     * <code>int32 error = 2;</code>
     * @return The error.
     */
    @java.lang.Override
    public int getError() {
      return error_;
    }

    public static final int ACCUMULATEDREWARDS_FIELD_NUMBER = 3;
    private com.proto.CommonMessage.DItemShow accumulatedRewards_;
    /**
     * <pre>
     *累计奖励
     * </pre>
     *
     * <code>.ProtoMessage.DItemShow accumulatedRewards = 3;</code>
     * @return Whether the accumulatedRewards field is set.
     */
    @java.lang.Override
    public boolean hasAccumulatedRewards() {
      return accumulatedRewards_ != null;
    }
    /**
     * <pre>
     *累计奖励
     * </pre>
     *
     * <code>.ProtoMessage.DItemShow accumulatedRewards = 3;</code>
     * @return The accumulatedRewards.
     */
    @java.lang.Override
    public com.proto.CommonMessage.DItemShow getAccumulatedRewards() {
      return accumulatedRewards_ == null ? com.proto.CommonMessage.DItemShow.getDefaultInstance() : accumulatedRewards_;
    }
    /**
     * <pre>
     *累计奖励
     * </pre>
     *
     * <code>.ProtoMessage.DItemShow accumulatedRewards = 3;</code>
     */
    @java.lang.Override
    public com.proto.CommonMessage.DItemShowOrBuilder getAccumulatedRewardsOrBuilder() {
      return getAccumulatedRewards();
    }

    public static final int CURRENTTIME_FIELD_NUMBER = 4;
    private long currentTime_;
    /**
     * <pre>
     *当前时间
     * </pre>
     *
     * <code>int64 currentTime = 4;</code>
     * @return The currentTime.
     */
    @java.lang.Override
    public long getCurrentTime() {
      return currentTime_;
    }

    public static final int DAILYENDTIME_FIELD_NUMBER = 5;
    private long dailyEndTime_;
    /**
     * <pre>
     *每日结束时间
     * </pre>
     *
     * <code>int64 dailyEndTime = 5;</code>
     * @return The dailyEndTime.
     */
    @java.lang.Override
    public long getDailyEndTime() {
      return dailyEndTime_;
    }

    public static final int WEEKLYENDTIME_FIELD_NUMBER = 6;
    private long weeklyEndTime_;
    /**
     * <pre>
     *每周结束时间
     * </pre>
     *
     * <code>int64 weeklyEndTime = 6;</code>
     * @return The weeklyEndTime.
     */
    @java.lang.Override
    public long getWeeklyEndTime() {
      return weeklyEndTime_;
    }

    public static final int QUESTLIST_FIELD_NUMBER = 7;
    private java.util.List<com.proto.QuestMessage.QuestInfo> questList_;
    /**
     * <pre>
     *任务列表
     * </pre>
     *
     * <code>repeated .ProtoMessage.QuestInfo questList = 7;</code>
     */
    @java.lang.Override
    public java.util.List<com.proto.QuestMessage.QuestInfo> getQuestListList() {
      return questList_;
    }
    /**
     * <pre>
     *任务列表
     * </pre>
     *
     * <code>repeated .ProtoMessage.QuestInfo questList = 7;</code>
     */
    @java.lang.Override
    public java.util.List<? extends com.proto.QuestMessage.QuestInfoOrBuilder> 
        getQuestListOrBuilderList() {
      return questList_;
    }
    /**
     * <pre>
     *任务列表
     * </pre>
     *
     * <code>repeated .ProtoMessage.QuestInfo questList = 7;</code>
     */
    @java.lang.Override
    public int getQuestListCount() {
      return questList_.size();
    }
    /**
     * <pre>
     *任务列表
     * </pre>
     *
     * <code>repeated .ProtoMessage.QuestInfo questList = 7;</code>
     */
    @java.lang.Override
    public com.proto.QuestMessage.QuestInfo getQuestList(int index) {
      return questList_.get(index);
    }
    /**
     * <pre>
     *任务列表
     * </pre>
     *
     * <code>repeated .ProtoMessage.QuestInfo questList = 7;</code>
     */
    @java.lang.Override
    public com.proto.QuestMessage.QuestInfoOrBuilder getQuestListOrBuilder(
        int index) {
      return questList_.get(index);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (msgID_ != 0) {
        output.writeInt32(1, msgID_);
      }
      if (error_ != 0) {
        output.writeInt32(2, error_);
      }
      if (accumulatedRewards_ != null) {
        output.writeMessage(3, getAccumulatedRewards());
      }
      if (currentTime_ != 0L) {
        output.writeInt64(4, currentTime_);
      }
      if (dailyEndTime_ != 0L) {
        output.writeInt64(5, dailyEndTime_);
      }
      if (weeklyEndTime_ != 0L) {
        output.writeInt64(6, weeklyEndTime_);
      }
      for (int i = 0; i < questList_.size(); i++) {
        output.writeMessage(7, questList_.get(i));
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (msgID_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, msgID_);
      }
      if (error_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(2, error_);
      }
      if (accumulatedRewards_ != null) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(3, getAccumulatedRewards());
      }
      if (currentTime_ != 0L) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(4, currentTime_);
      }
      if (dailyEndTime_ != 0L) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(5, dailyEndTime_);
      }
      if (weeklyEndTime_ != 0L) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(6, weeklyEndTime_);
      }
      for (int i = 0; i < questList_.size(); i++) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(7, questList_.get(i));
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.proto.QuestMessage.ResQuestDataMessage)) {
        return super.equals(obj);
      }
      com.proto.QuestMessage.ResQuestDataMessage other = (com.proto.QuestMessage.ResQuestDataMessage) obj;

      if (getMsgID()
          != other.getMsgID()) return false;
      if (getError()
          != other.getError()) return false;
      if (hasAccumulatedRewards() != other.hasAccumulatedRewards()) return false;
      if (hasAccumulatedRewards()) {
        if (!getAccumulatedRewards()
            .equals(other.getAccumulatedRewards())) return false;
      }
      if (getCurrentTime()
          != other.getCurrentTime()) return false;
      if (getDailyEndTime()
          != other.getDailyEndTime()) return false;
      if (getWeeklyEndTime()
          != other.getWeeklyEndTime()) return false;
      if (!getQuestListList()
          .equals(other.getQuestListList())) return false;
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + MSGID_FIELD_NUMBER;
      hash = (53 * hash) + getMsgID();
      hash = (37 * hash) + ERROR_FIELD_NUMBER;
      hash = (53 * hash) + getError();
      if (hasAccumulatedRewards()) {
        hash = (37 * hash) + ACCUMULATEDREWARDS_FIELD_NUMBER;
        hash = (53 * hash) + getAccumulatedRewards().hashCode();
      }
      hash = (37 * hash) + CURRENTTIME_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getCurrentTime());
      hash = (37 * hash) + DAILYENDTIME_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getDailyEndTime());
      hash = (37 * hash) + WEEKLYENDTIME_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getWeeklyEndTime());
      if (getQuestListCount() > 0) {
        hash = (37 * hash) + QUESTLIST_FIELD_NUMBER;
        hash = (53 * hash) + getQuestListList().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.proto.QuestMessage.ResQuestDataMessage parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.proto.QuestMessage.ResQuestDataMessage parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.proto.QuestMessage.ResQuestDataMessage parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.proto.QuestMessage.ResQuestDataMessage parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.proto.QuestMessage.ResQuestDataMessage parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.proto.QuestMessage.ResQuestDataMessage parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.proto.QuestMessage.ResQuestDataMessage parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.proto.QuestMessage.ResQuestDataMessage parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.proto.QuestMessage.ResQuestDataMessage parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.proto.QuestMessage.ResQuestDataMessage parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.proto.QuestMessage.ResQuestDataMessage parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.proto.QuestMessage.ResQuestDataMessage parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.proto.QuestMessage.ResQuestDataMessage prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     *返回任务数据
     * </pre>
     *
     * Protobuf type {@code ProtoMessage.ResQuestDataMessage}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:ProtoMessage.ResQuestDataMessage)
        com.proto.QuestMessage.ResQuestDataMessageOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.proto.QuestMessage.internal_static_ProtoMessage_ResQuestDataMessage_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.proto.QuestMessage.internal_static_ProtoMessage_ResQuestDataMessage_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.proto.QuestMessage.ResQuestDataMessage.class, com.proto.QuestMessage.ResQuestDataMessage.Builder.class);
      }

      // Construct using com.proto.QuestMessage.ResQuestDataMessage.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
          getQuestListFieldBuilder();
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        msgID_ = 0;

        error_ = 0;

        if (accumulatedRewardsBuilder_ == null) {
          accumulatedRewards_ = null;
        } else {
          accumulatedRewards_ = null;
          accumulatedRewardsBuilder_ = null;
        }
        currentTime_ = 0L;

        dailyEndTime_ = 0L;

        weeklyEndTime_ = 0L;

        if (questListBuilder_ == null) {
          questList_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000001);
        } else {
          questListBuilder_.clear();
        }
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.proto.QuestMessage.internal_static_ProtoMessage_ResQuestDataMessage_descriptor;
      }

      @java.lang.Override
      public com.proto.QuestMessage.ResQuestDataMessage getDefaultInstanceForType() {
        return com.proto.QuestMessage.ResQuestDataMessage.getDefaultInstance();
      }

      @java.lang.Override
      public com.proto.QuestMessage.ResQuestDataMessage build() {
        com.proto.QuestMessage.ResQuestDataMessage result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.proto.QuestMessage.ResQuestDataMessage buildPartial() {
        com.proto.QuestMessage.ResQuestDataMessage result = new com.proto.QuestMessage.ResQuestDataMessage(this);
        int from_bitField0_ = bitField0_;
        result.msgID_ = msgID_;
        result.error_ = error_;
        if (accumulatedRewardsBuilder_ == null) {
          result.accumulatedRewards_ = accumulatedRewards_;
        } else {
          result.accumulatedRewards_ = accumulatedRewardsBuilder_.build();
        }
        result.currentTime_ = currentTime_;
        result.dailyEndTime_ = dailyEndTime_;
        result.weeklyEndTime_ = weeklyEndTime_;
        if (questListBuilder_ == null) {
          if (((bitField0_ & 0x00000001) != 0)) {
            questList_ = java.util.Collections.unmodifiableList(questList_);
            bitField0_ = (bitField0_ & ~0x00000001);
          }
          result.questList_ = questList_;
        } else {
          result.questList_ = questListBuilder_.build();
        }
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.proto.QuestMessage.ResQuestDataMessage) {
          return mergeFrom((com.proto.QuestMessage.ResQuestDataMessage)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.proto.QuestMessage.ResQuestDataMessage other) {
        if (other == com.proto.QuestMessage.ResQuestDataMessage.getDefaultInstance()) return this;
        if (other.getMsgID() != 0) {
          setMsgID(other.getMsgID());
        }
        if (other.getError() != 0) {
          setError(other.getError());
        }
        if (other.hasAccumulatedRewards()) {
          mergeAccumulatedRewards(other.getAccumulatedRewards());
        }
        if (other.getCurrentTime() != 0L) {
          setCurrentTime(other.getCurrentTime());
        }
        if (other.getDailyEndTime() != 0L) {
          setDailyEndTime(other.getDailyEndTime());
        }
        if (other.getWeeklyEndTime() != 0L) {
          setWeeklyEndTime(other.getWeeklyEndTime());
        }
        if (questListBuilder_ == null) {
          if (!other.questList_.isEmpty()) {
            if (questList_.isEmpty()) {
              questList_ = other.questList_;
              bitField0_ = (bitField0_ & ~0x00000001);
            } else {
              ensureQuestListIsMutable();
              questList_.addAll(other.questList_);
            }
            onChanged();
          }
        } else {
          if (!other.questList_.isEmpty()) {
            if (questListBuilder_.isEmpty()) {
              questListBuilder_.dispose();
              questListBuilder_ = null;
              questList_ = other.questList_;
              bitField0_ = (bitField0_ & ~0x00000001);
              questListBuilder_ = 
                com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                   getQuestListFieldBuilder() : null;
            } else {
              questListBuilder_.addAllMessages(other.questList_);
            }
          }
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.proto.QuestMessage.ResQuestDataMessage parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.proto.QuestMessage.ResQuestDataMessage) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private int msgID_ ;
      /**
       * <code>int32 msgID = 1;</code>
       * @return The msgID.
       */
      @java.lang.Override
      public int getMsgID() {
        return msgID_;
      }
      /**
       * <code>int32 msgID = 1;</code>
       * @param value The msgID to set.
       * @return This builder for chaining.
       */
      public Builder setMsgID(int value) {
        
        msgID_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>int32 msgID = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearMsgID() {
        
        msgID_ = 0;
        onChanged();
        return this;
      }

      private int error_ ;
      /**
       * <pre>
       *错误码
       * </pre>
       *
       * <code>int32 error = 2;</code>
       * @return The error.
       */
      @java.lang.Override
      public int getError() {
        return error_;
      }
      /**
       * <pre>
       *错误码
       * </pre>
       *
       * <code>int32 error = 2;</code>
       * @param value The error to set.
       * @return This builder for chaining.
       */
      public Builder setError(int value) {
        
        error_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *错误码
       * </pre>
       *
       * <code>int32 error = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearError() {
        
        error_ = 0;
        onChanged();
        return this;
      }

      private com.proto.CommonMessage.DItemShow accumulatedRewards_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.proto.CommonMessage.DItemShow, com.proto.CommonMessage.DItemShow.Builder, com.proto.CommonMessage.DItemShowOrBuilder> accumulatedRewardsBuilder_;
      /**
       * <pre>
       *累计奖励
       * </pre>
       *
       * <code>.ProtoMessage.DItemShow accumulatedRewards = 3;</code>
       * @return Whether the accumulatedRewards field is set.
       */
      public boolean hasAccumulatedRewards() {
        return accumulatedRewardsBuilder_ != null || accumulatedRewards_ != null;
      }
      /**
       * <pre>
       *累计奖励
       * </pre>
       *
       * <code>.ProtoMessage.DItemShow accumulatedRewards = 3;</code>
       * @return The accumulatedRewards.
       */
      public com.proto.CommonMessage.DItemShow getAccumulatedRewards() {
        if (accumulatedRewardsBuilder_ == null) {
          return accumulatedRewards_ == null ? com.proto.CommonMessage.DItemShow.getDefaultInstance() : accumulatedRewards_;
        } else {
          return accumulatedRewardsBuilder_.getMessage();
        }
      }
      /**
       * <pre>
       *累计奖励
       * </pre>
       *
       * <code>.ProtoMessage.DItemShow accumulatedRewards = 3;</code>
       */
      public Builder setAccumulatedRewards(com.proto.CommonMessage.DItemShow value) {
        if (accumulatedRewardsBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          accumulatedRewards_ = value;
          onChanged();
        } else {
          accumulatedRewardsBuilder_.setMessage(value);
        }

        return this;
      }
      /**
       * <pre>
       *累计奖励
       * </pre>
       *
       * <code>.ProtoMessage.DItemShow accumulatedRewards = 3;</code>
       */
      public Builder setAccumulatedRewards(
          com.proto.CommonMessage.DItemShow.Builder builderForValue) {
        if (accumulatedRewardsBuilder_ == null) {
          accumulatedRewards_ = builderForValue.build();
          onChanged();
        } else {
          accumulatedRewardsBuilder_.setMessage(builderForValue.build());
        }

        return this;
      }
      /**
       * <pre>
       *累计奖励
       * </pre>
       *
       * <code>.ProtoMessage.DItemShow accumulatedRewards = 3;</code>
       */
      public Builder mergeAccumulatedRewards(com.proto.CommonMessage.DItemShow value) {
        if (accumulatedRewardsBuilder_ == null) {
          if (accumulatedRewards_ != null) {
            accumulatedRewards_ =
              com.proto.CommonMessage.DItemShow.newBuilder(accumulatedRewards_).mergeFrom(value).buildPartial();
          } else {
            accumulatedRewards_ = value;
          }
          onChanged();
        } else {
          accumulatedRewardsBuilder_.mergeFrom(value);
        }

        return this;
      }
      /**
       * <pre>
       *累计奖励
       * </pre>
       *
       * <code>.ProtoMessage.DItemShow accumulatedRewards = 3;</code>
       */
      public Builder clearAccumulatedRewards() {
        if (accumulatedRewardsBuilder_ == null) {
          accumulatedRewards_ = null;
          onChanged();
        } else {
          accumulatedRewards_ = null;
          accumulatedRewardsBuilder_ = null;
        }

        return this;
      }
      /**
       * <pre>
       *累计奖励
       * </pre>
       *
       * <code>.ProtoMessage.DItemShow accumulatedRewards = 3;</code>
       */
      public com.proto.CommonMessage.DItemShow.Builder getAccumulatedRewardsBuilder() {
        
        onChanged();
        return getAccumulatedRewardsFieldBuilder().getBuilder();
      }
      /**
       * <pre>
       *累计奖励
       * </pre>
       *
       * <code>.ProtoMessage.DItemShow accumulatedRewards = 3;</code>
       */
      public com.proto.CommonMessage.DItemShowOrBuilder getAccumulatedRewardsOrBuilder() {
        if (accumulatedRewardsBuilder_ != null) {
          return accumulatedRewardsBuilder_.getMessageOrBuilder();
        } else {
          return accumulatedRewards_ == null ?
              com.proto.CommonMessage.DItemShow.getDefaultInstance() : accumulatedRewards_;
        }
      }
      /**
       * <pre>
       *累计奖励
       * </pre>
       *
       * <code>.ProtoMessage.DItemShow accumulatedRewards = 3;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.proto.CommonMessage.DItemShow, com.proto.CommonMessage.DItemShow.Builder, com.proto.CommonMessage.DItemShowOrBuilder> 
          getAccumulatedRewardsFieldBuilder() {
        if (accumulatedRewardsBuilder_ == null) {
          accumulatedRewardsBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.proto.CommonMessage.DItemShow, com.proto.CommonMessage.DItemShow.Builder, com.proto.CommonMessage.DItemShowOrBuilder>(
                  getAccumulatedRewards(),
                  getParentForChildren(),
                  isClean());
          accumulatedRewards_ = null;
        }
        return accumulatedRewardsBuilder_;
      }

      private long currentTime_ ;
      /**
       * <pre>
       *当前时间
       * </pre>
       *
       * <code>int64 currentTime = 4;</code>
       * @return The currentTime.
       */
      @java.lang.Override
      public long getCurrentTime() {
        return currentTime_;
      }
      /**
       * <pre>
       *当前时间
       * </pre>
       *
       * <code>int64 currentTime = 4;</code>
       * @param value The currentTime to set.
       * @return This builder for chaining.
       */
      public Builder setCurrentTime(long value) {
        
        currentTime_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *当前时间
       * </pre>
       *
       * <code>int64 currentTime = 4;</code>
       * @return This builder for chaining.
       */
      public Builder clearCurrentTime() {
        
        currentTime_ = 0L;
        onChanged();
        return this;
      }

      private long dailyEndTime_ ;
      /**
       * <pre>
       *每日结束时间
       * </pre>
       *
       * <code>int64 dailyEndTime = 5;</code>
       * @return The dailyEndTime.
       */
      @java.lang.Override
      public long getDailyEndTime() {
        return dailyEndTime_;
      }
      /**
       * <pre>
       *每日结束时间
       * </pre>
       *
       * <code>int64 dailyEndTime = 5;</code>
       * @param value The dailyEndTime to set.
       * @return This builder for chaining.
       */
      public Builder setDailyEndTime(long value) {
        
        dailyEndTime_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *每日结束时间
       * </pre>
       *
       * <code>int64 dailyEndTime = 5;</code>
       * @return This builder for chaining.
       */
      public Builder clearDailyEndTime() {
        
        dailyEndTime_ = 0L;
        onChanged();
        return this;
      }

      private long weeklyEndTime_ ;
      /**
       * <pre>
       *每周结束时间
       * </pre>
       *
       * <code>int64 weeklyEndTime = 6;</code>
       * @return The weeklyEndTime.
       */
      @java.lang.Override
      public long getWeeklyEndTime() {
        return weeklyEndTime_;
      }
      /**
       * <pre>
       *每周结束时间
       * </pre>
       *
       * <code>int64 weeklyEndTime = 6;</code>
       * @param value The weeklyEndTime to set.
       * @return This builder for chaining.
       */
      public Builder setWeeklyEndTime(long value) {
        
        weeklyEndTime_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *每周结束时间
       * </pre>
       *
       * <code>int64 weeklyEndTime = 6;</code>
       * @return This builder for chaining.
       */
      public Builder clearWeeklyEndTime() {
        
        weeklyEndTime_ = 0L;
        onChanged();
        return this;
      }

      private java.util.List<com.proto.QuestMessage.QuestInfo> questList_ =
        java.util.Collections.emptyList();
      private void ensureQuestListIsMutable() {
        if (!((bitField0_ & 0x00000001) != 0)) {
          questList_ = new java.util.ArrayList<com.proto.QuestMessage.QuestInfo>(questList_);
          bitField0_ |= 0x00000001;
         }
      }

      private com.google.protobuf.RepeatedFieldBuilderV3<
          com.proto.QuestMessage.QuestInfo, com.proto.QuestMessage.QuestInfo.Builder, com.proto.QuestMessage.QuestInfoOrBuilder> questListBuilder_;

      /**
       * <pre>
       *任务列表
       * </pre>
       *
       * <code>repeated .ProtoMessage.QuestInfo questList = 7;</code>
       */
      public java.util.List<com.proto.QuestMessage.QuestInfo> getQuestListList() {
        if (questListBuilder_ == null) {
          return java.util.Collections.unmodifiableList(questList_);
        } else {
          return questListBuilder_.getMessageList();
        }
      }
      /**
       * <pre>
       *任务列表
       * </pre>
       *
       * <code>repeated .ProtoMessage.QuestInfo questList = 7;</code>
       */
      public int getQuestListCount() {
        if (questListBuilder_ == null) {
          return questList_.size();
        } else {
          return questListBuilder_.getCount();
        }
      }
      /**
       * <pre>
       *任务列表
       * </pre>
       *
       * <code>repeated .ProtoMessage.QuestInfo questList = 7;</code>
       */
      public com.proto.QuestMessage.QuestInfo getQuestList(int index) {
        if (questListBuilder_ == null) {
          return questList_.get(index);
        } else {
          return questListBuilder_.getMessage(index);
        }
      }
      /**
       * <pre>
       *任务列表
       * </pre>
       *
       * <code>repeated .ProtoMessage.QuestInfo questList = 7;</code>
       */
      public Builder setQuestList(
          int index, com.proto.QuestMessage.QuestInfo value) {
        if (questListBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureQuestListIsMutable();
          questList_.set(index, value);
          onChanged();
        } else {
          questListBuilder_.setMessage(index, value);
        }
        return this;
      }
      /**
       * <pre>
       *任务列表
       * </pre>
       *
       * <code>repeated .ProtoMessage.QuestInfo questList = 7;</code>
       */
      public Builder setQuestList(
          int index, com.proto.QuestMessage.QuestInfo.Builder builderForValue) {
        if (questListBuilder_ == null) {
          ensureQuestListIsMutable();
          questList_.set(index, builderForValue.build());
          onChanged();
        } else {
          questListBuilder_.setMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <pre>
       *任务列表
       * </pre>
       *
       * <code>repeated .ProtoMessage.QuestInfo questList = 7;</code>
       */
      public Builder addQuestList(com.proto.QuestMessage.QuestInfo value) {
        if (questListBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureQuestListIsMutable();
          questList_.add(value);
          onChanged();
        } else {
          questListBuilder_.addMessage(value);
        }
        return this;
      }
      /**
       * <pre>
       *任务列表
       * </pre>
       *
       * <code>repeated .ProtoMessage.QuestInfo questList = 7;</code>
       */
      public Builder addQuestList(
          int index, com.proto.QuestMessage.QuestInfo value) {
        if (questListBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureQuestListIsMutable();
          questList_.add(index, value);
          onChanged();
        } else {
          questListBuilder_.addMessage(index, value);
        }
        return this;
      }
      /**
       * <pre>
       *任务列表
       * </pre>
       *
       * <code>repeated .ProtoMessage.QuestInfo questList = 7;</code>
       */
      public Builder addQuestList(
          com.proto.QuestMessage.QuestInfo.Builder builderForValue) {
        if (questListBuilder_ == null) {
          ensureQuestListIsMutable();
          questList_.add(builderForValue.build());
          onChanged();
        } else {
          questListBuilder_.addMessage(builderForValue.build());
        }
        return this;
      }
      /**
       * <pre>
       *任务列表
       * </pre>
       *
       * <code>repeated .ProtoMessage.QuestInfo questList = 7;</code>
       */
      public Builder addQuestList(
          int index, com.proto.QuestMessage.QuestInfo.Builder builderForValue) {
        if (questListBuilder_ == null) {
          ensureQuestListIsMutable();
          questList_.add(index, builderForValue.build());
          onChanged();
        } else {
          questListBuilder_.addMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <pre>
       *任务列表
       * </pre>
       *
       * <code>repeated .ProtoMessage.QuestInfo questList = 7;</code>
       */
      public Builder addAllQuestList(
          java.lang.Iterable<? extends com.proto.QuestMessage.QuestInfo> values) {
        if (questListBuilder_ == null) {
          ensureQuestListIsMutable();
          com.google.protobuf.AbstractMessageLite.Builder.addAll(
              values, questList_);
          onChanged();
        } else {
          questListBuilder_.addAllMessages(values);
        }
        return this;
      }
      /**
       * <pre>
       *任务列表
       * </pre>
       *
       * <code>repeated .ProtoMessage.QuestInfo questList = 7;</code>
       */
      public Builder clearQuestList() {
        if (questListBuilder_ == null) {
          questList_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000001);
          onChanged();
        } else {
          questListBuilder_.clear();
        }
        return this;
      }
      /**
       * <pre>
       *任务列表
       * </pre>
       *
       * <code>repeated .ProtoMessage.QuestInfo questList = 7;</code>
       */
      public Builder removeQuestList(int index) {
        if (questListBuilder_ == null) {
          ensureQuestListIsMutable();
          questList_.remove(index);
          onChanged();
        } else {
          questListBuilder_.remove(index);
        }
        return this;
      }
      /**
       * <pre>
       *任务列表
       * </pre>
       *
       * <code>repeated .ProtoMessage.QuestInfo questList = 7;</code>
       */
      public com.proto.QuestMessage.QuestInfo.Builder getQuestListBuilder(
          int index) {
        return getQuestListFieldBuilder().getBuilder(index);
      }
      /**
       * <pre>
       *任务列表
       * </pre>
       *
       * <code>repeated .ProtoMessage.QuestInfo questList = 7;</code>
       */
      public com.proto.QuestMessage.QuestInfoOrBuilder getQuestListOrBuilder(
          int index) {
        if (questListBuilder_ == null) {
          return questList_.get(index);  } else {
          return questListBuilder_.getMessageOrBuilder(index);
        }
      }
      /**
       * <pre>
       *任务列表
       * </pre>
       *
       * <code>repeated .ProtoMessage.QuestInfo questList = 7;</code>
       */
      public java.util.List<? extends com.proto.QuestMessage.QuestInfoOrBuilder> 
           getQuestListOrBuilderList() {
        if (questListBuilder_ != null) {
          return questListBuilder_.getMessageOrBuilderList();
        } else {
          return java.util.Collections.unmodifiableList(questList_);
        }
      }
      /**
       * <pre>
       *任务列表
       * </pre>
       *
       * <code>repeated .ProtoMessage.QuestInfo questList = 7;</code>
       */
      public com.proto.QuestMessage.QuestInfo.Builder addQuestListBuilder() {
        return getQuestListFieldBuilder().addBuilder(
            com.proto.QuestMessage.QuestInfo.getDefaultInstance());
      }
      /**
       * <pre>
       *任务列表
       * </pre>
       *
       * <code>repeated .ProtoMessage.QuestInfo questList = 7;</code>
       */
      public com.proto.QuestMessage.QuestInfo.Builder addQuestListBuilder(
          int index) {
        return getQuestListFieldBuilder().addBuilder(
            index, com.proto.QuestMessage.QuestInfo.getDefaultInstance());
      }
      /**
       * <pre>
       *任务列表
       * </pre>
       *
       * <code>repeated .ProtoMessage.QuestInfo questList = 7;</code>
       */
      public java.util.List<com.proto.QuestMessage.QuestInfo.Builder> 
           getQuestListBuilderList() {
        return getQuestListFieldBuilder().getBuilderList();
      }
      private com.google.protobuf.RepeatedFieldBuilderV3<
          com.proto.QuestMessage.QuestInfo, com.proto.QuestMessage.QuestInfo.Builder, com.proto.QuestMessage.QuestInfoOrBuilder> 
          getQuestListFieldBuilder() {
        if (questListBuilder_ == null) {
          questListBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
              com.proto.QuestMessage.QuestInfo, com.proto.QuestMessage.QuestInfo.Builder, com.proto.QuestMessage.QuestInfoOrBuilder>(
                  questList_,
                  ((bitField0_ & 0x00000001) != 0),
                  getParentForChildren(),
                  isClean());
          questList_ = null;
        }
        return questListBuilder_;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:ProtoMessage.ResQuestDataMessage)
    }

    // @@protoc_insertion_point(class_scope:ProtoMessage.ResQuestDataMessage)
    private static final com.proto.QuestMessage.ResQuestDataMessage DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.proto.QuestMessage.ResQuestDataMessage();
    }

    public static com.proto.QuestMessage.ResQuestDataMessage getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<ResQuestDataMessage>
        PARSER = new com.google.protobuf.AbstractParser<ResQuestDataMessage>() {
      @java.lang.Override
      public ResQuestDataMessage parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new ResQuestDataMessage(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<ResQuestDataMessage> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<ResQuestDataMessage> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.proto.QuestMessage.ResQuestDataMessage getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface QuestInfoOrBuilder extends
      // @@protoc_insertion_point(interface_extends:ProtoMessage.QuestInfo)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     *当前任务id
     * </pre>
     *
     * <code>int32 questId = 1;</code>
     * @return The questId.
     */
    int getQuestId();

    /**
     * <pre>
     *任务名字
     * </pre>
     *
     * <code>string questName = 2;</code>
     * @return The questName.
     */
    java.lang.String getQuestName();
    /**
     * <pre>
     *任务名字
     * </pre>
     *
     * <code>string questName = 2;</code>
     * @return The bytes for questName.
     */
    com.google.protobuf.ByteString
        getQuestNameBytes();

    /**
     * <pre>
     *任务类型 1.每日 2.每周
     * </pre>
     *
     * <code>int32 questType = 3;</code>
     * @return The questType.
     */
    int getQuestType();

    /**
     * <pre>
     *目标类型
     * </pre>
     *
     * <code>int32 goalType = 4;</code>
     * @return The goalType.
     */
    int getGoalType();

    /**
     * <pre>
     *任务状态 1.接收 2.完成 3.领取
     * </pre>
     *
     * <code>int32 state = 5;</code>
     * @return The state.
     */
    int getState();

    /**
     * <pre>
     *当前进度
     * </pre>
     *
     * <code>double progressive = 6;</code>
     * @return The progressive.
     */
    double getProgressive();

    /**
     * <pre>
     *完成时间
     * </pre>
     *
     * <code>int64 finishedTime = 7;</code>
     * @return The finishedTime.
     */
    long getFinishedTime();

    /**
     * <pre>
     *条件
     * </pre>
     *
     * <code>.ProtoMessage.ConditionInfo conditionInfo = 8;</code>
     * @return Whether the conditionInfo field is set.
     */
    boolean hasConditionInfo();
    /**
     * <pre>
     *条件
     * </pre>
     *
     * <code>.ProtoMessage.ConditionInfo conditionInfo = 8;</code>
     * @return The conditionInfo.
     */
    com.proto.QuestMessage.ConditionInfo getConditionInfo();
    /**
     * <pre>
     *条件
     * </pre>
     *
     * <code>.ProtoMessage.ConditionInfo conditionInfo = 8;</code>
     */
    com.proto.QuestMessage.ConditionInfoOrBuilder getConditionInfoOrBuilder();

    /**
     * <pre>
     *奖励
     * </pre>
     *
     * <code>.ProtoMessage.DItemShow rewards = 9;</code>
     * @return Whether the rewards field is set.
     */
    boolean hasRewards();
    /**
     * <pre>
     *奖励
     * </pre>
     *
     * <code>.ProtoMessage.DItemShow rewards = 9;</code>
     * @return The rewards.
     */
    com.proto.CommonMessage.DItemShow getRewards();
    /**
     * <pre>
     *奖励
     * </pre>
     *
     * <code>.ProtoMessage.DItemShow rewards = 9;</code>
     */
    com.proto.CommonMessage.DItemShowOrBuilder getRewardsOrBuilder();

    /**
     * <pre>
     *任务图标
     * </pre>
     *
     * <code>string icon = 10;</code>
     * @return The icon.
     */
    java.lang.String getIcon();
    /**
     * <pre>
     *任务图标
     * </pre>
     *
     * <code>string icon = 10;</code>
     * @return The bytes for icon.
     */
    com.google.protobuf.ByteString
        getIconBytes();

    /**
     * <pre>
     *任务描述
     * </pre>
     *
     * <code>string desc = 11;</code>
     * @return The desc.
     */
    java.lang.String getDesc();
    /**
     * <pre>
     *任务描述
     * </pre>
     *
     * <code>string desc = 11;</code>
     * @return The bytes for desc.
     */
    com.google.protobuf.ByteString
        getDescBytes();

    /**
     * <pre>
     *任务唯一id
     * </pre>
     *
     * <code>string uniqueId = 12;</code>
     * @return The uniqueId.
     */
    java.lang.String getUniqueId();
    /**
     * <pre>
     *任务唯一id
     * </pre>
     *
     * <code>string uniqueId = 12;</code>
     * @return The bytes for uniqueId.
     */
    com.google.protobuf.ByteString
        getUniqueIdBytes();

    /**
     * <pre>
     *任务目标
     * </pre>
     *
     * <code>.ProtoMessage.QuestTarget questTarget = 13;</code>
     * @return Whether the questTarget field is set.
     */
    boolean hasQuestTarget();
    /**
     * <pre>
     *任务目标
     * </pre>
     *
     * <code>.ProtoMessage.QuestTarget questTarget = 13;</code>
     * @return The questTarget.
     */
    com.proto.QuestMessage.QuestTarget getQuestTarget();
    /**
     * <pre>
     *任务目标
     * </pre>
     *
     * <code>.ProtoMessage.QuestTarget questTarget = 13;</code>
     */
    com.proto.QuestMessage.QuestTargetOrBuilder getQuestTargetOrBuilder();
  }
  /**
   * Protobuf type {@code ProtoMessage.QuestInfo}
   */
  public static final class QuestInfo extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:ProtoMessage.QuestInfo)
      QuestInfoOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use QuestInfo.newBuilder() to construct.
    private QuestInfo(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private QuestInfo() {
      questName_ = "";
      icon_ = "";
      desc_ = "";
      uniqueId_ = "";
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new QuestInfo();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private QuestInfo(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {

              questId_ = input.readInt32();
              break;
            }
            case 18: {
              java.lang.String s = input.readStringRequireUtf8();

              questName_ = s;
              break;
            }
            case 24: {

              questType_ = input.readInt32();
              break;
            }
            case 32: {

              goalType_ = input.readInt32();
              break;
            }
            case 40: {

              state_ = input.readInt32();
              break;
            }
            case 49: {

              progressive_ = input.readDouble();
              break;
            }
            case 56: {

              finishedTime_ = input.readInt64();
              break;
            }
            case 66: {
              com.proto.QuestMessage.ConditionInfo.Builder subBuilder = null;
              if (conditionInfo_ != null) {
                subBuilder = conditionInfo_.toBuilder();
              }
              conditionInfo_ = input.readMessage(com.proto.QuestMessage.ConditionInfo.parser(), extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(conditionInfo_);
                conditionInfo_ = subBuilder.buildPartial();
              }

              break;
            }
            case 74: {
              com.proto.CommonMessage.DItemShow.Builder subBuilder = null;
              if (rewards_ != null) {
                subBuilder = rewards_.toBuilder();
              }
              rewards_ = input.readMessage(com.proto.CommonMessage.DItemShow.parser(), extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(rewards_);
                rewards_ = subBuilder.buildPartial();
              }

              break;
            }
            case 82: {
              java.lang.String s = input.readStringRequireUtf8();

              icon_ = s;
              break;
            }
            case 90: {
              java.lang.String s = input.readStringRequireUtf8();

              desc_ = s;
              break;
            }
            case 98: {
              java.lang.String s = input.readStringRequireUtf8();

              uniqueId_ = s;
              break;
            }
            case 106: {
              com.proto.QuestMessage.QuestTarget.Builder subBuilder = null;
              if (questTarget_ != null) {
                subBuilder = questTarget_.toBuilder();
              }
              questTarget_ = input.readMessage(com.proto.QuestMessage.QuestTarget.parser(), extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(questTarget_);
                questTarget_ = subBuilder.buildPartial();
              }

              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.proto.QuestMessage.internal_static_ProtoMessage_QuestInfo_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.proto.QuestMessage.internal_static_ProtoMessage_QuestInfo_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.proto.QuestMessage.QuestInfo.class, com.proto.QuestMessage.QuestInfo.Builder.class);
    }

    public static final int QUESTID_FIELD_NUMBER = 1;
    private int questId_;
    /**
     * <pre>
     *当前任务id
     * </pre>
     *
     * <code>int32 questId = 1;</code>
     * @return The questId.
     */
    @java.lang.Override
    public int getQuestId() {
      return questId_;
    }

    public static final int QUESTNAME_FIELD_NUMBER = 2;
    private volatile java.lang.Object questName_;
    /**
     * <pre>
     *任务名字
     * </pre>
     *
     * <code>string questName = 2;</code>
     * @return The questName.
     */
    @java.lang.Override
    public java.lang.String getQuestName() {
      java.lang.Object ref = questName_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        questName_ = s;
        return s;
      }
    }
    /**
     * <pre>
     *任务名字
     * </pre>
     *
     * <code>string questName = 2;</code>
     * @return The bytes for questName.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getQuestNameBytes() {
      java.lang.Object ref = questName_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        questName_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int QUESTTYPE_FIELD_NUMBER = 3;
    private int questType_;
    /**
     * <pre>
     *任务类型 1.每日 2.每周
     * </pre>
     *
     * <code>int32 questType = 3;</code>
     * @return The questType.
     */
    @java.lang.Override
    public int getQuestType() {
      return questType_;
    }

    public static final int GOALTYPE_FIELD_NUMBER = 4;
    private int goalType_;
    /**
     * <pre>
     *目标类型
     * </pre>
     *
     * <code>int32 goalType = 4;</code>
     * @return The goalType.
     */
    @java.lang.Override
    public int getGoalType() {
      return goalType_;
    }

    public static final int STATE_FIELD_NUMBER = 5;
    private int state_;
    /**
     * <pre>
     *任务状态 1.接收 2.完成 3.领取
     * </pre>
     *
     * <code>int32 state = 5;</code>
     * @return The state.
     */
    @java.lang.Override
    public int getState() {
      return state_;
    }

    public static final int PROGRESSIVE_FIELD_NUMBER = 6;
    private double progressive_;
    /**
     * <pre>
     *当前进度
     * </pre>
     *
     * <code>double progressive = 6;</code>
     * @return The progressive.
     */
    @java.lang.Override
    public double getProgressive() {
      return progressive_;
    }

    public static final int FINISHEDTIME_FIELD_NUMBER = 7;
    private long finishedTime_;
    /**
     * <pre>
     *完成时间
     * </pre>
     *
     * <code>int64 finishedTime = 7;</code>
     * @return The finishedTime.
     */
    @java.lang.Override
    public long getFinishedTime() {
      return finishedTime_;
    }

    public static final int CONDITIONINFO_FIELD_NUMBER = 8;
    private com.proto.QuestMessage.ConditionInfo conditionInfo_;
    /**
     * <pre>
     *条件
     * </pre>
     *
     * <code>.ProtoMessage.ConditionInfo conditionInfo = 8;</code>
     * @return Whether the conditionInfo field is set.
     */
    @java.lang.Override
    public boolean hasConditionInfo() {
      return conditionInfo_ != null;
    }
    /**
     * <pre>
     *条件
     * </pre>
     *
     * <code>.ProtoMessage.ConditionInfo conditionInfo = 8;</code>
     * @return The conditionInfo.
     */
    @java.lang.Override
    public com.proto.QuestMessage.ConditionInfo getConditionInfo() {
      return conditionInfo_ == null ? com.proto.QuestMessage.ConditionInfo.getDefaultInstance() : conditionInfo_;
    }
    /**
     * <pre>
     *条件
     * </pre>
     *
     * <code>.ProtoMessage.ConditionInfo conditionInfo = 8;</code>
     */
    @java.lang.Override
    public com.proto.QuestMessage.ConditionInfoOrBuilder getConditionInfoOrBuilder() {
      return getConditionInfo();
    }

    public static final int REWARDS_FIELD_NUMBER = 9;
    private com.proto.CommonMessage.DItemShow rewards_;
    /**
     * <pre>
     *奖励
     * </pre>
     *
     * <code>.ProtoMessage.DItemShow rewards = 9;</code>
     * @return Whether the rewards field is set.
     */
    @java.lang.Override
    public boolean hasRewards() {
      return rewards_ != null;
    }
    /**
     * <pre>
     *奖励
     * </pre>
     *
     * <code>.ProtoMessage.DItemShow rewards = 9;</code>
     * @return The rewards.
     */
    @java.lang.Override
    public com.proto.CommonMessage.DItemShow getRewards() {
      return rewards_ == null ? com.proto.CommonMessage.DItemShow.getDefaultInstance() : rewards_;
    }
    /**
     * <pre>
     *奖励
     * </pre>
     *
     * <code>.ProtoMessage.DItemShow rewards = 9;</code>
     */
    @java.lang.Override
    public com.proto.CommonMessage.DItemShowOrBuilder getRewardsOrBuilder() {
      return getRewards();
    }

    public static final int ICON_FIELD_NUMBER = 10;
    private volatile java.lang.Object icon_;
    /**
     * <pre>
     *任务图标
     * </pre>
     *
     * <code>string icon = 10;</code>
     * @return The icon.
     */
    @java.lang.Override
    public java.lang.String getIcon() {
      java.lang.Object ref = icon_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        icon_ = s;
        return s;
      }
    }
    /**
     * <pre>
     *任务图标
     * </pre>
     *
     * <code>string icon = 10;</code>
     * @return The bytes for icon.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getIconBytes() {
      java.lang.Object ref = icon_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        icon_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int DESC_FIELD_NUMBER = 11;
    private volatile java.lang.Object desc_;
    /**
     * <pre>
     *任务描述
     * </pre>
     *
     * <code>string desc = 11;</code>
     * @return The desc.
     */
    @java.lang.Override
    public java.lang.String getDesc() {
      java.lang.Object ref = desc_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        desc_ = s;
        return s;
      }
    }
    /**
     * <pre>
     *任务描述
     * </pre>
     *
     * <code>string desc = 11;</code>
     * @return The bytes for desc.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getDescBytes() {
      java.lang.Object ref = desc_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        desc_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int UNIQUEID_FIELD_NUMBER = 12;
    private volatile java.lang.Object uniqueId_;
    /**
     * <pre>
     *任务唯一id
     * </pre>
     *
     * <code>string uniqueId = 12;</code>
     * @return The uniqueId.
     */
    @java.lang.Override
    public java.lang.String getUniqueId() {
      java.lang.Object ref = uniqueId_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        uniqueId_ = s;
        return s;
      }
    }
    /**
     * <pre>
     *任务唯一id
     * </pre>
     *
     * <code>string uniqueId = 12;</code>
     * @return The bytes for uniqueId.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getUniqueIdBytes() {
      java.lang.Object ref = uniqueId_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        uniqueId_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int QUESTTARGET_FIELD_NUMBER = 13;
    private com.proto.QuestMessage.QuestTarget questTarget_;
    /**
     * <pre>
     *任务目标
     * </pre>
     *
     * <code>.ProtoMessage.QuestTarget questTarget = 13;</code>
     * @return Whether the questTarget field is set.
     */
    @java.lang.Override
    public boolean hasQuestTarget() {
      return questTarget_ != null;
    }
    /**
     * <pre>
     *任务目标
     * </pre>
     *
     * <code>.ProtoMessage.QuestTarget questTarget = 13;</code>
     * @return The questTarget.
     */
    @java.lang.Override
    public com.proto.QuestMessage.QuestTarget getQuestTarget() {
      return questTarget_ == null ? com.proto.QuestMessage.QuestTarget.getDefaultInstance() : questTarget_;
    }
    /**
     * <pre>
     *任务目标
     * </pre>
     *
     * <code>.ProtoMessage.QuestTarget questTarget = 13;</code>
     */
    @java.lang.Override
    public com.proto.QuestMessage.QuestTargetOrBuilder getQuestTargetOrBuilder() {
      return getQuestTarget();
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (questId_ != 0) {
        output.writeInt32(1, questId_);
      }
      if (!getQuestNameBytes().isEmpty()) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 2, questName_);
      }
      if (questType_ != 0) {
        output.writeInt32(3, questType_);
      }
      if (goalType_ != 0) {
        output.writeInt32(4, goalType_);
      }
      if (state_ != 0) {
        output.writeInt32(5, state_);
      }
      if (progressive_ != 0D) {
        output.writeDouble(6, progressive_);
      }
      if (finishedTime_ != 0L) {
        output.writeInt64(7, finishedTime_);
      }
      if (conditionInfo_ != null) {
        output.writeMessage(8, getConditionInfo());
      }
      if (rewards_ != null) {
        output.writeMessage(9, getRewards());
      }
      if (!getIconBytes().isEmpty()) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 10, icon_);
      }
      if (!getDescBytes().isEmpty()) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 11, desc_);
      }
      if (!getUniqueIdBytes().isEmpty()) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 12, uniqueId_);
      }
      if (questTarget_ != null) {
        output.writeMessage(13, getQuestTarget());
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (questId_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, questId_);
      }
      if (!getQuestNameBytes().isEmpty()) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(2, questName_);
      }
      if (questType_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(3, questType_);
      }
      if (goalType_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(4, goalType_);
      }
      if (state_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(5, state_);
      }
      if (progressive_ != 0D) {
        size += com.google.protobuf.CodedOutputStream
          .computeDoubleSize(6, progressive_);
      }
      if (finishedTime_ != 0L) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(7, finishedTime_);
      }
      if (conditionInfo_ != null) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(8, getConditionInfo());
      }
      if (rewards_ != null) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(9, getRewards());
      }
      if (!getIconBytes().isEmpty()) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(10, icon_);
      }
      if (!getDescBytes().isEmpty()) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(11, desc_);
      }
      if (!getUniqueIdBytes().isEmpty()) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(12, uniqueId_);
      }
      if (questTarget_ != null) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(13, getQuestTarget());
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.proto.QuestMessage.QuestInfo)) {
        return super.equals(obj);
      }
      com.proto.QuestMessage.QuestInfo other = (com.proto.QuestMessage.QuestInfo) obj;

      if (getQuestId()
          != other.getQuestId()) return false;
      if (!getQuestName()
          .equals(other.getQuestName())) return false;
      if (getQuestType()
          != other.getQuestType()) return false;
      if (getGoalType()
          != other.getGoalType()) return false;
      if (getState()
          != other.getState()) return false;
      if (java.lang.Double.doubleToLongBits(getProgressive())
          != java.lang.Double.doubleToLongBits(
              other.getProgressive())) return false;
      if (getFinishedTime()
          != other.getFinishedTime()) return false;
      if (hasConditionInfo() != other.hasConditionInfo()) return false;
      if (hasConditionInfo()) {
        if (!getConditionInfo()
            .equals(other.getConditionInfo())) return false;
      }
      if (hasRewards() != other.hasRewards()) return false;
      if (hasRewards()) {
        if (!getRewards()
            .equals(other.getRewards())) return false;
      }
      if (!getIcon()
          .equals(other.getIcon())) return false;
      if (!getDesc()
          .equals(other.getDesc())) return false;
      if (!getUniqueId()
          .equals(other.getUniqueId())) return false;
      if (hasQuestTarget() != other.hasQuestTarget()) return false;
      if (hasQuestTarget()) {
        if (!getQuestTarget()
            .equals(other.getQuestTarget())) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + QUESTID_FIELD_NUMBER;
      hash = (53 * hash) + getQuestId();
      hash = (37 * hash) + QUESTNAME_FIELD_NUMBER;
      hash = (53 * hash) + getQuestName().hashCode();
      hash = (37 * hash) + QUESTTYPE_FIELD_NUMBER;
      hash = (53 * hash) + getQuestType();
      hash = (37 * hash) + GOALTYPE_FIELD_NUMBER;
      hash = (53 * hash) + getGoalType();
      hash = (37 * hash) + STATE_FIELD_NUMBER;
      hash = (53 * hash) + getState();
      hash = (37 * hash) + PROGRESSIVE_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          java.lang.Double.doubleToLongBits(getProgressive()));
      hash = (37 * hash) + FINISHEDTIME_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getFinishedTime());
      if (hasConditionInfo()) {
        hash = (37 * hash) + CONDITIONINFO_FIELD_NUMBER;
        hash = (53 * hash) + getConditionInfo().hashCode();
      }
      if (hasRewards()) {
        hash = (37 * hash) + REWARDS_FIELD_NUMBER;
        hash = (53 * hash) + getRewards().hashCode();
      }
      hash = (37 * hash) + ICON_FIELD_NUMBER;
      hash = (53 * hash) + getIcon().hashCode();
      hash = (37 * hash) + DESC_FIELD_NUMBER;
      hash = (53 * hash) + getDesc().hashCode();
      hash = (37 * hash) + UNIQUEID_FIELD_NUMBER;
      hash = (53 * hash) + getUniqueId().hashCode();
      if (hasQuestTarget()) {
        hash = (37 * hash) + QUESTTARGET_FIELD_NUMBER;
        hash = (53 * hash) + getQuestTarget().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.proto.QuestMessage.QuestInfo parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.proto.QuestMessage.QuestInfo parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.proto.QuestMessage.QuestInfo parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.proto.QuestMessage.QuestInfo parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.proto.QuestMessage.QuestInfo parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.proto.QuestMessage.QuestInfo parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.proto.QuestMessage.QuestInfo parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.proto.QuestMessage.QuestInfo parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.proto.QuestMessage.QuestInfo parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.proto.QuestMessage.QuestInfo parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.proto.QuestMessage.QuestInfo parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.proto.QuestMessage.QuestInfo parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.proto.QuestMessage.QuestInfo prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code ProtoMessage.QuestInfo}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:ProtoMessage.QuestInfo)
        com.proto.QuestMessage.QuestInfoOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.proto.QuestMessage.internal_static_ProtoMessage_QuestInfo_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.proto.QuestMessage.internal_static_ProtoMessage_QuestInfo_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.proto.QuestMessage.QuestInfo.class, com.proto.QuestMessage.QuestInfo.Builder.class);
      }

      // Construct using com.proto.QuestMessage.QuestInfo.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        questId_ = 0;

        questName_ = "";

        questType_ = 0;

        goalType_ = 0;

        state_ = 0;

        progressive_ = 0D;

        finishedTime_ = 0L;

        if (conditionInfoBuilder_ == null) {
          conditionInfo_ = null;
        } else {
          conditionInfo_ = null;
          conditionInfoBuilder_ = null;
        }
        if (rewardsBuilder_ == null) {
          rewards_ = null;
        } else {
          rewards_ = null;
          rewardsBuilder_ = null;
        }
        icon_ = "";

        desc_ = "";

        uniqueId_ = "";

        if (questTargetBuilder_ == null) {
          questTarget_ = null;
        } else {
          questTarget_ = null;
          questTargetBuilder_ = null;
        }
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.proto.QuestMessage.internal_static_ProtoMessage_QuestInfo_descriptor;
      }

      @java.lang.Override
      public com.proto.QuestMessage.QuestInfo getDefaultInstanceForType() {
        return com.proto.QuestMessage.QuestInfo.getDefaultInstance();
      }

      @java.lang.Override
      public com.proto.QuestMessage.QuestInfo build() {
        com.proto.QuestMessage.QuestInfo result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.proto.QuestMessage.QuestInfo buildPartial() {
        com.proto.QuestMessage.QuestInfo result = new com.proto.QuestMessage.QuestInfo(this);
        result.questId_ = questId_;
        result.questName_ = questName_;
        result.questType_ = questType_;
        result.goalType_ = goalType_;
        result.state_ = state_;
        result.progressive_ = progressive_;
        result.finishedTime_ = finishedTime_;
        if (conditionInfoBuilder_ == null) {
          result.conditionInfo_ = conditionInfo_;
        } else {
          result.conditionInfo_ = conditionInfoBuilder_.build();
        }
        if (rewardsBuilder_ == null) {
          result.rewards_ = rewards_;
        } else {
          result.rewards_ = rewardsBuilder_.build();
        }
        result.icon_ = icon_;
        result.desc_ = desc_;
        result.uniqueId_ = uniqueId_;
        if (questTargetBuilder_ == null) {
          result.questTarget_ = questTarget_;
        } else {
          result.questTarget_ = questTargetBuilder_.build();
        }
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.proto.QuestMessage.QuestInfo) {
          return mergeFrom((com.proto.QuestMessage.QuestInfo)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.proto.QuestMessage.QuestInfo other) {
        if (other == com.proto.QuestMessage.QuestInfo.getDefaultInstance()) return this;
        if (other.getQuestId() != 0) {
          setQuestId(other.getQuestId());
        }
        if (!other.getQuestName().isEmpty()) {
          questName_ = other.questName_;
          onChanged();
        }
        if (other.getQuestType() != 0) {
          setQuestType(other.getQuestType());
        }
        if (other.getGoalType() != 0) {
          setGoalType(other.getGoalType());
        }
        if (other.getState() != 0) {
          setState(other.getState());
        }
        if (other.getProgressive() != 0D) {
          setProgressive(other.getProgressive());
        }
        if (other.getFinishedTime() != 0L) {
          setFinishedTime(other.getFinishedTime());
        }
        if (other.hasConditionInfo()) {
          mergeConditionInfo(other.getConditionInfo());
        }
        if (other.hasRewards()) {
          mergeRewards(other.getRewards());
        }
        if (!other.getIcon().isEmpty()) {
          icon_ = other.icon_;
          onChanged();
        }
        if (!other.getDesc().isEmpty()) {
          desc_ = other.desc_;
          onChanged();
        }
        if (!other.getUniqueId().isEmpty()) {
          uniqueId_ = other.uniqueId_;
          onChanged();
        }
        if (other.hasQuestTarget()) {
          mergeQuestTarget(other.getQuestTarget());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.proto.QuestMessage.QuestInfo parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.proto.QuestMessage.QuestInfo) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }

      private int questId_ ;
      /**
       * <pre>
       *当前任务id
       * </pre>
       *
       * <code>int32 questId = 1;</code>
       * @return The questId.
       */
      @java.lang.Override
      public int getQuestId() {
        return questId_;
      }
      /**
       * <pre>
       *当前任务id
       * </pre>
       *
       * <code>int32 questId = 1;</code>
       * @param value The questId to set.
       * @return This builder for chaining.
       */
      public Builder setQuestId(int value) {
        
        questId_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *当前任务id
       * </pre>
       *
       * <code>int32 questId = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearQuestId() {
        
        questId_ = 0;
        onChanged();
        return this;
      }

      private java.lang.Object questName_ = "";
      /**
       * <pre>
       *任务名字
       * </pre>
       *
       * <code>string questName = 2;</code>
       * @return The questName.
       */
      public java.lang.String getQuestName() {
        java.lang.Object ref = questName_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          questName_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       *任务名字
       * </pre>
       *
       * <code>string questName = 2;</code>
       * @return The bytes for questName.
       */
      public com.google.protobuf.ByteString
          getQuestNameBytes() {
        java.lang.Object ref = questName_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          questName_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       *任务名字
       * </pre>
       *
       * <code>string questName = 2;</code>
       * @param value The questName to set.
       * @return This builder for chaining.
       */
      public Builder setQuestName(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  
        questName_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *任务名字
       * </pre>
       *
       * <code>string questName = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearQuestName() {
        
        questName_ = getDefaultInstance().getQuestName();
        onChanged();
        return this;
      }
      /**
       * <pre>
       *任务名字
       * </pre>
       *
       * <code>string questName = 2;</code>
       * @param value The bytes for questName to set.
       * @return This builder for chaining.
       */
      public Builder setQuestNameBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
        
        questName_ = value;
        onChanged();
        return this;
      }

      private int questType_ ;
      /**
       * <pre>
       *任务类型 1.每日 2.每周
       * </pre>
       *
       * <code>int32 questType = 3;</code>
       * @return The questType.
       */
      @java.lang.Override
      public int getQuestType() {
        return questType_;
      }
      /**
       * <pre>
       *任务类型 1.每日 2.每周
       * </pre>
       *
       * <code>int32 questType = 3;</code>
       * @param value The questType to set.
       * @return This builder for chaining.
       */
      public Builder setQuestType(int value) {
        
        questType_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *任务类型 1.每日 2.每周
       * </pre>
       *
       * <code>int32 questType = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearQuestType() {
        
        questType_ = 0;
        onChanged();
        return this;
      }

      private int goalType_ ;
      /**
       * <pre>
       *目标类型
       * </pre>
       *
       * <code>int32 goalType = 4;</code>
       * @return The goalType.
       */
      @java.lang.Override
      public int getGoalType() {
        return goalType_;
      }
      /**
       * <pre>
       *目标类型
       * </pre>
       *
       * <code>int32 goalType = 4;</code>
       * @param value The goalType to set.
       * @return This builder for chaining.
       */
      public Builder setGoalType(int value) {
        
        goalType_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *目标类型
       * </pre>
       *
       * <code>int32 goalType = 4;</code>
       * @return This builder for chaining.
       */
      public Builder clearGoalType() {
        
        goalType_ = 0;
        onChanged();
        return this;
      }

      private int state_ ;
      /**
       * <pre>
       *任务状态 1.接收 2.完成 3.领取
       * </pre>
       *
       * <code>int32 state = 5;</code>
       * @return The state.
       */
      @java.lang.Override
      public int getState() {
        return state_;
      }
      /**
       * <pre>
       *任务状态 1.接收 2.完成 3.领取
       * </pre>
       *
       * <code>int32 state = 5;</code>
       * @param value The state to set.
       * @return This builder for chaining.
       */
      public Builder setState(int value) {
        
        state_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *任务状态 1.接收 2.完成 3.领取
       * </pre>
       *
       * <code>int32 state = 5;</code>
       * @return This builder for chaining.
       */
      public Builder clearState() {
        
        state_ = 0;
        onChanged();
        return this;
      }

      private double progressive_ ;
      /**
       * <pre>
       *当前进度
       * </pre>
       *
       * <code>double progressive = 6;</code>
       * @return The progressive.
       */
      @java.lang.Override
      public double getProgressive() {
        return progressive_;
      }
      /**
       * <pre>
       *当前进度
       * </pre>
       *
       * <code>double progressive = 6;</code>
       * @param value The progressive to set.
       * @return This builder for chaining.
       */
      public Builder setProgressive(double value) {
        
        progressive_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *当前进度
       * </pre>
       *
       * <code>double progressive = 6;</code>
       * @return This builder for chaining.
       */
      public Builder clearProgressive() {
        
        progressive_ = 0D;
        onChanged();
        return this;
      }

      private long finishedTime_ ;
      /**
       * <pre>
       *完成时间
       * </pre>
       *
       * <code>int64 finishedTime = 7;</code>
       * @return The finishedTime.
       */
      @java.lang.Override
      public long getFinishedTime() {
        return finishedTime_;
      }
      /**
       * <pre>
       *完成时间
       * </pre>
       *
       * <code>int64 finishedTime = 7;</code>
       * @param value The finishedTime to set.
       * @return This builder for chaining.
       */
      public Builder setFinishedTime(long value) {
        
        finishedTime_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *完成时间
       * </pre>
       *
       * <code>int64 finishedTime = 7;</code>
       * @return This builder for chaining.
       */
      public Builder clearFinishedTime() {
        
        finishedTime_ = 0L;
        onChanged();
        return this;
      }

      private com.proto.QuestMessage.ConditionInfo conditionInfo_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.proto.QuestMessage.ConditionInfo, com.proto.QuestMessage.ConditionInfo.Builder, com.proto.QuestMessage.ConditionInfoOrBuilder> conditionInfoBuilder_;
      /**
       * <pre>
       *条件
       * </pre>
       *
       * <code>.ProtoMessage.ConditionInfo conditionInfo = 8;</code>
       * @return Whether the conditionInfo field is set.
       */
      public boolean hasConditionInfo() {
        return conditionInfoBuilder_ != null || conditionInfo_ != null;
      }
      /**
       * <pre>
       *条件
       * </pre>
       *
       * <code>.ProtoMessage.ConditionInfo conditionInfo = 8;</code>
       * @return The conditionInfo.
       */
      public com.proto.QuestMessage.ConditionInfo getConditionInfo() {
        if (conditionInfoBuilder_ == null) {
          return conditionInfo_ == null ? com.proto.QuestMessage.ConditionInfo.getDefaultInstance() : conditionInfo_;
        } else {
          return conditionInfoBuilder_.getMessage();
        }
      }
      /**
       * <pre>
       *条件
       * </pre>
       *
       * <code>.ProtoMessage.ConditionInfo conditionInfo = 8;</code>
       */
      public Builder setConditionInfo(com.proto.QuestMessage.ConditionInfo value) {
        if (conditionInfoBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          conditionInfo_ = value;
          onChanged();
        } else {
          conditionInfoBuilder_.setMessage(value);
        }

        return this;
      }
      /**
       * <pre>
       *条件
       * </pre>
       *
       * <code>.ProtoMessage.ConditionInfo conditionInfo = 8;</code>
       */
      public Builder setConditionInfo(
          com.proto.QuestMessage.ConditionInfo.Builder builderForValue) {
        if (conditionInfoBuilder_ == null) {
          conditionInfo_ = builderForValue.build();
          onChanged();
        } else {
          conditionInfoBuilder_.setMessage(builderForValue.build());
        }

        return this;
      }
      /**
       * <pre>
       *条件
       * </pre>
       *
       * <code>.ProtoMessage.ConditionInfo conditionInfo = 8;</code>
       */
      public Builder mergeConditionInfo(com.proto.QuestMessage.ConditionInfo value) {
        if (conditionInfoBuilder_ == null) {
          if (conditionInfo_ != null) {
            conditionInfo_ =
              com.proto.QuestMessage.ConditionInfo.newBuilder(conditionInfo_).mergeFrom(value).buildPartial();
          } else {
            conditionInfo_ = value;
          }
          onChanged();
        } else {
          conditionInfoBuilder_.mergeFrom(value);
        }

        return this;
      }
      /**
       * <pre>
       *条件
       * </pre>
       *
       * <code>.ProtoMessage.ConditionInfo conditionInfo = 8;</code>
       */
      public Builder clearConditionInfo() {
        if (conditionInfoBuilder_ == null) {
          conditionInfo_ = null;
          onChanged();
        } else {
          conditionInfo_ = null;
          conditionInfoBuilder_ = null;
        }

        return this;
      }
      /**
       * <pre>
       *条件
       * </pre>
       *
       * <code>.ProtoMessage.ConditionInfo conditionInfo = 8;</code>
       */
      public com.proto.QuestMessage.ConditionInfo.Builder getConditionInfoBuilder() {
        
        onChanged();
        return getConditionInfoFieldBuilder().getBuilder();
      }
      /**
       * <pre>
       *条件
       * </pre>
       *
       * <code>.ProtoMessage.ConditionInfo conditionInfo = 8;</code>
       */
      public com.proto.QuestMessage.ConditionInfoOrBuilder getConditionInfoOrBuilder() {
        if (conditionInfoBuilder_ != null) {
          return conditionInfoBuilder_.getMessageOrBuilder();
        } else {
          return conditionInfo_ == null ?
              com.proto.QuestMessage.ConditionInfo.getDefaultInstance() : conditionInfo_;
        }
      }
      /**
       * <pre>
       *条件
       * </pre>
       *
       * <code>.ProtoMessage.ConditionInfo conditionInfo = 8;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.proto.QuestMessage.ConditionInfo, com.proto.QuestMessage.ConditionInfo.Builder, com.proto.QuestMessage.ConditionInfoOrBuilder> 
          getConditionInfoFieldBuilder() {
        if (conditionInfoBuilder_ == null) {
          conditionInfoBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.proto.QuestMessage.ConditionInfo, com.proto.QuestMessage.ConditionInfo.Builder, com.proto.QuestMessage.ConditionInfoOrBuilder>(
                  getConditionInfo(),
                  getParentForChildren(),
                  isClean());
          conditionInfo_ = null;
        }
        return conditionInfoBuilder_;
      }

      private com.proto.CommonMessage.DItemShow rewards_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.proto.CommonMessage.DItemShow, com.proto.CommonMessage.DItemShow.Builder, com.proto.CommonMessage.DItemShowOrBuilder> rewardsBuilder_;
      /**
       * <pre>
       *奖励
       * </pre>
       *
       * <code>.ProtoMessage.DItemShow rewards = 9;</code>
       * @return Whether the rewards field is set.
       */
      public boolean hasRewards() {
        return rewardsBuilder_ != null || rewards_ != null;
      }
      /**
       * <pre>
       *奖励
       * </pre>
       *
       * <code>.ProtoMessage.DItemShow rewards = 9;</code>
       * @return The rewards.
       */
      public com.proto.CommonMessage.DItemShow getRewards() {
        if (rewardsBuilder_ == null) {
          return rewards_ == null ? com.proto.CommonMessage.DItemShow.getDefaultInstance() : rewards_;
        } else {
          return rewardsBuilder_.getMessage();
        }
      }
      /**
       * <pre>
       *奖励
       * </pre>
       *
       * <code>.ProtoMessage.DItemShow rewards = 9;</code>
       */
      public Builder setRewards(com.proto.CommonMessage.DItemShow value) {
        if (rewardsBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          rewards_ = value;
          onChanged();
        } else {
          rewardsBuilder_.setMessage(value);
        }

        return this;
      }
      /**
       * <pre>
       *奖励
       * </pre>
       *
       * <code>.ProtoMessage.DItemShow rewards = 9;</code>
       */
      public Builder setRewards(
          com.proto.CommonMessage.DItemShow.Builder builderForValue) {
        if (rewardsBuilder_ == null) {
          rewards_ = builderForValue.build();
          onChanged();
        } else {
          rewardsBuilder_.setMessage(builderForValue.build());
        }

        return this;
      }
      /**
       * <pre>
       *奖励
       * </pre>
       *
       * <code>.ProtoMessage.DItemShow rewards = 9;</code>
       */
      public Builder mergeRewards(com.proto.CommonMessage.DItemShow value) {
        if (rewardsBuilder_ == null) {
          if (rewards_ != null) {
            rewards_ =
              com.proto.CommonMessage.DItemShow.newBuilder(rewards_).mergeFrom(value).buildPartial();
          } else {
            rewards_ = value;
          }
          onChanged();
        } else {
          rewardsBuilder_.mergeFrom(value);
        }

        return this;
      }
      /**
       * <pre>
       *奖励
       * </pre>
       *
       * <code>.ProtoMessage.DItemShow rewards = 9;</code>
       */
      public Builder clearRewards() {
        if (rewardsBuilder_ == null) {
          rewards_ = null;
          onChanged();
        } else {
          rewards_ = null;
          rewardsBuilder_ = null;
        }

        return this;
      }
      /**
       * <pre>
       *奖励
       * </pre>
       *
       * <code>.ProtoMessage.DItemShow rewards = 9;</code>
       */
      public com.proto.CommonMessage.DItemShow.Builder getRewardsBuilder() {
        
        onChanged();
        return getRewardsFieldBuilder().getBuilder();
      }
      /**
       * <pre>
       *奖励
       * </pre>
       *
       * <code>.ProtoMessage.DItemShow rewards = 9;</code>
       */
      public com.proto.CommonMessage.DItemShowOrBuilder getRewardsOrBuilder() {
        if (rewardsBuilder_ != null) {
          return rewardsBuilder_.getMessageOrBuilder();
        } else {
          return rewards_ == null ?
              com.proto.CommonMessage.DItemShow.getDefaultInstance() : rewards_;
        }
      }
      /**
       * <pre>
       *奖励
       * </pre>
       *
       * <code>.ProtoMessage.DItemShow rewards = 9;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.proto.CommonMessage.DItemShow, com.proto.CommonMessage.DItemShow.Builder, com.proto.CommonMessage.DItemShowOrBuilder> 
          getRewardsFieldBuilder() {
        if (rewardsBuilder_ == null) {
          rewardsBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.proto.CommonMessage.DItemShow, com.proto.CommonMessage.DItemShow.Builder, com.proto.CommonMessage.DItemShowOrBuilder>(
                  getRewards(),
                  getParentForChildren(),
                  isClean());
          rewards_ = null;
        }
        return rewardsBuilder_;
      }

      private java.lang.Object icon_ = "";
      /**
       * <pre>
       *任务图标
       * </pre>
       *
       * <code>string icon = 10;</code>
       * @return The icon.
       */
      public java.lang.String getIcon() {
        java.lang.Object ref = icon_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          icon_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       *任务图标
       * </pre>
       *
       * <code>string icon = 10;</code>
       * @return The bytes for icon.
       */
      public com.google.protobuf.ByteString
          getIconBytes() {
        java.lang.Object ref = icon_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          icon_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       *任务图标
       * </pre>
       *
       * <code>string icon = 10;</code>
       * @param value The icon to set.
       * @return This builder for chaining.
       */
      public Builder setIcon(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  
        icon_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *任务图标
       * </pre>
       *
       * <code>string icon = 10;</code>
       * @return This builder for chaining.
       */
      public Builder clearIcon() {
        
        icon_ = getDefaultInstance().getIcon();
        onChanged();
        return this;
      }
      /**
       * <pre>
       *任务图标
       * </pre>
       *
       * <code>string icon = 10;</code>
       * @param value The bytes for icon to set.
       * @return This builder for chaining.
       */
      public Builder setIconBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
        
        icon_ = value;
        onChanged();
        return this;
      }

      private java.lang.Object desc_ = "";
      /**
       * <pre>
       *任务描述
       * </pre>
       *
       * <code>string desc = 11;</code>
       * @return The desc.
       */
      public java.lang.String getDesc() {
        java.lang.Object ref = desc_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          desc_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       *任务描述
       * </pre>
       *
       * <code>string desc = 11;</code>
       * @return The bytes for desc.
       */
      public com.google.protobuf.ByteString
          getDescBytes() {
        java.lang.Object ref = desc_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          desc_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       *任务描述
       * </pre>
       *
       * <code>string desc = 11;</code>
       * @param value The desc to set.
       * @return This builder for chaining.
       */
      public Builder setDesc(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  
        desc_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *任务描述
       * </pre>
       *
       * <code>string desc = 11;</code>
       * @return This builder for chaining.
       */
      public Builder clearDesc() {
        
        desc_ = getDefaultInstance().getDesc();
        onChanged();
        return this;
      }
      /**
       * <pre>
       *任务描述
       * </pre>
       *
       * <code>string desc = 11;</code>
       * @param value The bytes for desc to set.
       * @return This builder for chaining.
       */
      public Builder setDescBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
        
        desc_ = value;
        onChanged();
        return this;
      }

      private java.lang.Object uniqueId_ = "";
      /**
       * <pre>
       *任务唯一id
       * </pre>
       *
       * <code>string uniqueId = 12;</code>
       * @return The uniqueId.
       */
      public java.lang.String getUniqueId() {
        java.lang.Object ref = uniqueId_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          uniqueId_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       *任务唯一id
       * </pre>
       *
       * <code>string uniqueId = 12;</code>
       * @return The bytes for uniqueId.
       */
      public com.google.protobuf.ByteString
          getUniqueIdBytes() {
        java.lang.Object ref = uniqueId_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          uniqueId_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       *任务唯一id
       * </pre>
       *
       * <code>string uniqueId = 12;</code>
       * @param value The uniqueId to set.
       * @return This builder for chaining.
       */
      public Builder setUniqueId(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  
        uniqueId_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *任务唯一id
       * </pre>
       *
       * <code>string uniqueId = 12;</code>
       * @return This builder for chaining.
       */
      public Builder clearUniqueId() {
        
        uniqueId_ = getDefaultInstance().getUniqueId();
        onChanged();
        return this;
      }
      /**
       * <pre>
       *任务唯一id
       * </pre>
       *
       * <code>string uniqueId = 12;</code>
       * @param value The bytes for uniqueId to set.
       * @return This builder for chaining.
       */
      public Builder setUniqueIdBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
        
        uniqueId_ = value;
        onChanged();
        return this;
      }

      private com.proto.QuestMessage.QuestTarget questTarget_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.proto.QuestMessage.QuestTarget, com.proto.QuestMessage.QuestTarget.Builder, com.proto.QuestMessage.QuestTargetOrBuilder> questTargetBuilder_;
      /**
       * <pre>
       *任务目标
       * </pre>
       *
       * <code>.ProtoMessage.QuestTarget questTarget = 13;</code>
       * @return Whether the questTarget field is set.
       */
      public boolean hasQuestTarget() {
        return questTargetBuilder_ != null || questTarget_ != null;
      }
      /**
       * <pre>
       *任务目标
       * </pre>
       *
       * <code>.ProtoMessage.QuestTarget questTarget = 13;</code>
       * @return The questTarget.
       */
      public com.proto.QuestMessage.QuestTarget getQuestTarget() {
        if (questTargetBuilder_ == null) {
          return questTarget_ == null ? com.proto.QuestMessage.QuestTarget.getDefaultInstance() : questTarget_;
        } else {
          return questTargetBuilder_.getMessage();
        }
      }
      /**
       * <pre>
       *任务目标
       * </pre>
       *
       * <code>.ProtoMessage.QuestTarget questTarget = 13;</code>
       */
      public Builder setQuestTarget(com.proto.QuestMessage.QuestTarget value) {
        if (questTargetBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          questTarget_ = value;
          onChanged();
        } else {
          questTargetBuilder_.setMessage(value);
        }

        return this;
      }
      /**
       * <pre>
       *任务目标
       * </pre>
       *
       * <code>.ProtoMessage.QuestTarget questTarget = 13;</code>
       */
      public Builder setQuestTarget(
          com.proto.QuestMessage.QuestTarget.Builder builderForValue) {
        if (questTargetBuilder_ == null) {
          questTarget_ = builderForValue.build();
          onChanged();
        } else {
          questTargetBuilder_.setMessage(builderForValue.build());
        }

        return this;
      }
      /**
       * <pre>
       *任务目标
       * </pre>
       *
       * <code>.ProtoMessage.QuestTarget questTarget = 13;</code>
       */
      public Builder mergeQuestTarget(com.proto.QuestMessage.QuestTarget value) {
        if (questTargetBuilder_ == null) {
          if (questTarget_ != null) {
            questTarget_ =
              com.proto.QuestMessage.QuestTarget.newBuilder(questTarget_).mergeFrom(value).buildPartial();
          } else {
            questTarget_ = value;
          }
          onChanged();
        } else {
          questTargetBuilder_.mergeFrom(value);
        }

        return this;
      }
      /**
       * <pre>
       *任务目标
       * </pre>
       *
       * <code>.ProtoMessage.QuestTarget questTarget = 13;</code>
       */
      public Builder clearQuestTarget() {
        if (questTargetBuilder_ == null) {
          questTarget_ = null;
          onChanged();
        } else {
          questTarget_ = null;
          questTargetBuilder_ = null;
        }

        return this;
      }
      /**
       * <pre>
       *任务目标
       * </pre>
       *
       * <code>.ProtoMessage.QuestTarget questTarget = 13;</code>
       */
      public com.proto.QuestMessage.QuestTarget.Builder getQuestTargetBuilder() {
        
        onChanged();
        return getQuestTargetFieldBuilder().getBuilder();
      }
      /**
       * <pre>
       *任务目标
       * </pre>
       *
       * <code>.ProtoMessage.QuestTarget questTarget = 13;</code>
       */
      public com.proto.QuestMessage.QuestTargetOrBuilder getQuestTargetOrBuilder() {
        if (questTargetBuilder_ != null) {
          return questTargetBuilder_.getMessageOrBuilder();
        } else {
          return questTarget_ == null ?
              com.proto.QuestMessage.QuestTarget.getDefaultInstance() : questTarget_;
        }
      }
      /**
       * <pre>
       *任务目标
       * </pre>
       *
       * <code>.ProtoMessage.QuestTarget questTarget = 13;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.proto.QuestMessage.QuestTarget, com.proto.QuestMessage.QuestTarget.Builder, com.proto.QuestMessage.QuestTargetOrBuilder> 
          getQuestTargetFieldBuilder() {
        if (questTargetBuilder_ == null) {
          questTargetBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.proto.QuestMessage.QuestTarget, com.proto.QuestMessage.QuestTarget.Builder, com.proto.QuestMessage.QuestTargetOrBuilder>(
                  getQuestTarget(),
                  getParentForChildren(),
                  isClean());
          questTarget_ = null;
        }
        return questTargetBuilder_;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:ProtoMessage.QuestInfo)
    }

    // @@protoc_insertion_point(class_scope:ProtoMessage.QuestInfo)
    private static final com.proto.QuestMessage.QuestInfo DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.proto.QuestMessage.QuestInfo();
    }

    public static com.proto.QuestMessage.QuestInfo getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<QuestInfo>
        PARSER = new com.google.protobuf.AbstractParser<QuestInfo>() {
      @java.lang.Override
      public QuestInfo parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new QuestInfo(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<QuestInfo> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<QuestInfo> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.proto.QuestMessage.QuestInfo getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface QuestTargetOrBuilder extends
      // @@protoc_insertion_point(interface_extends:ProtoMessage.QuestTarget)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     *频道id
     * </pre>
     *
     * <code>int32 channelId = 1;</code>
     * @return The channelId.
     */
    int getChannelId();
  }
  /**
   * Protobuf type {@code ProtoMessage.QuestTarget}
   */
  public static final class QuestTarget extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:ProtoMessage.QuestTarget)
      QuestTargetOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use QuestTarget.newBuilder() to construct.
    private QuestTarget(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private QuestTarget() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new QuestTarget();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private QuestTarget(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {

              channelId_ = input.readInt32();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.proto.QuestMessage.internal_static_ProtoMessage_QuestTarget_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.proto.QuestMessage.internal_static_ProtoMessage_QuestTarget_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.proto.QuestMessage.QuestTarget.class, com.proto.QuestMessage.QuestTarget.Builder.class);
    }

    public static final int CHANNELID_FIELD_NUMBER = 1;
    private int channelId_;
    /**
     * <pre>
     *频道id
     * </pre>
     *
     * <code>int32 channelId = 1;</code>
     * @return The channelId.
     */
    @java.lang.Override
    public int getChannelId() {
      return channelId_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (channelId_ != 0) {
        output.writeInt32(1, channelId_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (channelId_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, channelId_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.proto.QuestMessage.QuestTarget)) {
        return super.equals(obj);
      }
      com.proto.QuestMessage.QuestTarget other = (com.proto.QuestMessage.QuestTarget) obj;

      if (getChannelId()
          != other.getChannelId()) return false;
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + CHANNELID_FIELD_NUMBER;
      hash = (53 * hash) + getChannelId();
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.proto.QuestMessage.QuestTarget parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.proto.QuestMessage.QuestTarget parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.proto.QuestMessage.QuestTarget parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.proto.QuestMessage.QuestTarget parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.proto.QuestMessage.QuestTarget parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.proto.QuestMessage.QuestTarget parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.proto.QuestMessage.QuestTarget parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.proto.QuestMessage.QuestTarget parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.proto.QuestMessage.QuestTarget parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.proto.QuestMessage.QuestTarget parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.proto.QuestMessage.QuestTarget parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.proto.QuestMessage.QuestTarget parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.proto.QuestMessage.QuestTarget prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code ProtoMessage.QuestTarget}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:ProtoMessage.QuestTarget)
        com.proto.QuestMessage.QuestTargetOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.proto.QuestMessage.internal_static_ProtoMessage_QuestTarget_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.proto.QuestMessage.internal_static_ProtoMessage_QuestTarget_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.proto.QuestMessage.QuestTarget.class, com.proto.QuestMessage.QuestTarget.Builder.class);
      }

      // Construct using com.proto.QuestMessage.QuestTarget.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        channelId_ = 0;

        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.proto.QuestMessage.internal_static_ProtoMessage_QuestTarget_descriptor;
      }

      @java.lang.Override
      public com.proto.QuestMessage.QuestTarget getDefaultInstanceForType() {
        return com.proto.QuestMessage.QuestTarget.getDefaultInstance();
      }

      @java.lang.Override
      public com.proto.QuestMessage.QuestTarget build() {
        com.proto.QuestMessage.QuestTarget result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.proto.QuestMessage.QuestTarget buildPartial() {
        com.proto.QuestMessage.QuestTarget result = new com.proto.QuestMessage.QuestTarget(this);
        result.channelId_ = channelId_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.proto.QuestMessage.QuestTarget) {
          return mergeFrom((com.proto.QuestMessage.QuestTarget)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.proto.QuestMessage.QuestTarget other) {
        if (other == com.proto.QuestMessage.QuestTarget.getDefaultInstance()) return this;
        if (other.getChannelId() != 0) {
          setChannelId(other.getChannelId());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.proto.QuestMessage.QuestTarget parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.proto.QuestMessage.QuestTarget) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }

      private int channelId_ ;
      /**
       * <pre>
       *频道id
       * </pre>
       *
       * <code>int32 channelId = 1;</code>
       * @return The channelId.
       */
      @java.lang.Override
      public int getChannelId() {
        return channelId_;
      }
      /**
       * <pre>
       *频道id
       * </pre>
       *
       * <code>int32 channelId = 1;</code>
       * @param value The channelId to set.
       * @return This builder for chaining.
       */
      public Builder setChannelId(int value) {
        
        channelId_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *频道id
       * </pre>
       *
       * <code>int32 channelId = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearChannelId() {
        
        channelId_ = 0;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:ProtoMessage.QuestTarget)
    }

    // @@protoc_insertion_point(class_scope:ProtoMessage.QuestTarget)
    private static final com.proto.QuestMessage.QuestTarget DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.proto.QuestMessage.QuestTarget();
    }

    public static com.proto.QuestMessage.QuestTarget getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<QuestTarget>
        PARSER = new com.google.protobuf.AbstractParser<QuestTarget>() {
      @java.lang.Override
      public QuestTarget parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new QuestTarget(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<QuestTarget> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<QuestTarget> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.proto.QuestMessage.QuestTarget getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface ConditionInfoOrBuilder extends
      // @@protoc_insertion_point(interface_extends:ProtoMessage.ConditionInfo)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     *总进度
     * </pre>
     *
     * <code>int32 totalProgressive = 1;</code>
     * @return The totalProgressive.
     */
    int getTotalProgressive();

    /**
     * <pre>
     *条件1
     * </pre>
     *
     * <code>int32 param1 = 2;</code>
     * @return The param1.
     */
    int getParam1();

    /**
     * <pre>
     *条件2
     * </pre>
     *
     * <code>int32 param2 = 3;</code>
     * @return The param2.
     */
    int getParam2();

    /**
     * <pre>
     *条件3
     * </pre>
     *
     * <code>repeated int32 param3 = 4;</code>
     * @return A list containing the param3.
     */
    java.util.List<java.lang.Integer> getParam3List();
    /**
     * <pre>
     *条件3
     * </pre>
     *
     * <code>repeated int32 param3 = 4;</code>
     * @return The count of param3.
     */
    int getParam3Count();
    /**
     * <pre>
     *条件3
     * </pre>
     *
     * <code>repeated int32 param3 = 4;</code>
     * @param index The index of the element to return.
     * @return The param3 at the given index.
     */
    int getParam3(int index);
  }
  /**
   * Protobuf type {@code ProtoMessage.ConditionInfo}
   */
  public static final class ConditionInfo extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:ProtoMessage.ConditionInfo)
      ConditionInfoOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use ConditionInfo.newBuilder() to construct.
    private ConditionInfo(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private ConditionInfo() {
      param3_ = emptyIntList();
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new ConditionInfo();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private ConditionInfo(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {

              totalProgressive_ = input.readInt32();
              break;
            }
            case 16: {

              param1_ = input.readInt32();
              break;
            }
            case 24: {

              param2_ = input.readInt32();
              break;
            }
            case 32: {
              if (!((mutable_bitField0_ & 0x00000001) != 0)) {
                param3_ = newIntList();
                mutable_bitField0_ |= 0x00000001;
              }
              param3_.addInt(input.readInt32());
              break;
            }
            case 34: {
              int length = input.readRawVarint32();
              int limit = input.pushLimit(length);
              if (!((mutable_bitField0_ & 0x00000001) != 0) && input.getBytesUntilLimit() > 0) {
                param3_ = newIntList();
                mutable_bitField0_ |= 0x00000001;
              }
              while (input.getBytesUntilLimit() > 0) {
                param3_.addInt(input.readInt32());
              }
              input.popLimit(limit);
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        if (((mutable_bitField0_ & 0x00000001) != 0)) {
          param3_.makeImmutable(); // C
        }
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.proto.QuestMessage.internal_static_ProtoMessage_ConditionInfo_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.proto.QuestMessage.internal_static_ProtoMessage_ConditionInfo_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.proto.QuestMessage.ConditionInfo.class, com.proto.QuestMessage.ConditionInfo.Builder.class);
    }

    public static final int TOTALPROGRESSIVE_FIELD_NUMBER = 1;
    private int totalProgressive_;
    /**
     * <pre>
     *总进度
     * </pre>
     *
     * <code>int32 totalProgressive = 1;</code>
     * @return The totalProgressive.
     */
    @java.lang.Override
    public int getTotalProgressive() {
      return totalProgressive_;
    }

    public static final int PARAM1_FIELD_NUMBER = 2;
    private int param1_;
    /**
     * <pre>
     *条件1
     * </pre>
     *
     * <code>int32 param1 = 2;</code>
     * @return The param1.
     */
    @java.lang.Override
    public int getParam1() {
      return param1_;
    }

    public static final int PARAM2_FIELD_NUMBER = 3;
    private int param2_;
    /**
     * <pre>
     *条件2
     * </pre>
     *
     * <code>int32 param2 = 3;</code>
     * @return The param2.
     */
    @java.lang.Override
    public int getParam2() {
      return param2_;
    }

    public static final int PARAM3_FIELD_NUMBER = 4;
    private com.google.protobuf.Internal.IntList param3_;
    /**
     * <pre>
     *条件3
     * </pre>
     *
     * <code>repeated int32 param3 = 4;</code>
     * @return A list containing the param3.
     */
    @java.lang.Override
    public java.util.List<java.lang.Integer>
        getParam3List() {
      return param3_;
    }
    /**
     * <pre>
     *条件3
     * </pre>
     *
     * <code>repeated int32 param3 = 4;</code>
     * @return The count of param3.
     */
    public int getParam3Count() {
      return param3_.size();
    }
    /**
     * <pre>
     *条件3
     * </pre>
     *
     * <code>repeated int32 param3 = 4;</code>
     * @param index The index of the element to return.
     * @return The param3 at the given index.
     */
    public int getParam3(int index) {
      return param3_.getInt(index);
    }
    private int param3MemoizedSerializedSize = -1;

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      getSerializedSize();
      if (totalProgressive_ != 0) {
        output.writeInt32(1, totalProgressive_);
      }
      if (param1_ != 0) {
        output.writeInt32(2, param1_);
      }
      if (param2_ != 0) {
        output.writeInt32(3, param2_);
      }
      if (getParam3List().size() > 0) {
        output.writeUInt32NoTag(34);
        output.writeUInt32NoTag(param3MemoizedSerializedSize);
      }
      for (int i = 0; i < param3_.size(); i++) {
        output.writeInt32NoTag(param3_.getInt(i));
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (totalProgressive_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, totalProgressive_);
      }
      if (param1_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(2, param1_);
      }
      if (param2_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(3, param2_);
      }
      {
        int dataSize = 0;
        for (int i = 0; i < param3_.size(); i++) {
          dataSize += com.google.protobuf.CodedOutputStream
            .computeInt32SizeNoTag(param3_.getInt(i));
        }
        size += dataSize;
        if (!getParam3List().isEmpty()) {
          size += 1;
          size += com.google.protobuf.CodedOutputStream
              .computeInt32SizeNoTag(dataSize);
        }
        param3MemoizedSerializedSize = dataSize;
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.proto.QuestMessage.ConditionInfo)) {
        return super.equals(obj);
      }
      com.proto.QuestMessage.ConditionInfo other = (com.proto.QuestMessage.ConditionInfo) obj;

      if (getTotalProgressive()
          != other.getTotalProgressive()) return false;
      if (getParam1()
          != other.getParam1()) return false;
      if (getParam2()
          != other.getParam2()) return false;
      if (!getParam3List()
          .equals(other.getParam3List())) return false;
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + TOTALPROGRESSIVE_FIELD_NUMBER;
      hash = (53 * hash) + getTotalProgressive();
      hash = (37 * hash) + PARAM1_FIELD_NUMBER;
      hash = (53 * hash) + getParam1();
      hash = (37 * hash) + PARAM2_FIELD_NUMBER;
      hash = (53 * hash) + getParam2();
      if (getParam3Count() > 0) {
        hash = (37 * hash) + PARAM3_FIELD_NUMBER;
        hash = (53 * hash) + getParam3List().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.proto.QuestMessage.ConditionInfo parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.proto.QuestMessage.ConditionInfo parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.proto.QuestMessage.ConditionInfo parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.proto.QuestMessage.ConditionInfo parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.proto.QuestMessage.ConditionInfo parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.proto.QuestMessage.ConditionInfo parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.proto.QuestMessage.ConditionInfo parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.proto.QuestMessage.ConditionInfo parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.proto.QuestMessage.ConditionInfo parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.proto.QuestMessage.ConditionInfo parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.proto.QuestMessage.ConditionInfo parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.proto.QuestMessage.ConditionInfo parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.proto.QuestMessage.ConditionInfo prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code ProtoMessage.ConditionInfo}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:ProtoMessage.ConditionInfo)
        com.proto.QuestMessage.ConditionInfoOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.proto.QuestMessage.internal_static_ProtoMessage_ConditionInfo_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.proto.QuestMessage.internal_static_ProtoMessage_ConditionInfo_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.proto.QuestMessage.ConditionInfo.class, com.proto.QuestMessage.ConditionInfo.Builder.class);
      }

      // Construct using com.proto.QuestMessage.ConditionInfo.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        totalProgressive_ = 0;

        param1_ = 0;

        param2_ = 0;

        param3_ = emptyIntList();
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.proto.QuestMessage.internal_static_ProtoMessage_ConditionInfo_descriptor;
      }

      @java.lang.Override
      public com.proto.QuestMessage.ConditionInfo getDefaultInstanceForType() {
        return com.proto.QuestMessage.ConditionInfo.getDefaultInstance();
      }

      @java.lang.Override
      public com.proto.QuestMessage.ConditionInfo build() {
        com.proto.QuestMessage.ConditionInfo result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.proto.QuestMessage.ConditionInfo buildPartial() {
        com.proto.QuestMessage.ConditionInfo result = new com.proto.QuestMessage.ConditionInfo(this);
        int from_bitField0_ = bitField0_;
        result.totalProgressive_ = totalProgressive_;
        result.param1_ = param1_;
        result.param2_ = param2_;
        if (((bitField0_ & 0x00000001) != 0)) {
          param3_.makeImmutable();
          bitField0_ = (bitField0_ & ~0x00000001);
        }
        result.param3_ = param3_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.proto.QuestMessage.ConditionInfo) {
          return mergeFrom((com.proto.QuestMessage.ConditionInfo)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.proto.QuestMessage.ConditionInfo other) {
        if (other == com.proto.QuestMessage.ConditionInfo.getDefaultInstance()) return this;
        if (other.getTotalProgressive() != 0) {
          setTotalProgressive(other.getTotalProgressive());
        }
        if (other.getParam1() != 0) {
          setParam1(other.getParam1());
        }
        if (other.getParam2() != 0) {
          setParam2(other.getParam2());
        }
        if (!other.param3_.isEmpty()) {
          if (param3_.isEmpty()) {
            param3_ = other.param3_;
            bitField0_ = (bitField0_ & ~0x00000001);
          } else {
            ensureParam3IsMutable();
            param3_.addAll(other.param3_);
          }
          onChanged();
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.proto.QuestMessage.ConditionInfo parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.proto.QuestMessage.ConditionInfo) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private int totalProgressive_ ;
      /**
       * <pre>
       *总进度
       * </pre>
       *
       * <code>int32 totalProgressive = 1;</code>
       * @return The totalProgressive.
       */
      @java.lang.Override
      public int getTotalProgressive() {
        return totalProgressive_;
      }
      /**
       * <pre>
       *总进度
       * </pre>
       *
       * <code>int32 totalProgressive = 1;</code>
       * @param value The totalProgressive to set.
       * @return This builder for chaining.
       */
      public Builder setTotalProgressive(int value) {
        
        totalProgressive_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *总进度
       * </pre>
       *
       * <code>int32 totalProgressive = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearTotalProgressive() {
        
        totalProgressive_ = 0;
        onChanged();
        return this;
      }

      private int param1_ ;
      /**
       * <pre>
       *条件1
       * </pre>
       *
       * <code>int32 param1 = 2;</code>
       * @return The param1.
       */
      @java.lang.Override
      public int getParam1() {
        return param1_;
      }
      /**
       * <pre>
       *条件1
       * </pre>
       *
       * <code>int32 param1 = 2;</code>
       * @param value The param1 to set.
       * @return This builder for chaining.
       */
      public Builder setParam1(int value) {
        
        param1_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *条件1
       * </pre>
       *
       * <code>int32 param1 = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearParam1() {
        
        param1_ = 0;
        onChanged();
        return this;
      }

      private int param2_ ;
      /**
       * <pre>
       *条件2
       * </pre>
       *
       * <code>int32 param2 = 3;</code>
       * @return The param2.
       */
      @java.lang.Override
      public int getParam2() {
        return param2_;
      }
      /**
       * <pre>
       *条件2
       * </pre>
       *
       * <code>int32 param2 = 3;</code>
       * @param value The param2 to set.
       * @return This builder for chaining.
       */
      public Builder setParam2(int value) {
        
        param2_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *条件2
       * </pre>
       *
       * <code>int32 param2 = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearParam2() {
        
        param2_ = 0;
        onChanged();
        return this;
      }

      private com.google.protobuf.Internal.IntList param3_ = emptyIntList();
      private void ensureParam3IsMutable() {
        if (!((bitField0_ & 0x00000001) != 0)) {
          param3_ = mutableCopy(param3_);
          bitField0_ |= 0x00000001;
         }
      }
      /**
       * <pre>
       *条件3
       * </pre>
       *
       * <code>repeated int32 param3 = 4;</code>
       * @return A list containing the param3.
       */
      public java.util.List<java.lang.Integer>
          getParam3List() {
        return ((bitField0_ & 0x00000001) != 0) ?
                 java.util.Collections.unmodifiableList(param3_) : param3_;
      }
      /**
       * <pre>
       *条件3
       * </pre>
       *
       * <code>repeated int32 param3 = 4;</code>
       * @return The count of param3.
       */
      public int getParam3Count() {
        return param3_.size();
      }
      /**
       * <pre>
       *条件3
       * </pre>
       *
       * <code>repeated int32 param3 = 4;</code>
       * @param index The index of the element to return.
       * @return The param3 at the given index.
       */
      public int getParam3(int index) {
        return param3_.getInt(index);
      }
      /**
       * <pre>
       *条件3
       * </pre>
       *
       * <code>repeated int32 param3 = 4;</code>
       * @param index The index to set the value at.
       * @param value The param3 to set.
       * @return This builder for chaining.
       */
      public Builder setParam3(
          int index, int value) {
        ensureParam3IsMutable();
        param3_.setInt(index, value);
        onChanged();
        return this;
      }
      /**
       * <pre>
       *条件3
       * </pre>
       *
       * <code>repeated int32 param3 = 4;</code>
       * @param value The param3 to add.
       * @return This builder for chaining.
       */
      public Builder addParam3(int value) {
        ensureParam3IsMutable();
        param3_.addInt(value);
        onChanged();
        return this;
      }
      /**
       * <pre>
       *条件3
       * </pre>
       *
       * <code>repeated int32 param3 = 4;</code>
       * @param values The param3 to add.
       * @return This builder for chaining.
       */
      public Builder addAllParam3(
          java.lang.Iterable<? extends java.lang.Integer> values) {
        ensureParam3IsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, param3_);
        onChanged();
        return this;
      }
      /**
       * <pre>
       *条件3
       * </pre>
       *
       * <code>repeated int32 param3 = 4;</code>
       * @return This builder for chaining.
       */
      public Builder clearParam3() {
        param3_ = emptyIntList();
        bitField0_ = (bitField0_ & ~0x00000001);
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:ProtoMessage.ConditionInfo)
    }

    // @@protoc_insertion_point(class_scope:ProtoMessage.ConditionInfo)
    private static final com.proto.QuestMessage.ConditionInfo DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.proto.QuestMessage.ConditionInfo();
    }

    public static com.proto.QuestMessage.ConditionInfo getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<ConditionInfo>
        PARSER = new com.google.protobuf.AbstractParser<ConditionInfo>() {
      @java.lang.Override
      public ConditionInfo parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new ConditionInfo(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<ConditionInfo> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<ConditionInfo> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.proto.QuestMessage.ConditionInfo getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface ReqReceiveQuestRewardMessageOrBuilder extends
      // @@protoc_insertion_point(interface_extends:ProtoMessage.ReqReceiveQuestRewardMessage)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>int32 msgID = 1;</code>
     * @return The msgID.
     */
    int getMsgID();

    /**
     * <pre>
     *任务id
     * </pre>
     *
     * <code>int32 questId = 2;</code>
     * @return The questId.
     */
    int getQuestId();

    /**
     * <pre>
     *任务唯一id (此id在历史记录使用)
     * </pre>
     *
     * <code>string uniqueId = 3;</code>
     * @return The uniqueId.
     */
    java.lang.String getUniqueId();
    /**
     * <pre>
     *任务唯一id (此id在历史记录使用)
     * </pre>
     *
     * <code>string uniqueId = 3;</code>
     * @return The bytes for uniqueId.
     */
    com.google.protobuf.ByteString
        getUniqueIdBytes();
  }
  /**
   * <pre>
   *请求领取任务奖励
   * </pre>
   *
   * Protobuf type {@code ProtoMessage.ReqReceiveQuestRewardMessage}
   */
  public static final class ReqReceiveQuestRewardMessage extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:ProtoMessage.ReqReceiveQuestRewardMessage)
      ReqReceiveQuestRewardMessageOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use ReqReceiveQuestRewardMessage.newBuilder() to construct.
    private ReqReceiveQuestRewardMessage(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private ReqReceiveQuestRewardMessage() {
      uniqueId_ = "";
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new ReqReceiveQuestRewardMessage();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private ReqReceiveQuestRewardMessage(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {

              msgID_ = input.readInt32();
              break;
            }
            case 16: {

              questId_ = input.readInt32();
              break;
            }
            case 26: {
              java.lang.String s = input.readStringRequireUtf8();

              uniqueId_ = s;
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.proto.QuestMessage.internal_static_ProtoMessage_ReqReceiveQuestRewardMessage_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.proto.QuestMessage.internal_static_ProtoMessage_ReqReceiveQuestRewardMessage_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.proto.QuestMessage.ReqReceiveQuestRewardMessage.class, com.proto.QuestMessage.ReqReceiveQuestRewardMessage.Builder.class);
    }

    public static final int MSGID_FIELD_NUMBER = 1;
    private int msgID_;
    /**
     * <code>int32 msgID = 1;</code>
     * @return The msgID.
     */
    @java.lang.Override
    public int getMsgID() {
      return msgID_;
    }

    public static final int QUESTID_FIELD_NUMBER = 2;
    private int questId_;
    /**
     * <pre>
     *任务id
     * </pre>
     *
     * <code>int32 questId = 2;</code>
     * @return The questId.
     */
    @java.lang.Override
    public int getQuestId() {
      return questId_;
    }

    public static final int UNIQUEID_FIELD_NUMBER = 3;
    private volatile java.lang.Object uniqueId_;
    /**
     * <pre>
     *任务唯一id (此id在历史记录使用)
     * </pre>
     *
     * <code>string uniqueId = 3;</code>
     * @return The uniqueId.
     */
    @java.lang.Override
    public java.lang.String getUniqueId() {
      java.lang.Object ref = uniqueId_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        uniqueId_ = s;
        return s;
      }
    }
    /**
     * <pre>
     *任务唯一id (此id在历史记录使用)
     * </pre>
     *
     * <code>string uniqueId = 3;</code>
     * @return The bytes for uniqueId.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getUniqueIdBytes() {
      java.lang.Object ref = uniqueId_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        uniqueId_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (msgID_ != 0) {
        output.writeInt32(1, msgID_);
      }
      if (questId_ != 0) {
        output.writeInt32(2, questId_);
      }
      if (!getUniqueIdBytes().isEmpty()) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 3, uniqueId_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (msgID_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, msgID_);
      }
      if (questId_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(2, questId_);
      }
      if (!getUniqueIdBytes().isEmpty()) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(3, uniqueId_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.proto.QuestMessage.ReqReceiveQuestRewardMessage)) {
        return super.equals(obj);
      }
      com.proto.QuestMessage.ReqReceiveQuestRewardMessage other = (com.proto.QuestMessage.ReqReceiveQuestRewardMessage) obj;

      if (getMsgID()
          != other.getMsgID()) return false;
      if (getQuestId()
          != other.getQuestId()) return false;
      if (!getUniqueId()
          .equals(other.getUniqueId())) return false;
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + MSGID_FIELD_NUMBER;
      hash = (53 * hash) + getMsgID();
      hash = (37 * hash) + QUESTID_FIELD_NUMBER;
      hash = (53 * hash) + getQuestId();
      hash = (37 * hash) + UNIQUEID_FIELD_NUMBER;
      hash = (53 * hash) + getUniqueId().hashCode();
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.proto.QuestMessage.ReqReceiveQuestRewardMessage parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.proto.QuestMessage.ReqReceiveQuestRewardMessage parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.proto.QuestMessage.ReqReceiveQuestRewardMessage parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.proto.QuestMessage.ReqReceiveQuestRewardMessage parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.proto.QuestMessage.ReqReceiveQuestRewardMessage parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.proto.QuestMessage.ReqReceiveQuestRewardMessage parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.proto.QuestMessage.ReqReceiveQuestRewardMessage parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.proto.QuestMessage.ReqReceiveQuestRewardMessage parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.proto.QuestMessage.ReqReceiveQuestRewardMessage parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.proto.QuestMessage.ReqReceiveQuestRewardMessage parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.proto.QuestMessage.ReqReceiveQuestRewardMessage parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.proto.QuestMessage.ReqReceiveQuestRewardMessage parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.proto.QuestMessage.ReqReceiveQuestRewardMessage prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     *请求领取任务奖励
     * </pre>
     *
     * Protobuf type {@code ProtoMessage.ReqReceiveQuestRewardMessage}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:ProtoMessage.ReqReceiveQuestRewardMessage)
        com.proto.QuestMessage.ReqReceiveQuestRewardMessageOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.proto.QuestMessage.internal_static_ProtoMessage_ReqReceiveQuestRewardMessage_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.proto.QuestMessage.internal_static_ProtoMessage_ReqReceiveQuestRewardMessage_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.proto.QuestMessage.ReqReceiveQuestRewardMessage.class, com.proto.QuestMessage.ReqReceiveQuestRewardMessage.Builder.class);
      }

      // Construct using com.proto.QuestMessage.ReqReceiveQuestRewardMessage.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        msgID_ = 0;

        questId_ = 0;

        uniqueId_ = "";

        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.proto.QuestMessage.internal_static_ProtoMessage_ReqReceiveQuestRewardMessage_descriptor;
      }

      @java.lang.Override
      public com.proto.QuestMessage.ReqReceiveQuestRewardMessage getDefaultInstanceForType() {
        return com.proto.QuestMessage.ReqReceiveQuestRewardMessage.getDefaultInstance();
      }

      @java.lang.Override
      public com.proto.QuestMessage.ReqReceiveQuestRewardMessage build() {
        com.proto.QuestMessage.ReqReceiveQuestRewardMessage result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.proto.QuestMessage.ReqReceiveQuestRewardMessage buildPartial() {
        com.proto.QuestMessage.ReqReceiveQuestRewardMessage result = new com.proto.QuestMessage.ReqReceiveQuestRewardMessage(this);
        result.msgID_ = msgID_;
        result.questId_ = questId_;
        result.uniqueId_ = uniqueId_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.proto.QuestMessage.ReqReceiveQuestRewardMessage) {
          return mergeFrom((com.proto.QuestMessage.ReqReceiveQuestRewardMessage)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.proto.QuestMessage.ReqReceiveQuestRewardMessage other) {
        if (other == com.proto.QuestMessage.ReqReceiveQuestRewardMessage.getDefaultInstance()) return this;
        if (other.getMsgID() != 0) {
          setMsgID(other.getMsgID());
        }
        if (other.getQuestId() != 0) {
          setQuestId(other.getQuestId());
        }
        if (!other.getUniqueId().isEmpty()) {
          uniqueId_ = other.uniqueId_;
          onChanged();
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.proto.QuestMessage.ReqReceiveQuestRewardMessage parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.proto.QuestMessage.ReqReceiveQuestRewardMessage) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }

      private int msgID_ ;
      /**
       * <code>int32 msgID = 1;</code>
       * @return The msgID.
       */
      @java.lang.Override
      public int getMsgID() {
        return msgID_;
      }
      /**
       * <code>int32 msgID = 1;</code>
       * @param value The msgID to set.
       * @return This builder for chaining.
       */
      public Builder setMsgID(int value) {
        
        msgID_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>int32 msgID = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearMsgID() {
        
        msgID_ = 0;
        onChanged();
        return this;
      }

      private int questId_ ;
      /**
       * <pre>
       *任务id
       * </pre>
       *
       * <code>int32 questId = 2;</code>
       * @return The questId.
       */
      @java.lang.Override
      public int getQuestId() {
        return questId_;
      }
      /**
       * <pre>
       *任务id
       * </pre>
       *
       * <code>int32 questId = 2;</code>
       * @param value The questId to set.
       * @return This builder for chaining.
       */
      public Builder setQuestId(int value) {
        
        questId_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *任务id
       * </pre>
       *
       * <code>int32 questId = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearQuestId() {
        
        questId_ = 0;
        onChanged();
        return this;
      }

      private java.lang.Object uniqueId_ = "";
      /**
       * <pre>
       *任务唯一id (此id在历史记录使用)
       * </pre>
       *
       * <code>string uniqueId = 3;</code>
       * @return The uniqueId.
       */
      public java.lang.String getUniqueId() {
        java.lang.Object ref = uniqueId_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          uniqueId_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       *任务唯一id (此id在历史记录使用)
       * </pre>
       *
       * <code>string uniqueId = 3;</code>
       * @return The bytes for uniqueId.
       */
      public com.google.protobuf.ByteString
          getUniqueIdBytes() {
        java.lang.Object ref = uniqueId_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          uniqueId_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       *任务唯一id (此id在历史记录使用)
       * </pre>
       *
       * <code>string uniqueId = 3;</code>
       * @param value The uniqueId to set.
       * @return This builder for chaining.
       */
      public Builder setUniqueId(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  
        uniqueId_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *任务唯一id (此id在历史记录使用)
       * </pre>
       *
       * <code>string uniqueId = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearUniqueId() {
        
        uniqueId_ = getDefaultInstance().getUniqueId();
        onChanged();
        return this;
      }
      /**
       * <pre>
       *任务唯一id (此id在历史记录使用)
       * </pre>
       *
       * <code>string uniqueId = 3;</code>
       * @param value The bytes for uniqueId to set.
       * @return This builder for chaining.
       */
      public Builder setUniqueIdBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
        
        uniqueId_ = value;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:ProtoMessage.ReqReceiveQuestRewardMessage)
    }

    // @@protoc_insertion_point(class_scope:ProtoMessage.ReqReceiveQuestRewardMessage)
    private static final com.proto.QuestMessage.ReqReceiveQuestRewardMessage DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.proto.QuestMessage.ReqReceiveQuestRewardMessage();
    }

    public static com.proto.QuestMessage.ReqReceiveQuestRewardMessage getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<ReqReceiveQuestRewardMessage>
        PARSER = new com.google.protobuf.AbstractParser<ReqReceiveQuestRewardMessage>() {
      @java.lang.Override
      public ReqReceiveQuestRewardMessage parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new ReqReceiveQuestRewardMessage(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<ReqReceiveQuestRewardMessage> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<ReqReceiveQuestRewardMessage> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.proto.QuestMessage.ReqReceiveQuestRewardMessage getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface ResReceiveQuestRewardMessageOrBuilder extends
      // @@protoc_insertion_point(interface_extends:ProtoMessage.ResReceiveQuestRewardMessage)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>int32 msgID = 1;</code>
     * @return The msgID.
     */
    int getMsgID();

    /**
     * <pre>
     *错误码
     * </pre>
     *
     * <code>int32 error = 2;</code>
     * @return The error.
     */
    int getError();

    /**
     * <pre>
     *任务状态 1.接收 2.完成 3.领取
     * </pre>
     *
     * <code>int32 state = 3;</code>
     * @return The state.
     */
    int getState();

    /**
     * <pre>
     *奖励
     * </pre>
     *
     * <code>.ProtoMessage.DItemShow rewards = 4;</code>
     * @return Whether the rewards field is set.
     */
    boolean hasRewards();
    /**
     * <pre>
     *奖励
     * </pre>
     *
     * <code>.ProtoMessage.DItemShow rewards = 4;</code>
     * @return The rewards.
     */
    com.proto.CommonMessage.DItemShow getRewards();
    /**
     * <pre>
     *奖励
     * </pre>
     *
     * <code>.ProtoMessage.DItemShow rewards = 4;</code>
     */
    com.proto.CommonMessage.DItemShowOrBuilder getRewardsOrBuilder();
  }
  /**
   * <pre>
   *返回领取任务奖励
   * </pre>
   *
   * Protobuf type {@code ProtoMessage.ResReceiveQuestRewardMessage}
   */
  public static final class ResReceiveQuestRewardMessage extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:ProtoMessage.ResReceiveQuestRewardMessage)
      ResReceiveQuestRewardMessageOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use ResReceiveQuestRewardMessage.newBuilder() to construct.
    private ResReceiveQuestRewardMessage(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private ResReceiveQuestRewardMessage() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new ResReceiveQuestRewardMessage();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private ResReceiveQuestRewardMessage(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {

              msgID_ = input.readInt32();
              break;
            }
            case 16: {

              error_ = input.readInt32();
              break;
            }
            case 24: {

              state_ = input.readInt32();
              break;
            }
            case 34: {
              com.proto.CommonMessage.DItemShow.Builder subBuilder = null;
              if (rewards_ != null) {
                subBuilder = rewards_.toBuilder();
              }
              rewards_ = input.readMessage(com.proto.CommonMessage.DItemShow.parser(), extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(rewards_);
                rewards_ = subBuilder.buildPartial();
              }

              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.proto.QuestMessage.internal_static_ProtoMessage_ResReceiveQuestRewardMessage_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.proto.QuestMessage.internal_static_ProtoMessage_ResReceiveQuestRewardMessage_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.proto.QuestMessage.ResReceiveQuestRewardMessage.class, com.proto.QuestMessage.ResReceiveQuestRewardMessage.Builder.class);
    }

    public static final int MSGID_FIELD_NUMBER = 1;
    private int msgID_;
    /**
     * <code>int32 msgID = 1;</code>
     * @return The msgID.
     */
    @java.lang.Override
    public int getMsgID() {
      return msgID_;
    }

    public static final int ERROR_FIELD_NUMBER = 2;
    private int error_;
    /**
     * <pre>
     *错误码
     * </pre>
     *
     * <code>int32 error = 2;</code>
     * @return The error.
     */
    @java.lang.Override
    public int getError() {
      return error_;
    }

    public static final int STATE_FIELD_NUMBER = 3;
    private int state_;
    /**
     * <pre>
     *任务状态 1.接收 2.完成 3.领取
     * </pre>
     *
     * <code>int32 state = 3;</code>
     * @return The state.
     */
    @java.lang.Override
    public int getState() {
      return state_;
    }

    public static final int REWARDS_FIELD_NUMBER = 4;
    private com.proto.CommonMessage.DItemShow rewards_;
    /**
     * <pre>
     *奖励
     * </pre>
     *
     * <code>.ProtoMessage.DItemShow rewards = 4;</code>
     * @return Whether the rewards field is set.
     */
    @java.lang.Override
    public boolean hasRewards() {
      return rewards_ != null;
    }
    /**
     * <pre>
     *奖励
     * </pre>
     *
     * <code>.ProtoMessage.DItemShow rewards = 4;</code>
     * @return The rewards.
     */
    @java.lang.Override
    public com.proto.CommonMessage.DItemShow getRewards() {
      return rewards_ == null ? com.proto.CommonMessage.DItemShow.getDefaultInstance() : rewards_;
    }
    /**
     * <pre>
     *奖励
     * </pre>
     *
     * <code>.ProtoMessage.DItemShow rewards = 4;</code>
     */
    @java.lang.Override
    public com.proto.CommonMessage.DItemShowOrBuilder getRewardsOrBuilder() {
      return getRewards();
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (msgID_ != 0) {
        output.writeInt32(1, msgID_);
      }
      if (error_ != 0) {
        output.writeInt32(2, error_);
      }
      if (state_ != 0) {
        output.writeInt32(3, state_);
      }
      if (rewards_ != null) {
        output.writeMessage(4, getRewards());
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (msgID_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, msgID_);
      }
      if (error_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(2, error_);
      }
      if (state_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(3, state_);
      }
      if (rewards_ != null) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(4, getRewards());
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.proto.QuestMessage.ResReceiveQuestRewardMessage)) {
        return super.equals(obj);
      }
      com.proto.QuestMessage.ResReceiveQuestRewardMessage other = (com.proto.QuestMessage.ResReceiveQuestRewardMessage) obj;

      if (getMsgID()
          != other.getMsgID()) return false;
      if (getError()
          != other.getError()) return false;
      if (getState()
          != other.getState()) return false;
      if (hasRewards() != other.hasRewards()) return false;
      if (hasRewards()) {
        if (!getRewards()
            .equals(other.getRewards())) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + MSGID_FIELD_NUMBER;
      hash = (53 * hash) + getMsgID();
      hash = (37 * hash) + ERROR_FIELD_NUMBER;
      hash = (53 * hash) + getError();
      hash = (37 * hash) + STATE_FIELD_NUMBER;
      hash = (53 * hash) + getState();
      if (hasRewards()) {
        hash = (37 * hash) + REWARDS_FIELD_NUMBER;
        hash = (53 * hash) + getRewards().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.proto.QuestMessage.ResReceiveQuestRewardMessage parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.proto.QuestMessage.ResReceiveQuestRewardMessage parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.proto.QuestMessage.ResReceiveQuestRewardMessage parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.proto.QuestMessage.ResReceiveQuestRewardMessage parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.proto.QuestMessage.ResReceiveQuestRewardMessage parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.proto.QuestMessage.ResReceiveQuestRewardMessage parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.proto.QuestMessage.ResReceiveQuestRewardMessage parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.proto.QuestMessage.ResReceiveQuestRewardMessage parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.proto.QuestMessage.ResReceiveQuestRewardMessage parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.proto.QuestMessage.ResReceiveQuestRewardMessage parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.proto.QuestMessage.ResReceiveQuestRewardMessage parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.proto.QuestMessage.ResReceiveQuestRewardMessage parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.proto.QuestMessage.ResReceiveQuestRewardMessage prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     *返回领取任务奖励
     * </pre>
     *
     * Protobuf type {@code ProtoMessage.ResReceiveQuestRewardMessage}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:ProtoMessage.ResReceiveQuestRewardMessage)
        com.proto.QuestMessage.ResReceiveQuestRewardMessageOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.proto.QuestMessage.internal_static_ProtoMessage_ResReceiveQuestRewardMessage_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.proto.QuestMessage.internal_static_ProtoMessage_ResReceiveQuestRewardMessage_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.proto.QuestMessage.ResReceiveQuestRewardMessage.class, com.proto.QuestMessage.ResReceiveQuestRewardMessage.Builder.class);
      }

      // Construct using com.proto.QuestMessage.ResReceiveQuestRewardMessage.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        msgID_ = 0;

        error_ = 0;

        state_ = 0;

        if (rewardsBuilder_ == null) {
          rewards_ = null;
        } else {
          rewards_ = null;
          rewardsBuilder_ = null;
        }
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.proto.QuestMessage.internal_static_ProtoMessage_ResReceiveQuestRewardMessage_descriptor;
      }

      @java.lang.Override
      public com.proto.QuestMessage.ResReceiveQuestRewardMessage getDefaultInstanceForType() {
        return com.proto.QuestMessage.ResReceiveQuestRewardMessage.getDefaultInstance();
      }

      @java.lang.Override
      public com.proto.QuestMessage.ResReceiveQuestRewardMessage build() {
        com.proto.QuestMessage.ResReceiveQuestRewardMessage result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.proto.QuestMessage.ResReceiveQuestRewardMessage buildPartial() {
        com.proto.QuestMessage.ResReceiveQuestRewardMessage result = new com.proto.QuestMessage.ResReceiveQuestRewardMessage(this);
        result.msgID_ = msgID_;
        result.error_ = error_;
        result.state_ = state_;
        if (rewardsBuilder_ == null) {
          result.rewards_ = rewards_;
        } else {
          result.rewards_ = rewardsBuilder_.build();
        }
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.proto.QuestMessage.ResReceiveQuestRewardMessage) {
          return mergeFrom((com.proto.QuestMessage.ResReceiveQuestRewardMessage)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.proto.QuestMessage.ResReceiveQuestRewardMessage other) {
        if (other == com.proto.QuestMessage.ResReceiveQuestRewardMessage.getDefaultInstance()) return this;
        if (other.getMsgID() != 0) {
          setMsgID(other.getMsgID());
        }
        if (other.getError() != 0) {
          setError(other.getError());
        }
        if (other.getState() != 0) {
          setState(other.getState());
        }
        if (other.hasRewards()) {
          mergeRewards(other.getRewards());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.proto.QuestMessage.ResReceiveQuestRewardMessage parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.proto.QuestMessage.ResReceiveQuestRewardMessage) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }

      private int msgID_ ;
      /**
       * <code>int32 msgID = 1;</code>
       * @return The msgID.
       */
      @java.lang.Override
      public int getMsgID() {
        return msgID_;
      }
      /**
       * <code>int32 msgID = 1;</code>
       * @param value The msgID to set.
       * @return This builder for chaining.
       */
      public Builder setMsgID(int value) {
        
        msgID_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>int32 msgID = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearMsgID() {
        
        msgID_ = 0;
        onChanged();
        return this;
      }

      private int error_ ;
      /**
       * <pre>
       *错误码
       * </pre>
       *
       * <code>int32 error = 2;</code>
       * @return The error.
       */
      @java.lang.Override
      public int getError() {
        return error_;
      }
      /**
       * <pre>
       *错误码
       * </pre>
       *
       * <code>int32 error = 2;</code>
       * @param value The error to set.
       * @return This builder for chaining.
       */
      public Builder setError(int value) {
        
        error_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *错误码
       * </pre>
       *
       * <code>int32 error = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearError() {
        
        error_ = 0;
        onChanged();
        return this;
      }

      private int state_ ;
      /**
       * <pre>
       *任务状态 1.接收 2.完成 3.领取
       * </pre>
       *
       * <code>int32 state = 3;</code>
       * @return The state.
       */
      @java.lang.Override
      public int getState() {
        return state_;
      }
      /**
       * <pre>
       *任务状态 1.接收 2.完成 3.领取
       * </pre>
       *
       * <code>int32 state = 3;</code>
       * @param value The state to set.
       * @return This builder for chaining.
       */
      public Builder setState(int value) {
        
        state_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *任务状态 1.接收 2.完成 3.领取
       * </pre>
       *
       * <code>int32 state = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearState() {
        
        state_ = 0;
        onChanged();
        return this;
      }

      private com.proto.CommonMessage.DItemShow rewards_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.proto.CommonMessage.DItemShow, com.proto.CommonMessage.DItemShow.Builder, com.proto.CommonMessage.DItemShowOrBuilder> rewardsBuilder_;
      /**
       * <pre>
       *奖励
       * </pre>
       *
       * <code>.ProtoMessage.DItemShow rewards = 4;</code>
       * @return Whether the rewards field is set.
       */
      public boolean hasRewards() {
        return rewardsBuilder_ != null || rewards_ != null;
      }
      /**
       * <pre>
       *奖励
       * </pre>
       *
       * <code>.ProtoMessage.DItemShow rewards = 4;</code>
       * @return The rewards.
       */
      public com.proto.CommonMessage.DItemShow getRewards() {
        if (rewardsBuilder_ == null) {
          return rewards_ == null ? com.proto.CommonMessage.DItemShow.getDefaultInstance() : rewards_;
        } else {
          return rewardsBuilder_.getMessage();
        }
      }
      /**
       * <pre>
       *奖励
       * </pre>
       *
       * <code>.ProtoMessage.DItemShow rewards = 4;</code>
       */
      public Builder setRewards(com.proto.CommonMessage.DItemShow value) {
        if (rewardsBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          rewards_ = value;
          onChanged();
        } else {
          rewardsBuilder_.setMessage(value);
        }

        return this;
      }
      /**
       * <pre>
       *奖励
       * </pre>
       *
       * <code>.ProtoMessage.DItemShow rewards = 4;</code>
       */
      public Builder setRewards(
          com.proto.CommonMessage.DItemShow.Builder builderForValue) {
        if (rewardsBuilder_ == null) {
          rewards_ = builderForValue.build();
          onChanged();
        } else {
          rewardsBuilder_.setMessage(builderForValue.build());
        }

        return this;
      }
      /**
       * <pre>
       *奖励
       * </pre>
       *
       * <code>.ProtoMessage.DItemShow rewards = 4;</code>
       */
      public Builder mergeRewards(com.proto.CommonMessage.DItemShow value) {
        if (rewardsBuilder_ == null) {
          if (rewards_ != null) {
            rewards_ =
              com.proto.CommonMessage.DItemShow.newBuilder(rewards_).mergeFrom(value).buildPartial();
          } else {
            rewards_ = value;
          }
          onChanged();
        } else {
          rewardsBuilder_.mergeFrom(value);
        }

        return this;
      }
      /**
       * <pre>
       *奖励
       * </pre>
       *
       * <code>.ProtoMessage.DItemShow rewards = 4;</code>
       */
      public Builder clearRewards() {
        if (rewardsBuilder_ == null) {
          rewards_ = null;
          onChanged();
        } else {
          rewards_ = null;
          rewardsBuilder_ = null;
        }

        return this;
      }
      /**
       * <pre>
       *奖励
       * </pre>
       *
       * <code>.ProtoMessage.DItemShow rewards = 4;</code>
       */
      public com.proto.CommonMessage.DItemShow.Builder getRewardsBuilder() {
        
        onChanged();
        return getRewardsFieldBuilder().getBuilder();
      }
      /**
       * <pre>
       *奖励
       * </pre>
       *
       * <code>.ProtoMessage.DItemShow rewards = 4;</code>
       */
      public com.proto.CommonMessage.DItemShowOrBuilder getRewardsOrBuilder() {
        if (rewardsBuilder_ != null) {
          return rewardsBuilder_.getMessageOrBuilder();
        } else {
          return rewards_ == null ?
              com.proto.CommonMessage.DItemShow.getDefaultInstance() : rewards_;
        }
      }
      /**
       * <pre>
       *奖励
       * </pre>
       *
       * <code>.ProtoMessage.DItemShow rewards = 4;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.proto.CommonMessage.DItemShow, com.proto.CommonMessage.DItemShow.Builder, com.proto.CommonMessage.DItemShowOrBuilder> 
          getRewardsFieldBuilder() {
        if (rewardsBuilder_ == null) {
          rewardsBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.proto.CommonMessage.DItemShow, com.proto.CommonMessage.DItemShow.Builder, com.proto.CommonMessage.DItemShowOrBuilder>(
                  getRewards(),
                  getParentForChildren(),
                  isClean());
          rewards_ = null;
        }
        return rewardsBuilder_;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:ProtoMessage.ResReceiveQuestRewardMessage)
    }

    // @@protoc_insertion_point(class_scope:ProtoMessage.ResReceiveQuestRewardMessage)
    private static final com.proto.QuestMessage.ResReceiveQuestRewardMessage DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.proto.QuestMessage.ResReceiveQuestRewardMessage();
    }

    public static com.proto.QuestMessage.ResReceiveQuestRewardMessage getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<ResReceiveQuestRewardMessage>
        PARSER = new com.google.protobuf.AbstractParser<ResReceiveQuestRewardMessage>() {
      @java.lang.Override
      public ResReceiveQuestRewardMessage parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new ResReceiveQuestRewardMessage(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<ResReceiveQuestRewardMessage> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<ResReceiveQuestRewardMessage> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.proto.QuestMessage.ResReceiveQuestRewardMessage getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface ReqPreviousQuestsDataMessageOrBuilder extends
      // @@protoc_insertion_point(interface_extends:ProtoMessage.ReqPreviousQuestsDataMessage)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>int32 msgID = 1;</code>
     * @return The msgID.
     */
    int getMsgID();

    /**
     * <pre>
     *页
     * </pre>
     *
     * <code>int32 page = 2;</code>
     * @return The page.
     */
    int getPage();

    /**
     * <pre>
     *页数量
     * </pre>
     *
     * <code>int32 pageSize = 3;</code>
     * @return The pageSize.
     */
    int getPageSize();
  }
  /**
   * <pre>
   *请求之前任务数据
   * </pre>
   *
   * Protobuf type {@code ProtoMessage.ReqPreviousQuestsDataMessage}
   */
  public static final class ReqPreviousQuestsDataMessage extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:ProtoMessage.ReqPreviousQuestsDataMessage)
      ReqPreviousQuestsDataMessageOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use ReqPreviousQuestsDataMessage.newBuilder() to construct.
    private ReqPreviousQuestsDataMessage(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private ReqPreviousQuestsDataMessage() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new ReqPreviousQuestsDataMessage();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private ReqPreviousQuestsDataMessage(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {

              msgID_ = input.readInt32();
              break;
            }
            case 16: {

              page_ = input.readInt32();
              break;
            }
            case 24: {

              pageSize_ = input.readInt32();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.proto.QuestMessage.internal_static_ProtoMessage_ReqPreviousQuestsDataMessage_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.proto.QuestMessage.internal_static_ProtoMessage_ReqPreviousQuestsDataMessage_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.proto.QuestMessage.ReqPreviousQuestsDataMessage.class, com.proto.QuestMessage.ReqPreviousQuestsDataMessage.Builder.class);
    }

    public static final int MSGID_FIELD_NUMBER = 1;
    private int msgID_;
    /**
     * <code>int32 msgID = 1;</code>
     * @return The msgID.
     */
    @java.lang.Override
    public int getMsgID() {
      return msgID_;
    }

    public static final int PAGE_FIELD_NUMBER = 2;
    private int page_;
    /**
     * <pre>
     *页
     * </pre>
     *
     * <code>int32 page = 2;</code>
     * @return The page.
     */
    @java.lang.Override
    public int getPage() {
      return page_;
    }

    public static final int PAGESIZE_FIELD_NUMBER = 3;
    private int pageSize_;
    /**
     * <pre>
     *页数量
     * </pre>
     *
     * <code>int32 pageSize = 3;</code>
     * @return The pageSize.
     */
    @java.lang.Override
    public int getPageSize() {
      return pageSize_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (msgID_ != 0) {
        output.writeInt32(1, msgID_);
      }
      if (page_ != 0) {
        output.writeInt32(2, page_);
      }
      if (pageSize_ != 0) {
        output.writeInt32(3, pageSize_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (msgID_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, msgID_);
      }
      if (page_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(2, page_);
      }
      if (pageSize_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(3, pageSize_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.proto.QuestMessage.ReqPreviousQuestsDataMessage)) {
        return super.equals(obj);
      }
      com.proto.QuestMessage.ReqPreviousQuestsDataMessage other = (com.proto.QuestMessage.ReqPreviousQuestsDataMessage) obj;

      if (getMsgID()
          != other.getMsgID()) return false;
      if (getPage()
          != other.getPage()) return false;
      if (getPageSize()
          != other.getPageSize()) return false;
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + MSGID_FIELD_NUMBER;
      hash = (53 * hash) + getMsgID();
      hash = (37 * hash) + PAGE_FIELD_NUMBER;
      hash = (53 * hash) + getPage();
      hash = (37 * hash) + PAGESIZE_FIELD_NUMBER;
      hash = (53 * hash) + getPageSize();
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.proto.QuestMessage.ReqPreviousQuestsDataMessage parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.proto.QuestMessage.ReqPreviousQuestsDataMessage parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.proto.QuestMessage.ReqPreviousQuestsDataMessage parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.proto.QuestMessage.ReqPreviousQuestsDataMessage parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.proto.QuestMessage.ReqPreviousQuestsDataMessage parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.proto.QuestMessage.ReqPreviousQuestsDataMessage parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.proto.QuestMessage.ReqPreviousQuestsDataMessage parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.proto.QuestMessage.ReqPreviousQuestsDataMessage parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.proto.QuestMessage.ReqPreviousQuestsDataMessage parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.proto.QuestMessage.ReqPreviousQuestsDataMessage parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.proto.QuestMessage.ReqPreviousQuestsDataMessage parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.proto.QuestMessage.ReqPreviousQuestsDataMessage parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.proto.QuestMessage.ReqPreviousQuestsDataMessage prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     *请求之前任务数据
     * </pre>
     *
     * Protobuf type {@code ProtoMessage.ReqPreviousQuestsDataMessage}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:ProtoMessage.ReqPreviousQuestsDataMessage)
        com.proto.QuestMessage.ReqPreviousQuestsDataMessageOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.proto.QuestMessage.internal_static_ProtoMessage_ReqPreviousQuestsDataMessage_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.proto.QuestMessage.internal_static_ProtoMessage_ReqPreviousQuestsDataMessage_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.proto.QuestMessage.ReqPreviousQuestsDataMessage.class, com.proto.QuestMessage.ReqPreviousQuestsDataMessage.Builder.class);
      }

      // Construct using com.proto.QuestMessage.ReqPreviousQuestsDataMessage.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        msgID_ = 0;

        page_ = 0;

        pageSize_ = 0;

        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.proto.QuestMessage.internal_static_ProtoMessage_ReqPreviousQuestsDataMessage_descriptor;
      }

      @java.lang.Override
      public com.proto.QuestMessage.ReqPreviousQuestsDataMessage getDefaultInstanceForType() {
        return com.proto.QuestMessage.ReqPreviousQuestsDataMessage.getDefaultInstance();
      }

      @java.lang.Override
      public com.proto.QuestMessage.ReqPreviousQuestsDataMessage build() {
        com.proto.QuestMessage.ReqPreviousQuestsDataMessage result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.proto.QuestMessage.ReqPreviousQuestsDataMessage buildPartial() {
        com.proto.QuestMessage.ReqPreviousQuestsDataMessage result = new com.proto.QuestMessage.ReqPreviousQuestsDataMessage(this);
        result.msgID_ = msgID_;
        result.page_ = page_;
        result.pageSize_ = pageSize_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.proto.QuestMessage.ReqPreviousQuestsDataMessage) {
          return mergeFrom((com.proto.QuestMessage.ReqPreviousQuestsDataMessage)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.proto.QuestMessage.ReqPreviousQuestsDataMessage other) {
        if (other == com.proto.QuestMessage.ReqPreviousQuestsDataMessage.getDefaultInstance()) return this;
        if (other.getMsgID() != 0) {
          setMsgID(other.getMsgID());
        }
        if (other.getPage() != 0) {
          setPage(other.getPage());
        }
        if (other.getPageSize() != 0) {
          setPageSize(other.getPageSize());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.proto.QuestMessage.ReqPreviousQuestsDataMessage parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.proto.QuestMessage.ReqPreviousQuestsDataMessage) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }

      private int msgID_ ;
      /**
       * <code>int32 msgID = 1;</code>
       * @return The msgID.
       */
      @java.lang.Override
      public int getMsgID() {
        return msgID_;
      }
      /**
       * <code>int32 msgID = 1;</code>
       * @param value The msgID to set.
       * @return This builder for chaining.
       */
      public Builder setMsgID(int value) {
        
        msgID_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>int32 msgID = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearMsgID() {
        
        msgID_ = 0;
        onChanged();
        return this;
      }

      private int page_ ;
      /**
       * <pre>
       *页
       * </pre>
       *
       * <code>int32 page = 2;</code>
       * @return The page.
       */
      @java.lang.Override
      public int getPage() {
        return page_;
      }
      /**
       * <pre>
       *页
       * </pre>
       *
       * <code>int32 page = 2;</code>
       * @param value The page to set.
       * @return This builder for chaining.
       */
      public Builder setPage(int value) {
        
        page_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *页
       * </pre>
       *
       * <code>int32 page = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearPage() {
        
        page_ = 0;
        onChanged();
        return this;
      }

      private int pageSize_ ;
      /**
       * <pre>
       *页数量
       * </pre>
       *
       * <code>int32 pageSize = 3;</code>
       * @return The pageSize.
       */
      @java.lang.Override
      public int getPageSize() {
        return pageSize_;
      }
      /**
       * <pre>
       *页数量
       * </pre>
       *
       * <code>int32 pageSize = 3;</code>
       * @param value The pageSize to set.
       * @return This builder for chaining.
       */
      public Builder setPageSize(int value) {
        
        pageSize_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *页数量
       * </pre>
       *
       * <code>int32 pageSize = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearPageSize() {
        
        pageSize_ = 0;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:ProtoMessage.ReqPreviousQuestsDataMessage)
    }

    // @@protoc_insertion_point(class_scope:ProtoMessage.ReqPreviousQuestsDataMessage)
    private static final com.proto.QuestMessage.ReqPreviousQuestsDataMessage DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.proto.QuestMessage.ReqPreviousQuestsDataMessage();
    }

    public static com.proto.QuestMessage.ReqPreviousQuestsDataMessage getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<ReqPreviousQuestsDataMessage>
        PARSER = new com.google.protobuf.AbstractParser<ReqPreviousQuestsDataMessage>() {
      @java.lang.Override
      public ReqPreviousQuestsDataMessage parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new ReqPreviousQuestsDataMessage(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<ReqPreviousQuestsDataMessage> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<ReqPreviousQuestsDataMessage> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.proto.QuestMessage.ReqPreviousQuestsDataMessage getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface ResPreviousQuestsDataMessageOrBuilder extends
      // @@protoc_insertion_point(interface_extends:ProtoMessage.ResPreviousQuestsDataMessage)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>int32 msgID = 1;</code>
     * @return The msgID.
     */
    int getMsgID();

    /**
     * <pre>
     *错误码
     * </pre>
     *
     * <code>int32 error = 2;</code>
     * @return The error.
     */
    int getError();

    /**
     * <pre>
     *页
     * </pre>
     *
     * <code>int32 page = 3;</code>
     * @return The page.
     */
    int getPage();

    /**
     * <pre>
     *页数量
     * </pre>
     *
     * <code>int32 pageSize = 4;</code>
     * @return The pageSize.
     */
    int getPageSize();

    /**
     * <pre>
     *总条数
     * </pre>
     *
     * <code>int32 total = 5;</code>
     * @return The total.
     */
    int getTotal();

    /**
     * <pre>
     *总页数
     * </pre>
     *
     * <code>int32 totalPage = 6;</code>
     * @return The totalPage.
     */
    int getTotalPage();

    /**
     * <pre>
     *任务
     * </pre>
     *
     * <code>repeated .ProtoMessage.QuestInfo questList = 7;</code>
     */
    java.util.List<com.proto.QuestMessage.QuestInfo> 
        getQuestListList();
    /**
     * <pre>
     *任务
     * </pre>
     *
     * <code>repeated .ProtoMessage.QuestInfo questList = 7;</code>
     */
    com.proto.QuestMessage.QuestInfo getQuestList(int index);
    /**
     * <pre>
     *任务
     * </pre>
     *
     * <code>repeated .ProtoMessage.QuestInfo questList = 7;</code>
     */
    int getQuestListCount();
    /**
     * <pre>
     *任务
     * </pre>
     *
     * <code>repeated .ProtoMessage.QuestInfo questList = 7;</code>
     */
    java.util.List<? extends com.proto.QuestMessage.QuestInfoOrBuilder> 
        getQuestListOrBuilderList();
    /**
     * <pre>
     *任务
     * </pre>
     *
     * <code>repeated .ProtoMessage.QuestInfo questList = 7;</code>
     */
    com.proto.QuestMessage.QuestInfoOrBuilder getQuestListOrBuilder(
        int index);

    /**
     * <pre>
     *清理奖励时间
     * </pre>
     *
     * <code>int64 clearRewardTime = 8;</code>
     * @return The clearRewardTime.
     */
    long getClearRewardTime();
  }
  /**
   * <pre>
   *返回之前任务数据
   * </pre>
   *
   * Protobuf type {@code ProtoMessage.ResPreviousQuestsDataMessage}
   */
  public static final class ResPreviousQuestsDataMessage extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:ProtoMessage.ResPreviousQuestsDataMessage)
      ResPreviousQuestsDataMessageOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use ResPreviousQuestsDataMessage.newBuilder() to construct.
    private ResPreviousQuestsDataMessage(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private ResPreviousQuestsDataMessage() {
      questList_ = java.util.Collections.emptyList();
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new ResPreviousQuestsDataMessage();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private ResPreviousQuestsDataMessage(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {

              msgID_ = input.readInt32();
              break;
            }
            case 16: {

              error_ = input.readInt32();
              break;
            }
            case 24: {

              page_ = input.readInt32();
              break;
            }
            case 32: {

              pageSize_ = input.readInt32();
              break;
            }
            case 40: {

              total_ = input.readInt32();
              break;
            }
            case 48: {

              totalPage_ = input.readInt32();
              break;
            }
            case 58: {
              if (!((mutable_bitField0_ & 0x00000001) != 0)) {
                questList_ = new java.util.ArrayList<com.proto.QuestMessage.QuestInfo>();
                mutable_bitField0_ |= 0x00000001;
              }
              questList_.add(
                  input.readMessage(com.proto.QuestMessage.QuestInfo.parser(), extensionRegistry));
              break;
            }
            case 64: {

              clearRewardTime_ = input.readInt64();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        if (((mutable_bitField0_ & 0x00000001) != 0)) {
          questList_ = java.util.Collections.unmodifiableList(questList_);
        }
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.proto.QuestMessage.internal_static_ProtoMessage_ResPreviousQuestsDataMessage_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.proto.QuestMessage.internal_static_ProtoMessage_ResPreviousQuestsDataMessage_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.proto.QuestMessage.ResPreviousQuestsDataMessage.class, com.proto.QuestMessage.ResPreviousQuestsDataMessage.Builder.class);
    }

    public static final int MSGID_FIELD_NUMBER = 1;
    private int msgID_;
    /**
     * <code>int32 msgID = 1;</code>
     * @return The msgID.
     */
    @java.lang.Override
    public int getMsgID() {
      return msgID_;
    }

    public static final int ERROR_FIELD_NUMBER = 2;
    private int error_;
    /**
     * <pre>
     *错误码
     * </pre>
     *
     * <code>int32 error = 2;</code>
     * @return The error.
     */
    @java.lang.Override
    public int getError() {
      return error_;
    }

    public static final int PAGE_FIELD_NUMBER = 3;
    private int page_;
    /**
     * <pre>
     *页
     * </pre>
     *
     * <code>int32 page = 3;</code>
     * @return The page.
     */
    @java.lang.Override
    public int getPage() {
      return page_;
    }

    public static final int PAGESIZE_FIELD_NUMBER = 4;
    private int pageSize_;
    /**
     * <pre>
     *页数量
     * </pre>
     *
     * <code>int32 pageSize = 4;</code>
     * @return The pageSize.
     */
    @java.lang.Override
    public int getPageSize() {
      return pageSize_;
    }

    public static final int TOTAL_FIELD_NUMBER = 5;
    private int total_;
    /**
     * <pre>
     *总条数
     * </pre>
     *
     * <code>int32 total = 5;</code>
     * @return The total.
     */
    @java.lang.Override
    public int getTotal() {
      return total_;
    }

    public static final int TOTALPAGE_FIELD_NUMBER = 6;
    private int totalPage_;
    /**
     * <pre>
     *总页数
     * </pre>
     *
     * <code>int32 totalPage = 6;</code>
     * @return The totalPage.
     */
    @java.lang.Override
    public int getTotalPage() {
      return totalPage_;
    }

    public static final int QUESTLIST_FIELD_NUMBER = 7;
    private java.util.List<com.proto.QuestMessage.QuestInfo> questList_;
    /**
     * <pre>
     *任务
     * </pre>
     *
     * <code>repeated .ProtoMessage.QuestInfo questList = 7;</code>
     */
    @java.lang.Override
    public java.util.List<com.proto.QuestMessage.QuestInfo> getQuestListList() {
      return questList_;
    }
    /**
     * <pre>
     *任务
     * </pre>
     *
     * <code>repeated .ProtoMessage.QuestInfo questList = 7;</code>
     */
    @java.lang.Override
    public java.util.List<? extends com.proto.QuestMessage.QuestInfoOrBuilder> 
        getQuestListOrBuilderList() {
      return questList_;
    }
    /**
     * <pre>
     *任务
     * </pre>
     *
     * <code>repeated .ProtoMessage.QuestInfo questList = 7;</code>
     */
    @java.lang.Override
    public int getQuestListCount() {
      return questList_.size();
    }
    /**
     * <pre>
     *任务
     * </pre>
     *
     * <code>repeated .ProtoMessage.QuestInfo questList = 7;</code>
     */
    @java.lang.Override
    public com.proto.QuestMessage.QuestInfo getQuestList(int index) {
      return questList_.get(index);
    }
    /**
     * <pre>
     *任务
     * </pre>
     *
     * <code>repeated .ProtoMessage.QuestInfo questList = 7;</code>
     */
    @java.lang.Override
    public com.proto.QuestMessage.QuestInfoOrBuilder getQuestListOrBuilder(
        int index) {
      return questList_.get(index);
    }

    public static final int CLEARREWARDTIME_FIELD_NUMBER = 8;
    private long clearRewardTime_;
    /**
     * <pre>
     *清理奖励时间
     * </pre>
     *
     * <code>int64 clearRewardTime = 8;</code>
     * @return The clearRewardTime.
     */
    @java.lang.Override
    public long getClearRewardTime() {
      return clearRewardTime_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (msgID_ != 0) {
        output.writeInt32(1, msgID_);
      }
      if (error_ != 0) {
        output.writeInt32(2, error_);
      }
      if (page_ != 0) {
        output.writeInt32(3, page_);
      }
      if (pageSize_ != 0) {
        output.writeInt32(4, pageSize_);
      }
      if (total_ != 0) {
        output.writeInt32(5, total_);
      }
      if (totalPage_ != 0) {
        output.writeInt32(6, totalPage_);
      }
      for (int i = 0; i < questList_.size(); i++) {
        output.writeMessage(7, questList_.get(i));
      }
      if (clearRewardTime_ != 0L) {
        output.writeInt64(8, clearRewardTime_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (msgID_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, msgID_);
      }
      if (error_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(2, error_);
      }
      if (page_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(3, page_);
      }
      if (pageSize_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(4, pageSize_);
      }
      if (total_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(5, total_);
      }
      if (totalPage_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(6, totalPage_);
      }
      for (int i = 0; i < questList_.size(); i++) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(7, questList_.get(i));
      }
      if (clearRewardTime_ != 0L) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(8, clearRewardTime_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.proto.QuestMessage.ResPreviousQuestsDataMessage)) {
        return super.equals(obj);
      }
      com.proto.QuestMessage.ResPreviousQuestsDataMessage other = (com.proto.QuestMessage.ResPreviousQuestsDataMessage) obj;

      if (getMsgID()
          != other.getMsgID()) return false;
      if (getError()
          != other.getError()) return false;
      if (getPage()
          != other.getPage()) return false;
      if (getPageSize()
          != other.getPageSize()) return false;
      if (getTotal()
          != other.getTotal()) return false;
      if (getTotalPage()
          != other.getTotalPage()) return false;
      if (!getQuestListList()
          .equals(other.getQuestListList())) return false;
      if (getClearRewardTime()
          != other.getClearRewardTime()) return false;
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + MSGID_FIELD_NUMBER;
      hash = (53 * hash) + getMsgID();
      hash = (37 * hash) + ERROR_FIELD_NUMBER;
      hash = (53 * hash) + getError();
      hash = (37 * hash) + PAGE_FIELD_NUMBER;
      hash = (53 * hash) + getPage();
      hash = (37 * hash) + PAGESIZE_FIELD_NUMBER;
      hash = (53 * hash) + getPageSize();
      hash = (37 * hash) + TOTAL_FIELD_NUMBER;
      hash = (53 * hash) + getTotal();
      hash = (37 * hash) + TOTALPAGE_FIELD_NUMBER;
      hash = (53 * hash) + getTotalPage();
      if (getQuestListCount() > 0) {
        hash = (37 * hash) + QUESTLIST_FIELD_NUMBER;
        hash = (53 * hash) + getQuestListList().hashCode();
      }
      hash = (37 * hash) + CLEARREWARDTIME_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getClearRewardTime());
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.proto.QuestMessage.ResPreviousQuestsDataMessage parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.proto.QuestMessage.ResPreviousQuestsDataMessage parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.proto.QuestMessage.ResPreviousQuestsDataMessage parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.proto.QuestMessage.ResPreviousQuestsDataMessage parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.proto.QuestMessage.ResPreviousQuestsDataMessage parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.proto.QuestMessage.ResPreviousQuestsDataMessage parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.proto.QuestMessage.ResPreviousQuestsDataMessage parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.proto.QuestMessage.ResPreviousQuestsDataMessage parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.proto.QuestMessage.ResPreviousQuestsDataMessage parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.proto.QuestMessage.ResPreviousQuestsDataMessage parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.proto.QuestMessage.ResPreviousQuestsDataMessage parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.proto.QuestMessage.ResPreviousQuestsDataMessage parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.proto.QuestMessage.ResPreviousQuestsDataMessage prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     *返回之前任务数据
     * </pre>
     *
     * Protobuf type {@code ProtoMessage.ResPreviousQuestsDataMessage}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:ProtoMessage.ResPreviousQuestsDataMessage)
        com.proto.QuestMessage.ResPreviousQuestsDataMessageOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.proto.QuestMessage.internal_static_ProtoMessage_ResPreviousQuestsDataMessage_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.proto.QuestMessage.internal_static_ProtoMessage_ResPreviousQuestsDataMessage_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.proto.QuestMessage.ResPreviousQuestsDataMessage.class, com.proto.QuestMessage.ResPreviousQuestsDataMessage.Builder.class);
      }

      // Construct using com.proto.QuestMessage.ResPreviousQuestsDataMessage.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
          getQuestListFieldBuilder();
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        msgID_ = 0;

        error_ = 0;

        page_ = 0;

        pageSize_ = 0;

        total_ = 0;

        totalPage_ = 0;

        if (questListBuilder_ == null) {
          questList_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000001);
        } else {
          questListBuilder_.clear();
        }
        clearRewardTime_ = 0L;

        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.proto.QuestMessage.internal_static_ProtoMessage_ResPreviousQuestsDataMessage_descriptor;
      }

      @java.lang.Override
      public com.proto.QuestMessage.ResPreviousQuestsDataMessage getDefaultInstanceForType() {
        return com.proto.QuestMessage.ResPreviousQuestsDataMessage.getDefaultInstance();
      }

      @java.lang.Override
      public com.proto.QuestMessage.ResPreviousQuestsDataMessage build() {
        com.proto.QuestMessage.ResPreviousQuestsDataMessage result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.proto.QuestMessage.ResPreviousQuestsDataMessage buildPartial() {
        com.proto.QuestMessage.ResPreviousQuestsDataMessage result = new com.proto.QuestMessage.ResPreviousQuestsDataMessage(this);
        int from_bitField0_ = bitField0_;
        result.msgID_ = msgID_;
        result.error_ = error_;
        result.page_ = page_;
        result.pageSize_ = pageSize_;
        result.total_ = total_;
        result.totalPage_ = totalPage_;
        if (questListBuilder_ == null) {
          if (((bitField0_ & 0x00000001) != 0)) {
            questList_ = java.util.Collections.unmodifiableList(questList_);
            bitField0_ = (bitField0_ & ~0x00000001);
          }
          result.questList_ = questList_;
        } else {
          result.questList_ = questListBuilder_.build();
        }
        result.clearRewardTime_ = clearRewardTime_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.proto.QuestMessage.ResPreviousQuestsDataMessage) {
          return mergeFrom((com.proto.QuestMessage.ResPreviousQuestsDataMessage)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.proto.QuestMessage.ResPreviousQuestsDataMessage other) {
        if (other == com.proto.QuestMessage.ResPreviousQuestsDataMessage.getDefaultInstance()) return this;
        if (other.getMsgID() != 0) {
          setMsgID(other.getMsgID());
        }
        if (other.getError() != 0) {
          setError(other.getError());
        }
        if (other.getPage() != 0) {
          setPage(other.getPage());
        }
        if (other.getPageSize() != 0) {
          setPageSize(other.getPageSize());
        }
        if (other.getTotal() != 0) {
          setTotal(other.getTotal());
        }
        if (other.getTotalPage() != 0) {
          setTotalPage(other.getTotalPage());
        }
        if (questListBuilder_ == null) {
          if (!other.questList_.isEmpty()) {
            if (questList_.isEmpty()) {
              questList_ = other.questList_;
              bitField0_ = (bitField0_ & ~0x00000001);
            } else {
              ensureQuestListIsMutable();
              questList_.addAll(other.questList_);
            }
            onChanged();
          }
        } else {
          if (!other.questList_.isEmpty()) {
            if (questListBuilder_.isEmpty()) {
              questListBuilder_.dispose();
              questListBuilder_ = null;
              questList_ = other.questList_;
              bitField0_ = (bitField0_ & ~0x00000001);
              questListBuilder_ = 
                com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                   getQuestListFieldBuilder() : null;
            } else {
              questListBuilder_.addAllMessages(other.questList_);
            }
          }
        }
        if (other.getClearRewardTime() != 0L) {
          setClearRewardTime(other.getClearRewardTime());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.proto.QuestMessage.ResPreviousQuestsDataMessage parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.proto.QuestMessage.ResPreviousQuestsDataMessage) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private int msgID_ ;
      /**
       * <code>int32 msgID = 1;</code>
       * @return The msgID.
       */
      @java.lang.Override
      public int getMsgID() {
        return msgID_;
      }
      /**
       * <code>int32 msgID = 1;</code>
       * @param value The msgID to set.
       * @return This builder for chaining.
       */
      public Builder setMsgID(int value) {
        
        msgID_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>int32 msgID = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearMsgID() {
        
        msgID_ = 0;
        onChanged();
        return this;
      }

      private int error_ ;
      /**
       * <pre>
       *错误码
       * </pre>
       *
       * <code>int32 error = 2;</code>
       * @return The error.
       */
      @java.lang.Override
      public int getError() {
        return error_;
      }
      /**
       * <pre>
       *错误码
       * </pre>
       *
       * <code>int32 error = 2;</code>
       * @param value The error to set.
       * @return This builder for chaining.
       */
      public Builder setError(int value) {
        
        error_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *错误码
       * </pre>
       *
       * <code>int32 error = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearError() {
        
        error_ = 0;
        onChanged();
        return this;
      }

      private int page_ ;
      /**
       * <pre>
       *页
       * </pre>
       *
       * <code>int32 page = 3;</code>
       * @return The page.
       */
      @java.lang.Override
      public int getPage() {
        return page_;
      }
      /**
       * <pre>
       *页
       * </pre>
       *
       * <code>int32 page = 3;</code>
       * @param value The page to set.
       * @return This builder for chaining.
       */
      public Builder setPage(int value) {
        
        page_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *页
       * </pre>
       *
       * <code>int32 page = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearPage() {
        
        page_ = 0;
        onChanged();
        return this;
      }

      private int pageSize_ ;
      /**
       * <pre>
       *页数量
       * </pre>
       *
       * <code>int32 pageSize = 4;</code>
       * @return The pageSize.
       */
      @java.lang.Override
      public int getPageSize() {
        return pageSize_;
      }
      /**
       * <pre>
       *页数量
       * </pre>
       *
       * <code>int32 pageSize = 4;</code>
       * @param value The pageSize to set.
       * @return This builder for chaining.
       */
      public Builder setPageSize(int value) {
        
        pageSize_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *页数量
       * </pre>
       *
       * <code>int32 pageSize = 4;</code>
       * @return This builder for chaining.
       */
      public Builder clearPageSize() {
        
        pageSize_ = 0;
        onChanged();
        return this;
      }

      private int total_ ;
      /**
       * <pre>
       *总条数
       * </pre>
       *
       * <code>int32 total = 5;</code>
       * @return The total.
       */
      @java.lang.Override
      public int getTotal() {
        return total_;
      }
      /**
       * <pre>
       *总条数
       * </pre>
       *
       * <code>int32 total = 5;</code>
       * @param value The total to set.
       * @return This builder for chaining.
       */
      public Builder setTotal(int value) {
        
        total_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *总条数
       * </pre>
       *
       * <code>int32 total = 5;</code>
       * @return This builder for chaining.
       */
      public Builder clearTotal() {
        
        total_ = 0;
        onChanged();
        return this;
      }

      private int totalPage_ ;
      /**
       * <pre>
       *总页数
       * </pre>
       *
       * <code>int32 totalPage = 6;</code>
       * @return The totalPage.
       */
      @java.lang.Override
      public int getTotalPage() {
        return totalPage_;
      }
      /**
       * <pre>
       *总页数
       * </pre>
       *
       * <code>int32 totalPage = 6;</code>
       * @param value The totalPage to set.
       * @return This builder for chaining.
       */
      public Builder setTotalPage(int value) {
        
        totalPage_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *总页数
       * </pre>
       *
       * <code>int32 totalPage = 6;</code>
       * @return This builder for chaining.
       */
      public Builder clearTotalPage() {
        
        totalPage_ = 0;
        onChanged();
        return this;
      }

      private java.util.List<com.proto.QuestMessage.QuestInfo> questList_ =
        java.util.Collections.emptyList();
      private void ensureQuestListIsMutable() {
        if (!((bitField0_ & 0x00000001) != 0)) {
          questList_ = new java.util.ArrayList<com.proto.QuestMessage.QuestInfo>(questList_);
          bitField0_ |= 0x00000001;
         }
      }

      private com.google.protobuf.RepeatedFieldBuilderV3<
          com.proto.QuestMessage.QuestInfo, com.proto.QuestMessage.QuestInfo.Builder, com.proto.QuestMessage.QuestInfoOrBuilder> questListBuilder_;

      /**
       * <pre>
       *任务
       * </pre>
       *
       * <code>repeated .ProtoMessage.QuestInfo questList = 7;</code>
       */
      public java.util.List<com.proto.QuestMessage.QuestInfo> getQuestListList() {
        if (questListBuilder_ == null) {
          return java.util.Collections.unmodifiableList(questList_);
        } else {
          return questListBuilder_.getMessageList();
        }
      }
      /**
       * <pre>
       *任务
       * </pre>
       *
       * <code>repeated .ProtoMessage.QuestInfo questList = 7;</code>
       */
      public int getQuestListCount() {
        if (questListBuilder_ == null) {
          return questList_.size();
        } else {
          return questListBuilder_.getCount();
        }
      }
      /**
       * <pre>
       *任务
       * </pre>
       *
       * <code>repeated .ProtoMessage.QuestInfo questList = 7;</code>
       */
      public com.proto.QuestMessage.QuestInfo getQuestList(int index) {
        if (questListBuilder_ == null) {
          return questList_.get(index);
        } else {
          return questListBuilder_.getMessage(index);
        }
      }
      /**
       * <pre>
       *任务
       * </pre>
       *
       * <code>repeated .ProtoMessage.QuestInfo questList = 7;</code>
       */
      public Builder setQuestList(
          int index, com.proto.QuestMessage.QuestInfo value) {
        if (questListBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureQuestListIsMutable();
          questList_.set(index, value);
          onChanged();
        } else {
          questListBuilder_.setMessage(index, value);
        }
        return this;
      }
      /**
       * <pre>
       *任务
       * </pre>
       *
       * <code>repeated .ProtoMessage.QuestInfo questList = 7;</code>
       */
      public Builder setQuestList(
          int index, com.proto.QuestMessage.QuestInfo.Builder builderForValue) {
        if (questListBuilder_ == null) {
          ensureQuestListIsMutable();
          questList_.set(index, builderForValue.build());
          onChanged();
        } else {
          questListBuilder_.setMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <pre>
       *任务
       * </pre>
       *
       * <code>repeated .ProtoMessage.QuestInfo questList = 7;</code>
       */
      public Builder addQuestList(com.proto.QuestMessage.QuestInfo value) {
        if (questListBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureQuestListIsMutable();
          questList_.add(value);
          onChanged();
        } else {
          questListBuilder_.addMessage(value);
        }
        return this;
      }
      /**
       * <pre>
       *任务
       * </pre>
       *
       * <code>repeated .ProtoMessage.QuestInfo questList = 7;</code>
       */
      public Builder addQuestList(
          int index, com.proto.QuestMessage.QuestInfo value) {
        if (questListBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureQuestListIsMutable();
          questList_.add(index, value);
          onChanged();
        } else {
          questListBuilder_.addMessage(index, value);
        }
        return this;
      }
      /**
       * <pre>
       *任务
       * </pre>
       *
       * <code>repeated .ProtoMessage.QuestInfo questList = 7;</code>
       */
      public Builder addQuestList(
          com.proto.QuestMessage.QuestInfo.Builder builderForValue) {
        if (questListBuilder_ == null) {
          ensureQuestListIsMutable();
          questList_.add(builderForValue.build());
          onChanged();
        } else {
          questListBuilder_.addMessage(builderForValue.build());
        }
        return this;
      }
      /**
       * <pre>
       *任务
       * </pre>
       *
       * <code>repeated .ProtoMessage.QuestInfo questList = 7;</code>
       */
      public Builder addQuestList(
          int index, com.proto.QuestMessage.QuestInfo.Builder builderForValue) {
        if (questListBuilder_ == null) {
          ensureQuestListIsMutable();
          questList_.add(index, builderForValue.build());
          onChanged();
        } else {
          questListBuilder_.addMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <pre>
       *任务
       * </pre>
       *
       * <code>repeated .ProtoMessage.QuestInfo questList = 7;</code>
       */
      public Builder addAllQuestList(
          java.lang.Iterable<? extends com.proto.QuestMessage.QuestInfo> values) {
        if (questListBuilder_ == null) {
          ensureQuestListIsMutable();
          com.google.protobuf.AbstractMessageLite.Builder.addAll(
              values, questList_);
          onChanged();
        } else {
          questListBuilder_.addAllMessages(values);
        }
        return this;
      }
      /**
       * <pre>
       *任务
       * </pre>
       *
       * <code>repeated .ProtoMessage.QuestInfo questList = 7;</code>
       */
      public Builder clearQuestList() {
        if (questListBuilder_ == null) {
          questList_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000001);
          onChanged();
        } else {
          questListBuilder_.clear();
        }
        return this;
      }
      /**
       * <pre>
       *任务
       * </pre>
       *
       * <code>repeated .ProtoMessage.QuestInfo questList = 7;</code>
       */
      public Builder removeQuestList(int index) {
        if (questListBuilder_ == null) {
          ensureQuestListIsMutable();
          questList_.remove(index);
          onChanged();
        } else {
          questListBuilder_.remove(index);
        }
        return this;
      }
      /**
       * <pre>
       *任务
       * </pre>
       *
       * <code>repeated .ProtoMessage.QuestInfo questList = 7;</code>
       */
      public com.proto.QuestMessage.QuestInfo.Builder getQuestListBuilder(
          int index) {
        return getQuestListFieldBuilder().getBuilder(index);
      }
      /**
       * <pre>
       *任务
       * </pre>
       *
       * <code>repeated .ProtoMessage.QuestInfo questList = 7;</code>
       */
      public com.proto.QuestMessage.QuestInfoOrBuilder getQuestListOrBuilder(
          int index) {
        if (questListBuilder_ == null) {
          return questList_.get(index);  } else {
          return questListBuilder_.getMessageOrBuilder(index);
        }
      }
      /**
       * <pre>
       *任务
       * </pre>
       *
       * <code>repeated .ProtoMessage.QuestInfo questList = 7;</code>
       */
      public java.util.List<? extends com.proto.QuestMessage.QuestInfoOrBuilder> 
           getQuestListOrBuilderList() {
        if (questListBuilder_ != null) {
          return questListBuilder_.getMessageOrBuilderList();
        } else {
          return java.util.Collections.unmodifiableList(questList_);
        }
      }
      /**
       * <pre>
       *任务
       * </pre>
       *
       * <code>repeated .ProtoMessage.QuestInfo questList = 7;</code>
       */
      public com.proto.QuestMessage.QuestInfo.Builder addQuestListBuilder() {
        return getQuestListFieldBuilder().addBuilder(
            com.proto.QuestMessage.QuestInfo.getDefaultInstance());
      }
      /**
       * <pre>
       *任务
       * </pre>
       *
       * <code>repeated .ProtoMessage.QuestInfo questList = 7;</code>
       */
      public com.proto.QuestMessage.QuestInfo.Builder addQuestListBuilder(
          int index) {
        return getQuestListFieldBuilder().addBuilder(
            index, com.proto.QuestMessage.QuestInfo.getDefaultInstance());
      }
      /**
       * <pre>
       *任务
       * </pre>
       *
       * <code>repeated .ProtoMessage.QuestInfo questList = 7;</code>
       */
      public java.util.List<com.proto.QuestMessage.QuestInfo.Builder> 
           getQuestListBuilderList() {
        return getQuestListFieldBuilder().getBuilderList();
      }
      private com.google.protobuf.RepeatedFieldBuilderV3<
          com.proto.QuestMessage.QuestInfo, com.proto.QuestMessage.QuestInfo.Builder, com.proto.QuestMessage.QuestInfoOrBuilder> 
          getQuestListFieldBuilder() {
        if (questListBuilder_ == null) {
          questListBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
              com.proto.QuestMessage.QuestInfo, com.proto.QuestMessage.QuestInfo.Builder, com.proto.QuestMessage.QuestInfoOrBuilder>(
                  questList_,
                  ((bitField0_ & 0x00000001) != 0),
                  getParentForChildren(),
                  isClean());
          questList_ = null;
        }
        return questListBuilder_;
      }

      private long clearRewardTime_ ;
      /**
       * <pre>
       *清理奖励时间
       * </pre>
       *
       * <code>int64 clearRewardTime = 8;</code>
       * @return The clearRewardTime.
       */
      @java.lang.Override
      public long getClearRewardTime() {
        return clearRewardTime_;
      }
      /**
       * <pre>
       *清理奖励时间
       * </pre>
       *
       * <code>int64 clearRewardTime = 8;</code>
       * @param value The clearRewardTime to set.
       * @return This builder for chaining.
       */
      public Builder setClearRewardTime(long value) {
        
        clearRewardTime_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *清理奖励时间
       * </pre>
       *
       * <code>int64 clearRewardTime = 8;</code>
       * @return This builder for chaining.
       */
      public Builder clearClearRewardTime() {
        
        clearRewardTime_ = 0L;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:ProtoMessage.ResPreviousQuestsDataMessage)
    }

    // @@protoc_insertion_point(class_scope:ProtoMessage.ResPreviousQuestsDataMessage)
    private static final com.proto.QuestMessage.ResPreviousQuestsDataMessage DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.proto.QuestMessage.ResPreviousQuestsDataMessage();
    }

    public static com.proto.QuestMessage.ResPreviousQuestsDataMessage getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<ResPreviousQuestsDataMessage>
        PARSER = new com.google.protobuf.AbstractParser<ResPreviousQuestsDataMessage>() {
      @java.lang.Override
      public ResPreviousQuestsDataMessage parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new ResPreviousQuestsDataMessage(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<ResPreviousQuestsDataMessage> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<ResPreviousQuestsDataMessage> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.proto.QuestMessage.ResPreviousQuestsDataMessage getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_ProtoMessage_ReqQuestDataMessage_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_ProtoMessage_ReqQuestDataMessage_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_ProtoMessage_ResQuestDataMessage_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_ProtoMessage_ResQuestDataMessage_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_ProtoMessage_QuestInfo_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_ProtoMessage_QuestInfo_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_ProtoMessage_QuestTarget_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_ProtoMessage_QuestTarget_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_ProtoMessage_ConditionInfo_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_ProtoMessage_ConditionInfo_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_ProtoMessage_ReqReceiveQuestRewardMessage_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_ProtoMessage_ReqReceiveQuestRewardMessage_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_ProtoMessage_ResReceiveQuestRewardMessage_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_ProtoMessage_ResReceiveQuestRewardMessage_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_ProtoMessage_ReqPreviousQuestsDataMessage_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_ProtoMessage_ReqPreviousQuestsDataMessage_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_ProtoMessage_ResPreviousQuestsDataMessage_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_ProtoMessage_ResPreviousQuestsDataMessage_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n\022QuestMessage.proto\022\014ProtoMessage\032\023Comm" +
      "onMessage.proto\"$\n\023ReqQuestDataMessage\022\r" +
      "\n\005msgID\030\001 \001(\005\"\326\001\n\023ResQuestDataMessage\022\r\n" +
      "\005msgID\030\001 \001(\005\022\r\n\005error\030\002 \001(\005\0223\n\022accumulat" +
      "edRewards\030\003 \001(\0132\027.ProtoMessage.DItemShow" +
      "\022\023\n\013currentTime\030\004 \001(\003\022\024\n\014dailyEndTime\030\005 " +
      "\001(\003\022\025\n\rweeklyEndTime\030\006 \001(\003\022*\n\tquestList\030" +
      "\007 \003(\0132\027.ProtoMessage.QuestInfo\"\312\002\n\tQuest" +
      "Info\022\017\n\007questId\030\001 \001(\005\022\021\n\tquestName\030\002 \001(\t" +
      "\022\021\n\tquestType\030\003 \001(\005\022\020\n\010goalType\030\004 \001(\005\022\r\n" +
      "\005state\030\005 \001(\005\022\023\n\013progressive\030\006 \001(\001\022\024\n\014fin" +
      "ishedTime\030\007 \001(\003\0222\n\rconditionInfo\030\010 \001(\0132\033" +
      ".ProtoMessage.ConditionInfo\022(\n\007rewards\030\t" +
      " \001(\0132\027.ProtoMessage.DItemShow\022\014\n\004icon\030\n " +
      "\001(\t\022\014\n\004desc\030\013 \001(\t\022\020\n\010uniqueId\030\014 \001(\t\022.\n\013q" +
      "uestTarget\030\r \001(\0132\031.ProtoMessage.QuestTar" +
      "get\" \n\013QuestTarget\022\021\n\tchannelId\030\001 \001(\005\"Y\n" +
      "\rConditionInfo\022\030\n\020totalProgressive\030\001 \001(\005" +
      "\022\016\n\006param1\030\002 \001(\005\022\016\n\006param2\030\003 \001(\005\022\016\n\006para" +
      "m3\030\004 \003(\005\"P\n\034ReqReceiveQuestRewardMessage" +
      "\022\r\n\005msgID\030\001 \001(\005\022\017\n\007questId\030\002 \001(\005\022\020\n\010uniq" +
      "ueId\030\003 \001(\t\"u\n\034ResReceiveQuestRewardMessa" +
      "ge\022\r\n\005msgID\030\001 \001(\005\022\r\n\005error\030\002 \001(\005\022\r\n\005stat" +
      "e\030\003 \001(\005\022(\n\007rewards\030\004 \001(\0132\027.ProtoMessage." +
      "DItemShow\"M\n\034ReqPreviousQuestsDataMessag" +
      "e\022\r\n\005msgID\030\001 \001(\005\022\014\n\004page\030\002 \001(\005\022\020\n\010pageSi" +
      "ze\030\003 \001(\005\"\303\001\n\034ResPreviousQuestsDataMessag" +
      "e\022\r\n\005msgID\030\001 \001(\005\022\r\n\005error\030\002 \001(\005\022\014\n\004page\030" +
      "\003 \001(\005\022\020\n\010pageSize\030\004 \001(\005\022\r\n\005total\030\005 \001(\005\022\021" +
      "\n\ttotalPage\030\006 \001(\005\022*\n\tquestList\030\007 \003(\0132\027.P" +
      "rotoMessage.QuestInfo\022\027\n\017clearRewardTime" +
      "\030\010 \001(\003B\013\n\tcom.protob\006proto3"
    };
    descriptor = com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
          com.proto.CommonMessage.getDescriptor(),
        });
    internal_static_ProtoMessage_ReqQuestDataMessage_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_ProtoMessage_ReqQuestDataMessage_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_ProtoMessage_ReqQuestDataMessage_descriptor,
        new java.lang.String[] { "MsgID", });
    internal_static_ProtoMessage_ResQuestDataMessage_descriptor =
      getDescriptor().getMessageTypes().get(1);
    internal_static_ProtoMessage_ResQuestDataMessage_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_ProtoMessage_ResQuestDataMessage_descriptor,
        new java.lang.String[] { "MsgID", "Error", "AccumulatedRewards", "CurrentTime", "DailyEndTime", "WeeklyEndTime", "QuestList", });
    internal_static_ProtoMessage_QuestInfo_descriptor =
      getDescriptor().getMessageTypes().get(2);
    internal_static_ProtoMessage_QuestInfo_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_ProtoMessage_QuestInfo_descriptor,
        new java.lang.String[] { "QuestId", "QuestName", "QuestType", "GoalType", "State", "Progressive", "FinishedTime", "ConditionInfo", "Rewards", "Icon", "Desc", "UniqueId", "QuestTarget", });
    internal_static_ProtoMessage_QuestTarget_descriptor =
      getDescriptor().getMessageTypes().get(3);
    internal_static_ProtoMessage_QuestTarget_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_ProtoMessage_QuestTarget_descriptor,
        new java.lang.String[] { "ChannelId", });
    internal_static_ProtoMessage_ConditionInfo_descriptor =
      getDescriptor().getMessageTypes().get(4);
    internal_static_ProtoMessage_ConditionInfo_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_ProtoMessage_ConditionInfo_descriptor,
        new java.lang.String[] { "TotalProgressive", "Param1", "Param2", "Param3", });
    internal_static_ProtoMessage_ReqReceiveQuestRewardMessage_descriptor =
      getDescriptor().getMessageTypes().get(5);
    internal_static_ProtoMessage_ReqReceiveQuestRewardMessage_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_ProtoMessage_ReqReceiveQuestRewardMessage_descriptor,
        new java.lang.String[] { "MsgID", "QuestId", "UniqueId", });
    internal_static_ProtoMessage_ResReceiveQuestRewardMessage_descriptor =
      getDescriptor().getMessageTypes().get(6);
    internal_static_ProtoMessage_ResReceiveQuestRewardMessage_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_ProtoMessage_ResReceiveQuestRewardMessage_descriptor,
        new java.lang.String[] { "MsgID", "Error", "State", "Rewards", });
    internal_static_ProtoMessage_ReqPreviousQuestsDataMessage_descriptor =
      getDescriptor().getMessageTypes().get(7);
    internal_static_ProtoMessage_ReqPreviousQuestsDataMessage_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_ProtoMessage_ReqPreviousQuestsDataMessage_descriptor,
        new java.lang.String[] { "MsgID", "Page", "PageSize", });
    internal_static_ProtoMessage_ResPreviousQuestsDataMessage_descriptor =
      getDescriptor().getMessageTypes().get(8);
    internal_static_ProtoMessage_ResPreviousQuestsDataMessage_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_ProtoMessage_ResPreviousQuestsDataMessage_descriptor,
        new java.lang.String[] { "MsgID", "Error", "Page", "PageSize", "Total", "TotalPage", "QuestList", "ClearRewardTime", });
    com.proto.CommonMessage.getDescriptor();
  }

  // @@protoc_insertion_point(outer_class_scope)
}
