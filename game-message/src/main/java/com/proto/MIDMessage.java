// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: MIDMessage.proto

package com.proto;

public final class MIDMessage {
  private MIDMessage() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  /**
   * Protobuf enum {@code ProtoMessage.MID}
   */
  public enum MID
      implements com.google.protobuf.ProtocolMessageEnum {
    /**
     * <code>none = 0;</code>
     */
    none(0),
    /**
     * <pre>
     *服务器内部消息
     * </pre>
     *
     * <code>InnerReqRegisterUpdate = 100001;</code>
     */
    InnerReqRegisterUpdate(100001),
    /**
     * <pre>
     * 内部http处理
     * </pre>
     *
     * <code>InnerReqHttpHandler = 100002;</code>
     */
    InnerReqHttpHandler(100002),
    /**
     * <pre>
     * 内部http处理
     * </pre>
     *
     * <code>InnerResHttpHandler = 100003;</code>
     */
    InnerResHttpHandler(100003),
    /**
     * <pre>
     * 广播消息
     * </pre>
     *
     * <code>InnerBroadcast = 100004;</code>
     */
    InnerBroadcast(100004),
    /**
     * <pre>
     * 请求服务器列表
     * </pre>
     *
     * <code>InnerReqServerList = 100005;</code>
     */
    InnerReqServerList(100005),
    /**
     * <pre>
     * 返回服务器列表
     * </pre>
     *
     * <code>InnerResServerList = 100006;</code>
     */
    InnerResServerList(100006),
    /**
     * <pre>
     * 请求退出大厅
     * </pre>
     *
     * <code>InnerReqLoginOutHall = 100007;</code>
     */
    InnerReqLoginOutHall(100007),
    /**
     * <pre>
     * 通知充值
     * </pre>
     *
     * <code>InnerNotifyRecharge = 100008;</code>
     */
    InnerNotifyRecharge(100008),
    /**
     * <pre>
     * 通知提现
     * </pre>
     *
     * <code>InnerNotifyWithdraw = 100009;</code>
     */
    InnerNotifyWithdraw(100009),
    /**
     * <pre>
     * 通知货币更新
     * </pre>
     *
     * <code>InnerNotifyCurrencyUpdate = 100010;</code>
     */
    InnerNotifyCurrencyUpdate(100010),
    /**
     * <pre>
     * 通知更新游戏信息
     * </pre>
     *
     * <code>InnerNotifyUpdateGameInfo = 100011;</code>
     */
    InnerNotifyUpdateGameInfo(100011),
    /**
     * <pre>
     * 获取游戏列表信息
     * </pre>
     *
     * <code>InnerGetGameList = 100012;</code>
     */
    InnerGetGameList(100012),
    /**
     * <pre>
     * 通知游戏数据更新
     * </pre>
     *
     * <code>InnerNotifyGameDataUpdate = 100013;</code>
     */
    InnerNotifyGameDataUpdate(100013),
    /**
     * <pre>
     *登录 200001
     * </pre>
     *
     * <code>ReqRegister = 200001;</code>
     */
    ReqRegister(200001),
    /**
     * <pre>
     * 请求登录
     * </pre>
     *
     * <code>ReqLogin = 200002;</code>
     */
    ReqLogin(200002),
    /**
     * <pre>
     * 返回登录
     * </pre>
     *
     * <code>ResLogin = 200003;</code>
     */
    ResLogin(200003),
    /**
     * <pre>
     * 请求邮箱验证码
     * </pre>
     *
     * <code>ReqMailVerifyCode = 200004;</code>
     */
    ReqMailVerifyCode(200004),
    /**
     * <pre>
     * 返回邮箱验证码
     * </pre>
     *
     * <code>ResMailVerifyCode = 200005;</code>
     */
    ResMailVerifyCode(200005),
    /**
     * <pre>
     * 请求未登录
     * </pre>
     *
     * <code>ReqNotLoggedIn = 200006;</code>
     */
    ReqNotLoggedIn(200006),
    /**
     * <pre>
     * 返回未登录
     * </pre>
     *
     * <code>ResNotLoggedIn = 200007;</code>
     */
    ResNotLoggedIn(200007),
    /**
     * <pre>
     * 请求网站模板
     * </pre>
     *
     * <code>ReqWebSiteModel = 200008;</code>
     */
    ReqWebSiteModel(200008),
    /**
     * <pre>
     * 返回网站模板
     * </pre>
     *
     * <code>ResWebSiteModel = 200009;</code>
     */
    ResWebSiteModel(200009),
    /**
     * <pre>
     * 请求电话验证码
     * </pre>
     *
     * <code>ReqPhoneVerifyCode = 200010;</code>
     */
    ReqPhoneVerifyCode(200010),
    /**
     * <pre>
     * 返回电话验证码
     * </pre>
     *
     * <code>ResPhoneVerifyCode = 200011;</code>
     */
    ResPhoneVerifyCode(200011),
    /**
     * <pre>
     * 请求注册认证
     * </pre>
     *
     * <code>ReqRegisterAuth = 200012;</code>
     */
    ReqRegisterAuth(200012),
    /**
     * <pre>
     * 返回注册认证
     * </pre>
     *
     * <code>ResRegisterAuth = 200013;</code>
     */
    ResRegisterAuth(200013),
    /**
     * <pre>
     * 请求渠道安装
     * </pre>
     *
     * <code>ReqChannelInstall = 200014;</code>
     */
    ReqChannelInstall(200014),
    /**
     * <pre>
     * 返回渠道安装
     * </pre>
     *
     * <code>ResChannelInstall = 200015;</code>
     */
    ResChannelInstall(200015),
    /**
     * <pre>
     *大厅 400001-400200
     * </pre>
     *
     * <code>ReqPlayerEntryHall = 400001;</code>
     */
    ReqPlayerEntryHall(400001),
    /**
     * <pre>
     * 返回玩家进入大厅
     * </pre>
     *
     * <code>ResPlayerEntryHall = 400002;</code>
     */
    ResPlayerEntryHall(400002),
    /**
     * <pre>
     * 请求玩家登出
     * </pre>
     *
     * <code>ReqPlayerSignOut = 400003;</code>
     */
    ReqPlayerSignOut(400003),
    /**
     * <pre>
     * 返回玩家登出
     * </pre>
     *
     * <code>ResPlayerSignOut = 400004;</code>
     */
    ResPlayerSignOut(400004),
    /**
     * <pre>
     * 请求头像更换
     * </pre>
     *
     * <code>ReqHeadChange = 400005;</code>
     */
    ReqHeadChange(400005),
    /**
     * <pre>
     * 返回头像更换
     * </pre>
     *
     * <code>ResHeadChange = 400006;</code>
     */
    ResHeadChange(400006),
    /**
     * <pre>
     * 请求绑定账号
     * </pre>
     *
     * <code>ReqBindAccount = 400007;</code>
     */
    ReqBindAccount(400007),
    /**
     * <pre>
     * 返回绑定账号
     * </pre>
     *
     * <code>ResBindAccount = 400008;</code>
     */
    ResBindAccount(400008),
    /**
     * <pre>
     * 请求重置密码
     * </pre>
     *
     * <code>ReqResetPassword = 400009;</code>
     */
    ReqResetPassword(400009),
    /**
     * <pre>
     * 返回重置密码
     * </pre>
     *
     * <code>ResResetPassword = 400010;</code>
     */
    ResResetPassword(400010),
    /**
     * <pre>
     * 请求名字修改
     * </pre>
     *
     * <code>ReqNameModify = 400011;</code>
     */
    ReqNameModify(400011),
    /**
     * <pre>
     * 返回名字修改
     * </pre>
     *
     * <code>ResNameModify = 400012;</code>
     */
    ResNameModify(400012),
    /**
     * <pre>
     * 请求用户数据
     * </pre>
     *
     * <code>ReqAccountData = 400013;</code>
     */
    ReqAccountData(400013),
    /**
     * <pre>
     * 返回用户数据
     * </pre>
     *
     * <code>ResAccountData = 400014;</code>
     */
    ResAccountData(400014),
    /**
     * <pre>
     * 请求邮件数据
     * </pre>
     *
     * <code>ReqInboxData = 400015;</code>
     */
    ReqInboxData(400015),
    /**
     * <pre>
     * 返回邮件数据
     * </pre>
     *
     * <code>ResInboxData = 400016;</code>
     */
    ResInboxData(400016),
    /**
     * <pre>
     * 请求读邮件
     * </pre>
     *
     * <code>ReqReadInBox = 400017;</code>
     */
    ReqReadInBox(400017),
    /**
     * <pre>
     * 返回读邮件
     * </pre>
     *
     * <code>ResReadInBox = 400018;</code>
     */
    ResReadInBox(400018),
    /**
     * <pre>
     * 请求删除邮件
     * </pre>
     *
     * <code>ReqDeleteInbox = 400019;</code>
     */
    ReqDeleteInbox(400019),
    /**
     * <pre>
     * 返回删除邮件
     * </pre>
     *
     * <code>ResDeleteInbox = 400020;</code>
     */
    ResDeleteInbox(400020),
    /**
     * <pre>
     * 请求切换货币
     * </pre>
     *
     * <code>ReqChangeCurrency = 400021;</code>
     */
    ReqChangeCurrency(400021),
    /**
     * <pre>
     * 返回切换货币
     * </pre>
     *
     * <code>ResChangeCurrency = 400022;</code>
     */
    ResChangeCurrency(400022),
    /**
     * <pre>
     * 请求显示法币
     * </pre>
     *
     * <code>ReqViewInFiat = 400023;</code>
     */
    ReqViewInFiat(400023),
    /**
     * <pre>
     * 返回显示法币
     * </pre>
     *
     * <code>ResViewInFiat = 400024;</code>
     */
    ResViewInFiat(400024),
    /**
     * <pre>
     * 请求游戏列表数据
     * </pre>
     *
     * <code>ReqCasinoData = 400027;</code>
     */
    ReqCasinoData(400027),
    /**
     * <pre>
     * 返回游戏列表数据
     * </pre>
     *
     * <code>ResCasinoData = 400028;</code>
     */
    ResCasinoData(400028),
    /**
     * <pre>
     * 请求游戏频道数据
     * </pre>
     *
     * <code>ReqGameChannelData = 400029;</code>
     */
    ReqGameChannelData(400029),
    /**
     * <pre>
     * 返回游戏频道数据
     * </pre>
     *
     * <code>ResGameChannelData = 400030;</code>
     */
    ResGameChannelData(400030),
    /**
     * <pre>
     * 请求搜索游戏
     * </pre>
     *
     * <code>ReqSearchGameData = 400031;</code>
     */
    ReqSearchGameData(400031),
    /**
     * <pre>
     * 返回搜索游戏
     * </pre>
     *
     * <code>ResSearchGameData = 400032;</code>
     */
    ResSearchGameData(400032),
    /**
     * <pre>
     * 请求Enable2FA数据
     * </pre>
     *
     * <code>ReqEnable2FAData = 400033;</code>
     */
    ReqEnable2FAData(400033),
    /**
     * <pre>
     * 返回Enable2FA数据
     * </pre>
     *
     * <code>ResEnable2FAData = 400034;</code>
     */
    ResEnable2FAData(400034),
    /**
     * <pre>
     * 请求2FA验证码
     * </pre>
     *
     * <code>Req2FAVerificationCode = 400035;</code>
     */
    Req2FAVerificationCode(400035),
    /**
     * <pre>
     * 返回2FA验证码
     * </pre>
     *
     * <code>Res2FAVerificationCode = 400036;</code>
     */
    Res2FAVerificationCode(400036),
    /**
     * <pre>
     * 请求进入三方游戏
     * </pre>
     *
     * <code>ReqEntryAgentGame = 400037;</code>
     */
    ReqEntryAgentGame(400037),
    /**
     * <pre>
     * 返回进入三方游戏
     * </pre>
     *
     * <code>ResEntryAgentGame = 400038;</code>
     */
    ResEntryAgentGame(400038),
    /**
     * <pre>
     * 请求收藏、点赞
     * </pre>
     *
     * <code>ReqUserInteraction = 400039;</code>
     */
    ReqUserInteraction(400039),
    /**
     * <pre>
     * 返回收藏、点赞
     * </pre>
     *
     * <code>ResUserInteraction = 400040;</code>
     */
    ResUserInteraction(400040),
    /**
     * <pre>
     * 请求游戏opt数据
     * </pre>
     *
     * <code>ReqGameOptData = 400041;</code>
     */
    ReqGameOptData(400041),
    /**
     * <pre>
     * 返回游戏opt数据
     * </pre>
     *
     * <code>ResGameOptData = 400042;</code>
     */
    ResGameOptData(400042),
    /**
     * <pre>
     * 请求获取排行数据
     * </pre>
     *
     * <code>ReqGetRankData = 400043;</code>
     */
    ReqGetRankData(400043),
    /**
     * <pre>
     * 返回获取排行数据
     * </pre>
     *
     * <code>ResGetRankData = 400044;</code>
     */
    ResGetRankData(400044),
    /**
     * <pre>
     * 请求最近最大数据
     * </pre>
     *
     * <code>ReqRecentBigWinsData = 400045;</code>
     */
    ReqRecentBigWinsData(400045),
    /**
     * <pre>
     * 返回最近最大数据
     * </pre>
     *
     * <code>ResRecentBigWinsData = 400046;</code>
     */
    ResRecentBigWinsData(400046),
    /**
     * <pre>
     * 请求解绑三方
     * </pre>
     *
     * <code>ReqUnbindThreeParty = 400051;</code>
     */
    ReqUnbindThreeParty(400051),
    /**
     * <pre>
     * 返回解绑三方
     * </pre>
     *
     * <code>ResUnbindThreeParty = 400052;</code>
     */
    ResUnbindThreeParty(400052),
    /**
     * <pre>
     * 请求设置隐私
     * </pre>
     *
     * <code>ReqSetPrivacyPreferences = 400053;</code>
     */
    ReqSetPrivacyPreferences(400053),
    /**
     * <pre>
     * 返回设置隐私
     * </pre>
     *
     * <code>ResSetPrivacyPreferences = 400054;</code>
     */
    ResSetPrivacyPreferences(400054),
    /**
     * <pre>
     * 请求绑定三方
     * </pre>
     *
     * <code>ReqBindThreeParty = 400055;</code>
     */
    ReqBindThreeParty(400055),
    /**
     * <pre>
     * 返回绑定三方
     * </pre>
     *
     * <code>ResBindThreeParty = 400056;</code>
     */
    ResBindThreeParty(400056),
    /**
     * <pre>
     * 请求支付方式管理数据
     * </pre>
     *
     * <code>ReqPaymentMethodsData = 400057;</code>
     */
    ReqPaymentMethodsData(400057),
    /**
     * <pre>
     * 返回支付方式管理数据
     * </pre>
     *
     * <code>ResPaymentMethodsData = 400058;</code>
     */
    ResPaymentMethodsData(400058),
    /**
     * <pre>
     * 请求查看sessions数据
     * </pre>
     *
     * <code>ReqCheckSessionData = 400059;</code>
     */
    ReqCheckSessionData(400059),
    /**
     * <pre>
     * 返回查看sessions数据
     * </pre>
     *
     * <code>ResCheckSessionData = 400060;</code>
     */
    ResCheckSessionData(400060),
    /**
     * <pre>
     * 请求切换语言
     * </pre>
     *
     * <code>ReqChangeLanguage = 400061;</code>
     */
    ReqChangeLanguage(400061),
    /**
     * <pre>
     * 返回切换语言
     * </pre>
     *
     * <code>ResChangeLanguage = 400062;</code>
     */
    ResChangeLanguage(400062),
    /**
     * <pre>
     * 请求刷新玩家数据
     * </pre>
     *
     * <code>ReqRefreshPlayerData = 400063;</code>
     */
    ReqRefreshPlayerData(400063),
    /**
     * <pre>
     * 返回刷新玩家数据
     * </pre>
     *
     * <code>ResRefreshPlayerData = 400064;</code>
     */
    ResRefreshPlayerData(400064),
    /**
     * <pre>
     * 请求验证账号
     * </pre>
     *
     * <code>ReqVerifyAccount = 400065;</code>
     */
    ReqVerifyAccount(400065),
    /**
     * <pre>
     * 返回验证账号
     * </pre>
     *
     * <code>ResVerifyAccount = 400066;</code>
     */
    ResVerifyAccount(400066),
    /**
     * <pre>
     * 请求更换账号
     * </pre>
     *
     * <code>ReqChangeAccount = 400067;</code>
     */
    ReqChangeAccount(400067),
    /**
     * <pre>
     * 返回更换账号
     * </pre>
     *
     * <code>ResChangeAccount = 400068;</code>
     */
    ResChangeAccount(400068),
    /**
     * <pre>
     * 请求kyc认证
     * </pre>
     *
     * <code>ReqKycAuth = 400069;</code>
     */
    ReqKycAuth(400069),
    /**
     * <pre>
     * 请求kyc认证
     * </pre>
     *
     * <code>ResKycAuth = 400070;</code>
     */
    ResKycAuth(400070),
    /**
     * <pre>
     * 请求添加账号
     * </pre>
     *
     * <code>ReqAddAccount = 400071;</code>
     */
    ReqAddAccount(400071),
    /**
     * <pre>
     * 返回添加账号
     * </pre>
     *
     * <code>ResAddAccount = 400072;</code>
     */
    ResAddAccount(400072),
    /**
     * <pre>
     * 请求兑换码奖励
     * </pre>
     *
     * <code>ReqRedemptionCodeReward = 400073;</code>
     */
    ReqRedemptionCodeReward(400073),
    /**
     * <pre>
     * 返回兑换码奖励
     * </pre>
     *
     * <code>ResRedemptionCodeReward = 400074;</code>
     */
    ResRedemptionCodeReward(400074),
    /**
     * <pre>
     * 请求充值奖励开关
     * </pre>
     *
     * <code>ReqRechargeBonusOpen = 400075;</code>
     */
    ReqRechargeBonusOpen(400075),
    /**
     * <pre>
     * 返回充值奖励开关
     * </pre>
     *
     * <code>ResRechargeBonusOpen = 400076;</code>
     */
    ResRechargeBonusOpen(400076),
    /**
     * <pre>
     *钱包 400201-400300
     * </pre>
     *
     * <code>ReqBetHistoryData = 400201;</code>
     */
    ReqBetHistoryData(400201),
    /**
     * <pre>
     * 返回下注历史数据
     * </pre>
     *
     * <code>ResBetHistoryData = 400202;</code>
     */
    ResBetHistoryData(400202),
    /**
     * <pre>
     * 请求交易数据
     * </pre>
     *
     * <code>ReqTransactionData = 400203;</code>
     */
    ReqTransactionData(400203),
    /**
     * <pre>
     * 返回交易数据
     * </pre>
     *
     * <code>ResTransactionData = 400204;</code>
     */
    ResTransactionData(400204),
    /**
     * <pre>
     *充值 400301-400400
     * </pre>
     *
     * <code>ReqDepositWithdrawData = 400301;</code>
     */
    ReqDepositWithdrawData(400301),
    /**
     * <pre>
     * 返回充提货币数据
     * </pre>
     *
     * <code>ResDepositWithdrawData = 400302;</code>
     */
    ResDepositWithdrawData(400302),
    /**
     * <pre>
     * 请求充值数据
     * </pre>
     *
     * <code>ReqDepositData = 400303;</code>
     */
    ReqDepositData(400303),
    /**
     * <pre>
     * 请求充值数据
     * </pre>
     *
     * <code>ResDepositData = 400304;</code>
     */
    ResDepositData(400304),
    /**
     * <pre>
     * 请求提现数据
     * </pre>
     *
     * <code>ReqWithdrawData = 400305;</code>
     */
    ReqWithdrawData(400305),
    /**
     * <pre>
     * 返回提现数据
     * </pre>
     *
     * <code>ResWithdrawData = 400306;</code>
     */
    ResWithdrawData(400306),
    /**
     * <pre>
     * 请求创建充值订单
     * </pre>
     *
     * <code>ReqCreateRechargeOrder = 400307;</code>
     */
    ReqCreateRechargeOrder(400307),
    /**
     * <pre>
     * 返回创建充值订单
     * </pre>
     *
     * <code>ResCreateRechargeOrder = 400308;</code>
     */
    ResCreateRechargeOrder(400308),
    /**
     * <pre>
     * 请求创建提现订单
     * </pre>
     *
     * <code>ReqCreateWithdrawOrder = 400309;</code>
     */
    ReqCreateWithdrawOrder(400309),
    /**
     * <pre>
     * 返回创建提现订单
     * </pre>
     *
     * <code>ResCreateWithdrawOrder = 400310;</code>
     */
    ResCreateWithdrawOrder(400310),
    /**
     * <pre>
     * 请求添加删除提现账户
     * </pre>
     *
     * <code>ReqAddDeleteWithdrawAccount = 400311;</code>
     */
    ReqAddDeleteWithdrawAccount(400311),
    /**
     * <pre>
     * 返回添加删除提现账户
     * </pre>
     *
     * <code>ResAddDeleteWithdrawAccount = 400312;</code>
     */
    ResAddDeleteWithdrawAccount(400312),
    /**
     * <pre>
     *推广 400401-400500
     * </pre>
     *
     * <code>ReqBindSuperior = 400401;</code>
     */
    ReqBindSuperior(400401),
    /**
     * <pre>
     * 返回绑定上级
     * </pre>
     *
     * <code>ResBindSuperior = 400402;</code>
     */
    ResBindSuperior(400402),
    /**
     * <pre>
     * 请求仪表盘数据
     * </pre>
     *
     * <code>ReqDashboardData = 400403;</code>
     */
    ReqDashboardData(400403),
    /**
     * <pre>
     * 返回仪表盘数据
     * </pre>
     *
     * <code>ResDashboardData = 400404;</code>
     */
    ResDashboardData(400404),
    /**
     * <pre>
     * 请求我的奖励数据
     * </pre>
     *
     * <code>ReqMyRewardData = 400405;</code>
     */
    ReqMyRewardData(400405),
    /**
     * <pre>
     * 返回我的奖励数据
     * </pre>
     *
     * <code>ResMyRewardData = 400406;</code>
     */
    ResMyRewardData(400406),
    /**
     * <pre>
     * 请求佣金奖励数据
     * </pre>
     *
     * <code>ReqCommissionRewardData = 400407;</code>
     */
    ReqCommissionRewardData(400407),
    /**
     * <pre>
     * 返回佣金奖励数据
     * </pre>
     *
     * <code>ResCommissionRewardData = 400408;</code>
     */
    ResCommissionRewardData(400408),
    /**
     * <pre>
     * 请求推广奖励数据
     * </pre>
     *
     * <code>ReqReferralRewardData = 400409;</code>
     */
    ReqReferralRewardData(400409),
    /**
     * <pre>
     * 返回推广奖励数据
     * </pre>
     *
     * <code>ResReferralRewardData = 400410;</code>
     */
    ResReferralRewardData(400410),
    /**
     * <pre>
     * 请求推广码和下级数据
     * </pre>
     *
     * <code>ReqReferralCodeAndFriendsData = 400411;</code>
     */
    ReqReferralCodeAndFriendsData(400411),
    /**
     * <pre>
     * 返回推广码和下级数据
     * </pre>
     *
     * <code>ResReferralCodeAndFriendsData = 400412;</code>
     */
    ResReferralCodeAndFriendsData(400412),
    /**
     * <pre>
     * 请求创建推广码
     * </pre>
     *
     * <code>ReqCreateReferralCode = 400413;</code>
     */
    ReqCreateReferralCode(400413),
    /**
     * <pre>
     * 返回创建推广码
     * </pre>
     *
     * <code>ResCreateReferralCode = 400414;</code>
     */
    ResCreateReferralCode(400414),
    /**
     * <pre>
     * 请求推广码数据
     * </pre>
     *
     * <code>ReqReferralCodeData = 400415;</code>
     */
    ReqReferralCodeData(400415),
    /**
     * <pre>
     * 返回推广码数据
     * </pre>
     *
     * <code>ResReferralCodeData = 400416;</code>
     */
    ResReferralCodeData(400416),
    /**
     * <pre>
     * 请求下级数据
     * </pre>
     *
     * <code>ReqFriendsData = 400417;</code>
     */
    ReqFriendsData(400417),
    /**
     * <pre>
     * 返回下级数据
     * </pre>
     *
     * <code>ResFriendsData = 400418;</code>
     */
    ResFriendsData(400418),
    /**
     * <pre>
     * 请求提现到钱包
     * </pre>
     *
     * <code>ReqWithdrawToWallet = 400419;</code>
     */
    ReqWithdrawToWallet(400419),
    /**
     * <pre>
     * 返回提现到钱包
     * </pre>
     *
     * <code>ResWithdrawToWallet = 400420;</code>
     */
    ResWithdrawToWallet(400420),
    /**
     * <pre>
     * 请求历史数据
     * </pre>
     *
     * <code>ReqHistoryData = 400421;</code>
     */
    ReqHistoryData(400421),
    /**
     * <pre>
     * 返回历史数据
     * </pre>
     *
     * <code>ResHistoryData = 400422;</code>
     */
    ResHistoryData(400422),
    /**
     * <pre>
     * 请求提现数据
     * </pre>
     *
     * <code>ReqAffiliateWithdrawData = 400423;</code>
     */
    ReqAffiliateWithdrawData(400423),
    /**
     * <pre>
     * 返回提现数据
     * </pre>
     *
     * <code>ResAffiliateWithdrawData = 400424;</code>
     */
    ResAffiliateWithdrawData(400424),
    /**
     * <pre>
     * 请求团长奖励
     * </pre>
     *
     * <code>ReqTeamRewardData = 400425;</code>
     */
    ReqTeamRewardData(400425),
    /**
     * <pre>
     * 返回团长奖励
     * </pre>
     *
     * <code>ResTeamRewardData = 400426;</code>
     */
    ResTeamRewardData(400426),
    /**
     * <pre>
     * 请求团长数据
     * </pre>
     *
     * <code>ReqTeamData = 400427;</code>
     */
    ReqTeamData(400427),
    /**
     * <pre>
     * 返回团长数据
     * </pre>
     *
     * <code>ResTeamData = 400428;</code>
     */
    ResTeamData(400428),
    /**
     * <pre>
     * 请求三级奖励数据
     * </pre>
     *
     * <code>ReqThreeLevelRewardData = 400429;</code>
     */
    ReqThreeLevelRewardData(400429),
    /**
     * <pre>
     * 返回三级奖励数据
     * </pre>
     *
     * <code>ResThreeLevelRewardData = 400430;</code>
     */
    ResThreeLevelRewardData(400430),
    /**
     * <pre>
     * 请求三级数据
     * </pre>
     *
     * <code>ReqThreeLevelData = 400431;</code>
     */
    ReqThreeLevelData(400431),
    /**
     * <pre>
     * 返回三级数据
     * </pre>
     *
     * <code>ResThreeLevelData = 400432;</code>
     */
    ResThreeLevelData(400432),
    /**
     * <pre>
     *配置数据 400501-400600
     * </pre>
     *
     * <code>ReqHelpCenterData = 400501;</code>
     */
    ReqHelpCenterData(400501),
    /**
     * <pre>
     * 返回帮助中心数据
     * </pre>
     *
     * <code>ResHelpCenterData = 400502;</code>
     */
    ResHelpCenterData(400502),
    /**
     * <pre>
     * 请求新闻数据
     * </pre>
     *
     * <code>ReqNewsData = 400503;</code>
     */
    ReqNewsData(400503),
    /**
     * <pre>
     * 返回新闻数据
     * </pre>
     *
     * <code>ResNewsData = 400504;</code>
     */
    ResNewsData(400504),
    /**
     * <pre>
     * 请求配置数据
     * </pre>
     *
     * <code>ReqConfigData = 400505;</code>
     */
    ReqConfigData(400505),
    /**
     * <pre>
     * 返回配置数据
     * </pre>
     *
     * <code>ResConfigData = 400506;</code>
     */
    ResConfigData(400506),
    /**
     * <pre>
     *活动数据 400601-400700
     * </pre>
     *
     * <code>ReqPromotionsData = 400601;</code>
     */
    ReqPromotionsData(400601),
    /**
     * <pre>
     * 返回活动数据
     * </pre>
     *
     * <code>ResPromotionsData = 400602;</code>
     */
    ResPromotionsData(400602),
    /**
     * <pre>
     * 请求幸运转盘数据
     * </pre>
     *
     * <code>ReqLuckSpinData = 400603;</code>
     */
    ReqLuckSpinData(400603),
    /**
     * <pre>
     * 返回幸运转盘数据
     * </pre>
     *
     * <code>ResLuckSpinData = 400604;</code>
     */
    ResLuckSpinData(400604),
    /**
     * <pre>
     * 请求点击幸运转盘
     * </pre>
     *
     * <code>ReqClickLuckSpin = 400605;</code>
     */
    ReqClickLuckSpin(400605),
    /**
     * <pre>
     * 返回点击幸运转盘
     * </pre>
     *
     * <code>ResClickLuckSpin = 400606;</code>
     */
    ResClickLuckSpin(400606),
    /**
     * <pre>
     * 请求幸运推广提现
     * </pre>
     *
     * <code>ReqLuckSpinReferralWithdraw = 400607;</code>
     */
    ReqLuckSpinReferralWithdraw(400607),
    /**
     * <pre>
     * 返回幸运推广提现
     * </pre>
     *
     * <code>ResLuckSpinReferralWithdraw = 400608;</code>
     */
    ResLuckSpinReferralWithdraw(400608),
    /**
     * <pre>
     * 请求bonus数据
     * </pre>
     *
     * <code>ReqBonusData = 400609;</code>
     */
    ReqBonusData(400609),
    /**
     * <pre>
     * 返回bonus数据
     * </pre>
     *
     * <code>ResBonusData = 400610;</code>
     */
    ResBonusData(400610),
    /**
     * <pre>
     * 请求vipBonus奖励领取
     * </pre>
     *
     * <code>ReqVipBonusRewardsReceive = 400611;</code>
     */
    ReqVipBonusRewardsReceive(400611),
    /**
     * <pre>
     * 返回vipBonus奖励领取
     * </pre>
     *
     * <code>ResVipBonusRewardsReceive = 400612;</code>
     */
    ResVipBonusRewardsReceive(400612),
    /**
     * <pre>
     * 请求bonus明细数据
     * </pre>
     *
     * <code>ReqBonusDetailsData = 400613;</code>
     */
    ReqBonusDetailsData(400613),
    /**
     * <pre>
     * 返回bonus明细数据
     * </pre>
     *
     * <code>ResBonusDetailsData = 400614;</code>
     */
    ResBonusDetailsData(400614),
    /**
     * <pre>
     * 请求bonus交易数据
     * </pre>
     *
     * <code>ReqBonusTransactionsData = 400615;</code>
     */
    ReqBonusTransactionsData(400615),
    /**
     * <pre>
     * 返回bonus交易数据
     * </pre>
     *
     * <code>ResBonusTransactionsData = 400616;</code>
     */
    ResBonusTransactionsData(400616),
    /**
     * <pre>
     * 请求每日竞赛数据
     * </pre>
     *
     * <code>ReqDailyContestData = 400617;</code>
     */
    ReqDailyContestData(400617),
    /**
     * <pre>
     * 返回每日竞赛数据
     * </pre>
     *
     * <code>ResDailyContestData = 400618;</code>
     */
    ResDailyContestData(400618),
    /**
     * <pre>
     * 请求每日竞赛历史数据
     * </pre>
     *
     * <code>ReqDailyContestHistoryData = 400619;</code>
     */
    ReqDailyContestHistoryData(400619),
    /**
     * <pre>
     * 返回每日竞赛历史数据
     * </pre>
     *
     * <code>ResDailyContestHistoryData = 400620;</code>
     */
    ResDailyContestHistoryData(400620),
    /**
     * <pre>
     * 请求每周抽奖数据
     * </pre>
     *
     * <code>ReqWeeklyRaffleData = 400621;</code>
     */
    ReqWeeklyRaffleData(400621),
    /**
     * <pre>
     * 返回每周抽奖数据
     * </pre>
     *
     * <code>ResWeeklyRaffleData = 400622;</code>
     */
    ResWeeklyRaffleData(400622),
    /**
     * <pre>
     * 请求每周抽奖我的数据
     * </pre>
     *
     * <code>ReqWeeklyRaffleMyTicketsData = 400623;</code>
     */
    ReqWeeklyRaffleMyTicketsData(400623),
    /**
     * <pre>
     * 返回每周抽奖我的数据
     * </pre>
     *
     * <code>ResWeeklyRaffleMyTicketsData = 400624;</code>
     */
    ResWeeklyRaffleMyTicketsData(400624),
    /**
     * <pre>
     * 请求每周抽奖结果数据
     * </pre>
     *
     * <code>ReqWeeklyRaffleResultData = 400625;</code>
     */
    ReqWeeklyRaffleResultData(400625),
    /**
     * <pre>
     * 返回每周抽奖结果数据
     * </pre>
     *
     * <code>ResWeeklyRaffleResultData = 400626;</code>
     */
    ResWeeklyRaffleResultData(400626),
    /**
     * <pre>
     * 请求领取活动奖励
     * </pre>
     *
     * <code>ReqReceivePromotionsReward = 400627;</code>
     */
    ReqReceivePromotionsReward(400627),
    /**
     * <pre>
     * 返回领取活动奖励
     * </pre>
     *
     * <code>ResReceivePromotionsReward = 400628;</code>
     */
    ResReceivePromotionsReward(400628),
    /**
     * <pre>
     * 请求活动报名
     * </pre>
     *
     * <code>ReqActivitySignUp = 400629;</code>
     */
    ReqActivitySignUp(400629),
    /**
     * <pre>
     * 返回活动报名
     * </pre>
     *
     * <code>ResActivitySignUp = 400630;</code>
     */
    ResActivitySignUp(400630),
    /**
     * <pre>
     * 请求活动排行数据
     * </pre>
     *
     * <code>ReqActivityRankData = 400631;</code>
     */
    ReqActivityRankData(400631),
    /**
     * <pre>
     * 请求活动排行数据
     * </pre>
     *
     * <code>ResActivityRankData = 400632;</code>
     */
    ResActivityRankData(400632),
    /**
     * <pre>
     * 请求活动数据
     * </pre>
     *
     * <code>ReqActivityData = 400633;</code>
     */
    ReqActivityData(400633),
    /**
     * <pre>
     * 返回活动雨数据
     * </pre>
     *
     * <code>ResActivityData = 400634;</code>
     */
    ResActivityData(400634),
    /**
     * <pre>
     * 请求领取红包
     * </pre>
     *
     * <code>ReqReceiveRedEnvelope = 400635;</code>
     */
    ReqReceiveRedEnvelope(400635),
    /**
     * <pre>
     * 返回领取红包
     * </pre>
     *
     * <code>ResReceiveRedEnvelope = 400636;</code>
     */
    ResReceiveRedEnvelope(400636),
    /**
     * <pre>
     * 请求领取推荐宝箱
     * </pre>
     *
     * <code>ReqReceiveRewardBox = 400637;</code>
     */
    ReqReceiveRewardBox(400637),
    /**
     * <pre>
     * 返回领取推荐宝箱
     * </pre>
     *
     * <code>ResReceiveRewardBox = 400638;</code>
     */
    ResReceiveRewardBox(400638),
    /**
     * <pre>
     * 请求领取神秘奖金
     * </pre>
     *
     * <code>ReqReceiveMysteryBonus = 400639;</code>
     */
    ReqReceiveMysteryBonus(400639),
    /**
     * <pre>
     * 返回领取神秘奖金
     * </pre>
     *
     * <code>ResReceiveMysteryBonus = 400640;</code>
     */
    ResReceiveMysteryBonus(400640),
    /**
     * <pre>
     * 请求领取存钱罐
     * </pre>
     *
     * <code>ReqReceivePiggyBank = 400641;</code>
     */
    ReqReceivePiggyBank(400641),
    /**
     * <pre>
     * 返回领取存钱罐
     * </pre>
     *
     * <code>ResReceivePiggyBank = 400642;</code>
     */
    ResReceivePiggyBank(400642),
    /**
     * <pre>
     * 请求推荐宝箱下级数据
     * </pre>
     *
     * <code>ReqRewardBoxSubordinateData = 400643;</code>
     */
    ReqRewardBoxSubordinateData(400643),
    /**
     * <pre>
     * 返回推荐宝箱下级数据
     * </pre>
     *
     * <code>ResRewardBoxSubordinateData = 400644;</code>
     */
    ResRewardBoxSubordinateData(400644),
    /**
     * <pre>
     * 请求vip数据
     * </pre>
     *
     * <code>ReqVipData = 400645;</code>
     */
    ReqVipData(400645),
    /**
     * <pre>
     * 返回vip数据
     * </pre>
     *
     * <code>ResVipData = 400646;</code>
     */
    ResVipData(400646),
    /**
     * <pre>
     * 请求vip签到
     * </pre>
     *
     * <code>ReqVipSignIn = 400647;</code>
     */
    ReqVipSignIn(400647),
    /**
     * <pre>
     * 返回vip签到
     * </pre>
     *
     * <code>ResVipSignIn = 400648;</code>
     */
    ResVipSignIn(400648),
    /**
     * <pre>
     * 请求领取vip奖励
     * </pre>
     *
     * <code>ReqReceiveVipReward = 400649;</code>
     */
    ReqReceiveVipReward(400649),
    /**
     * <pre>
     * 返回领取vip奖励
     * </pre>
     *
     * <code>ResReceiveVipReward = 400650;</code>
     */
    ResReceiveVipReward(400650),
    /**
     * <pre>
     * 请求首充签到
     * </pre>
     *
     * <code>ReqFirstChargeSignIn = 400651;</code>
     */
    ReqFirstChargeSignIn(400651),
    /**
     * <pre>
     * 返回首充签到
     * </pre>
     *
     * <code>ResFirstChargeSignIn = 400652;</code>
     */
    ResFirstChargeSignIn(400652),
    /**
     * <pre>
     * 请求首充返奖
     * </pre>
     *
     * <code>ReqReceiveRechargeRecover = 400653;</code>
     */
    ReqReceiveRechargeRecover(400653),
    /**
     * <pre>
     * 返回首充返奖
     * </pre>
     *
     * <code>ResReceiveRechargeRecover = 400654;</code>
     */
    ResReceiveRechargeRecover(400654),
    /**
     * <pre>
     * 请求领取投注返利奖励
     * </pre>
     *
     * <code>ReqReceiveWageredRebates = 400655;</code>
     */
    ReqReceiveWageredRebates(400655),
    /**
     * <pre>
     * 返回领取投注返利奖励
     * </pre>
     *
     * <code>ResReceiveWageredRebates = 400656;</code>
     */
    ResReceiveWageredRebates(400656),
    /**
     * <pre>
     * 请求领取充值邀请奖励
     * </pre>
     *
     * <code>ReqReceiveDepositInviteBonus = 400657;</code>
     */
    ReqReceiveDepositInviteBonus(400657),
    /**
     * <pre>
     * 返回领取充值邀请奖励
     * </pre>
     *
     * <code>ResReceiveDepositInviteBonus = 400658;</code>
     */
    ResReceiveDepositInviteBonus(400658),
    /**
     * <pre>
     * 请求查看明细数据
     * </pre>
     *
     * <code>ReqCheckDetailsData = 400659;</code>
     */
    ReqCheckDetailsData(400659),
    /**
     * <pre>
     * 返回查看明细数据
     * </pre>
     *
     * <code>ResCheckDetailsData = 400660;</code>
     */
    ResCheckDetailsData(400660),
    /**
     * <pre>
     *任务 400801-400900
     * </pre>
     *
     * <code>ReqQuestData = 400801;</code>
     */
    ReqQuestData(400801),
    /**
     * <pre>
     *返回任务数据
     * </pre>
     *
     * <code>ResQuestData = 400802;</code>
     */
    ResQuestData(400802),
    /**
     * <pre>
     *请求领取任务奖励
     * </pre>
     *
     * <code>ReqReceiveQuestReward = 400803;</code>
     */
    ReqReceiveQuestReward(400803),
    /**
     * <pre>
     *返回领取任务奖励
     * </pre>
     *
     * <code>ResReceiveQuestReward = 400804;</code>
     */
    ResReceiveQuestReward(400804),
    /**
     * <pre>
     *请求先前任务数据
     * </pre>
     *
     * <code>ReqPreviousQuestsData = 400805;</code>
     */
    ReqPreviousQuestsData(400805),
    /**
     * <pre>
     *返回先前任务奖励
     * </pre>
     *
     * <code>ResPreviousQuestsData = 400806;</code>
     */
    ResPreviousQuestsData(400806),
    /**
     * <pre>
     *TCP 450001-450100
     * </pre>
     *
     * <code>ReqTcpTokenAuth = 450001;</code>
     */
    ReqTcpTokenAuth(450001),
    /**
     * <pre>
     * 返回token认证
     * </pre>
     *
     * <code>ResTcpTokenAuth = 450002;</code>
     */
    ResTcpTokenAuth(450002),
    /**
     * <pre>
     * 请求心跳
     * </pre>
     *
     * <code>ReqTcpHeartBeat = 450003;</code>
     */
    ReqTcpHeartBeat(450003),
    /**
     * <pre>
     * 返回心跳
     * </pre>
     *
     * <code>ResTcpHeartBeat = 450004;</code>
     */
    ResTcpHeartBeat(450004),
    /**
     * <pre>
     * 返回错误异常
     * </pre>
     *
     * <code>ResTcpSysError = 450005;</code>
     */
    ResTcpSysError(450005),
    /**
     * <pre>
     * 返回货币更新
     * </pre>
     *
     * <code>ResTcpCurrencyUpdate = 450006;</code>
     */
    ResTcpCurrencyUpdate(450006),
    /**
     * <pre>
     * 返回接受邮件
     * </pre>
     *
     * <code>ResTcpReceiveInbox = 450007;</code>
     */
    ResTcpReceiveInbox(450007),
    /**
     * <pre>
     * 返回vip经验改变
     * </pre>
     *
     * <code>ResTcpVipClubExpChange = 450008;</code>
     */
    ResTcpVipClubExpChange(450008),
    /**
     * <pre>
     * 返回公告数据
     * </pre>
     *
     * <code>ResTcpBulletinData = 450009;</code>
     */
    ResTcpBulletinData(450009),
    /**
     * <pre>
     * 返回游戏记录
     * </pre>
     *
     * <code>ResTcpGameNoteData = 450010;</code>
     */
    ResTcpGameNoteData(450010),
    /**
     * <pre>
     * 返回我的下注列表
     * </pre>
     *
     * <code>ResTcpMyBetData = 450011;</code>
     */
    ResTcpMyBetData(450011),
    /**
     * <pre>
     * 返回每日竞赛奖池
     * </pre>
     *
     * <code>ResTcpDailyContestPrizePool = 450012;</code>
     */
    ResTcpDailyContestPrizePool(450012),
    /**
     * <pre>
     * 请求退出三方游戏
     * </pre>
     *
     * <code>ReqTcpQuitAgentGame = 450013;</code>
     */
    ReqTcpQuitAgentGame(450013),
    /**
     * <pre>
     * 返回退出三方游戏
     * </pre>
     *
     * <code>ResTcpQuitAgentGame = 450014;</code>
     */
    ResTcpQuitAgentGame(450014),
    /**
     * <pre>
     * 返回踢出玩家
     * </pre>
     *
     * <code>ResTcpKickOutPlayer = 450015;</code>
     */
    ResTcpKickOutPlayer(450015),
    /**
     * <pre>
     * 返回充值成功
     * </pre>
     *
     * <code>ResTcpRechargeSuccess = 450016;</code>
     */
    ResTcpRechargeSuccess(450016),
    UNRECOGNIZED(-1),
    ;

    /**
     * <code>none = 0;</code>
     */
    public static final int none_VALUE = 0;
    /**
     * <pre>
     *服务器内部消息
     * </pre>
     *
     * <code>InnerReqRegisterUpdate = 100001;</code>
     */
    public static final int InnerReqRegisterUpdate_VALUE = 100001;
    /**
     * <pre>
     * 内部http处理
     * </pre>
     *
     * <code>InnerReqHttpHandler = 100002;</code>
     */
    public static final int InnerReqHttpHandler_VALUE = 100002;
    /**
     * <pre>
     * 内部http处理
     * </pre>
     *
     * <code>InnerResHttpHandler = 100003;</code>
     */
    public static final int InnerResHttpHandler_VALUE = 100003;
    /**
     * <pre>
     * 广播消息
     * </pre>
     *
     * <code>InnerBroadcast = 100004;</code>
     */
    public static final int InnerBroadcast_VALUE = 100004;
    /**
     * <pre>
     * 请求服务器列表
     * </pre>
     *
     * <code>InnerReqServerList = 100005;</code>
     */
    public static final int InnerReqServerList_VALUE = 100005;
    /**
     * <pre>
     * 返回服务器列表
     * </pre>
     *
     * <code>InnerResServerList = 100006;</code>
     */
    public static final int InnerResServerList_VALUE = 100006;
    /**
     * <pre>
     * 请求退出大厅
     * </pre>
     *
     * <code>InnerReqLoginOutHall = 100007;</code>
     */
    public static final int InnerReqLoginOutHall_VALUE = 100007;
    /**
     * <pre>
     * 通知充值
     * </pre>
     *
     * <code>InnerNotifyRecharge = 100008;</code>
     */
    public static final int InnerNotifyRecharge_VALUE = 100008;
    /**
     * <pre>
     * 通知提现
     * </pre>
     *
     * <code>InnerNotifyWithdraw = 100009;</code>
     */
    public static final int InnerNotifyWithdraw_VALUE = 100009;
    /**
     * <pre>
     * 通知货币更新
     * </pre>
     *
     * <code>InnerNotifyCurrencyUpdate = 100010;</code>
     */
    public static final int InnerNotifyCurrencyUpdate_VALUE = 100010;
    /**
     * <pre>
     * 通知更新游戏信息
     * </pre>
     *
     * <code>InnerNotifyUpdateGameInfo = 100011;</code>
     */
    public static final int InnerNotifyUpdateGameInfo_VALUE = 100011;
    /**
     * <pre>
     * 获取游戏列表信息
     * </pre>
     *
     * <code>InnerGetGameList = 100012;</code>
     */
    public static final int InnerGetGameList_VALUE = 100012;
    /**
     * <pre>
     * 通知游戏数据更新
     * </pre>
     *
     * <code>InnerNotifyGameDataUpdate = 100013;</code>
     */
    public static final int InnerNotifyGameDataUpdate_VALUE = 100013;
    /**
     * <pre>
     *登录 200001
     * </pre>
     *
     * <code>ReqRegister = 200001;</code>
     */
    public static final int ReqRegister_VALUE = 200001;
    /**
     * <pre>
     * 请求登录
     * </pre>
     *
     * <code>ReqLogin = 200002;</code>
     */
    public static final int ReqLogin_VALUE = 200002;
    /**
     * <pre>
     * 返回登录
     * </pre>
     *
     * <code>ResLogin = 200003;</code>
     */
    public static final int ResLogin_VALUE = 200003;
    /**
     * <pre>
     * 请求邮箱验证码
     * </pre>
     *
     * <code>ReqMailVerifyCode = 200004;</code>
     */
    public static final int ReqMailVerifyCode_VALUE = 200004;
    /**
     * <pre>
     * 返回邮箱验证码
     * </pre>
     *
     * <code>ResMailVerifyCode = 200005;</code>
     */
    public static final int ResMailVerifyCode_VALUE = 200005;
    /**
     * <pre>
     * 请求未登录
     * </pre>
     *
     * <code>ReqNotLoggedIn = 200006;</code>
     */
    public static final int ReqNotLoggedIn_VALUE = 200006;
    /**
     * <pre>
     * 返回未登录
     * </pre>
     *
     * <code>ResNotLoggedIn = 200007;</code>
     */
    public static final int ResNotLoggedIn_VALUE = 200007;
    /**
     * <pre>
     * 请求网站模板
     * </pre>
     *
     * <code>ReqWebSiteModel = 200008;</code>
     */
    public static final int ReqWebSiteModel_VALUE = 200008;
    /**
     * <pre>
     * 返回网站模板
     * </pre>
     *
     * <code>ResWebSiteModel = 200009;</code>
     */
    public static final int ResWebSiteModel_VALUE = 200009;
    /**
     * <pre>
     * 请求电话验证码
     * </pre>
     *
     * <code>ReqPhoneVerifyCode = 200010;</code>
     */
    public static final int ReqPhoneVerifyCode_VALUE = 200010;
    /**
     * <pre>
     * 返回电话验证码
     * </pre>
     *
     * <code>ResPhoneVerifyCode = 200011;</code>
     */
    public static final int ResPhoneVerifyCode_VALUE = 200011;
    /**
     * <pre>
     * 请求注册认证
     * </pre>
     *
     * <code>ReqRegisterAuth = 200012;</code>
     */
    public static final int ReqRegisterAuth_VALUE = 200012;
    /**
     * <pre>
     * 返回注册认证
     * </pre>
     *
     * <code>ResRegisterAuth = 200013;</code>
     */
    public static final int ResRegisterAuth_VALUE = 200013;
    /**
     * <pre>
     * 请求渠道安装
     * </pre>
     *
     * <code>ReqChannelInstall = 200014;</code>
     */
    public static final int ReqChannelInstall_VALUE = 200014;
    /**
     * <pre>
     * 返回渠道安装
     * </pre>
     *
     * <code>ResChannelInstall = 200015;</code>
     */
    public static final int ResChannelInstall_VALUE = 200015;
    /**
     * <pre>
     *大厅 400001-400200
     * </pre>
     *
     * <code>ReqPlayerEntryHall = 400001;</code>
     */
    public static final int ReqPlayerEntryHall_VALUE = 400001;
    /**
     * <pre>
     * 返回玩家进入大厅
     * </pre>
     *
     * <code>ResPlayerEntryHall = 400002;</code>
     */
    public static final int ResPlayerEntryHall_VALUE = 400002;
    /**
     * <pre>
     * 请求玩家登出
     * </pre>
     *
     * <code>ReqPlayerSignOut = 400003;</code>
     */
    public static final int ReqPlayerSignOut_VALUE = 400003;
    /**
     * <pre>
     * 返回玩家登出
     * </pre>
     *
     * <code>ResPlayerSignOut = 400004;</code>
     */
    public static final int ResPlayerSignOut_VALUE = 400004;
    /**
     * <pre>
     * 请求头像更换
     * </pre>
     *
     * <code>ReqHeadChange = 400005;</code>
     */
    public static final int ReqHeadChange_VALUE = 400005;
    /**
     * <pre>
     * 返回头像更换
     * </pre>
     *
     * <code>ResHeadChange = 400006;</code>
     */
    public static final int ResHeadChange_VALUE = 400006;
    /**
     * <pre>
     * 请求绑定账号
     * </pre>
     *
     * <code>ReqBindAccount = 400007;</code>
     */
    public static final int ReqBindAccount_VALUE = 400007;
    /**
     * <pre>
     * 返回绑定账号
     * </pre>
     *
     * <code>ResBindAccount = 400008;</code>
     */
    public static final int ResBindAccount_VALUE = 400008;
    /**
     * <pre>
     * 请求重置密码
     * </pre>
     *
     * <code>ReqResetPassword = 400009;</code>
     */
    public static final int ReqResetPassword_VALUE = 400009;
    /**
     * <pre>
     * 返回重置密码
     * </pre>
     *
     * <code>ResResetPassword = 400010;</code>
     */
    public static final int ResResetPassword_VALUE = 400010;
    /**
     * <pre>
     * 请求名字修改
     * </pre>
     *
     * <code>ReqNameModify = 400011;</code>
     */
    public static final int ReqNameModify_VALUE = 400011;
    /**
     * <pre>
     * 返回名字修改
     * </pre>
     *
     * <code>ResNameModify = 400012;</code>
     */
    public static final int ResNameModify_VALUE = 400012;
    /**
     * <pre>
     * 请求用户数据
     * </pre>
     *
     * <code>ReqAccountData = 400013;</code>
     */
    public static final int ReqAccountData_VALUE = 400013;
    /**
     * <pre>
     * 返回用户数据
     * </pre>
     *
     * <code>ResAccountData = 400014;</code>
     */
    public static final int ResAccountData_VALUE = 400014;
    /**
     * <pre>
     * 请求邮件数据
     * </pre>
     *
     * <code>ReqInboxData = 400015;</code>
     */
    public static final int ReqInboxData_VALUE = 400015;
    /**
     * <pre>
     * 返回邮件数据
     * </pre>
     *
     * <code>ResInboxData = 400016;</code>
     */
    public static final int ResInboxData_VALUE = 400016;
    /**
     * <pre>
     * 请求读邮件
     * </pre>
     *
     * <code>ReqReadInBox = 400017;</code>
     */
    public static final int ReqReadInBox_VALUE = 400017;
    /**
     * <pre>
     * 返回读邮件
     * </pre>
     *
     * <code>ResReadInBox = 400018;</code>
     */
    public static final int ResReadInBox_VALUE = 400018;
    /**
     * <pre>
     * 请求删除邮件
     * </pre>
     *
     * <code>ReqDeleteInbox = 400019;</code>
     */
    public static final int ReqDeleteInbox_VALUE = 400019;
    /**
     * <pre>
     * 返回删除邮件
     * </pre>
     *
     * <code>ResDeleteInbox = 400020;</code>
     */
    public static final int ResDeleteInbox_VALUE = 400020;
    /**
     * <pre>
     * 请求切换货币
     * </pre>
     *
     * <code>ReqChangeCurrency = 400021;</code>
     */
    public static final int ReqChangeCurrency_VALUE = 400021;
    /**
     * <pre>
     * 返回切换货币
     * </pre>
     *
     * <code>ResChangeCurrency = 400022;</code>
     */
    public static final int ResChangeCurrency_VALUE = 400022;
    /**
     * <pre>
     * 请求显示法币
     * </pre>
     *
     * <code>ReqViewInFiat = 400023;</code>
     */
    public static final int ReqViewInFiat_VALUE = 400023;
    /**
     * <pre>
     * 返回显示法币
     * </pre>
     *
     * <code>ResViewInFiat = 400024;</code>
     */
    public static final int ResViewInFiat_VALUE = 400024;
    /**
     * <pre>
     * 请求游戏列表数据
     * </pre>
     *
     * <code>ReqCasinoData = 400027;</code>
     */
    public static final int ReqCasinoData_VALUE = 400027;
    /**
     * <pre>
     * 返回游戏列表数据
     * </pre>
     *
     * <code>ResCasinoData = 400028;</code>
     */
    public static final int ResCasinoData_VALUE = 400028;
    /**
     * <pre>
     * 请求游戏频道数据
     * </pre>
     *
     * <code>ReqGameChannelData = 400029;</code>
     */
    public static final int ReqGameChannelData_VALUE = 400029;
    /**
     * <pre>
     * 返回游戏频道数据
     * </pre>
     *
     * <code>ResGameChannelData = 400030;</code>
     */
    public static final int ResGameChannelData_VALUE = 400030;
    /**
     * <pre>
     * 请求搜索游戏
     * </pre>
     *
     * <code>ReqSearchGameData = 400031;</code>
     */
    public static final int ReqSearchGameData_VALUE = 400031;
    /**
     * <pre>
     * 返回搜索游戏
     * </pre>
     *
     * <code>ResSearchGameData = 400032;</code>
     */
    public static final int ResSearchGameData_VALUE = 400032;
    /**
     * <pre>
     * 请求Enable2FA数据
     * </pre>
     *
     * <code>ReqEnable2FAData = 400033;</code>
     */
    public static final int ReqEnable2FAData_VALUE = 400033;
    /**
     * <pre>
     * 返回Enable2FA数据
     * </pre>
     *
     * <code>ResEnable2FAData = 400034;</code>
     */
    public static final int ResEnable2FAData_VALUE = 400034;
    /**
     * <pre>
     * 请求2FA验证码
     * </pre>
     *
     * <code>Req2FAVerificationCode = 400035;</code>
     */
    public static final int Req2FAVerificationCode_VALUE = 400035;
    /**
     * <pre>
     * 返回2FA验证码
     * </pre>
     *
     * <code>Res2FAVerificationCode = 400036;</code>
     */
    public static final int Res2FAVerificationCode_VALUE = 400036;
    /**
     * <pre>
     * 请求进入三方游戏
     * </pre>
     *
     * <code>ReqEntryAgentGame = 400037;</code>
     */
    public static final int ReqEntryAgentGame_VALUE = 400037;
    /**
     * <pre>
     * 返回进入三方游戏
     * </pre>
     *
     * <code>ResEntryAgentGame = 400038;</code>
     */
    public static final int ResEntryAgentGame_VALUE = 400038;
    /**
     * <pre>
     * 请求收藏、点赞
     * </pre>
     *
     * <code>ReqUserInteraction = 400039;</code>
     */
    public static final int ReqUserInteraction_VALUE = 400039;
    /**
     * <pre>
     * 返回收藏、点赞
     * </pre>
     *
     * <code>ResUserInteraction = 400040;</code>
     */
    public static final int ResUserInteraction_VALUE = 400040;
    /**
     * <pre>
     * 请求游戏opt数据
     * </pre>
     *
     * <code>ReqGameOptData = 400041;</code>
     */
    public static final int ReqGameOptData_VALUE = 400041;
    /**
     * <pre>
     * 返回游戏opt数据
     * </pre>
     *
     * <code>ResGameOptData = 400042;</code>
     */
    public static final int ResGameOptData_VALUE = 400042;
    /**
     * <pre>
     * 请求获取排行数据
     * </pre>
     *
     * <code>ReqGetRankData = 400043;</code>
     */
    public static final int ReqGetRankData_VALUE = 400043;
    /**
     * <pre>
     * 返回获取排行数据
     * </pre>
     *
     * <code>ResGetRankData = 400044;</code>
     */
    public static final int ResGetRankData_VALUE = 400044;
    /**
     * <pre>
     * 请求最近最大数据
     * </pre>
     *
     * <code>ReqRecentBigWinsData = 400045;</code>
     */
    public static final int ReqRecentBigWinsData_VALUE = 400045;
    /**
     * <pre>
     * 返回最近最大数据
     * </pre>
     *
     * <code>ResRecentBigWinsData = 400046;</code>
     */
    public static final int ResRecentBigWinsData_VALUE = 400046;
    /**
     * <pre>
     * 请求解绑三方
     * </pre>
     *
     * <code>ReqUnbindThreeParty = 400051;</code>
     */
    public static final int ReqUnbindThreeParty_VALUE = 400051;
    /**
     * <pre>
     * 返回解绑三方
     * </pre>
     *
     * <code>ResUnbindThreeParty = 400052;</code>
     */
    public static final int ResUnbindThreeParty_VALUE = 400052;
    /**
     * <pre>
     * 请求设置隐私
     * </pre>
     *
     * <code>ReqSetPrivacyPreferences = 400053;</code>
     */
    public static final int ReqSetPrivacyPreferences_VALUE = 400053;
    /**
     * <pre>
     * 返回设置隐私
     * </pre>
     *
     * <code>ResSetPrivacyPreferences = 400054;</code>
     */
    public static final int ResSetPrivacyPreferences_VALUE = 400054;
    /**
     * <pre>
     * 请求绑定三方
     * </pre>
     *
     * <code>ReqBindThreeParty = 400055;</code>
     */
    public static final int ReqBindThreeParty_VALUE = 400055;
    /**
     * <pre>
     * 返回绑定三方
     * </pre>
     *
     * <code>ResBindThreeParty = 400056;</code>
     */
    public static final int ResBindThreeParty_VALUE = 400056;
    /**
     * <pre>
     * 请求支付方式管理数据
     * </pre>
     *
     * <code>ReqPaymentMethodsData = 400057;</code>
     */
    public static final int ReqPaymentMethodsData_VALUE = 400057;
    /**
     * <pre>
     * 返回支付方式管理数据
     * </pre>
     *
     * <code>ResPaymentMethodsData = 400058;</code>
     */
    public static final int ResPaymentMethodsData_VALUE = 400058;
    /**
     * <pre>
     * 请求查看sessions数据
     * </pre>
     *
     * <code>ReqCheckSessionData = 400059;</code>
     */
    public static final int ReqCheckSessionData_VALUE = 400059;
    /**
     * <pre>
     * 返回查看sessions数据
     * </pre>
     *
     * <code>ResCheckSessionData = 400060;</code>
     */
    public static final int ResCheckSessionData_VALUE = 400060;
    /**
     * <pre>
     * 请求切换语言
     * </pre>
     *
     * <code>ReqChangeLanguage = 400061;</code>
     */
    public static final int ReqChangeLanguage_VALUE = 400061;
    /**
     * <pre>
     * 返回切换语言
     * </pre>
     *
     * <code>ResChangeLanguage = 400062;</code>
     */
    public static final int ResChangeLanguage_VALUE = 400062;
    /**
     * <pre>
     * 请求刷新玩家数据
     * </pre>
     *
     * <code>ReqRefreshPlayerData = 400063;</code>
     */
    public static final int ReqRefreshPlayerData_VALUE = 400063;
    /**
     * <pre>
     * 返回刷新玩家数据
     * </pre>
     *
     * <code>ResRefreshPlayerData = 400064;</code>
     */
    public static final int ResRefreshPlayerData_VALUE = 400064;
    /**
     * <pre>
     * 请求验证账号
     * </pre>
     *
     * <code>ReqVerifyAccount = 400065;</code>
     */
    public static final int ReqVerifyAccount_VALUE = 400065;
    /**
     * <pre>
     * 返回验证账号
     * </pre>
     *
     * <code>ResVerifyAccount = 400066;</code>
     */
    public static final int ResVerifyAccount_VALUE = 400066;
    /**
     * <pre>
     * 请求更换账号
     * </pre>
     *
     * <code>ReqChangeAccount = 400067;</code>
     */
    public static final int ReqChangeAccount_VALUE = 400067;
    /**
     * <pre>
     * 返回更换账号
     * </pre>
     *
     * <code>ResChangeAccount = 400068;</code>
     */
    public static final int ResChangeAccount_VALUE = 400068;
    /**
     * <pre>
     * 请求kyc认证
     * </pre>
     *
     * <code>ReqKycAuth = 400069;</code>
     */
    public static final int ReqKycAuth_VALUE = 400069;
    /**
     * <pre>
     * 请求kyc认证
     * </pre>
     *
     * <code>ResKycAuth = 400070;</code>
     */
    public static final int ResKycAuth_VALUE = 400070;
    /**
     * <pre>
     * 请求添加账号
     * </pre>
     *
     * <code>ReqAddAccount = 400071;</code>
     */
    public static final int ReqAddAccount_VALUE = 400071;
    /**
     * <pre>
     * 返回添加账号
     * </pre>
     *
     * <code>ResAddAccount = 400072;</code>
     */
    public static final int ResAddAccount_VALUE = 400072;
    /**
     * <pre>
     * 请求兑换码奖励
     * </pre>
     *
     * <code>ReqRedemptionCodeReward = 400073;</code>
     */
    public static final int ReqRedemptionCodeReward_VALUE = 400073;
    /**
     * <pre>
     * 返回兑换码奖励
     * </pre>
     *
     * <code>ResRedemptionCodeReward = 400074;</code>
     */
    public static final int ResRedemptionCodeReward_VALUE = 400074;
    /**
     * <pre>
     * 请求充值奖励开关
     * </pre>
     *
     * <code>ReqRechargeBonusOpen = 400075;</code>
     */
    public static final int ReqRechargeBonusOpen_VALUE = 400075;
    /**
     * <pre>
     * 返回充值奖励开关
     * </pre>
     *
     * <code>ResRechargeBonusOpen = 400076;</code>
     */
    public static final int ResRechargeBonusOpen_VALUE = 400076;
    /**
     * <pre>
     *钱包 400201-400300
     * </pre>
     *
     * <code>ReqBetHistoryData = 400201;</code>
     */
    public static final int ReqBetHistoryData_VALUE = 400201;
    /**
     * <pre>
     * 返回下注历史数据
     * </pre>
     *
     * <code>ResBetHistoryData = 400202;</code>
     */
    public static final int ResBetHistoryData_VALUE = 400202;
    /**
     * <pre>
     * 请求交易数据
     * </pre>
     *
     * <code>ReqTransactionData = 400203;</code>
     */
    public static final int ReqTransactionData_VALUE = 400203;
    /**
     * <pre>
     * 返回交易数据
     * </pre>
     *
     * <code>ResTransactionData = 400204;</code>
     */
    public static final int ResTransactionData_VALUE = 400204;
    /**
     * <pre>
     *充值 400301-400400
     * </pre>
     *
     * <code>ReqDepositWithdrawData = 400301;</code>
     */
    public static final int ReqDepositWithdrawData_VALUE = 400301;
    /**
     * <pre>
     * 返回充提货币数据
     * </pre>
     *
     * <code>ResDepositWithdrawData = 400302;</code>
     */
    public static final int ResDepositWithdrawData_VALUE = 400302;
    /**
     * <pre>
     * 请求充值数据
     * </pre>
     *
     * <code>ReqDepositData = 400303;</code>
     */
    public static final int ReqDepositData_VALUE = 400303;
    /**
     * <pre>
     * 请求充值数据
     * </pre>
     *
     * <code>ResDepositData = 400304;</code>
     */
    public static final int ResDepositData_VALUE = 400304;
    /**
     * <pre>
     * 请求提现数据
     * </pre>
     *
     * <code>ReqWithdrawData = 400305;</code>
     */
    public static final int ReqWithdrawData_VALUE = 400305;
    /**
     * <pre>
     * 返回提现数据
     * </pre>
     *
     * <code>ResWithdrawData = 400306;</code>
     */
    public static final int ResWithdrawData_VALUE = 400306;
    /**
     * <pre>
     * 请求创建充值订单
     * </pre>
     *
     * <code>ReqCreateRechargeOrder = 400307;</code>
     */
    public static final int ReqCreateRechargeOrder_VALUE = 400307;
    /**
     * <pre>
     * 返回创建充值订单
     * </pre>
     *
     * <code>ResCreateRechargeOrder = 400308;</code>
     */
    public static final int ResCreateRechargeOrder_VALUE = 400308;
    /**
     * <pre>
     * 请求创建提现订单
     * </pre>
     *
     * <code>ReqCreateWithdrawOrder = 400309;</code>
     */
    public static final int ReqCreateWithdrawOrder_VALUE = 400309;
    /**
     * <pre>
     * 返回创建提现订单
     * </pre>
     *
     * <code>ResCreateWithdrawOrder = 400310;</code>
     */
    public static final int ResCreateWithdrawOrder_VALUE = 400310;
    /**
     * <pre>
     * 请求添加删除提现账户
     * </pre>
     *
     * <code>ReqAddDeleteWithdrawAccount = 400311;</code>
     */
    public static final int ReqAddDeleteWithdrawAccount_VALUE = 400311;
    /**
     * <pre>
     * 返回添加删除提现账户
     * </pre>
     *
     * <code>ResAddDeleteWithdrawAccount = 400312;</code>
     */
    public static final int ResAddDeleteWithdrawAccount_VALUE = 400312;
    /**
     * <pre>
     *推广 400401-400500
     * </pre>
     *
     * <code>ReqBindSuperior = 400401;</code>
     */
    public static final int ReqBindSuperior_VALUE = 400401;
    /**
     * <pre>
     * 返回绑定上级
     * </pre>
     *
     * <code>ResBindSuperior = 400402;</code>
     */
    public static final int ResBindSuperior_VALUE = 400402;
    /**
     * <pre>
     * 请求仪表盘数据
     * </pre>
     *
     * <code>ReqDashboardData = 400403;</code>
     */
    public static final int ReqDashboardData_VALUE = 400403;
    /**
     * <pre>
     * 返回仪表盘数据
     * </pre>
     *
     * <code>ResDashboardData = 400404;</code>
     */
    public static final int ResDashboardData_VALUE = 400404;
    /**
     * <pre>
     * 请求我的奖励数据
     * </pre>
     *
     * <code>ReqMyRewardData = 400405;</code>
     */
    public static final int ReqMyRewardData_VALUE = 400405;
    /**
     * <pre>
     * 返回我的奖励数据
     * </pre>
     *
     * <code>ResMyRewardData = 400406;</code>
     */
    public static final int ResMyRewardData_VALUE = 400406;
    /**
     * <pre>
     * 请求佣金奖励数据
     * </pre>
     *
     * <code>ReqCommissionRewardData = 400407;</code>
     */
    public static final int ReqCommissionRewardData_VALUE = 400407;
    /**
     * <pre>
     * 返回佣金奖励数据
     * </pre>
     *
     * <code>ResCommissionRewardData = 400408;</code>
     */
    public static final int ResCommissionRewardData_VALUE = 400408;
    /**
     * <pre>
     * 请求推广奖励数据
     * </pre>
     *
     * <code>ReqReferralRewardData = 400409;</code>
     */
    public static final int ReqReferralRewardData_VALUE = 400409;
    /**
     * <pre>
     * 返回推广奖励数据
     * </pre>
     *
     * <code>ResReferralRewardData = 400410;</code>
     */
    public static final int ResReferralRewardData_VALUE = 400410;
    /**
     * <pre>
     * 请求推广码和下级数据
     * </pre>
     *
     * <code>ReqReferralCodeAndFriendsData = 400411;</code>
     */
    public static final int ReqReferralCodeAndFriendsData_VALUE = 400411;
    /**
     * <pre>
     * 返回推广码和下级数据
     * </pre>
     *
     * <code>ResReferralCodeAndFriendsData = 400412;</code>
     */
    public static final int ResReferralCodeAndFriendsData_VALUE = 400412;
    /**
     * <pre>
     * 请求创建推广码
     * </pre>
     *
     * <code>ReqCreateReferralCode = 400413;</code>
     */
    public static final int ReqCreateReferralCode_VALUE = 400413;
    /**
     * <pre>
     * 返回创建推广码
     * </pre>
     *
     * <code>ResCreateReferralCode = 400414;</code>
     */
    public static final int ResCreateReferralCode_VALUE = 400414;
    /**
     * <pre>
     * 请求推广码数据
     * </pre>
     *
     * <code>ReqReferralCodeData = 400415;</code>
     */
    public static final int ReqReferralCodeData_VALUE = 400415;
    /**
     * <pre>
     * 返回推广码数据
     * </pre>
     *
     * <code>ResReferralCodeData = 400416;</code>
     */
    public static final int ResReferralCodeData_VALUE = 400416;
    /**
     * <pre>
     * 请求下级数据
     * </pre>
     *
     * <code>ReqFriendsData = 400417;</code>
     */
    public static final int ReqFriendsData_VALUE = 400417;
    /**
     * <pre>
     * 返回下级数据
     * </pre>
     *
     * <code>ResFriendsData = 400418;</code>
     */
    public static final int ResFriendsData_VALUE = 400418;
    /**
     * <pre>
     * 请求提现到钱包
     * </pre>
     *
     * <code>ReqWithdrawToWallet = 400419;</code>
     */
    public static final int ReqWithdrawToWallet_VALUE = 400419;
    /**
     * <pre>
     * 返回提现到钱包
     * </pre>
     *
     * <code>ResWithdrawToWallet = 400420;</code>
     */
    public static final int ResWithdrawToWallet_VALUE = 400420;
    /**
     * <pre>
     * 请求历史数据
     * </pre>
     *
     * <code>ReqHistoryData = 400421;</code>
     */
    public static final int ReqHistoryData_VALUE = 400421;
    /**
     * <pre>
     * 返回历史数据
     * </pre>
     *
     * <code>ResHistoryData = 400422;</code>
     */
    public static final int ResHistoryData_VALUE = 400422;
    /**
     * <pre>
     * 请求提现数据
     * </pre>
     *
     * <code>ReqAffiliateWithdrawData = 400423;</code>
     */
    public static final int ReqAffiliateWithdrawData_VALUE = 400423;
    /**
     * <pre>
     * 返回提现数据
     * </pre>
     *
     * <code>ResAffiliateWithdrawData = 400424;</code>
     */
    public static final int ResAffiliateWithdrawData_VALUE = 400424;
    /**
     * <pre>
     * 请求团长奖励
     * </pre>
     *
     * <code>ReqTeamRewardData = 400425;</code>
     */
    public static final int ReqTeamRewardData_VALUE = 400425;
    /**
     * <pre>
     * 返回团长奖励
     * </pre>
     *
     * <code>ResTeamRewardData = 400426;</code>
     */
    public static final int ResTeamRewardData_VALUE = 400426;
    /**
     * <pre>
     * 请求团长数据
     * </pre>
     *
     * <code>ReqTeamData = 400427;</code>
     */
    public static final int ReqTeamData_VALUE = 400427;
    /**
     * <pre>
     * 返回团长数据
     * </pre>
     *
     * <code>ResTeamData = 400428;</code>
     */
    public static final int ResTeamData_VALUE = 400428;
    /**
     * <pre>
     * 请求三级奖励数据
     * </pre>
     *
     * <code>ReqThreeLevelRewardData = 400429;</code>
     */
    public static final int ReqThreeLevelRewardData_VALUE = 400429;
    /**
     * <pre>
     * 返回三级奖励数据
     * </pre>
     *
     * <code>ResThreeLevelRewardData = 400430;</code>
     */
    public static final int ResThreeLevelRewardData_VALUE = 400430;
    /**
     * <pre>
     * 请求三级数据
     * </pre>
     *
     * <code>ReqThreeLevelData = 400431;</code>
     */
    public static final int ReqThreeLevelData_VALUE = 400431;
    /**
     * <pre>
     * 返回三级数据
     * </pre>
     *
     * <code>ResThreeLevelData = 400432;</code>
     */
    public static final int ResThreeLevelData_VALUE = 400432;
    /**
     * <pre>
     *配置数据 400501-400600
     * </pre>
     *
     * <code>ReqHelpCenterData = 400501;</code>
     */
    public static final int ReqHelpCenterData_VALUE = 400501;
    /**
     * <pre>
     * 返回帮助中心数据
     * </pre>
     *
     * <code>ResHelpCenterData = 400502;</code>
     */
    public static final int ResHelpCenterData_VALUE = 400502;
    /**
     * <pre>
     * 请求新闻数据
     * </pre>
     *
     * <code>ReqNewsData = 400503;</code>
     */
    public static final int ReqNewsData_VALUE = 400503;
    /**
     * <pre>
     * 返回新闻数据
     * </pre>
     *
     * <code>ResNewsData = 400504;</code>
     */
    public static final int ResNewsData_VALUE = 400504;
    /**
     * <pre>
     * 请求配置数据
     * </pre>
     *
     * <code>ReqConfigData = 400505;</code>
     */
    public static final int ReqConfigData_VALUE = 400505;
    /**
     * <pre>
     * 返回配置数据
     * </pre>
     *
     * <code>ResConfigData = 400506;</code>
     */
    public static final int ResConfigData_VALUE = 400506;
    /**
     * <pre>
     *活动数据 400601-400700
     * </pre>
     *
     * <code>ReqPromotionsData = 400601;</code>
     */
    public static final int ReqPromotionsData_VALUE = 400601;
    /**
     * <pre>
     * 返回活动数据
     * </pre>
     *
     * <code>ResPromotionsData = 400602;</code>
     */
    public static final int ResPromotionsData_VALUE = 400602;
    /**
     * <pre>
     * 请求幸运转盘数据
     * </pre>
     *
     * <code>ReqLuckSpinData = 400603;</code>
     */
    public static final int ReqLuckSpinData_VALUE = 400603;
    /**
     * <pre>
     * 返回幸运转盘数据
     * </pre>
     *
     * <code>ResLuckSpinData = 400604;</code>
     */
    public static final int ResLuckSpinData_VALUE = 400604;
    /**
     * <pre>
     * 请求点击幸运转盘
     * </pre>
     *
     * <code>ReqClickLuckSpin = 400605;</code>
     */
    public static final int ReqClickLuckSpin_VALUE = 400605;
    /**
     * <pre>
     * 返回点击幸运转盘
     * </pre>
     *
     * <code>ResClickLuckSpin = 400606;</code>
     */
    public static final int ResClickLuckSpin_VALUE = 400606;
    /**
     * <pre>
     * 请求幸运推广提现
     * </pre>
     *
     * <code>ReqLuckSpinReferralWithdraw = 400607;</code>
     */
    public static final int ReqLuckSpinReferralWithdraw_VALUE = 400607;
    /**
     * <pre>
     * 返回幸运推广提现
     * </pre>
     *
     * <code>ResLuckSpinReferralWithdraw = 400608;</code>
     */
    public static final int ResLuckSpinReferralWithdraw_VALUE = 400608;
    /**
     * <pre>
     * 请求bonus数据
     * </pre>
     *
     * <code>ReqBonusData = 400609;</code>
     */
    public static final int ReqBonusData_VALUE = 400609;
    /**
     * <pre>
     * 返回bonus数据
     * </pre>
     *
     * <code>ResBonusData = 400610;</code>
     */
    public static final int ResBonusData_VALUE = 400610;
    /**
     * <pre>
     * 请求vipBonus奖励领取
     * </pre>
     *
     * <code>ReqVipBonusRewardsReceive = 400611;</code>
     */
    public static final int ReqVipBonusRewardsReceive_VALUE = 400611;
    /**
     * <pre>
     * 返回vipBonus奖励领取
     * </pre>
     *
     * <code>ResVipBonusRewardsReceive = 400612;</code>
     */
    public static final int ResVipBonusRewardsReceive_VALUE = 400612;
    /**
     * <pre>
     * 请求bonus明细数据
     * </pre>
     *
     * <code>ReqBonusDetailsData = 400613;</code>
     */
    public static final int ReqBonusDetailsData_VALUE = 400613;
    /**
     * <pre>
     * 返回bonus明细数据
     * </pre>
     *
     * <code>ResBonusDetailsData = 400614;</code>
     */
    public static final int ResBonusDetailsData_VALUE = 400614;
    /**
     * <pre>
     * 请求bonus交易数据
     * </pre>
     *
     * <code>ReqBonusTransactionsData = 400615;</code>
     */
    public static final int ReqBonusTransactionsData_VALUE = 400615;
    /**
     * <pre>
     * 返回bonus交易数据
     * </pre>
     *
     * <code>ResBonusTransactionsData = 400616;</code>
     */
    public static final int ResBonusTransactionsData_VALUE = 400616;
    /**
     * <pre>
     * 请求每日竞赛数据
     * </pre>
     *
     * <code>ReqDailyContestData = 400617;</code>
     */
    public static final int ReqDailyContestData_VALUE = 400617;
    /**
     * <pre>
     * 返回每日竞赛数据
     * </pre>
     *
     * <code>ResDailyContestData = 400618;</code>
     */
    public static final int ResDailyContestData_VALUE = 400618;
    /**
     * <pre>
     * 请求每日竞赛历史数据
     * </pre>
     *
     * <code>ReqDailyContestHistoryData = 400619;</code>
     */
    public static final int ReqDailyContestHistoryData_VALUE = 400619;
    /**
     * <pre>
     * 返回每日竞赛历史数据
     * </pre>
     *
     * <code>ResDailyContestHistoryData = 400620;</code>
     */
    public static final int ResDailyContestHistoryData_VALUE = 400620;
    /**
     * <pre>
     * 请求每周抽奖数据
     * </pre>
     *
     * <code>ReqWeeklyRaffleData = 400621;</code>
     */
    public static final int ReqWeeklyRaffleData_VALUE = 400621;
    /**
     * <pre>
     * 返回每周抽奖数据
     * </pre>
     *
     * <code>ResWeeklyRaffleData = 400622;</code>
     */
    public static final int ResWeeklyRaffleData_VALUE = 400622;
    /**
     * <pre>
     * 请求每周抽奖我的数据
     * </pre>
     *
     * <code>ReqWeeklyRaffleMyTicketsData = 400623;</code>
     */
    public static final int ReqWeeklyRaffleMyTicketsData_VALUE = 400623;
    /**
     * <pre>
     * 返回每周抽奖我的数据
     * </pre>
     *
     * <code>ResWeeklyRaffleMyTicketsData = 400624;</code>
     */
    public static final int ResWeeklyRaffleMyTicketsData_VALUE = 400624;
    /**
     * <pre>
     * 请求每周抽奖结果数据
     * </pre>
     *
     * <code>ReqWeeklyRaffleResultData = 400625;</code>
     */
    public static final int ReqWeeklyRaffleResultData_VALUE = 400625;
    /**
     * <pre>
     * 返回每周抽奖结果数据
     * </pre>
     *
     * <code>ResWeeklyRaffleResultData = 400626;</code>
     */
    public static final int ResWeeklyRaffleResultData_VALUE = 400626;
    /**
     * <pre>
     * 请求领取活动奖励
     * </pre>
     *
     * <code>ReqReceivePromotionsReward = 400627;</code>
     */
    public static final int ReqReceivePromotionsReward_VALUE = 400627;
    /**
     * <pre>
     * 返回领取活动奖励
     * </pre>
     *
     * <code>ResReceivePromotionsReward = 400628;</code>
     */
    public static final int ResReceivePromotionsReward_VALUE = 400628;
    /**
     * <pre>
     * 请求活动报名
     * </pre>
     *
     * <code>ReqActivitySignUp = 400629;</code>
     */
    public static final int ReqActivitySignUp_VALUE = 400629;
    /**
     * <pre>
     * 返回活动报名
     * </pre>
     *
     * <code>ResActivitySignUp = 400630;</code>
     */
    public static final int ResActivitySignUp_VALUE = 400630;
    /**
     * <pre>
     * 请求活动排行数据
     * </pre>
     *
     * <code>ReqActivityRankData = 400631;</code>
     */
    public static final int ReqActivityRankData_VALUE = 400631;
    /**
     * <pre>
     * 请求活动排行数据
     * </pre>
     *
     * <code>ResActivityRankData = 400632;</code>
     */
    public static final int ResActivityRankData_VALUE = 400632;
    /**
     * <pre>
     * 请求活动数据
     * </pre>
     *
     * <code>ReqActivityData = 400633;</code>
     */
    public static final int ReqActivityData_VALUE = 400633;
    /**
     * <pre>
     * 返回活动雨数据
     * </pre>
     *
     * <code>ResActivityData = 400634;</code>
     */
    public static final int ResActivityData_VALUE = 400634;
    /**
     * <pre>
     * 请求领取红包
     * </pre>
     *
     * <code>ReqReceiveRedEnvelope = 400635;</code>
     */
    public static final int ReqReceiveRedEnvelope_VALUE = 400635;
    /**
     * <pre>
     * 返回领取红包
     * </pre>
     *
     * <code>ResReceiveRedEnvelope = 400636;</code>
     */
    public static final int ResReceiveRedEnvelope_VALUE = 400636;
    /**
     * <pre>
     * 请求领取推荐宝箱
     * </pre>
     *
     * <code>ReqReceiveRewardBox = 400637;</code>
     */
    public static final int ReqReceiveRewardBox_VALUE = 400637;
    /**
     * <pre>
     * 返回领取推荐宝箱
     * </pre>
     *
     * <code>ResReceiveRewardBox = 400638;</code>
     */
    public static final int ResReceiveRewardBox_VALUE = 400638;
    /**
     * <pre>
     * 请求领取神秘奖金
     * </pre>
     *
     * <code>ReqReceiveMysteryBonus = 400639;</code>
     */
    public static final int ReqReceiveMysteryBonus_VALUE = 400639;
    /**
     * <pre>
     * 返回领取神秘奖金
     * </pre>
     *
     * <code>ResReceiveMysteryBonus = 400640;</code>
     */
    public static final int ResReceiveMysteryBonus_VALUE = 400640;
    /**
     * <pre>
     * 请求领取存钱罐
     * </pre>
     *
     * <code>ReqReceivePiggyBank = 400641;</code>
     */
    public static final int ReqReceivePiggyBank_VALUE = 400641;
    /**
     * <pre>
     * 返回领取存钱罐
     * </pre>
     *
     * <code>ResReceivePiggyBank = 400642;</code>
     */
    public static final int ResReceivePiggyBank_VALUE = 400642;
    /**
     * <pre>
     * 请求推荐宝箱下级数据
     * </pre>
     *
     * <code>ReqRewardBoxSubordinateData = 400643;</code>
     */
    public static final int ReqRewardBoxSubordinateData_VALUE = 400643;
    /**
     * <pre>
     * 返回推荐宝箱下级数据
     * </pre>
     *
     * <code>ResRewardBoxSubordinateData = 400644;</code>
     */
    public static final int ResRewardBoxSubordinateData_VALUE = 400644;
    /**
     * <pre>
     * 请求vip数据
     * </pre>
     *
     * <code>ReqVipData = 400645;</code>
     */
    public static final int ReqVipData_VALUE = 400645;
    /**
     * <pre>
     * 返回vip数据
     * </pre>
     *
     * <code>ResVipData = 400646;</code>
     */
    public static final int ResVipData_VALUE = 400646;
    /**
     * <pre>
     * 请求vip签到
     * </pre>
     *
     * <code>ReqVipSignIn = 400647;</code>
     */
    public static final int ReqVipSignIn_VALUE = 400647;
    /**
     * <pre>
     * 返回vip签到
     * </pre>
     *
     * <code>ResVipSignIn = 400648;</code>
     */
    public static final int ResVipSignIn_VALUE = 400648;
    /**
     * <pre>
     * 请求领取vip奖励
     * </pre>
     *
     * <code>ReqReceiveVipReward = 400649;</code>
     */
    public static final int ReqReceiveVipReward_VALUE = 400649;
    /**
     * <pre>
     * 返回领取vip奖励
     * </pre>
     *
     * <code>ResReceiveVipReward = 400650;</code>
     */
    public static final int ResReceiveVipReward_VALUE = 400650;
    /**
     * <pre>
     * 请求首充签到
     * </pre>
     *
     * <code>ReqFirstChargeSignIn = 400651;</code>
     */
    public static final int ReqFirstChargeSignIn_VALUE = 400651;
    /**
     * <pre>
     * 返回首充签到
     * </pre>
     *
     * <code>ResFirstChargeSignIn = 400652;</code>
     */
    public static final int ResFirstChargeSignIn_VALUE = 400652;
    /**
     * <pre>
     * 请求首充返奖
     * </pre>
     *
     * <code>ReqReceiveRechargeRecover = 400653;</code>
     */
    public static final int ReqReceiveRechargeRecover_VALUE = 400653;
    /**
     * <pre>
     * 返回首充返奖
     * </pre>
     *
     * <code>ResReceiveRechargeRecover = 400654;</code>
     */
    public static final int ResReceiveRechargeRecover_VALUE = 400654;
    /**
     * <pre>
     * 请求领取投注返利奖励
     * </pre>
     *
     * <code>ReqReceiveWageredRebates = 400655;</code>
     */
    public static final int ReqReceiveWageredRebates_VALUE = 400655;
    /**
     * <pre>
     * 返回领取投注返利奖励
     * </pre>
     *
     * <code>ResReceiveWageredRebates = 400656;</code>
     */
    public static final int ResReceiveWageredRebates_VALUE = 400656;
    /**
     * <pre>
     * 请求领取充值邀请奖励
     * </pre>
     *
     * <code>ReqReceiveDepositInviteBonus = 400657;</code>
     */
    public static final int ReqReceiveDepositInviteBonus_VALUE = 400657;
    /**
     * <pre>
     * 返回领取充值邀请奖励
     * </pre>
     *
     * <code>ResReceiveDepositInviteBonus = 400658;</code>
     */
    public static final int ResReceiveDepositInviteBonus_VALUE = 400658;
    /**
     * <pre>
     * 请求查看明细数据
     * </pre>
     *
     * <code>ReqCheckDetailsData = 400659;</code>
     */
    public static final int ReqCheckDetailsData_VALUE = 400659;
    /**
     * <pre>
     * 返回查看明细数据
     * </pre>
     *
     * <code>ResCheckDetailsData = 400660;</code>
     */
    public static final int ResCheckDetailsData_VALUE = 400660;
    /**
     * <pre>
     *任务 400801-400900
     * </pre>
     *
     * <code>ReqQuestData = 400801;</code>
     */
    public static final int ReqQuestData_VALUE = 400801;
    /**
     * <pre>
     *返回任务数据
     * </pre>
     *
     * <code>ResQuestData = 400802;</code>
     */
    public static final int ResQuestData_VALUE = 400802;
    /**
     * <pre>
     *请求领取任务奖励
     * </pre>
     *
     * <code>ReqReceiveQuestReward = 400803;</code>
     */
    public static final int ReqReceiveQuestReward_VALUE = 400803;
    /**
     * <pre>
     *返回领取任务奖励
     * </pre>
     *
     * <code>ResReceiveQuestReward = 400804;</code>
     */
    public static final int ResReceiveQuestReward_VALUE = 400804;
    /**
     * <pre>
     *请求先前任务数据
     * </pre>
     *
     * <code>ReqPreviousQuestsData = 400805;</code>
     */
    public static final int ReqPreviousQuestsData_VALUE = 400805;
    /**
     * <pre>
     *返回先前任务奖励
     * </pre>
     *
     * <code>ResPreviousQuestsData = 400806;</code>
     */
    public static final int ResPreviousQuestsData_VALUE = 400806;
    /**
     * <pre>
     *TCP 450001-450100
     * </pre>
     *
     * <code>ReqTcpTokenAuth = 450001;</code>
     */
    public static final int ReqTcpTokenAuth_VALUE = 450001;
    /**
     * <pre>
     * 返回token认证
     * </pre>
     *
     * <code>ResTcpTokenAuth = 450002;</code>
     */
    public static final int ResTcpTokenAuth_VALUE = 450002;
    /**
     * <pre>
     * 请求心跳
     * </pre>
     *
     * <code>ReqTcpHeartBeat = 450003;</code>
     */
    public static final int ReqTcpHeartBeat_VALUE = 450003;
    /**
     * <pre>
     * 返回心跳
     * </pre>
     *
     * <code>ResTcpHeartBeat = 450004;</code>
     */
    public static final int ResTcpHeartBeat_VALUE = 450004;
    /**
     * <pre>
     * 返回错误异常
     * </pre>
     *
     * <code>ResTcpSysError = 450005;</code>
     */
    public static final int ResTcpSysError_VALUE = 450005;
    /**
     * <pre>
     * 返回货币更新
     * </pre>
     *
     * <code>ResTcpCurrencyUpdate = 450006;</code>
     */
    public static final int ResTcpCurrencyUpdate_VALUE = 450006;
    /**
     * <pre>
     * 返回接受邮件
     * </pre>
     *
     * <code>ResTcpReceiveInbox = 450007;</code>
     */
    public static final int ResTcpReceiveInbox_VALUE = 450007;
    /**
     * <pre>
     * 返回vip经验改变
     * </pre>
     *
     * <code>ResTcpVipClubExpChange = 450008;</code>
     */
    public static final int ResTcpVipClubExpChange_VALUE = 450008;
    /**
     * <pre>
     * 返回公告数据
     * </pre>
     *
     * <code>ResTcpBulletinData = 450009;</code>
     */
    public static final int ResTcpBulletinData_VALUE = 450009;
    /**
     * <pre>
     * 返回游戏记录
     * </pre>
     *
     * <code>ResTcpGameNoteData = 450010;</code>
     */
    public static final int ResTcpGameNoteData_VALUE = 450010;
    /**
     * <pre>
     * 返回我的下注列表
     * </pre>
     *
     * <code>ResTcpMyBetData = 450011;</code>
     */
    public static final int ResTcpMyBetData_VALUE = 450011;
    /**
     * <pre>
     * 返回每日竞赛奖池
     * </pre>
     *
     * <code>ResTcpDailyContestPrizePool = 450012;</code>
     */
    public static final int ResTcpDailyContestPrizePool_VALUE = 450012;
    /**
     * <pre>
     * 请求退出三方游戏
     * </pre>
     *
     * <code>ReqTcpQuitAgentGame = 450013;</code>
     */
    public static final int ReqTcpQuitAgentGame_VALUE = 450013;
    /**
     * <pre>
     * 返回退出三方游戏
     * </pre>
     *
     * <code>ResTcpQuitAgentGame = 450014;</code>
     */
    public static final int ResTcpQuitAgentGame_VALUE = 450014;
    /**
     * <pre>
     * 返回踢出玩家
     * </pre>
     *
     * <code>ResTcpKickOutPlayer = 450015;</code>
     */
    public static final int ResTcpKickOutPlayer_VALUE = 450015;
    /**
     * <pre>
     * 返回充值成功
     * </pre>
     *
     * <code>ResTcpRechargeSuccess = 450016;</code>
     */
    public static final int ResTcpRechargeSuccess_VALUE = 450016;


    public final int getNumber() {
      if (this == UNRECOGNIZED) {
        throw new java.lang.IllegalArgumentException(
            "Can't get the number of an unknown enum value.");
      }
      return value;
    }

    /**
     * @param value The numeric wire value of the corresponding enum entry.
     * @return The enum associated with the given numeric wire value.
     * @deprecated Use {@link #forNumber(int)} instead.
     */
    @java.lang.Deprecated
    public static MID valueOf(int value) {
      return forNumber(value);
    }

    /**
     * @param value The numeric wire value of the corresponding enum entry.
     * @return The enum associated with the given numeric wire value.
     */
    public static MID forNumber(int value) {
      switch (value) {
        case 0: return none;
        case 100001: return InnerReqRegisterUpdate;
        case 100002: return InnerReqHttpHandler;
        case 100003: return InnerResHttpHandler;
        case 100004: return InnerBroadcast;
        case 100005: return InnerReqServerList;
        case 100006: return InnerResServerList;
        case 100007: return InnerReqLoginOutHall;
        case 100008: return InnerNotifyRecharge;
        case 100009: return InnerNotifyWithdraw;
        case 100010: return InnerNotifyCurrencyUpdate;
        case 100011: return InnerNotifyUpdateGameInfo;
        case 100012: return InnerGetGameList;
        case 100013: return InnerNotifyGameDataUpdate;
        case 200001: return ReqRegister;
        case 200002: return ReqLogin;
        case 200003: return ResLogin;
        case 200004: return ReqMailVerifyCode;
        case 200005: return ResMailVerifyCode;
        case 200006: return ReqNotLoggedIn;
        case 200007: return ResNotLoggedIn;
        case 200008: return ReqWebSiteModel;
        case 200009: return ResWebSiteModel;
        case 200010: return ReqPhoneVerifyCode;
        case 200011: return ResPhoneVerifyCode;
        case 200012: return ReqRegisterAuth;
        case 200013: return ResRegisterAuth;
        case 200014: return ReqChannelInstall;
        case 200015: return ResChannelInstall;
        case 400001: return ReqPlayerEntryHall;
        case 400002: return ResPlayerEntryHall;
        case 400003: return ReqPlayerSignOut;
        case 400004: return ResPlayerSignOut;
        case 400005: return ReqHeadChange;
        case 400006: return ResHeadChange;
        case 400007: return ReqBindAccount;
        case 400008: return ResBindAccount;
        case 400009: return ReqResetPassword;
        case 400010: return ResResetPassword;
        case 400011: return ReqNameModify;
        case 400012: return ResNameModify;
        case 400013: return ReqAccountData;
        case 400014: return ResAccountData;
        case 400015: return ReqInboxData;
        case 400016: return ResInboxData;
        case 400017: return ReqReadInBox;
        case 400018: return ResReadInBox;
        case 400019: return ReqDeleteInbox;
        case 400020: return ResDeleteInbox;
        case 400021: return ReqChangeCurrency;
        case 400022: return ResChangeCurrency;
        case 400023: return ReqViewInFiat;
        case 400024: return ResViewInFiat;
        case 400027: return ReqCasinoData;
        case 400028: return ResCasinoData;
        case 400029: return ReqGameChannelData;
        case 400030: return ResGameChannelData;
        case 400031: return ReqSearchGameData;
        case 400032: return ResSearchGameData;
        case 400033: return ReqEnable2FAData;
        case 400034: return ResEnable2FAData;
        case 400035: return Req2FAVerificationCode;
        case 400036: return Res2FAVerificationCode;
        case 400037: return ReqEntryAgentGame;
        case 400038: return ResEntryAgentGame;
        case 400039: return ReqUserInteraction;
        case 400040: return ResUserInteraction;
        case 400041: return ReqGameOptData;
        case 400042: return ResGameOptData;
        case 400043: return ReqGetRankData;
        case 400044: return ResGetRankData;
        case 400045: return ReqRecentBigWinsData;
        case 400046: return ResRecentBigWinsData;
        case 400051: return ReqUnbindThreeParty;
        case 400052: return ResUnbindThreeParty;
        case 400053: return ReqSetPrivacyPreferences;
        case 400054: return ResSetPrivacyPreferences;
        case 400055: return ReqBindThreeParty;
        case 400056: return ResBindThreeParty;
        case 400057: return ReqPaymentMethodsData;
        case 400058: return ResPaymentMethodsData;
        case 400059: return ReqCheckSessionData;
        case 400060: return ResCheckSessionData;
        case 400061: return ReqChangeLanguage;
        case 400062: return ResChangeLanguage;
        case 400063: return ReqRefreshPlayerData;
        case 400064: return ResRefreshPlayerData;
        case 400065: return ReqVerifyAccount;
        case 400066: return ResVerifyAccount;
        case 400067: return ReqChangeAccount;
        case 400068: return ResChangeAccount;
        case 400069: return ReqKycAuth;
        case 400070: return ResKycAuth;
        case 400071: return ReqAddAccount;
        case 400072: return ResAddAccount;
        case 400073: return ReqRedemptionCodeReward;
        case 400074: return ResRedemptionCodeReward;
        case 400075: return ReqRechargeBonusOpen;
        case 400076: return ResRechargeBonusOpen;
        case 400201: return ReqBetHistoryData;
        case 400202: return ResBetHistoryData;
        case 400203: return ReqTransactionData;
        case 400204: return ResTransactionData;
        case 400301: return ReqDepositWithdrawData;
        case 400302: return ResDepositWithdrawData;
        case 400303: return ReqDepositData;
        case 400304: return ResDepositData;
        case 400305: return ReqWithdrawData;
        case 400306: return ResWithdrawData;
        case 400307: return ReqCreateRechargeOrder;
        case 400308: return ResCreateRechargeOrder;
        case 400309: return ReqCreateWithdrawOrder;
        case 400310: return ResCreateWithdrawOrder;
        case 400311: return ReqAddDeleteWithdrawAccount;
        case 400312: return ResAddDeleteWithdrawAccount;
        case 400401: return ReqBindSuperior;
        case 400402: return ResBindSuperior;
        case 400403: return ReqDashboardData;
        case 400404: return ResDashboardData;
        case 400405: return ReqMyRewardData;
        case 400406: return ResMyRewardData;
        case 400407: return ReqCommissionRewardData;
        case 400408: return ResCommissionRewardData;
        case 400409: return ReqReferralRewardData;
        case 400410: return ResReferralRewardData;
        case 400411: return ReqReferralCodeAndFriendsData;
        case 400412: return ResReferralCodeAndFriendsData;
        case 400413: return ReqCreateReferralCode;
        case 400414: return ResCreateReferralCode;
        case 400415: return ReqReferralCodeData;
        case 400416: return ResReferralCodeData;
        case 400417: return ReqFriendsData;
        case 400418: return ResFriendsData;
        case 400419: return ReqWithdrawToWallet;
        case 400420: return ResWithdrawToWallet;
        case 400421: return ReqHistoryData;
        case 400422: return ResHistoryData;
        case 400423: return ReqAffiliateWithdrawData;
        case 400424: return ResAffiliateWithdrawData;
        case 400425: return ReqTeamRewardData;
        case 400426: return ResTeamRewardData;
        case 400427: return ReqTeamData;
        case 400428: return ResTeamData;
        case 400429: return ReqThreeLevelRewardData;
        case 400430: return ResThreeLevelRewardData;
        case 400431: return ReqThreeLevelData;
        case 400432: return ResThreeLevelData;
        case 400501: return ReqHelpCenterData;
        case 400502: return ResHelpCenterData;
        case 400503: return ReqNewsData;
        case 400504: return ResNewsData;
        case 400505: return ReqConfigData;
        case 400506: return ResConfigData;
        case 400601: return ReqPromotionsData;
        case 400602: return ResPromotionsData;
        case 400603: return ReqLuckSpinData;
        case 400604: return ResLuckSpinData;
        case 400605: return ReqClickLuckSpin;
        case 400606: return ResClickLuckSpin;
        case 400607: return ReqLuckSpinReferralWithdraw;
        case 400608: return ResLuckSpinReferralWithdraw;
        case 400609: return ReqBonusData;
        case 400610: return ResBonusData;
        case 400611: return ReqVipBonusRewardsReceive;
        case 400612: return ResVipBonusRewardsReceive;
        case 400613: return ReqBonusDetailsData;
        case 400614: return ResBonusDetailsData;
        case 400615: return ReqBonusTransactionsData;
        case 400616: return ResBonusTransactionsData;
        case 400617: return ReqDailyContestData;
        case 400618: return ResDailyContestData;
        case 400619: return ReqDailyContestHistoryData;
        case 400620: return ResDailyContestHistoryData;
        case 400621: return ReqWeeklyRaffleData;
        case 400622: return ResWeeklyRaffleData;
        case 400623: return ReqWeeklyRaffleMyTicketsData;
        case 400624: return ResWeeklyRaffleMyTicketsData;
        case 400625: return ReqWeeklyRaffleResultData;
        case 400626: return ResWeeklyRaffleResultData;
        case 400627: return ReqReceivePromotionsReward;
        case 400628: return ResReceivePromotionsReward;
        case 400629: return ReqActivitySignUp;
        case 400630: return ResActivitySignUp;
        case 400631: return ReqActivityRankData;
        case 400632: return ResActivityRankData;
        case 400633: return ReqActivityData;
        case 400634: return ResActivityData;
        case 400635: return ReqReceiveRedEnvelope;
        case 400636: return ResReceiveRedEnvelope;
        case 400637: return ReqReceiveRewardBox;
        case 400638: return ResReceiveRewardBox;
        case 400639: return ReqReceiveMysteryBonus;
        case 400640: return ResReceiveMysteryBonus;
        case 400641: return ReqReceivePiggyBank;
        case 400642: return ResReceivePiggyBank;
        case 400643: return ReqRewardBoxSubordinateData;
        case 400644: return ResRewardBoxSubordinateData;
        case 400645: return ReqVipData;
        case 400646: return ResVipData;
        case 400647: return ReqVipSignIn;
        case 400648: return ResVipSignIn;
        case 400649: return ReqReceiveVipReward;
        case 400650: return ResReceiveVipReward;
        case 400651: return ReqFirstChargeSignIn;
        case 400652: return ResFirstChargeSignIn;
        case 400653: return ReqReceiveRechargeRecover;
        case 400654: return ResReceiveRechargeRecover;
        case 400655: return ReqReceiveWageredRebates;
        case 400656: return ResReceiveWageredRebates;
        case 400657: return ReqReceiveDepositInviteBonus;
        case 400658: return ResReceiveDepositInviteBonus;
        case 400659: return ReqCheckDetailsData;
        case 400660: return ResCheckDetailsData;
        case 400801: return ReqQuestData;
        case 400802: return ResQuestData;
        case 400803: return ReqReceiveQuestReward;
        case 400804: return ResReceiveQuestReward;
        case 400805: return ReqPreviousQuestsData;
        case 400806: return ResPreviousQuestsData;
        case 450001: return ReqTcpTokenAuth;
        case 450002: return ResTcpTokenAuth;
        case 450003: return ReqTcpHeartBeat;
        case 450004: return ResTcpHeartBeat;
        case 450005: return ResTcpSysError;
        case 450006: return ResTcpCurrencyUpdate;
        case 450007: return ResTcpReceiveInbox;
        case 450008: return ResTcpVipClubExpChange;
        case 450009: return ResTcpBulletinData;
        case 450010: return ResTcpGameNoteData;
        case 450011: return ResTcpMyBetData;
        case 450012: return ResTcpDailyContestPrizePool;
        case 450013: return ReqTcpQuitAgentGame;
        case 450014: return ResTcpQuitAgentGame;
        case 450015: return ResTcpKickOutPlayer;
        case 450016: return ResTcpRechargeSuccess;
        default: return null;
      }
    }

    public static com.google.protobuf.Internal.EnumLiteMap<MID>
        internalGetValueMap() {
      return internalValueMap;
    }
    private static final com.google.protobuf.Internal.EnumLiteMap<
        MID> internalValueMap =
          new com.google.protobuf.Internal.EnumLiteMap<MID>() {
            public MID findValueByNumber(int number) {
              return MID.forNumber(number);
            }
          };

    public final com.google.protobuf.Descriptors.EnumValueDescriptor
        getValueDescriptor() {
      if (this == UNRECOGNIZED) {
        throw new java.lang.IllegalStateException(
            "Can't get the descriptor of an unrecognized enum value.");
      }
      return getDescriptor().getValues().get(ordinal());
    }
    public final com.google.protobuf.Descriptors.EnumDescriptor
        getDescriptorForType() {
      return getDescriptor();
    }
    public static final com.google.protobuf.Descriptors.EnumDescriptor
        getDescriptor() {
      return com.proto.MIDMessage.getDescriptor().getEnumTypes().get(0);
    }

    private static final MID[] VALUES = values();

    public static MID valueOf(
        com.google.protobuf.Descriptors.EnumValueDescriptor desc) {
      if (desc.getType() != getDescriptor()) {
        throw new java.lang.IllegalArgumentException(
          "EnumValueDescriptor is not for this type.");
      }
      if (desc.getIndex() == -1) {
        return UNRECOGNIZED;
      }
      return VALUES[desc.getIndex()];
    }

    private final int value;

    private MID(int value) {
      this.value = value;
    }

    // @@protoc_insertion_point(enum_scope:ProtoMessage.MID)
  }


  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n\020MIDMessage.proto\022\014ProtoMessage*\367/\n\003MID" +
      "\022\010\n\004none\020\000\022\034\n\026InnerReqRegisterUpdate\020\241\215\006" +
      "\022\031\n\023InnerReqHttpHandler\020\242\215\006\022\031\n\023InnerResH" +
      "ttpHandler\020\243\215\006\022\024\n\016InnerBroadcast\020\244\215\006\022\030\n\022" +
      "InnerReqServerList\020\245\215\006\022\030\n\022InnerResServer" +
      "List\020\246\215\006\022\032\n\024InnerReqLoginOutHall\020\247\215\006\022\031\n\023" +
      "InnerNotifyRecharge\020\250\215\006\022\031\n\023InnerNotifyWi" +
      "thdraw\020\251\215\006\022\037\n\031InnerNotifyCurrencyUpdate\020" +
      "\252\215\006\022\037\n\031InnerNotifyUpdateGameInfo\020\253\215\006\022\026\n\020" +
      "InnerGetGameList\020\254\215\006\022\037\n\031InnerNotifyGameD" +
      "ataUpdate\020\255\215\006\022\021\n\013ReqRegister\020\301\232\014\022\016\n\010ReqL" +
      "ogin\020\302\232\014\022\016\n\010ResLogin\020\303\232\014\022\027\n\021ReqMailVerif" +
      "yCode\020\304\232\014\022\027\n\021ResMailVerifyCode\020\305\232\014\022\024\n\016Re" +
      "qNotLoggedIn\020\306\232\014\022\024\n\016ResNotLoggedIn\020\307\232\014\022\025" +
      "\n\017ReqWebSiteModel\020\310\232\014\022\025\n\017ResWebSiteModel" +
      "\020\311\232\014\022\030\n\022ReqPhoneVerifyCode\020\312\232\014\022\030\n\022ResPho" +
      "neVerifyCode\020\313\232\014\022\025\n\017ReqRegisterAuth\020\314\232\014\022" +
      "\025\n\017ResRegisterAuth\020\315\232\014\022\027\n\021ReqChannelInst" +
      "all\020\316\232\014\022\027\n\021ResChannelInstall\020\317\232\014\022\030\n\022ReqP" +
      "layerEntryHall\020\201\265\030\022\030\n\022ResPlayerEntryHall" +
      "\020\202\265\030\022\026\n\020ReqPlayerSignOut\020\203\265\030\022\026\n\020ResPlaye" +
      "rSignOut\020\204\265\030\022\023\n\rReqHeadChange\020\205\265\030\022\023\n\rRes" +
      "HeadChange\020\206\265\030\022\024\n\016ReqBindAccount\020\207\265\030\022\024\n\016" +
      "ResBindAccount\020\210\265\030\022\026\n\020ReqResetPassword\020\211" +
      "\265\030\022\026\n\020ResResetPassword\020\212\265\030\022\023\n\rReqNameMod" +
      "ify\020\213\265\030\022\023\n\rResNameModify\020\214\265\030\022\024\n\016ReqAccou" +
      "ntData\020\215\265\030\022\024\n\016ResAccountData\020\216\265\030\022\022\n\014ReqI" +
      "nboxData\020\217\265\030\022\022\n\014ResInboxData\020\220\265\030\022\022\n\014ReqR" +
      "eadInBox\020\221\265\030\022\022\n\014ResReadInBox\020\222\265\030\022\024\n\016ReqD" +
      "eleteInbox\020\223\265\030\022\024\n\016ResDeleteInbox\020\224\265\030\022\027\n\021" +
      "ReqChangeCurrency\020\225\265\030\022\027\n\021ResChangeCurren" +
      "cy\020\226\265\030\022\023\n\rReqViewInFiat\020\227\265\030\022\023\n\rResViewIn" +
      "Fiat\020\230\265\030\022\023\n\rReqCasinoData\020\233\265\030\022\023\n\rResCasi" +
      "noData\020\234\265\030\022\030\n\022ReqGameChannelData\020\235\265\030\022\030\n\022" +
      "ResGameChannelData\020\236\265\030\022\027\n\021ReqSearchGameD" +
      "ata\020\237\265\030\022\027\n\021ResSearchGameData\020\240\265\030\022\026\n\020ReqE" +
      "nable2FAData\020\241\265\030\022\026\n\020ResEnable2FAData\020\242\265\030" +
      "\022\034\n\026Req2FAVerificationCode\020\243\265\030\022\034\n\026Res2FA" +
      "VerificationCode\020\244\265\030\022\027\n\021ReqEntryAgentGam" +
      "e\020\245\265\030\022\027\n\021ResEntryAgentGame\020\246\265\030\022\030\n\022ReqUse" +
      "rInteraction\020\247\265\030\022\030\n\022ResUserInteraction\020\250" +
      "\265\030\022\024\n\016ReqGameOptData\020\251\265\030\022\024\n\016ResGameOptDa" +
      "ta\020\252\265\030\022\024\n\016ReqGetRankData\020\253\265\030\022\024\n\016ResGetRa" +
      "nkData\020\254\265\030\022\032\n\024ReqRecentBigWinsData\020\255\265\030\022\032" +
      "\n\024ResRecentBigWinsData\020\256\265\030\022\031\n\023ReqUnbindT" +
      "hreeParty\020\263\265\030\022\031\n\023ResUnbindThreeParty\020\264\265\030" +
      "\022\036\n\030ReqSetPrivacyPreferences\020\265\265\030\022\036\n\030ResS" +
      "etPrivacyPreferences\020\266\265\030\022\027\n\021ReqBindThree" +
      "Party\020\267\265\030\022\027\n\021ResBindThreeParty\020\270\265\030\022\033\n\025Re" +
      "qPaymentMethodsData\020\271\265\030\022\033\n\025ResPaymentMet" +
      "hodsData\020\272\265\030\022\031\n\023ReqCheckSessionData\020\273\265\030\022" +
      "\031\n\023ResCheckSessionData\020\274\265\030\022\027\n\021ReqChangeL" +
      "anguage\020\275\265\030\022\027\n\021ResChangeLanguage\020\276\265\030\022\032\n\024" +
      "ReqRefreshPlayerData\020\277\265\030\022\032\n\024ResRefreshPl" +
      "ayerData\020\300\265\030\022\026\n\020ReqVerifyAccount\020\301\265\030\022\026\n\020" +
      "ResVerifyAccount\020\302\265\030\022\026\n\020ReqChangeAccount" +
      "\020\303\265\030\022\026\n\020ResChangeAccount\020\304\265\030\022\020\n\nReqKycAu" +
      "th\020\305\265\030\022\020\n\nResKycAuth\020\306\265\030\022\023\n\rReqAddAccoun" +
      "t\020\307\265\030\022\023\n\rResAddAccount\020\310\265\030\022\035\n\027ReqRedempt" +
      "ionCodeReward\020\311\265\030\022\035\n\027ResRedemptionCodeRe" +
      "ward\020\312\265\030\022\032\n\024ReqRechargeBonusOpen\020\313\265\030\022\032\n\024" +
      "ResRechargeBonusOpen\020\314\265\030\022\027\n\021ReqBetHistor" +
      "yData\020\311\266\030\022\027\n\021ResBetHistoryData\020\312\266\030\022\030\n\022Re" +
      "qTransactionData\020\313\266\030\022\030\n\022ResTransactionDa" +
      "ta\020\314\266\030\022\034\n\026ReqDepositWithdrawData\020\255\267\030\022\034\n\026" +
      "ResDepositWithdrawData\020\256\267\030\022\024\n\016ReqDeposit" +
      "Data\020\257\267\030\022\024\n\016ResDepositData\020\260\267\030\022\025\n\017ReqWit" +
      "hdrawData\020\261\267\030\022\025\n\017ResWithdrawData\020\262\267\030\022\034\n\026" +
      "ReqCreateRechargeOrder\020\263\267\030\022\034\n\026ResCreateR" +
      "echargeOrder\020\264\267\030\022\034\n\026ReqCreateWithdrawOrd" +
      "er\020\265\267\030\022\034\n\026ResCreateWithdrawOrder\020\266\267\030\022!\n\033" +
      "ReqAddDeleteWithdrawAccount\020\267\267\030\022!\n\033ResAd" +
      "dDeleteWithdrawAccount\020\270\267\030\022\025\n\017ReqBindSup" +
      "erior\020\221\270\030\022\025\n\017ResBindSuperior\020\222\270\030\022\026\n\020ReqD" +
      "ashboardData\020\223\270\030\022\026\n\020ResDashboardData\020\224\270\030" +
      "\022\025\n\017ReqMyRewardData\020\225\270\030\022\025\n\017ResMyRewardDa" +
      "ta\020\226\270\030\022\035\n\027ReqCommissionRewardData\020\227\270\030\022\035\n" +
      "\027ResCommissionRewardData\020\230\270\030\022\033\n\025ReqRefer" +
      "ralRewardData\020\231\270\030\022\033\n\025ResReferralRewardDa" +
      "ta\020\232\270\030\022#\n\035ReqReferralCodeAndFriendsData\020" +
      "\233\270\030\022#\n\035ResReferralCodeAndFriendsData\020\234\270\030" +
      "\022\033\n\025ReqCreateReferralCode\020\235\270\030\022\033\n\025ResCrea" +
      "teReferralCode\020\236\270\030\022\031\n\023ReqReferralCodeDat" +
      "a\020\237\270\030\022\031\n\023ResReferralCodeData\020\240\270\030\022\024\n\016ReqF" +
      "riendsData\020\241\270\030\022\024\n\016ResFriendsData\020\242\270\030\022\031\n\023" +
      "ReqWithdrawToWallet\020\243\270\030\022\031\n\023ResWithdrawTo" +
      "Wallet\020\244\270\030\022\024\n\016ReqHistoryData\020\245\270\030\022\024\n\016ResH" +
      "istoryData\020\246\270\030\022\036\n\030ReqAffiliateWithdrawDa" +
      "ta\020\247\270\030\022\036\n\030ResAffiliateWithdrawData\020\250\270\030\022\027" +
      "\n\021ReqTeamRewardData\020\251\270\030\022\027\n\021ResTeamReward" +
      "Data\020\252\270\030\022\021\n\013ReqTeamData\020\253\270\030\022\021\n\013ResTeamDa" +
      "ta\020\254\270\030\022\035\n\027ReqThreeLevelRewardData\020\255\270\030\022\035\n" +
      "\027ResThreeLevelRewardData\020\256\270\030\022\027\n\021ReqThree" +
      "LevelData\020\257\270\030\022\027\n\021ResThreeLevelData\020\260\270\030\022\027" +
      "\n\021ReqHelpCenterData\020\365\270\030\022\027\n\021ResHelpCenter" +
      "Data\020\366\270\030\022\021\n\013ReqNewsData\020\367\270\030\022\021\n\013ResNewsDa" +
      "ta\020\370\270\030\022\023\n\rReqConfigData\020\371\270\030\022\023\n\rResConfig" +
      "Data\020\372\270\030\022\027\n\021ReqPromotionsData\020\331\271\030\022\027\n\021Res" +
      "PromotionsData\020\332\271\030\022\025\n\017ReqLuckSpinData\020\333\271" +
      "\030\022\025\n\017ResLuckSpinData\020\334\271\030\022\026\n\020ReqClickLuck" +
      "Spin\020\335\271\030\022\026\n\020ResClickLuckSpin\020\336\271\030\022!\n\033ReqL" +
      "uckSpinReferralWithdraw\020\337\271\030\022!\n\033ResLuckSp" +
      "inReferralWithdraw\020\340\271\030\022\022\n\014ReqBonusData\020\341" +
      "\271\030\022\022\n\014ResBonusData\020\342\271\030\022\037\n\031ReqVipBonusRew" +
      "ardsReceive\020\343\271\030\022\037\n\031ResVipBonusRewardsRec" +
      "eive\020\344\271\030\022\031\n\023ReqBonusDetailsData\020\345\271\030\022\031\n\023R" +
      "esBonusDetailsData\020\346\271\030\022\036\n\030ReqBonusTransa" +
      "ctionsData\020\347\271\030\022\036\n\030ResBonusTransactionsDa" +
      "ta\020\350\271\030\022\031\n\023ReqDailyContestData\020\351\271\030\022\031\n\023Res" +
      "DailyContestData\020\352\271\030\022 \n\032ReqDailyContestH" +
      "istoryData\020\353\271\030\022 \n\032ResDailyContestHistory" +
      "Data\020\354\271\030\022\031\n\023ReqWeeklyRaffleData\020\355\271\030\022\031\n\023R" +
      "esWeeklyRaffleData\020\356\271\030\022\"\n\034ReqWeeklyRaffl" +
      "eMyTicketsData\020\357\271\030\022\"\n\034ResWeeklyRaffleMyT" +
      "icketsData\020\360\271\030\022\037\n\031ReqWeeklyRaffleResultD" +
      "ata\020\361\271\030\022\037\n\031ResWeeklyRaffleResultData\020\362\271\030" +
      "\022 \n\032ReqReceivePromotionsReward\020\363\271\030\022 \n\032Re" +
      "sReceivePromotionsReward\020\364\271\030\022\027\n\021ReqActiv" +
      "itySignUp\020\365\271\030\022\027\n\021ResActivitySignUp\020\366\271\030\022\031" +
      "\n\023ReqActivityRankData\020\367\271\030\022\031\n\023ResActivity" +
      "RankData\020\370\271\030\022\025\n\017ReqActivityData\020\371\271\030\022\025\n\017R" +
      "esActivityData\020\372\271\030\022\033\n\025ReqReceiveRedEnvel" +
      "ope\020\373\271\030\022\033\n\025ResReceiveRedEnvelope\020\374\271\030\022\031\n\023" +
      "ReqReceiveRewardBox\020\375\271\030\022\031\n\023ResReceiveRew" +
      "ardBox\020\376\271\030\022\034\n\026ReqReceiveMysteryBonus\020\377\271\030" +
      "\022\034\n\026ResReceiveMysteryBonus\020\200\272\030\022\031\n\023ReqRec" +
      "eivePiggyBank\020\201\272\030\022\031\n\023ResReceivePiggyBank" +
      "\020\202\272\030\022!\n\033ReqRewardBoxSubordinateData\020\203\272\030\022" +
      "!\n\033ResRewardBoxSubordinateData\020\204\272\030\022\020\n\nRe" +
      "qVipData\020\205\272\030\022\020\n\nResVipData\020\206\272\030\022\022\n\014ReqVip" +
      "SignIn\020\207\272\030\022\022\n\014ResVipSignIn\020\210\272\030\022\031\n\023ReqRec" +
      "eiveVipReward\020\211\272\030\022\031\n\023ResReceiveVipReward" +
      "\020\212\272\030\022\032\n\024ReqFirstChargeSignIn\020\213\272\030\022\032\n\024ResF" +
      "irstChargeSignIn\020\214\272\030\022\037\n\031ReqReceiveRechar" +
      "geRecover\020\215\272\030\022\037\n\031ResReceiveRechargeRecov" +
      "er\020\216\272\030\022\036\n\030ReqReceiveWageredRebates\020\217\272\030\022\036" +
      "\n\030ResReceiveWageredRebates\020\220\272\030\022\"\n\034ReqRec" +
      "eiveDepositInviteBonus\020\221\272\030\022\"\n\034ResReceive" +
      "DepositInviteBonus\020\222\272\030\022\031\n\023ReqCheckDetail" +
      "sData\020\223\272\030\022\031\n\023ResCheckDetailsData\020\224\272\030\022\022\n\014" +
      "ReqQuestData\020\241\273\030\022\022\n\014ResQuestData\020\242\273\030\022\033\n\025" +
      "ReqReceiveQuestReward\020\243\273\030\022\033\n\025ResReceiveQ" +
      "uestReward\020\244\273\030\022\033\n\025ReqPreviousQuestsData\020" +
      "\245\273\030\022\033\n\025ResPreviousQuestsData\020\246\273\030\022\025\n\017ReqT" +
      "cpTokenAuth\020\321\273\033\022\025\n\017ResTcpTokenAuth\020\322\273\033\022\025" +
      "\n\017ReqTcpHeartBeat\020\323\273\033\022\025\n\017ResTcpHeartBeat" +
      "\020\324\273\033\022\024\n\016ResTcpSysError\020\325\273\033\022\032\n\024ResTcpCurr" +
      "encyUpdate\020\326\273\033\022\030\n\022ResTcpReceiveInbox\020\327\273\033" +
      "\022\034\n\026ResTcpVipClubExpChange\020\330\273\033\022\030\n\022ResTcp" +
      "BulletinData\020\331\273\033\022\030\n\022ResTcpGameNoteData\020\332" +
      "\273\033\022\025\n\017ResTcpMyBetData\020\333\273\033\022!\n\033ResTcpDaily" +
      "ContestPrizePool\020\334\273\033\022\031\n\023ReqTcpQuitAgentG" +
      "ame\020\335\273\033\022\031\n\023ResTcpQuitAgentGame\020\336\273\033\022\031\n\023Re" +
      "sTcpKickOutPlayer\020\337\273\033\022\033\n\025ResTcpRechargeS" +
      "uccess\020\340\273\033B\013\n\tcom.protob\006proto3"
    };
    descriptor = com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
        });
  }

  // @@protoc_insertion_point(outer_class_scope)
}
