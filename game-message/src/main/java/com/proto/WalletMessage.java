// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: WalletMessage.proto

package com.proto;

public final class WalletMessage {
  private WalletMessage() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  public interface ReqBetHistoryDataMessageOrBuilder extends
      // @@protoc_insertion_point(interface_extends:ProtoMessage.ReqBetHistoryDataMessage)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>int32 msgID = 1;</code>
     * @return The msgID.
     */
    int getMsgID();

    /**
     * <pre>
     *游戏id
     * </pre>
     *
     * <code>int32 gameId = 2;</code>
     * @return The gameId.
     */
    int getGameId();

    /**
     * <pre>
     *0.all 101.原创 102.电子 103.视讯 201.体育
     * </pre>
     *
     * <code>int32 gameType = 3;</code>
     * @return The gameType.
     */
    int getGameType();

    /**
     * <pre>
     *平台
     * </pre>
     *
     * <code>int32 platformId = 4;</code>
     * @return The platformId.
     */
    int getPlatformId();

    /**
     * <pre>
     *资产 0.all
     * </pre>
     *
     * <code>int32 assets = 5;</code>
     * @return The assets.
     */
    int getAssets();

    /**
     * <pre>
     *0.all 1.24小时 2.7day 3.30day 4.60day
     * </pre>
     *
     * <code>int32 past = 6;</code>
     * @return The past.
     */
    int getPast();

    /**
     * <pre>
     *页
     * </pre>
     *
     * <code>int32 page = 7;</code>
     * @return The page.
     */
    int getPage();

    /**
     * <pre>
     *页数量
     * </pre>
     *
     * <code>int32 pageSize = 8;</code>
     * @return The pageSize.
     */
    int getPageSize();

    /**
     * <pre>
     *开始时间
     * </pre>
     *
     * <code>int64 statTime = 9;</code>
     * @return The statTime.
     */
    long getStatTime();

    /**
     * <pre>
     *结束时间
     * </pre>
     *
     * <code>int64 endTime = 10;</code>
     * @return The endTime.
     */
    long getEndTime();

    /**
     * <pre>
     *语言id
     * </pre>
     *
     * <code>int32 language = 11;</code>
     * @return The language.
     */
    int getLanguage();
  }
  /**
   * <pre>
   *请求下注历史
   * </pre>
   *
   * Protobuf type {@code ProtoMessage.ReqBetHistoryDataMessage}
   */
  public static final class ReqBetHistoryDataMessage extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:ProtoMessage.ReqBetHistoryDataMessage)
      ReqBetHistoryDataMessageOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use ReqBetHistoryDataMessage.newBuilder() to construct.
    private ReqBetHistoryDataMessage(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private ReqBetHistoryDataMessage() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new ReqBetHistoryDataMessage();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private ReqBetHistoryDataMessage(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {

              msgID_ = input.readInt32();
              break;
            }
            case 16: {

              gameId_ = input.readInt32();
              break;
            }
            case 24: {

              gameType_ = input.readInt32();
              break;
            }
            case 32: {

              platformId_ = input.readInt32();
              break;
            }
            case 40: {

              assets_ = input.readInt32();
              break;
            }
            case 48: {

              past_ = input.readInt32();
              break;
            }
            case 56: {

              page_ = input.readInt32();
              break;
            }
            case 64: {

              pageSize_ = input.readInt32();
              break;
            }
            case 72: {

              statTime_ = input.readInt64();
              break;
            }
            case 80: {

              endTime_ = input.readInt64();
              break;
            }
            case 88: {

              language_ = input.readInt32();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.proto.WalletMessage.internal_static_ProtoMessage_ReqBetHistoryDataMessage_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.proto.WalletMessage.internal_static_ProtoMessage_ReqBetHistoryDataMessage_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.proto.WalletMessage.ReqBetHistoryDataMessage.class, com.proto.WalletMessage.ReqBetHistoryDataMessage.Builder.class);
    }

    public static final int MSGID_FIELD_NUMBER = 1;
    private int msgID_;
    /**
     * <code>int32 msgID = 1;</code>
     * @return The msgID.
     */
    @java.lang.Override
    public int getMsgID() {
      return msgID_;
    }

    public static final int GAMEID_FIELD_NUMBER = 2;
    private int gameId_;
    /**
     * <pre>
     *游戏id
     * </pre>
     *
     * <code>int32 gameId = 2;</code>
     * @return The gameId.
     */
    @java.lang.Override
    public int getGameId() {
      return gameId_;
    }

    public static final int GAMETYPE_FIELD_NUMBER = 3;
    private int gameType_;
    /**
     * <pre>
     *0.all 101.原创 102.电子 103.视讯 201.体育
     * </pre>
     *
     * <code>int32 gameType = 3;</code>
     * @return The gameType.
     */
    @java.lang.Override
    public int getGameType() {
      return gameType_;
    }

    public static final int PLATFORMID_FIELD_NUMBER = 4;
    private int platformId_;
    /**
     * <pre>
     *平台
     * </pre>
     *
     * <code>int32 platformId = 4;</code>
     * @return The platformId.
     */
    @java.lang.Override
    public int getPlatformId() {
      return platformId_;
    }

    public static final int ASSETS_FIELD_NUMBER = 5;
    private int assets_;
    /**
     * <pre>
     *资产 0.all
     * </pre>
     *
     * <code>int32 assets = 5;</code>
     * @return The assets.
     */
    @java.lang.Override
    public int getAssets() {
      return assets_;
    }

    public static final int PAST_FIELD_NUMBER = 6;
    private int past_;
    /**
     * <pre>
     *0.all 1.24小时 2.7day 3.30day 4.60day
     * </pre>
     *
     * <code>int32 past = 6;</code>
     * @return The past.
     */
    @java.lang.Override
    public int getPast() {
      return past_;
    }

    public static final int PAGE_FIELD_NUMBER = 7;
    private int page_;
    /**
     * <pre>
     *页
     * </pre>
     *
     * <code>int32 page = 7;</code>
     * @return The page.
     */
    @java.lang.Override
    public int getPage() {
      return page_;
    }

    public static final int PAGESIZE_FIELD_NUMBER = 8;
    private int pageSize_;
    /**
     * <pre>
     *页数量
     * </pre>
     *
     * <code>int32 pageSize = 8;</code>
     * @return The pageSize.
     */
    @java.lang.Override
    public int getPageSize() {
      return pageSize_;
    }

    public static final int STATTIME_FIELD_NUMBER = 9;
    private long statTime_;
    /**
     * <pre>
     *开始时间
     * </pre>
     *
     * <code>int64 statTime = 9;</code>
     * @return The statTime.
     */
    @java.lang.Override
    public long getStatTime() {
      return statTime_;
    }

    public static final int ENDTIME_FIELD_NUMBER = 10;
    private long endTime_;
    /**
     * <pre>
     *结束时间
     * </pre>
     *
     * <code>int64 endTime = 10;</code>
     * @return The endTime.
     */
    @java.lang.Override
    public long getEndTime() {
      return endTime_;
    }

    public static final int LANGUAGE_FIELD_NUMBER = 11;
    private int language_;
    /**
     * <pre>
     *语言id
     * </pre>
     *
     * <code>int32 language = 11;</code>
     * @return The language.
     */
    @java.lang.Override
    public int getLanguage() {
      return language_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (msgID_ != 0) {
        output.writeInt32(1, msgID_);
      }
      if (gameId_ != 0) {
        output.writeInt32(2, gameId_);
      }
      if (gameType_ != 0) {
        output.writeInt32(3, gameType_);
      }
      if (platformId_ != 0) {
        output.writeInt32(4, platformId_);
      }
      if (assets_ != 0) {
        output.writeInt32(5, assets_);
      }
      if (past_ != 0) {
        output.writeInt32(6, past_);
      }
      if (page_ != 0) {
        output.writeInt32(7, page_);
      }
      if (pageSize_ != 0) {
        output.writeInt32(8, pageSize_);
      }
      if (statTime_ != 0L) {
        output.writeInt64(9, statTime_);
      }
      if (endTime_ != 0L) {
        output.writeInt64(10, endTime_);
      }
      if (language_ != 0) {
        output.writeInt32(11, language_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (msgID_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, msgID_);
      }
      if (gameId_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(2, gameId_);
      }
      if (gameType_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(3, gameType_);
      }
      if (platformId_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(4, platformId_);
      }
      if (assets_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(5, assets_);
      }
      if (past_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(6, past_);
      }
      if (page_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(7, page_);
      }
      if (pageSize_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(8, pageSize_);
      }
      if (statTime_ != 0L) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(9, statTime_);
      }
      if (endTime_ != 0L) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(10, endTime_);
      }
      if (language_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(11, language_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.proto.WalletMessage.ReqBetHistoryDataMessage)) {
        return super.equals(obj);
      }
      com.proto.WalletMessage.ReqBetHistoryDataMessage other = (com.proto.WalletMessage.ReqBetHistoryDataMessage) obj;

      if (getMsgID()
          != other.getMsgID()) return false;
      if (getGameId()
          != other.getGameId()) return false;
      if (getGameType()
          != other.getGameType()) return false;
      if (getPlatformId()
          != other.getPlatformId()) return false;
      if (getAssets()
          != other.getAssets()) return false;
      if (getPast()
          != other.getPast()) return false;
      if (getPage()
          != other.getPage()) return false;
      if (getPageSize()
          != other.getPageSize()) return false;
      if (getStatTime()
          != other.getStatTime()) return false;
      if (getEndTime()
          != other.getEndTime()) return false;
      if (getLanguage()
          != other.getLanguage()) return false;
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + MSGID_FIELD_NUMBER;
      hash = (53 * hash) + getMsgID();
      hash = (37 * hash) + GAMEID_FIELD_NUMBER;
      hash = (53 * hash) + getGameId();
      hash = (37 * hash) + GAMETYPE_FIELD_NUMBER;
      hash = (53 * hash) + getGameType();
      hash = (37 * hash) + PLATFORMID_FIELD_NUMBER;
      hash = (53 * hash) + getPlatformId();
      hash = (37 * hash) + ASSETS_FIELD_NUMBER;
      hash = (53 * hash) + getAssets();
      hash = (37 * hash) + PAST_FIELD_NUMBER;
      hash = (53 * hash) + getPast();
      hash = (37 * hash) + PAGE_FIELD_NUMBER;
      hash = (53 * hash) + getPage();
      hash = (37 * hash) + PAGESIZE_FIELD_NUMBER;
      hash = (53 * hash) + getPageSize();
      hash = (37 * hash) + STATTIME_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getStatTime());
      hash = (37 * hash) + ENDTIME_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getEndTime());
      hash = (37 * hash) + LANGUAGE_FIELD_NUMBER;
      hash = (53 * hash) + getLanguage();
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.proto.WalletMessage.ReqBetHistoryDataMessage parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.proto.WalletMessage.ReqBetHistoryDataMessage parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.proto.WalletMessage.ReqBetHistoryDataMessage parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.proto.WalletMessage.ReqBetHistoryDataMessage parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.proto.WalletMessage.ReqBetHistoryDataMessage parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.proto.WalletMessage.ReqBetHistoryDataMessage parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.proto.WalletMessage.ReqBetHistoryDataMessage parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.proto.WalletMessage.ReqBetHistoryDataMessage parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.proto.WalletMessage.ReqBetHistoryDataMessage parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.proto.WalletMessage.ReqBetHistoryDataMessage parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.proto.WalletMessage.ReqBetHistoryDataMessage parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.proto.WalletMessage.ReqBetHistoryDataMessage parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.proto.WalletMessage.ReqBetHistoryDataMessage prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     *请求下注历史
     * </pre>
     *
     * Protobuf type {@code ProtoMessage.ReqBetHistoryDataMessage}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:ProtoMessage.ReqBetHistoryDataMessage)
        com.proto.WalletMessage.ReqBetHistoryDataMessageOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.proto.WalletMessage.internal_static_ProtoMessage_ReqBetHistoryDataMessage_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.proto.WalletMessage.internal_static_ProtoMessage_ReqBetHistoryDataMessage_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.proto.WalletMessage.ReqBetHistoryDataMessage.class, com.proto.WalletMessage.ReqBetHistoryDataMessage.Builder.class);
      }

      // Construct using com.proto.WalletMessage.ReqBetHistoryDataMessage.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        msgID_ = 0;

        gameId_ = 0;

        gameType_ = 0;

        platformId_ = 0;

        assets_ = 0;

        past_ = 0;

        page_ = 0;

        pageSize_ = 0;

        statTime_ = 0L;

        endTime_ = 0L;

        language_ = 0;

        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.proto.WalletMessage.internal_static_ProtoMessage_ReqBetHistoryDataMessage_descriptor;
      }

      @java.lang.Override
      public com.proto.WalletMessage.ReqBetHistoryDataMessage getDefaultInstanceForType() {
        return com.proto.WalletMessage.ReqBetHistoryDataMessage.getDefaultInstance();
      }

      @java.lang.Override
      public com.proto.WalletMessage.ReqBetHistoryDataMessage build() {
        com.proto.WalletMessage.ReqBetHistoryDataMessage result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.proto.WalletMessage.ReqBetHistoryDataMessage buildPartial() {
        com.proto.WalletMessage.ReqBetHistoryDataMessage result = new com.proto.WalletMessage.ReqBetHistoryDataMessage(this);
        result.msgID_ = msgID_;
        result.gameId_ = gameId_;
        result.gameType_ = gameType_;
        result.platformId_ = platformId_;
        result.assets_ = assets_;
        result.past_ = past_;
        result.page_ = page_;
        result.pageSize_ = pageSize_;
        result.statTime_ = statTime_;
        result.endTime_ = endTime_;
        result.language_ = language_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.proto.WalletMessage.ReqBetHistoryDataMessage) {
          return mergeFrom((com.proto.WalletMessage.ReqBetHistoryDataMessage)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.proto.WalletMessage.ReqBetHistoryDataMessage other) {
        if (other == com.proto.WalletMessage.ReqBetHistoryDataMessage.getDefaultInstance()) return this;
        if (other.getMsgID() != 0) {
          setMsgID(other.getMsgID());
        }
        if (other.getGameId() != 0) {
          setGameId(other.getGameId());
        }
        if (other.getGameType() != 0) {
          setGameType(other.getGameType());
        }
        if (other.getPlatformId() != 0) {
          setPlatformId(other.getPlatformId());
        }
        if (other.getAssets() != 0) {
          setAssets(other.getAssets());
        }
        if (other.getPast() != 0) {
          setPast(other.getPast());
        }
        if (other.getPage() != 0) {
          setPage(other.getPage());
        }
        if (other.getPageSize() != 0) {
          setPageSize(other.getPageSize());
        }
        if (other.getStatTime() != 0L) {
          setStatTime(other.getStatTime());
        }
        if (other.getEndTime() != 0L) {
          setEndTime(other.getEndTime());
        }
        if (other.getLanguage() != 0) {
          setLanguage(other.getLanguage());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.proto.WalletMessage.ReqBetHistoryDataMessage parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.proto.WalletMessage.ReqBetHistoryDataMessage) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }

      private int msgID_ ;
      /**
       * <code>int32 msgID = 1;</code>
       * @return The msgID.
       */
      @java.lang.Override
      public int getMsgID() {
        return msgID_;
      }
      /**
       * <code>int32 msgID = 1;</code>
       * @param value The msgID to set.
       * @return This builder for chaining.
       */
      public Builder setMsgID(int value) {
        
        msgID_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>int32 msgID = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearMsgID() {
        
        msgID_ = 0;
        onChanged();
        return this;
      }

      private int gameId_ ;
      /**
       * <pre>
       *游戏id
       * </pre>
       *
       * <code>int32 gameId = 2;</code>
       * @return The gameId.
       */
      @java.lang.Override
      public int getGameId() {
        return gameId_;
      }
      /**
       * <pre>
       *游戏id
       * </pre>
       *
       * <code>int32 gameId = 2;</code>
       * @param value The gameId to set.
       * @return This builder for chaining.
       */
      public Builder setGameId(int value) {
        
        gameId_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *游戏id
       * </pre>
       *
       * <code>int32 gameId = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearGameId() {
        
        gameId_ = 0;
        onChanged();
        return this;
      }

      private int gameType_ ;
      /**
       * <pre>
       *0.all 101.原创 102.电子 103.视讯 201.体育
       * </pre>
       *
       * <code>int32 gameType = 3;</code>
       * @return The gameType.
       */
      @java.lang.Override
      public int getGameType() {
        return gameType_;
      }
      /**
       * <pre>
       *0.all 101.原创 102.电子 103.视讯 201.体育
       * </pre>
       *
       * <code>int32 gameType = 3;</code>
       * @param value The gameType to set.
       * @return This builder for chaining.
       */
      public Builder setGameType(int value) {
        
        gameType_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *0.all 101.原创 102.电子 103.视讯 201.体育
       * </pre>
       *
       * <code>int32 gameType = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearGameType() {
        
        gameType_ = 0;
        onChanged();
        return this;
      }

      private int platformId_ ;
      /**
       * <pre>
       *平台
       * </pre>
       *
       * <code>int32 platformId = 4;</code>
       * @return The platformId.
       */
      @java.lang.Override
      public int getPlatformId() {
        return platformId_;
      }
      /**
       * <pre>
       *平台
       * </pre>
       *
       * <code>int32 platformId = 4;</code>
       * @param value The platformId to set.
       * @return This builder for chaining.
       */
      public Builder setPlatformId(int value) {
        
        platformId_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *平台
       * </pre>
       *
       * <code>int32 platformId = 4;</code>
       * @return This builder for chaining.
       */
      public Builder clearPlatformId() {
        
        platformId_ = 0;
        onChanged();
        return this;
      }

      private int assets_ ;
      /**
       * <pre>
       *资产 0.all
       * </pre>
       *
       * <code>int32 assets = 5;</code>
       * @return The assets.
       */
      @java.lang.Override
      public int getAssets() {
        return assets_;
      }
      /**
       * <pre>
       *资产 0.all
       * </pre>
       *
       * <code>int32 assets = 5;</code>
       * @param value The assets to set.
       * @return This builder for chaining.
       */
      public Builder setAssets(int value) {
        
        assets_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *资产 0.all
       * </pre>
       *
       * <code>int32 assets = 5;</code>
       * @return This builder for chaining.
       */
      public Builder clearAssets() {
        
        assets_ = 0;
        onChanged();
        return this;
      }

      private int past_ ;
      /**
       * <pre>
       *0.all 1.24小时 2.7day 3.30day 4.60day
       * </pre>
       *
       * <code>int32 past = 6;</code>
       * @return The past.
       */
      @java.lang.Override
      public int getPast() {
        return past_;
      }
      /**
       * <pre>
       *0.all 1.24小时 2.7day 3.30day 4.60day
       * </pre>
       *
       * <code>int32 past = 6;</code>
       * @param value The past to set.
       * @return This builder for chaining.
       */
      public Builder setPast(int value) {
        
        past_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *0.all 1.24小时 2.7day 3.30day 4.60day
       * </pre>
       *
       * <code>int32 past = 6;</code>
       * @return This builder for chaining.
       */
      public Builder clearPast() {
        
        past_ = 0;
        onChanged();
        return this;
      }

      private int page_ ;
      /**
       * <pre>
       *页
       * </pre>
       *
       * <code>int32 page = 7;</code>
       * @return The page.
       */
      @java.lang.Override
      public int getPage() {
        return page_;
      }
      /**
       * <pre>
       *页
       * </pre>
       *
       * <code>int32 page = 7;</code>
       * @param value The page to set.
       * @return This builder for chaining.
       */
      public Builder setPage(int value) {
        
        page_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *页
       * </pre>
       *
       * <code>int32 page = 7;</code>
       * @return This builder for chaining.
       */
      public Builder clearPage() {
        
        page_ = 0;
        onChanged();
        return this;
      }

      private int pageSize_ ;
      /**
       * <pre>
       *页数量
       * </pre>
       *
       * <code>int32 pageSize = 8;</code>
       * @return The pageSize.
       */
      @java.lang.Override
      public int getPageSize() {
        return pageSize_;
      }
      /**
       * <pre>
       *页数量
       * </pre>
       *
       * <code>int32 pageSize = 8;</code>
       * @param value The pageSize to set.
       * @return This builder for chaining.
       */
      public Builder setPageSize(int value) {
        
        pageSize_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *页数量
       * </pre>
       *
       * <code>int32 pageSize = 8;</code>
       * @return This builder for chaining.
       */
      public Builder clearPageSize() {
        
        pageSize_ = 0;
        onChanged();
        return this;
      }

      private long statTime_ ;
      /**
       * <pre>
       *开始时间
       * </pre>
       *
       * <code>int64 statTime = 9;</code>
       * @return The statTime.
       */
      @java.lang.Override
      public long getStatTime() {
        return statTime_;
      }
      /**
       * <pre>
       *开始时间
       * </pre>
       *
       * <code>int64 statTime = 9;</code>
       * @param value The statTime to set.
       * @return This builder for chaining.
       */
      public Builder setStatTime(long value) {
        
        statTime_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *开始时间
       * </pre>
       *
       * <code>int64 statTime = 9;</code>
       * @return This builder for chaining.
       */
      public Builder clearStatTime() {
        
        statTime_ = 0L;
        onChanged();
        return this;
      }

      private long endTime_ ;
      /**
       * <pre>
       *结束时间
       * </pre>
       *
       * <code>int64 endTime = 10;</code>
       * @return The endTime.
       */
      @java.lang.Override
      public long getEndTime() {
        return endTime_;
      }
      /**
       * <pre>
       *结束时间
       * </pre>
       *
       * <code>int64 endTime = 10;</code>
       * @param value The endTime to set.
       * @return This builder for chaining.
       */
      public Builder setEndTime(long value) {
        
        endTime_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *结束时间
       * </pre>
       *
       * <code>int64 endTime = 10;</code>
       * @return This builder for chaining.
       */
      public Builder clearEndTime() {
        
        endTime_ = 0L;
        onChanged();
        return this;
      }

      private int language_ ;
      /**
       * <pre>
       *语言id
       * </pre>
       *
       * <code>int32 language = 11;</code>
       * @return The language.
       */
      @java.lang.Override
      public int getLanguage() {
        return language_;
      }
      /**
       * <pre>
       *语言id
       * </pre>
       *
       * <code>int32 language = 11;</code>
       * @param value The language to set.
       * @return This builder for chaining.
       */
      public Builder setLanguage(int value) {
        
        language_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *语言id
       * </pre>
       *
       * <code>int32 language = 11;</code>
       * @return This builder for chaining.
       */
      public Builder clearLanguage() {
        
        language_ = 0;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:ProtoMessage.ReqBetHistoryDataMessage)
    }

    // @@protoc_insertion_point(class_scope:ProtoMessage.ReqBetHistoryDataMessage)
    private static final com.proto.WalletMessage.ReqBetHistoryDataMessage DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.proto.WalletMessage.ReqBetHistoryDataMessage();
    }

    public static com.proto.WalletMessage.ReqBetHistoryDataMessage getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<ReqBetHistoryDataMessage>
        PARSER = new com.google.protobuf.AbstractParser<ReqBetHistoryDataMessage>() {
      @java.lang.Override
      public ReqBetHistoryDataMessage parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new ReqBetHistoryDataMessage(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<ReqBetHistoryDataMessage> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<ReqBetHistoryDataMessage> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.proto.WalletMessage.ReqBetHistoryDataMessage getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface ResBetHistoryDataMessageOrBuilder extends
      // @@protoc_insertion_point(interface_extends:ProtoMessage.ResBetHistoryDataMessage)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>int32 msgID = 1;</code>
     * @return The msgID.
     */
    int getMsgID();

    /**
     * <pre>
     *错误码
     * </pre>
     *
     * <code>int32 error = 2;</code>
     * @return The error.
     */
    int getError();

    /**
     * <pre>
     *游戏id
     * </pre>
     *
     * <code>int32 gameId = 3;</code>
     * @return The gameId.
     */
    int getGameId();

    /**
     * <pre>
     *0.all 101.原创 102.电子 103.视讯 201.体育
     * </pre>
     *
     * <code>int32 gameType = 4;</code>
     * @return The gameType.
     */
    int getGameType();

    /**
     * <pre>
     *平台
     * </pre>
     *
     * <code>int32 platformId = 5;</code>
     * @return The platformId.
     */
    int getPlatformId();

    /**
     * <pre>
     *资产 0.all
     * </pre>
     *
     * <code>int32 assets = 6;</code>
     * @return The assets.
     */
    int getAssets();

    /**
     * <pre>
     *0.all 1.24小时 2.7day 3.30day 4.60day
     * </pre>
     *
     * <code>int32 past = 7;</code>
     * @return The past.
     */
    int getPast();

    /**
     * <code>int32 page = 8;</code>
     * @return The page.
     */
    int getPage();

    /**
     * <code>int32 pageSize = 9;</code>
     * @return The pageSize.
     */
    int getPageSize();

    /**
     * <pre>
     *总条数
     * </pre>
     *
     * <code>int32 total = 10;</code>
     * @return The total.
     */
    int getTotal();

    /**
     * <pre>
     *总页数
     * </pre>
     *
     * <code>int32 totalPage = 11;</code>
     * @return The totalPage.
     */
    int getTotalPage();

    /**
     * <pre>
     *下注列表
     * </pre>
     *
     * <code>repeated .ProtoMessage.BetInfo betList = 12;</code>
     */
    java.util.List<com.proto.CommonMessage.BetInfo> 
        getBetListList();
    /**
     * <pre>
     *下注列表
     * </pre>
     *
     * <code>repeated .ProtoMessage.BetInfo betList = 12;</code>
     */
    com.proto.CommonMessage.BetInfo getBetList(int index);
    /**
     * <pre>
     *下注列表
     * </pre>
     *
     * <code>repeated .ProtoMessage.BetInfo betList = 12;</code>
     */
    int getBetListCount();
    /**
     * <pre>
     *下注列表
     * </pre>
     *
     * <code>repeated .ProtoMessage.BetInfo betList = 12;</code>
     */
    java.util.List<? extends com.proto.CommonMessage.BetInfoOrBuilder> 
        getBetListOrBuilderList();
    /**
     * <pre>
     *下注列表
     * </pre>
     *
     * <code>repeated .ProtoMessage.BetInfo betList = 12;</code>
     */
    com.proto.CommonMessage.BetInfoOrBuilder getBetListOrBuilder(
        int index);
  }
  /**
   * <pre>
   *返回下注历史
   * </pre>
   *
   * Protobuf type {@code ProtoMessage.ResBetHistoryDataMessage}
   */
  public static final class ResBetHistoryDataMessage extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:ProtoMessage.ResBetHistoryDataMessage)
      ResBetHistoryDataMessageOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use ResBetHistoryDataMessage.newBuilder() to construct.
    private ResBetHistoryDataMessage(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private ResBetHistoryDataMessage() {
      betList_ = java.util.Collections.emptyList();
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new ResBetHistoryDataMessage();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private ResBetHistoryDataMessage(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {

              msgID_ = input.readInt32();
              break;
            }
            case 16: {

              error_ = input.readInt32();
              break;
            }
            case 24: {

              gameId_ = input.readInt32();
              break;
            }
            case 32: {

              gameType_ = input.readInt32();
              break;
            }
            case 40: {

              platformId_ = input.readInt32();
              break;
            }
            case 48: {

              assets_ = input.readInt32();
              break;
            }
            case 56: {

              past_ = input.readInt32();
              break;
            }
            case 64: {

              page_ = input.readInt32();
              break;
            }
            case 72: {

              pageSize_ = input.readInt32();
              break;
            }
            case 80: {

              total_ = input.readInt32();
              break;
            }
            case 88: {

              totalPage_ = input.readInt32();
              break;
            }
            case 98: {
              if (!((mutable_bitField0_ & 0x00000001) != 0)) {
                betList_ = new java.util.ArrayList<com.proto.CommonMessage.BetInfo>();
                mutable_bitField0_ |= 0x00000001;
              }
              betList_.add(
                  input.readMessage(com.proto.CommonMessage.BetInfo.parser(), extensionRegistry));
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        if (((mutable_bitField0_ & 0x00000001) != 0)) {
          betList_ = java.util.Collections.unmodifiableList(betList_);
        }
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.proto.WalletMessage.internal_static_ProtoMessage_ResBetHistoryDataMessage_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.proto.WalletMessage.internal_static_ProtoMessage_ResBetHistoryDataMessage_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.proto.WalletMessage.ResBetHistoryDataMessage.class, com.proto.WalletMessage.ResBetHistoryDataMessage.Builder.class);
    }

    public static final int MSGID_FIELD_NUMBER = 1;
    private int msgID_;
    /**
     * <code>int32 msgID = 1;</code>
     * @return The msgID.
     */
    @java.lang.Override
    public int getMsgID() {
      return msgID_;
    }

    public static final int ERROR_FIELD_NUMBER = 2;
    private int error_;
    /**
     * <pre>
     *错误码
     * </pre>
     *
     * <code>int32 error = 2;</code>
     * @return The error.
     */
    @java.lang.Override
    public int getError() {
      return error_;
    }

    public static final int GAMEID_FIELD_NUMBER = 3;
    private int gameId_;
    /**
     * <pre>
     *游戏id
     * </pre>
     *
     * <code>int32 gameId = 3;</code>
     * @return The gameId.
     */
    @java.lang.Override
    public int getGameId() {
      return gameId_;
    }

    public static final int GAMETYPE_FIELD_NUMBER = 4;
    private int gameType_;
    /**
     * <pre>
     *0.all 101.原创 102.电子 103.视讯 201.体育
     * </pre>
     *
     * <code>int32 gameType = 4;</code>
     * @return The gameType.
     */
    @java.lang.Override
    public int getGameType() {
      return gameType_;
    }

    public static final int PLATFORMID_FIELD_NUMBER = 5;
    private int platformId_;
    /**
     * <pre>
     *平台
     * </pre>
     *
     * <code>int32 platformId = 5;</code>
     * @return The platformId.
     */
    @java.lang.Override
    public int getPlatformId() {
      return platformId_;
    }

    public static final int ASSETS_FIELD_NUMBER = 6;
    private int assets_;
    /**
     * <pre>
     *资产 0.all
     * </pre>
     *
     * <code>int32 assets = 6;</code>
     * @return The assets.
     */
    @java.lang.Override
    public int getAssets() {
      return assets_;
    }

    public static final int PAST_FIELD_NUMBER = 7;
    private int past_;
    /**
     * <pre>
     *0.all 1.24小时 2.7day 3.30day 4.60day
     * </pre>
     *
     * <code>int32 past = 7;</code>
     * @return The past.
     */
    @java.lang.Override
    public int getPast() {
      return past_;
    }

    public static final int PAGE_FIELD_NUMBER = 8;
    private int page_;
    /**
     * <code>int32 page = 8;</code>
     * @return The page.
     */
    @java.lang.Override
    public int getPage() {
      return page_;
    }

    public static final int PAGESIZE_FIELD_NUMBER = 9;
    private int pageSize_;
    /**
     * <code>int32 pageSize = 9;</code>
     * @return The pageSize.
     */
    @java.lang.Override
    public int getPageSize() {
      return pageSize_;
    }

    public static final int TOTAL_FIELD_NUMBER = 10;
    private int total_;
    /**
     * <pre>
     *总条数
     * </pre>
     *
     * <code>int32 total = 10;</code>
     * @return The total.
     */
    @java.lang.Override
    public int getTotal() {
      return total_;
    }

    public static final int TOTALPAGE_FIELD_NUMBER = 11;
    private int totalPage_;
    /**
     * <pre>
     *总页数
     * </pre>
     *
     * <code>int32 totalPage = 11;</code>
     * @return The totalPage.
     */
    @java.lang.Override
    public int getTotalPage() {
      return totalPage_;
    }

    public static final int BETLIST_FIELD_NUMBER = 12;
    private java.util.List<com.proto.CommonMessage.BetInfo> betList_;
    /**
     * <pre>
     *下注列表
     * </pre>
     *
     * <code>repeated .ProtoMessage.BetInfo betList = 12;</code>
     */
    @java.lang.Override
    public java.util.List<com.proto.CommonMessage.BetInfo> getBetListList() {
      return betList_;
    }
    /**
     * <pre>
     *下注列表
     * </pre>
     *
     * <code>repeated .ProtoMessage.BetInfo betList = 12;</code>
     */
    @java.lang.Override
    public java.util.List<? extends com.proto.CommonMessage.BetInfoOrBuilder> 
        getBetListOrBuilderList() {
      return betList_;
    }
    /**
     * <pre>
     *下注列表
     * </pre>
     *
     * <code>repeated .ProtoMessage.BetInfo betList = 12;</code>
     */
    @java.lang.Override
    public int getBetListCount() {
      return betList_.size();
    }
    /**
     * <pre>
     *下注列表
     * </pre>
     *
     * <code>repeated .ProtoMessage.BetInfo betList = 12;</code>
     */
    @java.lang.Override
    public com.proto.CommonMessage.BetInfo getBetList(int index) {
      return betList_.get(index);
    }
    /**
     * <pre>
     *下注列表
     * </pre>
     *
     * <code>repeated .ProtoMessage.BetInfo betList = 12;</code>
     */
    @java.lang.Override
    public com.proto.CommonMessage.BetInfoOrBuilder getBetListOrBuilder(
        int index) {
      return betList_.get(index);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (msgID_ != 0) {
        output.writeInt32(1, msgID_);
      }
      if (error_ != 0) {
        output.writeInt32(2, error_);
      }
      if (gameId_ != 0) {
        output.writeInt32(3, gameId_);
      }
      if (gameType_ != 0) {
        output.writeInt32(4, gameType_);
      }
      if (platformId_ != 0) {
        output.writeInt32(5, platformId_);
      }
      if (assets_ != 0) {
        output.writeInt32(6, assets_);
      }
      if (past_ != 0) {
        output.writeInt32(7, past_);
      }
      if (page_ != 0) {
        output.writeInt32(8, page_);
      }
      if (pageSize_ != 0) {
        output.writeInt32(9, pageSize_);
      }
      if (total_ != 0) {
        output.writeInt32(10, total_);
      }
      if (totalPage_ != 0) {
        output.writeInt32(11, totalPage_);
      }
      for (int i = 0; i < betList_.size(); i++) {
        output.writeMessage(12, betList_.get(i));
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (msgID_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, msgID_);
      }
      if (error_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(2, error_);
      }
      if (gameId_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(3, gameId_);
      }
      if (gameType_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(4, gameType_);
      }
      if (platformId_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(5, platformId_);
      }
      if (assets_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(6, assets_);
      }
      if (past_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(7, past_);
      }
      if (page_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(8, page_);
      }
      if (pageSize_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(9, pageSize_);
      }
      if (total_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(10, total_);
      }
      if (totalPage_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(11, totalPage_);
      }
      for (int i = 0; i < betList_.size(); i++) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(12, betList_.get(i));
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.proto.WalletMessage.ResBetHistoryDataMessage)) {
        return super.equals(obj);
      }
      com.proto.WalletMessage.ResBetHistoryDataMessage other = (com.proto.WalletMessage.ResBetHistoryDataMessage) obj;

      if (getMsgID()
          != other.getMsgID()) return false;
      if (getError()
          != other.getError()) return false;
      if (getGameId()
          != other.getGameId()) return false;
      if (getGameType()
          != other.getGameType()) return false;
      if (getPlatformId()
          != other.getPlatformId()) return false;
      if (getAssets()
          != other.getAssets()) return false;
      if (getPast()
          != other.getPast()) return false;
      if (getPage()
          != other.getPage()) return false;
      if (getPageSize()
          != other.getPageSize()) return false;
      if (getTotal()
          != other.getTotal()) return false;
      if (getTotalPage()
          != other.getTotalPage()) return false;
      if (!getBetListList()
          .equals(other.getBetListList())) return false;
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + MSGID_FIELD_NUMBER;
      hash = (53 * hash) + getMsgID();
      hash = (37 * hash) + ERROR_FIELD_NUMBER;
      hash = (53 * hash) + getError();
      hash = (37 * hash) + GAMEID_FIELD_NUMBER;
      hash = (53 * hash) + getGameId();
      hash = (37 * hash) + GAMETYPE_FIELD_NUMBER;
      hash = (53 * hash) + getGameType();
      hash = (37 * hash) + PLATFORMID_FIELD_NUMBER;
      hash = (53 * hash) + getPlatformId();
      hash = (37 * hash) + ASSETS_FIELD_NUMBER;
      hash = (53 * hash) + getAssets();
      hash = (37 * hash) + PAST_FIELD_NUMBER;
      hash = (53 * hash) + getPast();
      hash = (37 * hash) + PAGE_FIELD_NUMBER;
      hash = (53 * hash) + getPage();
      hash = (37 * hash) + PAGESIZE_FIELD_NUMBER;
      hash = (53 * hash) + getPageSize();
      hash = (37 * hash) + TOTAL_FIELD_NUMBER;
      hash = (53 * hash) + getTotal();
      hash = (37 * hash) + TOTALPAGE_FIELD_NUMBER;
      hash = (53 * hash) + getTotalPage();
      if (getBetListCount() > 0) {
        hash = (37 * hash) + BETLIST_FIELD_NUMBER;
        hash = (53 * hash) + getBetListList().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.proto.WalletMessage.ResBetHistoryDataMessage parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.proto.WalletMessage.ResBetHistoryDataMessage parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.proto.WalletMessage.ResBetHistoryDataMessage parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.proto.WalletMessage.ResBetHistoryDataMessage parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.proto.WalletMessage.ResBetHistoryDataMessage parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.proto.WalletMessage.ResBetHistoryDataMessage parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.proto.WalletMessage.ResBetHistoryDataMessage parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.proto.WalletMessage.ResBetHistoryDataMessage parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.proto.WalletMessage.ResBetHistoryDataMessage parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.proto.WalletMessage.ResBetHistoryDataMessage parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.proto.WalletMessage.ResBetHistoryDataMessage parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.proto.WalletMessage.ResBetHistoryDataMessage parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.proto.WalletMessage.ResBetHistoryDataMessage prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     *返回下注历史
     * </pre>
     *
     * Protobuf type {@code ProtoMessage.ResBetHistoryDataMessage}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:ProtoMessage.ResBetHistoryDataMessage)
        com.proto.WalletMessage.ResBetHistoryDataMessageOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.proto.WalletMessage.internal_static_ProtoMessage_ResBetHistoryDataMessage_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.proto.WalletMessage.internal_static_ProtoMessage_ResBetHistoryDataMessage_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.proto.WalletMessage.ResBetHistoryDataMessage.class, com.proto.WalletMessage.ResBetHistoryDataMessage.Builder.class);
      }

      // Construct using com.proto.WalletMessage.ResBetHistoryDataMessage.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
          getBetListFieldBuilder();
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        msgID_ = 0;

        error_ = 0;

        gameId_ = 0;

        gameType_ = 0;

        platformId_ = 0;

        assets_ = 0;

        past_ = 0;

        page_ = 0;

        pageSize_ = 0;

        total_ = 0;

        totalPage_ = 0;

        if (betListBuilder_ == null) {
          betList_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000001);
        } else {
          betListBuilder_.clear();
        }
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.proto.WalletMessage.internal_static_ProtoMessage_ResBetHistoryDataMessage_descriptor;
      }

      @java.lang.Override
      public com.proto.WalletMessage.ResBetHistoryDataMessage getDefaultInstanceForType() {
        return com.proto.WalletMessage.ResBetHistoryDataMessage.getDefaultInstance();
      }

      @java.lang.Override
      public com.proto.WalletMessage.ResBetHistoryDataMessage build() {
        com.proto.WalletMessage.ResBetHistoryDataMessage result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.proto.WalletMessage.ResBetHistoryDataMessage buildPartial() {
        com.proto.WalletMessage.ResBetHistoryDataMessage result = new com.proto.WalletMessage.ResBetHistoryDataMessage(this);
        int from_bitField0_ = bitField0_;
        result.msgID_ = msgID_;
        result.error_ = error_;
        result.gameId_ = gameId_;
        result.gameType_ = gameType_;
        result.platformId_ = platformId_;
        result.assets_ = assets_;
        result.past_ = past_;
        result.page_ = page_;
        result.pageSize_ = pageSize_;
        result.total_ = total_;
        result.totalPage_ = totalPage_;
        if (betListBuilder_ == null) {
          if (((bitField0_ & 0x00000001) != 0)) {
            betList_ = java.util.Collections.unmodifiableList(betList_);
            bitField0_ = (bitField0_ & ~0x00000001);
          }
          result.betList_ = betList_;
        } else {
          result.betList_ = betListBuilder_.build();
        }
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.proto.WalletMessage.ResBetHistoryDataMessage) {
          return mergeFrom((com.proto.WalletMessage.ResBetHistoryDataMessage)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.proto.WalletMessage.ResBetHistoryDataMessage other) {
        if (other == com.proto.WalletMessage.ResBetHistoryDataMessage.getDefaultInstance()) return this;
        if (other.getMsgID() != 0) {
          setMsgID(other.getMsgID());
        }
        if (other.getError() != 0) {
          setError(other.getError());
        }
        if (other.getGameId() != 0) {
          setGameId(other.getGameId());
        }
        if (other.getGameType() != 0) {
          setGameType(other.getGameType());
        }
        if (other.getPlatformId() != 0) {
          setPlatformId(other.getPlatformId());
        }
        if (other.getAssets() != 0) {
          setAssets(other.getAssets());
        }
        if (other.getPast() != 0) {
          setPast(other.getPast());
        }
        if (other.getPage() != 0) {
          setPage(other.getPage());
        }
        if (other.getPageSize() != 0) {
          setPageSize(other.getPageSize());
        }
        if (other.getTotal() != 0) {
          setTotal(other.getTotal());
        }
        if (other.getTotalPage() != 0) {
          setTotalPage(other.getTotalPage());
        }
        if (betListBuilder_ == null) {
          if (!other.betList_.isEmpty()) {
            if (betList_.isEmpty()) {
              betList_ = other.betList_;
              bitField0_ = (bitField0_ & ~0x00000001);
            } else {
              ensureBetListIsMutable();
              betList_.addAll(other.betList_);
            }
            onChanged();
          }
        } else {
          if (!other.betList_.isEmpty()) {
            if (betListBuilder_.isEmpty()) {
              betListBuilder_.dispose();
              betListBuilder_ = null;
              betList_ = other.betList_;
              bitField0_ = (bitField0_ & ~0x00000001);
              betListBuilder_ = 
                com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                   getBetListFieldBuilder() : null;
            } else {
              betListBuilder_.addAllMessages(other.betList_);
            }
          }
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.proto.WalletMessage.ResBetHistoryDataMessage parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.proto.WalletMessage.ResBetHistoryDataMessage) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private int msgID_ ;
      /**
       * <code>int32 msgID = 1;</code>
       * @return The msgID.
       */
      @java.lang.Override
      public int getMsgID() {
        return msgID_;
      }
      /**
       * <code>int32 msgID = 1;</code>
       * @param value The msgID to set.
       * @return This builder for chaining.
       */
      public Builder setMsgID(int value) {
        
        msgID_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>int32 msgID = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearMsgID() {
        
        msgID_ = 0;
        onChanged();
        return this;
      }

      private int error_ ;
      /**
       * <pre>
       *错误码
       * </pre>
       *
       * <code>int32 error = 2;</code>
       * @return The error.
       */
      @java.lang.Override
      public int getError() {
        return error_;
      }
      /**
       * <pre>
       *错误码
       * </pre>
       *
       * <code>int32 error = 2;</code>
       * @param value The error to set.
       * @return This builder for chaining.
       */
      public Builder setError(int value) {
        
        error_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *错误码
       * </pre>
       *
       * <code>int32 error = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearError() {
        
        error_ = 0;
        onChanged();
        return this;
      }

      private int gameId_ ;
      /**
       * <pre>
       *游戏id
       * </pre>
       *
       * <code>int32 gameId = 3;</code>
       * @return The gameId.
       */
      @java.lang.Override
      public int getGameId() {
        return gameId_;
      }
      /**
       * <pre>
       *游戏id
       * </pre>
       *
       * <code>int32 gameId = 3;</code>
       * @param value The gameId to set.
       * @return This builder for chaining.
       */
      public Builder setGameId(int value) {
        
        gameId_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *游戏id
       * </pre>
       *
       * <code>int32 gameId = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearGameId() {
        
        gameId_ = 0;
        onChanged();
        return this;
      }

      private int gameType_ ;
      /**
       * <pre>
       *0.all 101.原创 102.电子 103.视讯 201.体育
       * </pre>
       *
       * <code>int32 gameType = 4;</code>
       * @return The gameType.
       */
      @java.lang.Override
      public int getGameType() {
        return gameType_;
      }
      /**
       * <pre>
       *0.all 101.原创 102.电子 103.视讯 201.体育
       * </pre>
       *
       * <code>int32 gameType = 4;</code>
       * @param value The gameType to set.
       * @return This builder for chaining.
       */
      public Builder setGameType(int value) {
        
        gameType_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *0.all 101.原创 102.电子 103.视讯 201.体育
       * </pre>
       *
       * <code>int32 gameType = 4;</code>
       * @return This builder for chaining.
       */
      public Builder clearGameType() {
        
        gameType_ = 0;
        onChanged();
        return this;
      }

      private int platformId_ ;
      /**
       * <pre>
       *平台
       * </pre>
       *
       * <code>int32 platformId = 5;</code>
       * @return The platformId.
       */
      @java.lang.Override
      public int getPlatformId() {
        return platformId_;
      }
      /**
       * <pre>
       *平台
       * </pre>
       *
       * <code>int32 platformId = 5;</code>
       * @param value The platformId to set.
       * @return This builder for chaining.
       */
      public Builder setPlatformId(int value) {
        
        platformId_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *平台
       * </pre>
       *
       * <code>int32 platformId = 5;</code>
       * @return This builder for chaining.
       */
      public Builder clearPlatformId() {
        
        platformId_ = 0;
        onChanged();
        return this;
      }

      private int assets_ ;
      /**
       * <pre>
       *资产 0.all
       * </pre>
       *
       * <code>int32 assets = 6;</code>
       * @return The assets.
       */
      @java.lang.Override
      public int getAssets() {
        return assets_;
      }
      /**
       * <pre>
       *资产 0.all
       * </pre>
       *
       * <code>int32 assets = 6;</code>
       * @param value The assets to set.
       * @return This builder for chaining.
       */
      public Builder setAssets(int value) {
        
        assets_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *资产 0.all
       * </pre>
       *
       * <code>int32 assets = 6;</code>
       * @return This builder for chaining.
       */
      public Builder clearAssets() {
        
        assets_ = 0;
        onChanged();
        return this;
      }

      private int past_ ;
      /**
       * <pre>
       *0.all 1.24小时 2.7day 3.30day 4.60day
       * </pre>
       *
       * <code>int32 past = 7;</code>
       * @return The past.
       */
      @java.lang.Override
      public int getPast() {
        return past_;
      }
      /**
       * <pre>
       *0.all 1.24小时 2.7day 3.30day 4.60day
       * </pre>
       *
       * <code>int32 past = 7;</code>
       * @param value The past to set.
       * @return This builder for chaining.
       */
      public Builder setPast(int value) {
        
        past_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *0.all 1.24小时 2.7day 3.30day 4.60day
       * </pre>
       *
       * <code>int32 past = 7;</code>
       * @return This builder for chaining.
       */
      public Builder clearPast() {
        
        past_ = 0;
        onChanged();
        return this;
      }

      private int page_ ;
      /**
       * <code>int32 page = 8;</code>
       * @return The page.
       */
      @java.lang.Override
      public int getPage() {
        return page_;
      }
      /**
       * <code>int32 page = 8;</code>
       * @param value The page to set.
       * @return This builder for chaining.
       */
      public Builder setPage(int value) {
        
        page_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>int32 page = 8;</code>
       * @return This builder for chaining.
       */
      public Builder clearPage() {
        
        page_ = 0;
        onChanged();
        return this;
      }

      private int pageSize_ ;
      /**
       * <code>int32 pageSize = 9;</code>
       * @return The pageSize.
       */
      @java.lang.Override
      public int getPageSize() {
        return pageSize_;
      }
      /**
       * <code>int32 pageSize = 9;</code>
       * @param value The pageSize to set.
       * @return This builder for chaining.
       */
      public Builder setPageSize(int value) {
        
        pageSize_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>int32 pageSize = 9;</code>
       * @return This builder for chaining.
       */
      public Builder clearPageSize() {
        
        pageSize_ = 0;
        onChanged();
        return this;
      }

      private int total_ ;
      /**
       * <pre>
       *总条数
       * </pre>
       *
       * <code>int32 total = 10;</code>
       * @return The total.
       */
      @java.lang.Override
      public int getTotal() {
        return total_;
      }
      /**
       * <pre>
       *总条数
       * </pre>
       *
       * <code>int32 total = 10;</code>
       * @param value The total to set.
       * @return This builder for chaining.
       */
      public Builder setTotal(int value) {
        
        total_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *总条数
       * </pre>
       *
       * <code>int32 total = 10;</code>
       * @return This builder for chaining.
       */
      public Builder clearTotal() {
        
        total_ = 0;
        onChanged();
        return this;
      }

      private int totalPage_ ;
      /**
       * <pre>
       *总页数
       * </pre>
       *
       * <code>int32 totalPage = 11;</code>
       * @return The totalPage.
       */
      @java.lang.Override
      public int getTotalPage() {
        return totalPage_;
      }
      /**
       * <pre>
       *总页数
       * </pre>
       *
       * <code>int32 totalPage = 11;</code>
       * @param value The totalPage to set.
       * @return This builder for chaining.
       */
      public Builder setTotalPage(int value) {
        
        totalPage_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *总页数
       * </pre>
       *
       * <code>int32 totalPage = 11;</code>
       * @return This builder for chaining.
       */
      public Builder clearTotalPage() {
        
        totalPage_ = 0;
        onChanged();
        return this;
      }

      private java.util.List<com.proto.CommonMessage.BetInfo> betList_ =
        java.util.Collections.emptyList();
      private void ensureBetListIsMutable() {
        if (!((bitField0_ & 0x00000001) != 0)) {
          betList_ = new java.util.ArrayList<com.proto.CommonMessage.BetInfo>(betList_);
          bitField0_ |= 0x00000001;
         }
      }

      private com.google.protobuf.RepeatedFieldBuilderV3<
          com.proto.CommonMessage.BetInfo, com.proto.CommonMessage.BetInfo.Builder, com.proto.CommonMessage.BetInfoOrBuilder> betListBuilder_;

      /**
       * <pre>
       *下注列表
       * </pre>
       *
       * <code>repeated .ProtoMessage.BetInfo betList = 12;</code>
       */
      public java.util.List<com.proto.CommonMessage.BetInfo> getBetListList() {
        if (betListBuilder_ == null) {
          return java.util.Collections.unmodifiableList(betList_);
        } else {
          return betListBuilder_.getMessageList();
        }
      }
      /**
       * <pre>
       *下注列表
       * </pre>
       *
       * <code>repeated .ProtoMessage.BetInfo betList = 12;</code>
       */
      public int getBetListCount() {
        if (betListBuilder_ == null) {
          return betList_.size();
        } else {
          return betListBuilder_.getCount();
        }
      }
      /**
       * <pre>
       *下注列表
       * </pre>
       *
       * <code>repeated .ProtoMessage.BetInfo betList = 12;</code>
       */
      public com.proto.CommonMessage.BetInfo getBetList(int index) {
        if (betListBuilder_ == null) {
          return betList_.get(index);
        } else {
          return betListBuilder_.getMessage(index);
        }
      }
      /**
       * <pre>
       *下注列表
       * </pre>
       *
       * <code>repeated .ProtoMessage.BetInfo betList = 12;</code>
       */
      public Builder setBetList(
          int index, com.proto.CommonMessage.BetInfo value) {
        if (betListBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureBetListIsMutable();
          betList_.set(index, value);
          onChanged();
        } else {
          betListBuilder_.setMessage(index, value);
        }
        return this;
      }
      /**
       * <pre>
       *下注列表
       * </pre>
       *
       * <code>repeated .ProtoMessage.BetInfo betList = 12;</code>
       */
      public Builder setBetList(
          int index, com.proto.CommonMessage.BetInfo.Builder builderForValue) {
        if (betListBuilder_ == null) {
          ensureBetListIsMutable();
          betList_.set(index, builderForValue.build());
          onChanged();
        } else {
          betListBuilder_.setMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <pre>
       *下注列表
       * </pre>
       *
       * <code>repeated .ProtoMessage.BetInfo betList = 12;</code>
       */
      public Builder addBetList(com.proto.CommonMessage.BetInfo value) {
        if (betListBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureBetListIsMutable();
          betList_.add(value);
          onChanged();
        } else {
          betListBuilder_.addMessage(value);
        }
        return this;
      }
      /**
       * <pre>
       *下注列表
       * </pre>
       *
       * <code>repeated .ProtoMessage.BetInfo betList = 12;</code>
       */
      public Builder addBetList(
          int index, com.proto.CommonMessage.BetInfo value) {
        if (betListBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureBetListIsMutable();
          betList_.add(index, value);
          onChanged();
        } else {
          betListBuilder_.addMessage(index, value);
        }
        return this;
      }
      /**
       * <pre>
       *下注列表
       * </pre>
       *
       * <code>repeated .ProtoMessage.BetInfo betList = 12;</code>
       */
      public Builder addBetList(
          com.proto.CommonMessage.BetInfo.Builder builderForValue) {
        if (betListBuilder_ == null) {
          ensureBetListIsMutable();
          betList_.add(builderForValue.build());
          onChanged();
        } else {
          betListBuilder_.addMessage(builderForValue.build());
        }
        return this;
      }
      /**
       * <pre>
       *下注列表
       * </pre>
       *
       * <code>repeated .ProtoMessage.BetInfo betList = 12;</code>
       */
      public Builder addBetList(
          int index, com.proto.CommonMessage.BetInfo.Builder builderForValue) {
        if (betListBuilder_ == null) {
          ensureBetListIsMutable();
          betList_.add(index, builderForValue.build());
          onChanged();
        } else {
          betListBuilder_.addMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <pre>
       *下注列表
       * </pre>
       *
       * <code>repeated .ProtoMessage.BetInfo betList = 12;</code>
       */
      public Builder addAllBetList(
          java.lang.Iterable<? extends com.proto.CommonMessage.BetInfo> values) {
        if (betListBuilder_ == null) {
          ensureBetListIsMutable();
          com.google.protobuf.AbstractMessageLite.Builder.addAll(
              values, betList_);
          onChanged();
        } else {
          betListBuilder_.addAllMessages(values);
        }
        return this;
      }
      /**
       * <pre>
       *下注列表
       * </pre>
       *
       * <code>repeated .ProtoMessage.BetInfo betList = 12;</code>
       */
      public Builder clearBetList() {
        if (betListBuilder_ == null) {
          betList_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000001);
          onChanged();
        } else {
          betListBuilder_.clear();
        }
        return this;
      }
      /**
       * <pre>
       *下注列表
       * </pre>
       *
       * <code>repeated .ProtoMessage.BetInfo betList = 12;</code>
       */
      public Builder removeBetList(int index) {
        if (betListBuilder_ == null) {
          ensureBetListIsMutable();
          betList_.remove(index);
          onChanged();
        } else {
          betListBuilder_.remove(index);
        }
        return this;
      }
      /**
       * <pre>
       *下注列表
       * </pre>
       *
       * <code>repeated .ProtoMessage.BetInfo betList = 12;</code>
       */
      public com.proto.CommonMessage.BetInfo.Builder getBetListBuilder(
          int index) {
        return getBetListFieldBuilder().getBuilder(index);
      }
      /**
       * <pre>
       *下注列表
       * </pre>
       *
       * <code>repeated .ProtoMessage.BetInfo betList = 12;</code>
       */
      public com.proto.CommonMessage.BetInfoOrBuilder getBetListOrBuilder(
          int index) {
        if (betListBuilder_ == null) {
          return betList_.get(index);  } else {
          return betListBuilder_.getMessageOrBuilder(index);
        }
      }
      /**
       * <pre>
       *下注列表
       * </pre>
       *
       * <code>repeated .ProtoMessage.BetInfo betList = 12;</code>
       */
      public java.util.List<? extends com.proto.CommonMessage.BetInfoOrBuilder> 
           getBetListOrBuilderList() {
        if (betListBuilder_ != null) {
          return betListBuilder_.getMessageOrBuilderList();
        } else {
          return java.util.Collections.unmodifiableList(betList_);
        }
      }
      /**
       * <pre>
       *下注列表
       * </pre>
       *
       * <code>repeated .ProtoMessage.BetInfo betList = 12;</code>
       */
      public com.proto.CommonMessage.BetInfo.Builder addBetListBuilder() {
        return getBetListFieldBuilder().addBuilder(
            com.proto.CommonMessage.BetInfo.getDefaultInstance());
      }
      /**
       * <pre>
       *下注列表
       * </pre>
       *
       * <code>repeated .ProtoMessage.BetInfo betList = 12;</code>
       */
      public com.proto.CommonMessage.BetInfo.Builder addBetListBuilder(
          int index) {
        return getBetListFieldBuilder().addBuilder(
            index, com.proto.CommonMessage.BetInfo.getDefaultInstance());
      }
      /**
       * <pre>
       *下注列表
       * </pre>
       *
       * <code>repeated .ProtoMessage.BetInfo betList = 12;</code>
       */
      public java.util.List<com.proto.CommonMessage.BetInfo.Builder> 
           getBetListBuilderList() {
        return getBetListFieldBuilder().getBuilderList();
      }
      private com.google.protobuf.RepeatedFieldBuilderV3<
          com.proto.CommonMessage.BetInfo, com.proto.CommonMessage.BetInfo.Builder, com.proto.CommonMessage.BetInfoOrBuilder> 
          getBetListFieldBuilder() {
        if (betListBuilder_ == null) {
          betListBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
              com.proto.CommonMessage.BetInfo, com.proto.CommonMessage.BetInfo.Builder, com.proto.CommonMessage.BetInfoOrBuilder>(
                  betList_,
                  ((bitField0_ & 0x00000001) != 0),
                  getParentForChildren(),
                  isClean());
          betList_ = null;
        }
        return betListBuilder_;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:ProtoMessage.ResBetHistoryDataMessage)
    }

    // @@protoc_insertion_point(class_scope:ProtoMessage.ResBetHistoryDataMessage)
    private static final com.proto.WalletMessage.ResBetHistoryDataMessage DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.proto.WalletMessage.ResBetHistoryDataMessage();
    }

    public static com.proto.WalletMessage.ResBetHistoryDataMessage getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<ResBetHistoryDataMessage>
        PARSER = new com.google.protobuf.AbstractParser<ResBetHistoryDataMessage>() {
      @java.lang.Override
      public ResBetHistoryDataMessage parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new ResBetHistoryDataMessage(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<ResBetHistoryDataMessage> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<ResBetHistoryDataMessage> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.proto.WalletMessage.ResBetHistoryDataMessage getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface ReqTransactionDataMessageOrBuilder extends
      // @@protoc_insertion_point(interface_extends:ProtoMessage.ReqTransactionDataMessage)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>int32 msgID = 1;</code>
     * @return The msgID.
     */
    int getMsgID();

    /**
     * <pre>
     *0.all 1.deposit 2.withdraw 3.bill
     * </pre>
     *
     * <code>int32 transactionType = 2;</code>
     * @return The transactionType.
     */
    int getTransactionType();

    /**
     * <pre>
     *资产 0.all
     * </pre>
     *
     * <code>int32 assets = 3;</code>
     * @return The assets.
     */
    int getAssets();

    /**
     * <pre>
     *1.24小时 2.7day 3.30day 4.60day 5.90day
     * </pre>
     *
     * <code>int32 past = 4;</code>
     * @return The past.
     */
    int getPast();

    /**
     * <pre>
     *0.all 1.complete 2.processing 3.failed 4.canceled
     * </pre>
     *
     * <code>int32 status = 5;</code>
     * @return The status.
     */
    int getStatus();

    /**
     * <pre>
     *页
     * </pre>
     *
     * <code>int32 page = 6;</code>
     * @return The page.
     */
    int getPage();

    /**
     * <pre>
     *页数量
     * </pre>
     *
     * <code>int32 pageSize = 7;</code>
     * @return The pageSize.
     */
    int getPageSize();

    /**
     * <pre>
     *开始时间
     * </pre>
     *
     * <code>int64 statTime = 8;</code>
     * @return The statTime.
     */
    long getStatTime();

    /**
     * <pre>
     *结束时间
     * </pre>
     *
     * <code>int64 endTime = 9;</code>
     * @return The endTime.
     */
    long getEndTime();

    /**
     * <pre>
     *语言id
     * </pre>
     *
     * <code>int32 language = 10;</code>
     * @return The language.
     */
    int getLanguage();
  }
  /**
   * <pre>
   *请求交易
   * </pre>
   *
   * Protobuf type {@code ProtoMessage.ReqTransactionDataMessage}
   */
  public static final class ReqTransactionDataMessage extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:ProtoMessage.ReqTransactionDataMessage)
      ReqTransactionDataMessageOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use ReqTransactionDataMessage.newBuilder() to construct.
    private ReqTransactionDataMessage(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private ReqTransactionDataMessage() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new ReqTransactionDataMessage();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private ReqTransactionDataMessage(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {

              msgID_ = input.readInt32();
              break;
            }
            case 16: {

              transactionType_ = input.readInt32();
              break;
            }
            case 24: {

              assets_ = input.readInt32();
              break;
            }
            case 32: {

              past_ = input.readInt32();
              break;
            }
            case 40: {

              status_ = input.readInt32();
              break;
            }
            case 48: {

              page_ = input.readInt32();
              break;
            }
            case 56: {

              pageSize_ = input.readInt32();
              break;
            }
            case 64: {

              statTime_ = input.readInt64();
              break;
            }
            case 72: {

              endTime_ = input.readInt64();
              break;
            }
            case 80: {

              language_ = input.readInt32();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.proto.WalletMessage.internal_static_ProtoMessage_ReqTransactionDataMessage_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.proto.WalletMessage.internal_static_ProtoMessage_ReqTransactionDataMessage_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.proto.WalletMessage.ReqTransactionDataMessage.class, com.proto.WalletMessage.ReqTransactionDataMessage.Builder.class);
    }

    public static final int MSGID_FIELD_NUMBER = 1;
    private int msgID_;
    /**
     * <code>int32 msgID = 1;</code>
     * @return The msgID.
     */
    @java.lang.Override
    public int getMsgID() {
      return msgID_;
    }

    public static final int TRANSACTIONTYPE_FIELD_NUMBER = 2;
    private int transactionType_;
    /**
     * <pre>
     *0.all 1.deposit 2.withdraw 3.bill
     * </pre>
     *
     * <code>int32 transactionType = 2;</code>
     * @return The transactionType.
     */
    @java.lang.Override
    public int getTransactionType() {
      return transactionType_;
    }

    public static final int ASSETS_FIELD_NUMBER = 3;
    private int assets_;
    /**
     * <pre>
     *资产 0.all
     * </pre>
     *
     * <code>int32 assets = 3;</code>
     * @return The assets.
     */
    @java.lang.Override
    public int getAssets() {
      return assets_;
    }

    public static final int PAST_FIELD_NUMBER = 4;
    private int past_;
    /**
     * <pre>
     *1.24小时 2.7day 3.30day 4.60day 5.90day
     * </pre>
     *
     * <code>int32 past = 4;</code>
     * @return The past.
     */
    @java.lang.Override
    public int getPast() {
      return past_;
    }

    public static final int STATUS_FIELD_NUMBER = 5;
    private int status_;
    /**
     * <pre>
     *0.all 1.complete 2.processing 3.failed 4.canceled
     * </pre>
     *
     * <code>int32 status = 5;</code>
     * @return The status.
     */
    @java.lang.Override
    public int getStatus() {
      return status_;
    }

    public static final int PAGE_FIELD_NUMBER = 6;
    private int page_;
    /**
     * <pre>
     *页
     * </pre>
     *
     * <code>int32 page = 6;</code>
     * @return The page.
     */
    @java.lang.Override
    public int getPage() {
      return page_;
    }

    public static final int PAGESIZE_FIELD_NUMBER = 7;
    private int pageSize_;
    /**
     * <pre>
     *页数量
     * </pre>
     *
     * <code>int32 pageSize = 7;</code>
     * @return The pageSize.
     */
    @java.lang.Override
    public int getPageSize() {
      return pageSize_;
    }

    public static final int STATTIME_FIELD_NUMBER = 8;
    private long statTime_;
    /**
     * <pre>
     *开始时间
     * </pre>
     *
     * <code>int64 statTime = 8;</code>
     * @return The statTime.
     */
    @java.lang.Override
    public long getStatTime() {
      return statTime_;
    }

    public static final int ENDTIME_FIELD_NUMBER = 9;
    private long endTime_;
    /**
     * <pre>
     *结束时间
     * </pre>
     *
     * <code>int64 endTime = 9;</code>
     * @return The endTime.
     */
    @java.lang.Override
    public long getEndTime() {
      return endTime_;
    }

    public static final int LANGUAGE_FIELD_NUMBER = 10;
    private int language_;
    /**
     * <pre>
     *语言id
     * </pre>
     *
     * <code>int32 language = 10;</code>
     * @return The language.
     */
    @java.lang.Override
    public int getLanguage() {
      return language_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (msgID_ != 0) {
        output.writeInt32(1, msgID_);
      }
      if (transactionType_ != 0) {
        output.writeInt32(2, transactionType_);
      }
      if (assets_ != 0) {
        output.writeInt32(3, assets_);
      }
      if (past_ != 0) {
        output.writeInt32(4, past_);
      }
      if (status_ != 0) {
        output.writeInt32(5, status_);
      }
      if (page_ != 0) {
        output.writeInt32(6, page_);
      }
      if (pageSize_ != 0) {
        output.writeInt32(7, pageSize_);
      }
      if (statTime_ != 0L) {
        output.writeInt64(8, statTime_);
      }
      if (endTime_ != 0L) {
        output.writeInt64(9, endTime_);
      }
      if (language_ != 0) {
        output.writeInt32(10, language_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (msgID_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, msgID_);
      }
      if (transactionType_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(2, transactionType_);
      }
      if (assets_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(3, assets_);
      }
      if (past_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(4, past_);
      }
      if (status_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(5, status_);
      }
      if (page_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(6, page_);
      }
      if (pageSize_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(7, pageSize_);
      }
      if (statTime_ != 0L) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(8, statTime_);
      }
      if (endTime_ != 0L) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(9, endTime_);
      }
      if (language_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(10, language_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.proto.WalletMessage.ReqTransactionDataMessage)) {
        return super.equals(obj);
      }
      com.proto.WalletMessage.ReqTransactionDataMessage other = (com.proto.WalletMessage.ReqTransactionDataMessage) obj;

      if (getMsgID()
          != other.getMsgID()) return false;
      if (getTransactionType()
          != other.getTransactionType()) return false;
      if (getAssets()
          != other.getAssets()) return false;
      if (getPast()
          != other.getPast()) return false;
      if (getStatus()
          != other.getStatus()) return false;
      if (getPage()
          != other.getPage()) return false;
      if (getPageSize()
          != other.getPageSize()) return false;
      if (getStatTime()
          != other.getStatTime()) return false;
      if (getEndTime()
          != other.getEndTime()) return false;
      if (getLanguage()
          != other.getLanguage()) return false;
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + MSGID_FIELD_NUMBER;
      hash = (53 * hash) + getMsgID();
      hash = (37 * hash) + TRANSACTIONTYPE_FIELD_NUMBER;
      hash = (53 * hash) + getTransactionType();
      hash = (37 * hash) + ASSETS_FIELD_NUMBER;
      hash = (53 * hash) + getAssets();
      hash = (37 * hash) + PAST_FIELD_NUMBER;
      hash = (53 * hash) + getPast();
      hash = (37 * hash) + STATUS_FIELD_NUMBER;
      hash = (53 * hash) + getStatus();
      hash = (37 * hash) + PAGE_FIELD_NUMBER;
      hash = (53 * hash) + getPage();
      hash = (37 * hash) + PAGESIZE_FIELD_NUMBER;
      hash = (53 * hash) + getPageSize();
      hash = (37 * hash) + STATTIME_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getStatTime());
      hash = (37 * hash) + ENDTIME_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getEndTime());
      hash = (37 * hash) + LANGUAGE_FIELD_NUMBER;
      hash = (53 * hash) + getLanguage();
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.proto.WalletMessage.ReqTransactionDataMessage parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.proto.WalletMessage.ReqTransactionDataMessage parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.proto.WalletMessage.ReqTransactionDataMessage parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.proto.WalletMessage.ReqTransactionDataMessage parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.proto.WalletMessage.ReqTransactionDataMessage parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.proto.WalletMessage.ReqTransactionDataMessage parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.proto.WalletMessage.ReqTransactionDataMessage parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.proto.WalletMessage.ReqTransactionDataMessage parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.proto.WalletMessage.ReqTransactionDataMessage parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.proto.WalletMessage.ReqTransactionDataMessage parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.proto.WalletMessage.ReqTransactionDataMessage parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.proto.WalletMessage.ReqTransactionDataMessage parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.proto.WalletMessage.ReqTransactionDataMessage prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     *请求交易
     * </pre>
     *
     * Protobuf type {@code ProtoMessage.ReqTransactionDataMessage}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:ProtoMessage.ReqTransactionDataMessage)
        com.proto.WalletMessage.ReqTransactionDataMessageOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.proto.WalletMessage.internal_static_ProtoMessage_ReqTransactionDataMessage_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.proto.WalletMessage.internal_static_ProtoMessage_ReqTransactionDataMessage_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.proto.WalletMessage.ReqTransactionDataMessage.class, com.proto.WalletMessage.ReqTransactionDataMessage.Builder.class);
      }

      // Construct using com.proto.WalletMessage.ReqTransactionDataMessage.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        msgID_ = 0;

        transactionType_ = 0;

        assets_ = 0;

        past_ = 0;

        status_ = 0;

        page_ = 0;

        pageSize_ = 0;

        statTime_ = 0L;

        endTime_ = 0L;

        language_ = 0;

        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.proto.WalletMessage.internal_static_ProtoMessage_ReqTransactionDataMessage_descriptor;
      }

      @java.lang.Override
      public com.proto.WalletMessage.ReqTransactionDataMessage getDefaultInstanceForType() {
        return com.proto.WalletMessage.ReqTransactionDataMessage.getDefaultInstance();
      }

      @java.lang.Override
      public com.proto.WalletMessage.ReqTransactionDataMessage build() {
        com.proto.WalletMessage.ReqTransactionDataMessage result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.proto.WalletMessage.ReqTransactionDataMessage buildPartial() {
        com.proto.WalletMessage.ReqTransactionDataMessage result = new com.proto.WalletMessage.ReqTransactionDataMessage(this);
        result.msgID_ = msgID_;
        result.transactionType_ = transactionType_;
        result.assets_ = assets_;
        result.past_ = past_;
        result.status_ = status_;
        result.page_ = page_;
        result.pageSize_ = pageSize_;
        result.statTime_ = statTime_;
        result.endTime_ = endTime_;
        result.language_ = language_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.proto.WalletMessage.ReqTransactionDataMessage) {
          return mergeFrom((com.proto.WalletMessage.ReqTransactionDataMessage)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.proto.WalletMessage.ReqTransactionDataMessage other) {
        if (other == com.proto.WalletMessage.ReqTransactionDataMessage.getDefaultInstance()) return this;
        if (other.getMsgID() != 0) {
          setMsgID(other.getMsgID());
        }
        if (other.getTransactionType() != 0) {
          setTransactionType(other.getTransactionType());
        }
        if (other.getAssets() != 0) {
          setAssets(other.getAssets());
        }
        if (other.getPast() != 0) {
          setPast(other.getPast());
        }
        if (other.getStatus() != 0) {
          setStatus(other.getStatus());
        }
        if (other.getPage() != 0) {
          setPage(other.getPage());
        }
        if (other.getPageSize() != 0) {
          setPageSize(other.getPageSize());
        }
        if (other.getStatTime() != 0L) {
          setStatTime(other.getStatTime());
        }
        if (other.getEndTime() != 0L) {
          setEndTime(other.getEndTime());
        }
        if (other.getLanguage() != 0) {
          setLanguage(other.getLanguage());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.proto.WalletMessage.ReqTransactionDataMessage parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.proto.WalletMessage.ReqTransactionDataMessage) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }

      private int msgID_ ;
      /**
       * <code>int32 msgID = 1;</code>
       * @return The msgID.
       */
      @java.lang.Override
      public int getMsgID() {
        return msgID_;
      }
      /**
       * <code>int32 msgID = 1;</code>
       * @param value The msgID to set.
       * @return This builder for chaining.
       */
      public Builder setMsgID(int value) {
        
        msgID_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>int32 msgID = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearMsgID() {
        
        msgID_ = 0;
        onChanged();
        return this;
      }

      private int transactionType_ ;
      /**
       * <pre>
       *0.all 1.deposit 2.withdraw 3.bill
       * </pre>
       *
       * <code>int32 transactionType = 2;</code>
       * @return The transactionType.
       */
      @java.lang.Override
      public int getTransactionType() {
        return transactionType_;
      }
      /**
       * <pre>
       *0.all 1.deposit 2.withdraw 3.bill
       * </pre>
       *
       * <code>int32 transactionType = 2;</code>
       * @param value The transactionType to set.
       * @return This builder for chaining.
       */
      public Builder setTransactionType(int value) {
        
        transactionType_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *0.all 1.deposit 2.withdraw 3.bill
       * </pre>
       *
       * <code>int32 transactionType = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearTransactionType() {
        
        transactionType_ = 0;
        onChanged();
        return this;
      }

      private int assets_ ;
      /**
       * <pre>
       *资产 0.all
       * </pre>
       *
       * <code>int32 assets = 3;</code>
       * @return The assets.
       */
      @java.lang.Override
      public int getAssets() {
        return assets_;
      }
      /**
       * <pre>
       *资产 0.all
       * </pre>
       *
       * <code>int32 assets = 3;</code>
       * @param value The assets to set.
       * @return This builder for chaining.
       */
      public Builder setAssets(int value) {
        
        assets_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *资产 0.all
       * </pre>
       *
       * <code>int32 assets = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearAssets() {
        
        assets_ = 0;
        onChanged();
        return this;
      }

      private int past_ ;
      /**
       * <pre>
       *1.24小时 2.7day 3.30day 4.60day 5.90day
       * </pre>
       *
       * <code>int32 past = 4;</code>
       * @return The past.
       */
      @java.lang.Override
      public int getPast() {
        return past_;
      }
      /**
       * <pre>
       *1.24小时 2.7day 3.30day 4.60day 5.90day
       * </pre>
       *
       * <code>int32 past = 4;</code>
       * @param value The past to set.
       * @return This builder for chaining.
       */
      public Builder setPast(int value) {
        
        past_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *1.24小时 2.7day 3.30day 4.60day 5.90day
       * </pre>
       *
       * <code>int32 past = 4;</code>
       * @return This builder for chaining.
       */
      public Builder clearPast() {
        
        past_ = 0;
        onChanged();
        return this;
      }

      private int status_ ;
      /**
       * <pre>
       *0.all 1.complete 2.processing 3.failed 4.canceled
       * </pre>
       *
       * <code>int32 status = 5;</code>
       * @return The status.
       */
      @java.lang.Override
      public int getStatus() {
        return status_;
      }
      /**
       * <pre>
       *0.all 1.complete 2.processing 3.failed 4.canceled
       * </pre>
       *
       * <code>int32 status = 5;</code>
       * @param value The status to set.
       * @return This builder for chaining.
       */
      public Builder setStatus(int value) {
        
        status_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *0.all 1.complete 2.processing 3.failed 4.canceled
       * </pre>
       *
       * <code>int32 status = 5;</code>
       * @return This builder for chaining.
       */
      public Builder clearStatus() {
        
        status_ = 0;
        onChanged();
        return this;
      }

      private int page_ ;
      /**
       * <pre>
       *页
       * </pre>
       *
       * <code>int32 page = 6;</code>
       * @return The page.
       */
      @java.lang.Override
      public int getPage() {
        return page_;
      }
      /**
       * <pre>
       *页
       * </pre>
       *
       * <code>int32 page = 6;</code>
       * @param value The page to set.
       * @return This builder for chaining.
       */
      public Builder setPage(int value) {
        
        page_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *页
       * </pre>
       *
       * <code>int32 page = 6;</code>
       * @return This builder for chaining.
       */
      public Builder clearPage() {
        
        page_ = 0;
        onChanged();
        return this;
      }

      private int pageSize_ ;
      /**
       * <pre>
       *页数量
       * </pre>
       *
       * <code>int32 pageSize = 7;</code>
       * @return The pageSize.
       */
      @java.lang.Override
      public int getPageSize() {
        return pageSize_;
      }
      /**
       * <pre>
       *页数量
       * </pre>
       *
       * <code>int32 pageSize = 7;</code>
       * @param value The pageSize to set.
       * @return This builder for chaining.
       */
      public Builder setPageSize(int value) {
        
        pageSize_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *页数量
       * </pre>
       *
       * <code>int32 pageSize = 7;</code>
       * @return This builder for chaining.
       */
      public Builder clearPageSize() {
        
        pageSize_ = 0;
        onChanged();
        return this;
      }

      private long statTime_ ;
      /**
       * <pre>
       *开始时间
       * </pre>
       *
       * <code>int64 statTime = 8;</code>
       * @return The statTime.
       */
      @java.lang.Override
      public long getStatTime() {
        return statTime_;
      }
      /**
       * <pre>
       *开始时间
       * </pre>
       *
       * <code>int64 statTime = 8;</code>
       * @param value The statTime to set.
       * @return This builder for chaining.
       */
      public Builder setStatTime(long value) {
        
        statTime_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *开始时间
       * </pre>
       *
       * <code>int64 statTime = 8;</code>
       * @return This builder for chaining.
       */
      public Builder clearStatTime() {
        
        statTime_ = 0L;
        onChanged();
        return this;
      }

      private long endTime_ ;
      /**
       * <pre>
       *结束时间
       * </pre>
       *
       * <code>int64 endTime = 9;</code>
       * @return The endTime.
       */
      @java.lang.Override
      public long getEndTime() {
        return endTime_;
      }
      /**
       * <pre>
       *结束时间
       * </pre>
       *
       * <code>int64 endTime = 9;</code>
       * @param value The endTime to set.
       * @return This builder for chaining.
       */
      public Builder setEndTime(long value) {
        
        endTime_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *结束时间
       * </pre>
       *
       * <code>int64 endTime = 9;</code>
       * @return This builder for chaining.
       */
      public Builder clearEndTime() {
        
        endTime_ = 0L;
        onChanged();
        return this;
      }

      private int language_ ;
      /**
       * <pre>
       *语言id
       * </pre>
       *
       * <code>int32 language = 10;</code>
       * @return The language.
       */
      @java.lang.Override
      public int getLanguage() {
        return language_;
      }
      /**
       * <pre>
       *语言id
       * </pre>
       *
       * <code>int32 language = 10;</code>
       * @param value The language to set.
       * @return This builder for chaining.
       */
      public Builder setLanguage(int value) {
        
        language_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *语言id
       * </pre>
       *
       * <code>int32 language = 10;</code>
       * @return This builder for chaining.
       */
      public Builder clearLanguage() {
        
        language_ = 0;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:ProtoMessage.ReqTransactionDataMessage)
    }

    // @@protoc_insertion_point(class_scope:ProtoMessage.ReqTransactionDataMessage)
    private static final com.proto.WalletMessage.ReqTransactionDataMessage DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.proto.WalletMessage.ReqTransactionDataMessage();
    }

    public static com.proto.WalletMessage.ReqTransactionDataMessage getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<ReqTransactionDataMessage>
        PARSER = new com.google.protobuf.AbstractParser<ReqTransactionDataMessage>() {
      @java.lang.Override
      public ReqTransactionDataMessage parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new ReqTransactionDataMessage(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<ReqTransactionDataMessage> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<ReqTransactionDataMessage> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.proto.WalletMessage.ReqTransactionDataMessage getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface ResTransactionDataMessageOrBuilder extends
      // @@protoc_insertion_point(interface_extends:ProtoMessage.ResTransactionDataMessage)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>int32 msgID = 1;</code>
     * @return The msgID.
     */
    int getMsgID();

    /**
     * <pre>
     *错误码
     * </pre>
     *
     * <code>int32 error = 2;</code>
     * @return The error.
     */
    int getError();

    /**
     * <pre>
     *0.all 1.deposit 2.withdraw 3.bill
     * </pre>
     *
     * <code>int32 transactionType = 3;</code>
     * @return The transactionType.
     */
    int getTransactionType();

    /**
     * <pre>
     *资产 0.all
     * </pre>
     *
     * <code>int32 assets = 4;</code>
     * @return The assets.
     */
    int getAssets();

    /**
     * <pre>
     *1.24小时 2.7day 3.30day 4.60day 5.90day
     * </pre>
     *
     * <code>int32 past = 5;</code>
     * @return The past.
     */
    int getPast();

    /**
     * <pre>
     *0.all 1.complete 2.processing 3.failed 4.canceled
     * </pre>
     *
     * <code>int32 status = 6;</code>
     * @return The status.
     */
    int getStatus();

    /**
     * <code>int32 page = 7;</code>
     * @return The page.
     */
    int getPage();

    /**
     * <code>int32 pageSize = 8;</code>
     * @return The pageSize.
     */
    int getPageSize();

    /**
     * <pre>
     *总条数
     * </pre>
     *
     * <code>int32 total = 9;</code>
     * @return The total.
     */
    int getTotal();

    /**
     * <pre>
     *总页数
     * </pre>
     *
     * <code>int32 totalPage = 10;</code>
     * @return The totalPage.
     */
    int getTotalPage();

    /**
     * <pre>
     *交易列表
     * </pre>
     *
     * <code>repeated .ProtoMessage.TransactionInfo transactionList = 11;</code>
     */
    java.util.List<com.proto.WalletMessage.TransactionInfo> 
        getTransactionListList();
    /**
     * <pre>
     *交易列表
     * </pre>
     *
     * <code>repeated .ProtoMessage.TransactionInfo transactionList = 11;</code>
     */
    com.proto.WalletMessage.TransactionInfo getTransactionList(int index);
    /**
     * <pre>
     *交易列表
     * </pre>
     *
     * <code>repeated .ProtoMessage.TransactionInfo transactionList = 11;</code>
     */
    int getTransactionListCount();
    /**
     * <pre>
     *交易列表
     * </pre>
     *
     * <code>repeated .ProtoMessage.TransactionInfo transactionList = 11;</code>
     */
    java.util.List<? extends com.proto.WalletMessage.TransactionInfoOrBuilder> 
        getTransactionListOrBuilderList();
    /**
     * <pre>
     *交易列表
     * </pre>
     *
     * <code>repeated .ProtoMessage.TransactionInfo transactionList = 11;</code>
     */
    com.proto.WalletMessage.TransactionInfoOrBuilder getTransactionListOrBuilder(
        int index);
  }
  /**
   * <pre>
   *返回交易
   * </pre>
   *
   * Protobuf type {@code ProtoMessage.ResTransactionDataMessage}
   */
  public static final class ResTransactionDataMessage extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:ProtoMessage.ResTransactionDataMessage)
      ResTransactionDataMessageOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use ResTransactionDataMessage.newBuilder() to construct.
    private ResTransactionDataMessage(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private ResTransactionDataMessage() {
      transactionList_ = java.util.Collections.emptyList();
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new ResTransactionDataMessage();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private ResTransactionDataMessage(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {

              msgID_ = input.readInt32();
              break;
            }
            case 16: {

              error_ = input.readInt32();
              break;
            }
            case 24: {

              transactionType_ = input.readInt32();
              break;
            }
            case 32: {

              assets_ = input.readInt32();
              break;
            }
            case 40: {

              past_ = input.readInt32();
              break;
            }
            case 48: {

              status_ = input.readInt32();
              break;
            }
            case 56: {

              page_ = input.readInt32();
              break;
            }
            case 64: {

              pageSize_ = input.readInt32();
              break;
            }
            case 72: {

              total_ = input.readInt32();
              break;
            }
            case 80: {

              totalPage_ = input.readInt32();
              break;
            }
            case 90: {
              if (!((mutable_bitField0_ & 0x00000001) != 0)) {
                transactionList_ = new java.util.ArrayList<com.proto.WalletMessage.TransactionInfo>();
                mutable_bitField0_ |= 0x00000001;
              }
              transactionList_.add(
                  input.readMessage(com.proto.WalletMessage.TransactionInfo.parser(), extensionRegistry));
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        if (((mutable_bitField0_ & 0x00000001) != 0)) {
          transactionList_ = java.util.Collections.unmodifiableList(transactionList_);
        }
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.proto.WalletMessage.internal_static_ProtoMessage_ResTransactionDataMessage_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.proto.WalletMessage.internal_static_ProtoMessage_ResTransactionDataMessage_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.proto.WalletMessage.ResTransactionDataMessage.class, com.proto.WalletMessage.ResTransactionDataMessage.Builder.class);
    }

    public static final int MSGID_FIELD_NUMBER = 1;
    private int msgID_;
    /**
     * <code>int32 msgID = 1;</code>
     * @return The msgID.
     */
    @java.lang.Override
    public int getMsgID() {
      return msgID_;
    }

    public static final int ERROR_FIELD_NUMBER = 2;
    private int error_;
    /**
     * <pre>
     *错误码
     * </pre>
     *
     * <code>int32 error = 2;</code>
     * @return The error.
     */
    @java.lang.Override
    public int getError() {
      return error_;
    }

    public static final int TRANSACTIONTYPE_FIELD_NUMBER = 3;
    private int transactionType_;
    /**
     * <pre>
     *0.all 1.deposit 2.withdraw 3.bill
     * </pre>
     *
     * <code>int32 transactionType = 3;</code>
     * @return The transactionType.
     */
    @java.lang.Override
    public int getTransactionType() {
      return transactionType_;
    }

    public static final int ASSETS_FIELD_NUMBER = 4;
    private int assets_;
    /**
     * <pre>
     *资产 0.all
     * </pre>
     *
     * <code>int32 assets = 4;</code>
     * @return The assets.
     */
    @java.lang.Override
    public int getAssets() {
      return assets_;
    }

    public static final int PAST_FIELD_NUMBER = 5;
    private int past_;
    /**
     * <pre>
     *1.24小时 2.7day 3.30day 4.60day 5.90day
     * </pre>
     *
     * <code>int32 past = 5;</code>
     * @return The past.
     */
    @java.lang.Override
    public int getPast() {
      return past_;
    }

    public static final int STATUS_FIELD_NUMBER = 6;
    private int status_;
    /**
     * <pre>
     *0.all 1.complete 2.processing 3.failed 4.canceled
     * </pre>
     *
     * <code>int32 status = 6;</code>
     * @return The status.
     */
    @java.lang.Override
    public int getStatus() {
      return status_;
    }

    public static final int PAGE_FIELD_NUMBER = 7;
    private int page_;
    /**
     * <code>int32 page = 7;</code>
     * @return The page.
     */
    @java.lang.Override
    public int getPage() {
      return page_;
    }

    public static final int PAGESIZE_FIELD_NUMBER = 8;
    private int pageSize_;
    /**
     * <code>int32 pageSize = 8;</code>
     * @return The pageSize.
     */
    @java.lang.Override
    public int getPageSize() {
      return pageSize_;
    }

    public static final int TOTAL_FIELD_NUMBER = 9;
    private int total_;
    /**
     * <pre>
     *总条数
     * </pre>
     *
     * <code>int32 total = 9;</code>
     * @return The total.
     */
    @java.lang.Override
    public int getTotal() {
      return total_;
    }

    public static final int TOTALPAGE_FIELD_NUMBER = 10;
    private int totalPage_;
    /**
     * <pre>
     *总页数
     * </pre>
     *
     * <code>int32 totalPage = 10;</code>
     * @return The totalPage.
     */
    @java.lang.Override
    public int getTotalPage() {
      return totalPage_;
    }

    public static final int TRANSACTIONLIST_FIELD_NUMBER = 11;
    private java.util.List<com.proto.WalletMessage.TransactionInfo> transactionList_;
    /**
     * <pre>
     *交易列表
     * </pre>
     *
     * <code>repeated .ProtoMessage.TransactionInfo transactionList = 11;</code>
     */
    @java.lang.Override
    public java.util.List<com.proto.WalletMessage.TransactionInfo> getTransactionListList() {
      return transactionList_;
    }
    /**
     * <pre>
     *交易列表
     * </pre>
     *
     * <code>repeated .ProtoMessage.TransactionInfo transactionList = 11;</code>
     */
    @java.lang.Override
    public java.util.List<? extends com.proto.WalletMessage.TransactionInfoOrBuilder> 
        getTransactionListOrBuilderList() {
      return transactionList_;
    }
    /**
     * <pre>
     *交易列表
     * </pre>
     *
     * <code>repeated .ProtoMessage.TransactionInfo transactionList = 11;</code>
     */
    @java.lang.Override
    public int getTransactionListCount() {
      return transactionList_.size();
    }
    /**
     * <pre>
     *交易列表
     * </pre>
     *
     * <code>repeated .ProtoMessage.TransactionInfo transactionList = 11;</code>
     */
    @java.lang.Override
    public com.proto.WalletMessage.TransactionInfo getTransactionList(int index) {
      return transactionList_.get(index);
    }
    /**
     * <pre>
     *交易列表
     * </pre>
     *
     * <code>repeated .ProtoMessage.TransactionInfo transactionList = 11;</code>
     */
    @java.lang.Override
    public com.proto.WalletMessage.TransactionInfoOrBuilder getTransactionListOrBuilder(
        int index) {
      return transactionList_.get(index);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (msgID_ != 0) {
        output.writeInt32(1, msgID_);
      }
      if (error_ != 0) {
        output.writeInt32(2, error_);
      }
      if (transactionType_ != 0) {
        output.writeInt32(3, transactionType_);
      }
      if (assets_ != 0) {
        output.writeInt32(4, assets_);
      }
      if (past_ != 0) {
        output.writeInt32(5, past_);
      }
      if (status_ != 0) {
        output.writeInt32(6, status_);
      }
      if (page_ != 0) {
        output.writeInt32(7, page_);
      }
      if (pageSize_ != 0) {
        output.writeInt32(8, pageSize_);
      }
      if (total_ != 0) {
        output.writeInt32(9, total_);
      }
      if (totalPage_ != 0) {
        output.writeInt32(10, totalPage_);
      }
      for (int i = 0; i < transactionList_.size(); i++) {
        output.writeMessage(11, transactionList_.get(i));
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (msgID_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, msgID_);
      }
      if (error_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(2, error_);
      }
      if (transactionType_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(3, transactionType_);
      }
      if (assets_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(4, assets_);
      }
      if (past_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(5, past_);
      }
      if (status_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(6, status_);
      }
      if (page_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(7, page_);
      }
      if (pageSize_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(8, pageSize_);
      }
      if (total_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(9, total_);
      }
      if (totalPage_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(10, totalPage_);
      }
      for (int i = 0; i < transactionList_.size(); i++) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(11, transactionList_.get(i));
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.proto.WalletMessage.ResTransactionDataMessage)) {
        return super.equals(obj);
      }
      com.proto.WalletMessage.ResTransactionDataMessage other = (com.proto.WalletMessage.ResTransactionDataMessage) obj;

      if (getMsgID()
          != other.getMsgID()) return false;
      if (getError()
          != other.getError()) return false;
      if (getTransactionType()
          != other.getTransactionType()) return false;
      if (getAssets()
          != other.getAssets()) return false;
      if (getPast()
          != other.getPast()) return false;
      if (getStatus()
          != other.getStatus()) return false;
      if (getPage()
          != other.getPage()) return false;
      if (getPageSize()
          != other.getPageSize()) return false;
      if (getTotal()
          != other.getTotal()) return false;
      if (getTotalPage()
          != other.getTotalPage()) return false;
      if (!getTransactionListList()
          .equals(other.getTransactionListList())) return false;
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + MSGID_FIELD_NUMBER;
      hash = (53 * hash) + getMsgID();
      hash = (37 * hash) + ERROR_FIELD_NUMBER;
      hash = (53 * hash) + getError();
      hash = (37 * hash) + TRANSACTIONTYPE_FIELD_NUMBER;
      hash = (53 * hash) + getTransactionType();
      hash = (37 * hash) + ASSETS_FIELD_NUMBER;
      hash = (53 * hash) + getAssets();
      hash = (37 * hash) + PAST_FIELD_NUMBER;
      hash = (53 * hash) + getPast();
      hash = (37 * hash) + STATUS_FIELD_NUMBER;
      hash = (53 * hash) + getStatus();
      hash = (37 * hash) + PAGE_FIELD_NUMBER;
      hash = (53 * hash) + getPage();
      hash = (37 * hash) + PAGESIZE_FIELD_NUMBER;
      hash = (53 * hash) + getPageSize();
      hash = (37 * hash) + TOTAL_FIELD_NUMBER;
      hash = (53 * hash) + getTotal();
      hash = (37 * hash) + TOTALPAGE_FIELD_NUMBER;
      hash = (53 * hash) + getTotalPage();
      if (getTransactionListCount() > 0) {
        hash = (37 * hash) + TRANSACTIONLIST_FIELD_NUMBER;
        hash = (53 * hash) + getTransactionListList().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.proto.WalletMessage.ResTransactionDataMessage parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.proto.WalletMessage.ResTransactionDataMessage parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.proto.WalletMessage.ResTransactionDataMessage parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.proto.WalletMessage.ResTransactionDataMessage parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.proto.WalletMessage.ResTransactionDataMessage parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.proto.WalletMessage.ResTransactionDataMessage parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.proto.WalletMessage.ResTransactionDataMessage parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.proto.WalletMessage.ResTransactionDataMessage parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.proto.WalletMessage.ResTransactionDataMessage parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.proto.WalletMessage.ResTransactionDataMessage parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.proto.WalletMessage.ResTransactionDataMessage parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.proto.WalletMessage.ResTransactionDataMessage parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.proto.WalletMessage.ResTransactionDataMessage prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     *返回交易
     * </pre>
     *
     * Protobuf type {@code ProtoMessage.ResTransactionDataMessage}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:ProtoMessage.ResTransactionDataMessage)
        com.proto.WalletMessage.ResTransactionDataMessageOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.proto.WalletMessage.internal_static_ProtoMessage_ResTransactionDataMessage_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.proto.WalletMessage.internal_static_ProtoMessage_ResTransactionDataMessage_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.proto.WalletMessage.ResTransactionDataMessage.class, com.proto.WalletMessage.ResTransactionDataMessage.Builder.class);
      }

      // Construct using com.proto.WalletMessage.ResTransactionDataMessage.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
          getTransactionListFieldBuilder();
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        msgID_ = 0;

        error_ = 0;

        transactionType_ = 0;

        assets_ = 0;

        past_ = 0;

        status_ = 0;

        page_ = 0;

        pageSize_ = 0;

        total_ = 0;

        totalPage_ = 0;

        if (transactionListBuilder_ == null) {
          transactionList_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000001);
        } else {
          transactionListBuilder_.clear();
        }
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.proto.WalletMessage.internal_static_ProtoMessage_ResTransactionDataMessage_descriptor;
      }

      @java.lang.Override
      public com.proto.WalletMessage.ResTransactionDataMessage getDefaultInstanceForType() {
        return com.proto.WalletMessage.ResTransactionDataMessage.getDefaultInstance();
      }

      @java.lang.Override
      public com.proto.WalletMessage.ResTransactionDataMessage build() {
        com.proto.WalletMessage.ResTransactionDataMessage result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.proto.WalletMessage.ResTransactionDataMessage buildPartial() {
        com.proto.WalletMessage.ResTransactionDataMessage result = new com.proto.WalletMessage.ResTransactionDataMessage(this);
        int from_bitField0_ = bitField0_;
        result.msgID_ = msgID_;
        result.error_ = error_;
        result.transactionType_ = transactionType_;
        result.assets_ = assets_;
        result.past_ = past_;
        result.status_ = status_;
        result.page_ = page_;
        result.pageSize_ = pageSize_;
        result.total_ = total_;
        result.totalPage_ = totalPage_;
        if (transactionListBuilder_ == null) {
          if (((bitField0_ & 0x00000001) != 0)) {
            transactionList_ = java.util.Collections.unmodifiableList(transactionList_);
            bitField0_ = (bitField0_ & ~0x00000001);
          }
          result.transactionList_ = transactionList_;
        } else {
          result.transactionList_ = transactionListBuilder_.build();
        }
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.proto.WalletMessage.ResTransactionDataMessage) {
          return mergeFrom((com.proto.WalletMessage.ResTransactionDataMessage)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.proto.WalletMessage.ResTransactionDataMessage other) {
        if (other == com.proto.WalletMessage.ResTransactionDataMessage.getDefaultInstance()) return this;
        if (other.getMsgID() != 0) {
          setMsgID(other.getMsgID());
        }
        if (other.getError() != 0) {
          setError(other.getError());
        }
        if (other.getTransactionType() != 0) {
          setTransactionType(other.getTransactionType());
        }
        if (other.getAssets() != 0) {
          setAssets(other.getAssets());
        }
        if (other.getPast() != 0) {
          setPast(other.getPast());
        }
        if (other.getStatus() != 0) {
          setStatus(other.getStatus());
        }
        if (other.getPage() != 0) {
          setPage(other.getPage());
        }
        if (other.getPageSize() != 0) {
          setPageSize(other.getPageSize());
        }
        if (other.getTotal() != 0) {
          setTotal(other.getTotal());
        }
        if (other.getTotalPage() != 0) {
          setTotalPage(other.getTotalPage());
        }
        if (transactionListBuilder_ == null) {
          if (!other.transactionList_.isEmpty()) {
            if (transactionList_.isEmpty()) {
              transactionList_ = other.transactionList_;
              bitField0_ = (bitField0_ & ~0x00000001);
            } else {
              ensureTransactionListIsMutable();
              transactionList_.addAll(other.transactionList_);
            }
            onChanged();
          }
        } else {
          if (!other.transactionList_.isEmpty()) {
            if (transactionListBuilder_.isEmpty()) {
              transactionListBuilder_.dispose();
              transactionListBuilder_ = null;
              transactionList_ = other.transactionList_;
              bitField0_ = (bitField0_ & ~0x00000001);
              transactionListBuilder_ = 
                com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                   getTransactionListFieldBuilder() : null;
            } else {
              transactionListBuilder_.addAllMessages(other.transactionList_);
            }
          }
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.proto.WalletMessage.ResTransactionDataMessage parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.proto.WalletMessage.ResTransactionDataMessage) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private int msgID_ ;
      /**
       * <code>int32 msgID = 1;</code>
       * @return The msgID.
       */
      @java.lang.Override
      public int getMsgID() {
        return msgID_;
      }
      /**
       * <code>int32 msgID = 1;</code>
       * @param value The msgID to set.
       * @return This builder for chaining.
       */
      public Builder setMsgID(int value) {
        
        msgID_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>int32 msgID = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearMsgID() {
        
        msgID_ = 0;
        onChanged();
        return this;
      }

      private int error_ ;
      /**
       * <pre>
       *错误码
       * </pre>
       *
       * <code>int32 error = 2;</code>
       * @return The error.
       */
      @java.lang.Override
      public int getError() {
        return error_;
      }
      /**
       * <pre>
       *错误码
       * </pre>
       *
       * <code>int32 error = 2;</code>
       * @param value The error to set.
       * @return This builder for chaining.
       */
      public Builder setError(int value) {
        
        error_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *错误码
       * </pre>
       *
       * <code>int32 error = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearError() {
        
        error_ = 0;
        onChanged();
        return this;
      }

      private int transactionType_ ;
      /**
       * <pre>
       *0.all 1.deposit 2.withdraw 3.bill
       * </pre>
       *
       * <code>int32 transactionType = 3;</code>
       * @return The transactionType.
       */
      @java.lang.Override
      public int getTransactionType() {
        return transactionType_;
      }
      /**
       * <pre>
       *0.all 1.deposit 2.withdraw 3.bill
       * </pre>
       *
       * <code>int32 transactionType = 3;</code>
       * @param value The transactionType to set.
       * @return This builder for chaining.
       */
      public Builder setTransactionType(int value) {
        
        transactionType_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *0.all 1.deposit 2.withdraw 3.bill
       * </pre>
       *
       * <code>int32 transactionType = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearTransactionType() {
        
        transactionType_ = 0;
        onChanged();
        return this;
      }

      private int assets_ ;
      /**
       * <pre>
       *资产 0.all
       * </pre>
       *
       * <code>int32 assets = 4;</code>
       * @return The assets.
       */
      @java.lang.Override
      public int getAssets() {
        return assets_;
      }
      /**
       * <pre>
       *资产 0.all
       * </pre>
       *
       * <code>int32 assets = 4;</code>
       * @param value The assets to set.
       * @return This builder for chaining.
       */
      public Builder setAssets(int value) {
        
        assets_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *资产 0.all
       * </pre>
       *
       * <code>int32 assets = 4;</code>
       * @return This builder for chaining.
       */
      public Builder clearAssets() {
        
        assets_ = 0;
        onChanged();
        return this;
      }

      private int past_ ;
      /**
       * <pre>
       *1.24小时 2.7day 3.30day 4.60day 5.90day
       * </pre>
       *
       * <code>int32 past = 5;</code>
       * @return The past.
       */
      @java.lang.Override
      public int getPast() {
        return past_;
      }
      /**
       * <pre>
       *1.24小时 2.7day 3.30day 4.60day 5.90day
       * </pre>
       *
       * <code>int32 past = 5;</code>
       * @param value The past to set.
       * @return This builder for chaining.
       */
      public Builder setPast(int value) {
        
        past_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *1.24小时 2.7day 3.30day 4.60day 5.90day
       * </pre>
       *
       * <code>int32 past = 5;</code>
       * @return This builder for chaining.
       */
      public Builder clearPast() {
        
        past_ = 0;
        onChanged();
        return this;
      }

      private int status_ ;
      /**
       * <pre>
       *0.all 1.complete 2.processing 3.failed 4.canceled
       * </pre>
       *
       * <code>int32 status = 6;</code>
       * @return The status.
       */
      @java.lang.Override
      public int getStatus() {
        return status_;
      }
      /**
       * <pre>
       *0.all 1.complete 2.processing 3.failed 4.canceled
       * </pre>
       *
       * <code>int32 status = 6;</code>
       * @param value The status to set.
       * @return This builder for chaining.
       */
      public Builder setStatus(int value) {
        
        status_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *0.all 1.complete 2.processing 3.failed 4.canceled
       * </pre>
       *
       * <code>int32 status = 6;</code>
       * @return This builder for chaining.
       */
      public Builder clearStatus() {
        
        status_ = 0;
        onChanged();
        return this;
      }

      private int page_ ;
      /**
       * <code>int32 page = 7;</code>
       * @return The page.
       */
      @java.lang.Override
      public int getPage() {
        return page_;
      }
      /**
       * <code>int32 page = 7;</code>
       * @param value The page to set.
       * @return This builder for chaining.
       */
      public Builder setPage(int value) {
        
        page_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>int32 page = 7;</code>
       * @return This builder for chaining.
       */
      public Builder clearPage() {
        
        page_ = 0;
        onChanged();
        return this;
      }

      private int pageSize_ ;
      /**
       * <code>int32 pageSize = 8;</code>
       * @return The pageSize.
       */
      @java.lang.Override
      public int getPageSize() {
        return pageSize_;
      }
      /**
       * <code>int32 pageSize = 8;</code>
       * @param value The pageSize to set.
       * @return This builder for chaining.
       */
      public Builder setPageSize(int value) {
        
        pageSize_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>int32 pageSize = 8;</code>
       * @return This builder for chaining.
       */
      public Builder clearPageSize() {
        
        pageSize_ = 0;
        onChanged();
        return this;
      }

      private int total_ ;
      /**
       * <pre>
       *总条数
       * </pre>
       *
       * <code>int32 total = 9;</code>
       * @return The total.
       */
      @java.lang.Override
      public int getTotal() {
        return total_;
      }
      /**
       * <pre>
       *总条数
       * </pre>
       *
       * <code>int32 total = 9;</code>
       * @param value The total to set.
       * @return This builder for chaining.
       */
      public Builder setTotal(int value) {
        
        total_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *总条数
       * </pre>
       *
       * <code>int32 total = 9;</code>
       * @return This builder for chaining.
       */
      public Builder clearTotal() {
        
        total_ = 0;
        onChanged();
        return this;
      }

      private int totalPage_ ;
      /**
       * <pre>
       *总页数
       * </pre>
       *
       * <code>int32 totalPage = 10;</code>
       * @return The totalPage.
       */
      @java.lang.Override
      public int getTotalPage() {
        return totalPage_;
      }
      /**
       * <pre>
       *总页数
       * </pre>
       *
       * <code>int32 totalPage = 10;</code>
       * @param value The totalPage to set.
       * @return This builder for chaining.
       */
      public Builder setTotalPage(int value) {
        
        totalPage_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *总页数
       * </pre>
       *
       * <code>int32 totalPage = 10;</code>
       * @return This builder for chaining.
       */
      public Builder clearTotalPage() {
        
        totalPage_ = 0;
        onChanged();
        return this;
      }

      private java.util.List<com.proto.WalletMessage.TransactionInfo> transactionList_ =
        java.util.Collections.emptyList();
      private void ensureTransactionListIsMutable() {
        if (!((bitField0_ & 0x00000001) != 0)) {
          transactionList_ = new java.util.ArrayList<com.proto.WalletMessage.TransactionInfo>(transactionList_);
          bitField0_ |= 0x00000001;
         }
      }

      private com.google.protobuf.RepeatedFieldBuilderV3<
          com.proto.WalletMessage.TransactionInfo, com.proto.WalletMessage.TransactionInfo.Builder, com.proto.WalletMessage.TransactionInfoOrBuilder> transactionListBuilder_;

      /**
       * <pre>
       *交易列表
       * </pre>
       *
       * <code>repeated .ProtoMessage.TransactionInfo transactionList = 11;</code>
       */
      public java.util.List<com.proto.WalletMessage.TransactionInfo> getTransactionListList() {
        if (transactionListBuilder_ == null) {
          return java.util.Collections.unmodifiableList(transactionList_);
        } else {
          return transactionListBuilder_.getMessageList();
        }
      }
      /**
       * <pre>
       *交易列表
       * </pre>
       *
       * <code>repeated .ProtoMessage.TransactionInfo transactionList = 11;</code>
       */
      public int getTransactionListCount() {
        if (transactionListBuilder_ == null) {
          return transactionList_.size();
        } else {
          return transactionListBuilder_.getCount();
        }
      }
      /**
       * <pre>
       *交易列表
       * </pre>
       *
       * <code>repeated .ProtoMessage.TransactionInfo transactionList = 11;</code>
       */
      public com.proto.WalletMessage.TransactionInfo getTransactionList(int index) {
        if (transactionListBuilder_ == null) {
          return transactionList_.get(index);
        } else {
          return transactionListBuilder_.getMessage(index);
        }
      }
      /**
       * <pre>
       *交易列表
       * </pre>
       *
       * <code>repeated .ProtoMessage.TransactionInfo transactionList = 11;</code>
       */
      public Builder setTransactionList(
          int index, com.proto.WalletMessage.TransactionInfo value) {
        if (transactionListBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureTransactionListIsMutable();
          transactionList_.set(index, value);
          onChanged();
        } else {
          transactionListBuilder_.setMessage(index, value);
        }
        return this;
      }
      /**
       * <pre>
       *交易列表
       * </pre>
       *
       * <code>repeated .ProtoMessage.TransactionInfo transactionList = 11;</code>
       */
      public Builder setTransactionList(
          int index, com.proto.WalletMessage.TransactionInfo.Builder builderForValue) {
        if (transactionListBuilder_ == null) {
          ensureTransactionListIsMutable();
          transactionList_.set(index, builderForValue.build());
          onChanged();
        } else {
          transactionListBuilder_.setMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <pre>
       *交易列表
       * </pre>
       *
       * <code>repeated .ProtoMessage.TransactionInfo transactionList = 11;</code>
       */
      public Builder addTransactionList(com.proto.WalletMessage.TransactionInfo value) {
        if (transactionListBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureTransactionListIsMutable();
          transactionList_.add(value);
          onChanged();
        } else {
          transactionListBuilder_.addMessage(value);
        }
        return this;
      }
      /**
       * <pre>
       *交易列表
       * </pre>
       *
       * <code>repeated .ProtoMessage.TransactionInfo transactionList = 11;</code>
       */
      public Builder addTransactionList(
          int index, com.proto.WalletMessage.TransactionInfo value) {
        if (transactionListBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureTransactionListIsMutable();
          transactionList_.add(index, value);
          onChanged();
        } else {
          transactionListBuilder_.addMessage(index, value);
        }
        return this;
      }
      /**
       * <pre>
       *交易列表
       * </pre>
       *
       * <code>repeated .ProtoMessage.TransactionInfo transactionList = 11;</code>
       */
      public Builder addTransactionList(
          com.proto.WalletMessage.TransactionInfo.Builder builderForValue) {
        if (transactionListBuilder_ == null) {
          ensureTransactionListIsMutable();
          transactionList_.add(builderForValue.build());
          onChanged();
        } else {
          transactionListBuilder_.addMessage(builderForValue.build());
        }
        return this;
      }
      /**
       * <pre>
       *交易列表
       * </pre>
       *
       * <code>repeated .ProtoMessage.TransactionInfo transactionList = 11;</code>
       */
      public Builder addTransactionList(
          int index, com.proto.WalletMessage.TransactionInfo.Builder builderForValue) {
        if (transactionListBuilder_ == null) {
          ensureTransactionListIsMutable();
          transactionList_.add(index, builderForValue.build());
          onChanged();
        } else {
          transactionListBuilder_.addMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <pre>
       *交易列表
       * </pre>
       *
       * <code>repeated .ProtoMessage.TransactionInfo transactionList = 11;</code>
       */
      public Builder addAllTransactionList(
          java.lang.Iterable<? extends com.proto.WalletMessage.TransactionInfo> values) {
        if (transactionListBuilder_ == null) {
          ensureTransactionListIsMutable();
          com.google.protobuf.AbstractMessageLite.Builder.addAll(
              values, transactionList_);
          onChanged();
        } else {
          transactionListBuilder_.addAllMessages(values);
        }
        return this;
      }
      /**
       * <pre>
       *交易列表
       * </pre>
       *
       * <code>repeated .ProtoMessage.TransactionInfo transactionList = 11;</code>
       */
      public Builder clearTransactionList() {
        if (transactionListBuilder_ == null) {
          transactionList_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000001);
          onChanged();
        } else {
          transactionListBuilder_.clear();
        }
        return this;
      }
      /**
       * <pre>
       *交易列表
       * </pre>
       *
       * <code>repeated .ProtoMessage.TransactionInfo transactionList = 11;</code>
       */
      public Builder removeTransactionList(int index) {
        if (transactionListBuilder_ == null) {
          ensureTransactionListIsMutable();
          transactionList_.remove(index);
          onChanged();
        } else {
          transactionListBuilder_.remove(index);
        }
        return this;
      }
      /**
       * <pre>
       *交易列表
       * </pre>
       *
       * <code>repeated .ProtoMessage.TransactionInfo transactionList = 11;</code>
       */
      public com.proto.WalletMessage.TransactionInfo.Builder getTransactionListBuilder(
          int index) {
        return getTransactionListFieldBuilder().getBuilder(index);
      }
      /**
       * <pre>
       *交易列表
       * </pre>
       *
       * <code>repeated .ProtoMessage.TransactionInfo transactionList = 11;</code>
       */
      public com.proto.WalletMessage.TransactionInfoOrBuilder getTransactionListOrBuilder(
          int index) {
        if (transactionListBuilder_ == null) {
          return transactionList_.get(index);  } else {
          return transactionListBuilder_.getMessageOrBuilder(index);
        }
      }
      /**
       * <pre>
       *交易列表
       * </pre>
       *
       * <code>repeated .ProtoMessage.TransactionInfo transactionList = 11;</code>
       */
      public java.util.List<? extends com.proto.WalletMessage.TransactionInfoOrBuilder> 
           getTransactionListOrBuilderList() {
        if (transactionListBuilder_ != null) {
          return transactionListBuilder_.getMessageOrBuilderList();
        } else {
          return java.util.Collections.unmodifiableList(transactionList_);
        }
      }
      /**
       * <pre>
       *交易列表
       * </pre>
       *
       * <code>repeated .ProtoMessage.TransactionInfo transactionList = 11;</code>
       */
      public com.proto.WalletMessage.TransactionInfo.Builder addTransactionListBuilder() {
        return getTransactionListFieldBuilder().addBuilder(
            com.proto.WalletMessage.TransactionInfo.getDefaultInstance());
      }
      /**
       * <pre>
       *交易列表
       * </pre>
       *
       * <code>repeated .ProtoMessage.TransactionInfo transactionList = 11;</code>
       */
      public com.proto.WalletMessage.TransactionInfo.Builder addTransactionListBuilder(
          int index) {
        return getTransactionListFieldBuilder().addBuilder(
            index, com.proto.WalletMessage.TransactionInfo.getDefaultInstance());
      }
      /**
       * <pre>
       *交易列表
       * </pre>
       *
       * <code>repeated .ProtoMessage.TransactionInfo transactionList = 11;</code>
       */
      public java.util.List<com.proto.WalletMessage.TransactionInfo.Builder> 
           getTransactionListBuilderList() {
        return getTransactionListFieldBuilder().getBuilderList();
      }
      private com.google.protobuf.RepeatedFieldBuilderV3<
          com.proto.WalletMessage.TransactionInfo, com.proto.WalletMessage.TransactionInfo.Builder, com.proto.WalletMessage.TransactionInfoOrBuilder> 
          getTransactionListFieldBuilder() {
        if (transactionListBuilder_ == null) {
          transactionListBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
              com.proto.WalletMessage.TransactionInfo, com.proto.WalletMessage.TransactionInfo.Builder, com.proto.WalletMessage.TransactionInfoOrBuilder>(
                  transactionList_,
                  ((bitField0_ & 0x00000001) != 0),
                  getParentForChildren(),
                  isClean());
          transactionList_ = null;
        }
        return transactionListBuilder_;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:ProtoMessage.ResTransactionDataMessage)
    }

    // @@protoc_insertion_point(class_scope:ProtoMessage.ResTransactionDataMessage)
    private static final com.proto.WalletMessage.ResTransactionDataMessage DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.proto.WalletMessage.ResTransactionDataMessage();
    }

    public static com.proto.WalletMessage.ResTransactionDataMessage getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<ResTransactionDataMessage>
        PARSER = new com.google.protobuf.AbstractParser<ResTransactionDataMessage>() {
      @java.lang.Override
      public ResTransactionDataMessage parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new ResTransactionDataMessage(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<ResTransactionDataMessage> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<ResTransactionDataMessage> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.proto.WalletMessage.ResTransactionDataMessage getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface TransactionInfoOrBuilder extends
      // @@protoc_insertion_point(interface_extends:ProtoMessage.TransactionInfo)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     *下注id
     * </pre>
     *
     * <code>string betId = 1;</code>
     * @return The betId.
     */
    java.lang.String getBetId();
    /**
     * <pre>
     *下注id
     * </pre>
     *
     * <code>string betId = 1;</code>
     * @return The bytes for betId.
     */
    com.google.protobuf.ByteString
        getBetIdBytes();

    /**
     * <pre>
     *游戏名字
     * </pre>
     *
     * <code>string gameName = 2;</code>
     * @return The gameName.
     */
    java.lang.String getGameName();
    /**
     * <pre>
     *游戏名字
     * </pre>
     *
     * <code>string gameName = 2;</code>
     * @return The bytes for gameName.
     */
    com.google.protobuf.ByteString
        getGameNameBytes();

    /**
     * <pre>
     *货币id
     * </pre>
     *
     * <code>int32 currencyId = 3;</code>
     * @return The currencyId.
     */
    int getCurrencyId();

    /**
     * <pre>
     *金额
     * </pre>
     *
     * <code>double amount = 4;</code>
     * @return The amount.
     */
    double getAmount();

    /**
     * <pre>
     *时间
     * </pre>
     *
     * <code>int64 time = 5;</code>
     * @return The time.
     */
    long getTime();

    /**
     * <pre>
     *赔率
     * </pre>
     *
     * <code>double payout = 6;</code>
     * @return The payout.
     */
    double getPayout();

    /**
     * <pre>
     *利润
     * </pre>
     *
     * <code>double profit = 7;</code>
     * @return The profit.
     */
    double getProfit();

    /**
     * <pre>
     *余额
     * </pre>
     *
     * <code>double balance = 8;</code>
     * @return The balance.
     */
    double getBalance();

    /**
     * <pre>
     *状态 1.complete 2.processing 3.failed 4.canceled
     * </pre>
     *
     * <code>int32 status = 9;</code>
     * @return The status.
     */
    int getStatus();

    /**
     * <pre>
     *类型 1.deposit 2.withdraw 3.bill 4.bonus
     * </pre>
     *
     * <code>int32 type = 10;</code>
     * @return The type.
     */
    int getType();

    /**
     * <pre>
     *钱包地址
     * </pre>
     *
     * <code>string walletAddress = 11;</code>
     * @return The walletAddress.
     */
    java.lang.String getWalletAddress();
    /**
     * <pre>
     *钱包地址
     * </pre>
     *
     * <code>string walletAddress = 11;</code>
     * @return The bytes for walletAddress.
     */
    com.google.protobuf.ByteString
        getWalletAddressBytes();

    /**
     * <pre>
     *订单id
     * </pre>
     *
     * <code>string orderId = 12;</code>
     * @return The orderId.
     */
    java.lang.String getOrderId();
    /**
     * <pre>
     *订单id
     * </pre>
     *
     * <code>string orderId = 12;</code>
     * @return The bytes for orderId.
     */
    com.google.protobuf.ByteString
        getOrderIdBytes();

    /**
     * <pre>
     *手续费
     * </pre>
     *
     * <code>double fee = 13;</code>
     * @return The fee.
     */
    double getFee();

    /**
     * <pre>
     *渠道
     * </pre>
     *
     * <code>string channel = 14;</code>
     * @return The channel.
     */
    java.lang.String getChannel();
    /**
     * <pre>
     *渠道
     * </pre>
     *
     * <code>string channel = 14;</code>
     * @return The bytes for channel.
     */
    com.google.protobuf.ByteString
        getChannelBytes();

    /**
     * <pre>
     *备注
     * </pre>
     *
     * <code>string tips = 15;</code>
     * @return The tips.
     */
    java.lang.String getTips();
    /**
     * <pre>
     *备注
     * </pre>
     *
     * <code>string tips = 15;</code>
     * @return The bytes for tips.
     */
    com.google.protobuf.ByteString
        getTipsBytes();
  }
  /**
   * Protobuf type {@code ProtoMessage.TransactionInfo}
   */
  public static final class TransactionInfo extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:ProtoMessage.TransactionInfo)
      TransactionInfoOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use TransactionInfo.newBuilder() to construct.
    private TransactionInfo(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private TransactionInfo() {
      betId_ = "";
      gameName_ = "";
      walletAddress_ = "";
      orderId_ = "";
      channel_ = "";
      tips_ = "";
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new TransactionInfo();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private TransactionInfo(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              java.lang.String s = input.readStringRequireUtf8();

              betId_ = s;
              break;
            }
            case 18: {
              java.lang.String s = input.readStringRequireUtf8();

              gameName_ = s;
              break;
            }
            case 24: {

              currencyId_ = input.readInt32();
              break;
            }
            case 33: {

              amount_ = input.readDouble();
              break;
            }
            case 40: {

              time_ = input.readInt64();
              break;
            }
            case 49: {

              payout_ = input.readDouble();
              break;
            }
            case 57: {

              profit_ = input.readDouble();
              break;
            }
            case 65: {

              balance_ = input.readDouble();
              break;
            }
            case 72: {

              status_ = input.readInt32();
              break;
            }
            case 80: {

              type_ = input.readInt32();
              break;
            }
            case 90: {
              java.lang.String s = input.readStringRequireUtf8();

              walletAddress_ = s;
              break;
            }
            case 98: {
              java.lang.String s = input.readStringRequireUtf8();

              orderId_ = s;
              break;
            }
            case 105: {

              fee_ = input.readDouble();
              break;
            }
            case 114: {
              java.lang.String s = input.readStringRequireUtf8();

              channel_ = s;
              break;
            }
            case 122: {
              java.lang.String s = input.readStringRequireUtf8();

              tips_ = s;
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.proto.WalletMessage.internal_static_ProtoMessage_TransactionInfo_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.proto.WalletMessage.internal_static_ProtoMessage_TransactionInfo_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.proto.WalletMessage.TransactionInfo.class, com.proto.WalletMessage.TransactionInfo.Builder.class);
    }

    public static final int BETID_FIELD_NUMBER = 1;
    private volatile java.lang.Object betId_;
    /**
     * <pre>
     *下注id
     * </pre>
     *
     * <code>string betId = 1;</code>
     * @return The betId.
     */
    @java.lang.Override
    public java.lang.String getBetId() {
      java.lang.Object ref = betId_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        betId_ = s;
        return s;
      }
    }
    /**
     * <pre>
     *下注id
     * </pre>
     *
     * <code>string betId = 1;</code>
     * @return The bytes for betId.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getBetIdBytes() {
      java.lang.Object ref = betId_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        betId_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int GAMENAME_FIELD_NUMBER = 2;
    private volatile java.lang.Object gameName_;
    /**
     * <pre>
     *游戏名字
     * </pre>
     *
     * <code>string gameName = 2;</code>
     * @return The gameName.
     */
    @java.lang.Override
    public java.lang.String getGameName() {
      java.lang.Object ref = gameName_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        gameName_ = s;
        return s;
      }
    }
    /**
     * <pre>
     *游戏名字
     * </pre>
     *
     * <code>string gameName = 2;</code>
     * @return The bytes for gameName.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getGameNameBytes() {
      java.lang.Object ref = gameName_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        gameName_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int CURRENCYID_FIELD_NUMBER = 3;
    private int currencyId_;
    /**
     * <pre>
     *货币id
     * </pre>
     *
     * <code>int32 currencyId = 3;</code>
     * @return The currencyId.
     */
    @java.lang.Override
    public int getCurrencyId() {
      return currencyId_;
    }

    public static final int AMOUNT_FIELD_NUMBER = 4;
    private double amount_;
    /**
     * <pre>
     *金额
     * </pre>
     *
     * <code>double amount = 4;</code>
     * @return The amount.
     */
    @java.lang.Override
    public double getAmount() {
      return amount_;
    }

    public static final int TIME_FIELD_NUMBER = 5;
    private long time_;
    /**
     * <pre>
     *时间
     * </pre>
     *
     * <code>int64 time = 5;</code>
     * @return The time.
     */
    @java.lang.Override
    public long getTime() {
      return time_;
    }

    public static final int PAYOUT_FIELD_NUMBER = 6;
    private double payout_;
    /**
     * <pre>
     *赔率
     * </pre>
     *
     * <code>double payout = 6;</code>
     * @return The payout.
     */
    @java.lang.Override
    public double getPayout() {
      return payout_;
    }

    public static final int PROFIT_FIELD_NUMBER = 7;
    private double profit_;
    /**
     * <pre>
     *利润
     * </pre>
     *
     * <code>double profit = 7;</code>
     * @return The profit.
     */
    @java.lang.Override
    public double getProfit() {
      return profit_;
    }

    public static final int BALANCE_FIELD_NUMBER = 8;
    private double balance_;
    /**
     * <pre>
     *余额
     * </pre>
     *
     * <code>double balance = 8;</code>
     * @return The balance.
     */
    @java.lang.Override
    public double getBalance() {
      return balance_;
    }

    public static final int STATUS_FIELD_NUMBER = 9;
    private int status_;
    /**
     * <pre>
     *状态 1.complete 2.processing 3.failed 4.canceled
     * </pre>
     *
     * <code>int32 status = 9;</code>
     * @return The status.
     */
    @java.lang.Override
    public int getStatus() {
      return status_;
    }

    public static final int TYPE_FIELD_NUMBER = 10;
    private int type_;
    /**
     * <pre>
     *类型 1.deposit 2.withdraw 3.bill 4.bonus
     * </pre>
     *
     * <code>int32 type = 10;</code>
     * @return The type.
     */
    @java.lang.Override
    public int getType() {
      return type_;
    }

    public static final int WALLETADDRESS_FIELD_NUMBER = 11;
    private volatile java.lang.Object walletAddress_;
    /**
     * <pre>
     *钱包地址
     * </pre>
     *
     * <code>string walletAddress = 11;</code>
     * @return The walletAddress.
     */
    @java.lang.Override
    public java.lang.String getWalletAddress() {
      java.lang.Object ref = walletAddress_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        walletAddress_ = s;
        return s;
      }
    }
    /**
     * <pre>
     *钱包地址
     * </pre>
     *
     * <code>string walletAddress = 11;</code>
     * @return The bytes for walletAddress.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getWalletAddressBytes() {
      java.lang.Object ref = walletAddress_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        walletAddress_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int ORDERID_FIELD_NUMBER = 12;
    private volatile java.lang.Object orderId_;
    /**
     * <pre>
     *订单id
     * </pre>
     *
     * <code>string orderId = 12;</code>
     * @return The orderId.
     */
    @java.lang.Override
    public java.lang.String getOrderId() {
      java.lang.Object ref = orderId_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        orderId_ = s;
        return s;
      }
    }
    /**
     * <pre>
     *订单id
     * </pre>
     *
     * <code>string orderId = 12;</code>
     * @return The bytes for orderId.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getOrderIdBytes() {
      java.lang.Object ref = orderId_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        orderId_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int FEE_FIELD_NUMBER = 13;
    private double fee_;
    /**
     * <pre>
     *手续费
     * </pre>
     *
     * <code>double fee = 13;</code>
     * @return The fee.
     */
    @java.lang.Override
    public double getFee() {
      return fee_;
    }

    public static final int CHANNEL_FIELD_NUMBER = 14;
    private volatile java.lang.Object channel_;
    /**
     * <pre>
     *渠道
     * </pre>
     *
     * <code>string channel = 14;</code>
     * @return The channel.
     */
    @java.lang.Override
    public java.lang.String getChannel() {
      java.lang.Object ref = channel_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        channel_ = s;
        return s;
      }
    }
    /**
     * <pre>
     *渠道
     * </pre>
     *
     * <code>string channel = 14;</code>
     * @return The bytes for channel.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getChannelBytes() {
      java.lang.Object ref = channel_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        channel_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int TIPS_FIELD_NUMBER = 15;
    private volatile java.lang.Object tips_;
    /**
     * <pre>
     *备注
     * </pre>
     *
     * <code>string tips = 15;</code>
     * @return The tips.
     */
    @java.lang.Override
    public java.lang.String getTips() {
      java.lang.Object ref = tips_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        tips_ = s;
        return s;
      }
    }
    /**
     * <pre>
     *备注
     * </pre>
     *
     * <code>string tips = 15;</code>
     * @return The bytes for tips.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getTipsBytes() {
      java.lang.Object ref = tips_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        tips_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (!getBetIdBytes().isEmpty()) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 1, betId_);
      }
      if (!getGameNameBytes().isEmpty()) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 2, gameName_);
      }
      if (currencyId_ != 0) {
        output.writeInt32(3, currencyId_);
      }
      if (amount_ != 0D) {
        output.writeDouble(4, amount_);
      }
      if (time_ != 0L) {
        output.writeInt64(5, time_);
      }
      if (payout_ != 0D) {
        output.writeDouble(6, payout_);
      }
      if (profit_ != 0D) {
        output.writeDouble(7, profit_);
      }
      if (balance_ != 0D) {
        output.writeDouble(8, balance_);
      }
      if (status_ != 0) {
        output.writeInt32(9, status_);
      }
      if (type_ != 0) {
        output.writeInt32(10, type_);
      }
      if (!getWalletAddressBytes().isEmpty()) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 11, walletAddress_);
      }
      if (!getOrderIdBytes().isEmpty()) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 12, orderId_);
      }
      if (fee_ != 0D) {
        output.writeDouble(13, fee_);
      }
      if (!getChannelBytes().isEmpty()) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 14, channel_);
      }
      if (!getTipsBytes().isEmpty()) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 15, tips_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (!getBetIdBytes().isEmpty()) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(1, betId_);
      }
      if (!getGameNameBytes().isEmpty()) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(2, gameName_);
      }
      if (currencyId_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(3, currencyId_);
      }
      if (amount_ != 0D) {
        size += com.google.protobuf.CodedOutputStream
          .computeDoubleSize(4, amount_);
      }
      if (time_ != 0L) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(5, time_);
      }
      if (payout_ != 0D) {
        size += com.google.protobuf.CodedOutputStream
          .computeDoubleSize(6, payout_);
      }
      if (profit_ != 0D) {
        size += com.google.protobuf.CodedOutputStream
          .computeDoubleSize(7, profit_);
      }
      if (balance_ != 0D) {
        size += com.google.protobuf.CodedOutputStream
          .computeDoubleSize(8, balance_);
      }
      if (status_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(9, status_);
      }
      if (type_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(10, type_);
      }
      if (!getWalletAddressBytes().isEmpty()) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(11, walletAddress_);
      }
      if (!getOrderIdBytes().isEmpty()) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(12, orderId_);
      }
      if (fee_ != 0D) {
        size += com.google.protobuf.CodedOutputStream
          .computeDoubleSize(13, fee_);
      }
      if (!getChannelBytes().isEmpty()) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(14, channel_);
      }
      if (!getTipsBytes().isEmpty()) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(15, tips_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.proto.WalletMessage.TransactionInfo)) {
        return super.equals(obj);
      }
      com.proto.WalletMessage.TransactionInfo other = (com.proto.WalletMessage.TransactionInfo) obj;

      if (!getBetId()
          .equals(other.getBetId())) return false;
      if (!getGameName()
          .equals(other.getGameName())) return false;
      if (getCurrencyId()
          != other.getCurrencyId()) return false;
      if (java.lang.Double.doubleToLongBits(getAmount())
          != java.lang.Double.doubleToLongBits(
              other.getAmount())) return false;
      if (getTime()
          != other.getTime()) return false;
      if (java.lang.Double.doubleToLongBits(getPayout())
          != java.lang.Double.doubleToLongBits(
              other.getPayout())) return false;
      if (java.lang.Double.doubleToLongBits(getProfit())
          != java.lang.Double.doubleToLongBits(
              other.getProfit())) return false;
      if (java.lang.Double.doubleToLongBits(getBalance())
          != java.lang.Double.doubleToLongBits(
              other.getBalance())) return false;
      if (getStatus()
          != other.getStatus()) return false;
      if (getType()
          != other.getType()) return false;
      if (!getWalletAddress()
          .equals(other.getWalletAddress())) return false;
      if (!getOrderId()
          .equals(other.getOrderId())) return false;
      if (java.lang.Double.doubleToLongBits(getFee())
          != java.lang.Double.doubleToLongBits(
              other.getFee())) return false;
      if (!getChannel()
          .equals(other.getChannel())) return false;
      if (!getTips()
          .equals(other.getTips())) return false;
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + BETID_FIELD_NUMBER;
      hash = (53 * hash) + getBetId().hashCode();
      hash = (37 * hash) + GAMENAME_FIELD_NUMBER;
      hash = (53 * hash) + getGameName().hashCode();
      hash = (37 * hash) + CURRENCYID_FIELD_NUMBER;
      hash = (53 * hash) + getCurrencyId();
      hash = (37 * hash) + AMOUNT_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          java.lang.Double.doubleToLongBits(getAmount()));
      hash = (37 * hash) + TIME_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getTime());
      hash = (37 * hash) + PAYOUT_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          java.lang.Double.doubleToLongBits(getPayout()));
      hash = (37 * hash) + PROFIT_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          java.lang.Double.doubleToLongBits(getProfit()));
      hash = (37 * hash) + BALANCE_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          java.lang.Double.doubleToLongBits(getBalance()));
      hash = (37 * hash) + STATUS_FIELD_NUMBER;
      hash = (53 * hash) + getStatus();
      hash = (37 * hash) + TYPE_FIELD_NUMBER;
      hash = (53 * hash) + getType();
      hash = (37 * hash) + WALLETADDRESS_FIELD_NUMBER;
      hash = (53 * hash) + getWalletAddress().hashCode();
      hash = (37 * hash) + ORDERID_FIELD_NUMBER;
      hash = (53 * hash) + getOrderId().hashCode();
      hash = (37 * hash) + FEE_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          java.lang.Double.doubleToLongBits(getFee()));
      hash = (37 * hash) + CHANNEL_FIELD_NUMBER;
      hash = (53 * hash) + getChannel().hashCode();
      hash = (37 * hash) + TIPS_FIELD_NUMBER;
      hash = (53 * hash) + getTips().hashCode();
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.proto.WalletMessage.TransactionInfo parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.proto.WalletMessage.TransactionInfo parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.proto.WalletMessage.TransactionInfo parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.proto.WalletMessage.TransactionInfo parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.proto.WalletMessage.TransactionInfo parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.proto.WalletMessage.TransactionInfo parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.proto.WalletMessage.TransactionInfo parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.proto.WalletMessage.TransactionInfo parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.proto.WalletMessage.TransactionInfo parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.proto.WalletMessage.TransactionInfo parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.proto.WalletMessage.TransactionInfo parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.proto.WalletMessage.TransactionInfo parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.proto.WalletMessage.TransactionInfo prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code ProtoMessage.TransactionInfo}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:ProtoMessage.TransactionInfo)
        com.proto.WalletMessage.TransactionInfoOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.proto.WalletMessage.internal_static_ProtoMessage_TransactionInfo_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.proto.WalletMessage.internal_static_ProtoMessage_TransactionInfo_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.proto.WalletMessage.TransactionInfo.class, com.proto.WalletMessage.TransactionInfo.Builder.class);
      }

      // Construct using com.proto.WalletMessage.TransactionInfo.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        betId_ = "";

        gameName_ = "";

        currencyId_ = 0;

        amount_ = 0D;

        time_ = 0L;

        payout_ = 0D;

        profit_ = 0D;

        balance_ = 0D;

        status_ = 0;

        type_ = 0;

        walletAddress_ = "";

        orderId_ = "";

        fee_ = 0D;

        channel_ = "";

        tips_ = "";

        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.proto.WalletMessage.internal_static_ProtoMessage_TransactionInfo_descriptor;
      }

      @java.lang.Override
      public com.proto.WalletMessage.TransactionInfo getDefaultInstanceForType() {
        return com.proto.WalletMessage.TransactionInfo.getDefaultInstance();
      }

      @java.lang.Override
      public com.proto.WalletMessage.TransactionInfo build() {
        com.proto.WalletMessage.TransactionInfo result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.proto.WalletMessage.TransactionInfo buildPartial() {
        com.proto.WalletMessage.TransactionInfo result = new com.proto.WalletMessage.TransactionInfo(this);
        result.betId_ = betId_;
        result.gameName_ = gameName_;
        result.currencyId_ = currencyId_;
        result.amount_ = amount_;
        result.time_ = time_;
        result.payout_ = payout_;
        result.profit_ = profit_;
        result.balance_ = balance_;
        result.status_ = status_;
        result.type_ = type_;
        result.walletAddress_ = walletAddress_;
        result.orderId_ = orderId_;
        result.fee_ = fee_;
        result.channel_ = channel_;
        result.tips_ = tips_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.proto.WalletMessage.TransactionInfo) {
          return mergeFrom((com.proto.WalletMessage.TransactionInfo)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.proto.WalletMessage.TransactionInfo other) {
        if (other == com.proto.WalletMessage.TransactionInfo.getDefaultInstance()) return this;
        if (!other.getBetId().isEmpty()) {
          betId_ = other.betId_;
          onChanged();
        }
        if (!other.getGameName().isEmpty()) {
          gameName_ = other.gameName_;
          onChanged();
        }
        if (other.getCurrencyId() != 0) {
          setCurrencyId(other.getCurrencyId());
        }
        if (other.getAmount() != 0D) {
          setAmount(other.getAmount());
        }
        if (other.getTime() != 0L) {
          setTime(other.getTime());
        }
        if (other.getPayout() != 0D) {
          setPayout(other.getPayout());
        }
        if (other.getProfit() != 0D) {
          setProfit(other.getProfit());
        }
        if (other.getBalance() != 0D) {
          setBalance(other.getBalance());
        }
        if (other.getStatus() != 0) {
          setStatus(other.getStatus());
        }
        if (other.getType() != 0) {
          setType(other.getType());
        }
        if (!other.getWalletAddress().isEmpty()) {
          walletAddress_ = other.walletAddress_;
          onChanged();
        }
        if (!other.getOrderId().isEmpty()) {
          orderId_ = other.orderId_;
          onChanged();
        }
        if (other.getFee() != 0D) {
          setFee(other.getFee());
        }
        if (!other.getChannel().isEmpty()) {
          channel_ = other.channel_;
          onChanged();
        }
        if (!other.getTips().isEmpty()) {
          tips_ = other.tips_;
          onChanged();
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.proto.WalletMessage.TransactionInfo parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.proto.WalletMessage.TransactionInfo) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }

      private java.lang.Object betId_ = "";
      /**
       * <pre>
       *下注id
       * </pre>
       *
       * <code>string betId = 1;</code>
       * @return The betId.
       */
      public java.lang.String getBetId() {
        java.lang.Object ref = betId_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          betId_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       *下注id
       * </pre>
       *
       * <code>string betId = 1;</code>
       * @return The bytes for betId.
       */
      public com.google.protobuf.ByteString
          getBetIdBytes() {
        java.lang.Object ref = betId_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          betId_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       *下注id
       * </pre>
       *
       * <code>string betId = 1;</code>
       * @param value The betId to set.
       * @return This builder for chaining.
       */
      public Builder setBetId(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  
        betId_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *下注id
       * </pre>
       *
       * <code>string betId = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearBetId() {
        
        betId_ = getDefaultInstance().getBetId();
        onChanged();
        return this;
      }
      /**
       * <pre>
       *下注id
       * </pre>
       *
       * <code>string betId = 1;</code>
       * @param value The bytes for betId to set.
       * @return This builder for chaining.
       */
      public Builder setBetIdBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
        
        betId_ = value;
        onChanged();
        return this;
      }

      private java.lang.Object gameName_ = "";
      /**
       * <pre>
       *游戏名字
       * </pre>
       *
       * <code>string gameName = 2;</code>
       * @return The gameName.
       */
      public java.lang.String getGameName() {
        java.lang.Object ref = gameName_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          gameName_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       *游戏名字
       * </pre>
       *
       * <code>string gameName = 2;</code>
       * @return The bytes for gameName.
       */
      public com.google.protobuf.ByteString
          getGameNameBytes() {
        java.lang.Object ref = gameName_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          gameName_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       *游戏名字
       * </pre>
       *
       * <code>string gameName = 2;</code>
       * @param value The gameName to set.
       * @return This builder for chaining.
       */
      public Builder setGameName(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  
        gameName_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *游戏名字
       * </pre>
       *
       * <code>string gameName = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearGameName() {
        
        gameName_ = getDefaultInstance().getGameName();
        onChanged();
        return this;
      }
      /**
       * <pre>
       *游戏名字
       * </pre>
       *
       * <code>string gameName = 2;</code>
       * @param value The bytes for gameName to set.
       * @return This builder for chaining.
       */
      public Builder setGameNameBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
        
        gameName_ = value;
        onChanged();
        return this;
      }

      private int currencyId_ ;
      /**
       * <pre>
       *货币id
       * </pre>
       *
       * <code>int32 currencyId = 3;</code>
       * @return The currencyId.
       */
      @java.lang.Override
      public int getCurrencyId() {
        return currencyId_;
      }
      /**
       * <pre>
       *货币id
       * </pre>
       *
       * <code>int32 currencyId = 3;</code>
       * @param value The currencyId to set.
       * @return This builder for chaining.
       */
      public Builder setCurrencyId(int value) {
        
        currencyId_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *货币id
       * </pre>
       *
       * <code>int32 currencyId = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearCurrencyId() {
        
        currencyId_ = 0;
        onChanged();
        return this;
      }

      private double amount_ ;
      /**
       * <pre>
       *金额
       * </pre>
       *
       * <code>double amount = 4;</code>
       * @return The amount.
       */
      @java.lang.Override
      public double getAmount() {
        return amount_;
      }
      /**
       * <pre>
       *金额
       * </pre>
       *
       * <code>double amount = 4;</code>
       * @param value The amount to set.
       * @return This builder for chaining.
       */
      public Builder setAmount(double value) {
        
        amount_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *金额
       * </pre>
       *
       * <code>double amount = 4;</code>
       * @return This builder for chaining.
       */
      public Builder clearAmount() {
        
        amount_ = 0D;
        onChanged();
        return this;
      }

      private long time_ ;
      /**
       * <pre>
       *时间
       * </pre>
       *
       * <code>int64 time = 5;</code>
       * @return The time.
       */
      @java.lang.Override
      public long getTime() {
        return time_;
      }
      /**
       * <pre>
       *时间
       * </pre>
       *
       * <code>int64 time = 5;</code>
       * @param value The time to set.
       * @return This builder for chaining.
       */
      public Builder setTime(long value) {
        
        time_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *时间
       * </pre>
       *
       * <code>int64 time = 5;</code>
       * @return This builder for chaining.
       */
      public Builder clearTime() {
        
        time_ = 0L;
        onChanged();
        return this;
      }

      private double payout_ ;
      /**
       * <pre>
       *赔率
       * </pre>
       *
       * <code>double payout = 6;</code>
       * @return The payout.
       */
      @java.lang.Override
      public double getPayout() {
        return payout_;
      }
      /**
       * <pre>
       *赔率
       * </pre>
       *
       * <code>double payout = 6;</code>
       * @param value The payout to set.
       * @return This builder for chaining.
       */
      public Builder setPayout(double value) {
        
        payout_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *赔率
       * </pre>
       *
       * <code>double payout = 6;</code>
       * @return This builder for chaining.
       */
      public Builder clearPayout() {
        
        payout_ = 0D;
        onChanged();
        return this;
      }

      private double profit_ ;
      /**
       * <pre>
       *利润
       * </pre>
       *
       * <code>double profit = 7;</code>
       * @return The profit.
       */
      @java.lang.Override
      public double getProfit() {
        return profit_;
      }
      /**
       * <pre>
       *利润
       * </pre>
       *
       * <code>double profit = 7;</code>
       * @param value The profit to set.
       * @return This builder for chaining.
       */
      public Builder setProfit(double value) {
        
        profit_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *利润
       * </pre>
       *
       * <code>double profit = 7;</code>
       * @return This builder for chaining.
       */
      public Builder clearProfit() {
        
        profit_ = 0D;
        onChanged();
        return this;
      }

      private double balance_ ;
      /**
       * <pre>
       *余额
       * </pre>
       *
       * <code>double balance = 8;</code>
       * @return The balance.
       */
      @java.lang.Override
      public double getBalance() {
        return balance_;
      }
      /**
       * <pre>
       *余额
       * </pre>
       *
       * <code>double balance = 8;</code>
       * @param value The balance to set.
       * @return This builder for chaining.
       */
      public Builder setBalance(double value) {
        
        balance_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *余额
       * </pre>
       *
       * <code>double balance = 8;</code>
       * @return This builder for chaining.
       */
      public Builder clearBalance() {
        
        balance_ = 0D;
        onChanged();
        return this;
      }

      private int status_ ;
      /**
       * <pre>
       *状态 1.complete 2.processing 3.failed 4.canceled
       * </pre>
       *
       * <code>int32 status = 9;</code>
       * @return The status.
       */
      @java.lang.Override
      public int getStatus() {
        return status_;
      }
      /**
       * <pre>
       *状态 1.complete 2.processing 3.failed 4.canceled
       * </pre>
       *
       * <code>int32 status = 9;</code>
       * @param value The status to set.
       * @return This builder for chaining.
       */
      public Builder setStatus(int value) {
        
        status_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *状态 1.complete 2.processing 3.failed 4.canceled
       * </pre>
       *
       * <code>int32 status = 9;</code>
       * @return This builder for chaining.
       */
      public Builder clearStatus() {
        
        status_ = 0;
        onChanged();
        return this;
      }

      private int type_ ;
      /**
       * <pre>
       *类型 1.deposit 2.withdraw 3.bill 4.bonus
       * </pre>
       *
       * <code>int32 type = 10;</code>
       * @return The type.
       */
      @java.lang.Override
      public int getType() {
        return type_;
      }
      /**
       * <pre>
       *类型 1.deposit 2.withdraw 3.bill 4.bonus
       * </pre>
       *
       * <code>int32 type = 10;</code>
       * @param value The type to set.
       * @return This builder for chaining.
       */
      public Builder setType(int value) {
        
        type_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *类型 1.deposit 2.withdraw 3.bill 4.bonus
       * </pre>
       *
       * <code>int32 type = 10;</code>
       * @return This builder for chaining.
       */
      public Builder clearType() {
        
        type_ = 0;
        onChanged();
        return this;
      }

      private java.lang.Object walletAddress_ = "";
      /**
       * <pre>
       *钱包地址
       * </pre>
       *
       * <code>string walletAddress = 11;</code>
       * @return The walletAddress.
       */
      public java.lang.String getWalletAddress() {
        java.lang.Object ref = walletAddress_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          walletAddress_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       *钱包地址
       * </pre>
       *
       * <code>string walletAddress = 11;</code>
       * @return The bytes for walletAddress.
       */
      public com.google.protobuf.ByteString
          getWalletAddressBytes() {
        java.lang.Object ref = walletAddress_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          walletAddress_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       *钱包地址
       * </pre>
       *
       * <code>string walletAddress = 11;</code>
       * @param value The walletAddress to set.
       * @return This builder for chaining.
       */
      public Builder setWalletAddress(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  
        walletAddress_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *钱包地址
       * </pre>
       *
       * <code>string walletAddress = 11;</code>
       * @return This builder for chaining.
       */
      public Builder clearWalletAddress() {
        
        walletAddress_ = getDefaultInstance().getWalletAddress();
        onChanged();
        return this;
      }
      /**
       * <pre>
       *钱包地址
       * </pre>
       *
       * <code>string walletAddress = 11;</code>
       * @param value The bytes for walletAddress to set.
       * @return This builder for chaining.
       */
      public Builder setWalletAddressBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
        
        walletAddress_ = value;
        onChanged();
        return this;
      }

      private java.lang.Object orderId_ = "";
      /**
       * <pre>
       *订单id
       * </pre>
       *
       * <code>string orderId = 12;</code>
       * @return The orderId.
       */
      public java.lang.String getOrderId() {
        java.lang.Object ref = orderId_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          orderId_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       *订单id
       * </pre>
       *
       * <code>string orderId = 12;</code>
       * @return The bytes for orderId.
       */
      public com.google.protobuf.ByteString
          getOrderIdBytes() {
        java.lang.Object ref = orderId_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          orderId_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       *订单id
       * </pre>
       *
       * <code>string orderId = 12;</code>
       * @param value The orderId to set.
       * @return This builder for chaining.
       */
      public Builder setOrderId(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  
        orderId_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *订单id
       * </pre>
       *
       * <code>string orderId = 12;</code>
       * @return This builder for chaining.
       */
      public Builder clearOrderId() {
        
        orderId_ = getDefaultInstance().getOrderId();
        onChanged();
        return this;
      }
      /**
       * <pre>
       *订单id
       * </pre>
       *
       * <code>string orderId = 12;</code>
       * @param value The bytes for orderId to set.
       * @return This builder for chaining.
       */
      public Builder setOrderIdBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
        
        orderId_ = value;
        onChanged();
        return this;
      }

      private double fee_ ;
      /**
       * <pre>
       *手续费
       * </pre>
       *
       * <code>double fee = 13;</code>
       * @return The fee.
       */
      @java.lang.Override
      public double getFee() {
        return fee_;
      }
      /**
       * <pre>
       *手续费
       * </pre>
       *
       * <code>double fee = 13;</code>
       * @param value The fee to set.
       * @return This builder for chaining.
       */
      public Builder setFee(double value) {
        
        fee_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *手续费
       * </pre>
       *
       * <code>double fee = 13;</code>
       * @return This builder for chaining.
       */
      public Builder clearFee() {
        
        fee_ = 0D;
        onChanged();
        return this;
      }

      private java.lang.Object channel_ = "";
      /**
       * <pre>
       *渠道
       * </pre>
       *
       * <code>string channel = 14;</code>
       * @return The channel.
       */
      public java.lang.String getChannel() {
        java.lang.Object ref = channel_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          channel_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       *渠道
       * </pre>
       *
       * <code>string channel = 14;</code>
       * @return The bytes for channel.
       */
      public com.google.protobuf.ByteString
          getChannelBytes() {
        java.lang.Object ref = channel_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          channel_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       *渠道
       * </pre>
       *
       * <code>string channel = 14;</code>
       * @param value The channel to set.
       * @return This builder for chaining.
       */
      public Builder setChannel(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  
        channel_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *渠道
       * </pre>
       *
       * <code>string channel = 14;</code>
       * @return This builder for chaining.
       */
      public Builder clearChannel() {
        
        channel_ = getDefaultInstance().getChannel();
        onChanged();
        return this;
      }
      /**
       * <pre>
       *渠道
       * </pre>
       *
       * <code>string channel = 14;</code>
       * @param value The bytes for channel to set.
       * @return This builder for chaining.
       */
      public Builder setChannelBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
        
        channel_ = value;
        onChanged();
        return this;
      }

      private java.lang.Object tips_ = "";
      /**
       * <pre>
       *备注
       * </pre>
       *
       * <code>string tips = 15;</code>
       * @return The tips.
       */
      public java.lang.String getTips() {
        java.lang.Object ref = tips_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          tips_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       *备注
       * </pre>
       *
       * <code>string tips = 15;</code>
       * @return The bytes for tips.
       */
      public com.google.protobuf.ByteString
          getTipsBytes() {
        java.lang.Object ref = tips_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          tips_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       *备注
       * </pre>
       *
       * <code>string tips = 15;</code>
       * @param value The tips to set.
       * @return This builder for chaining.
       */
      public Builder setTips(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  
        tips_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *备注
       * </pre>
       *
       * <code>string tips = 15;</code>
       * @return This builder for chaining.
       */
      public Builder clearTips() {
        
        tips_ = getDefaultInstance().getTips();
        onChanged();
        return this;
      }
      /**
       * <pre>
       *备注
       * </pre>
       *
       * <code>string tips = 15;</code>
       * @param value The bytes for tips to set.
       * @return This builder for chaining.
       */
      public Builder setTipsBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
        
        tips_ = value;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:ProtoMessage.TransactionInfo)
    }

    // @@protoc_insertion_point(class_scope:ProtoMessage.TransactionInfo)
    private static final com.proto.WalletMessage.TransactionInfo DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.proto.WalletMessage.TransactionInfo();
    }

    public static com.proto.WalletMessage.TransactionInfo getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<TransactionInfo>
        PARSER = new com.google.protobuf.AbstractParser<TransactionInfo>() {
      @java.lang.Override
      public TransactionInfo parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new TransactionInfo(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<TransactionInfo> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<TransactionInfo> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.proto.WalletMessage.TransactionInfo getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_ProtoMessage_ReqBetHistoryDataMessage_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_ProtoMessage_ReqBetHistoryDataMessage_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_ProtoMessage_ResBetHistoryDataMessage_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_ProtoMessage_ResBetHistoryDataMessage_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_ProtoMessage_ReqTransactionDataMessage_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_ProtoMessage_ReqTransactionDataMessage_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_ProtoMessage_ResTransactionDataMessage_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_ProtoMessage_ResTransactionDataMessage_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_ProtoMessage_TransactionInfo_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_ProtoMessage_TransactionInfo_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n\023WalletMessage.proto\022\014ProtoMessage\032\023Com" +
      "monMessage.proto\"\322\001\n\030ReqBetHistoryDataMe" +
      "ssage\022\r\n\005msgID\030\001 \001(\005\022\016\n\006gameId\030\002 \001(\005\022\020\n\010" +
      "gameType\030\003 \001(\005\022\022\n\nplatformId\030\004 \001(\005\022\016\n\006as" +
      "sets\030\005 \001(\005\022\014\n\004past\030\006 \001(\005\022\014\n\004page\030\007 \001(\005\022\020" +
      "\n\010pageSize\030\010 \001(\005\022\020\n\010statTime\030\t \001(\003\022\017\n\007en" +
      "dTime\030\n \001(\003\022\020\n\010language\030\013 \001(\005\"\366\001\n\030ResBet" +
      "HistoryDataMessage\022\r\n\005msgID\030\001 \001(\005\022\r\n\005err" +
      "or\030\002 \001(\005\022\016\n\006gameId\030\003 \001(\005\022\020\n\010gameType\030\004 \001" +
      "(\005\022\022\n\nplatformId\030\005 \001(\005\022\016\n\006assets\030\006 \001(\005\022\014" +
      "\n\004past\030\007 \001(\005\022\014\n\004page\030\010 \001(\005\022\020\n\010pageSize\030\t" +
      " \001(\005\022\r\n\005total\030\n \001(\005\022\021\n\ttotalPage\030\013 \001(\005\022&" +
      "\n\007betList\030\014 \003(\0132\025.ProtoMessage.BetInfo\"\306" +
      "\001\n\031ReqTransactionDataMessage\022\r\n\005msgID\030\001 " +
      "\001(\005\022\027\n\017transactionType\030\002 \001(\005\022\016\n\006assets\030\003" +
      " \001(\005\022\014\n\004past\030\004 \001(\005\022\016\n\006status\030\005 \001(\005\022\014\n\004pa" +
      "ge\030\006 \001(\005\022\020\n\010pageSize\030\007 \001(\005\022\020\n\010statTime\030\010" +
      " \001(\003\022\017\n\007endTime\030\t \001(\003\022\020\n\010language\030\n \001(\005\"" +
      "\372\001\n\031ResTransactionDataMessage\022\r\n\005msgID\030\001" +
      " \001(\005\022\r\n\005error\030\002 \001(\005\022\027\n\017transactionType\030\003" +
      " \001(\005\022\016\n\006assets\030\004 \001(\005\022\014\n\004past\030\005 \001(\005\022\016\n\006st" +
      "atus\030\006 \001(\005\022\014\n\004page\030\007 \001(\005\022\020\n\010pageSize\030\010 \001" +
      "(\005\022\r\n\005total\030\t \001(\005\022\021\n\ttotalPage\030\n \001(\005\0226\n\017" +
      "transactionList\030\013 \003(\0132\035.ProtoMessage.Tra" +
      "nsactionInfo\"\207\002\n\017TransactionInfo\022\r\n\005betI" +
      "d\030\001 \001(\t\022\020\n\010gameName\030\002 \001(\t\022\022\n\ncurrencyId\030" +
      "\003 \001(\005\022\016\n\006amount\030\004 \001(\001\022\014\n\004time\030\005 \001(\003\022\016\n\006p" +
      "ayout\030\006 \001(\001\022\016\n\006profit\030\007 \001(\001\022\017\n\007balance\030\010" +
      " \001(\001\022\016\n\006status\030\t \001(\005\022\014\n\004type\030\n \001(\005\022\025\n\rwa" +
      "lletAddress\030\013 \001(\t\022\017\n\007orderId\030\014 \001(\t\022\013\n\003fe" +
      "e\030\r \001(\001\022\017\n\007channel\030\016 \001(\t\022\014\n\004tips\030\017 \001(\tB\013" +
      "\n\tcom.protob\006proto3"
    };
    descriptor = com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
          com.proto.CommonMessage.getDescriptor(),
        });
    internal_static_ProtoMessage_ReqBetHistoryDataMessage_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_ProtoMessage_ReqBetHistoryDataMessage_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_ProtoMessage_ReqBetHistoryDataMessage_descriptor,
        new java.lang.String[] { "MsgID", "GameId", "GameType", "PlatformId", "Assets", "Past", "Page", "PageSize", "StatTime", "EndTime", "Language", });
    internal_static_ProtoMessage_ResBetHistoryDataMessage_descriptor =
      getDescriptor().getMessageTypes().get(1);
    internal_static_ProtoMessage_ResBetHistoryDataMessage_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_ProtoMessage_ResBetHistoryDataMessage_descriptor,
        new java.lang.String[] { "MsgID", "Error", "GameId", "GameType", "PlatformId", "Assets", "Past", "Page", "PageSize", "Total", "TotalPage", "BetList", });
    internal_static_ProtoMessage_ReqTransactionDataMessage_descriptor =
      getDescriptor().getMessageTypes().get(2);
    internal_static_ProtoMessage_ReqTransactionDataMessage_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_ProtoMessage_ReqTransactionDataMessage_descriptor,
        new java.lang.String[] { "MsgID", "TransactionType", "Assets", "Past", "Status", "Page", "PageSize", "StatTime", "EndTime", "Language", });
    internal_static_ProtoMessage_ResTransactionDataMessage_descriptor =
      getDescriptor().getMessageTypes().get(3);
    internal_static_ProtoMessage_ResTransactionDataMessage_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_ProtoMessage_ResTransactionDataMessage_descriptor,
        new java.lang.String[] { "MsgID", "Error", "TransactionType", "Assets", "Past", "Status", "Page", "PageSize", "Total", "TotalPage", "TransactionList", });
    internal_static_ProtoMessage_TransactionInfo_descriptor =
      getDescriptor().getMessageTypes().get(4);
    internal_static_ProtoMessage_TransactionInfo_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_ProtoMessage_TransactionInfo_descriptor,
        new java.lang.String[] { "BetId", "GameName", "CurrencyId", "Amount", "Time", "Payout", "Profit", "Balance", "Status", "Type", "WalletAddress", "OrderId", "Fee", "Channel", "Tips", });
    com.proto.CommonMessage.getDescriptor();
  }

  // @@protoc_insertion_point(outer_class_scope)
}
