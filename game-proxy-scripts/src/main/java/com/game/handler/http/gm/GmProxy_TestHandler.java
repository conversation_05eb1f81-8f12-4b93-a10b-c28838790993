package com.game.handler.http.gm;

import com.game.engine.io.handler.HttpHandler;
import com.game.engine.io.redis.RedisPoolManager;
import com.game.engine.math.MathUtils;
import com.game.engine.script.IHandlerEntity;
import com.game.engine.utils.MsgUtil;
import com.game.engine.utils.ShareCodeUtil;
import com.game.engine.utils.TimeUtil;
import com.game.engine.utils.UniqueIDGenerator;
import com.game.entity.player.Player;
import com.game.enums.ErrorCode;
import com.game.enums.redis.RedisHall;
import com.game.enums.redis.RedisLogin;
import com.game.utils.VirtualThreadUtils;
import io.lettuce.core.ScriptOutputType;
import io.lettuce.core.api.sync.RedisCommands;
import it.unimi.dsi.fastutil.ints.IntArrayList;
import it.unimi.dsi.fastutil.ints.IntList;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.atomic.AtomicInteger;

//http://127.0.0.1:8380/gmProxy/test?default=
@IHandlerEntity(path = "/gmProxy/test", desc = "重新加载脚本")
public class GmProxy_TestHandler extends HttpHandler {
    private static final Logger LOGGER = LoggerFactory.getLogger(GmProxy_TestHandler.class);

    @Override
    public void run() {
        final int THREAD_COUNT = 500;
        try {
            Map<Integer, Double> currencyMap = new HashMap<Integer, Double>();
            currencyMap.put(1000, 11d);
            currencyMap.put(10000, 44d);

//            RedisPoolManager.getInstance().function(commands -> {
//                        final RedisCommands<String, String> redis = commands.sync();
//                        for (Map.Entry<Integer, Double> entry : currencyMap.entrySet()) {
//                            try {
//                                redis.hset(RedisHall.Platform_Role_Map_Currency.getKey(1111111), String.valueOf(entry.getKey()), String.valueOf(entry.getValue()));
//                            } catch (Exception e) {
//                                LOGGER.warn("Currency update failed at index {}", entry.getKey(), e);
//                            }
//                        }
//                        return true;
//                    }
//            );

//            RedisPoolManager.getInstance().function(commands -> {
//                final RedisCommands<String, String> redis = commands.sync();
//                try {
//                    Map<String, String> data = new HashMap<>();
//                    for (Map.Entry<Integer, Double> entry : currencyMap.entrySet()) {
//                        data.put(String.valueOf(entry.getKey()), String.valueOf(entry.getValue()));
//                    }
//                    redis.hset(RedisHall.Platform_Role_Map_Currency.getKey(1111111), data);
//                } catch (Exception e) {
//                    LOGGER.warn("Batch currency update failed", e);
//                }
//                return true;
//            });

//            ExecutorService executor = Executors.newFixedThreadPool(200); // 可调节线程池大小
//            CountDownLatch latch = new CountDownLatch(THREAD_COUNT);
//            AtomicInteger successCount = new AtomicInteger();
//            AtomicInteger failCount = new AtomicInteger();
//
//            long startTime = System.currentTimeMillis();
//
//            for (int i = 0; i < THREAD_COUNT; i++) {
//                final int index = i;
//                executor.execute(() -> {
//                    try {
//                        RedisPoolManager.getInstance().asyncPipeline(commands -> {
//                            List<CompletableFuture<?>> futures = new ArrayList<>();
//
//                            // 每个线程执行不同的 Redis 命令
//                            String key = "stress:test:" + index;
//                            futures.add(commands.set(key, "value-" + index).toCompletableFuture());
//                            futures.add(commands.expire(key, 300).toCompletableFuture());
//
//                            return futures;
//                        }).whenComplete((v, ex) -> {
//                            if (ex == null) {
//                                successCount.incrementAndGet();
//                            } else {
//                                failCount.incrementAndGet();
//                                ex.printStackTrace();
//                            }
//                            latch.countDown();
//                        });
//                    } catch (Exception e) {
//                        failCount.incrementAndGet();
//                        e.printStackTrace();
//                        latch.countDown();
//                    }
//                });
//            }
//
//            latch.await(); // 等待所有任务完成
//            executor.shutdown();
//
//            long endTime = System.currentTimeMillis();
//            System.out.println("✅ 测试完成");
//            System.out.println("成功次数: " + successCount.get());
//            System.out.println("失败次数: " + failCount.get());
//            System.out.println("耗时(ms): " + (endTime - startTime));
//            final long startTime = System.currentTimeMillis();
//            VirtualThreadUtils.execute(() -> {
//                RedisPoolManager.getInstance().syncConsumer(commands -> {
//                            List<CompletableFuture<?>> futures = new ArrayList<>();
//                            for (int i = 1; i <= MathUtils.random(2, 10); i++) {
//                                try {
////                                commands.hset(RedisHall.Platform_Role_Map_Currency.getKey(11111111), String.valueOf(1000), String.valueOf(i));
//                                    commands.hset(RedisHall.Platform_Role_Map_Currency.getKey(11111111), String.valueOf(10000), String.valueOf(i));
//                                } catch (Exception e) {
//                                    LOGGER.warn("Currency update failed at index {}", i, e);
//                                }
//                            }
//                        }
//                );
//            });

//            LOGGER.warn("cost：{}ms", System.currentTimeMillis() - startTime);

//            final String value = RedisPoolManager.getInstance().function(connection ->
//                    connection.sync().hget(RedisHall.Platform_Role_Map_Currency.getKey(11111111), String.valueOf(1000)));
//
//            final String value1 = RedisPoolManager.getInstance().function(connection ->
//                    connection.sync().hget(RedisHall.Platform_Role_Map_Currency.getKey(11111111), String.valueOf(10000)));
//
//            LOGGER.warn("value：{}，value1：{}", value, value1);

            final String newCode = RedisPoolManager.getInstance().function(jedis -> {
                final Player player = new Player(3633954816L);
                try {
                    int batchSize = 5; // 每次尝试5个邀请码
                    int maxRetries = 20;  // 最多重试20次（每次尝试5个，总尝试100个码）
                    int retryCount = 0;

                    final String luaScript =
                            "for i=1, #ARGV do " +
                                    "    if redis.call('hsetnx', KEYS[1], ARGV[i], ARGV[#ARGV]) == 1 then " +
                                    "        return ARGV[i] " +
                                    "    end " +
                                    "end " +
                                    "return nil";

                    while (retryCount < maxRetries) {
                        List<String> codes = new ArrayList<>();
                        for (int i = 0; i < batchSize; i++) {
//                            codes.add(ShareCodeUtil.generateShareCode());
                            codes.add("t6oz1kwhk");
                        }

                        List<String> args = new ArrayList<>(codes);
                        args.add(String.valueOf(player.getPlayerId())); // 把accountId放在最后一个ARGV

                        final String result = jedis.sync().eval(
                                luaScript,
                                ScriptOutputType.VALUE,
                                new String[]{RedisLogin.Platform_LG_Map_InvitationID.getKey()},
                                args.toArray(new String[0])
                        );
                        if (result != null) {
                            return result; // 成功返回
                        }

                        retryCount++;

                        if (retryCount % 5 == 0) {
                            try {
                                Thread.sleep(1); // 每5次冲突稍微让下CPU
                            } catch (InterruptedException e) {
                                LOGGER.error("InterruptedException", e);
                            }
                        }

                        LOGGER.warn("批量生成邀请码失败，重试 {}/{}", retryCount, maxRetries);
                    }

                    throw new IllegalStateException("生成邀请码失败，请稍后重试");
                } catch (Exception e) {
                    LOGGER.error("generateInvitationCode", e);
                    return null;
                }
            });

//            LOGGER.warn("newCode: {}", newCode);
//            LOGGER.warn("code: {}", code);
            MsgUtil.responseHttp(ErrorCode.Success.getCode(), session);
        } catch (Exception e) {
            LOGGER.error("", e);
            MsgUtil.responseHttp(ErrorCode.Internal_Server_Error.getCode(), session);
        }
    }
}
