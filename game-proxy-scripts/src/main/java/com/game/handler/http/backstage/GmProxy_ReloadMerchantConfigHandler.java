//package com.game.handler.http.backstage;
//
//import com.game.engine.io.handler.HttpHandler;
//import com.game.engine.script.IHandlerEntity;
//import com.game.engine.utils.MsgUtil;
//import com.game.enums.ErrorCode;
//import com.game.enums.MerchantConfigType;
//import com.game.proxy.manager.DataProxyMrg;
//import io.netty.util.internal.StringUtil;
//import org.slf4j.Logger;
//import org.slf4j.LoggerFactory;
//
////http://127.0.0.1:8380/gmProxy/ReloadMerchantConfig?business_no=&reloadType=
//@IHandlerEntity(path = "/gmProxy/ReloadMerchantConfig", desc = "重新加载商户配置")
//public class GmProxy_ReloadMerchantConfigHandler extends HttpHandler {
//    private static final Logger LOGGER = LoggerFactory.getLogger(GmProxy_ReloadMerchantConfigHandler.class);
//
//    @Override
//    public void run() {
//        try {
//            final String business_no = (String) paramsMap.get("business_no");
//            final String reloadType = (String) paramsMap.get("reloadType");
//
//            if (StringUtil.isNullOrEmpty(reloadType)) {
//                MsgUtil.responseHttp(ErrorCode.Parameter_Error.getCode(), session);
//                return;
//            }
//
//            switch (MerchantConfigType.valueOf(Integer.parseInt(reloadType))) {
//                case HelpCenter:
//                    if (StringUtil.isNullOrEmpty(business_no)) {
//                        MsgUtil.responseHttp(ErrorCode.Parameter_Error.getCode(), session);
//                        return;
//                    }
//                    DataProxyMrg.getInstance().loadByBusiness_noHelpCenter(business_no);
//                    break;
//                default:
//                    throw new IllegalArgumentException(reloadType + "，not exist");
//            }
//
//            LOGGER.info("ReloadMerchantConfig，business_no：{}，reloadType：{}， success ...", business_no, reloadType);
//            MsgUtil.responseHttp(ErrorCode.Success.getCode(), session);
//        } catch (Exception e) {
//            LOGGER.error("", e);
//            MsgUtil.responseHttp(ErrorCode.Internal_Server_Error.getCode(), session);
//        }
//    }
//}
