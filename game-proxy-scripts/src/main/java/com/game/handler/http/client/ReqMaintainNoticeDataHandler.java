package com.game.handler.http.client;

import com.game.c_entity.merchant.C_MaintainNotice;
import com.game.c_entity.middleplatform.C_BaseMaintainNotice;
import com.game.engine.io.handler.HttpHandler;
import com.game.engine.script.IHandlerEntity;
import com.game.engine.utils.JsonUtils;
import com.game.engine.utils.MsgUtil;
import com.game.enums.ErrorCode;
import com.game.proxy.manager.DataProxyMrg;
import com.proto.BackStageMessage;
import com.proto.MIDMessage;
import io.netty.util.internal.StringUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

//http://127.0.0.1:8380/api/client/maintainNoticeData
@IHandlerEntity(path = "/api/client/maintainNoticeData", desc = "请求维护公告")
public class ReqMaintainNoticeDataHandler extends HttpHandler {
    private final Logger LOGGER = LoggerFactory.getLogger(ReqMaintainNoticeDataHandler.class);

    @Override
    public void run() {
        final BackStageMessage.ResConfigDataMessage.Builder res = BackStageMessage.ResConfigDataMessage.newBuilder();
        res.setMsgID(MIDMessage.MID.ResConfigData_VALUE);
        try {
            final String data = (String) paramsMap.get("data");
            final byte[] bytes = MsgUtil.decode(data);

            final BackStageMessage.ReqConfigDataMessage req = BackStageMessage.ReqConfigDataMessage.parseFrom(bytes);
            final String host = req.getHost();
            final int language = req.getLanguage();

            final String business_no = DataProxyMrg.getInstance().findBusiness_no(host);
            if (StringUtil.isNullOrEmpty(business_no)) {
                res.setError(ErrorCode.Data_Error.getCode());
                MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                return;
            }

            {
                //中台维护公告
                final C_BaseMaintainNotice c_baseMaintainNotice = DataProxyMrg.getInstance().findBaseMaintainNotice();
                if (c_baseMaintainNotice != null) {
                    c_baseMaintainNotice.check();
                    final BackStageMessage.MaintainNoticeInfo maintainNoticeInfo = buildBaseMaintainNoticeInfo(c_baseMaintainNotice, language);
                    if (maintainNoticeInfo != null) {
                        res.setMaintainNoticeInfo(maintainNoticeInfo);
                    }
                    MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                    return;
                }
            }

            {
                //维护公告
                final C_MaintainNotice c_maintainNotice = DataProxyMrg.getInstance().findMaintainNotice(business_no);
                if (c_maintainNotice != null) {
                    c_maintainNotice.check();
                    final BackStageMessage.MaintainNoticeInfo maintainNoticeInfo = buildMaintainNoticeInfo(c_maintainNotice, language);
                    if (maintainNoticeInfo != null) {
                        res.setMaintainNoticeInfo(maintainNoticeInfo);
                    }
                    MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                    return;
                }
            }

            MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
        } catch (Exception e) {
            LOGGER.error("ReqMaintainNoticeDataHandler", e);
            res.setError(ErrorCode.Internal_Server_Error.getCode());
            MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
        }
    }

    private BackStageMessage.MaintainNoticeInfo buildBaseMaintainNoticeInfo(C_BaseMaintainNotice c_baseMaintainNotice, int language) {
        final C_BaseMaintainNotice.MaintainInfo maintainInfo = c_baseMaintainNotice.getMaintainInfoMap().get(language);
        if (maintainInfo == null) {
            return null;
        }
        final BackStageMessage.MaintainNoticeInfo.Builder maintainNoticeInfo = BackStageMessage.MaintainNoticeInfo.newBuilder()
                .setInfo(maintainInfo.getInfo())
                .setStartTime(c_baseMaintainNotice.getStartTime())
                .setEndTime(c_baseMaintainNotice.getEndTime());
        return maintainNoticeInfo.build();
    }

    private BackStageMessage.MaintainNoticeInfo buildMaintainNoticeInfo(C_MaintainNotice c_maintainNotice, int language) {
        final C_MaintainNotice.MaintainInfo maintainInfo = c_maintainNotice.getMaintainInfoMap().get(language);
        if (maintainInfo == null) {
            return null;
        }
        final BackStageMessage.MaintainNoticeInfo.Builder maintainNoticeInfo = BackStageMessage.MaintainNoticeInfo.newBuilder()
                .setInfo(maintainInfo.getInfo())
                .setStartTime(c_maintainNotice.getStartTime())
                .setEndTime(c_maintainNotice.getEndTime());
        return maintainNoticeInfo.build();
    }
}
