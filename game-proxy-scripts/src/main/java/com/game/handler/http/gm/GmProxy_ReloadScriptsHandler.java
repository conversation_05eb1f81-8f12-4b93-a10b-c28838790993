package com.game.handler.http.gm;

import com.game.engine.io.handler.HttpHandler;
import com.game.engine.script.IHandlerEntity;
import com.game.engine.script.ScriptLoader;
import com.game.engine.utils.MsgUtil;
import com.game.engine.utils.Symbol;
import com.game.enums.ErrorCode;
import io.netty.util.internal.StringUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.HashMap;
import java.util.Map;

//http://127.0.0.1:8380/gmProxy/reloadScripts?scriptsName=GmProxy_GameMachineListHandler
@IHandlerEntity(path = "/gmProxy/reloadScripts", desc = "重新加载脚本")
public class GmProxy_ReloadScriptsHandler extends HttpHandler {
    private static final Logger LOGGER = LoggerFactory.getLogger(GmProxy_ReloadScriptsHandler.class);

    @Override
    public void run() {
        try {
            final Map<String, Object> paramsMap = getParamsMap();
            final String scriptsName = (String) paramsMap.get("scriptsName");
            if (StringUtil.isNullOrEmpty(scriptsName)) {
                MsgUtil.responseHttp(ErrorCode.Parameter_Error.getCode(), session);
                return;
            }
            final String[] scriptsNames = scriptsName.split(Symbol.DOUHAO_REG);
            final String sourceDir = ScriptLoader.getInstance().getSourceDir();
            final String data = ScriptLoader.getInstance().loadJava(sourceDir, scriptsNames);

            final Map<String, Object> res = new HashMap<>();
            res.put("error", ErrorCode.Success.getCode());
            res.put("data", data);
            response(res);
        } catch (Exception e) {
            LOGGER.error("", e);
            MsgUtil.responseHttp(ErrorCode.Internal_Server_Error.getCode(), session);
        }
    }
}
