package com.game.handler.http.client;

import com.game.c_entity.merchant.C_WebSiteInfo;
import com.game.engine.io.handler.HttpHandler;
import com.game.engine.script.IHandlerEntity;
import com.game.engine.utils.JsonUtils;
import com.game.engine.utils.MsgUtil;
import com.game.enums.ErrorCode;
import com.game.proxy.manager.DataProxyMrg;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

//http://127.0.0.1:8380/api/client/webSiteInfoData
@IHandlerEntity(path = "/api/client/webSiteInfoData", desc = "请求站点信息")
public class ReqWebSiteInfoDataHandler extends HttpHandler {
    private final Logger LOGGER = LoggerFactory.getLogger(ReqWebSiteInfoDataHandler.class);

    @Override
    public void run() {
        try {
            final String host = (String) paramsMap.get("host");

            final C_WebSiteInfo c_webSiteInfo = DataProxyMrg.getInstance().findWebSiteInfo(host);
            if (c_webSiteInfo == null) {
                MsgUtil.responseHttp(ErrorCode.Data_Error.getCode(), session);
                return;
            }
            c_webSiteInfo.check();

            final Optional<C_WebSiteInfo.WebSiteInfo> optional = c_webSiteInfo.getWebSiteInfoMap().values().stream().findFirst();
            if (optional.isEmpty()) {
                MsgUtil.responseHttp(ErrorCode.Data_Error.getCode(), session);
                return;
            }
            C_WebSiteInfo.WebSiteInfo webSiteInfo = optional.get();

            final Map<String, Object> jsonObject = new HashMap<>();
            jsonObject.put("ogTitle", webSiteInfo.getOgTile());
            jsonObject.put("ogType", webSiteInfo.getOgType());
            jsonObject.put("ogImage", webSiteInfo.getOgImage());
            jsonObject.put("ogDescription", webSiteInfo.getOgDescription());
            jsonObject.put("site_name", webSiteInfo.getSite_name());
            MsgUtil.responseHttp(jsonObject, session);
        } catch (Exception e) {
            LOGGER.error("ReqWebSiteInfoDataHandler", e);
            MsgUtil.responseHttp(ErrorCode.Internal_Server_Error.getCode(), session);
        }
    }
}
