package com.game.handler.script;

import com.game.dao.activity.BonusNoteDao;
import com.game.dao.activity.SpinBonusNoteDao;
import com.game.dao.activity.TicketsNoteDao;
import com.game.dao.game.GameNoteDao;
import com.game.dao.order.RechargeOrderDao;
import com.game.dao.order.WithdrawOrderDao;
import com.game.dao.quest.QuestNoteDao;
import com.game.dao.session.SessionDao;
import com.game.engine.io.redis.RedisPoolManager;
import com.game.engine.utils.TimeHelper;
import com.game.engine.utils.TimeUtil;
import com.game.engine.utils.TimeUtils;
import com.game.enums.redis.RedisAllGame;
import com.game.enums.redis.RedisRanking;
import com.game.manager.EntityDaoMrg;
import com.game.proxy.manager.DataProxyMrg;
import com.game.proxy.server.script.IDataDeleteScript;
import com.game.utils.VirtualThreadUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.time.DayOfWeek;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ThreadLocalRandom;

public class DataDeleteScript implements IDataDeleteScript {
    private static final Logger LOGGER = LoggerFactory.getLogger(DataDeleteScript.class);

    private static long lastTime = nextZeroDelay(TimeUtil.currentTimeMillis());

    @Override
    public void delete() {
        if (TimeUtil.currentTimeMillis() < lastTime) {
            return;
        }

        try {
            LOGGER.warn("execute data delete。。。");
            long start = TimeUtil.currentTimeMillis();
            {
                VirtualThreadUtils.execute(() -> {
                    //bonus 60
                    final long tick = start - 60 * TimeUtil.DAY;
                    final long count = EntityDaoMrg.getInstance().getDao(BonusNoteDao.class).delete(tick);
                    LOGGER.info("delete bonusNote count；{}，cost：{}", count, TimeUtil.currentTimeMillis() - start);
                });
            }

            {
                VirtualThreadUtils.execute(() -> {
                    //game 90
                    final long tick = start - 90 * TimeUtil.DAY;
                    final long count = EntityDaoMrg.getInstance().getDao(GameNoteDao.class).delete(tick);
                    LOGGER.info("delete gameNote count；{}，cost：{}", count, TimeUtil.currentTimeMillis() - start);
                });
            }

            {
                VirtualThreadUtils.execute(() -> {
                    //recharge 90
                    final long tick = start - 90 * TimeUtil.DAY;
                    final long count = EntityDaoMrg.getInstance().getDao(RechargeOrderDao.class).delete(tick);
                    LOGGER.info("delete rechargeNote count；{}，cost：{}", count, TimeUtil.currentTimeMillis() - start);
                });
            }

            {
                VirtualThreadUtils.execute(() -> {
                    //withdraw 90
                    final long tick = start - 90 * TimeUtil.DAY;
                    final long count = EntityDaoMrg.getInstance().getDao(WithdrawOrderDao.class).delete(tick);
                    LOGGER.info("delete withdrawNote count；{}，cost：{}", count, TimeUtil.currentTimeMillis() - start);
                });
            }

            {
                VirtualThreadUtils.execute(() -> {
                    //session 60
                    final long tick = start - 60 * TimeUtil.DAY;
                    final long count = EntityDaoMrg.getInstance().getDao(SessionDao.class).delete(tick);
                    LOGGER.info("delete sessionNote count；{}，cost：{}", count, TimeUtil.currentTimeMillis() - start);
                });
            }

            {
                VirtualThreadUtils.execute(() -> {
                    //weeklyRaffle tickets 90
                    final long tick = start - 90 * TimeUtil.DAY;
                    final long count = EntityDaoMrg.getInstance().getDao(TicketsNoteDao.class).delete(tick);
                    LOGGER.info("delete ticketsNote count；{}，cost：{}", count, TimeUtil.currentTimeMillis() - start);
                });
            }

            {
                VirtualThreadUtils.execute(() -> {
                    //quest 30
                    final long tick = start - 30 * TimeUtil.DAY;
                    final long count = EntityDaoMrg.getInstance().getDao(QuestNoteDao.class).delete(tick);
                    LOGGER.info("delete questNote count；{}，cost：{}", count, TimeUtil.currentTimeMillis() - start);
                });
            }

            {
                VirtualThreadUtils.execute(() -> {
                    //luckSpin 30
                    final long tick = start - 30 * TimeUtil.DAY;
                    final long count = EntityDaoMrg.getInstance().getDao(SpinBonusNoteDao.class).delete(tick);
                    LOGGER.info("delete spinBonus count；{}，cost：{}", count, TimeUtil.currentTimeMillis() - start);
                });
            }

            final List<String> business_noList = DataProxyMrg.getInstance().loadAllMerchant();
            {
                //每日竞赛
                final long tick = TimeHelper.SYSTEM.getTimeEndOfToday(start - 7 * TimeUtil.DAY);
                final String gameId = TimeUtil.getDateTimeFormat(tick, TimeUtil.YYYYMMDD);
                RedisPoolManager.getInstance().asyncPipeline(commands -> {
                    final List<CompletableFuture<?>> futures = new ArrayList<>();
                    for (String business_no : business_noList) {
                        futures.add(commands.del(RedisRanking.RANKING_DAILY_CONTEST.getKey(business_no, gameId))
                                .toCompletableFuture());
                        futures.add(commands.del(RedisAllGame.Platform_All_DailyContest_PrizePool.getKey(business_no, gameId))
                                .toCompletableFuture());
                    }
                    return futures;
                });
                LOGGER.info("delete dailyContest ，gameId：{}，cost：{}", gameId, TimeUtil.currentTimeMillis() - start);
            }

            {
                //每周抽奖
                final long tick = TimeUtil.getDayOfWeekEndTimestamp(start, ZoneId.systemDefault().toString(), DayOfWeek.SUNDAY) - 10 * 7 * TimeUtil.DAY;
                final String gameId = TimeUtil.getDateTimeFormat(tick, TimeUtil.YYYYMMDDHHMMSS);
                RedisPoolManager.getInstance().asyncPipeline(commands -> {
                    final List<CompletableFuture<?>> futures = new ArrayList<>();
                    for (String business_no : business_noList) {
                        futures.add(commands.del(RedisAllGame.Platform_All_WeeklyRaffle_Tickets.getKey(business_no, gameId))
                                .toCompletableFuture());
                    }
                    return futures;
                });
                LOGGER.info("delete weeklyRaffle，gameId：{}，cost：{}", gameId, TimeUtil.currentTimeMillis() - start);
            }
        } catch (Exception e) {
            LOGGER.error("delete", e);
        } finally {
            lastTime = nextZeroDelay(TimeUtil.currentTimeMillis());
        }
    }

    /**
     * 一个随机延迟，避免所有game过于集中更新数据
     */
    private static int randomDelay() {
        return ThreadLocalRandom.current().nextInt(1000, 5000);
    }

    private static long nextZeroDelay(long curTimeMillis) {
        final long timeBeginOfToday = TimeHelper.SYSTEM.getTimeBeginOfToday(curTimeMillis) + 3 * TimeUtils.HOUR;
        return timeBeginOfToday + TimeUtils.DAY + randomDelay();
    }

}
