package com.game.handler.script;

import com.game.engine.enums.ServerType;
import com.game.engine.io.handler.IHandler;
import com.game.engine.io.message.HttpMessageBean;
import com.game.engine.script.ScriptLoader;
import com.game.engine.struct.ServerInfo;
import com.game.engine.utils.JsonUtils;
import com.game.engine.utils.MsgUtil;
import com.game.engine.utils.TimeUtil;
import com.game.entity.player.PlayerFields;
import com.game.enums.ErrorCode;
import com.game.proxy.manager.ServerMrg;
import com.game.proxy.server.script.IHttpLogicScript;
import com.game.redis.RedisUtils;
import com.game.utils.VirtualThreadUtils;
import com.proto.InnerMessage;
import com.proto.MIDMessage;
import io.netty.channel.Channel;
import io.netty.util.internal.StringUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Map;

public class HttpLogicScript implements IHttpLogicScript {
    public static Logger LOGGER = LoggerFactory.getLogger(HttpLogicScript.class);

    @Override
    public void httpHandler(Object msg, Object uri, Channel session) {
        try {
            @SuppressWarnings("unchecked") final Map<String, Object> paramsMap = (Map<String, Object>) msg;
            final String requestPath = (String) uri;
            if (StringUtil.isNullOrEmpty(requestPath) || paramsMap.isEmpty()) {
                LOGGER.warn("ip ：{}，request error：{}，params：{}", MsgUtil.getClientIp(session), requestPath, JsonUtils.writeAsJson(paramsMap));
                MsgUtil.responseHttp(ErrorCode.Parameter_Error.getCode(), session);
                return;
            }

            final String playerId = (String) paramsMap.get("playerId");
            if (!StringUtil.isNullOrEmpty(playerId)) {
                VirtualThreadUtils.execute(() -> forwardPlayerHallHttp(requestPath, paramsMap, session));
                //记录httpSession
                ServerMrg.getInstance().addHttpChannel(session);
                return;
            }

            if (forwardServerHttp(requestPath, paramsMap, session)) {
                return;
            }

            final HttpMessageBean httpMessageBean = ScriptLoader.getInstance().getHttpMessageBean(requestPath);
            if (httpMessageBean == null) {
                LOGGER.error("HttpMessagePoll，未能找到，content = {} 的 httpMessageBean", requestPath);
                MsgUtil.responseHttp(ErrorCode.Parameter_Error.getCode(), session);
                return;
            }

            final IHandler handler = httpMessageBean.newHandler();
            handler.setSession(session);
            handler.setCreateTime(TimeUtil.currentTimeMillis());
            handler.setParamsMap(paramsMap);
            if (requestPath.contains("reloadScripts") || requestPath.contains("Data")) {
                VirtualThreadUtils.execute(handler);
            } else {
                handler.run();
            }
        } catch (Exception e) {
            LOGGER.error("", e);
            MsgUtil.responseHttp(ErrorCode.Internal_Server_Error.getCode(), session);
        }
    }

    private void forwardPlayerHallHttp(String requestPath, Map<String, Object> paramsMap, Channel session) {
        try {
            final String playerId = (String) paramsMap.get("playerId");

            final String hallId = RedisUtils.getPlayerInfo(Long.parseLong(playerId), PlayerFields.hallId);

            final int serverId = StringUtil.isNullOrEmpty(hallId) ? 0 : Integer.parseInt(hallId);
            final ServerInfo serverInfo = ServerMrg.getInstance().getServerType(ServerType.HALL, Long.parseLong(playerId), serverId);
            if (serverInfo == null) {
                LOGGER.warn("serverId：{}，not exits", serverId);
                MsgUtil.responseHttp(ErrorCode.Internal_Server_Error.getCode(), session);
                return;
            }

            final InnerMessage.InnerReqHttpHandlerMessage.Builder httpHandler = InnerMessage.InnerReqHttpHandlerMessage.newBuilder();
            httpHandler.setMsgID(MIDMessage.MID.InnerReqHttpHandler_VALUE)
                    .setRequestPath(requestPath)
                    .setParams(JsonUtils.writeAsJson(paramsMap));
            MsgUtil.sendInnerMsg(serverInfo.getActiveSession(), httpHandler.build(), MsgUtil.getSessionID(session));
        } catch (Exception e) {
            LOGGER.error("", e);
            MsgUtil.responseHttp(ErrorCode.Internal_Server_Error.getCode(), session);
        }
    }

    private boolean forwardServerHttp(String requestPath, Map<String, Object> paramsMap, Channel session) {
        try {
            ServerType serverType = null;
            if (requestPath.contains("gmHall")) {
                serverType = ServerType.HALL;
            } else if (requestPath.contains("gmLogin")) {
                serverType = ServerType.LOGIN;
            }

            if (serverType == null) {
                return false;
            }

            final Map<Integer, ServerInfo> serversMap = ServerMrg.getInstance().getServersMap(serverType);
            if (serversMap.isEmpty()) {
                LOGGER.warn("serverType：{}，not exits", serverType);
                MsgUtil.responseHttp(ErrorCode.Internal_Server_Error.getCode(), session);
                return true;
            }

            final InnerMessage.InnerReqHttpHandlerMessage.Builder httpHandler = InnerMessage.InnerReqHttpHandlerMessage.newBuilder();
            httpHandler.setMsgID(MIDMessage.MID.InnerReqHttpHandler_VALUE)
                    .setRequestPath(requestPath)
                    .setParams(JsonUtils.writeAsJson(paramsMap));
            for (ServerInfo hallServer : serversMap.values()) {
                MsgUtil.sendInnerMsg(hallServer.getActiveSession(), httpHandler.build(), MsgUtil.getSessionID(session));
            }

            //记录httpSession
            ServerMrg.getInstance().addHttpChannel(session);
        } catch (Exception e) {
            LOGGER.error("", e);
            MsgUtil.responseHttp(ErrorCode.Internal_Server_Error.getCode(), session);
        }
        return true;
    }

}
