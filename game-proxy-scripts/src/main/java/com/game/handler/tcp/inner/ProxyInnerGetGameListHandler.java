//package com.game.handler.tcp.inner;
//
//import com.game.engine.io.handler.TcpHandler;
//import com.game.engine.script.IHandlerEntity;
//import com.game.entity.AgentGameInfo;
//import com.game.proxy.manager.ServerMrg;
//import com.proto.InnerMessage;
//import com.proto.MIDMessage;
//
//@IHandlerEntity(mid = MIDMessage.MID.InnerGetGameList_VALUE, msg = InnerMessage.InnerGetGameListMessage.class)
//public class ProxyInnerGetGameListHandler extends TcpHandler {
//    @Override
//    public void run() {
//        final InnerMessage.InnerGetGameListMessage.Builder res = InnerMessage.InnerGetGameListMessage.newBuilder();
//        res.setMsgID(MIDMessage.MID.InnerGetGameList_VALUE);
//        for (AgentGameInfo gameInfo : ServerMrg.getInstance().getGameInfoMap().values()) {
//            res.addAgentGameList(buildAgentGameInfo(gameInfo));
//        }
//        replyWithID(res.build());
//    }
//
//    private InnerMessage.AgentGameInfo buildAgentGameInfo(AgentGameInfo gameInfo) {
//        return InnerMessage.AgentGameInfo.newBuilder()
//                .setGameId(gameInfo.getGameId())
//                .setOnline(gameInfo.getOnline())
//                .build();
//    }
//}
