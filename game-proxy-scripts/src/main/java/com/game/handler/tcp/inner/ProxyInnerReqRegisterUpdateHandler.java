package com.game.handler.tcp.inner;

import com.game.engine.enums.ServerType;
import com.game.engine.io.handler.TcpHandler;
import com.game.engine.script.IHandlerEntity;
import com.game.engine.struct.ServerInfo;
import com.game.engine.utils.MsgUtil;
import com.game.proxy.manager.ServerMrg;
import com.proto.InnerMessage;
import com.proto.MIDMessage.MID;
import io.netty.util.internal.StringUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 服务器内部请求注册更新服务器信息
 */
@IHandlerEntity(mid = MID.InnerReqRegisterUpdate_VALUE, msg = InnerMessage.InnerReqRegisterUpdateMessage.class)
public final class ProxyInnerReqRegisterUpdateHandler extends TcpHandler {

    private static final Logger LOGGER = LoggerFactory.getLogger(ProxyInnerReqRegisterUpdateHandler.class);

    @Override
    public void run() {
        /**
         * 注册登录、网关、大厅
         */
        final InnerMessage.InnerReqRegisterUpdateMessage reqMessage = (InnerMessage.InnerReqRegisterUpdateMessage) getMessage();
        final InnerMessage.InnerServerInfo serverInfo = reqMessage.getServerInfo();

        final String ip = !StringUtil.isNullOrEmpty(serverInfo.getIp()) ? serverInfo.getIp() : MsgUtil.getIp(session);
        final String wwwIp = serverInfo.getWwwIp();
        final int serverId = serverInfo.getId();
        final ServerType serverType = ServerType.valueOf(serverInfo.getType());
        ServerInfo info = ServerMrg.getInstance().getServerInfo(serverType, serverId);
        boolean isRegister = false;
        if (info == null) {
            info = new ServerInfo();
            info.setId(serverId);
            info.setName(serverInfo.getName());
            info.setServerType(serverInfo.getType());
            ServerMrg.getInstance().addServerInfo(serverType, info);
            isRegister = true;
        }
        info.setIp(ip);
        info.setWwwip(wwwIp);//外网ip
        info.setPort(serverInfo.getPort());
        info.setHttpPort(serverInfo.getHttpPort());
        info.setPower(serverInfo.getPower());//最大重载并发量
        info.setContent(serverInfo.getContent());
        info.setOnline(serverInfo.getOnline());
        info.setVersion(serverInfo.getVersion());
        info.onSessionOpen(session);
        if (!StringUtil.isNullOrEmpty(info.getAccountWhiteList())) {
            info.setAccountWhiteList(info.getAccountWhiteList());
        }
        if (!StringUtil.isNullOrEmpty(info.getIpWhiteList())) {
            info.setIpWhiteList(info.getIpWhiteList());
        }
        if ((!info.getIp().equals(ip) && !ip.equals("127.0.0.1"))) {
            LOGGER.error("服务器：{}，与配置不一致：ip{}，配置ip{}", serverType, ip, info.getIp());
            this.session.close();
            return;
        }

        if (isRegister) {
            LOGGER.warn("register proxy：{}，server：{}：", ServerType.valueOf(info.getServerType()), info.toString());
        }

    }

}
