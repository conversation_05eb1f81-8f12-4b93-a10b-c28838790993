//package com.game.handler.tcp.inner;
//
//import com.game.engine.io.handler.TcpHandler;
//import com.game.engine.script.IHandlerEntity;
//import com.game.entity.AgentGameInfo;
//import com.game.proxy.manager.ServerMrg;
//import com.proto.InnerMessage;
//import com.proto.MIDMessage;
//
//@IHandlerEntity(mid = MIDMessage.MID.InnerNotifyUpdateGameInfo_VALUE, msg = InnerMessage.InnerNotifyUpdateGameInfoMessage.class)
//public class ProxyInnerNotifyUpdateGameInfoHandler extends TcpHandler {
//    @Override
//    public void run() {
//        final InnerMessage.InnerNotifyUpdateGameInfoMessage req = (InnerMessage.InnerNotifyUpdateGameInfoMessage) getMessage();
//        final int gameId = req.getGameId();
//        final AgentGameInfo gameInfo = ServerMrg.getInstance().getGameInfo(gameId);
//        switch (req.getType()) {
//            case 1://进入
//                gameInfo.setOnline(gameInfo.getOnline() + 1);
//                break;
//            case 2://退出
//                gameInfo.setOnline(gameInfo.getOnline() - 1);
//                if (gameInfo.getOnline() <= 0) {
//                    gameInfo.setOnline(1);
//                }
//                break;
//            default:
//                throw new IllegalStateException("Unexpected value: " + req.getType());
//        }
//    }
//}
