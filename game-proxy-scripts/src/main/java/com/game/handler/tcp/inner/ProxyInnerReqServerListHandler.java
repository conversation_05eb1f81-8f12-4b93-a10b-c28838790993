/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.game.handler.tcp.inner;

import com.game.engine.enums.ServerType;
import com.game.engine.io.handler.TcpHandler;
import com.game.engine.script.IHandlerEntity;
import com.game.engine.struct.ServerInfo;
import com.game.proxy.manager.ServerMrg;
import com.proto.InnerMessage;
import com.proto.MIDMessage.MID;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Iterator;
import java.util.List;
import java.util.Map;

/**
 * 服务器列表
 *
 */
@IHandlerEntity(mid = MID.InnerReqServerList_VALUE, msg = InnerMessage.InnerReqServerListMessage.class)
public class ProxyInnerReqServerListHandler extends TcpHandler {
    private static final Logger LOGGER = LoggerFactory.getLogger(ProxyInnerReqServerListHandler.class);

    @Override
    public void run() {
        try {
            final InnerMessage.InnerReqServerListMessage req = (InnerMessage.InnerReqServerListMessage) getMessage();
            final InnerMessage.InnerResServerListMessage.Builder reply = InnerMessage.InnerResServerListMessage.newBuilder();
            reply.setMsgID(MID.InnerResServerList_VALUE);
            final List<Integer> typeList = req.getTypeList();
            for (int serverType : typeList) {
                getServerList(serverType, reply);
            }
            replyWithID(reply.build());
        } catch (Exception e) {
            LOGGER.error("InnerReqServerListHandler ", e);
        }
    }

    private void getServerList(int serverType, InnerMessage.InnerResServerListMessage.Builder reply) {
        final InnerMessage.ServerInfoList.Builder serverList = InnerMessage.ServerInfoList.newBuilder();
        serverList.setType(serverType);

        final InnerMessage.InnerServerInfo.Builder serverInfo = InnerMessage.InnerServerInfo.newBuilder();
        final Map<Integer, ServerInfo> serversMap = ServerMrg.getInstance().getServersMap(ServerType.valueOf(serverType));
        if (serversMap != null && !serversMap.isEmpty()) {
            final Iterator<ServerInfo> iterator = serversMap.values().iterator();
            while (iterator.hasNext()) {
                final ServerInfo server = iterator.next();
                if (server.getActiveSession() == null || !server.getActiveSession().isActive()) {
                    iterator.remove();
                    continue;
                }
                serverInfo.clear();
                serverInfo.setId(server.getId())
                        .setIp(server.getIp())
                        .setType(server.getServerType())
                        .setPort(server.getPort())
                        .setGameState(server.getGameState())
                        .setVersion(server.getVersion())
                        .setContent(server.getContent())
                        .setPower(server.getPower())
                        .setOnline(server.getOnline())
                        .setHttpPort(server.getHttpPort())
                        .setName(server.getName())
                        .setWwwIp(server.getWwwip());
                serverList.addServerInfos(serverInfo.build());
            }
            reply.addServerList(serverList.build());
        }
    }

}
