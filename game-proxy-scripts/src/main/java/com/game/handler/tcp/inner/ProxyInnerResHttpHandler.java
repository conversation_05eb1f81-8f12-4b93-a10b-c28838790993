package com.game.handler.tcp.inner;

import com.game.engine.io.handler.TcpHandler;
import com.game.engine.script.IHandlerEntity;
import com.game.engine.utils.JsonUtils;
import com.game.engine.utils.MsgUtil;
import com.game.enums.ErrorCode;
import com.game.proxy.manager.ServerMrg;
import com.proto.InnerMessage;
import com.proto.MIDMessage;
import io.netty.channel.Channel;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Map;

@IHandlerEntity(mid = MIDMessage.MID.InnerResHttpHandler_VALUE, msg = InnerMessage.InnerResHttpHandlerMessage.class)
public class ProxyInnerResHttpHandler extends TcpHandler {
    private static final Logger LOGGER = LoggerFactory.getLogger(ProxyInnerResHttpHandler.class);

    @Override
    public void run() {
        try {
            final InnerMessage.InnerResHttpHandlerMessage res = (InnerMessage.InnerResHttpHandlerMessage) getMessage();
            final String params = res.getParams();
            final Channel session = ServerMrg.getInstance().getHttpSessionMap().get(pid);
            if (session == null) {
                return;
            }
            @SuppressWarnings("unchecked") final Map<String, Object> response = JsonUtils.readFromJson(params, Map.class);
            MsgUtil.responseHttp(response, session);
        } catch (Exception e) {
            LOGGER.error("ProxyInnerResHttpHandler", e);
            MsgUtil.responseHttp(ErrorCode.Internal_Server_Error.getCode(), session);
        } finally {
            ServerMrg.getInstance().getHttpSessionMap().remove(pid);
        }

    }
}
