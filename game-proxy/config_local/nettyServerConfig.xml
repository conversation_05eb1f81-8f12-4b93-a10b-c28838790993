<?xml version="1.0" encoding="UTF-8"?>
<NettyServerConfig>
    <id>${PROXYID}</id>
    <name>代理服务器</name>
    <port>8300</port>
    <type>PROXY</type>
    <sendBufferSize>2048</sendBufferSize>
    <receiveBufferSize>8196</receiveBufferSize>
    <reuseAddress>true</reuseAddress>
    <tcpNoDelay>true</tcpNoDelay>
    <readerIdleTime>180</readerIdleTime>
    <writerIdleTime>180</writerIdleTime>
    <mongo_connection_string>${mongo.url}</mongo_connection_string>
    <mongo_read_write_database>${mongo.db}</mongo_read_write_database>
    <mongo_config_database>${mongo.config}</mongo_config_database>
</NettyServerConfig>