package com.game.proxy.main;

import com.game.engine.enums.MsgType;
import com.game.engine.enums.state.ServerState;
import com.game.engine.io.conf.JedisClusterConfig;
import com.game.engine.io.conf.NettyServerConfig;
import com.game.engine.io.handler.TcpHandler;
import com.game.engine.io.message.MessageBean;
import com.game.engine.io.redis.RedisPoolManager;
import com.game.engine.mongo.DBConnectionMrg;
import com.game.engine.net.*;
import com.game.engine.util.async.DefaultSameThreadScheduledExecutor;
import com.game.engine.util.async.SameThreadScheduledExecutor;
import com.game.engine.utils.*;
import com.game.engine.script.ScriptLoader;
import com.game.proxy.manager.DataProxyMrg;
import com.game.proxy.manager.ServerMrg;
import com.game.proxy.server.http.ProxyHttpServer;
import com.game.proxy.server.script.IDataDeleteScript;
import com.game.proxy.server.script.IHttpLogicScript;
import com.game.proxy.server.tcp.ProxyTcpServer;
import com.game.utils.VirtualThreadUtils;
import com.google.protobuf.Message;
import io.netty.channel.Channel;
import org.slf4j.LoggerFactory;
import org.slf4j.Logger;

import java.util.*;
import java.util.concurrent.*;

public class ProxyServer extends World {

    private static final Logger LOGGER = LoggerFactory.getLogger(ProxyServer.class);

    private static ProxyServer proxyServer;

    private ProxyTcpServer proxyTcpServer;
    private ProxyHttpServer proxyHttpServer;

    private NettyServerConfig nettyServerConfig;
    private NettyServerConfig nettyServerConfig_http;

    private SameThreadScheduledExecutor timerSystem;

    private ProxyServer() {
        super(1000 / 30);
    }

    public static ProxyServer getInstance() {
        return proxyServer;
    }

    public NettyServerConfig getNettyServerConfig() {
        return nettyServerConfig;
    }

    public static void main(String[] args) {
        LOGGER.info("服务器启动时间：{}", TimeUtil.getDateTimeFormat(TimeUtil.YYYYMMDDHHMMSS));

        Config.path = FileUtil.getMainPath();
        LOGGER.info("配置路径为：" + Config.path);
        ScriptLoader.getInstance().init((str) -> SysUtil.exit(ProxyServer.class, null, "加载脚本错误"));

        proxyServer = new ProxyServer();
        final ExecutorService service = Executors.newFixedThreadPool(1, (r) -> new Thread(r, "LOGIC_THREAD"));
        final EventConsumer<LogicEvent> eventConsumer = new EventConsumer<>(proxyServer);
        final GlobalQueue<LogicEvent> queue = new GlobalQueue<>(service, eventConsumer);
        GlobalQueueContainerMrg.getInstance().setGlobalQueue(queue);

        Runtime.getRuntime().addShutdownHook(new Thread(ProxyServer::stops));
    }

    private void initServerConfig() {
        NettyServerConfig nettyServerConfig = FileUtil.getConfigXML(Config.path, "nettyServerConfig.xml", NettyServerConfig.class);
        if (nettyServerConfig == null) {
            SysUtil.exit(ProxyServer.class, null, "nettyServerConfig");
            return;
        }

        NettyServerConfig nettyServerConfig_http = FileUtil.getConfigXML(Config.path, "nettyServerConfig_http.xml", NettyServerConfig.class);
        if (nettyServerConfig_http == null) {
            SysUtil.exit(ProxyServer.class, null, "nettyServerConfig_http");
            return;
        }

        this.nettyServerConfig = nettyServerConfig;
        this.nettyServerConfig_http = nettyServerConfig_http;

        Config.SERVER_ID = nettyServerConfig.getId(); // 设置SERVERID
        Config.SERVER_NAME = nettyServerConfig.getName(); // 设置SERVERNAME
        Config.SERVER_CHANNEL = nettyServerConfig.getChannel(); // 设置SERVERWEB
        Config.serverState = ServerState.NORMAL;
    }

    private void startServer() {
        proxyTcpServer = new ProxyTcpServer(nettyServerConfig);
        proxyHttpServer = new ProxyHttpServer(nettyServerConfig_http);
        {
            proxyTcpServer.start();
            proxyHttpServer.start();
        }
    }

    private static void stops() {
        try {
            Config.serverState = ServerState.CLOSING;
            if (proxyServer.timerSystem != null) {
                proxyServer.timerSystem.shutdownNow();
            }
            VirtualThreadUtils.shutdown();
            Thread.sleep(1000);
            RedisPoolManager.getInstance().destroy();
            proxyServer.stop();
        } catch (Exception e) {
            LOGGER.error("释放资源", e);
        }
    }

    private void stop() {
        if (proxyTcpServer != null) {
            proxyTcpServer.stop();
        }

        if (proxyHttpServer != null) {
            proxyHttpServer.stop();
        }
    }

    public ProxyTcpServer getProxyTcpServer() {
        return proxyTcpServer;
    }

    @Override
    protected void registerSeri() {

    }

    @Override
    protected void registerProtoHandler() {

    }

    @Override
    protected void listenOrConnect() throws Exception {
        startServer();
    }

    @Override
    protected void initWhenThreadStartImpl() throws Exception {
        initServerConfig();
        this.timerSystem = new DefaultSameThreadScheduledExecutor(4);

        final JedisClusterConfig jpc = FileUtil.getConfigXML(Config.path, "jedisClusterConfig.xml", JedisClusterConfig.class);
        final RedisPoolManager redisPoolManager = new RedisPoolManager(jpc);

        final String mongoUrl = nettyServerConfig.getMongo_connection_string();
        final String mongoData = nettyServerConfig.getMongo_read_write_database();
        DBConnectionMrg.getInstance().dBConnection(mongoUrl, mongoData);
        DataProxyMrg.getInstance().initMongoConfig();

        this.timerSystem.scheduleAtFixedDelay(0, 5 * TimeUtil.SEC, this::timerCheckHttpSession);
        this.timerSystem.scheduleAtFixedDelay(10, TimeUtil.MIN, () ->
                ScriptLoader.getInstance().consumerScript("DataDeleteScript", IDataDeleteScript::delete));
    }

    @Override
    protected void tickImpl(long curTime) throws Exception {
        timerSystem.tick();
    }

    @Override
    protected void onLogicEvent(LogicEvent evt) {
        switch (evt.getLogicEventType()) {
            case PROXYSERVER_ON_TCP_CONNECT: {
                proxyTcpServer.onIoSessionConnect(evt.getChannel());
                break;
            }
            case PROXYSERVER_ON_DISCONNECT: {
                proxyTcpServer.onIoSessionClosed(evt.getChannel());
                break;
            }
            case PROXYSERVER_MESSAGE_EVENT_S_RECV: {
                innerMessageHandler(evt);
                break;
            }
            case UDP_MESSAGE_EVENT_S_RECV: {
                ScriptLoader.getInstance().consumerScript("HttpLogicScript", (IHttpLogicScript script) ->
                        script.httpHandler(evt.getParamA(), evt.getParamB(), evt.getChannel()));
                break;
            }
            default: {
                throw new RuntimeException();
            }
        }
    }

    private void innerMessageHandler(LogicEvent event) {
        try {
            final int msgType = event.getIntParamA();
            final long id = event.getLongParamA();
            final int msgId = event.getIntParamB();
            final byte[] bytes = (byte[]) event.getParamA();
            if (msgType == MsgType.IDMESSAGE.getType()) {// 数据结构:msgId:pfbytes
                // 在本地注册，必须预处理
                final MessageBean messageBean = ScriptLoader.getInstance().getMessageBean(msgId);
                if (messageBean != null) {
                    final Message message = messageBean.buildMessage(bytes);
                    final TcpHandler handler = (TcpHandler) messageBean.newHandler();
                    if (handler != null) {
                        handler.setPid(id);
                        handler.setMsgBytes(bytes);
                        handler.setMessage(message);
                        handler.setSession(event.getChannel());
                        handler.run();
                    }
                } else {
                    LOGGER.warn("消息[{}]代码未实现逻辑", msgId);
                }
            } else {
                LOGGER.warn("消息类型{}未实现,玩家{}消息发送失败", msgType, id);
            }
        } catch (Exception e) {
            LOGGER.error("channelRead", e);
        }
    }

    private void timerCheckHttpSession() {
        try {
            final Map<Long, Channel> httpSessionMap = ServerMrg.getInstance().getHttpSessionMap();
            if (httpSessionMap.isEmpty()) {
                return;
            }
            httpSessionMap.values().removeIf(httpSession -> !httpSession.isActive());
        } catch (Exception e) {
            LOGGER.error("timerCheckHttpSession", e);
        }
    }
}
