package com.game.proxy.manager;

import com.game.engine.enums.ServerType;
import com.game.engine.struct.ServerInfo;

import java.util.*;

import com.game.engine.utils.MsgUtil;
import com.game.entity.AgentGameInfo;
import io.netty.channel.Channel;
import org.slf4j.LoggerFactory;
import org.slf4j.Logger;

/**
 * 服务器信息
 */
public class ServerMrg {

    private static final Logger log = LoggerFactory.getLogger(ServerMrg.class);
    private static final ServerMrg INSTANCE = new ServerMrg();

    /**
     * 服务器信息 serverId （登录，网关、大厅、充值）
     */
    private final Map<ServerType, Map<Integer, ServerInfo>> serversMap = new HashMap<>(5);

    private final Map<Integer, AgentGameInfo> gameInfoMap = new HashMap<>(8);

    private final Map<Long, Channel> httpSessionMap = new HashMap<>(8);

    public static ServerMrg getInstance() {
        return INSTANCE;
    }

    public ServerInfo getServerInfo(ServerType serverType, int serverId) {
        final Map<Integer, ServerInfo> serverMap = serversMap.get(serverType);
        if (serverMap != null) {
            return serverMap.get(serverId);
        }
        return null;
    }

    public Map<Integer, ServerInfo> getServersMap(ServerType serverType) {
        return serversMap.get(serverType);
    }

    public ServerInfo getServerType(ServerType serverType, long accountId, int hallServerId) {
        final Map<Integer, ServerInfo> serverInfoMap = getServersMap(serverType);
        if (serverInfoMap.isEmpty()) {
            return null;
        }

        final ServerInfo serverInfo = serverInfoMap.get(hallServerId);
        if (serverInfo != null) {
            return serverInfo;
        }

        final int index = (int) (accountId % serverInfoMap.size());
        final List<ServerInfo> serverInfos = new ArrayList<>(serverInfoMap.values());
        return serverInfos.get(index);
    }

    public void addServerInfo(ServerType serverType, ServerInfo serverInfo) {
        Map<Integer, ServerInfo> serverMap = serversMap.putIfAbsent(serverType, new TreeMap<>(Comparator.comparingInt(o -> o)));
        if (serverMap == null) {
            serverMap = serversMap.get(serverType);
        }
        serverMap.put(serverInfo.getId(), serverInfo);
    }

    public AgentGameInfo getGameInfo(int gameId) {
        AgentGameInfo gameInfo = this.gameInfoMap.putIfAbsent(gameId, new AgentGameInfo());
        if (gameInfo == null) {
            gameInfo = gameInfoMap.get(gameId);
            gameInfo.setGameId(gameId);
            gameInfo.setOnline(1);
        }
        return gameInfo;
    }

    public Map<Integer, AgentGameInfo> getGameInfoMap() {
        return gameInfoMap;
    }

    public Map<Long, Channel> getHttpSessionMap() {
        return httpSessionMap;
    }

    public void addHttpChannel(Channel session) {
        final long sessionID = MsgUtil.getSessionID(session);
        httpSessionMap.put(sessionID, session);
    }
}
