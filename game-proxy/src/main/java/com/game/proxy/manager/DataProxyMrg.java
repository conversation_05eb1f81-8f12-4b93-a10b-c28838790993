package com.game.proxy.manager;

import com.game.c_entity.merchant.C_MaintainNotice;
import com.game.c_entity.merchant.C_WebSiteInfo;
import com.game.c_entity.middleplatform.C_BaseMaintainNotice;
import com.game.c_entity.middleplatform.C_BaseMerchant;
import com.game.engine.io.conf.NettyServerConfig;
import com.game.engine.mongo.DBConnectionMrg;
import com.game.proxy.main.ProxyServer;
import com.mongodb.client.MongoClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;

import java.util.List;
import java.util.stream.Collectors;

public class DataProxyMrg {
    private static final Logger LOGGER = LoggerFactory.getLogger(DataProxyMrg.class);

    private static final DataProxyMrg instance = new DataProxyMrg();

    public static DataProxyMrg getInstance() {
        return instance;
    }

    static MongoTemplate mongoTemplate;

    public void initMongoConfig() {
        final NettyServerConfig nettyServerConfig = ProxyServer.getInstance().getNettyServerConfig();
        final String gameData = nettyServerConfig.getMongo_config_database();
        final MongoClient mongoClient = DBConnectionMrg.getInstance().getMongoClient();
        mongoTemplate = new MongoTemplate(mongoClient, gameData);
    }

    public List<String> loadAllMerchant() throws Exception {
        final Query query = new Query();
        query.addCriteria(Criteria.where("status").is(1)
                .and("deleteTime").is(0));
        //商户
        final List<C_BaseMerchant> c_baseMerchants = mongoTemplate.find(query, C_BaseMerchant.class);
        return c_baseMerchants.stream().map(C_BaseMerchant::getBusiness_no).collect(Collectors.toList());
    }

    public String findBusiness_no(String host) {
        final Query query = new Query();
        query.addCriteria(Criteria.where("status").is(1)
                .and("merchantDomain").is(host));
        final C_BaseMerchant c_baseMerchant = mongoTemplate.findOne(query, C_BaseMerchant.class);
        if (c_baseMerchant == null) {
            LOGGER.warn("c_baseMerchant，merchantDomain：{}，not exist", host);
            return "";
        }
        return c_baseMerchant.getBusiness_no();
    }

    public C_BaseMaintainNotice findBaseMaintainNotice() {
        //维护公告
        final Query query = new Query();
        query.addCriteria(Criteria.where("status").is(true));

        return mongoTemplate.findOne(query, C_BaseMaintainNotice.class);
    }

    public C_MaintainNotice findMaintainNotice(String business_no) {
        //维护公告
        final Query query = new Query();
        query.addCriteria(Criteria.where("business_no").is(business_no)
                .and("status").is(true));

        return mongoTemplate.findOne(query, C_MaintainNotice.class);
    }

    public C_WebSiteInfo findWebSiteInfo(String host) {
        final Query query = new Query();
        query.addCriteria(Criteria.where("domainName")
                .in(host));

        return mongoTemplate.findOne(query, C_WebSiteInfo.class);
    }

}
