<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <groupId>com.wingame</groupId>
    <artifactId>wingame</artifactId>
    <version>1.0-SNAPSHOT</version>
    <name>win_game</name>
    <packaging>pom</packaging>
    <modules>
        <module>game-message</module>
        <module>game-model</module>
        <module>game-proxy</module>
        <module>game-hall</module>
<!--        <module>game-gatesr</module>-->
        <module>game-loginsr</module>
        <module>game-billingsr</module>
        <module>game-agentGame</module>
        <module>game-agentGame-scripts</module>
    </modules>

    <properties>
        <maven.compiler.source>21</maven.compiler.source>
        <maven.compiler.target>21</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    </properties>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.10.1</version>
                <configuration>
                    <source>21</source>
                    <target>21</target>
                    <encoding>UTF-8</encoding>
                </configuration>
            </plugin>
        </plugins>
    </build>

    <profiles>
        <!--公共服务器节点-->
        <profile>
            <id>debug_local</id>
            <build>
                <filters>
                    <filter>
                        ../src/main/resources/Filter/LOCAL_PUBLIC.properties
                    </filter>
                </filters>

                <resources>
                    <!-- 过滤配置资源 -->
                    <resource>
                        <directory>src/main/resources/</directory>
                        <filtering>true</filtering>
                        <excludes> <!--不过滤的资源文件-->
                            <exclude>Filter/*</exclude>
                        </excludes>
                    </resource>
                </resources>
            </build>
        </profile>

        <profile>
            <id>debug_wan</id>
            <build>
                <filters>
                    <filter>
                        ../src/main/resources/Filter/LOCAL_WAN.properties
                    </filter>
                </filters>

                <resources>
                    <!-- 过滤配置资源 -->
                    <resource>
                        <directory>src/main/resources/</directory>
                        <filtering>true</filtering>
                        <excludes> <!--不过滤的资源文件-->
                            <exclude>Filter/*</exclude>
                        </excludes>
                    </resource>
                </resources>
            </build>
        </profile>

        <profile>
            <id>debug_hk_test</id>
            <build>
                <filters>
                    <filter>
                        ../src/main/resources/Filter/LOCAL_HK_TEST.properties
                    </filter>
                </filters>

                <resources>
                    <!-- 过滤配置资源 -->
                    <resource>
                        <directory>src/main/resources/</directory>
                        <filtering>true</filtering>
                        <excludes> <!--不过滤的资源文件-->
                            <exclude>Filter/*</exclude>
                        </excludes>
                    </resource>
                </resources>
            </build>
        </profile>

        <profile>
            <id>debug_hk_test_1</id>
            <build>
                <filters>
                    <filter>
                        ../src/main/resources/Filter/LOCAL_HK_TEST_1.properties
                    </filter>
                </filters>

                <resources>
                    <!-- 过滤配置资源 -->
                    <resource>
                        <directory>src/main/resources/</directory>
                        <filtering>true</filtering>
                        <excludes> <!--不过滤的资源文件-->
                            <exclude>Filter/*</exclude>
                        </excludes>
                    </resource>
                </resources>
            </build>
        </profile>

        <profile>
            <id>debug_formal</id>
            <build>
                <filters>
                    <filter>
                        ../src/main/resources/Filter/LOCAL_FORMAL.properties
                    </filter>
                </filters>

                <resources>
                    <!-- 过滤配置资源 -->
                    <resource>
                        <directory>src/main/resources/</directory>
                        <filtering>true</filtering>
                        <excludes> <!--不过滤的资源文件-->
                            <exclude>Filter/*</exclude>
                        </excludes>
                    </resource>
                </resources>
            </build>
        </profile>

        <profile>
            <id>debug_formal_1</id>
            <build>
                <filters>
                    <filter>
                        ../src/main/resources/Filter/LOCAL_FORMAL_1.properties
                    </filter>
                </filters>

                <resources>
                    <!-- 过滤配置资源 -->
                    <resource>
                        <directory>src/main/resources/</directory>
                        <filtering>true</filtering>
                        <excludes> <!--不过滤的资源文件-->
                            <exclude>Filter/*</exclude>
                        </excludes>
                    </resource>
                </resources>
            </build>
        </profile>
    </profiles>
</project>