#\u5916\u7F51ip
WWWIP=api.wgsaas.ai
WWWPORT=37376

#\u6743\u91CD \u503C\u8D8A\u5927\u4F18\u5148
POWER=0
#\u673A\u5668\u4EBA
openRobot=false
#\u670D\u52A1\u5668\u7248\u672C\u53F7
version=1.0
#\u540E\u53F0\u5730\u5740
backstageUrl=saascenter.wgsaas.ai
#\u4E09\u65B9\u6E38\u620F
agentGame=true
agentDebug=false
kWai=false

#\u7F51\u5173\u670D\u52A1\u5668\u6807\u8BC6
#GATEID=1000
#GATEHOST=***********
maxMsgSendTimes=100

#\u767B\u5F55\u670D\u52A1\u5668\u6807\u8BC6
LOGINID=2000
LOGINHOST=***********

#\u4EE3\u7406\u670D\u52A1\u5668\u6807\u8BC6
PROXYID=5000
PROXYHOST=***********

#\u5927\u5385\u670D\u52A1\u5668\u6807\u8BC6
HALLID=4000
HALLHOST=************

#\u5145\u503C\u670D\u52A1\u5668\u6807\u8BC6
BILLINGID=6000
BILLINGHOST=************

#\u6E38\u620F\u670D\u52A1\u5668\u6807\u8BC6
AGENTGAMEID=8000

#kafka
kafka.url=***********:9092

#redis
REDIS_IP_1=***********
REDIS_IP_2=***********
REDIS_IP_3=***********
port=26379
REDIS_PD=N1pU50xSoInFRGwmZ5Qhr5PWCzaT9fqU
#single sentinel
mode=sentinel

#mongodb
#mongo.url=*********************************************************************************************************
mongo.url=**************************************************************************************************************************
mongo.db=wingame_game
mongo.config=wingame_config

smtpHost=email-smtp.sa-east-1.amazonaws.com
smtpSender=<EMAIL>
smtpUserName=AKIA4SX7VAMAJ3YQKIFU
smtpPassWard=BFNOeukfBwGiJ78WMAlrNR0mnNbkT31GqBVhR3K0MWeS

#\u670D\u52A1\u5668debug\u72B6\u6001
debug=false

