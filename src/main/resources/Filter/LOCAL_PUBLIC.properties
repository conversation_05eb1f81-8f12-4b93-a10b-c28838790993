#\u5916\u7F51ip
WWWIP=api.cashcat.club
WWWPORT=37376

#\u6743\u91CD \u503C\u8D8A\u5927\u4F18\u5148
POWER=0
#\u673A\u5668\u4EBA
openRobot=false
#\u670D\u52A1\u5668\u7248\u672C\u53F7
version=1.1
#\u540E\u53F0\u5730\u5740
backstageUrl=************
#\u4E09\u65B9\u6E38\u620F
agentGame=false
agentDebug=true
kWai=true

#\u7F51\u5173\u670D\u52A1\u5668\u6807\u8BC6
GATEID=1000
GATEHOST=127.0.0.1
maxMsgSendTimes=100

INNER_HOST=127.0.0.1
#\u767B\u5F55\u670D\u52A1\u5668\u6807\u8BC6
LOGINID=2000
LOGINHOST=${INNER_HOST}

#\u4EE3\u7406\u670D\u52A1\u5668\u6807\u8BC6
PROXYID=3000
PROXYHOST=${INNER_HOST}

#\u5927\u5385\u670D\u52A1\u5668\u6807\u8BC6
HALLID=4000
HALLHOST=${INNER_HOST}

#\u4EE3\u7406\u670D\u52A1\u5668\u6807\u8BC6
BILLINGID=6000
BILLINGHOST=${INNER_HOST}

#\u4EE3\u7406\u6E38\u620F\u670D\u52A1\u5668\u6807\u8BC6
AGENTGAMEID=8000

#kafka
#kafka.url=
kafka.url=*************:19092

# redis \u7684ip
REDIS_IP_1=*************
REDIS_IP_2=
REDIS_PD=Hq3ke09Ks692gVZN
port=6379
#single sentinel
mode=single

#mongodb
mongo.url=***************************************************
mongo.db=wingame_game
mongo.config=wingame_config

#mailSmtp
smtpSender=<EMAIL>
smtpHost=smtp.larksuite.com
smtpPassWard=29wcz6aWnBSII0qG

#\u65E5\u5FD7\u7EA7\u522B ERROR\u3001WARN\u3001INFO\u3001DEBUG   DEBUG\u5C06\u663E\u793A\u6240\u6709
LOG_LEVEL=DEBUG,stdout
#\u670D\u52A1\u5668debug\u72B6\u6001
debug=true

