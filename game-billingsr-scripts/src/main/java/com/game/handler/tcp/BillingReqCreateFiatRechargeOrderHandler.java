package com.game.handler.tcp;

import com.game.billingsr.main.BillingServer;
import com.game.billingsr.manager.BillingMrg;
import com.game.dao.order.RechargeOrderDao;
import com.game.engine.io.handler.TcpHandler;
import com.game.engine.log.GameLog;
import com.game.engine.script.IHandlerEntity;
import com.game.engine.utils.JsonUtils;
import com.game.engine.utils.UniqueIDGenerator;
import com.game.entity.order.RechargeInfo;
import com.game.entity.order.RechargeOrder;
import com.game.entity.player.Player;
import com.game.enums.ErrorCode;
import com.game.enums.OrderType;
import com.game.manager.EntityDaoMrg;
import com.proto.BillingMessage;
import com.proto.CommonMessage;
import com.proto.MIDMessage;
import io.netty.util.internal.StringUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 法币的充值订单
 */
@IHandlerEntity(mid = MIDMessage.MID.ReqCreateRechargeOrder_VALUE, msg = BillingMessage.ReqCreateRechargeOrderMessage.class)
public class BillingReqCreateFiatRechargeOrderHandler extends TcpHandler {

    private static final Logger LOGGER = LoggerFactory.getLogger(BillingReqCreateFiatRechargeOrderHandler.class);

    @Override
    public void run() {
        final BillingMessage.ResCreateRechargeOrderMessage.Builder res = BillingMessage.ResCreateRechargeOrderMessage.newBuilder();
        res.setMsgID(MIDMessage.MID.ResCreateRechargeOrder_VALUE);
        try {
            final BillingMessage.ReqCreateRechargeOrderMessage req = (BillingMessage.ReqCreateRechargeOrderMessage) getMessage();
            final int currencyId = req.getCurrencyId();
            final int paymentMethod = req.getPaymentMethod();
            final double amount = req.getAmounts();
            final int channelId = req.getChannel();
            final BillingMessage.RechargeAccount rechargeMsg = req.getRechargeAccount();
            final CommonMessage.FbInfo fbInfo = req.getFbInfo();
            final CommonMessage.PlayerInfo playerInfo = req.getPlayerInfo();

            final RechargeInfo.RechargeAccount rechargeAccount = new RechargeInfo.RechargeAccount();
            rechargeAccount.setExtend(rechargeMsg.getExtend0());
            rechargeAccount.setExtend_1(rechargeMsg.getExtend1());
            rechargeAccount.setExtend_2(rechargeMsg.getExtend2());
            rechargeAccount.setExtend_3(rechargeMsg.getExtend3());

            final UniqueIDGenerator uniqueID = BillingServer.getInstance().getUniqueIDGenerator();
            final RechargeOrder rechargeOrder = new RechargeOrder(uniqueID.nextId());

            final Player player = new Player(pid);
            player.setBusiness_no(playerInfo.getBusinessNo());
            BillingMrg.getInstance().createOrderInfo(player, rechargeOrder, OrderType.RECHARGE, currencyId, amount);
            rechargeOrder.setHallId(playerInfo.getHallId());
            rechargeOrder.setPaymentMethod(paymentMethod);
            rechargeOrder.setRechargeAccount(JsonUtils.writeAsJson(rechargeAccount));
            rechargeOrder.setEvent_name(StringUtil.isNullOrEmpty(fbInfo.getEventName()) ? "" : fbInfo.getEventName());
            rechargeOrder.setFbToken(StringUtil.isNullOrEmpty(fbInfo.getFbToken()) ? "" : fbInfo.getFbToken());
            rechargeOrder.setPixelId(StringUtil.isNullOrEmpty(fbInfo.getPixelId()) ? "" : fbInfo.getPixelId());

            BillingMrg.getInstance().addOrderInfo(rechargeOrder);
            EntityDaoMrg.getInstance().getDao(RechargeOrderDao.class).insert(rechargeOrder);

            final GameLog gameLog = BillingMrg.newGameLog("platform_rechargeOrder", rechargeOrder)
                    .append("site", playerInfo.getSite())
                    .append("region", playerInfo.getRegisterRegion())
                    .append("agentId", playerInfo.getAgentId())
                    .append("channelId", playerInfo.getChannelId())
                    .append("mediaId", playerInfo.getMediaId())
                    .append("adId", playerInfo.getAdId())
                    .append("channel", playerInfo.getChannel())
                    .append("regChannel", playerInfo.getChannelsList().isEmpty() ? 0 : playerInfo.getChannelsList().getFirst())
                    .append("udpSessionId", udpSessionId)
                    .append("totalBetTimes", playerInfo.getTotalBetTimes())
                    .append("paymentMethod", paymentMethod)
                    .append("rechargeChannel", channelId)
                    .append("rechargeAccount", JsonUtils.writeAsJson(rechargeAccount))
                    .append("paymentSource", req.getPaymentSource())
                    .append("device", playerInfo.getDevice())
                    .append("model", playerInfo.getModel())
                    .append("beforeBalance", req.getBeforeBalance());
            BillingServer.getInstance().getLogProducerMrg().send(gameLog);

//            BillingMrg.getInstance().getUdpSessionId().put(pid, udpSessionId);
            LOGGER.info("playerId：{}，create orderId：{}，success", pid, rechargeOrder.getOrderId());
        } catch (Exception e) {
            LOGGER.error("ReqCreateRechargeOrderHandler", e);
            res.setError(ErrorCode.Internal_Server_Error.getCode());
            replyWithUdpSessionId(res.build());
        }
    }

}
