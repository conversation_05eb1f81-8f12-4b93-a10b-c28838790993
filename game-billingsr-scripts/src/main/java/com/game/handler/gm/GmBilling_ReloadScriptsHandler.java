package com.game.handler.gm;

import com.game.engine.io.handler.HttpHandler;
import com.game.engine.script.IHandlerEntity;
import com.game.engine.script.ScriptLoader;
import com.game.engine.utils.MsgUtil;
import com.game.engine.utils.Symbol;
import com.game.enums.ErrorCode;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.HashMap;
import java.util.Map;

//http://127.0.0.1:8680/gmBilling/reloadScripts?scriptsName=ReqDepositDataHandler
@IHandlerEntity(path = "/gmBilling/reloadScripts", desc = "重新加载脚本")
public class GmBilling_ReloadScriptsHandler extends HttpHandler {
    private static Logger logger = LoggerFactory.getLogger(GmBilling_ReloadScriptsHandler.class);

    @Override
    public void run() {
        try {
            final Map<String, Object> paramsMap = getParamsMap();
            final String scriptsName = (String) paramsMap.get("scriptsName");
            final String[] scriptsNames = scriptsName.split(Symbol.DOUHAO_REG);
            final String sourceDir = ScriptLoader.getInstance().getSourceDir();
            final String data = ScriptLoader.getInstance().loadJava(sourceDir, scriptsNames);

            final Map<String, Object> res = new HashMap<>();
            res.put("error", ErrorCode.Success.getCode());
            res.put("data", data);
            response(res);
        } catch (Exception e) {
            logger.error("GmBilling_ReloadScriptsHandler", e);
            MsgUtil.responseHttp(ErrorCode.Internal_Server_Error.getCode(), session);
        }
    }

}
