//package com.game.handler.gmtest;
//
//
//import com.game.billingsr.main.BillingServer;
//import com.game.billingsr.manager.BillingMrg;
//import com.game.dao.order.WithdrawOrderDao;
//import com.game.dao.player.promote.CommissionRewardsNoteDao;
//import com.game.dao.player.promote.ReferralRewardsNoteDao;
//import com.game.engine.io.handler.HttpHandler;
//import com.game.engine.math.BigDecimalUtils;
//import com.game.engine.math.MathUtils;
//import com.game.engine.script.IHandlerEntity;
//import com.game.engine.utils.MsgUtil;
//import com.game.engine.utils.UniqueIDGenerator;
//import com.game.entity.player.promote.CommissionRewardsNote;
//import com.game.entity.player.promote.ReferralRewardsNote;
//import com.game.enums.ErrorCode;
//import com.game.enums.OrderType;
//import com.game.manager.EntityDaoMrg;
//import org.slf4j.Logger;
//import org.slf4j.LoggerFactory;
//
//import java.util.Arrays;
//import java.util.List;
//
////http://127.0.0.1:8680/hall/test/createRewards?playerId=200100016
//@IHandlerEntity(path = "/hall/test/createRewards", desc = "")
//public class Billing_CreateRewardsHandler extends HttpHandler {
//    private static final Logger LOGGER = LoggerFactory.getLogger(Billing_CreateRewardsHandler.class);
//
//    @Override
//    public void run() {
//        try {
//            final String playerId = (String) paramsMap.get("playerId");
//
//            //充值
//            final UniqueIDGenerator uniqueID = BillingServer.getInstance().getUniqueIDGenerator();
//            List<Integer> currencys = Arrays.asList(1000, 1001, 1002, 2000);
//            for (int i = 0; i < 10; i++) {
//                final int currencyId = MathUtils.random(currencys);
//                final CommissionRewardsNote commissionRewardsNote = new CommissionRewardsNote(uniqueID.nextId());
//                commissionRewardsNote.setCurrencyId(currencyId);
//                commissionRewardsNote.setAmount(BigDecimalUtils.round(MathUtils.random(1.0d, 10.0d), 2));
//                commissionRewardsNote.setBusiness_no("39bac42a");
//                commissionRewardsNote.setPlayerId(Long.parseLong(playerId));
//                commissionRewardsNote.setStatus(1);
//                EntityDaoMrg.getInstance().getDao(CommissionRewardsNoteDao.class).insert(commissionRewardsNote);
//            }
//
//            for (int i = 0; i < 10; i++) {
//                final ReferralRewardsNote referralRewardsNote = new ReferralRewardsNote(uniqueID.nextId());
//                referralRewardsNote.setAmount(BigDecimalUtils.round(MathUtils.random(1.0d, 10.0d), 2));
//                referralRewardsNote.setBusiness_no("39bac42a");
//                referralRewardsNote.setPlayerId(Long.parseLong(playerId));
//                referralRewardsNote.setStatus(1);
//                EntityDaoMrg.getInstance().getDao(ReferralRewardsNoteDao.class).insert(referralRewardsNote);
//            }
//
//            MsgUtil.responseHttp(ErrorCode.Success.getCode(), session);
//        } catch (Exception e) {
//            LOGGER.error("", e);
//            MsgUtil.responseHttp(ErrorCode.Internal_Server_Error.getCode(), session);
//        }
//    }
//}
