//package com.game.handler.gmtest;
//
//
//import com.game.billingsr.main.BillingServer;
//import com.game.billingsr.manager.BillingMrg;
//import com.game.dao.order.WithdrawOrderDao;
//import com.game.dao.player.promote.CommissionRewardsNoteDao;
//import com.game.dao.player.promote.ReferralRewardsNoteDao;
//import com.game.engine.io.handler.HttpHandler;
//import com.game.engine.io.redis.RedisPoolManager;
//import com.game.engine.log.GameLog;
//import com.game.engine.math.BigDecimalUtils;
//import com.game.engine.mongo.DBConnectionMrg;
//import com.game.engine.script.IHandlerEntity;
//import com.game.engine.util.MathUtils;
//import com.game.engine.util.misc.Tuple2;
//import com.game.engine.utils.JsonUtils;
//import com.game.engine.utils.MsgUtil;
//import com.game.engine.utils.TimeUtil;
//import com.game.engine.utils.UniqueIDGenerator;
//import com.game.entity.game.GameNote;
//import com.game.entity.game.GameNoteFields;
//import com.game.entity.order.RechargeOrder;
//import com.game.entity.order.WithdrawInfo;
//import com.game.entity.order.WithdrawOrder;
//import com.game.entity.order.WithdrawOrderFields;
//import com.game.entity.player.Player;
//import com.game.entity.player.promote.CommissionRewardsNote;
//import com.game.entity.player.promote.ReferralRewardsNote;
//import com.game.entity.player.stats.Stats;
//import com.game.enums.ErrorCode;
//import com.game.enums.OrderType;
//import com.game.enums.redis.RedisHall;
//import com.game.enums.redis.RedisRanking;
//import com.game.manager.EntityDaoMrg;
//import com.mongodb.client.AggregateIterable;
//import com.mongodb.client.MongoCollection;
//import com.mongodb.client.model.Accumulators;
//import com.mongodb.client.model.Aggregates;
//import com.mongodb.client.model.Filters;
//import io.netty.util.internal.StringUtil;
//import org.bson.conversions.Bson;
//import org.slf4j.Logger;
//import org.slf4j.LoggerFactory;
//import org.springframework.data.domain.Sort;
//import org.springframework.data.mongodb.core.MongoTemplate;
//import org.springframework.data.mongodb.core.query.Criteria;
//import org.springframework.data.mongodb.core.query.Query;
//
//import java.util.Arrays;
//import java.util.List;
//
////http://127.0.0.1:8680/hall/test/queryOrder?playerId=200100017&currencyId=1000
//@IHandlerEntity(path = "/hall/test/queryOrder", desc = "")
//public class Billing_QueryHandler extends HttpHandler {
//    private static final Logger LOGGER = LoggerFactory.getLogger(Billing_QueryHandler.class);
//
//    @Override
//    public void run() {
//        try {
//            final String playerId = (String) paramsMap.get("playerId");
//            final String currencyId = (String) paramsMap.get("currencyId");
//
//            final Player player = BillingMrg.getInstance().findDbPlayer(Long.parseLong(playerId));
//
//            final UniqueIDGenerator uniqueID = BillingServer.getInstance().getUniqueIDGenerator();
//            for (int i = 0; i < 50; i++) {
//                recharge(player, uniqueID, Integer.parseInt(currencyId));
//            }
//
//            MsgUtil.responseHttp(ErrorCode.Success.getCode(), session);
//        } catch (Exception e) {
//            LOGGER.error("", e);
//            MsgUtil.responseHttp(ErrorCode.Internal_Server_Error.getCode(), session);
//        }
//    }
//
//    private void withdraw(Player player, UniqueIDGenerator uniqueID, int currencyId) {
//        final WithdrawInfo.WithdrawAccount withdrawAccount = new WithdrawInfo.WithdrawAccount();
//        final WithdrawInfo withdrawInfo = new WithdrawInfo();
//        withdrawInfo.setFee(MathUtils.random(1, 3));
//        withdrawInfo.setActualAmount(BigDecimalUtils.sub(MathUtils.random(30, 100), withdrawInfo.getFee(), 0));
//
//        final WithdrawOrder withdrawOrder = new WithdrawOrder(uniqueID.nextId());
//        BillingMrg.getInstance().createOrderInfo(player, withdrawOrder, OrderType.WITHDRAW, currencyId, withdrawInfo.getActualAmount() + withdrawInfo.getFee());
//        withdrawOrder.setWithdrawAccount(JsonUtils.writeAsJson(withdrawAccount));
//        withdrawOrder.setWithdrawInfo(JsonUtils.writeAsJson(withdrawInfo));
//
//        BillingMrg.getInstance().addOrderInfo(withdrawOrder);
//        EntityDaoMrg.getInstance().getDao(WithdrawOrderDao.class).insert(withdrawOrder);
//
//        final GameLog gameLog = BillingMrg.newGameLog("platform_withdrawOrder", withdrawOrder);
//        gameLog.append("paymentMethod", 1)
//                .append("balance", MathUtils.random(100, 200))
//                .append("withdrawAccount", withdrawOrder.getWithdrawAccount())
//                .append("withdrawInfo", withdrawOrder.getWithdrawInfo());
//
//        final WithdrawInfo.RechargeCondition rechargeCondition = new WithdrawInfo.RechargeCondition();
//        gameLog.append("rechargeTimes", JsonUtils.writeAsJson(rechargeCondition));
//
//        final WithdrawInfo.BetCondition betCondition = new WithdrawInfo.BetCondition();
//        gameLog.append("betTimesCondition", JsonUtils.writeAsJson(betCondition));
//
//        final WithdrawInfo.CodingCondition codingCondition = new WithdrawInfo.CodingCondition();
//        gameLog.append("washCodeCondition", JsonUtils.writeAsJson(codingCondition));
//
//        final WithdrawInfo.WithdrawCondition withdrawCondition = new WithdrawInfo.WithdrawCondition();
//        gameLog.append("withdrawCondition", JsonUtils.writeAsJson(withdrawCondition));
//        BillingServer.getInstance().getLogProducerMrg().send(gameLog);
//    }
//
//    private void recharge(Player player, UniqueIDGenerator uniqueID, int currencyId) {
//        final RechargeOrder rechargeOrder = new RechargeOrder(uniqueID.nextId());
//        BillingMrg.getInstance().createOrderInfo(player, rechargeOrder, OrderType.RECHARGE, currencyId, MathUtils.random(20, 100));
//        BillingMrg.getInstance().addOrderInfo(rechargeOrder);
//
//        final GameLog gameLog = BillingMrg.newGameLog("platform_rechargeOrder", rechargeOrder)
//                .append("region", StringUtil.isNullOrEmpty(player.getRegion()) ? "" : player.getRegion())
//                .append("paymentMethod", 1);
//        gameLog.append("totalBetTimes", MathUtils.random(10, 50));
//        BillingServer.getInstance().getLogProducerMrg().send(gameLog);
//    }
//}
