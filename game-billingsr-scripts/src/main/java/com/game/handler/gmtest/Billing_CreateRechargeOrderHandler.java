//package com.game.handler.gmtest;
//
//
//import com.game.billingsr.main.BillingServer;
//import com.game.billingsr.manager.BillingMrg;
//import com.game.dao.order.RechargeOrderDao;
//import com.game.dao.player.promote.CommissionRewardsNoteDao;
//import com.game.dao.player.promote.ReferralRewardsNoteDao;
//import com.game.engine.io.handler.HttpHandler;
//import com.game.engine.log.GameLog;
//import com.game.engine.math.BigDecimalUtils;
//import com.game.engine.math.MathUtils;
//import com.game.engine.script.IHandlerEntity;
//import com.game.engine.utils.MsgUtil;
//import com.game.engine.utils.UniqueIDGenerator;
//import com.game.entity.order.RechargeOrder;
//import com.game.entity.player.Player;
//import com.game.entity.player.promote.CommissionRewardsNote;
//import com.game.entity.player.promote.ReferralRewardsNote;
//import com.game.entity.player.stats.Stats;
//import com.game.enums.ErrorCode;
//import com.game.enums.OrderType;
//import com.game.manager.EntityDaoMrg;
//import io.netty.util.internal.StringUtil;
//import org.slf4j.Logger;
//import org.slf4j.LoggerFactory;
//
//import java.util.Arrays;
//import java.util.List;
//
////http://127.0.0.1:8680/billing/test/createRechargeOrder?playerId=200100016&currencyId=1000&&amount=100
//@IHandlerEntity(path = "/billing/test/createRechargeOrder", desc = "")
//public class Billing_CreateRechargeOrderHandler extends HttpHandler {
//    private static final Logger LOGGER = LoggerFactory.getLogger(Billing_CreateRechargeOrderHandler.class);
//
//    @Override
//    public void run() {
//        try {
//            final String playerId = (String) paramsMap.get("playerId");
//            final String currencyId = (String) paramsMap.get("currencyId");
//            final String amount = (String) paramsMap.get("amount");
//
//            final Player player = BillingMrg.getInstance().findDbPlayer(Long.parseLong(playerId));
//
//            //充值
//            final UniqueIDGenerator uniqueID = BillingServer.getInstance().getUniqueIDGenerator();
//            final RechargeOrder rechargeOrder = new RechargeOrder(uniqueID.nextId());
//            BillingMrg.getInstance().createOrderInfo(player, rechargeOrder, OrderType.RECHARGE, Integer.parseInt(currencyId), Double.parseDouble(amount));
//            BillingMrg.getInstance().addOrderInfo(rechargeOrder);
//
//            EntityDaoMrg.getInstance().getDao(RechargeOrderDao.class).insert(rechargeOrder);
//
//            final GameLog gameLog = BillingMrg.newGameLog("platform_rechargeOrder", rechargeOrder)
//                    .append("region", StringUtil.isNullOrEmpty(player.getRegion()) ? "" : player.getRegion())
//                    .append("paymentMethod", "101");
//            BillingServer.getInstance().getLogProducerMrg().send(gameLog);
//
//            BillingMrg.getInstance().getUdpSessionId().put(Long.parseLong(playerId), 1000);
//
//            MsgUtil.responseHttp(ErrorCode.Success.getCode(), session);
//        } catch (Exception e) {
//            LOGGER.error("", e);
//            MsgUtil.responseHttp(ErrorCode.Internal_Server_Error.getCode(), session);
//        }
//    }
//}
