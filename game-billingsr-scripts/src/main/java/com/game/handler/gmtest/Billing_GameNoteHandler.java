//package com.game.handler.gmtest;
//
//
//import com.game.dao.game.GameNoteDao;
//import com.game.engine.io.handler.HttpHandler;
//import com.game.engine.math.MathUtils;
//import com.game.engine.script.IHandlerEntity;
//import com.game.engine.utils.MsgUtil;
//import com.game.engine.utils.TimeUtil;
//import com.game.engine.utils.UniqueIDGenerator;
//import com.game.entity.game.GameNote;
//import com.game.enums.ErrorCode;
//import com.game.manager.EntityDaoMrg;
//import org.apache.poi.ss.formula.functions.T;
//import org.slf4j.Logger;
//import org.slf4j.LoggerFactory;
//
//import java.util.ArrayList;
//import java.util.Arrays;
//import java.util.List;
//
////http://127.0.0.1:8680/hall/test/gameNote?playerId=200100011
//@IHandlerEntity(path = "/hall/test/gameNote", desc = "")
//public class Billing_GameNoteHandler extends HttpHandler {
//    private static final Logger LOGGER = LoggerFactory.getLogger(Billing_GameNoteHandler.class);
//
//    @Override
//    public void run() {
//        try {
//            final String playerId = (String) paramsMap.get("playerId");
//
//            //充值
//            final UniqueIDGenerator uniqueIDGenerator = new UniqueIDGenerator(1, 1);
//            List<Integer> currencys = Arrays.asList(1000, 1001, 1002, 2000);
//            List<String> gameNames = Arrays.asList("Mines", "Dice", "Limo");
//            List<Integer> gameTypes = Arrays.asList(1, 2, 3, 4, 5, 6, 7);
//            List<GameNote> gameNoteList = new ArrayList<>();
//            long start = TimeUtil.currentTimeMillis();
//            for (int i = 0; i < 150; i++) {
//                final GameNote gameNote = new GameNote(uniqueIDGenerator.nextId());
//                final String gameName = MathUtils.random(gameNames);
//                gameNote.setPlayerId(Long.parseLong(playerId));
//                gameNote.setGameName(gameName);
//                gameNote.setAmount(MathUtils.random(100, 300));
//                final int currencyId = MathUtils.random(currencys);
//                gameNote.setCurrencyId(currencyId);
//                gameNote.setBusiness_no("39bac42a");
//                gameNote.setCreateTime(TimeUtil.currentTimeMillis());
//                gameNote.setCreateTime(TimeUtil.currentTimeMillis());
//                final int gameType = MathUtils.random(gameTypes);
//                gameNote.setGameType(gameType);
//                gameNote.setPayout(MathUtils.random(1, 10));
//                gameNote.setProfit(MathUtils.random(-10, 10));
//                if (gameNote.getProfit() > 0) {
//                    gameNote.setProfit(MathUtils.random(100, 200));
//                } else {
//                    gameNote.setProfit(0);
//                }
//                gameNoteList.add(gameNote);
//            }
//            LOGGER.warn("cost time：{}", TimeUtil.currentTimeMillis() - start);
//            MsgUtil.responseHttp(ErrorCode.Success.getCode(), session);
//        } catch (Exception e) {
//            LOGGER.error("", e);
//            MsgUtil.responseHttp(ErrorCode.Internal_Server_Error.getCode(), session);
//        }
//    }
//}
