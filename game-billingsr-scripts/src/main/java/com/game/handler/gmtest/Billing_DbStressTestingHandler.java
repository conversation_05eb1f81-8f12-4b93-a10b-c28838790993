//package com.game.handler.gmtest;
//
//
//import com.game.billingsr.main.BillingServer;
//import com.game.billingsr.manager.BillingMrg;
//import com.game.dao.order.WithdrawOrderDao;
//import com.game.dao.player.PlayerDao;
//import com.game.engine.io.handler.HttpHandler;
//import com.game.engine.log.GameLog;
//import com.game.engine.math.BigDecimalUtils;
//import com.game.engine.mongo.DBHandlerMrg;
//import com.game.engine.script.IHandlerEntity;
//import com.game.engine.util.MathUtils;
//import com.game.engine.utils.JsonUtils;
//import com.game.engine.utils.MsgUtil;
//import com.game.engine.utils.TimeUtil;
//import com.game.engine.utils.UniqueIDGenerator;
//import com.game.entity.order.RechargeOrder;
//import com.game.entity.order.WithdrawInfo;
//import com.game.entity.order.WithdrawOrder;
//import com.game.entity.player.Player;
//import com.game.entity.player.PlayerFields;
//import com.game.enums.ErrorCode;
//import com.game.enums.OrderType;
//import com.game.manager.EntityDaoMrg;
//import io.netty.util.internal.StringUtil;
//import it.unimi.dsi.fastutil.ints.IntLists;
//import org.slf4j.Logger;
//import org.slf4j.LoggerFactory;
//
////http://127.0.0.1:8680/hall/test/dbStressTesting?default=true
//@IHandlerEntity(path = "/hall/test/dbStressTesting", desc = "")
//public class Billing_DbStressTestingHandler extends HttpHandler {
//    private static final Logger LOGGER = LoggerFactory.getLogger(Billing_DbStressTestingHandler.class);
//
//    @Override
//    public void run() {
//        try {
//            final Player player = DBHandlerMrg.getInstance().getDBHandler(Player.class).tongbuQueryEntity(PlayerFields.playerId, 200100014L);
//
//            while (true) {
//                if (TimeUtil.getSecond() % 5 != 0) {
//                    continue;
//                }
//                for (int i = 0; i < 5000; i++) {
//                    player.getCurrencyMap().put(1000, MathUtils.random(100d, 500d));
//                    EntityDaoMrg.getInstance().getDao(PlayerDao.class).updateCurrency(player, IntLists.singleton(1000));
//                }
//                LOGGER.warn("更新数据库。。。");
//            }
//
////            MsgUtil.responseHttp(ErrorCode.Success.getCode(), session);
//        } catch (Exception e) {
//            LOGGER.error("", e);
//            MsgUtil.responseHttp(ErrorCode.Internal_Server_Error.getCode(), session);
//        }
//    }
//
//    private void withdraw(Player player, UniqueIDGenerator uniqueID, int currencyId) {
//        final WithdrawInfo.WithdrawAccount withdrawAccount = new WithdrawInfo.WithdrawAccount();
//        final WithdrawInfo withdrawInfo = new WithdrawInfo();
//        withdrawInfo.setFee(MathUtils.random(1, 3));
//        withdrawInfo.setActualAmount(BigDecimalUtils.sub(MathUtils.random(30, 100), withdrawInfo.getFee(), 0));
//
//        final WithdrawOrder withdrawOrder = new WithdrawOrder(uniqueID.nextId());
//        BillingMrg.getInstance().createOrderInfo(player, withdrawOrder, OrderType.WITHDRAW, currencyId, withdrawInfo.getActualAmount() + withdrawInfo.getFee());
//        withdrawOrder.setWithdrawAccount(JsonUtils.writeAsJson(withdrawAccount));
//        withdrawOrder.setWithdrawInfo(JsonUtils.writeAsJson(withdrawInfo));
//
//        BillingMrg.getInstance().addOrderInfo(withdrawOrder);
//        EntityDaoMrg.getInstance().getDao(WithdrawOrderDao.class).insert(withdrawOrder);
//
//        final GameLog gameLog = BillingMrg.newGameLog("platform_withdrawOrder", withdrawOrder);
//        gameLog.append("paymentMethod", 1)
//                .append("balance", MathUtils.random(100, 200))
//                .append("withdrawAccount", withdrawOrder.getWithdrawAccount())
//                .append("withdrawInfo", withdrawOrder.getWithdrawInfo());
//
//        final WithdrawInfo.RechargeCondition rechargeCondition = new WithdrawInfo.RechargeCondition();
//        gameLog.append("rechargeTimes", JsonUtils.writeAsJson(rechargeCondition));
//
//        final WithdrawInfo.BetCondition betCondition = new WithdrawInfo.BetCondition();
//        gameLog.append("betTimesCondition", JsonUtils.writeAsJson(betCondition));
//
//        final WithdrawInfo.CodingCondition codingCondition = new WithdrawInfo.CodingCondition();
//        gameLog.append("washCodeCondition", JsonUtils.writeAsJson(codingCondition));
//
//        final WithdrawInfo.WithdrawCondition withdrawCondition = new WithdrawInfo.WithdrawCondition();
//        gameLog.append("withdrawCondition", JsonUtils.writeAsJson(withdrawCondition));
//        BillingServer.getInstance().getLogProducerMrg().send(gameLog);
//    }
//
//    private void recharge(Player player, UniqueIDGenerator uniqueID, int currencyId) {
//        final RechargeOrder rechargeOrder = new RechargeOrder(uniqueID.nextId());
//        BillingMrg.getInstance().createOrderInfo(player, rechargeOrder, OrderType.RECHARGE, currencyId, MathUtils.random(20, 100));
//        BillingMrg.getInstance().addOrderInfo(rechargeOrder);
//
//        final GameLog gameLog = BillingMrg.newGameLog("platform_rechargeOrder", rechargeOrder)
//                .append("region", StringUtil.isNullOrEmpty(player.getRegion()) ? "" : player.getRegion())
//                .append("paymentMethod", 1);
//        gameLog.append("totalBetTimes", MathUtils.random(10, 50));
//        BillingServer.getInstance().getLogProducerMrg().send(gameLog);
//    }
//}
