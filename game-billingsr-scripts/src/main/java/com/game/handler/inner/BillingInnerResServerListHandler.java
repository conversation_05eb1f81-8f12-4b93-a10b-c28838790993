package com.game.handler.inner;

import com.game.billingsr.manager.ServerMrg;
import com.game.engine.enums.ServerType;
import com.game.engine.io.handler.TcpHandler;
import com.game.engine.script.IHandlerEntity;
import com.game.engine.struct.ServerInfo;
import com.proto.InnerMessage;
import com.proto.MIDMessage;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.*;

@IHandlerEntity(mid = MIDMessage.MID.InnerResServerList_VALUE, msg = InnerMessage.InnerResServerListMessage.class)
public class BillingInnerResServerListHandler extends TcpHandler {
    private static final Logger LOGGER = LoggerFactory.getLogger(BillingInnerResServerListHandler.class);

    @Override
    public void run() {
        try {
            final InnerMessage.InnerResServerListMessage resMessage = (InnerMessage.InnerResServerListMessage) getMessage();
            final List<InnerMessage.ServerInfoList> serverList = resMessage.getServerListList();
            for (InnerMessage.ServerInfoList serverInfoList : serverList) {
                if (serverInfoList.getType() == ServerType.HALL.getType()) {
                    final Set<Integer> serverIds = new HashSet<>();
                    for (InnerMessage.InnerServerInfo s : serverInfoList.getServerInfosList()) {
                        ServerMrg.getInstance().updateServer(s);
                        serverIds.add(s.getId());
                    }

                    //移除关闭的hall
                    final Map<Integer, ServerInfo> hallServers = ServerMrg.getInstance().getServersMap(ServerType.HALL);
                    if (hallServers.size() != serverInfoList.getServerInfosList().size()) {
                        final ArrayList<Integer> ids = new ArrayList<>(hallServers.keySet());
                        ids.removeAll(serverIds);
                        for (int id : ids) {
                            hallServers.remove(id);
                        }
                    }
                }
            }

        } catch (Exception e) {
            LOGGER.error("BillingInnerResServerListHandler ", e);
        }
    }

}
