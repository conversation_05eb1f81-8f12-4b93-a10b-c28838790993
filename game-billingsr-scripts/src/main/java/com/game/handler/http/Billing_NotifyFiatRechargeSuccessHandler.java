package com.game.handler.http;

import com.game.billingsr.main.BillingServer;
import com.game.billingsr.manager.BillingMrg;
import com.game.billingsr.manager.ServerMrg;
import com.game.dao.order.RechargeOrderDao;
import com.game.engine.enums.ServerType;
import com.game.engine.io.handler.HttpHandler;
import com.game.engine.script.IHandlerEntity;
import com.game.engine.struct.ServerInfo;
import com.game.engine.utils.MsgUtil;
import com.game.engine.utils.TimeUtil;
import com.game.entity.order.Order;
import com.game.entity.order.RechargeOrderFields;
import com.game.enums.ErrorCode;
import com.game.manager.EntityDaoMrg;
import com.proto.CommonMessage;
import com.proto.InnerMessage;
import com.proto.MIDMessage;
import io.netty.util.internal.StringUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.mongodb.core.query.Update;

//http://127.0.0.1:8680/billing/notifyFiatRechargeSuccess?playerId=200100011&orderId=844536599725080577&status=2
@IHandlerEntity(path = "/billing/notifyFiatRechargeSuccess", desc = "通知充值成功")
public class Billing_NotifyFiatRechargeSuccessHandler extends HttpHandler {
    private static final Logger LOGGER = LoggerFactory.getLogger(Billing_NotifyFiatRechargeSuccessHandler.class);

    @Override
    public void run() {
        final String playerId = (String) paramsMap.get("playerId");
        final String orderId = (String) paramsMap.get("orderId");
        final String status = (String) paramsMap.get("status");
        BillingServer.getInstance().asyncExecute(Long.parseLong(orderId), () -> {
            try {
                if (StringUtil.isNullOrEmpty(playerId) || StringUtil.isNullOrEmpty(orderId)
                        || StringUtil.isNullOrEmpty(status)) {
                    MsgUtil.responseHttp(ErrorCode.Parameter_Error.getCode(), session);
                    return;
                }

                final Order rechargeOrder = BillingMrg.getInstance().getRechargeOrder(Long.parseLong(orderId));
                if (rechargeOrder == null) {
                    LOGGER.warn("playerId：{}，orderId：{}，not exist", playerId, orderId);
                    MsgUtil.responseHttp(ErrorCode.OrderId_Not_Exist.getCode(), session);
                    return;
                }

                final ServerInfo hallServer = ServerMrg.getInstance().getServerType(ServerType.HALL, Long.parseLong(playerId), rechargeOrder.getHallId());
                if (hallServer == null) {
                    LOGGER.warn("serverId：{}，not exist", rechargeOrder.getHallId());
                    MsgUtil.responseHttp(ErrorCode.Internal_Server_Error.getCode(), session);
                    return;
                }

                if (rechargeOrder.getStatus() == Integer.parseInt(status)) {
                    MsgUtil.responseHttp(ErrorCode.Repeated_Recharge.getCode(), session);
                    return;
                }

                MsgUtil.responseHttp(ErrorCode.Success.getCode(), session);

                rechargeOrder.setStatus(Integer.parseInt(status));
                rechargeOrder.setPayEndTime(TimeUtil.currentTimeMillis());
                final Update update = new Update();
                update.set(RechargeOrderFields.status, rechargeOrder.getStatus())
                        .set(RechargeOrderFields.payEndTime, rechargeOrder.getPayEndTime());
                EntityDaoMrg.getInstance().getDao(RechargeOrderDao.class).updateOrder(rechargeOrder.getOrderId(), update);
                BillingMrg.getInstance().removeOrderInfo(Long.parseLong(orderId));

                final InnerMessage.InnerNotifyRechargeMessage.Builder notifyRecharge = InnerMessage.InnerNotifyRechargeMessage.newBuilder();
                notifyRecharge.setMsgID(MIDMessage.MID.InnerNotifyRecharge_VALUE)
                        .setCurrencyId(rechargeOrder.getCurrencyId())
                        .setAmount(rechargeOrder.getAmounts())
                        .setOrderId(rechargeOrder.getOrderId())
                        .setStatus(Integer.parseInt(status))
                        .setFbInfo(buildFbInfo(rechargeOrder));
                MsgUtil.sendInnerMsg(hallServer.getActiveSession(), notifyRecharge.build(), Long.parseLong(playerId));
            } catch (Exception e) {
                LOGGER.error("Billing_NotifyRechargeSuccessHandler", e);
                MsgUtil.responseHttp(ErrorCode.Internal_Server_Error.getCode(), session);
            }
        });
    }

    private CommonMessage.FbInfo buildFbInfo(Order rechargeOrder) {
        final CommonMessage.FbInfo.Builder fbInfo = CommonMessage.FbInfo.newBuilder();
        fbInfo.setEventName(StringUtil.isNullOrEmpty(rechargeOrder.getEvent_name()) ? "" : rechargeOrder.getEvent_name())
                .setFbToken(StringUtil.isNullOrEmpty(rechargeOrder.getFbToken()) ? "" : rechargeOrder.getFbToken())
                .setPixelId(StringUtil.isNullOrEmpty(rechargeOrder.getPixelId()) ? "" : rechargeOrder.getPixelId());
        return fbInfo.build();
    }
}
