package com.game.handler.http;

import com.game.billingsr.main.BillingServer;
import com.game.billingsr.manager.BillingMrg;
import com.game.billingsr.manager.ServerMrg;
import com.game.dao.order.WithdrawOrderDao;
import com.game.engine.enums.ServerType;
import com.game.engine.io.handler.HttpHandler;
import com.game.engine.script.IHandlerEntity;
import com.game.engine.struct.ServerInfo;
import com.game.engine.utils.MsgUtil;
import com.game.engine.utils.TimeUtil;
import com.game.entity.order.WithdrawOrder;
import com.game.entity.order.WithdrawOrderFields;
import com.game.enums.ErrorCode;
import com.game.manager.EntityDaoMrg;
import com.proto.InnerMessage;
import com.proto.MIDMessage;
import io.netty.util.internal.StringUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.mongodb.core.query.Update;

//http://127.0.0.1:8680/billing/notifyWithdrawSuccess?playerId=&orderId=&status=
@IHandlerEntity(path = "/billing/notifyWithdrawSuccess", desc = "通知提现成功")
public class Billing_NotifyWithdrawSuccessHandler extends HttpHandler {
    private static final Logger LOGGER = LoggerFactory.getLogger(Billing_NotifyWithdrawSuccessHandler.class);

    @Override
    public void run() {
        final String playerId = (String) paramsMap.get("playerId");
        final String orderId = (String) paramsMap.get("orderId");
        final String status = (String) paramsMap.get("status");
        final String tips = (String) paramsMap.get("tips");
        BillingServer.getInstance().asyncExecute(Long.parseLong(orderId), () -> {
            try {
                if (StringUtil.isNullOrEmpty(playerId) || StringUtil.isNullOrEmpty(orderId)
                        || StringUtil.isNullOrEmpty(status)) {
                    LOGGER.warn("playerId：{}，please check the request parameters", playerId);
                    MsgUtil.responseHttp(ErrorCode.Parameter_Error.getCode(), session);
                    return;
                }

                final WithdrawOrder withDrawOrder = (WithdrawOrder) BillingMrg.getInstance().getWithdrawOrder(Long.parseLong(orderId));
                if (withDrawOrder == null) {
                    LOGGER.warn("playerId：{}，orderId：{}，not exist", playerId, orderId);
                    MsgUtil.responseHttp(ErrorCode.OrderId_Not_Exist.getCode(), session);
                    return;
                }

                final ServerInfo hallServer = ServerMrg.getInstance().getServerType(ServerType.HALL, Long.parseLong(playerId), withDrawOrder.getHallId());
                if (hallServer == null) {
                    LOGGER.warn("serverId：{}，not exist", withDrawOrder.getHallId());
                    MsgUtil.responseHttp(ErrorCode.Internal_Server_Error.getCode(), session);
                    return;
                }

                if (withDrawOrder.getStatus() == Integer.parseInt(status)) {
                    MsgUtil.responseHttp(ErrorCode.Repeated_WithDraw.getCode(), session);
                    return;
                }

                MsgUtil.responseHttp(ErrorCode.Success.getCode(), session);

                withDrawOrder.setStatus(Integer.parseInt(status));
                withDrawOrder.setPayEndTime(TimeUtil.currentTimeMillis());
                withDrawOrder.setTips(StringUtil.isNullOrEmpty(tips) ? "" : tips);
                final Update update = new Update();
                update.set(WithdrawOrderFields.status, withDrawOrder.getStatus())
                        .set(WithdrawOrderFields.payEndTime, withDrawOrder.getPayEndTime())
                        .set(WithdrawOrderFields.tips, withDrawOrder.getTips());
                EntityDaoMrg.getInstance().getDao(WithdrawOrderDao.class).updateOrder(withDrawOrder.getOrderId(), update);

                BillingMrg.getInstance().removeOrderInfo(Long.parseLong(orderId));

                final InnerMessage.InnerNotifyWithdrawMessage.Builder notifyWithDraw = InnerMessage.InnerNotifyWithdrawMessage.newBuilder();
                notifyWithDraw.setMsgID(MIDMessage.MID.InnerNotifyWithdraw_VALUE)
                        .setCurrencyId(withDrawOrder.getCurrencyId())
                        .setAmount(withDrawOrder.getAmounts())
                        .setOrderId(Long.parseLong(orderId))
                        .setStatus(Integer.parseInt(status));
                MsgUtil.sendInnerMsg(hallServer.getActiveSession(), notifyWithDraw.build(), Long.parseLong(playerId));
            } catch (Exception e) {
                LOGGER.error("Billing_NotifyWithdrawSuccessHandler", e);
                MsgUtil.responseHttp(ErrorCode.Internal_Server_Error.getCode(), session);
            }
        });
    }

}
