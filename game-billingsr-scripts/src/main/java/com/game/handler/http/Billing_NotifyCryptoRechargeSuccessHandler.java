package com.game.handler.http;

import com.game.billingsr.main.BillingServer;
import com.game.billingsr.manager.BillingMrg;
import com.game.billingsr.manager.ServerMrg;
import com.game.dao.order.RechargeOrderDao;
import com.game.engine.enums.ServerType;
import com.game.engine.io.handler.HttpHandler;
import com.game.engine.script.IHandlerEntity;
import com.game.engine.struct.ServerInfo;
import com.game.engine.utils.MsgUtil;
import com.game.engine.utils.UniqueIDGenerator;
import com.game.entity.order.RechargeOrder;
import com.game.entity.player.Player;
import com.game.entity.player.PlayerFields;
import com.game.enums.ErrorCode;
import com.game.enums.OrderType;
import com.game.manager.EntityDaoMrg;
import com.game.redis.RedisUtils;
import com.proto.InnerMessage;
import com.proto.MIDMessage;
import io.netty.util.internal.StringUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

//http://127.0.0.1:8680/billing/notifyCryptoRechargeSuccess?playerId=&currencyId=&amount=
@IHandlerEntity(path = "/billing/notifyCryptoRechargeSuccess", desc = "通知加密充值成功")
public class Billing_NotifyCryptoRechargeSuccessHandler extends HttpHandler {
    private static final Logger LOGGER = LoggerFactory.getLogger(Billing_NotifyCryptoRechargeSuccessHandler.class);

    @Override
    public void run() {
        final String playerId = (String) paramsMap.get("playerId");
        final String currencyId = (String) paramsMap.get("currencyId");
        final String amount = (String) paramsMap.get("amount");
        final String business_no = (String) paramsMap.get("business_no");

        BillingServer.getInstance().asyncExecute(Long.parseLong(playerId), () -> {
            try {
                if (StringUtil.isNullOrEmpty(playerId) || StringUtil.isNullOrEmpty(currencyId)
                        || StringUtil.isNullOrEmpty(amount)) {
                    LOGGER.warn("playerId：{}，please check the request parameters", playerId);
                    MsgUtil.responseHttp(ErrorCode.Parameter_Error.getCode(), session);
                    return;
                }

                final String playerHallId = RedisUtils.getPlayerInfo(Long.parseLong(playerId), PlayerFields.hallId);
                final int hallId = StringUtil.isNullOrEmpty(playerHallId) ? 0 : Integer.parseInt(playerHallId);

                final UniqueIDGenerator uniqueID = BillingServer.getInstance().getUniqueIDGenerator();
                final RechargeOrder rechargeOrder = new RechargeOrder(uniqueID.nextId());
                final Player player = new Player(Long.parseLong(playerId));
                player.setBusiness_no(business_no);
                BillingMrg.getInstance().createOrderInfo(player, rechargeOrder, OrderType.RECHARGE,
                        Integer.parseInt(currencyId), Double.parseDouble(amount));
                rechargeOrder.setPaymentMethod(401);
                rechargeOrder.setStatus(1);
                EntityDaoMrg.getInstance().getDao(RechargeOrderDao.class).insert(rechargeOrder);

                MsgUtil.responseHttp(ErrorCode.Success.getCode(), session);

                final ServerInfo hallServer = ServerMrg.getInstance().getServerType(ServerType.HALL, Long.parseLong(playerId), hallId);
                if (hallServer == null) {
                    MsgUtil.responseHttp(ErrorCode.Internal_Server_Error.getCode(), session);
                    return;
                }

                final InnerMessage.InnerNotifyRechargeMessage.Builder notifyRecharge = InnerMessage.InnerNotifyRechargeMessage.newBuilder();
                notifyRecharge.setMsgID(MIDMessage.MID.InnerNotifyRecharge_VALUE)
                        .setCurrencyId(rechargeOrder.getCurrencyId())
                        .setAmount(rechargeOrder.getAmounts())
                        .setOrderId(rechargeOrder.getOrderId());
                MsgUtil.sendInnerMsg(hallServer.getActiveSession(), notifyRecharge.build(), Long.parseLong(playerId));
            } catch (Exception e) {
                LOGGER.error("Billing_NotifyCryptoRechargeSuccessOrderHandler", e);
                MsgUtil.responseHttp(ErrorCode.Internal_Server_Error.getCode(), session);
            }
        });
    }

}
