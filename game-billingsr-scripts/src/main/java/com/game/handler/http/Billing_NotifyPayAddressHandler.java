package com.game.handler.http;

import com.game.billingsr.manager.BillingMrg;
import com.game.billingsr.manager.ServerMrg;
import com.game.engine.enums.ServerType;
import com.game.engine.io.handler.HttpHandler;
import com.game.engine.script.IHandlerEntity;
import com.game.engine.struct.ServerInfo;
import com.game.engine.utils.MsgUtil;
import com.game.entity.order.Order;
import com.game.enums.ErrorCode;
import com.game.utils.VirtualThreadUtils;
import com.proto.BillingMessage;
import com.proto.MIDMessage;
import io.netty.util.internal.StringUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

//http://127.0.0.1:8680/billing/notifyPayAddress?playerId=&orderId=&payAddress=
@IHandlerEntity(path = "/billing/notifyPayAddress", desc = "通知支付地址")
public class Billing_NotifyPayAddressHandler extends HttpHandler {
    private static final Logger LOGGER = LoggerFactory.getLogger(Billing_NotifyPayAddressHandler.class);

    @Override
    public void run() {
        VirtualThreadUtils.execute(() -> {
            final String playerIdStr = (String) paramsMap.get("playerId");
            final String orderId = (String) paramsMap.get("orderId");
            final String payAddress = (String) paramsMap.get("payAddress");
            final String channelOrderNo = (String) paramsMap.get("channelOrderNo");
            final String expireTime = (String) paramsMap.get("expireTime");
            final String QRCode = (String) paramsMap.get("QRCode");
            final String paymentMethod = (String) paramsMap.get("paymentMethod");
            final String udpSessionId = (String) paramsMap.get("udpSessionId");

            final long playerId = Long.parseLong(playerIdStr);

            final BillingMessage.ResCreateRechargeOrderMessage.Builder res = BillingMessage.ResCreateRechargeOrderMessage.newBuilder();
            res.setMsgID(MIDMessage.MID.ResCreateRechargeOrder_VALUE);

            //校验一下订单是否一致
            final Order order = BillingMrg.getInstance().getRechargeOrder(Long.parseLong(orderId));
            if (order == null) {
                MsgUtil.responseHttp(ErrorCode.OrderId_Not_Exist.getCode(), this.session);
                return;
            }

            final ServerInfo hallServer = ServerMrg.getInstance().getServerType(ServerType.HALL, playerId, order.getHallId());
            if (hallServer == null) {
                MsgUtil.responseHttp(ErrorCode.Internal_Server_Error.getCode(), this.session);
                return;
            }
            try {
                if (StringUtil.isNullOrEmpty(payAddress)) {
                    MsgUtil.responseHttp(ErrorCode.Payment_Callback_Address_Error.getCode(), this.session);

                    res.setError(ErrorCode.Data_Error.getCode());
                    MsgUtil.sendInnerMsg(hallServer.getActiveSession(), res.build(), playerId, Long.parseLong(udpSessionId));
                    return;
                }

                MsgUtil.responseHttp(ErrorCode.Success.getCode(), this.session);

                res.setPayAddress(payAddress)
                        .setChannelOrderNo(StringUtil.isNullOrEmpty(channelOrderNo) ? "" : channelOrderNo)
                        .setExpireTime(Long.parseLong(expireTime))
                        .setOrderId(order.getOrderId())
                        .setCurrencyId(order.getCurrencyId())
                        .setPaymentMethod(StringUtil.isNullOrEmpty(paymentMethod) ? "" : paymentMethod)
                        .setRechargeAmount(order.getAmounts())
                        .setQRCode(StringUtil.isNullOrEmpty(QRCode) ? "" : QRCode)
                        .setCreateTime(order.getCreateTime());
                MsgUtil.sendInnerMsg(hallServer.getActiveSession(), res.build(), playerId, Long.parseLong(udpSessionId));
                LOGGER.warn("notify payAddress playerId；{}，orderId：{}", playerId, order.getOrderId());
            } catch (Exception e) {
                LOGGER.error("Billing_NotifyPayAddressHandler", e);
                MsgUtil.responseHttp(ErrorCode.Internal_Server_Error.getCode(), this.session);
                MsgUtil.sendInnerMsg(hallServer.getActiveSession(), res.build(), playerId, Long.parseLong(udpSessionId));
            }
        });
    }

}
